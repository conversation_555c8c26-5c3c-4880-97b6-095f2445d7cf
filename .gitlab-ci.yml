stages:
  # - build
  # - test
  - foradar-frontend-develop

# cache:
#   key: ${CI_COMMIT_REF_SLUG}
#   paths:
#     - node_modules/
# build-job:
#   stage: build
#   script:
#     - cd /data/foradar-frontend
#     - echo "Installing dependencies..."
#     - git config --global --add safe.directory /data/foradar-frontend
#     - sudo git fetch && sudo git checkout . && sudo git checkout develop && sudo git pull origin develop
#     - cd /data/foradar-frontend && sudo npm install
#     - cd /data/foradar-frontend && sudo yarn build:dev
#   artifacts:
#     paths:
#       - dist/ # 假设构建输出在 dist 目录
#     expire_in: 1 week
#   only:
#     - develop
#   environment:
#     name: $CI_COMMIT_BRANCH
#   tags:
#     - foradar2.0_develop

# # 测试阶段
# cypress-test:
#   stage: test
#   script:
#     - echo "Starting Cypress tests..."
#     - docker run --rm -v $(pwd):/e2e -w /e2e cypress/browsers:node16.14.2-chrome99-ff97 npx cypress run
#   artifacts:
#     when: always
#     reports:
#       junit: cypress/results/*.xml # 如果生成了 JUnit 测试报告
#     paths:
#       - cypress/screenshots/ # 保存 Cypress 截图
#       - cypress/videos/ # 保存 Cypress 视频
#   only:
#     - develop
#   environment:
#     name: $CI_COMMIT_BRANCH
#   tags:
#     - foradar2.0_develop

deploy:
  stage: foradar-frontend-develop
  script:
    - cd /data/foradar-frontend
    - git config --global --add safe.directory /data/foradar-frontend
    - sudo git fetch && sudo git checkout . && sudo git checkout develop && sudo git pull origin develop
    - cd /data/foradar-frontend && sudo npm install
    - cd /data/foradar-frontend && sudo yarn build:dev
    - sudo chown foradar:foradar -Rf /data/foradar-frontend
  only:
    - develop
  environment:
    name: $CI_COMMIT_BRANCH
  tags:
    - foradar2.0_develop
