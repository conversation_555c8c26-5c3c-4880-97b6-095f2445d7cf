#!/bin/bash
JOB_NAME="foradar"
BUILD_DISPLAY_NAME="foradar-frontend_$1"

#创建代码软链
/bin/ln -snf /data/web/$JOB_NAME/.$BUILD_DISPLAY_NAME  /data/foradar-frontend
#设置权限
sudo chown foradar:foradar /data/web/$JOB_NAME/.$BUILD_DISPLAY_NAME -Rf
sudo chown foradar:foradar /data/foradar-frontend
#删除历史部署文件
cd /data/web/$JOB_NAME/ && sudo ls -t foradar-frontend_*.gz |awk '{ if (NR > 2){print $1}}'|awk -F '_' '{print $2}'|awk -F '.' '{print "."$1}' |xargs rm -Rf
#删除历史压缩包
cd /data/web/$JOB_NAME/ && sudo ls -t foradar-frontend_*.gz |awk '{ if (NR > 2){print $1}}'|xargs rm -rf

