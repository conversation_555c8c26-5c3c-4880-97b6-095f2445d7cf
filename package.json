{"name": "project_test", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "build": "vue-cli-service build", "build:dev": "export NODE_ENV=dev&&vue-cli-service build", "test:e2e": "start-server-and-test serve http://***********:8080/ 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test serve http://***********:8080/ 'cypress open --e2e'", "cypress:open": "cypress open"}, "dependencies": {"@antv/g6": "^4.8.5", "axios": "^0.21.1", "codemirror": "5.64.0", "core-js": "^3.6.5", "docx-preview": "^0.1.11", "echarts": "^5.3.3", "element-china-area-data": "^5.0.2", "element-ui": "^2.15.13", "highcharts": "9.1.0", "hsxa-ui": "^0.9.32", "html-docx-js": "^0.3.1", "jquery": "3.6.0", "js-sha1": "^0.6.0", "jszip": "^3.10.1", "leaflet": "^1.9.4", "lib-flexible": "^0.3.2", "mammoth": "^1.4.21", "mavon-editor": "^2.10.4", "nprogress": "^0.2.0", "postcss-pxtorem": "^5.1.1", "px2rem": "^0.5.0", "qs": "^6.10.1", "save": "^2.9.0", "simditor": "2.3.6", "spark-md5": "^3.0.2", "v-viewer": "^1.6.4", "vue": "^2.6.11", "vue-clipboard2": "^0.3.3", "vue-infinite-loading": "^2.4.5", "vue-infinite-scroll": "^2.0.2", "vue-lottie": "^0.2.1", "vue-native-websocket": "^2.0.15", "vue-router": "^3.2.0", "vue-seamless-scroll": "^1.1.23", "vue-simple-uploader": "^0.7.6", "vuex": "^3.4.0", "zm-tree-org": "^2.1.3"}, "devDependencies": {"@types/codemirror": "5.60.5", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^1.1.11", "cross-env": "^7.0.3", "less": "^3.0.4", "less-loader": "^5.0.0", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.3.3", "px2rem-loader": "^0.1.9", "sass-resources-loader": "^2.2.3", "start-server-and-test": "^2.0.2", "style-resources-loader": "^1.4.1", "vue-cli-plugin-style-resources-loader": "^0.1.5", "vue-pdf": "^4.3.0", "vue-template-compiler": "^2.7.14", "webpack": "^4.46.0", "webpack-bundle-analyzer": "^4.9.1", "webpack-sentry-plugin": "^2.0.3"}}