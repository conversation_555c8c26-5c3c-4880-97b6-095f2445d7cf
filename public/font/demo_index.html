<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">

    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>

      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3274648" target="_blank" class="nav-more">查看项目</a>

    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">

            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">关键词</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">时间</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">菱形</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">LOGO</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">500</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">IP</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">端口</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">组件</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">硬件层</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">系统层</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">服务层</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">支持层</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">业务层</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">空</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">扫描</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">企业</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">警告</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">失败</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">成功</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">储存</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">Intel(R)</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">内存</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">头像</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">已知资产</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">企业管理</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">系统管理</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">报告管理</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">资产发现</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">数据泄露管理</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">资产认领</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">漏洞管理</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
            <li class="dib">
              <span class="icon iconfont">&#xe630;</span>
                <div class="name">证书</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>

          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot?t=1681181158668'); /* IE9 */
  src: url('iconfont.eot?t=1681181158668#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
       url('iconfont.woff?t=1681181158668') format('woff'),
       url('iconfont.ttf?t=1681181158668') format('truetype'),
       url('iconfont.svg?t=1681181158668#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">

          <li class="dib">
            <span class="icon iconfont icon-guanjianci"></span>
            <div class="name">
              关键词
            </div>
            <div class="code-name">.icon-guanjianci
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shijian"></span>
            <div class="name">
              时间
            </div>
            <div class="code-name">.icon-shijian
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-lianjie"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.icon-lianjie
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-lingxing"></span>
            <div class="name">
              菱形
            </div>
            <div class="code-name">.icon-lingxing
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-bianzu"></span>
            <div class="name">
              LOGO
            </div>
            <div class="code-name">.icon-bianzu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-bianzu4"></span>
            <div class="name">
              500
            </div>
            <div class="code-name">.icon-a-bianzu4
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-bianzu8"></span>
            <div class="name">
              IP
            </div>
            <div class="code-name">.icon-a-bianzu8
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-bianzu81"></span>
            <div class="name">
              端口
            </div>
            <div class="code-name">.icon-a-bianzu81
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-bianzu82"></span>
            <div class="name">
              组件
            </div>
            <div class="code-name">.icon-a-bianzu82
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-bianzu13"></span>
            <div class="name">
              硬件层
            </div>
            <div class="code-name">.icon-a-bianzu13
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-bianzu13beifen"></span>
            <div class="name">
              系统层
            </div>
            <div class="code-name">.icon-a-bianzu13beifen
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-bianzu13beifen2"></span>
            <div class="name">
              服务层
            </div>
            <div class="code-name">.icon-a-bianzu13beifen2
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-bianzu13beifen3"></span>
            <div class="name">
              支持层
            </div>
            <div class="code-name">.icon-a-bianzu13beifen3
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-bianzu13beifen4"></span>
            <div class="name">
              业务层
            </div>
            <div class="code-name">.icon-a-bianzu13beifen4
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-kong"></span>
            <div class="name">
              空
            </div>
            <div class="code-name">.icon-kong
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-saomiao"></span>
            <div class="name">
              扫描
            </div>
            <div class="code-name">.icon-saomiao
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-qiye"></span>
            <div class="name">
              企业
            </div>
            <div class="code-name">.icon-qiye
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-jinggao"></span>
            <div class="name">
              警告
            </div>
            <div class="code-name">.icon-jinggao
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shibai"></span>
            <div class="name">
              失败
            </div>
            <div class="code-name">.icon-shibai
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-chenggong"></span>
            <div class="name">
              成功
            </div>
            <div class="code-name">.icon-chenggong
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-chucun"></span>
            <div class="name">
              储存
            </div>
            <div class="code-name">.icon-chucun
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-IntelR"></span>
            <div class="name">
              Intel(R)
            </div>
            <div class="code-name">.icon-a-IntelR
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-neicun"></span>
            <div class="name">
              内存
            </div>
            <div class="code-name">.icon-neicun
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-touxiang"></span>
            <div class="name">
              头像
            </div>
            <div class="code-name">.icon-touxiang
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-yizhizichan"></span>
            <div class="name">
              已知资产
            </div>
            <div class="code-name">.icon-yizhizichan
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-qiyeguanli"></span>
            <div class="name">
              企业管理
            </div>
            <div class="code-name">.icon-qiyeguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-xitongguanli"></span>
            <div class="name">
              系统管理
            </div>
            <div class="code-name">.icon-xitongguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-baogaoguanli"></span>
            <div class="name">
              报告管理
            </div>
            <div class="code-name">.icon-baogaoguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-zichanfaxian"></span>
            <div class="name">
              资产发现
            </div>
            <div class="code-name">.icon-zichanfaxian
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shujuxielouguanli"></span>
            <div class="name">
              数据泄露管理
            </div>
            <div class="code-name">.icon-shujuxielouguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-zichanrenling"></span>
            <div class="name">
              资产认领
            </div>
            <div class="code-name">.icon-zichanrenling
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-loudongguanli"></span>
            <div class="name">
              漏洞管理
            </div>
            <div class="code-name">.icon-loudongguanli
            </div>
          </li>
          <li class="dib">
            <span class="icon iconfont icon-certificate"></span>
            <div class="name">
              证书
            </div>
            <div class="code-name">.icon-certificate
            </div>
          </li>
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanjianci"></use>
                </svg>
                <div class="name">关键词</div>
                <div class="code-name">#icon-guanjianci</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shijian"></use>
                </svg>
                <div class="name">时间</div>
                <div class="code-name">#icon-shijian</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianjie"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#icon-lianjie</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lingxing"></use>
                </svg>
                <div class="name">菱形</div>
                <div class="code-name">#icon-lingxing</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianzu"></use>
                </svg>
                <div class="name">LOGO</div>
                <div class="code-name">#icon-bianzu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu4"></use>
                </svg>
                <div class="name">500</div>
                <div class="code-name">#icon-a-bianzu4</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu8"></use>
                </svg>
                <div class="name">IP</div>
                <div class="code-name">#icon-a-bianzu8</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu81"></use>
                </svg>
                <div class="name">端口</div>
                <div class="code-name">#icon-a-bianzu81</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu82"></use>
                </svg>
                <div class="name">组件</div>
                <div class="code-name">#icon-a-bianzu82</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu13"></use>
                </svg>
                <div class="name">硬件层</div>
                <div class="code-name">#icon-a-bianzu13</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu13beifen"></use>
                </svg>
                <div class="name">系统层</div>
                <div class="code-name">#icon-a-bianzu13beifen</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu13beifen2"></use>
                </svg>
                <div class="name">服务层</div>
                <div class="code-name">#icon-a-bianzu13beifen2</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu13beifen3"></use>
                </svg>
                <div class="name">支持层</div>
                <div class="code-name">#icon-a-bianzu13beifen3</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu13beifen4"></use>
                </svg>
                <div class="name">业务层</div>
                <div class="code-name">#icon-a-bianzu13beifen4</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kong"></use>
                </svg>
                <div class="name">空</div>
                <div class="code-name">#icon-kong</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-saomiao"></use>
                </svg>
                <div class="name">扫描</div>
                <div class="code-name">#icon-saomiao</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiye"></use>
                </svg>
                <div class="name">企业</div>
                <div class="code-name">#icon-qiye</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jinggao"></use>
                </svg>
                <div class="name">警告</div>
                <div class="code-name">#icon-jinggao</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shibai"></use>
                </svg>
                <div class="name">失败</div>
                <div class="code-name">#icon-shibai</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chenggong"></use>
                </svg>
                <div class="name">成功</div>
                <div class="code-name">#icon-chenggong</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chucun"></use>
                </svg>
                <div class="name">储存</div>
                <div class="code-name">#icon-chucun</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-IntelR"></use>
                </svg>
                <div class="name">Intel(R)</div>
                <div class="code-name">#icon-a-IntelR</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-neicun"></use>
                </svg>
                <div class="name">内存</div>
                <div class="code-name">#icon-neicun</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-touxiang"></use>
                </svg>
                <div class="name">头像</div>
                <div class="code-name">#icon-touxiang</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yizhizichan"></use>
                </svg>
                <div class="name">已知资产</div>
                <div class="code-name">#icon-yizhizichan</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiyeguanli"></use>
                </svg>
                <div class="name">企业管理</div>
                <div class="code-name">#icon-qiyeguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xitongguanli"></use>
                </svg>
                <div class="name">系统管理</div>
                <div class="code-name">#icon-xitongguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baogaoguanli"></use>
                </svg>
                <div class="name">报告管理</div>
                <div class="code-name">#icon-baogaoguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zichanfaxian"></use>
                </svg>
                <div class="name">资产发现</div>
                <div class="code-name">#icon-zichanfaxian</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuxielouguanli"></use>
                </svg>
                <div class="name">数据泄露管理</div>
                <div class="code-name">#icon-shujuxielouguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zichanrenling"></use>
                </svg>
                <div class="name">资产认领</div>
                <div class="code-name">#icon-zichanrenling</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-loudongguanli"></use>
                </svg>
                <div class="name">漏洞管理</div>
                <div class="code-name">#icon-loudongguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-certificate"></use>
                </svg>
                <div class="name">证书</div>
                <div class="code-name">#icon-certificate</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-collapse"></use>
                </svg>
                <div class="name">收起</div>
                <div class="code-name">#icon-collapse</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-expand"></use>
                </svg>
                <div class="name">展开</div>
                <div class="code-name">#icon-expand</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-question"></use>
                </svg>
                <div class="name">疑问</div>
                <div class="code-name">#icon-question</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-close"></use>
                </svg>
                <div class="name">结束</div>
                <div class="code-name">#icon-close</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-assets"></use>
                </svg>
                <div class="name">资料</div>
                <div class="code-name">#icon-assets</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-history"></use>
                </svg>
                <div class="name">历史记录</div>
                <div class="code-name">#icon-history</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-diamondNum"></use>
                </svg>
                <div class="name">菱形序号</div>
                <div class="code-name">#icon-diamondNum</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-repeat"></use>
                </svg>
                <div class="name">菱形序号</div>
                <div class="code-name">#icon-repeat</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-more"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#icon-more</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rightArrow"></use>
                </svg>
                <div class="name">右向箭头</div>
                <div class="code-name">#icon-rightArrow</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-more-right"></use>
                </svg>
                <div class="name">右向更多细箭头</div>
                <div class="code-name">#icon-more-right</div>
            </li>
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tip"></use>
                </svg>
                <div class="name">感叹号</div>
                <div class="code-name">#icon-tip</div>
            </li>

          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
