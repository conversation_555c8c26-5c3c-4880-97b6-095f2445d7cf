<template>
  <div id="app">
    <router-view v-if="isRouterAlive"></router-view>
  </div>
</template>

<script>
import { getUpdateInfo } from '@/api/apiConfig/api.js'
import { onlineIs } from '@/api/apiConfig/person.js'

import { mapActions, mapMutations, mapGetters, mapState } from 'vuex'
export default {
  provide() {
    return {
      reload: this.reload,
      userData: ''
    }
  },
  data() {
    return {
      sendWebIcon: '',
      isRouterAlive: true,
      lockReturn: false,
      timeout: 60 * 1000,
      sendTimeoutObj: null,
      currentId: '',
      reportId: '',
      tuijianScanId: ''
    }
  },
  created() {
    if (sessionStorage.getItem('userMessage')) {
      this.user = JSON.parse(sessionStorage.getItem('userMessage')).user
    }
  },
  watch: {
    async $route() {
      if (this.$route.path == '/login') {
        clearInterval(this.sendTimeoutObj)
        this.sendTimeoutObj = null
      }

      // 进入系统请求websocket
      if (this.$route.path == '/home') {
        this.initWebSocket()
      }
      if (
        window.location.host == 'foradar.baimaohui.net' ||
        window.location.host == '***********' ||
        window.location.host == '************'
      ) {
        this.getOnlineIs() // 升级公告页面
      }
    },
    getterWebsocketMessage(msg) {
      // 大屏界面不显示ws公共提示
      if (this.$route.path != '/screenAssets') {
        this.handleMessage(msg)
      }
    },
    getterCurrentCompany(val) {
      if (this.user && this.user.role == 2) {
        if (this.websocket.readyState == 1) {
          this.websocket.send(
            JSON.stringify({
              data: { operate_company_id: this.currentCompany },
              cmd: 'bind_company'
            })
          )
        }
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.sendTimeoutObj)
    this.sendTimeoutObj = null
  },
  computed: {
    ...mapState([
      'websocket',
      'socketTimestamp',
      'websocketMessage',
      'currentCompany',
      'currentCompanyId'
    ]),
    ...mapGetters([
      'getterWebsocketMessage',
      'getterSocketTimestamp',
      'getterCurrentCompany',
      'getterCompanyId'
    ])
  },
  async mounted() {
    // if (sessionStorage.getItem('userMessage')) {
    //   // 用于后端调试
    //   let user = JSON.parse(sessionStorage.getItem('userMessage')).user.role
    //   if (user == 1) {
    //     window.vue = this
    //   } else {
    //     window.vue = ''
    //   }
    // }
    // 监听窗⼝关闭事件，当窗⼝关闭时，主动去关闭websocket连接，防⽌连接还没断开就关闭窗⼝，server端会抛异常。
    // window.onbeforeunload = function () {
    //   // this.$socket.close();
    // }

    this.initWebSocket()

    if (
      window.location.host == 'foradar.baimaohui.net' ||
      window.location.host == '***********' ||
      window.location.host == '************'
    ) {
      if (this.$route.path == '/login') {
        this.getOnlineIs()
      }
      return
    }
    this.getUploadInfo()
  },
  methods: {
    ...mapMutations(['setSocketMessage', 'changeMenuId']),
    ...mapActions(['initWebSocket']),
    async getOnlineIs() {
      let res = await onlineIs()
      if (res.code == 0 && res.data && res.data.online_state) {
        if (res.data.online_state.is_show == 1) {
          // 升级完成
          if (this.$route.path == '/login' || this.$route.path == '/noticePage') {
            // 升级成功时跳转登录
            sessionStorage.clear()
            localStorage.clear()
            this.$router.push({
              path: '/login'
            })
          }
        } else {
          // 正在升级，跳转到升级页面
          this.$router.push({
            path: '/noticePage'
          })
        }
      }
    },
    async getUploadInfo() {
      let res = await getUpdateInfo()
      // debugger
      if (res.code == 0) {
        if (res.data.status == 1) {
          this.$router.push({
            path: '/upgrading'
          })
        }
      }
    },
    reload() {
      this.isRouterAlive = false
      this.$nextTick((e) => {
        this.isRouterAlive = true
      })
    },
    // 立即查看推荐记录
    goToView(flag, group_id) {
      sessionStorage.setItem('menuId', '2-2')
      this.changeMenuId('2-2')
      this.$router.push({
        path: '/scanReg', // 去审核logShenhe
        query: {
          flag: flag,
          group_id: group_id
        }
      })
    },
    //查看新增线索
    goAssetsCloud(group_id) {
      sessionStorage.setItem('menuId', '1-6-2')
      this.changeMenuId('1-6-2')
      this.$router.push({
        path: '/companyBank',
        query: {
          group_id: group_id
        }
      })
    },
    // 立即推荐的全局方法
    handleMessage(res, o) {
      //处理接收到的信息
      if (this.currentCompanyId == res.data.user_id && res.cmd == 'recommend_progress') {
        // 推荐任务推送
        this.runningData = res.data
        if (this.currentId == res.data.flag) return // 相同的id,提示一次成功
        // this.currentCompany == res.ws_company_id &&
        if (res.data.status == 2) {
          this.currentId = res.data.flag
          const h = this.$createElement
          let notify = this.$notify({
            title: res.data.task_name + '任务推荐完成',
            type: 'success',
            duration: 180000,
            offset: 64,
            message: h('p', {}, [
              h('i', {}, `共推荐${res.data.count}个资产。`)
              // h('u', {
              //   on:{ click: () => {
              //     this.goToView(res.data.flag, res.data.group_id)
              //     notify.close()
              //   }}
              // }, '查看结果')
            ])
          })
        } else {
          // this.$message.success('扫描失败！')
        }
      } else if (this.currentCompanyId == res.data.user_id && res.cmd == 'create_report_success') {
        // 报告生成推送
        this.user = JSON.parse(sessionStorage.getItem('userMessage')).user
        if (this.reportId == res.data.id) return // 相同的id,提示一次成功
        if (this.user && this.user.id == res.data.user_id) {
          this.reportId = res.data.id
          const h = this.$createElement
          let notify = this.$notify({
            title: `${res.data.name}已生成`,
            type: 'success',
            duration: 180000,
            offset: 64,
            message: h('p', {}, [
              h(
                'u',
                {
                  on: {
                    click: () => {
                      this.goCheckReport(), notify.close()
                    } // 不能传参，否则会自动执行
                  }
                },
                '查看结果'
              )
            ])
          })
        }
      } else if (
        this.currentCompanyId == res.data.user_id &&
        res.cmd == 'scan_task_recpmmand_progress'
      ) {
        // 推荐任务详情-执行扫描推送
        this.runningData = res.data
        if (this.tuijianScanId == res.data.task_id) return // 相同的id,提示一次成功
        if (res.data.status == 2) {
          this.tuijianScanId = res.data.task_id
          const h = this.$createElement
          let notify = this.$notify({
            title: `${res.data.name}完成`,
            type: 'success',
            duration: 180000,
            offset: 64,
            customClass: 'myNotify',
            message: h('p', {}, [
              h('p', {}, '高可信度资产进入资产台账；'),
              // h('u', { on:{
              //   click: ()=>{this.doSth()} // 不能传参，否则会自动执行
              // }},'查看结果'),
              h('p', {}, '中低可信度资产进入疑似资产')
              // h('u', { on:{
              //   click: ()=>{this.doSths()} // 不能传参，否则会自动执行
              // }},'查看结果'),
            ])
          })
        }
      } else if (res.cmd == 'check_poc_scan_task_progress') {
        // 修复核查
        if (
          this.currentCompanyId == res.data.user_id &&
          sessionStorage.getItem('leakCheckFlag') &&
          sessionStorage.getItem('leakCheckFlag') == res.data.task_id
        ) {
          this.runningData = res.data
          if (this.currentId == res.data.task_id) return // 相同的id,提示一次成功
          if (res.data.status == 2) {
            this.currentId = res.data.task_id
            const h = this.$createElement
            let notify = this.$notify({
              title: '核查完成',
              type: 'success',
              duration: 180000,
              offset: 64,
              message: h('p', {}, [
                h(
                  'i',
                  {},
                  `共核查漏洞${res.data.total}个，发现已修复漏洞${res.data.repaired_num}个`
                ),
                h(
                  'u',
                  {
                    style: `${res.data.repaired_num > 0} ? '' : 'display: none'`,
                    on: {
                      click: () => {
                        this.goToRepaired()
                        notify.close()
                      }
                    }
                  },
                  '查看结果'
                )
              ])
            })
          } else if (res.data.status == 1) {
          } else {
            this.$message.error('核查失败！请重试')
          }
        }
        // 核查进度展示：核查没完成，切换页面再次展示进度，防止重复操作(涉及到安服切换用户，所以需要按照企业id存储核查进度)
        if (res.data.status == 2) {
          sessionStorage.setItem(
            'checkProgress' + res.data.user_id,
            JSON.stringify({
              id: res.data.user_id,
              value: ''
            })
          )
        } else if (res.data.status == 1) {
          sessionStorage.setItem(
            'checkProgress' + res.data.user_id,
            JSON.stringify({
              id: res.data.user_id,
              value: res.data.progress
            })
          )
        } else {
          sessionStorage.setItem(
            'checkProgress' + res.data.user_id,
            JSON.stringify({
              id: res.data.user_id,
              value: ''
            })
          )
        }
      } else if (
        this.currentCompanyId == res.data.user_id &&
        res.cmd == 'extract_asset_clues_job'
      ) {
        if (res.data.status == 2) {
          const h = this.$createElement
          let notify = this.$notify({
            title: '线索自动提取成功',
            type: 'success',
            duration: 180000,
            offset: 64,
            message: h('p', {}, [
              h('i', {}, `新增${res.data.num}个`),
              h(
                'u',
                {
                  on: {
                    click: () => {
                      this.goAssetsCloud(res.data.group_id)
                      notify.close()
                    }
                  }
                },
                '查看结果'
              )
            ])
          })
        }
      } else if (this.currentCompanyId == res.data.user_id && res.cmd == 'finish_risk_ip_count') {
        this.$notify({
          title: '更新风险ip数量完成',
          type: 'success',
          duration: 180000,
          offset: 64
        })
      }
    },
    goCheckReport() {
      sessionStorage.setItem('menuId', '5')
      this.changeMenuId('5')
      this.$router.push({
        path: '/reportManage', // 去已修复页面
        query: {
          flag: 1
        }
      })
    },
    goToRepaired() {
      sessionStorage.setItem('menuId', '3-1-2')
      this.changeMenuId('3-1-2')
      this.$router.push({
        path: '/repairLeakScan', // 去已修复页面
        query: {
          flag: '1'
        }
      })
    },
    doSth() {
      sessionStorage.setItem('menuId', '1-3-1')
      this.changeMenuId('1-3-1')
      this.$router.push('/assetsLedger')
    },
    doSths() {
      sessionStorage.setItem('menuId', '1-4')
      this.changeMenuId('1-4')
      this.$router.push('/unclaimCloud')
    }
    // 用于后端调试
    // sendWeb(cmd, data = '') {
    //   this.sendWebIcon = 11
    //   if (this.$socket.readyState == 1) {
    //     this.$socket.send(JSON.stringify({ status: 200, data: data, cmd: cmd }))
    //   } else {
    //     this.initWebSocket()
    //   }
    // }
  }
}
</script>

<style lang="less">
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #eeeeee;
  height: 100%;
  font-size: 14px;
}

html,
body {
  min-width: 1100px;
  height: 100%;
  margin: 0 !important;
  padding: 0 !important;
  background-color: #eeeeee;
  overflow: hidden;
}

img {
  vertical-align: bottom;
}

// p {
//   // font-size: 0.16rem;
// }

@import './style/huxa/index.less';
@import './style/common';
@import './style/elementui';
@import '../public/font/iconfont.css';

// @import 'https://at.alicdn.com/t/font_3274648_52j21nfl48d.css';
.myNotify {
  .el-notification__group {
    margin-right: 0px;
  }
  .el-button {
    display: inline !important;
    margin-left: 8px !important;
  }
}
</style>
