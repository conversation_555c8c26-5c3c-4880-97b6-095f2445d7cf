import request from "@/utils/request";

// 公告列表
export const noticeList = (data) => request({
  url: '/api/noticeList',
  method: 'post',
  data
})

// 公告文件上传接口
export const uploadFile = (data) => request({
  url: '/api/noticeFile',
  method: 'post',
  data
})

// 公告上传文件下载
export const uploadFileRead = (data) => request({
  url: `/api/file/read/${data}`,
  method: 'get',
})

// 公告的编辑和新增    edittype	1编辑0新增
export const editNotice = (data) => request({
  url: '/api/editNotice',
  method: 'post',
  data
})

// 管理员：发布和撤回/删除公告接口
export const noticePub = (data) => request({
  url: '/api/noticePub',
  method: 'post',
  data
})

// 公告详情接口
export const noticeDetail = (data) => request({
  url: '/api/noticeDetail',
  method: 'post',
  data
})

