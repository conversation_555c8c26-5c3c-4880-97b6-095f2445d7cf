import request from '@/utils/request'
// export default{

export function getFofanum(data) {
  return request({ url: '/clues/fofa_num', method: 'get', params: data })
} // ICON,证书线索在fofa资产数量
export function getWhoisInfo(data) {
  return request({ url: '/whois', method: 'get', params: data, is_golang: 1 })
} // whois信息查询
// ##################################其他
// areaIndex(data){ return request({url: `/area/list` , method: 'get',params: data})} // 资产台账的区域
export function area(data) {
  return request({ url: `/area/${data}`, method: 'get', params: data })
} // 区域
export function industry(data) {
  return request({ url: `/industry`, method: 'get', params: data })
} // 行业

// ##################################审计管理
export function logsIndex(data) {
  return request({ url: '/log', method: 'get', params: data })
} // 日志列表
export function exportLogs(data) {
  return request({ url: '/log', method: 'post', data })
} // 日志列表导出
export function companyList(data) {
  return request({ url: `/company`, method: 'get', params: data })
} // 单位列表

// ##################################报告管理
export function safeReportList(data) {
  return request({ url: '/safeReportList', method: 'post', data })
} // 安服查看需要下发的列表
export function previewReport(data) {
  return request({
    url: data.url,
    method: 'get',
    params: data.params,
    responseType: data.responseType
  })
} // 报告预览

// ##################################报告输出
// saveReportSetting(data){ return request({url: '/createReportSetting', method: 'post', data})} // 报告输出设置保存

// ##################################IP段管理
export function ipList(data) {
  return request({ url: '/ips', method: 'get', params: data })
} // IP段列表分页
export function ipListNoPage(data) {
  return request({ url: '/ips/list', method: 'post', data })
} // IP段列表不分页
export function addIp(data) {
  return request({ url: '/ips', method: 'post', data })
} // 添加IP段
export function delIp(data) {
  return request({ url: '/ips', method: 'delete', data })
} // 删除IP段

// ##################################数据泄露
export function sensitiveIndex(data) {
  return request({ url: '/leakage', method: 'get', params: data })
} // 列表
export function updateStatus(data) {
  return request({ url: '/leakage', method: 'put', data })
} // 忽略、确认
export function removeSensitiveData(data) {
  return request({ url: '/leakage', method: 'delete', data })
} // 删除
export function uploadSensitiveData(data) {
  return request({ url: '/leakage', method: 'post', data })
} // 数据泄露上传后保存接口
export function sensitiveRecommandSettings(data) {
  return request({ url: '/settings/info', method: 'get', params: data })
} // 推荐设置详情
export function storeSensitiveRecommandSetting(data) {
  return request({ url: '/settings', method: 'post', data })
} // 推荐设置保存
export function sensitiveRecommandRecordIndex(data) {
  return request({ url: '/settings', method: 'get', params: data })
} // 推荐设置推荐记录查看
export function exportLeakageList(data) {
  return request({ url: '/leakage/export', method: 'post', data })
} // 数据泄露导出
export function addSensitiveList(data) {
  return request({ url: '/data_leak', method: 'post', data })
} // 数据泄露单个添加
export function regetScreenshot(data) {
  return request({ url: '/screenshot', method: 'get', params: data })
} // 截图重试
export function editSensitiveList(data) {
  return request({ url: '/edit_data_leak', method: 'post', data })
} // 数据泄漏编辑

// ##################################新型资产
export function sensitiveIndexNewAssets(data) {
  return request({ url: '/data_assets', method: 'get', params: data })
} // 列表
export function updateStatusNewAssets(data) {
  return request({ url: '/data_assets', method: 'put', data })
} // 忽略、确认
export function removeSensitiveDataNewAssets(data) {
  return request({ url: '/data_assets', method: 'delete', data })
} // 删除
export function editLeakageList(data) {
  return request({ url: '/data_assets/edit', method: 'post', data })
} // 数字资产编辑
export function addLeakageList(data) {
  return request({ url: '/data_assets/add', method: 'post', data })
} // 数字资产单独新增
export function uploadSensitiveDataNewAssets(data) {
  return request({ url: '/data_assets', method: 'post', data })
} // 数据泄露上传后保存接口

// ##################################关键词管理
export function monitorKeywordIndex(data) {
  return request({ url: '/keyword', method: 'get', params: data })
} // 列表
export function updateKeywordStatus(data) {
  return request({ url: '/keyword', method: 'put', data })
} // 禁用启用
export function addSensitiveKeyword(data) {
  return request({ url: '/keyword', method: 'post', data })
} // 新增
export function delSensitiveKeyword(data) {
  return request({ url: '/keyword', method: 'delete', data })
} // 删除
export function keywordCopy(data) {
  return request({ url: '/keyword/copy', method: 'post', data })
} // 同步
export function updateTypeSensitiveKeyword(data) {
  return request({ url: '/keyword/' + data.id, method: 'put', data: data.data })
} // 更新类型

// ##################################线索黑名单管理
export function blackClueIndex(data) {
  return request({ url: '/clues/black_keyword', method: 'get', params: data })
} // 列表
export function addBlackClue(data) {
  return request({ url: '/clues/black_keyword', method: 'post', data })
} // 新增
export function delBlackClue(data) {
  return request({ url: '/clues/black_keyword', method: 'delete', data })
} // 删除

// ##################################高级筛选
export function getCondition(data) {
  return request({ url: '/assets/account/ansys_condition', method: 'post', data, is_golang: 1 })
} // ip+端口的高级筛选条件
export function getConditionV1(data) {
  return request({ url: '/assets/account/condition', method: 'post', data, is_golang: 1 })
} // ip维度的高级筛选条件
export function getConditionV2(data) {
  return request({ url: '/risk_assets/list/condition', method: 'post', data })
}

// ##################################资产认领
// assetsList(data){ return request({url: '/ansysDataIndexV2', method: 'post', data})} // 列表
// assetsSign(data){ return request({url: '/assetsSignToConfirm', method: 'post', data})} // 资产标记

// ##################################已知认领
// knownAssets(data){ return request({url: '/ansysDataIndexV2', method: 'post', data})} // 已认领资产列表
// delKnownAssets(data){ return request({url: '/delForadarAssets', method: 'post', data})} // 已认领资产列表删除
// exportAssets(data){ return request({url: '/exportDataV2', method: 'post', data})} // 认领、已知导出

// ##################################漏洞列表
export function getVulnerability(data) {
  return request({ url: '/threats/task', method: 'get', params: data })
}
//Poc进行单ip扫描
export function singlePocScan(data) {
  return request({ url: '/poc/scan', method: 'get', params: data })
}
export function addVulnerability(data) {
  return request({ url: '/threats/task', method: 'post', data })
}
export function getVulnerabilityDetails(data) {
  return request({
    url: `/threats/task/${data.id}?operate_company_id=${data.operate_company_id}`,
    method: 'get'
  })
} //漏洞详情
export function getVulnerabilityCycle(data) {
  return request({ url: '/threats/task/cycle', method: 'get', params: data })
} // 漏洞周期任务列表
export function remind(data) {
  return request({ url: `/threats/task/${data}/remind`, method: 'put', data })
} // 单次任务提醒钉钉机器人
export function remindCycle(data) {
  return request({ url: `/threats/task/cycle/${data}/remind`, method: 'put', data })
} // 周期任务提醒钉钉机器人
export function addCycle(data) {
  return request({ url: '/threats/task/cycle', method: 'post', data })
} // 添加周期任务
export function getIps(data) {
  return request({ url: '/threats/task/ips', method: 'get', params: data })
} // 获取漏洞扫描已认领资产ip
export function getDomains(data) {
  return request({ url: '/threats/task/domains', method: 'get', params: data })
} // 获取漏洞扫描已认领资产域名
export function deleteVulnerability(data) {
  return request({ url: '/threats/task', method: 'delete', data })
} //删除单次任务
export function deleteVulnerabilityCycle(data) {
  return request({ url: '/threats/task/cycle', method: 'delete', data })
} //删除周期任务
export function getVulnerabilityCycleDetails(data) {
  return request({
    url: `/threats/task/cycle/${data.id}?operate_company_id=${data.operate_company_id}`,
    method: 'get'
  })
} // 周期任务详情

// ##################################资产扫描核对
export function getScanList(data) {
  return request({
    url: `/assets/scan/check/${data.flag}`,
    method: 'get',
    params: data.query,
    is_golang: 1
  })
} //获取列表
export function delScanList(data) {
  return request({
    url: `/assets/scan/check/${data.flag}`,
    method: 'delete',
    data: data.data,
    is_golang: 1
  })
} // 删除推荐记录列表/评估列表删除
export function updateLevelList(data) {
  return request({ url: `/assets/scan/update/${data.flag}`, method: 'post', data: data.query })
} // 评估列表切换等级
export function updateLevelListV1(data) {
  return request({
    url: `/detect_assets/assets_confidence_level/update/${data.flag}`,
    method: 'post',
    data: data.query,
    is_golang: 1
  })
} // 评估列表切换等级---新
export function exportScanLis(data) {
  return request({
    url: '/assets/scan/check/' + data.flag + '/export',
    method: 'post',
    params: data.query
  })
} // 扫描资产推荐列表-导出

// ##################################新版报告管理-报告模板
export function getReportsTemplate(data) {
  return request({ url: '/reports_template', method: 'get', params: data })
} // 获取报告模板列表
export function addReportsTemplate(data) {
  return request({ url: '/reports_template', method: 'post', data })
} // 报告模板新建
export function editReportsTemplate(data) {
  return request({ url: '/reports_template/' + data.id, method: 'put', data: data.data })
} // 报告模板编辑
export function deleteReportsTemplate(data) {
  return request({ url: '/reports_template', method: 'delete', data })
} // 报告模板删除
export function infoReportsTemplate(data) {
  return request({ url: '/reports_template/' + data, method: 'get' })
} // 获取报告模板详情，用于编辑
export function changeReportsList(data) {
  return request({ url: '/assets_change_reports', method: 'get', params: data })
} // 报告模板数据筛选集合接口
export function createChangeReports(data) {
  return request({ url: '/create_assets_change_reports', method: 'post', data })
} // 报告模板数据筛选集合接口
export function deleteChangeReports(data) {
  return request({ url: '/delete_assets_change_reports', method: 'delete', data })
} // 报告模板数据筛选集合接口
// ##################################新版报告管理-报告列表
export function getReportsList(data) {
  return request({ url: '/reports', method: 'get', params: data })
} // 获取报告列表
export function delReports(data) {
  return request({ url: '/reports', method: 'delete', data })
} // 删除报告
export function addReports(data) {
  return request({ url: '/reports', method: 'post', data })
} // 生成报告

// ##################################资产概览
export function getOverviewStatistics(data) {
  return request({ url: '/assets_overview/assets/count', method: 'get', params: data })
} //资产概览-数量统计
export function getOverviewTypeCount(data) {
  return request({ url: '/assets_overview/assets_type/count', method: 'get', params: data })
} //资产概览-分类统计
export function getOverviewTrend(data) {
  return request({ url: '/assets_overview/assets/change_history', method: 'get', params: data })
} //资产概览-变化趋势
export function getOverviewDigital(data) {
  return request({ url: '/assets_overview/new_assets/count', method: 'get', params: data })
} //资产概览-数字资产统计
export function getOverviewLoophole(data) {
  return request({ url: '/assets_overview/poc/count', method: 'get', params: data })
} //资产概览-漏洞top5
export function getNewCloues(data) {
  return request({ url: '/detect_assets/clue/wait_confirm', method: 'get', params: data })
} //获取新增的线索个数
export function cdnAssetList(data) {
  return request({ url: '/assets/cdn_assets', method: 'get', params: data })
} //cdn资产，
// ##################################事件告警
export function eventWaringList(data) {
  return request({ url: '/event_warning/list', method: 'get', params: data })
} // 事件告警列表
export function delEventWaring(data) {
  return request({ url: '/event_warning/list', method: 'delete', data })
} // 事件告警列表删除
export function handleEventWaring(data) {
  return request({ url: '/event_warning/list', method: 'put', data })
} // 事件告警列表确认/忽略
export function exportEventWaring(data) {
  return request({ url: '/event_warning/list/export', method: 'get', params: data })
} // 事件告警列表导出
export function eventWaringInfo(data) {
  return request({ url: '/event_warning/' + data.id, method: 'get', params: data.data })
} // 事件告警列表-详情
export function eventWaringInfoDel(data) {
  return request({ url: '/event_warning/delete_ip', method: 'delete', data })
} // 事件告警列表-详情
// ##################################事件规则管理
export function eventRuleList(data) {
  return request({ url: '/system_rule/list', method: 'get', params: data })
} // 内置列表
export function eventRuleEnble(data) {
  return request({ url: '/system_rule/list', method: 'put', data })
} // 禁用启用
export function customRuleList(data) {
  return request({ url: '/custom_rule/list', method: 'get', params: data })
} // 自定义规则列表
export function addCustomRule(data) {
  return request({ url: '/custom_rule/list', method: 'post', data })
} // 添加自定义规则列表
export function delCustomRule(data) {
  return request({ url: '/custom_rule/list', method: 'delete', data })
} // 删除自定义规则列表
export function passCustomRule(data) {
  return request({ url: '/custom_rule/list', method: 'put', data })
} // 规则审核
export function delAdminRule(data) {
  return request({ url: '/custom_rule/admin', method: 'delete', data })
} // 内置规则删除
export function eventRuleListV1(data) {
  return request({ url: '/system_rule/list', method: 'get', params: data, is_golang: 1 })
} // 规则库/内置列表
export function addEventRuleList(data) {
  return request({ url: '/system_rule/list', method: 'post', data, is_golang: 1 })
} //规则库添加规则
export function editEventRuleList(data) {
  return request({ url: '/system_rule/list', method: 'put', data, is_golang: 1 })
} //规则库修改规则
export function delAdminRuleV1(data) {
  return request({ url: '/system_rule/list', method: 'delete', data, is_golang: 1 })
} //规则库删除规则
export function updateEventRuleStatus(data) {
  return request({ url: '/system_rule/status', method: 'put', data, is_golang: 1 })
} //更新规则状态
export function categoryList(data) {
  return request({ url: '/system_rule/category', method: 'get', params: data, is_golang: 1 })
} //风险分类列表
// ##################################黑ip封禁管理
export function blackIpList(data) {
  return request({ url: '/black_ips', method: 'get', params: data })
} // 列表
export function delBlackIpList(data) {
  return request({ url: '/black_ips', method: 'delete', params: data })
} // 删除列表
export function blackIpTaskList(data) {
  return request({ url: '/black_ips/task', method: 'get', params: data })
} // 任务列表
export function createBlackIpTask(data) {
  return request({ url: '/black_ips/task', method: 'post', data })
} // 下发任务
export function delBlackIpTask(data) {
  return request({ url: '/black_ips/task', method: 'delete', data })
} // 删除任务

// ##################################登录入口
export function loginEntyList(data) {
  return request({ url: '/login_assets/list', method: 'get', params: data, is_golang: 1 })
} // 登录入口列表
export function loginEntyCount(data) {
  return request({ url: '/login_assets/count', method: 'get', params: data, is_golang: 1 })
} // 登录入口列表
export function loginEntyStatus(data) {
  return request({ url: '/login_assets/list', method: 'put', data, is_golang: 1 })
}
export function delLoginEnty(data) {
  return request({ url: '/login_assets/list', method: 'delete', data, is_golang: 1 })
}
export function editLoginEnty(data) {
  return request({ url: '/login_assets', method: 'post', data, is_golang: 1 })
} // 编辑
export function uploadLoginUrl(data) {
  return request({ url: '/login_assets/upload/sure', method: 'post', data, is_golang: 1 })
} // 单个导入
export function uploadLoginExport(data) {
  return request({ url: '/login_assets/list/export', method: 'post', data, is_golang: 1 })
} // 导出
// ##################################推荐资产库
export function couldIpListAll(data) {
  return request({ url: '/assets/scan/all', method: 'get', params: data })
}
export function exportCouldIpList(data) {
  return request({ url: '/assets/scan/all/export', method: 'post', data })
}
// ##################################黑白名单列表
export function blackAndwhiteList(data) {
  return request({ url: '/leakage/keyword', method: 'get', params: data })
}
export function blackAndwhiteDel(data) {
  return request({ url: '/leakage/keyword', method: 'delete', data })
}
export function blackAndwhiteAdd(data) {
  return request({ url: '/leakage/keyword', method: 'post', data })
} //返回
// ##################################证书资产
export function certAssetsList(data) {
  return request({ url: '/cert_assets', method: 'get', params: data, is_golang: 1 })
} // 列表
export function certAssetsInfo(data) {
  return request({ url: '/cert_assets/' + data.id, method: 'get', params: data.data, is_golang: 1 })
} // 列表详情
export function delCertAssets(data) {
  return request({ url: '/cert_assets', method: 'delete', data, is_golang: 1 })
} // 列表删除
export function exportCertAssets(data) {
  return request({ url: '/cert_assets/export', method: 'post', data, is_golang: 1 })
} // 列表导出

// ##################################升级中心 ##################################升级中心FD01
export function getUploadList(data) {
  return request({ url: '/versions', method: 'get', params: data })
} //升级列表
export function setUploadFiles(data) {
  return request({ url: '/versions', method: 'post', data })
} //升级上传
export function delUploadFiles(data) {
  return request({ url: '/versions/' + data.id, method: 'delete', data })
} //升级列表删除

// ##################################资产核查任务
export function startAudit(data) {
  return request({ url: '/asset_audit/task/create', method: 'post', data, is_golang: 1 })
} // 开始核查
export function auditResult(data) {
  return request({ url: '/asset_audit/result/list', method: 'get', params: data, is_golang: 1 })
} // 核查结果
export function getAuditTask(data) {
  return request({
    url: '/asset_audit/task/list',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 任务记录
export function delAuditTask(data) {
  return request({ url: '/asset_audit/task/delete', method: 'delete', data, is_golang: 1 })
} // 删除任务记录
export function auditTaskInfo(data) {
  return request({ url: '/asset_audit/task/progress', method: 'get', params: data, is_golang: 1 })
} // 获取任务进度
export function auditDownload(data) {
  return request({ url: '/asset_audit/result/download', method: 'get', params: data, is_golang: 1 })
} // 下载
export function auditSyncAssets(data) {
  return request({ url: '/asset_audit/result/sync_assets', method: 'post', data, is_golang: 1 })
} // 核查后同步台账
export function finishAudit(data) {
  return request({ url: '/asset_audit/task/finished', method: 'put', data, is_golang: 1 })
} // 核查后点击完成
export function fileCheck(data) {
  return request({ url: '/asset_audit/file_check', method: 'get', params: data, is_golang: 1 })
} // 核查上传后文件校验

// ##################################资产状态检测
export function startDetect(data) {
  return request({ url: '/assets_status_detect/start', method: 'post', data, is_golang: 1 })
} // 开始检测
export function detectResult(data) {
  return request({ url: '/assets_status_detect/info', method: 'get', params: data, is_golang: 1 })
} // 检测结果
export function getDetectTask(data) {
  return request({
    url: '/assets_status_detect/list',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 检测任务记录
export function delCheckDetectTask(data) {
  return request({ url: '/assets_status_detect/del', method: 'delete', data, is_golang: 1 })
} // 删除检测任务记录
export function statusDetectTaskInfo(data) {
  return request({ url: '/assets_status_detect/status', method: 'get', params: data, is_golang: 1 })
} // 获取任务进度
export function detectDownload(data) {
  return request({
    url: '/assets_status_detect/download',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 下载
export function urlStartDetect(data) {
  return request({ url: '/url_status_detect/start', method: 'post', data, is_golang: 1 })
} // url访问状态开始检测
export function urlStatusDetectTaskInfo(data) {
  return request({ url: '/url_status_detect/status', method: 'get', params: data, is_golang: 1 })
} // 获取任务进度
export function urlDetectResult(data) {
  return request({ url: '/url_status_detect/info', method: 'get', params: data, is_golang: 1 })
} // 检测结果
export function getUrlDetectTask(data) {
  return request({ url: '/url_status_detect/list', method: 'get', params: data, is_golang: 1 })
} // 检测结果
export function urlDetectDownload(data) {
  return request({ url: '/url_status_detect/download', method: 'get', params: data, is_golang: 1 })
} // 下载

// ##################################任务概览
export function taskViewStatusCount(data) {
  return request({ url: '/task_overview/count', method: 'get', params: data, is_golang: 1 })
} // 任务统计-根据状态
export function taskViewTypeCount(data) {
  return request({ url: '/task_overview/count_by', method: 'get', params: data, is_golang: 1 })
} // 任务统计-根据任务类别
// ##################################资产概览
// assetsViewRuleCount(data) { return request({ url: '/assets_overview/rule_count', method: 'get', params: data, is_golang: 1 }) } // 组件统计
export function assetsViewRuleCount(data) {
  return request({ url: '/assets_overview/rule_count', method: 'get', params: data })
} // 组件统计 最新
export function assetsViewDigitalCount(data) {
  return request({
    url: '/assets_overview/digital_assets',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 数字资产统计
export function assetsViewRateCount(data) {
  return request({ url: '/assets_overview/rate', method: 'get', params: data, is_golang: 1 })
} // 环比统计
export function assetsDynamicList(data) {
  return request({
    url: '/assets_overview/assets_dynamic',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 资产动态
export function assetsDynamicCount(data) {
  return request({
    url: '/assets_overview/assets_dynamic_change',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 端口变更、组件变更、漏洞变更、风险事件
//##################################公告管理
export function publicNoticeList(data) {
  return request({ url: '/public_notice/list', method: 'get', params: data, is_golang: 1 })
} // 公告列表
export function addPublicNotice(data) {
  return request({ url: '/public_notice/add', method: 'post', data, is_golang: 1 })
} // 公告列表添加
export function editPublicNotice(data) {
  return request({ url: '/public_notice/save', method: 'post', data, is_golang: 1 })
} // 公告列表添加
export function latestPublicNotice(data) {
  return request({ url: '/public_notice/latest', method: 'get', is_golang: 1 })
} // 获取最新公告升级内容
export function has_logged_in(data) {
  return request({ url: '/public_notice/has_logged_in', method: 'get', is_golang: 1 })
} // 判断当前登录时间是否为上线前5天

// FD01和foradar本地化接口
export function getupgradeList(data) {
  return request({ url: '/upgrade/versions', method: 'get', params: data })
} //本地化在线升级列表不分页
export function getupgradeHistory(data) {
  return request({ url: '/upgrade/records', method: 'get', params: data })
} //本地化升级记录
export function getLineUpdate(data) {
  return request({ url: '/upgrade/download', method: 'post', data })
} //本地化在线升级列表-点击升级
export function getUpdateInfo(data) {
  return request({ url: '/upgrade/get_upgrade_info', method: 'get', params: data })
} //升级进度管理
// ##################################Foradar的升级中心
export function getUploadListForadar(data) {
  return request({ url: '/foradar/versions', method: 'get', params: data })
} // 升级列表
export function delUploadListForadar(data) {
  return request({ url: '/foradar/versions/' + data.id, method: 'delete', data: data.data })
} // 升级列表
export function eidtUploadFiles(data) {
  return request({ url: '/foradar/versions', method: 'post', data })
} // 上传和修改升级包
export function getbatchList(data) {
  return request({ url: '/foradar/versions/batch', method: 'get', params: data })
} // 批次号列表

// ##################################售后菜单管理
export function golang_client_account(data) {
  return request({ url: '/golang_client_account', method: 'get', params: data })
}
export function add_golang_client_account(data) {
  return request({ url: '/golang_client_account', method: 'post', data })
}
export function scope_golang_client_account(data) {
  return request({ url: '/auth/scopes', method: 'get', params: data, is_golang: 1 })
}
//##################################漏洞审核
export function pocAuditList(data) {
  return request({ url: '/vulnerability_audit', method: 'get', params: data })
} // 漏洞审核列表
export function pocAudit(data) {
  return request({ url: '/vulnerability_audit', method: 'post', data })
} // 漏洞审核
export function pocAuditCheck(data) {
  return request({ url: '/vulnerability_audit/check', method: 'post', data })
} // 漏洞审核列表-核查功能
// ##################################业务系统
export function businessInsert(data) {
  return request({ url: '/system_detect/create', method: 'post', data, is_golang: 1 })
} // 导入确定
export function businessEdit(data) {
  return request({ url: '/system_detect/update', method: 'put', data, is_golang: 1 })
} // 编辑
export function businessDel(data) {
  return request({ url: '/system_detect/delete', method: 'delete', data, is_golang: 1 })
} // 删除
export function businessDelPhp(data) {
  return request({ url: '/bussiness_system/delete_record', method: 'delete', data })
} // php删除记录
export function businessList(data) {
  return request({ url: '/system_detect/list', method: 'post', data, is_golang: 1 })
} // 列表
export function businessExport(data) {
  return request({ url: '/system_detect/download', method: 'post', data, is_golang: 1 })
} // 列表导出
export function businessSyncAssets(data) {
  return request({ url: '/system_detect/sync_assets', method: 'post', data, is_golang: 1 })
} // 同步台账
export function businessAssociation(data) {
  return request({ url: '/system_detect/ip_association', method: 'put', data, is_golang: 1 })
} // 关联台账
export function businessProgress(data) {
  return request({ url: '/system_detect/progress', method: 'get', params: data, is_golang: 1 })
} // 业务系统资产探测进度

// ##################################在线用户
export function userOnlineList(data) {
  return request({ url: '/user/online_list', method: 'get', params: data, is_golang: 1 })
} // 列表

// ##################################数据泄露总库
export function dataLeakTaskList(data) {
  return request({ url: '/dlp/task/result', method: 'post', data, is_golang: 1 })
} // 数据泄露总库列表
export function dataLeakTaskEdit(data) {
  return request({ url: '/dlp/task/result', method: 'put', data, is_golang: 1 })
} // 数据泄露总库编辑
export function dataLeakTaskAgg(data) {
  return request({ url: '/dlp/task/agg', method: 'get', params: data, is_golang: 1 })
} // 数据泄露总库筛选关键字枚举
// ##################################定时任务
export function cronTaskList(data) {
  return request({ url: '/cron/', method: 'get', params: data, is_golang: 1 })
} // 定时任务列表
export function addCronTask(data) {
  return request({ url: '/cron/', method: 'post', data, is_golang: 1 })
} // 添加定时任务
export function editCronTask(data) {
  return request({ url: '/cron/' + data.id, method: 'post', data, is_golang: 1 })
} // 编辑定时任务
export function delCronTask(data) {
  return request({ url: '/cron/', method: 'delete', data, is_golang: 1 })
} // 删除定时任务
// ##################################定时任务执行记录
export function cronTaskHistoryList(data) {
  return request({
    url: `/cron/${data.id}/history`,
    method: 'get',
    params: data.data,
    is_golang: 1
  })
} // 定时任务执行记录列表
export function delCronTaskHistory(data) {
  return request({ url: `/cron/${data.id}/history`, method: 'delete', is_golang: 1 })
} // 清除任务执行记录
// ##################################线索扩展任务管理
export function clueExpandTaskList(data) {
  return request({ url: '/clue/expand/task', method: 'get', params: data, is_golang: 1 })
} // 线索扩展任务列表
export function clueExpandTaskResult(data) {
  return request({ url: '/clue/expand/result', method: 'post', data, is_golang: 1 })
} // 线索扩展结果
export function clueExpandTaskIcp(data) {
  return request({ url: '/clue/expand/icp', method: 'post', data, is_golang: 1 })
} // 线索扩展-icp
export function clueExpandTaskDomain(data) {
  return request({ url: '/clue/expand/domain', method: 'post', data, is_golang: 1 })
} // 线索扩展-domain
export function clueExpandTaskSubdomain(data) {
  return request({ url: '/clue/expand/subdomain', method: 'post', data, is_golang: 1 })
} // 线索扩展-subdomain
export function clueExpandTaskKeyword(data) {
  return request({ url: '/clue/expand/keyword', method: 'post', data, is_golang: 1 })
} // 线索扩展-keyword
export function clueExpandTaskIp(data) {
  return request({ url: '/clue/expand/ip', method: 'post', data, is_golang: 1 })
} // 线索扩展-ip
export function clueExpandTaskCert(data) {
  return request({ url: '/clue/expand/cert', method: 'post', data, is_golang: 1 })
} // 线索扩展-cert
export function clueExpandTaskIcon(data) {
  return request({ url: '/clue/expand/icon', method: 'post', data, is_golang: 1 })
} // 线索扩展-icon
export function clueExpandTaskCompany(data) {
  return request({ url: '/clue/expand/company', method: 'post', data, is_golang: 1 })
} // 线索扩展-company
// ##################################配置管理
export function configList(data) {
  return request({ url: '/config/', method: 'get', params: data, is_golang: 1 })
} // 配置管理列表
export function updateConfig(data) {
  return request({ url: '/config/', method: 'post', data, is_golang: 1 })
} // 配置更新
// ##################################自定义规则
export function ruleList(data) {
  return request({ url: '/rules/list', method: 'get', params: data })
} // 自定义规则列表
export function ruleType(data) {
  return request({ url: '/rules/type', method: 'get', params: data })
} // 自定义规则层级列表
export function ruleAdd(data) {
  return request({ url: '/rules/list', method: 'post', data })
} // 新增自定义规则列表
export function ruleEdit(data) {
  return request({ url: '/rules/list', method: 'put', data })
} // 编辑自定义规则列表
export function ruleDel(data) {
  return request({ url: '/rules/list', method: 'delete', data })
} // 删除自定义规则列表
// ##################################集团企业资产梳理
export function organizationGroupList(data) {
  return request({ url: '/organization/group/list', method: 'get', params: data })
} // 账号下的一级企业列表
export function organizationCompanyLevelOne(data) {
  return request({ url: '/organization/company_level_one', method: 'get', params: data })
} // 组织架构列表
export function addCompany(data) {
  return request({ url: '/organization/group', method: 'post', data })
} // 组织架构列表
export function groupImport(data) {
  return request({ url: '/organization/group/import', method: 'post', data })
} // 组织架构列表
export function nextCompany(data) {
  return request({ url: '/organization/company/list', method: 'get', params: data })
} // 获取下级企业
export function setNextCompany(data) {
  return request({ url: '/organization/company/set_level', method: 'post', data })
} // 组织架构列表
export function delNextCompany(data) {
  return request({ url: '/organization/company', method: 'delete', data })
} // 组织架构列表
export function nextCompanySon(data) {
  return request({ url: '/organization/company/son', method: 'get', params: data })
} // 组织架构列表
export function groupConfirm(data) {
  return request({ url: '/organization/group/confirm', method: 'post', data })
} // 组织架构列表
export function otherGroupCompany(data) {
  return request({ url: '/organization/other_group/company', method: 'get', params: data })
} // 组织架构列表
export function addOneCompany(data) {
  return request({ url: '/organization/company/add', method: 'post', data })
} // 组织架构列表
export function delOneCompany(data) {
  return request({ url: '/organization/company', method: 'delete', data })
} // 删除子节点
export function organizationTask(data) {
  return request({ url: '/organization/task', method: 'post', data })
} // 删除子节点
export function organizationTaskInfo(data) {
  return request({ url: '/organization/task/info/' + data.id, method: 'get', params: data })
} // 删除子节点
export function organizationAssetsList(data) {
  return request({ url: '/organization/assets/' + data.flag, method: 'get', params: data })
} // 删除子节点
export function organizationCompanyClues(data) {
  return request({ url: '/organization/company/clues', method: 'get', params: data })
} // 删除子节点
export function organizationUnAssets(data) {
  return request({ url: '/organization/un_assets', method: 'post', data })
} // 规则筛选并移除
export function organizationAssetsExport(data) {
  return request({ url: `/organization/assets/${data.flag}/export`, method: 'post', data })
} // 企业资产台账导出
export function orgClueList(data) {
  return request({ url: `/organization/company/clues`, method: 'get', params: data })
} // 线索列表
export function orgAssetsList(data) {
  return request({ url: `/organization/company/assets`, method: 'get', params: data })
} // 资产列表
export function orgCluesList(data) {
  return request({ url: `/organization/assets_clues`, method: 'get', params: data })
} // 任务中获取所有的线索
export function orgDomainsList1(data) {
  return request({ url: `/organization/assets_domains`, method: 'get', params: data })
} // 任务中获取所有的线索
export function orgDomainsList(data) {
  return request({ url: `/organization/company/domains`, method: 'get', params: data })
} // 资产列表
export function orgExportAll(data) {
  return request({ url: '/organization/export_all', method: 'post', data })
} // 梳理最终结果导出
export function orgTaskList(data) {
  return request({ url: '/organization/task', method: 'get', params: data })
} // 集团资产梳理任务记录列表
export function orgTaskDelete(data) {
  return request({ url: '/organization/task', method: 'delete', data })
} // 集团资产梳理任务记录列表
export function orgTask2Exports(data) {
  return request({ url: `/organization/assets/${data.flag}/export`, method: 'post', data })
} // 集团资产梳理任务记录导出
export function orgTask2Delete(data) {
  return request({ url: `/organization/assets/${data.flag}`, method: 'delete', data })
} // 集团资产梳理任务记录删除
export function orgTask2Condition(data) {
  return request({ url: `organization/assets_condition/${data.flag}`, method: 'get', params: data })
} // 集团资产梳理任务记录删除
export function orgTaskFinish(data) {
  return request({ url: '/organization/task/finish', method: 'post', data })
} // 集团资产梳理任务手动完成结束
// orgTaskFinish(data) { return request({ url: '/organization/task/finish', method: 'post', data }) } // 集团资产梳理推荐记录列表
export function orgAssetsRecommend(data) {
  return request({ url: `/organization/assets/${data.flag}`, method: 'get', params: data })
} // 集团资产梳理推荐记录列表
export function orgGroupDel(data) {
  return request({ url: `/organization/group`, method: 'delete', data })
} // 集团资产梳理推荐记录列表
export function orgTaskClueList(data) {
  return request({ url: `/organization/org_task/clue_list`, method: 'get', params: data })
} // 集团资产梳理线索总表
export function orgTaskDeleteClue(data) {
  return request({ url: `/organization/org_task/delete_clue_list`, method: 'delete', data })
} // 集团资产梳理线索总表
export function orgTaskAddClue(data) {
  return request({ url: `/organization/org_task/add_clue_list`, method: 'post', data })
} // 集团资产梳理线索总表
export function orgTaskRecommendAssets(data) {
  return request({ url: `/organization/org_task/recommend_assets`, method: 'post', data })
} // 集团资产梳理下发推荐资产
export function orgTaskUpdateLevel(data) {
  return request({ url: `/organization/org_task/update_assets_level`, method: 'post', data })
} // 集团资产梳理下发信任度评估
export function orgTaskScanAssets(data) {
  return request({ url: `/organization/org_task/scan_assets`, method: 'post', data })
} // 集团资产梳理下发资产入账扫描
export function orgTaskScanAssetsData(data) {
  return request({ url: `/organization/org_task/assets`, method: 'get', params: data })
} // 集团资产梳理获取资产入账扫描后的数据
export function orgGroupAddOther(data) {
  return request({ url: `/organization/group`, method: 'post', data })
} // 集团资产梳理推荐记录列表
export function orgCluesExport(data) {
  return request({ url: `/organization/company/clues_export`, method: 'get', params: data })
} // 组织架构线索导出
export function orgDomainsExport(data) {
  return request({ url: `/organization/company/domains_export`, method: 'get', params: data })
} // 组织架构域名导出
export function orgAssetsExport(data) {
  return request({ url: `/organization/company/assets_export`, method: 'get', params: data })
} // 组织架构资产导出
export function orgCompanyTag(data) {
  return request({ url: '/organization/company/edit_tag', method: 'post', data })
} // 集团资产企业标记标签
export function orgCompanyInfoEdit(data) {
  return request({ url: '/organization/company/info_edit', method: 'post', data })
} // 集团资产企业信息编辑
export function orgCompanyAssetsNum(data) {
  return request({ url: '/organization/company/assets_count', method: 'get', params: data })
} // 集团资产企业信息编辑
export function orgCompanyAssetsDel(data) {
  return request({ url: '/organization/company/assets_delete', method: 'delete', data })
} // 集团资产企业ip资产删除
export function orgCompanyDomainDel(data) {
  return request({ url: '/organization/company/domain_delete', method: 'delete', data })
} // 集团资产企业域名删除
export function orgOtherAssetsList(data) {
  return request({ url: '/organization/group/other_assets', method: 'get', params: data })
} // 集团其他分组的IP资产
export function orgOtherDomainList(data) {
  return request({ url: '/organization/group/other_domain', method: 'get', params: data })
} // 集团其他分组的域名
export function orgOtherSync(data) {
  return request({ url: 'organization/group/other_sync', method: 'get', params: data })
} // 集团其他分组的域名
// #################################网口管理
export function networkList(data) {
  return request({ url: '/network', method: 'get', params: data })
} // 网口管理列表不分页
export function addNetworkList(data) {
  return request({ url: '/network', method: 'post', data })
} // 网口管理新增
export function editNetworkList(data) {
  return request({ url: '/network', method: 'put', data })
} // 网口管理编辑
// #################################空间检索
export function assetsSearchList(data) {
  return request({ url: '/fofa/parse_query', method: 'get', params: data, is_golang: 1 })
} // 空间检索列表
export function statisticalList(data) {
  return request({ url: '/fofa/parse_statistics', method: 'get', params: data, is_golang: 1 })
} // 静态数据列表
// #################################威胁词库
export function pgdAuditList(data) {
  return request({ url: '/manage/pgd_keyword/audit', method: 'get', params: data, is_golang: 1 })
} // 审核列表
export function pgdAuditAction(data) {
  return request({ url: '/manage/pgd_keyword/audit', method: 'put', data, is_golang: 1 })
} // 审核列表
export function pgdSystemList(data) {
  return request({ url: '/manage/pgd_keyword/system', method: 'get', params: data, is_golang: 1 })
} // 总库列表
export function pgdSystemAdd(data) {
  return request({ url: '/manage/pgd_keyword/system', method: 'post', data, is_golang: 1 })
} // 总库列表新增
export function pgdSystemDel(data) {
  return request({ url: '/manage/pgd_keyword/system', method: 'delete', data, is_golang: 1 })
} // 总库列表删除
export function pgdTypeAdd(data) {
  return request({ url: '/manage/pgd_keyword/type', method: 'post', data, is_golang: 1 })
} // 总库列表分类新增
export function pgdTypeList(data) {
  return request({ url: '/manage/pgd_keyword/type', method: 'get', params: data, is_golang: 1 })
} // 总库列表分类列表
export function pgdUserList(data) {
  return request({ url: '/user/list', method: 'get', params: data, is_golang: 1 })
} // 获取用户列表
export function pgdStatusUpdate(data) {
  return request({ url: '/manage/pgd_keyword/system', method: 'put', data, is_golang: 1 })
} // 获取用户列表

// #################################数字资产总库
export function digitalAssetTaskList(data) {
  return request({
    url: '/manage/digital_asset/task/list',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 任务列表
export function digitalAssetTaskResList(data) {
  return request({
    url: '/manage/digital_asset/task_result',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 任务详情列表
export function digitalAssetTaskInfo(data) {
  return request({ url: '/manage/digital_asset/task', method: 'get', params: data, is_golang: 1 })
} // 任务列表新建任务
export function digitalAssetTask(data) {
  return request({ url: '/manage/digital_asset/task', method: 'post', data, is_golang: 1 })
} // 任务列表新建任务
export function digitalAssetTaskDel(data) {
  return request({ url: '/manage/digital_asset/task', method: 'delete', data, is_golang: 1 })
} // 任务列表删除任务
export function digitalAssetList(data) {
  return request({
    url: '/manage/digital_asset/assets/list',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 数据管理列表
export function digitalAssetDataFilter(data) {
  return request({ url: '/manage/digital_asset/filter_group', method: 'post', data, is_golang: 1 })
} // 数字资产下拉列表
export function digitalAssetDataAdd(data) {
  return request({ url: '/manage/digital_asset/assets', method: 'post', data, is_golang: 1 })
} // 数据管理新增
export function digitalAssetDataEdit(data) {
  return request({ url: '/manage/digital_asset/assets', method: 'put', data, is_golang: 1 })
} // 数据管理编辑
export function digitalAssetDataImport(data) {
  return request({ url: '/manage/digital_asset/assets/import', method: 'post', data, is_golang: 1 })
} // 数据管理批量导入
export function digitalAssetDataDelete(data) {
  return request({ url: '/manage/digital_asset/assets', method: 'delete', data, is_golang: 1 })
} // 数据管理批量导入

// ##################################工作台
export function announcementList(data) {
  return request({ url: '/public_notice', method: 'get', params: data })
} // 公告列表
export function worktableList(data) {
  return request({ url: '/work_table_statistics', method: 'get', params: data })
} // 工作台总数大全
export function dataBeachList(data) {
  return request({ url: '/work_table_leak_statistics', method: 'get', params: data })
} // 数据泄露数据
export function clueChart(data) {
  return request({ url: '/clue_statistics_chart', method: 'get', params: data })
} //线索柱状图
export function pocworkList(data) {
  return request({ url: '/work_table_poc_statistics', method: 'get', params: data })
} //线索柱状图
export function unitTask(data) {
  return request({ url: '/detect_info', method: 'get', params: data })
} // 单位资产测绘任务详情
export function recommendTask(data) {
  return request({ url: '/recommend_task_info', method: 'get', params: data })
} // 云端推荐任务详情
export function assetsScanTask(data) {
  return request({ url: '/scan_assets_task_info', method: 'get', params: data })
} // 资产扫描任务详情
export function domainScanTask(data) {
  return request({ url: '/domian_scan_task_info', method: 'get', params: data })
} // 域名发现任务详情
export function assetsCheckTask(data) {
  return request({ url: '/assets_check_task_info', method: 'get', params: data })
} // 资产核查任务详情
export function assetsOnlineTask(data) {
  return request({ url: '/assets_online_task_info', method: 'get', params: data })
} // 资产状态检测任务详情
export function pocTask(data) {
  return request({ url: '/poc_task_info', method: 'get', params: data })
} // 漏洞扫描任务详情
export function fakeDetectTask(data) {
  return request({ url: '/fake_detect_task_info', method: 'get', params: data })
} // 钓鱼仿冒任务详情
export function assetsBacthMatch(data) {
  return request({ url: '/unsure/assets/bacth_match', method: 'post', data, is_golang: 1 })
} // 一键操作未知资产匹配黄赌毒词库

// #################################ICP备案总库
export function icpDomainList(data) {
  return request({ url: '/manage/icp/domain', method: 'get', params: data, is_golang: 1 })
} // ICP备案域名列表
export function icpDomainAdd(data) {
  return request({ url: '/manage/icp/domain', method: 'post', data, is_golang: 1 })
} // ICP备案域名新增
export function icpDomainUpdate(data) {
  return request({ url: '/manage/icp/domain', method: 'put', data, is_golang: 1 })
} // ICP备案域名编辑、强制刷新
export function icpAppList(data) {
  return request({ url: '/manage/icp/app', method: 'get', params: data, is_golang: 1 })
} // ICP备案APP、小程序、快应用列表
export function icpAppAdd(data) {
  return request({ url: '/manage/icp/app', method: 'post', data, is_golang: 1 })
} // ICP备案APP、小程序、快应用新增
export function icpAppUpdate(data) {
  return request({ url: '/manage/icp/app', method: 'put', data, is_golang: 1 })
} // ICP备案APP、小程序、快应用编辑、强制刷新
// ############资产概览-数字资产
export function dataAssetsTable(data) {
  return request({ url: '/work_table_data_assets', method: 'get', params: data })
} // 数字资产列表

// #################################URL(API)资产
export function urlList(data) {
  return request({ url: '/url_api', method: 'get', params: data })
} // url资产列表
export function urlListExport(data) {
  return request({ url: '/export_url_api', method: 'post', data })
} // url资产导出
export function urlListImport(data) {
  return request({ url: '/import_url_api', method: 'post', data })
} // url资产导入
export function urlListAdd(data) {
  return request({ url: '/add_url_api', method: 'post', data })
} // url资产新增
export function urlListDel(data) {
  return request({ url: '/url_api', method: 'delete', data })
} // url资产删除
// #################################扫描任务
export function scanTasking(data) {
  return request({ url: '/scan_task_ing', method: 'get', params: data })
} // ICP备案APP、小程序、快应用编辑、强制刷新

// #################################数据同步设置
export function syncCopyAssets(data) {
  return request({ url: '/sync_copy/foradar_saas/assets', method: 'post', data })
} // 数据同步设置
export function syncCopyList(data) {
  return request({ url: 'sync_copy/foradar_saas/index', method: 'get', params: data })
} // 数据同步任务列表

// #################################情报中心
export function hotList(data) {
  return request({
    url: '/intelligence/hot-poc/user',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 热点情报列表
export function hotIpDetail(data) {
  return request({
    url: `/intelligence/hot-poc/${data.id}/asset`,
    method: 'get',
    params: data.query,
    is_golang: 1,
    is_new_params: 1
  })
} // 热点情报详情
export function hotCheck(data) {
  return request({
    url: '/intelligence/hot-poc/check',
    method: 'post',
    data,
    is_golang: 1,
    is_new_params: 1
  })
} // 热点情报一键检测
export function hotCheckIPList(data) {
  return request({
    url: '/intelligence/hot-poc/asset',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 热点情报一键检测ip列表
export function hotCheckIPExport(data) {
  return request({
    url: '/intelligence/hot-poc/asset/export',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 热点情报检测结果ip列表导出
export function hotCheckProgress(data) {
  return request({
    url: '/intelligence/hot-poc/check/process',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 热点情报一键检测进度查询
export function fakeList(data) {
  return request({
    url: '/intelligence/fake/user',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 钓鱼仿冒情报列表
export function threatList(data) {
  return request({
    url: '/intelligence/threat/user',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 威胁（风险）情报列表
export function threatMatch(data) {
  return request({ url: '/intelligence/threat/match', method: 'post', data, is_golang: 1 })
} // 威胁（风险）情报一键匹配
export function threatMatchProgress(data) {
  return request({
    url: '/intelligence/threat/match/process',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 威胁（风险）情报一键匹配
export function otherList(data) {
  return request({
    url: '/intelligence/other/user',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 其他情报列表
export function eventList(data) {
  return request({
    url: '/intelligence/event/user',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 专项情报列表
export function eventCategoryList(data) {
  return request({
    url: '/intelligence/event/category',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 专项情报-情报类别枚举值
export function eventListDetail(data) {
  return request({
    url: `/intelligence/event/${data.event_id}`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 专项情报列表
export function eventListMatch(data) {
  return request({
    url: '/intelligence/event/match',
    method: 'post',
    data,
    is_golang: 1,
    is_new_params: 1
  })
} // 专项情报-一键检测列表
export function eventListMatchProgress(data) {
  return request({
    url: '/intelligence/event/match/progress',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 专项情报-一键检测进度
export function dataList(data) {
  return request({
    url: '/intelligence/dataSummary/user',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 数据情报列表
export function dataListCondition(data) {
  return request({
    url: '/intelligence/dataSummary/condition',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 数据情报列表
export function dataListDetail(data) {
  return request({
    url: `/intelligence/dataSummary/${data.dataSummaryId}/list/user`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 数据情报-详情页
export function changeLevel(data) {
  return request({ url: '/intelligence/hot-poc/user', method: 'post', data, is_golang: 1 })
} // 更改危险等级
export function hotLevelCount(data) {
  return request({
    url: '/intelligence/hot-poc/count/level',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 热点漏洞-级别数量
export function eventIpCount(data) {
  return request({
    url: '/intelligence/count/event/ip',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 数据情报列表
export function dataListMatch(data) {
  return request({
    url: '/intelligence/data/match',
    method: 'post',
    data,
    is_golang: 1,
    is_new_params: 1
  })
} // 数据情报-一键检测
export function dataListMatchProgress(data) {
  return request({
    url: '/intelligence/data/match/progress',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 数据情报-一键检测结果进度查询
export function dataListReport(data) {
  return request({
    url: `/intelligence/data/${data.data_id}/report/original`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1,
    responseType: 'blob'
  })
} // 数据情报查看报告 原始
export function dataListMaskedReport(data) {
  return request({
    url: `/intelligence/data/${data.data_id}/report/masked`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1,
    responseType: 'blob'
  })
} // 数据情报查看报告 加密
export function eventListReport(data) {
  return request({
    url: `/intelligence/event/${data.data_id}/report/original`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1,
    responseType: 'blob'
  })
} // 事件情报查看报告 原始
export function eventListMaskedReport(data) {
  return request({
    url: `/intelligence/event/${data.data_id}/report/masked`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1,
    responseType: 'blob'
  })
} // 事件情报查看报告 加密
export function intelligenceReduceData(data) {
  return request({
    url: `/intelligence/event/warning`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 最近新增的数据
export function hotListReport(data) {
  return request({
    url: `/intelligence/hot-poc/${data.data_id}/report/original`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1,
    responseType: 'blob'
  })
} // 事件情报查看报告 原始
export function hotListMaskedReport(data) {
  return request({
    url: `/intelligence/hot-poc/${data.data_id}/report/masked`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1,
    responseType: 'blob'
  })
} // 事件情报查看报告 加密
export function hotTagList(data) {
  return request({
    url: `/intelligence/hot-poc/condition`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 热点漏洞标签下拉列表
// #################################情报中心--关联情报
export function intellRelatedList(data) {
  return request({
    url: `/intelligence/related/list`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 关联情报列表
export function intellRelatedListExport(data) {
  return request({
    url: `/intelligence/related/export`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 关联情报导出
export function intellRelatedListCondition(data) {
  return request({
    url: `/intelligence/related/condition`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 关联情报筛选项
export function intellDataDetail(data) {
  return request({
    url: `/intelligence/data/${data.id}`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 关联情报-数据情报类型详情
// #################################情报中心--后台管理
export function hotListManage(data) {
  return request({
    url: '/intelligence/hot-poc',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 热点情报列表
export function hotAdd(data) {
  return request({ url: '/intelligence/hot-poc', method: 'post', data, is_golang: 1 })
} // 热点情报新增
export function hotEdit(data) {
  return request({ url: '/intelligence/hot-poc/' + data.id, method: 'post', data, is_golang: 1 })
} // 热点情报编辑
export function fakeListManage(data) {
  return request({
    url: '/intelligence/fake',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 钓鱼仿冒情报列表
export function fakeAdd(data) {
  return request({ url: '/intelligence/fake', method: 'post', data, is_golang: 1 })
} // 钓鱼仿冒情报新增
export function fakeEdit(data) {
  return request({ url: '/intelligence/fake/' + data.id, method: 'post', data, is_golang: 1 })
} // 钓鱼仿冒情报编辑

export function threatListManage(data) {
  return request({
    url: '/intelligence/threat',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 威胁（风险）情报列表
export function threatAdd(data) {
  return request({ url: '/intelligence/threat', method: 'post', data, is_golang: 1 })
} // 威胁（风险）情报新增
export function threatEdit(data) {
  return request({ url: '/intelligence/threat/' + data.id, method: 'post', data, is_golang: 1 })
} // 威胁（风险）情报编辑

export function otherListManage(data) {
  return request({
    url: '/intelligence/other',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 其他情报列表
export function otherAdd(data) {
  return request({ url: '/intelligence/other', method: 'post', data, is_golang: 1 })
} // 其他情报信新增
export function otherEdit(data) {
  return request({ url: '/intelligence/other/' + data.id, method: 'post', data, is_golang: 1 })
} // 其他情报编辑
export function dataListManage(data) {
  return request({
    url: '/intelligence/dataSummary',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 数据情报列表
export function dataListDetailManage(data) {
  return request({
    url: `/intelligence/dataSummary/${data.dataSummaryId}/list`,
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 数据情报-详情页

export function eventListManage(data) {
  return request({
    url: '/intelligence/event',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 专项情报管理列表
export function intelligenceTypeCount(data) {
  return request({
    url: '/intelligence/count',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 情报类别数量统计
export function editEventCategory(data) {
  return request({
    url: `/intelligence/event/${data.event_id}/category`,
    method: 'put',
    data,
    is_golang: 1,
    is_new_params: 1
  })
} // 专项情报编辑类型

export function intellCompanyList(data) {
  return request({
    url: '/intelligence/company',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 关联主体单位下拉列表
// #################################站内信--后台管理
export function webMsgList(data) {
  return request({
    url: '/website_message/list',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 站内信列表
export function webMsgNotify(data) {
  return request({
    url: '/website_message/notify',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 站内信配置详情
export function setWebMsgNotify(data) {
  return request({ url: '/website_message/notify', method: 'post', data, is_golang: 1 })
} // 站内信配置设置
export function delWebMsgNotify(data) {
  return request({ url: '/website_message/list', method: 'delete', data, is_golang: 1 })
} // 站内信配置设置
export function getNewList(data) {
  return request({ url: '/website_message/newlist', method: 'get', params: data, is_golang: 1 })
} //上次登录以来产生的新数据
// ################################微服务列表
export function microServeList(data) {
  return request({ url: '/client_api_list', method: 'get', params: data })
} // 站内信列表

export function ipPortListData(data) {
  return request({ url: '/port_list_data', method: 'get', params: data })
} //根据id获取ip对应的所有端口列表

export function updateDomainAssetsRow(data) {
  return request({ url: '/domain_assets/update', method: 'post', data })
} // 导入域名后 更新字段使用
export function updateDomainAssetsByFOFA(data) {
  return request({ url: '/domain_assets/list/fofa_update', method: 'post', data, is_golang: 1 })
} // 去fofa更新域名信息

export function updateKeywordCrawler(data) {
  return request({ url: '/keyword_crawler_now', method: 'get', params: data })
} // 强制更新(只针对数据泄露)

export function businessAssetsSync(data) {
  return request({ url: '/table_bussiness_sync', method: 'post', data })
} // 一键拉取IP台账
export function businessAssetsUpdateInfo(data) {
  return request({ url: '/update_business_list', method: 'post', data })
} // 获取最新更新的数据
export function businessAssetsUpdate(data) {
  return request({ url: '/update_business_now', method: 'post', data })
} // 更新业务系统数据
export function businessHandleStatus(data) {
  return request({ url: '/system_detect/ignore_confirm', method: 'post', data, is_golang: 1 })
} // 业务系统资产处理（确认、忽略）

export function apiAnalysisTask(data) {
  return request({ url: '/api_analyze/task', method: 'post', data, is_golang: 1 })
} // 创建api分析任务
export function deleteAnalysisTask(data) {
  return request({ url: '/api_analyze/user_task', method: 'delete', data, is_golang: 1 })
} // 删除用户任务
export function apiAnalysisTaskList(data) {
  return request({
    url: '/api_analyze/list',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 获取任务列表
export function apiAnalysisTaskResult(data) {
  return request({
    url: '/api_analyze/result',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 获取任务结果
export function deleteAnalysisTaskResult(data) {
  return request({ url: '/api_analyze/task', method: 'delete', data, is_golang: 1 })
} // 删除用户任务结果
export function apiAnalysisTaskDetail(data) {
  return request({
    url: '/api_analyze/detail',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
} // 获取任务详情
export function apiAnalysisUserTask(data) {
  return request({
    url: '/api_analyze/user_task',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
}
export function exportAnalysisTask(data) {
  return request({ url: '/api_analyze/result/export', method: 'post', data, is_golang: 1 })
} // 创建api分析任务
