import request from '@/utils/request'
// import { login } from '@/api/apiConfig/asset.js'
// ##################################资产台账ip维度
export function getstatistics(data) {
  return request({ url: '/statistics', method: 'get', params: data, is_golang: 1 })
} // ip维度统计ip、组件、端口
export function exportDataV1(data) {
  return request({ url: '/assets/account/export', method: 'post', data, is_golang: 1 })
} // 台账导出
export function ansysDataIndexV1(data) {
  return request({ url: '/assets/account', method: 'get', params: data, is_golang: 1 })
} // 台账列表
export function deleteStandingDataV1(data) {
  return request({ url: '/assets/account', method: 'delete', data, is_golang: 1 })
} // 台账删除
export function editStandingDataV1(data) {
  return request({ url: '/assets/account/edit_standing_data', method: 'post', data })
} // ip详情编辑
export function ansysdataAgainConfirm(data) {
  return request({ url: '/assets/account/confirm', method: 'post', data, is_golang: 1 })
} // 资产确权
export function ipAssetsHandle(data) {
  return request({ url: '/table/assets/unsign', method: 'post', data, is_golang: 1 })
} // 资产处置
export function ipinfoVulnsList(data) {
  return request({ url: '/assets/account/ip_vulns', method: 'get', params: data, is_golang: 1 })
} // IP详情-情报漏洞
export function sureassetsstatistics(data) {
  return request({ url: '/sure/assets/statistics', method: 'get', params: data })
} // 获取资产确权待确认，已确认数量
export function heduiKnownAssetsV1(data) {
  return request({ url: '/assets/account/check', method: 'post', data, is_golang: 1 })
} // 核对台账
export function importSureIpDataV1(data) {
  return request({ url: '/assets/account/import', method: 'post', data, is_golang: 1 })
} // 台账导入并扫描确认
export function importKnownAssets(data) {
  return request({ url: '/assets/save_import', method: 'post', data, is_golang: 1 })
} // 台账导入确认
export function setAssetsDefaultTags(data) {
  return request({ url: '/tables_assets/set_tags', method: 'post', data, is_golang: 1 })
} // 自定义标签设置
export function tableIpCompanyName(data) {
  return request({ url: '/tables_assets/set_clue_company_name', method: 'post', data, is_golang: 1 })
} //批量或单个编辑企业名称

// ##################################资产台账ip+端口
export function countStandingBook(data) {
  return request({ url: '/assets/account/statistics', method: 'post', data })
} // ip+端口维度ip/端口/组件统计
export function ansysDataIndex(data) {
  return request({ url: '/assets/account/port', method: 'get', params: data, is_golang: 1 })
} //列表
export function deleteStandingData(data) {
  return request({ url: '/delForadarAssets', method: 'post', data })
} // 台账删除
export function exportData(data) {
  return request({ url: '/assets/account/port/export', method: 'post', data, is_golang: 1 })
} // 台账导出
export function importSureIpData(data) {
  return request({ url: '/assets/account/save_import_data', method: 'post', data })
} // 台账文件上传保存
export function heduiKnownAssets(data) {
  return request({ url: '/assets/account/port/check', method: 'post', data })
} // 已认领资产核对台账
export function deleteAssetsV1(data) {
  return request({ url: '/sure/assets_port', method: 'delete', data })
} //删除端口
export function deleteAssetsURLV1(data) {
  return request({ url: '/assets/account/host', method: 'delete', data })
} //删除URL
export function setBlackList(data) {
  return request({ url: '/title/set_black', method: 'post', data, is_golang: 1 })
} //标记关键词到黑名单
export function scanAssets(data) {
  return request({ url: '/assets/scan/check', method: 'post', data })
} //扫描入账ip
export function setBusinessTag(data) {
  return request({ url: '/url_to_bussiness_system', method: 'post', data })
} //标记到业务系统

// ##################################疑似资产
// #################IP维度
export function getSuspectedList(data) {
  return request({
    url: `/unsure/assets?page=${data.page}&per_page=${data.per_page}&operate_company_id=${data.operate_company_id}`,
    method: 'get',
    data
  })
}
export function exportSuspected(data) {
  return request({ url: '/unsure/assets/export', method: 'post', data, is_golang: 1 })
} //导出疑似资产
export function deleteSuspected(data) {
  return request({ url: '/unsure/assets', method: 'delete', data, is_golang: 1 })
} //删除
export function changeSuspected(data) {
  return request({ url: '/unsure/assets/sign', method: 'post', data, is_golang: 1 })
} //标记
export function filterSuspectedList(data) {
  return request({ url: '/unsure/assets', method: 'get', params: data, is_golang: 1 })
} //筛选
// #################IP+端口维度
export function getSuspectedListV1(data) {
  return request({
    url: `/unsure/passets?page=${data.page}&per_page=${data.per_page}&operate_company_id=${data.operate_company_id}`,
    method: 'get',
    params: data
  })
}
export function filterSuspectedListV1(data) {
  return request({ url: '/unsure/passets', method: 'get', params: data, is_golang: 1 })
} //筛选
export function exportSuspectedV1(data) {
  return request({ url: '/unsure/passets/export', method: 'post', data, is_golang: 1 })
} //导出疑似资产

// ##################################忽略资产
// #################IP维度
export function getNeglectList(data) {
  return request({
    url: `/ignore/assets?page=${data.page}&per_page=${data.per_page}&operate_company_id=${data.operate_company_id}`,
    method: 'get',
    data
  })
} //获取忽略资产列表
export function exportNeglect(data) {
  return request({ url: '/ignore/assets/export', method: 'post', data, is_golang: 1 })
} //导出忽略资产
export function deleteNeglect(data) {
  return request({ url: '/ignore/assets', method: 'delete', data, is_golang: 1 })
} //删除
export function changeNeglect(data) {
  return request({ url: '/ignore/assets/sign', method: 'post', data, is_golang: 1 })
} //标记
export function filterNeglectList(data) {
  return request({ url: '/ignore/assets', method: 'get', params: data, is_golang: 1 })
} //筛选
// #################IP+端口维度
export function getNeglectListV1(data) {
  return request({
    url: `/ignore/passets?page=${data.page}&per_page=${data.per_page}&operate_company_id=${data.operate_company_id}`,
    method: 'get',
    data
  })
}
export function filterNeglectListV1(data) {
  return request({ url: '/ignore/passets', method: 'get', params: data, is_golang:1 })
} //筛选
export function exportNeglectV1(data) {
  return request({ url: '/ignore/passets/export', method: 'post', data, is_golang:1 })
} //导出忽略资产

// ##################################威胁资产
// #################IP维度
export function getThreatenList(data) {
  return request({ url: '/threaten/assets', method: 'get', params: data, is_golang: 1 })
} //获取威胁资产列表
export function exportThreaten(data) {
  return request({ url: '/threaten/assets/export', method: 'post', data, is_golang:1 })
} //导出威胁资产
export function deleteThreaten(data) {
  return request({ url: '/threaten/assets', method: 'delete', data, is_golang:1 })
} //删除
export function changeThreaten(data) {
  return request({ url: '/threaten/assets/sign', method: 'post', data, is_golang: 1 })
} //标记
export function editThreatenType(data) {
  return request({ url: 'threaten/assets/set_threaten_type', method: 'post', data, is_golang: 1 })
} //编辑威胁类型
// #################IP+端口维度
export function getThreatenListV1(data) {
  return request({ url: '/threaten/passets', method: 'get', params: data, is_golang:1 })
}
export function exportThreatenV1(data) {
  return request({ url: '/threaten/passets/export', method: 'post', data, is_golang:1 })
} //导出威胁资产

// ##################################未知资产推荐
export function recommandScanTaskDetail(data) {
  return request({ url: '/assets/scan/check/detail', method: 'post', data })
}
