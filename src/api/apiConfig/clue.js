import request from '@/utils/request'
// import { login } from '@/api/apiConfig/clue.js'

// ##################################云端推荐
export function cluesList(data) {
  return request({ url: '/clues/' + data.type, method: 'get', params: data.query, is_golang: 1 })
} // 线索列表
export function delCluesList(data) {
  return request({
    url: '/clues/' + data.type + '/' + data.group_id,
    method: 'delete',
    data: data.query
  })
} // 线索删除
export function delCluesListV1(data) {
  return request({ url: '/clues', method: 'delete', data })
} // 线索删除
export function editCluesList(data) {
  return request({ url: '/clues/' + data.type + '/' + data.id, method: 'post', data: data.query })
} // 线索编辑
export function tabNumClues(data) {
  return request({ url: '/clues/count/' + data.group_id, method: 'get', params: data.data, is_golang: 1 })
} // 线索tab数量统计
export function exportCluesList(data) {
  return request({
    url: '/clues/export/' + data.type + '/' + data.group_id,
    method: 'post',
    params: data.query
  })
} // 线索列表导出
export function exportCluesListV1(data) {
  return request({ url: '/clues/export', method: 'post', data })
} // 线索列表导出

export function batchAddClue(data) {
  return request({ url: '/clues/batching_clue_task', method: 'post', data })
} // 线索策略-批量扩展

// ##################################线索管理
export function cluesGroupSelect(data) {
  return request({ url: '/clues/group/select', method: 'get', params: data })
} // 线索确认-选项列表
export function cluesGroupList(data) {
  return request({ url: '/clues/group', method: 'get', params: data })
} // 线索分组列表
export function cluesGroupListNoPage(data) {
  return request({ url: '/clues/group_list', method: 'get', params: data })
} // 线索分组列表不分页
export function groupRecommend(data) {
  return request({ url: '/clues/group/recommend', method: 'post', params: data })
} // 线索分组-立即推荐
export function addCluesGroupList(data) {
  return request({ url: '/clues/group', method: 'post', data })
} // 线索分组-添加
export function delCluesGroupList(data) {
  return request({ url: '/clues/group', method: 'delete', params: data })
} // 线索分组-删除
export function editCluesGroupList(data) {
  return request({ url: '/clues/group/' + data.id, method: 'post', data: data.data })
} // 线索分组-编辑
export function exportClues(data) {
  return request({ url: '/clues/group/' + data.id + '/import', method: 'post', params: data.data })
} // 线索分组-批量导入
export function insertCluesV1(data) {
  return request({ url: '/clues', method: 'post', data, is_golang: 1})
} // V1新接口批量添加，线索分组-批量导入
export function passClue(data) {
  return request({ url: '/pass_clue', method: 'post', params: data })
} // 线索分组-批量导入
export function passClueV1(data) {
  return request({ url: '/clues/pass_clue', method: 'post', data, is_golang: 1 })
} // 线索处置忽略、确认
export function goPointClue(data) {
  return request({ url: '/clues/set_supply_chain', method: 'post', data })
} // 待确认线索供应链标记

// ##################################线索库/company_clues/info
export function getCuleMapData(data) {
  return request({ url: '/clues/atlas/' + data.group_id, method: 'get', params: data.data })
} //企业线索库图谱
export function getCuleMapDataChildren(data) {
  return request({ url: '/clues/atlas_child/' + data.id, method: 'get', params: data.data })
} //企业线索库图谱子集
export function getPublicSearchList(data) {
  return request({ url: '/clue_company/drop_list', method: 'get', params: data })
} //公共线索库下拉
export function getCluecompanyList(data) {
  return request({ url: '/clue_company/list', method: 'get', params: data })
} //公共线索库列表

export function getPublicExport(data) {
  return request({ url: '/clue_company/list/export', method: 'get', params: data })
}
export function getFofaAssetsAll(data) {
  return request({ url: '/clue_company/count', method: 'get', params: data })
} //fofa全量数据
export function getSupplyChiaList(data) {
  return request({ url: '/supply_chain/clues/' + data.type, method: 'get', params: data.data })
} //供应链线索库
// supplyChiaCancel(data){ return request({ url: '/clues/set_supply_chain', method: 'post', data})}//设置或取消供应链线索
export function supplyChiaExport(data) {
  return request({ url: '/supply_chain/clues/export', method: 'post', data })
} //供应链线索库导出
export function supplyChiaCount(data) {
  return request({ url: '/supply_chain/clues/count', method: 'get', params: data })
} //供应链线索库统计
// ##################################企业线索库
export function getCompanyClueBaseData(data) {
  return request({ url: '/company_clues/info', method: 'get', params: data })
}
export function noteClueBlack(data) {
  return request({ url: '/clue/set_black', method: 'post', data, is_golang: 1 })
} //将线索标记到黑名单

// #######################线索总览
export function clueCompanyCount(data) {
  return request({ url: '/clue_company/count', method: 'get', params: data, is_golang: 1 })
} // fofa的全量数量统计接口(icon数量，域名数量，证书数量，icp数量，org数量，及各项环比)
export function clueCompanyList(data) {
  return request({ url: '/clue_company/drop_list', method: 'get', params: data, is_golang: 1 })
} // 企业下拉列表接口
export function publicClueList(data) {
  return request({ url: '/clue_company/list', method: 'get', params: data, is_golang: 1 })
} // 公共线索库列表
export function exportPublicClueList(data) {
  return request({ url: '/clue_company/list/export', method: 'get', params: data, is_golang: 1 })
} // 公共线索库列表
// }

// ##################################线索总库
export function clueStoreList(data) {
  return request({ url: '/clue/', method: 'post', data, is_golang: 1 })
} // 总库列表
export function editClueStoreList(data) {
  return request({ url: '/clue/' + data.id, method: 'post', data: data, is_golang: 1 })
} // 编辑
export function batchEditClueStore(data) {
  return request({ url: '/clue/batch_update', method: 'post', data: data, is_golang: 1 })
} // 批量编辑线索库的企业名称
export function fakeClue(data) {
  return request({ url: '/clues/fake_clue', method: 'post', data: data })
} // ICP盗用标记
