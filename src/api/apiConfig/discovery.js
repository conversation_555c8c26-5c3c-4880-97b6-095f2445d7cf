import request from '@/utils/request'
// import { getPortProList } from '@/api/apiConfig/discovery.js'
// ##################################资产发现-通过扫描发现资产
export function getAllTaskList(data) {
  return request({ url: '/assets/task', method: 'get', params: data, is_golang: 1 })
} // 获取整合后任务列表 525新需求，把正在运行，等待，已完成整合到一个列表
export function getTaskResults(data) {
  return request({ url: '/assets/task/get_task_result', method: 'get', params: data, is_golang: 1 })
} // 获取任务扫描结果
export function addTask(data) {
  return request({ url: '/assets/task', method: 'post', data, is_golang: 1 })
} // 添加非周期任务
export function deleteTask(data) {
  return request({ url: '/assets/task', method: 'delete', data, is_golang: 1 })
} // 删除非周期任务
export function grontabTaskList(data) {
  return request({ url: '/assets/cycle', method: 'get', params: data, is_golang:1 })
} // 周期任务列表
export function addGrontabTask(data) {
  return request({ url: '/assets/cycle', method: 'post', data, is_golang: 1 })
} // 添加周期任务
export function scanCronTaskDetail(data) {
  return request({ url: '/assets/cycle/' + data.id, method: 'get', params: data, is_golang: 1 })
} // 周期任务详情
export function editGrontabTask(data) {
  return request({ url: '/assets/cycle/' + data.id, method: 'post', data, is_golang: 1 })
} // 编辑周期任务
export function delGrontabTask(data) {
  return request({ url: '/assets/cycle', method: 'delete', data, is_golang: 1 })
} // 删除周期任务
export function crontabTaskSwitch(data) {
  return request({ url: '/assets/cycle/task_switch', method: 'post', data, is_golang: 1 })
} // 周期任务开关
export function taskResultAnalyse(data) {
  return request({ url: '/assets/task/task_analyse', method: 'get', params: data, is_golang: 1 })
} // 获取已完成详情左侧数量统计部分数据
export function scanTaskDetail(data) {
  return request({
    url: `/assets/task/${data.id}?operate_company_id=${data.operate_company_id}`,
    method: 'get',
    is_golang:1
  })
} // 获取已完成详情左侧数量统计部分数据
export function getAllUser(data) {
  return request({ url: '/assets/task/get_all_user', method: 'get', params: data })
} // 已完成任务发起人列表
export function getTaskResultCondition(data) {
  return request({ url: '/assets/task/get_task_result_condition', method: 'get', params: data, is_golang: 1 })
} // 已完成任务高级筛选下拉列表字段
export function taskResultExport(data) {
  return request({ url: '/assets/task/task_result_export', method: 'post', data, is_golang: 1 })
} // 已完成任务-详情-导出
export function ipDetail(data) {
  return request({ url: '/assets/task/ipdetail', method: 'get', params: data})
} // 已完成任务列表-详情-IP详情
export function ipDetailHistory(data) {
  return request({ url: '/assets/ipdetail/history', method: 'get', params: data, is_golang: 1 })
} // ip资产详情的历史绑定域名
export function pauseRunningTask(data) {
  return request({ url: '/scans/pause', method: 'post', data: data })
} // 暂停正在运行任务
export function startRunningTask(data) {
  return request({ url: '/scans/execute', method: 'post', data: data })
} // 继续执行已暂停运行任务
