import request from '@/utils/request'
// import { login } from '@/api/apiConfig/domain.js'

//##################################域名资产
export function domainAssetList(data) {
  return request({ url: '/domain_assets/list', method: 'get', params: data, is_golang: 1 })
} // 域名资产列表

export function domainNextLevelList(data) {
  return request({ url: '/domain_assets/list/next/' + data.id, method: 'get', params: data })
} // 获取下一级域名资产列表
export function domainAssetDel(data) {
  return request({ url: '/domain_assets/list', method: 'delete', data })
} //域名资产删除
export function domainAssetUpload(data) {
  return request({ url: '/domain_assets/push', method: 'post', data, is_golang: 1 })
} // 域名资产导入
export function domainAssetDerive(data) {
  return request({ url: '/domain_assets/list/export', method: 'post', data, is_golang: 1 })
} // 域名资产导出
export function domainDetails(data) {
  return request({ url: '/domain_assets/info/' + data.id, method: 'get', params: data })
} // 获取域名详情
export function domainWhoIsDetails(data) {
  return request({ url: '/domain_whois', method: 'get', params: data })
} // 获取域名详情whois 信息
export function domainAssetsScan(data) {
  return request({ url: '/domain_assets/scan', method: 'post', data })
} // 域名资产列表扫描ip资产
export function domainAssetsReparse(data) {
  return request({ url: '/domain_assets/reparse', method: 'post', data, is_golang: 1 })
} // 域名资产周期更新-设置立即更新策略
export function domainAssetsCron(data) {
  return request({ url: '/domain_assets/cron', method: 'get', params: data, is_golang: 1 })
} // 域名资产周期更新

export function domainAssetTask(data) {
  return request({ url: '/domain_assets/task', method: 'post', data })
} // 域名爆破任务下发
export function domainAssetTaskList(data) {
  return request({ url: '/domain_assets/task', method: 'get', params: data })
} //域名任务结果页列表
export function domainAssetTaskListDel(data) {
  return request({ url: '/domain_assets/task', method: 'delete', data })
} // 任务提前结束
export function domainAssetProgreeUpdate(data) {
  return request({ url: '/domain_assets/progress', method: 'get', params: data })
} // 域名发现任务进度更新
export function getDomainTaskResult(data) {
  return request({ url: '/domain_assets/task/info/' + data.id, method: 'get', params: data.data })
}

export function downDomainTaskResult(data) {
  return request({ url: '/domain_assets_task/list/export', method: 'post', data })
} //域名发现任务结果导出
export function downDomainScreenList(data) {
  return request({ url: '/domain_assets/domains', method: 'get', params: data, is_golang: 1 })
} //选择域名资产列出所有的域名
