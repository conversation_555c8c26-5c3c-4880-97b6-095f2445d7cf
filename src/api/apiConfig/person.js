import request from '@/utils/request'
// import { login } from '@/api/apiConfig/person.js'
export function login(data) {
  return request({ url: '/auth/login', method: 'post', data })
} // 登录
export function captcha() {
  return request({ url: '/auth/captcha', method: 'get' })
}

export function onlineIs(data) {
  return request({ url: '/auth/online_state', method: 'get' })
} // 是否在升级
export function gettoken(data) {
  return request({ url: '/auth/gen-api-token', method: 'get' })
} // 登录
export function getVersion(data) {
  return request({ url: '/version', method: 'get' })
} // 获取版本信息
export function logout(data) {
  return request({ url: '/auth/logout', method: 'get' })
} // 登出
export function getcode(data) {
  return request({ url: '/auth/code', method: 'post', data })
} // 短信验证码
export function personInfo(data) {
  return request({ url: '/auth/me', method: 'get', params: data })
} // 个人信息
export function getBlackList(data) {
  return request({ url: '/title/list', method: 'get', params: data })
} // 黑名单列表
export function resetPwd(data) {
  return request({ url: '/auth/reset/pwd', method: 'put', data })
} // 个人信息修改密码
export function UpdateMe(data) {
  return request({ url: '/auth/revise/me', method: 'post', data: data })
} // 个人信息编辑
export function refresh() {
  return request({ url: '/auth/refresh', method: 'get' })
} // 用户刷新token
export function getSystemInfo() {
  return request({ url: '/system/infos', method: 'get' })
} // 获取系统信息
export function setAgree() {
  return request({ url: '/auth/agree', method: 'get' })
} // 同意免责声明
export function getCompanyInfo(data) {
  return request({ url: '/auth/company/info/' + data, method: 'get' })
} // 安服账号切换企业账号时，获取企业权限信息
export function getApiToken(data) {
  return request({ url: '/auth/api-token', method: 'get', params: data })
} // 安服账号切换企业账号时，获取企业权限信息
export function setFirstTaskNotice(data) {
  return request({ url: '/user/set_first_task_notice', method: 'put', params: data, is_golang: 1 })
} // 设置首次测绘任务通知
// ##################################禁扫IP
export function getForbidIps(data) {
  return request({ url: '/forbid/ips', method: 'get', params: data, is_golang: 1 })
} // 禁扫IP列表
export function addForbidIps(data) {
  return request({ url: '/forbid/ips', method: 'post', data, is_golang: 1 })
} // 添加禁扫IP
export function deleteForbidIps(data) {
  return request({ url: '/forbid/ips', method: 'delete', data, is_golang: 1 })
} // 删除禁扫IP

export function getMyCompany(data) {
  return request({ url: '/auth/company', method: 'get', data })
} // 安服角色查看企业列表

// ##################################禁扫时间
export function getProhibit(data) {
  return request({ url: '/prohibit/time', method: 'get', params: data })
} //获取禁扫时间
export function setProhibit(data) {
  return request({ url: '/prohibit/time', method: 'post', data })
} //设置禁扫时间

// ##################################自定义菜单
export function setColumn(data) {
  return request({ url: '/table_custom/list', method: 'post', data })
}
export function getColumn(data) {
  return request({ url: '/table_custom/list', method: 'get', params: data })
}

// ##################################微信登陆
export function weixiLogin(data) {
  return request({ url: '/wechat/login', method: 'post', data })
} //校验
export function weixiBind(data) {
  return request({ url: '/wechat/bind', method: 'post', data })
} //绑定
export function weixiUnBind(data) {
  return request({ url: '/wechat/unbind', method: 'post', data })
} //解绑
