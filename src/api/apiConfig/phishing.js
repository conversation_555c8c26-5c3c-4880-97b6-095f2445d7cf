import request from '@/utils/request'
// import { login } from '@/api/apiConfig/phishing.js'

// ##################################钓鱼仿冒网站-第一步
// export function getAssociate(data) { return request({ url: '/detect_assets/company/drop_list', method: 'get', params: data }) }//企业关键词下拉列表
export function fakeSureCompany(data) {
  return request({ url: '/fake_detect_assets/company/confirm', method: 'post', data })
} //企业名称确认后搜索
// ##################################钓鱼仿冒网站-第二步
export function fakeGetCluelist(data) {
  return request({ url: '/fake_detect_assets/clues', method: 'get', params: data })
} //企业搜索完成后初始线索列表
export function fakeExpanding(data) {
  return request({ url: '/fake_detect_assets/clues', method: 'post', data })
} //扩展
export function fakeAgainGetClue(data) {
  return request({ url: '/fake_detect_assets/company/repeat_clues', method: 'post', data })
} //重新获取原始线索
export function fakeModelSet(data) {
  return request({ url: '/fake_detect_assets/keyword_type', method: 'post', data })
} // 关键词联想模式设置
export function fakeModelList(data) {
  return request({ url: '/fake_detect_assets/keyword', method: 'get', params: data })
} // 关键词联想列表
export function getMyWordList(data) {
  return request({ url: '/fake_detect_assets/suport_keyword/list', method: 'get', params: data })
} // 个人的关键词列表
export function addMyWordList(data) {
  return request({ url: '/fake_detect_assets/suport_keyword/list', method: 'post', data })
} // 个人的关键词新增
export function delMyWordList(data) {
  return request({ url: '/fake_detect_assets/suport_keyword/list', method: 'delete', data })
} // 个人的关键词删除
export function fakeMergeKeyword(data) {
  return request({ url: '/fake_detect_assets/expend_keyword', method: 'post', data })
} // 自定义关键词模式排列组合关键词并入库
export function addfakeModelList(data) {
  return request({ url: '/fake_detect_assets/node_keyword', method: 'post', data })
} // 辅助词删除，新增
export function fakeAllClues(data) {
  return request({ url: '/fake_detect_assets/confirm_clues', method: 'post', data })
} // 生成线索总表
export function fakeReturn(data) {
  return request({ url: '/fake_detect_assets/clues/return', method: 'post', data })
} //返回
// ##################################钓鱼仿冒网站-第三步第四步
export function fakeDataFind(data) {
  return request({ url: '/fake_detect_assets/assets/find', method: 'post', data, is_golang: 1 })
} // 仿冒数据发现
export function fakeDataFindList(data) {
  return request({ url: '/fake_detect_assets/assets/list', method: 'post', data, is_golang: 1 })
} // 仿冒数据列表
export function fakeDataDetect(data) {
  return request({ url: '/fake_detect_assets/assets/detect', method: 'put', data, is_golang: 1 })
} // 仿冒数据-数据存活性探测
export function fakeDataSyncthreat(data) {
  return request({ url: '/fake_detect_assets/assets/sync', method: 'post', data, is_golang: 1 })
} // 仿冒数据-入账威胁
export function fakeImportSave(data) {
  return request({ url: '/fake_detect_assets/assets/import', method: 'post', data, is_golang: 1 })
} // 数据导入确定
export function fakeTabCount(data) {
  return request({
    url: '/fake_detect_assets/assets/count',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // tab数量统计
export function fakeCondition(data) {
  return request({
    url: '/fake_detect_assets/assets/agg',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 仿冒数据高级筛选统计
export function fakeDataSyncthreatDetect(data) {
  return request({
    url: '/fake_detect_assets/assets/deep/detect',
    method: 'put',
    data,
    is_golang: 1
  })
} // 仿冒数据高级筛选统计
export function fakeDataRetry(data) {
  return request({
    url: '/fake_detect_assets/assets/screenshot',
    method: 'post',
    data,
    is_golang: 1
  })
} // 仿冒数据-截图重试

// ##################################钓鱼仿冒网站-任务记录
export function fakeDetectTaskList(data) {
  return request({ url: '/fake_detect_assets/task', method: 'get', params: data, is_golang: 1 })
} // 任务记录列表
export function fakeTaskInfo(data) {
  return request({
    url: '/fake_detect_assets/task/info/' + data.taskId,
    method: 'get',
    params: data.data
  })
} // 任务详情
export function fakeDelTask(data) {
  return request({ url: '/fake_detect_assets/task', method: 'delete', data, is_golang: 1 })
} // 任务记录删除
export function fakeEndDetectTask(data) {
  return request({ url: '/fake_detect_assets/result/stop', method: 'delete', data })
} // 任务提前结束
export function fakeTaskDownload(data) {
  return request({
    url: '/fake_detect_assets/assets/download',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 任务数据下载
