import request from '@/utils/request'
// import { getPortProList } from '@/api/apiConfig/poc.js'
// ##################################POC管理
export function getPocList(data) {
  return request({ url: '/poc', method: 'get', params: data })
} // 获取poc管理列表
export function getPocListNoPage(data) {
  return request({ url: '/poc/list', method: 'get', params: data })
} // 获取poc不分页列表
export function pocStatusUpdate(data) {
  return request({ url: '/poc/update/status', method: 'put', data })
} // 禁用启用状态更新
export function pocTypeList(data) {
  return request({ url: '/poc/vulnerability', method: 'get', data })
} // poc类型
export function infoPoc(data) {
  return request({ url: '/poc/detail/' + data, method: 'get' })
} // poc详情
export function getRklInfo(data) {
  return request({ url: '/poc/' + data + '/weak', method: 'get' })
} // 新版弱口令编辑-获取弱口令详情
export function addEditRkl(data) {
  return request({
    url: '/poc/' + data.pocId + '/weak/' + data.dictId,
    method: 'post',
    data: data.data
  })
} // 新版弱口令编辑-自定义字典弱口令编辑/新增
export function delRkl(data) {
  return request({ url: '/poc/weak/' + data, method: 'delete' })
} // 新版弱口令编辑-自定义字典弱口令删除(同时删除对应字典)
export function saveRequireAttack(data) {
  return request({ url: '/poc/weak/' + data.id, method: 'put', data: data.data })
} // 新版弱口令编辑-保存字典需要破解开关
export function updatePocRiskIp(data) {
  return request({ url: 'poc/risk_count_ip', method: 'get', params: data })
} // 手动更新风险ip数
export function exportRiskAsset(data) {
  return request({ url: 'risk_assets/export', method: 'post', data: data })
} // 导出风险资产
// ##################################POC分组管理
export function getPocGroupListNoPage(data) {
  return request({ url: '/poc/groups/list', method: 'get', params: data })
} // 获取poc分组不分页列表
export function getPocGroupListPage(data) {
  return request({ url: '/poc/groups', method: 'get', params: data })
} // 获取poc分组分页列表
export function addPocGroup(data) {
  return request({ url: '/poc/groups', method: 'post', data })
}
//获取自定义poc列表
export function getPocCustomList(data) {
  return request({
    url: '/poc/custom_poc_list',
    method: 'get',
    params: data,
    is_golang: 1,
    is_new_params: 1
  })
}
//添加自定义poc
export function addPocCustom(data) {
  return request({ url: '/poc/custom_poc_create', method: 'post', data, is_golang: 1 })
}
//发布/下线自定义poc
export function releasePocCustom(data) {
  return request({ url: '/poc/custom_poc_publish', method: 'post', data, is_golang: 1 })
}
//更新风险资产
export function updateRiskAsset(data) {
  return request({ url: '/poc/risk_assets_update', method: 'post', data, is_golang: 1 })
}
//更新自定义poc
export function updatePocCustom(data) {
  return request({ url: '/poc/custom_poc_update', method: 'put', data, is_golang: 1 })
}
//删除自定义poc
export function delPocCustom(data) {
  return request({ url: '/poc/custom_poc_delete', method: 'delete', data, is_golang: 1 })
}
// 根据ID获取POC详情
export function infoPocCustom(data) {
  return request({ url: '/poc/custom_poc_info', method: 'get', params: data, is_golang: 1 })
}
// 添加poc分组
export function infoPocGroup(data) {
  return request({ url: '/poc/groups/detail', method: 'post', data })
} // 详情poc分组
export function updatePocGroup(data) {
  return request({ url: '/poc/groups/update', method: 'post', data })
} // 更新poc分组
export function delPocGroup(data) {
  return request({ url: '/poc/groups', method: 'delete', data })
} // 删除poc分组
export function pocRiskAssets(data) {
  return request({ url: '/risk_assets/list', method: 'get', params: data })
} // poc管理-风险资产数列表

// ##################################漏洞跟踪
export function getPocTaskResults(data) {
  return request({ url: '/threats/result', method: 'get', params: data })
} // 获取扫描结果列表
export function delPocs(data) {
  return request({ url: '/threats/result', method: 'delete', data })
} // 删除
export function exportPocTaskResult(data) {
  return request({ url: '/threats/result/export', method: 'post', data })
} // 已修复未修复列表导出
export function leakcheck(data) {
  return request({ url: '/threats/result/check', method: 'post', data })
} // 漏洞核查
export function pocCheckDetail(data) {
  return request({ url: '/threats/result/check_detail', method: 'get', params: data })
} // 漏洞验证详情
export function countPocTypesNum(data) {
  return request({ url: '/threats/result/count', method: 'post', data })
} // 已修复未修复漏洞等级数量
export function pocCheck(data) {
  return request({ url: '/threats/result/poc_check', method: 'post', data })
} // 漏洞验证
export function userPocPasswordInfo(data) {
  return request({ url: '/poc/weak/detail', method: 'post', data })
} // poc弱口令详情获取
export function updatePocPass(data) {
  return request({ url: '/poc/weak/edit', method: 'post', data })
} // poc弱口令编辑
