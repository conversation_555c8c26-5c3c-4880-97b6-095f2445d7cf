import request from '@/utils/request'
// import { getPortProList } from '@/api/apiConfig/port.js'

// ##################################端口管理
export function getPortList(data) {
  return request({ url: '/ports', method: 'get', params: data })
} // 获取端口管理列表
export function getPortProList(data) {
  return request({ url: '/ports/protocols', method: 'get', params: data })
} // 获取端口协议列表
export function addPort(data) {
  return request({ url: '/ports', method: 'post', data })
}
export function delPort(data) {
  return request({ url: '/ports', method: 'delete', data })
}
export function infoPort(data) {
  return request({ url: '/ports/detail', method: 'post', data })
}
export function editPort(data) {
  return request({ url: '/ports/' + data.id, method: 'put', data: data.data })
}
export function portUpdateStatus(data) {
  return request({ url: '/ports/update/status', method: 'put', data })
}
export function portGroupsNoPage(data) {
  return request({ url: '/ports/groups/list', method: 'get', params: data, is_golang: 1 })
} // 端口分组列表不分页

// ##################################端口分组管理
export function portGroupsPage(data) {
  return request({ url: '/ports/groups', method: 'get', params: data })
} // 端口分组列表分页
export function addPortGroups(data) {
  return request({ url: '/ports/groups', method: 'post', data })
}
export function delPortGroups(data) {
  return request({ url: '/ports/groups', method: 'delete', data })
}
export function editPortGroups(data) {
  return request({ url: '/ports/groups/' + data.id, method: 'put', data: data.data })
}
export function infoPortGroups(data) {
  return request({ url: '/ports/groups/detail', method: 'post', data })
}
export function getPortGroups(data) {
  return request({ url: '/ports/list', method: 'get', params: data })
} // 获取端口列表
