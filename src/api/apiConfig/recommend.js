import request from '@/utils/request'
// import { login } from '@/api/apiConfig/recommend.js'

// ##################################云端推荐

export function getRunningClueJob(data) {
  return request({ url: '/clues/batching_clue_task', method: 'get', params: data })
} // 获取正在扩展进度
export function delRunningClueJob(data) {
  return request({ url: '/clues/batching_clue_task', method: 'delete', data })
} // 删除正在扩展进度
export function expendLogList(data) {
  return request({ url: '/clues/expend_list', method: 'post', data })
} // 扩展记录
export function delExpendList(data) {
  return request({ url: '/clues/expend_list', method: 'delete', data })
} // 删除扩展记录
export function recommendClues(data) {
  return request({
    url: '/clued/recommend_clues/' + data.group_id,
    method: 'get',
    params: data.data
  })
} // 推荐记录-线索列表
export function recommendResult(data) {
  return request({ url: '/cloud/recommend/' + data.task_id, method: 'post', data: data.data })
} // 推荐记录-推荐记录审核列表
export function confirmRecommendTask(data) {
  return request({ url: '/cloud/wait_confirm_recommend_task', method: 'get', params: data })
} // 推荐记录-推荐记录审核任务下拉菜单
export function goRecommend(data) {
  return request({
    url: '/cloud/recommend_confirm/' + data.audit,
    method: 'post',
    params: data.data
  })
} // 推荐记录-推荐记录审核确认、忽略
export function xfRecommend(data) {
  return request({
    url: '/cloud/recommend_record/' + data.id + '/push',
    method: 'post',
    params: data.query
  })
} // 推荐记录-推荐记录审核下发
export function recommend(data) {
  return request({ url: '/cloud/recommend', method: 'post', data })
} // 推荐任务下发
export function recommendRecords(data) {
  return request({ url: '/cloud/recommend/records', method: 'get', params: data.data })
} // 推荐记录列表
export function exportRecommendRecords(data) {
  return request({ url: '/cloud/export', method: 'post', data })
} // 推荐记录列表导出
export function delRecommendRecords(data) {
  return request({ url: '/cloud/recommend/records', method: 'delete', params: data.data })
} // 删除推荐记录列表
export function regRecommendClues(data) {
  return request({ url: '/cloud/recommend_clues/' + data.flag, method: 'get', params: data.data, is_golang: 1 })
} // 推荐记录-扫描资产推荐线索
export function regRecommendRecords(data) {
  return request({ url: '/cloud/recommend/' + data.flag, method: 'get', params: data.query, is_golang: 1 })
} // 推荐记录-扫描资产推荐列表
export function delRegRecommendRecords(data) {
  return request({ url: '/cloud/recommend/' + data.flag, method: 'delete', params: data.data })
} // 推荐记录-扫描资产推荐列表-删除
export function handleScan(data) {
  return request({ url: '/assets/scan/check', method: 'post', data })
} // 推荐记录-扫描资产推荐列表-执行扫描
export function exportHandleScan(data) {
  return request({
    url: '/cloud/recommend/' + data.flag + '/export',
    method: 'post',
    params: data.query,
    is_golang: 1
  })
} // 扫描资产推荐列表-导出

export function companyEnquiry(data) {
  return request({ url: '/cloud/company_enquiry', method: 'get', params: data })
} // 通过企业全称查询企业信息和备案信息
export function companyCascadeEquity(data) {
  return request({ url: '/cloud/company_cascade_equity', method: 'get', params: data, is_golang: 1 })
} // 通过企业id查询子级股权分布信息
export function companyGroupClue(data) {
  return request({ url: '/cloud/company_group_clue', method: 'post', data })
} // 提取线索-企业及其子企业选择分组
export function companyIcpInfo(data) {
  return request({ url: '/cloud/company_icp_info', method: 'get', params: data })
} // 企业备案信息详情查询
export function checkIcpRecord(data) {
  return request({ url: '/cloud/check_icp_record', method: 'post', data })
} // 核查备案信息
