import request from '@/utils/request'
// import { login } from '@/api/apiConfig/screen.js'
//##################################大屏
export function assetOverview(data) {
  return request({ url: '/screen/asset_overview', method: 'get', params: data })
} //资产概况
export function screenAssetVennMap(data) {
  return request({ url: '/screen/count_asset_origin', method: 'get', params: data })
} //Venn图
export function screenLoopholeChart(data) {
  return request({ url: '/screen/loophole_overview', method: 'get', params: data })
} //漏洞图
export function screenHotspotChart(data) {
  return request({ url: '/screen/hotspot', method: 'get', params: data })
} //热点图
export function screenMapChart(data) {
  return request({ url: '/screen/map', method: 'get', params: data })
} //地图
export function screenThreaten<PERSON>hart(data) {
  return request({ url: '/screen/threaten_asset_count', method: 'get', params: data })
} //威胁分布
export function screenRisksChart(data) {
  return request({ url: '/screen/risks', method: 'get', params: data })
} //风险列表
export function screenCuleMapData(data) {
  return request({ url: '/screen/atlas/' + data.group_id, method: 'get', params: data.data })
}
