import request from '@/utils/request'
// import { login } from '@/api/apiConfig/surveying.js'
// ##################################单位资产测绘-第三步第四步-资产推荐
export function cloudAssets(data) {
  return request({ url: '/detect_assets/cloud', method: 'post', data, is_golang: 1 })
} // 云端资产推荐
export function cloudAssetsConfirm(data) {
  return request({ url: '/detect_assets/cloud/other_confirm', method: 'post', data, is_golang: 1 })
} // 三方导入确定
export function cloudAssetsHunter(data) {
  return request({
    url: '/detect_assets/clue/hunter_sql/' + data.id,
    method: 'get',
    params: data.data,
    is_golang:1
  })
} // 生成hutter查询语句
export function cloudAssetsPredict(data) {
  return request({ url: '/detect_assets/result/assets/evaluate', method: 'post', data, is_golang: 1 })
} // 资产评估
export function cloudAssetsAnyslate(data) {
  return request({
    url: '/detect_assets/result/anyslate/' + data.flag,
    method: 'get',
    params: data.data
  })
} // 资产评估等级ABCD数量统计,列表接口getScanList
export function cloudAssetsScan(data) {
  return request({ url: '/detect_assets/result/scan', method: 'post', data, is_golang: 1 })
} // 资产评估后下发资产扫描
export function cloudAssetsReport(data) {
  return request({ url: '/detect_assets/result/create_reports', method: 'post', data, is_golang:1 })
} // 下发资产扫描后-生成报告
export function cloudAssetsFenlei(data) {
  return request({ url: '/detect_assets/result/group', method: 'post', data })
} // 下发资产扫描后-资产分类
export function cloudAssetsList(data) {
  return request({ url: '/detect_assets/result/assets', method: 'get', params: data, is_golang:1 })
} // 统计资产分类的资产数量以及资产列表
export function delCloudAssetsList(data) {
  return request({ url: '/detect_assets/aseets', method: 'delete', data })
} // 入账扫描后资产列表删除
export function updateCloudAssetsCount(data) {
  return request({ url: '/detect_assets/task/count', method: 'post', data })
} // 入账扫描后触发重新计算数量（es计算不准）

// ##################################单位资产测绘-任务记录
export function detectTaskList(data) {
  return request({ url: 'detect_assets/task', method: 'get', params: data, is_golang:1 })
} // 任务记录列表
export function detectTaskInfo(data) {
  return request({
    url: '/detect_assets/task/info/' + data.taskId,
    method: 'get',
    params: data.data,
    is_golang:1
  })
} // 任务详情
export function delDetectTask(data) {
  return request({ url: '/detect_assets/task', method: 'delete', data, is_golang:1})
} // 任务记录删除
export function endDetectTask(data) {
  return request({ url: '/detect_assets/result/stop', method: 'delete', data, is_golang:1})
} // 任务提前结束
export function urlApiAgain(data) {
  return request({ url: '/url_api_again', method: 'post', data })
} // 任务重试
export function urlApiCopy(data) {
  return request({ url: '/url_api_domains', method: 'get', params: data })
} // 一键复制
// ##################################单位资产测绘-任务简报
export function detectReportInfo(data) {
  return request({ url: '/detect_assets/report/info', method: 'get', params: data, is_golang: 1 })
} // 任务简报数据
export function detectClueList(data) {
  return request({
    url: '/detect_assets/report/clue_list',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 任务简报数据
export function detectAssetDownload(data) {
  return request({
    url: '/detect_assets/report/import_asset_download',
    method: 'get',
    params: data,
    is_golang: 1
  })
} // 任务简报数据文件下载
// ##################################单位测绘-第一步
export function getAssociate(data) {
  return request({ url: '/detect_assets/company/drop_list', method: 'get', params: data, is_golang:1 })
} //企业关键词下拉列表
export function SureCompany(data) {
  return request({ url: '/detect_assets/company/confirm', method: 'post', data })
} //企业名称确认后搜索
export function taskParamsSave(data) {
  return request({ url: '/detect_assets/task/create', method: 'post', data, is_golang: 1 })
} // 开始测绘记录参数
export function taskConfirm(data) {
  return request({ url: '/detect_assets/job', method: 'get', params: data, is_golang:1 })
} // 开始测绘下发任务---新
export function companyBatchVerify(data) {
  return request({ url: '/company_name/batch_verify', method: 'post', data })
} //批量校验其他测绘企业名称

// ##################################单位测绘-第二步
export function getCluelist(data) {
  return request({ url: '/detect_assets/clues', method: 'get', params: data, is_golang:1 })
} //企业搜索完成后初始线索列表
export function Expanding(data) {
  return request({ url: '/detect_assets/clues', method: 'post', data, is_golang:1 })
} //扩展
export function againGetClue(data) {
  return request({ url: '/detect_assets/company/repeat_clues', method: 'post', data, is_golang:1 })
} //重新获取原始线索
export function generateClueAll(data) {
  return request({ url: '/detect_assets/confirm_clues', method: 'post', params: data, is_golang:1 })
} //生成线索总表
export function deleteExpandClue(data) {
  return request({ url: '/detect_assets/clues', method: 'post', data })
} //删除线索
export function cehuiReturn(data) {
  return request({ url: '/detect_assets/clues/return', method: 'post', data })
} //返回
export function kehuCompanyList(data) {
  return request({ url: '/clue_company/drop_list_all', method: 'get', params: data })
} //客户能操作的企业列表
export function fakeClueIsThreat(data) {
  return request({ url: '/detect_assets/fake_clue_assets', method: 'post', data: data })
} // ICP盗用标记完成后是否用作推荐威胁资产
export function cluesUpdateSync(data) {
  return request({ url: '/detect_assets/clues/update_sync', method: 'put', data, is_golang: 1 })
} // 线索库批量以及单个更新企业名称
// ##################################单位测绘-第五步
export function goRelate(data) {
  return request({ url: '/detect_assets/relate', method: 'post', data, is_golang:1 })
} // 关联任务自定义
