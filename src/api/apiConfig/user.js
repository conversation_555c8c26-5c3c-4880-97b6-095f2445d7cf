import request from '@/utils/request'
// import { login } from '@/api/apiConfig/user.js'
// ##################################用户管理
export function users(data) {
  return request({ url: '/users', method: 'get', params: data })
} // 列表
export function addUser(data) {
  return request({ url: '/users', method: 'post', data })
} // 添加
export function delUser(data) {
  return request({ url: '/users', method: 'delete', data })
} // 批量删除
export function editUser(data) {
  return request({ url: `/users/${data.id}`, method: 'put', data: data.query })
} // 编辑
export function exportUsers(data) {
  return request({ url: '/user/export', method: 'post', data })
} // 导出
export function resetPassword(data) {
  return request({ url: '/user/reset', method: 'post', data })
} // 重置密码
