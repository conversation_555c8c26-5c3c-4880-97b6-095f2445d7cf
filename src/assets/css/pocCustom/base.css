*,
::after,
::before {
    padding: 0;
    margin: 0;
    list-style: none;
}

/* 默认不可选择复制 */
html,
body {
    /* -moz-user-select: none; */
    /*火狐*/
    /* -webkit-user-select: none; */
    /*webkit浏览器*/
    /* -ms-user-select: none; */
    /*IE10*/
    /* -khtml-user-select: none; */
    /*早期浏览器*/
    /* user-select: none; */
}

.copy-no-select {
    -moz-user-select: none;
    /*火狐*/
    -webkit-user-select: none;
    /*webkit浏览器*/
    -ms-user-select: none;
    /*IE10*/
    -khtml-user-select: none;
    /*早期浏览器*/
    user-select: none;
}

/* 可选择复制 */
.copy-select {
    -moz-user-select: text;
    /*火狐*/
    -webkit-user-select: text;
    /*webkit浏览器*/
    -ms-user-select: text;
    /*IE10*/
    -khtml-user-select: text;
    /*早期浏览器*/
    user-select: text;
}

.clearfix:before,
.clearfix:after {
    /*清除浮动*/
    content: "";
    display: table;
}

.clearfix:after {
    clear: both;
}

/* .clearfix {
    *zoom: 1;
    IE/7/6
} */

body {
    color: #333;
    font-size: 14px;
    font-family: "San Francisco Pro",
    "Roboto",
    "Helvetica",
    "PingFang SC",
    "苹方",
    "PingFangSC-Regular, PingFang SC",
    "微软雅黑",
    "Source Han Sans CN",
    "思源黑体",
    "SimHei",
    "黑体",
    "SimSun",
    "宋体",
    sans-serif;
}

html {
    background-color: rgb(255, 255, 255);
}

a {
    color: #333;
    cursor: pointer;
    text-decoration: none;
    outline: none !important;
}

img {
    vertical-align: middle;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
    /* WebKit browsers */
    color: var(--level3Font-color);
}

input:-moz-placeholder,
textarea:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: var(--level3Font-color);
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: var(--level3Font-color);
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: var(--level3Font-color);
}

/*公共类*/
.fl {
    float: left;
}

.fr {
    float: right;
}

.jianfr {
    padding-left: 1px;
}

.al {
    text-align: left;
}

.ac {
    text-align: center;
}

.ar {
    text-align: right;
}

.hide {
    display: none;
}

.block {
    display: block;
}

.inl-block {
    display: inline-block;
}

.text-pre-wrap {
    white-space: pre-wrap;
    /*css-3*/
    white-space: -moz-pre-wrap;
    /*Mozilla,since1999*/
    white-space: -pre-wrap;
    /*Opera4-6*/
    white-space: -o-pre-wrap;
    /*Opera7*/
    word-wrap: break-word;
    /*InternetExplorer5.5+*/
    text-align: justify;
}

.pointer {
    cursor: pointer;
}

.ellipise {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.two-ellipise {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.three-ellipise {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.four-ellipise {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

.wid321 {
    width: 321px;
}

.wid120 {
    width: 120px;
}

@keyframes rotate {
    from {
        transform: rotate(0)
    }

    to {
        transform: rotate(360deg)
    }
}

.gb-rotate {
    -webkit-transform: rotate(360deg);
    animation: rotate 1.5s linear infinite;
    -moz-animation: rotate 1.5s linear infinite;
    -webkit-animation: rotate 1.5s linear infinite;
    -o-animation: rotate 1.5s linear infinite;
}

.gb-container,
.gb-left,
.gb-main {
    height: 100%;
}

.gb-container {
    position: relative;
}

.gb-left {
    position: relative;
    width: 161px;
    background-image: var(--leftNavBg-img);
    background-position: left bottom;
    background-repeat: no-repeat;
    background-color: var(--navBackground-color);
}

.gb-main {
    position: absolute;
    top: 0;
    left: 161px;
    right: 0;
    bottom: 0;
    padding: 0 20px;
    /* padding: 0 30px; */
    background-color: var(--primaryBackground-color);
    background-image: var(--rightBg-img);
    background-repeat: no-repeat;
    background-size: auto 100%;
    background-position: center;
}

.gb-main.about {
    background-color: var(--primaryBackground-color);
}

.gb-logo {
    display: block;
    margin: 0 auto;
    width: 114px !important;
    height: 81px !important;
    padding-top: 27px;
    /* padding-bottom: 12px; */
}

.gb-logo-sm {
    width: 42px !important;
    height: 81px !important;
}

.gb-left-menu>a {
    position: relative;
    display: block;
    font-size: 14px;
    color: #fff;
    height: 68px;
    line-height: 53px;
    padding: 17px 0;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
}

.fold-gb-left .gb-left-menu>a:not(.active):hover {
    background-size: auto 100%;
    /* background-image: url(../img/fold-bg-h.png); */
}

.gb-left-menu>a:hover span {
    /* color: var(--navHoverFont-color); */
    /* background-image: url(../img/navcur-h.png); */
}

.gb-left-menu>a.active span {
    color: var(--navHoverFont-color);
    /* background-image: url(../img/navcur.png); */
}

.fold-gb-left .gb-left-menu>a.active {
    background-size: auto 100%;
    /* background-image: url(../img/fold-bg.png); */
}

.gb-left-menu>a img {
    position: absolute;
    left: 21px;
    top: 28px;
    z-index: 2;
    width: 32px;
    /* margin-right: 8px;
    margin-left: 21px; */
}

.gb-left-menu>a span {
    position: absolute;
    left: 63px;
    top: 19px;
    z-index: 2;
    color: var(--navFont-color);
}

.gb-config {
    position: absolute;
    left: 0;
    bottom: 34px;
    padding-left: 20px;
    font-size: 12px;
    color: var(--navOtherFont-color);
}

.gb-icons .iconfont {
    font-size: 13px;
    margin-right: 6px;
}

.iconchangjianwenti_huaban {
    margin: 0 11px;
}

.gb-ver {
    line-height: 16px;
    margin-top: 8px;
}

.gb-menu-btn {
    position: absolute;
    right: -12px;
    bottom: 51px;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    z-index: 3;
    color: var(--navOtherFont-color);
    border-radius: 50%;
    background-color: var(--navBackground-color);
}

/* 正在扫描 */
.gb-main-title {
    position: relative;
    text-align: center;
    font-size: 16px;
    height: 24px;
    line-height: 24px;
    color: var(--level3Font-color);
    margin-top: 14px;
    margin-bottom: 14px;
}

.gb-main-title a {
    position: absolute;
    top: 0;
    left: 10px;
    font-size: 14px;
    color: var(--main-color);
    /* opacity: 0.7; */
}

.gb-main-title .iconbackicon_huaban {
    margin-right: 6px;
    font-size: 14px;
}

.gb-main-mid {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    justify-content: space-between;
    position: absolute;
    left: 20px;
    right: 20px;
    top: 54px;
    bottom: 70px;
}

.gb-scaning-asset-wrap {
    display: flex;
    height: 12%;
    justify-content: space-between;
}

.gb-scaning-ware-wrap {
    display: flex;
    height: 44.5%;
    justify-content: space-between;
}

.gb-scaning-other-wrap {
    display: flex;
    height: 43%;
    justify-content: space-between;
}

.gb-scaning-asset-card {
    position: relative;
    width: 25%;
    height: 100%;
    text-align: center;
    margin-left: 10px;
    background-color: var(--navBackground-color);
    /* box-shadow: 0px 0px 13px 0px rgba(71, 120, 199, 0.38); */
    border-radius: 4px;
    overflow: hidden;
}

.scaning-asset-card-name {
    position: absolute;
    top: 15.2%;
    right: 0;
    left: 0;
    font-size: 12px;
    line-height: 16px;
    color: var(--scanResTopSubFont-color);
}

.scaning-asset-card-num {
    position: absolute;
    top: 43.8%;
    right: 0;
    left: 0;
    font-size: 26px;
    line-height: 30px;
    font-weight: 600;
    color: var(--scanResTopMainFont-color);
}

.gb-scaning-asset-card .iconfont {
    position: absolute;
    bottom: -9px;
    right: -7px;
    font-size: 47px;
    color: var(--scanResTopIcon-color);
}

.gb-scaning-btn-wrap {
    position: absolute;
    bottom: 0;
    left: 50%;
    z-index: 20;
    margin-left: -24px;
}

.gb-pro-circle {
    position: relative;
    width: 64px;
    height: 64px;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
    margin-right: 16px;
    border-radius: 50%;
    background-color: var(--main-color);
    /* background-image: linear-gradient(23deg,
        var(--main-color) 0%,
        #669bf1 100%),
      linear-gradient(#5e6c8a,
        #5e6c8a);
    background-blend-mode: normal,
      normal; */
}

.gb-pro-circle.gray {
    background-color: var(-scanEnd-color);
    /* background-image: linear-gradient(23deg,
        var(--level3Font-color) 0%,
        #8c9ab6 100%),
      linear-gradient(#5e6c8a,
        #5e6c8a);
    background-blend-mode: normal,
      normal; */
}

.gb-pro-circle>div:first-child {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 52px;
    height: 52px;
    line-height: 44px;
    border-radius: 50%;
    border: solid 4px rgba(255, 255, 255, 0.28);
}

.gb-pro-circle>div.gb-stop-scan {
    line-height: 18px;
    padding-top: 13px;
    z-index: 99;
}

.gb-pro-circle.warning {
    background: linear-gradient(23deg, rgba(236, 80, 72, 1), rgba(243, 94, 87, 1));
}

#gb-circle-progress {
    position: absolute !important;
    top: 6px;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}

.gb-scaning-text {
    font-size: 12px;
    height: 16px;
    line-height: 16px;
    max-width: 240px;
    color: var(--secondaryFont-color);
    margin-top: 27px;
}

.gb-scaning-text i {
    color: var(--primaryIcon-color);
    margin-right: 6px;
}

.gb-scaning-ware-item,
.gb-scaning-other-item {
    position: relative;
    width: 25%;
    height: 100%;
}

.gb-ware-chart-wrap {
    position: absolute;
    top: 47px;
    left: 0;
    right: 0;
    bottom: 0;
    margin-left: 10px;
    background-color: var(--secondaryBackground-color);
    /* box-shadow: 0px 2px 16px 0px rgba(71, 120, 199, 0.12); */
    border-radius: 4px;
}

.gb-bar-self-wrap {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    justify-content: space-between;
    padding: 11px 15px;
    font-size: 12px;
    color: var(--secondaryFont-color);
}

.gb-bar-self-wrap div.celarfix {
    height: 18px;
    line-height: 18px;
}

.gb-bar-self-wrap div.celarfix p {
    max-width: 68%;
}

.bar-self-item {
    margin-bottom: 6px;
}

.gb-bar-pro {
    width: 0;
    height: 4px;
    border-radius: 2px;
    background-image: linear-gradient(-90deg,
    var(--main-color) 0%,
    transparent 100%),
    linear-gradient(transparent,
            transparent);
    background-blend-mode: normal,
    normal;
}

.gb-scaning-ware-item>div.clearfix,
.gb-scaning-other-item>div.clearfix {
    height: 59px;
    font-size: 14px;
    padding-left: 10px;
    line-height: 16px;
    padding-top: 20px;
    color: var(--primaryFont-color);
}

.gb-scaning-ware-item>div.clearfix .iconfont,
.gb-scaning-other-item>div.clearfix .iconfont {
    color: var(--primaryIcon-color);
}

.gb-scaning-ware-item>div.clearfix .iconfont:first-child,
.gb-scaning-other-item>div.clearfix .iconfont:first-child {
    margin-right: 10px;
}

.gb-scaning-ware-item>div.clearfix .iconfont.iconjiantou,
.gb-scaning-other-item>div.clearfix .iconfont.iconjiantou {
    font-size: 14px;
}

.iconfont.iconshuaxin,
.iconfont.iconshuaxinwanchengzhuangtai_huaban {
    display: inline-block;
    font-size: 13px;
    margin-left: 8px;
}

.iconfont.iconshuaxinwanchengzhuangtai_huaban {
    color: #66cc9a !important;
}

.gb-scaning-other-wrap .gb-ware-chart-wrap {
    /* display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    justify-content: space-between; */
    padding: 10px 0;
    padding-left: 15px;
    padding-right: 4px;
    font-size: 12px;
    line-height: 31px;
}

.gb-list-wrap {
    max-height: 100%;
    overflow: auto;
}

.gb-scaning-other-wrap .gb-list-count {
    color: var(--level3Font-color);
    padding-right: 17px;
}

.gb-scaning-other-wrap .gb-list-name {
    max-width: 62%;
    color: var(--secondaryFont-color);
}

.gb-vul-chart-wrap .gb-list-count {
    color: #f79d99!important;
}

.gb-vul-chart-wrap .gb-list-name {
    color: #ec5048!important;
}

.gb-vul-chart-wrap .gb-list-count .iconfont {
    font-size: 12px;
    color: #ec5048;
    margin-right: 6px;
}

/* 开始扫描 */
.gb-main>div {
    height: 100%;
}

.gb-begin-scan {
    position: absolute;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 431px;
    margin: auto;
}

.gb-begin-scan p {
    font-size: 36px;
    line-height: 42px;
    color: var(--primaryFont-color);
}

.gb-begin-scan p:last-child {
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 1px;
    color: var(--secondaryFont-color);
    margin-top: 15px;
}

.gb-begin-scan img,
.begin-svg {
    display: block;
    width: 310px !important;
    height: auto !important;
    margin: 0 auto;
    margin-bottom: 57px;
}

.dialog-scan-config-main {
    position: relative;
    height: auto;
    background-color: var(--lv3Background-color);
    /* box-shadow: 0px 5px 46px 0px rgba(71, 120, 199, 0.11); */
    border-radius: 4px;
}

.dialog-banner .dialog-scan-config-main {
    height: 310px;
}

.dialog-scan-config-main.history {
    /* height: 304px; */
}

.dialog-scan-config-main.history .dialog-scan-config-bot {
    /* padding: 30px 6px 38px 30px; */
    padding: 30px 6px 10px 30px;
}

.his-pagination {
    padding-right: 20px;
}

.dialog-scan-config-main.history .el-table td,
.dialog-scan-config-main.history .el-table td a {
    color: var(--primaryFont-color);
    /* font-weight: 600; */
}

.dialog-scan-config-main.history .el-table td {
    padding: 7px 0;
}

.dialog-scan-config-main.history .el-table-wrap {
    padding-right: 10px;
    max-height: 310px;
    margin-bottom: 40px;
    overflow: auto;
}

.dialog-scan-config-main.history .el-table .cell {
    line-height: 22px;
}

.dialog-scan-config-top {
    position: relative;
    height: 62px;
    border-radius: 4px 4px 0 0;
    padding-left: 20px;
    padding-top: 20px;
    /* background-image: linear-gradient(197deg,
        var(--main-color) 0%,
        #4b80d5 100%),
      linear-gradient(var(--main-color),
        var(--main-color));
    background-blend-mode: normal,
      normal; */
}

.dialog-scan-config-top p {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    font-weight: bold;
    color: #333333;
}

.dia-scan-bg {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 103px;
}

.dia-scan-bg-logo {
    width: auto;
    height: 22px;
    left: 5px;
    bottom: 68px;
    z-index: 2;
}

.dialog-scan-config-top .iconguanbi {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 14px;
}

.dialog-scan-config-bot {
    padding: 20px 0 0 20px;
    height: 290px;
}

.dialog-scan-config-bot .syn-refer-title{
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    font-weight: bold;
    color: #333333;
    background: #fff;
}

.dialog-banner .dialog-scan-config-bot {
    padding: 30px;
}

.dialog-scan-config-bot .debug-title{
    color: #D9534F;
    padding-bottom: 20px;
    margin-top: 4px;
}
.dialog-scan-config-bot .debug-title i{
    color: #D9534F;
}

.dialog-banner textarea:disabled {
    resize: none;
    border: none;
    outline: none;
    font-size: 12px;
    line-height: 24px;
    width: 100%;
    height: 151px;
    max-height: 151px;
    overflow: auto;
    color: var(--level3Font-color);
    background-color: transparent;
}

.gb-dia-form-item {
    font-size: 12px;
    color: var(--primaryFont-color);
    /* height: 32px; */
    margin-bottom: 20px;
    line-height: 32px;
}

.gb-dia-form-item span.fl {
    width: 84px;
    margin-right: 7px;
}

.gb-dia-btn {
    width: 140px;
    height: 38px;
    margin-left: 91px;
}

/* 资产管理页（未扫描）*/
.gb-asset-empty {
    position: absolute;
    top: 23%;
    left: 0;
    right: 0;
    margin: auto;
    width: 608px;
    height: 332px;
    padding-left: 246px;
    /* background-image: url(../img/zichandatu.png); */
    background-repeat: no-repeat;
    background-position: 6px 14px;
}

.gb-asset-empty>p {
    font-size: 32px;
    margin-bottom: 35px;
    margin-top: 6px;
    color: #0b377e;
}

.gb-asset-empty-item {
    position: relative;
    font-size: 16px;
    padding-left: 53px;
    color: #333;
    margin-bottom: 29px;
    background-position: left center;
    background-repeat: no-repeat;
    /* background-image: url(../img/zichan_duankou.png); */
}

.gb-asset-empty-item div {
    font-size: 14px;
    color: #85a1ce;
    margin-top: 8px;
}

.gb-asset-empty-item i {
    position: absolute;
    top: 3px;
    left: 0;
    font-size: 30px;
    color: var(--main-color);
}

.gb-asset-empty-ip-item {
    /* background-image: url(../img/zichan_ip.png); */
}

.gb-asset-empty-cate-item {
    /* background-image: url(../img/zichan_fenlei.png); */
}

/* 扫描完成 */
.gb-scan-end-text {
    margin-left: 37px;
    margin-top: 12px;
}

.gb-scan-end-text>p {
    font-size: 12px;
    line-height: 14px;
    color: var(--main-color);
}

.gb-to-asset-btn {
    margin-left: 8px;
}

.gb-to-asset-btn .iconfont {
    font-size: 12px;
    margin-left: 7px;
}

/* 折叠情况下的左侧菜单 */
.fold-gb-left {
    width: 65px;
}

.fold-gb-main {
    left: 65px;
}

/* .fold-gb-left .gb-logo {
  padding-left: 15px;
} */

.fold-gb-left .gb-left-menu>a {
    background-size: 151px 53px;
    background-position: left center;
}

/* 资产类别统计 */
.gb-com-two-cols-title {
    position: relative;
    line-height: 24px;
    font-size: 16px;
    color: var(--level3Font-color);
    height: 60px;
    background: #FFFFFF;
    box-shadow: 0 1px 0 0 #E6E6E6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px 0 22px;
}
.gb-com-two-cols-title .gb-com-two-cols-title-left{
    display: flex;
    align-items: center;
}
.gb-com-two-cols-title .gb-com-two-cols-title-left > img{
    width: 24px;
    height: 24px;
    flex-shrink: 0;
}
.gb-com-two-cols-title .gb-com-two-cols-title-left > span{
    font-size: 18px;
    font-family: PingFangSC-Regular, PingFang SC,sans-serif;
    color: #8C8C8C;
    padding-left: 10px;
    flex-shrink: 0;
}
.gb-com-two-cols-title .gb-com-two-cols-title-right{
    display: flex;
    align-items: center;
    height: 100%;
}
.gb-com-two-cols-title .gb-com-two-cols-title-right > i{
    width: 14px;
    height: 14px;
    color: #737373;
    margin-right: 14px;
    flex-shrink: 0;
    font-size: 14px;
    line-height: 14px;
    cursor: pointer;
}
.gb-com-two-cols-title .gb-com-two-cols-title-right > img{
    width: 26px;
    height: 26px;
    color: #737373;
    margin-left: 6px;
    flex-shrink: 0;
    cursor: pointer;
}

/* text */
.gb-com-two-cols-text{
    position: relative;
    font-size: 22px;
    font-family: PingFangSC-Semibold, PingFang SC, sans-serif;
    font-weight: 600;
    color: #333333;
    height: 98px;
    background: transparent;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
}
.gb-com-two-cols-text .gb-com-two-cols-text-left{
    font-size: 22px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: #404040;
}
.gb-com-two-cols-text .gb-com-two-cols-text-right{
    display: flex;
    align-items: center;
}
.gb-com-two-cols-text .gb-com-two-cols-text-right .el-input{
    width: 386px;
    height: 34px;
    line-height: 34px;
    background: #fff;
}

.gb-com-two-cols-text .gb-com-two-cols-text-right .el-button--primary{
    width: 88px;
    height: 34px;
    background: #4285F4;
    border-radius: 0 2px 2px 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}


/* .gb-com-two-cols-title .el-input-group__prepend div.el-select .el-input__inner:focus{
  border-top: 1px solid;
  border-bottom: 1px solid;
}
.gb-com-two-cols-title .el-input__inner:focus{
  border-color:var(--main-color) !important;
}  */

.gb-com-two-cols-title .gb-sea-wrap {
    position: absolute;
    top: -1px;
    right: 25px;
}

.gb-com-two-cols-title .gb-sea-wrap.no-login {
    right: 0;
}

.gb-sea-wrap>span,
.gb-export {
    position: absolute;
    top: 0;
    right: 4px;
    bottom: 0;
    margin: auto;
    width: 20px;
    height: 20px;
    line-height: 18px;
    text-align: center;
    border-radius: 50%;
    border-radius: 12px;
    color: var(--main-color);
    background-color: var(--lv4Background-color);
}

.gb-sea-wrap>span .iconfont,
.gb-export .iconfont {
    font-size: 10px;
    color: var(--main-color);
}

.gb-sea-wrap.fold>span,
.gb-sea-wrap>a.fold.none {
    display: none;
}

.gb-sea-wrap>a.fold {
    float: right;
    width: 26px;
    height: 26px;
    text-align: center;
    line-height: 24px;
    margin-top: 1px;
    background: var(--lv4Background-color);
    border: 1px solid var(--secondaryBorder-color);
    border-radius: 50%;
}

.gb-sea-wrap>a.fold span {
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    background: var(--lv4Background-color);
    border-radius: 50%;
}

.gb-sea-wrap>a.fold:hover span {
    background-color: var(--main-color);
}

.gb-sea-wrap>a.fold:hover span i {
    color: #fff;
}

.gb-sea-wrap>a.fold span i {
    font-size: 12px;
    color: var(--main-color);
}

.gb-export {
    top: 3px;
    right: 0;
    margin: 0;
}

.gb-com-two-cols-main {
    position: absolute;
    top: 54px;
    bottom: 40px;
    /* right: 30px; */
    right: 20px;
    left: 30px;
}

.gb-com-two-cols-left {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 240px;
    /* width: 205px; */
    padding-right: 15px;
}

.gb-com-two-cols-right {
    position: absolute;
    top: 0;
    bottom: 0;
    /* left: 220px; */
    left: 240px;
    right: 0;
    padding: 0 5px 10px 10px;
    background-color: var(--secondaryBackground-color);
    /* box-shadow: 0px 2px 16px 0px rgba(71, 120, 199, 0.12); */
}

.gb-com-two-cols-left-item>div:first-child {
    font-size: 14px;
    height: 34px;
    line-height: 28px;
    color: var(--primaryFont-color);
}

.gb-com-two-cols-left-item>div:first-child.active {
    color: var(--main-color);
}

.gb-com-two-cols-left-item>div:first-child .iconzhankai1,
.gb-com-two-cols-left-item>div:first-child .iconjianhao_huaban {
    cursor: pointer;
    font-size: 13px;
}

.gb-com-two-cols-left-item>div:first-child .iconzhankai1:hover,
.gb-com-two-cols-left-item>div:first-child .iconjianhao_huaban:hover {
    color: var(--main-color);
}

.gb-com-two-cols-left-item>div:first-child span .iconfont {
    font-size: 13px;
    margin-right: 9px;
}

.gb-com-two-cols-left-item .gb-list-wrap {
    padding-top: 0;
    margin-right: 4px;
    margin-bottom: 0;
}

.gb-list-no-drop-item,
.gb-list-drop-item {
    position: relative;
    display: block;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    margin-bottom: 8px;
    margin-right: 10px;
    padding-left: 23px;
    padding-right: 10px;
    color: var(--level3Font-color);
}

.gb-list-drop-item {
    padding: 0;
    height: auto;
}

.gb-list-drop-item i.el-icon-caret-bottom,
.gb-list-no-drop-item i.el-icon-caret-bottom {
    top: 3px;
}

.gb-list-drop-item i,
.gb-list-no-drop-item i {
    position: absolute;
    top: 4px;
    left: 4px;
    color: var(--primaryIcon-color);
    font-size: 14px;
}

.gb-list-no-drop-item p {
    color: var(--level3Font-color);
}

.gb-list-no-drop-item:hover,
.gb-list-no-drop-item.active,
.gb-list-drop-item>a:hover,
.gb-list-drop-item li:hover {
    background-color: #26afea;
    /* box-shadow: 0px 3px 12px 0px rgba(54, 173, 225, 0.54); */
    border-radius: 4px;
    color: #ffffff !important;
}

.gb-list-no-drop-item.active p,
.gb-list-no-drop-item:hover p,
.gb-list-drop-item li:hover span,
.gb-list-drop-item a:hover p,
.gb-list-no-drop-item.active .ellipise {
    color: #ffffff !important;
}

.gb-list-drop-item.active i,
.gb-list-drop-item:hover i,
.gb-list-no-drop-item.active i,
.gb-list-no-drop-item:hover i {
    color: var(--primaryIcon-color) !important;
}

.gb-list-no-drop-item .ellipise {
    max-width: 70%;
    color: var(--secondaryFont-color);
}

.gb-list-no-drop-item .ellipise+p {
    color: var(--level3Font-color);
}

.gb-list-no-drop-item:hover .ellipise,
.gb-list-no-drop-item:hover .ellipise,
.gb-list-no-drop-item.active:hover .ellipise+p,
.gb-list-no-drop-item.active:hover .ellipise+p {
    color: #ffffff !important;
}

.gb-list-drop-item i {
    position: absolute;
    top: 5px;
    left: 9px;
    color: var(--main-color);
    opacity: 0.5;
}

.gb-list-drop-item a {
    display: block;
    color: #333;
    padding-left: 25px;
    padding-right: 10px;
}

.gb-list-drop-item a p {
    color: #85a1ce;
}

.gb-list-drop-item li {
    margin: 8px 0;
    margin-left: 9px;
}

.gb-list-drop-item li a {
    padding-left: 16px;
}

.com-two-cols-right-top {
    position: relative;
    font-size: 20px;
    height: 20px;
    color: var(--primaryFont-color);
    padding-right: 23px;
}

/* .com-two-cols-right-top>span {
  padding-top: 13px;
} */

.com-two-cols-right-top>div {
    height: 20px;
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.com-two-cols-right-top>div a {
    float: left;
    width: 58px;
    height: 20px;
    line-height: 17px;
    text-align: center;
    font-size: 12px;
    color: var(--level3Font-color);
    border-bottom: 1px solid transparent;
}

.com-two-cols-right-top>div a.active {
    color: var(--main-color);
    font-weight: 600;
    border-bottom: 1px solid var(--main-color);
}

.com-two-cols-right-top>div a.active .el-icon-caret-top {
    display: block;
}

.com-two-cols-right-top>div a:hover {
    color: var(--main-color);
    border-bottom: 1px solid var(--main-color);
}

.com-two-cols-right-main {
    position: absolute;
    top: 72px;
    left: 10px;
    right: 5px;
    bottom: 10px;
}

/* 资产表格 */
.gb-ip-td .iconjinggao {
    font-size: 14px;
    margin-left: 6px;
    color: #ec5048;
}

.alert-ip {
    color: #ec5048;
}

.gb-ip-td {
    color: var(--primaryFont-color);
}

.goby_hostname {
    color: var(--secondaryFont-color) !important;
    max-width: 98px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.gb-ip-icons {
    width: 80%;
    margin-top: 5px;
    padding-left: 4px;
}

.gb-ip-icons span {
    float: left;
    width: 18px;
    height: 18px;
    margin: 3px 3px 0 0;
    text-align: center;
    line-height: 18px;
    border-radius: 50%;
    background-color: var(--level3Font-color);
}

.gb-ip-icons span i {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.9);
}

.gb-ip-icons span:hover {
    background-color: var(--main-color);
}

.gb-com-td>div {
    padding: 0 10px;
    /* padding-top: 5px; */
    text-align: center;
}

.gb-com-td img {
    width: 11px;
    height: 11px;
    margin-right: 5px;
}

.gb-com-td>div.empty {
    padding: 0;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    color: #85a1ce;
    text-align: center;
}

.gb-com-td>div.empty:nth-child(1) {
    background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div.empty:nth-child(2) {
    background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div.empty:nth-child(3) {
    background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div.empty:nth-child(4) {
    background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div.empty:nth-child(5) {
    background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div:nth-child(1) {
    background-color: rgba(187, 231, 250, 0.32);
    border: 0;
}

.gb-com-td>div:nth-child(2) {
    background-color: rgba(144, 213, 243, 0.4);
    border: 0;
    margin-top: 1px;
}

.gb-com-td>div:nth-child(3) {
    background-color: rgba(116, 202, 239, 0.6);
    border: 0;
    margin-top: 1px;
}

.gb-com-td>div:nth-child(4) {
    background-color: rgba(91, 191, 234, 0.8);
    border: 0;
    margin-top: 1px;
}

.gb-com-td>div:nth-child(5) {
    background-color: rgba(63, 176, 224, 1);
    border: 0;
    margin-top: 1px;
}

.gb-com-td>div span {
    display: inline-block;
    font-size: 12px;
    color: #1098d2 !important;
    padding: 0 6px;
    height: 16px;
    margin: 3px 5px 3px 0;
    line-height: 16px;
    border-radius: 7px;
    background-color: rgba(255, 255, 255, 0.75);
}

.gb-com-td>div span.bg {
    padding-left: 24px;
    /*background-image: url(../img/assets.png);*/
    background-repeat: no-repeat;
    background-size: 14px 10px;
    background-position: 6px center;
}

/* ip详情 */
.gb-com-full-page-wrap {
    position: relative;
    margin-left: 10px;
}

.gb-com-full-page-wrap .gb-com-two-cols-main {
    top: 39px;
    left: 0;
    right: 0;
    bottom: 42px;
    padding: 10px 5px 10px 10px;
    background-color: var(--secondaryBackground-color);
}

.gb-com-full-page-wrap .gb-com-two-cols-title .gb-sea-wrap {
    right: 22px;
}

.gb-back-btn {
    position: absolute;
    left: 0;
    top: 0;
    font-size: 14px;
    color: var(--main-color);
}

.gb-back-btn i {
    margin-right: 3px;
    font-size: 14px;
}

.ip-detailed-title {
    position: relative;
    margin-bottom: 15px;
}

.ip-detailed-title>span.fl {
    font-size: 18px;
    line-height: 40px;
    color: #0b377e;
    margin-right: 20px;
}

/* .ip-detailed-title .gb-ip-icons {
  padding-top: 10px;
} */

.table-delete-btn {
    font-size: 16px;
    color: var(--main-color);
    opacity: 0.5;
}

.com-table-wrap {
    padding-right: 15px;
}

.asset-ven-wrap,
.asset-pro-wrap {
    margin-bottom: 30px;
    min-height: 60px;
}

.com-table-wrap .asset-ven-wrap:nth-child(1),
.com-table-wrap .asset-pro-wrap:nth-child(1) {
    margin-top: 10px;
}

.ban-item {
    margin-bottom: 2px;
}

.ban-item>a {
    position: relative;
    display: block;
    font-size: 12px;
    color: var(--main-color);
    height: 47px;
    padding-left: 10px;
    line-height: 47px;
    background: rgba(162, 186, 227, 0.16);
}

.ban-item>a i:not(.iconfont) {
    position: absolute;
    top: 18px;
    right: 10px;
    color: var(--level3Font-color);
}

.ban-item .ip-span {
    margin-right: 10px;
    color: var(--primaryFont-color);
}

.ban-item .port-span {
    max-width: 90px;
    padding: 0 6px;
    height: 19px;
    text-align: center;
    line-height: 19px;
    color: #ffffff;
    border-radius: 4px;
    background-color: var(--main-color);
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ban-info {
    display: none;
    font-size: 12px;
    line-height: 22px;
    padding: 10px;
    word-break: break-all;
    word-wrap: break-word;
    white-space: pre-wrap;
    color: var(--secondaryFont-color);
    border-top: none;
}

.unfold-item.ban-item .ban-info {
    display: block;
    min-height: 50px;
    word-break: break-all;
    word-wrap: break-word;
    overflow-y: visible;
    width: 100%;
    resize: none;
    outline: none;
    border: none;
}

/* 新增banner产品标签 */
.banner-product {
    max-width: 116px;
    padding: 0 6px;
    height: 19px;
    line-height: 19px;
    border-radius: 7px;
    margin-top: 13px;
    margin-left: 5px;
    color: #ffffff;
    border-radius: 4px;
    background-color: var(--main-color);
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 厂商列表 */
.ven-pro-list-wrap {
    max-height: 178px;
    padding-left: 10px;
    padding-right: 15px;
}

.ven-pro-list-title {
    font-size: 12px;
    color: var(--level3Font-color);
    line-height: 20px;
    margin-bottom: 5px;
}

.ven-pro-list-title>span {
    position: relative;
    height: 20px;
    line-height: 20px;
    padding-left: 23px;
    padding-right: 8px;
    margin-right: 14px;
    color: #26afea;
    background-color: rgba(38, 175, 234, 0.12);
    /* background-image: linear-gradient(#e5f4fb,
        #e5f4fb),
      linear-gradient(#26afea,
        #26afea);
    background-blend-mode: normal,
      normal; */
    border-radius: 4px;
}

.ven-pro-list-title>span i {
    position: absolute;
    top: 1px;
    left: 6px;
    font-size: 12px;
}

.ven-pro-list-title>p span {
    color: #26afea;
}

.ven-list-item {
    background-position: left center;
    background-size: 13px 9px;
    background-repeat: no-repeat;
    padding: 12px 0;
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.ven-list-item img {
    width: 13px;
    height: 13px;
    margin-right: 8px;
}

.ven-list-item>div.fr {
    width: 54px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    color: #26afea;
    background-color: rgba(38, 175, 234, 0.12);
    /* background-image: linear-gradient(#e9f7fd,
        #e9f7fd),
      linear-gradient(#e9f7fd,
        #e9f7fd);
    background-blend-mode: normal,
      normal; */
    border-radius: 4px;
}

.ven-list-item>div.fr:hover {
    color: #ffffff!important;
    background-image: none;
    background-color: #26afea!important;
    /* box-shadow: 0px 3px 13px 0px rgba(229, 235, 247, 0.88); */
    border-radius: 4px;
}

.ven-list-item>div.fl {
    color: var(--primaryFont-color);
    height: 20px;
    line-height: 20px;
    font-size: 14px;
}

.ven-list-item>div.fl.alert {
    color: #ec5048;
}

.ven-list-item .iconjinggao {
    font-size: 12px;
    color: #ec5048;
    margin-left: 5px;
}

/* 产品列表 */
.pro-list-item {
    position: relative;
    padding: 0;
    height: 61px;
}

.ven-list-item.pro-list-item>div.fr {
    margin-top: 20px;
}

.ven-list-item.pro-list-item {
    background-position: 0 36px;
}

.ven-list-item.pro-list-item>div.fl {
    margin-top: 30px;
    color: var(--level3Font-color);
    font-size: 12px;
}

.ven-list-item.pro-list-item .iconjinggao {
    font-size: 14px;
    color: #ec5048;
    margin-left: 5px;
}

.ven-list-item .pro-name {
    position: absolute;
    top: 12px;
    left: 0;
    font-size: 14px;
    color: var(--primaryFont-color);
}

.asset-pro-wrap .ven-pro-list-title {
    margin-bottom: 12px !important;
}

.asset-pro-wrap .ven-pro-list-wrap {
    max-height: 175px;
}

/* 产品/厂商详情 */
.pro-ven-detailed-title>p {
    font-size: 20px;
    line-height: 28px;
    color: var(--primaryFont-color);
    margin-top: 8px;
    margin-bottom: 4px;
}

.pro-ven-detailed-title>div {
    font-size: 12px;
    line-height: 16px;
    color: var(--level3Font-color);
    margin-bottom: 16px;
}

.pro-ven-detailed-title>div span {
    color: #26afea;
}

.gb-com-two-cols-main-pro-ven .com-table-wrap {
    position: absolute;
    top: 78px;
    left: 10px;
    right: 5px;
    bottom: 10px;
}

/* 资产页左侧新增所有资产选项 */
a.gb-com-two-cols-left-item {
    display: block;
}

a.gb-com-two-cols-left-item .iconfont {
    font-size: 14px !important;
    margin-right: 10px !important;
}

a.gb-com-two-cols-left-item>div {
    position: relative;
    left: -10px;
    padding-left: 10px;
    margin-left: 2px;
    height: 21px !important;
    line-height: 21px !important;
    margin-right: 10px;
}

a.gb-com-two-cols-left-item>div:hover,
a.gb-com-two-cols-left-item>div.active {
    color: #fff;
    background-color: #26afea;
    /* box-shadow: 0px 3px 12px 0px rgba(54, 173, 225, 0.54); */
    border-radius: 4px;
}

a.gb-com-two-cols-left-item>div.active .iconfont,
a.gb-com-two-cols-left-item>div:hover .iconfont {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    /*竖向滚动条的宽度*/
    height: 8px;
    /*横向滚动条的高度*/
}

/* 滚动条轨道的样式 */
::-webkit-scrollbar-track {
    background-color: transparent;
    -webkit-border-radius: 0;
    border-radius: 0;
}

/* 滚动条轨道内滑块的样式 */
::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: var(--primaryBorder-color);
}

/* 当焦点不在当前区域滑块的状态 */
::-webkit-scrollbar-thumb:window-inactive {
    background-color: var(--secondaryBorder-color);
    -webkit-border-radius: 0;
    border-radius: 0;
}

.el-textarea__inner::-webkit-scrollbar {
    width: 11px;
    /*竖向滚动条的宽度*/
}

/* 资产三种维度切换tab */
.gb-list-tab a {
    position: relative;
}

.gb-list-tab .el-icon-caret-top {
    display: none;
    position: absolute;
    font-size: 10px;
    bottom: -3px;
    left: 0;
    right: 0;
    color: var(--main-color);
}

.vu-list-tab .el-icon-caret-top {
    left: 0px;
}

.vu-list-tab .el-icon-caret-topip {
    left: 0px;
}

/* 查看更多按钮 */
.to-more-btn {
    display: block;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    text-align: center;
    color: #333;
    background-color: #ffffff;
    /* box-shadow: 0px 5px 14px 0px rgba(118, 145, 189, 0.12); */
    border-radius: 4px;
}



/* 设置页面 */
.setting_lefticon {
    display: block;
    width: 169px;
    font-size: 14px;
    height: 28px;
    line-height: 28px;
    color: var(--secondaryFont-color);
    margin-bottom: 6px;
    cursor: pointer;
}

.setting_lefticon .iconfont {
    font-size: 14px;
    color: var(--primaryIcon-color);
    margin-left: 4px;
    margin-right: 7px;
}

.setting_lefticon.active,
.setting_lefticon:hover {
    color: #fff;
    background: rgba(38, 175, 234, 1);
    /* box-shadow: 0px 3px 12px 0px rgba(54, 173, 225, 0.54); */
    border-radius: 4px;
}

.setting_lefticon.active .iconfont,
.setting_lefticon:hover .iconfont {
    color: var(--primaryIcon-color);
}

.setting_title {
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: var(--level3Font-color);
}

.gb-com-two-cols-left-item>.setting_lefticon span .iconfont {
    font-size: 17px;
    margin-right: 9px;
}

.setting_right_top {
    width: 100%;
    height: 90%;
}

.setting_right_bottom {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 54px;
    border-top: 1px solid var(--secondaryBorder-color);
}

.setting_right_toptitle {
    position: relative;
    left: -10px;
    width: 100%;
    height: 20px;
    font-size: 20px;
    font-weight: normal;
    font-stretch: normal;
    line-height: 20px;
    letter-spacing: 0px;
    color: var(--primaryFont-color);
    margin: 28px 0px 33px 0;
}

/* 复选框 */
.gb-com-two-cols-right_setting .el-checkbox__inner {
    border-radius: 0%;
}

.gb-com-two-cols-right_setting .el-checkbox,
.subdomain-title {
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: var(--primaryFont-color);
    display: block;
}

.gb-com-two-cols-right_setting .el-checkbox .el-checkbox__label {
    font-size: 12px;
}

.gb-com-two-cols-right_setting .el-checkbox+.el-checkbox {
    margin-left: 24px;
    margin-bottom: 18px;
}

.gb-com-two-cols-right_setting .el-checkbox__input.is-checked+.el-checkbox__label {
    color: var(--primaryFont-color);
}

.el-input.is-active .el-input__inner,
.el-input__inner:focus {
    border-color: var(--main-color);
    color: var(--primaryFont-color);
}

/* .el-input__inner,
.el-textarea__inner {
    background-color: transparent !important;
} */

/* 右侧顶部搜索框背景色为3级背景色 */
.gb-com-two-cols-title .el-input__inner {
    opacity: 0.8;
    /* background-color: var(--lv3Background-color) !important; */
}

/* 下拉框 */
.setting_right_topselect {
    width: 100%;
    margin-top: 10px;
}

.gb-com-two-cols-right_setting .setting_right_topselect p,
.subdomain-main p {
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: var(--primaryFont-color);
    margin-top: 16px;
}

.gb-com-two-cols-right_setting .setting_right_topselect .el-select {
    width: 100%;
    height: 29px;
    border-radius: 2px;
    margin-top: 12px;
}

.setting_btn {
    width: 96px;
    height: 26px;
    border-radius: 4px;
    margin-top: 14px;
    margin-left: 20px;
    text-align: center;
    line-height: 26px;
    padding: 0;
}

.setting_btn:hover {
    background-color: #1f4d97;
}

.gb-com-two-cols-right_setting .el-button--primary,
.gb-com-two-cols-right_setting .el-button--primary:focus,
.gb-com-two-cols-right_setting .el-button--primary:hover {
    padding: 0;
}



.el-select input:hover {
    color: var(--primaryFont-color);
}





/* 速率 */
.setting_int {
    width: 100%;
    height: 29px;
    border-radius: 2px;
    margin-top: 12px;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    line-height: 16px;
    letter-spacing: 0px;
}

.el-input__inner {
    color: var(--primaryFont-color);
    border-radius: 2px;
}

.el-input__inner:hover {
    color: var(--primaryFont-color);
}

.el-textarea__inner {
    padding: 5px 10px;
    color: var(--primaryFont-color);
}

.proxy-tips .iconyuanxingxuanzhongfill {
    color: #52C68D;
    font-size: 14px;
    margin-right: 9px;
}

.proxy-tips .iconguanbi2fill {
    color: #EC5048;
    font-size: 14px;
    margin-right: 9px;
}

.proxy-tips {
    color: var(--level3Font-color);
    font-size: 12px;
    line-height: 14px;
    margin-top: -4px;
}

.proxy-tips>div {
    margin-right: 20px;
}

/* 漏洞等级 */
.vul-level-div span {
    /* display: inline-block; */
    /* width: 50px; */
    height: 18px;
    line-height: 18px;
    padding: 0 6px;
    text-align: center;
    font-size: 12px;
    background-position: center;
    background-size: 100%;
    background-repeat: no-repeat;
}

.ser-lv {
    color: #EC5048;
    /*background-image: url(../img/critical.svg);*/
}

.high-lv {
    color: #EC6F0A;
    /*background-image: url(../img/high.svg);*/
}

.mid-lv {
    color: #7297D2;
    /*background-image: url(../img/medium.svg);*/
}

.low-lv {
    color: #52C68D;
    /*background-image: url(../img/low.svg);*/
}

/* scan */
.scan_port_int {
    position: relative;
    margin-left: 91px;
    margin-bottom: 20px;
}

/* loading组件 */
.goby-loading,
.goby-empty {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    /* background-color: var(--secondaryBackground-color); */
}

.goby-loading img {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 35px;
    height: auto;
}

.goby-empty div {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: 10px;
    transform: translate(-50%, -50%);
}

.pos-rel {
    position: relative;
}

.goby-full-loading {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.5);
}

.goby-full-loading>div {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: 10px;
    color: #fff;
    font-size: 16px;
    transform: translate(-50%, -50%);
}

/* 资产页三个维度统计 */
.right-total {
    font-size: 14px;
    color: var(--level3Font-color);
}

.right-total span {
    color: #26afea;
    cursor: pointer;
}

/* 分页样式 */
.asset_page {
    position: absolute;
    bottom: -38px;
    right: -4px;
}

.el-pagination {
    font-weight: normal;
    padding-left: 0;
}

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
    width: 20px;
    min-width: 20px;
    height: 20px;
    border-radius: 50%;
    color: var(--primaryFont-color);
    background-color: var(--lv4Background-color);
    padding: 0;
    text-align: center;
    line-height: 22px;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: var(--main-color);
    color: #fff !important;
}

.el-pagination__editor.el-input .el-input__inner {
    height: 20px;
}

.el-pagination span:not([class*=suffix]) {
    font-size: 12px;
    line-height: 22px;
}

.el-pagination__total,
.el-pagination__jump {
    color: var(--level3Font-color);
    margin-left: 0;
}

.el-pagination:nth-of-type(1) {
    margin-right: -10px;
    vertical-align: middle;
    display: inline-block;
}

.el-pagination:nth-of-type(2) {
    display: inline-block;
    vertical-align: top;
}

.el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: var(--primaryFont-color);
}

.page_input {
    display: inline-block;
    font-size: 12px;
}

.page_input .el-input {
    width: 41px;
    height: 20px;
}

.page_input .el-input input {
    width: 41px;
    height: 18px;
    color: var(--primaryFont-color);
}

/* 资产页右侧当前item最大宽 */
.asset-right-title {
    float: left;
    max-width: 260px;
    text-overflow: ellipsis;
}

/* Assets more按钮 */
.assets_more {
    position: relative;
    display: block;
}

.assets_more em {
    font-style: normal;
    width: 40px;
    height: 16px;
    line-height: 16px;
    margin-top: 5px;
    margin-left: -5px;
    padding: 0 6px;
    text-align: center;
    font-size: 12px;
    color: #26afea;
    background-image: linear-gradient(#e9f7fd, #e9f7fd),
    linear-gradient(#e9f7fd, #e9f7fd);
    background-blend-mode: normal,
    normal;
    border-radius: 4px;
}

.assets_more:hover em {
    background-image: none;
    background-color: #26afea;
    box-shadow: 0px 3px 13px 0px rgba(229, 235, 247, 0.88);
    color: #ffffff;
}

.more_port_protocal:nth-of-type(1) {
    padding-top: 0;
}

.more_port_protocal:last-of-type(1) {
    padding-bottom: 0;
}

.more_port_protocal {
    /* padding: 5px 0px; */
    font-size: 12px;
    color: #333;
}

/* 小图标加hover样式 */
.seach-hover:hover,
.gb-export:hover {
    background-color: var(--main-color)!important;
}

.seach-hover:hover i,
.gb-export:hover i {
    color: #FFF;
}

/* 扫描页图标 */
.scan-icon-left {
    font-size: 15px !important;
}

/* 资产标题的搜索框样式 */
.gb-sea-wrap {
    border-top: 1px solid;
    border-bottom: 1px solid;
    border-left: 1.2px solid;
    border-right: 1.5px solid;
    border-color: var(--primaryBorder-color);
    border-radius: 12px;
}

.gb-sea-wrap.fold {
    border: none;
}

.gb-sea-wrap:hover {
    border-color: var(--primaryBorder-color);
}

.gb-sea-wrap:active,
.gb-sea-wrap:focus-within {
    border-color: var(--main-color);
    color: var(--primaryFont-color);
}

/* 资产页左侧有价值的分类统计 */
.left-cate-pie {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    /* border: 1px solid red; */
    height: 136px;
}

#cate-two-pie {
    bottom: 156px;
}

.gb-cate-list {
    min-height: 30px;
    padding: 0 10px;
}

.gb-cate-list .goby-empty div {
    margin-top: -4px;
}

.gb-cate-list .gb-cate-list-item {
    padding: 0 10px 0 13px;
    margin: 5px 0;
    font-size: 12px;
    line-height: 20px;
    cursor: pointer;
    /* height: 20px; */
    color: var(--level3Font-color);
}

.gb-cate-list .gb-cate-list-item:hover,
.gb-cate-list .gb-cate-list-item.active {
    background-color: var(--lv4Background-color);
    /* background-image: linear-gradient(#e9f7fd, #e9f7fd),
      linear-gradient(#e9f7fd, #e9f7fd);
    background-blend-mode: normal, normal; */
    border-radius: 4px;
}

.gb-cate-list .gb-cate-list-item>span {
    max-width: 143px;
    line-height: 16px;
    word-break: break-all;
    word-wrap: break-word;
    color: var(--secondaryFont-color);
}

.gb-cate-list .gb-cate-list-item>span+p {
    color: var(--level3Font-color);
}

/* 扫描弹窗高级配置显示按钮动画 */
.scan-zk {
    margin-bottom: 20px;
    text-align: right;
    font-size: 12px;
    font-weight: 400;
    color: var(--primaryFont-color);
    line-height: 16px;
}

.scan-zk-open {
    cursor: pointer;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: #4285F4;
    float: right;
}

.scan-zk i {
    display: inline-block;
    font-size: 12px !important;
    color: #C7D3E7;
}

.scan-zkicon {
    display: inline-block;
    transition: all 0.2s;
    font-size: 12px !important;
    transform: rotate(0deg);
}

.scan-zkicon-go {
    display: inline-block;
    font-size: 12px !important;
    transform: rotate(-180deg);
    transition: all 0.2s;
}

.gb-dia-btn:hover {
    background-color: #1F4D97;
    color: rgba(255, 255, 255, 1);
}

/* 资产列表页换行表示 */
.asset-title-box {
    height: 23px;
    color: var(--primaryFont-color);
    font-size: 20px;
    margin-top: 13px;
}

/* 资产列表的统计弹窗 */
.assmore-list {
    width: 100%;
    height: 33px;
    border-bottom: 1px solid var(--secondaryBorder-color);
    padding: 0 10px;
    line-height: 33px;
    font-size: 12px;
    color: var(--primaryFont-color);
}

.assmore-list.vul {
    color: rgba(236, 80, 72, 1)!important;
}

.assmore-list:hover {
    background: var(--lv4Background-color);
}

.assmore-list p {
    color: var(--level3Font-color);
}

.assmore-list.vul p {
    color: rgba(236, 80, 72, 1)!important;
}

.asset-total-title {
    text-indent: 112px;
}

.asset-total-title span:nth-of-type(1) {
    font-size: 14px;
    color: rgba(255, 255, 255, 1);
    opacity: 1;
    margin-right: 6px;
}

.asset-total-title span:nth-of-type(2) {
    font-size: 14px;
    font-size: 14px;
    color: rgba(255, 255, 255, 1);
    opacity: 0.6;
}

.asset-list-page {
    float: right;
    margin-bottom: 10px;
    padding: 0 24px 0 0px;
}

.dialog-assetmore-config-bot {
    padding: 0 26px 0 0;
    margin: 24px 4px 24px 24px;
}

/* 漏洞列表左侧页面 */
.Percent-title {
    font-size: 12px;
    color: var(--primaryFont-color);
    /* font-weight:bold; */
}

.vu-com-left-strip {
    margin-bottom: 20px;
}

.vu-com-left-strip:nth-last-of-type(1) {
    margin-bottom: 0px;
}

.Percent-title-txt {
    width: 100%;
    height: 12px;
    font-size: 12px;
    color: var(--level3Font-color);
    margin: 6px 0;
}

.Percent-title-txt span:nth-of-type(1) {
    float: left;
}

.Percent-title-txt span:nth-of-type(2) {
    float: right;
}

.Percent-sum {
    width: 100%;
    height: 4px;
    background: var(--lv4Background-color);
    border-radius: 2px;
}

.Percent-sum-fact {
    background: rgba(246, 143, 102, 1);
    height: 100%;
    border-radius: 2px;
}

/* 漏洞列表页面 */
.vu-list-box {
    width: 100%;
    padding: 18px 0;
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.vu-list-main {
    width: 100%;
    height: auto;
    padding: 0 20px;
    padding-right: 0;
}

.vu-list-title .ser-lv,
.vu-list-title .high-lv,
.vu-list-title .mid-lv,
.vu-list-title .low-lv,
.vu-list-ip-main .ser-lv,
.vu-list-ip-main .high-lv,
.vu-list-ip-main .mid-lv,
.vu-list-ip-main .low-lv {
    /* display: inline-block; */
    /* width: 50px; */
    padding: 0 6px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    font-size: 12px;
    background-position: center;
    background-size: 100%;
    background-repeat: no-repeat;
    vertical-align: middle;
    margin-right: 6px;
}

.vu-list-title i {
    color: var(--level3Font-color);
    font-size: 12px;
}

.vu-txt {
    display: inline-block;
    width: 350px;
    height: 18px;
    line-height: 18px;
    font-size: 12px;
    color: var(--primaryFont-color);
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
}

.vu-info {
    width: 54px;
    height: 18px;
    text-align: center;
    line-height: 18px;
    color: rgba(246, 143, 102, 1);
    font-size: 12px;
    background: rgba(246, 143, 102, 0.12);
    border-radius: 4px;
    float: right;
    cursor: pointer;
}

.vu-infototal {
    background: rgba(246, 143, 102, 1);
    color: rgba(255, 255, 255, 1);
    /* box-shadow: 0px 3px 13px 0px rgba(229, 235, 247, 0.88); */
}

.vu-list-main li {
    padding-top: 20px;
    font-size: 12px;
    color: var(--level3Font-color);
}

.vu-list-main li i {
    cursor: pointer;
}

.vu-list-main li .vu-vuip {
    margin-right: 30px;
}

.vu-vumessage {
    display: inline-block;
    margin-right: 20px;
    max-width: 170px;
    height: 18px;
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

.vu-vuUrl {
    display: inline-block;
    margin-right: 20px;
    max-width: 270px;
    height: 18px;
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
}

.vu-right-total span {
    color: #F68F66;
}

.ip-writing {
    display: inline-block;
    line-height: 26px;
    font-size: 14px;
    color: var(--level3Font-color);
    padding-left: 4px;
}

.ip-writing b {
    color: #26AFEA;
    font-weight: normal
}

.gb-com-ip-details {
    padding-left: 20px !important;
}

.ip-detailed-title-text {
    font-size: 18px;
    /* font-weight:bold; */
    color: var(--primaryFont-color);
}

.hode-name {
    color: #EC5048;
}

.port-span {
    margin-top: 13px;
}

.Title-text,
.Version-text {
    margin-left: 10px;
    display: inline-block;
    height: 44px;
    line-height: 44px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--primaryFont-color);
}

.Title-text {
    max-width: 160px;
    /* float: none; */
}

.Version-text {
    max-width: 140px;
    /* float: none; */
}

/* 历史任务下拉 */
.expand-list {
    padding-left: 25px;
    padding-right: 9px;
    padding-bottom: 11px;
    font-size: 12px;
    color: var(--level3Font-color);
}

.expand-item {
    line-height: 16px;
    margin-bottom: 8px;
    word-break: break-all;
    word-wrap: break-word;
}

.expand-item span {
    color: var(--primaryFont-color);
    margin-right: 5px;
}

.expand-list .expand-item:last-child {
    margin-bottom: 0;
}



/* 扫描初始页最近的历史记录 */
.his-recent {
    position: absolute;
    bottom: 20px;
    right: 44px;
}

.his-recent>div {
    position: relative;
    cursor: pointer;
    color: var(--secondaryFont-color);
    font-size: 12px;
    line-height: 20px;
    margin-bottom: 2px;
}

.his-recent>div span:first-child {
    margin-right: 30px;
}

.his-recent>div .iconfont {
    position: absolute;
    top: 0;
    right: -16px;
    font-size: 10px;
    transform: rotate(270deg);
}

.his-recent>div:hover {
    color: var(--main-color);
}

/* ip详情列表 */
.ip-detailed-box {
    width: 100%;
    height: auto;
    /* margin-bottom: 20px !important; */
    border-left: 1px solid var(--secondaryBorder-color);
    border-right: 1px solid var(--secondaryBorder-color);
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.hole-table tr.el-table__row:hover>td {
    /* background: rgba(255, 246, 246, 1) !important; */
}

.iplistleft {
    height: 100%;
    width: 50%;
    float: left;
    border: none;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 12px;
    color: var(--primaryFont-color);
}

.iplistright {
    width: 50%;
    float: right;
    height: auto;
    background-color: var(--secondaryBackground-color);
    word-wrap: break-word;
    word-break: normal;
    border-left: 1px solid var(--secondaryBorder-color);
}

.iplistleft tr {
    border-top: 1px solid var(--secondaryBorder-color);
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.newiptr {
    border-top: none !important;
    border-bottom: none !important;
}

.lastIptr {
    border-bottom: none !important;
}

.iplistleft td:nth-of-type(1) {
    width: 88px;
    padding-left: 10px;
    background: rgba(162, 186, 227, 0.16);
}

.iplistleft td:nth-of-type(2) {
    width: calc(100% - 88px);
    padding: 10px;
    word-wrap: break-word;
}

.iplistright li:nth-of-type(1) {
    width: 100%;
    padding-left: 10px;
    height: 30px;
    line-height: 30px;
    background: rgba(162, 186, 227, 0.16);
    font-size: 12px;
    color: var(--primaryFont-color);
}

.iplistright li:nth-of-type(2) {
    width: 100%;
    padding: 10px;
}

/* 更多操作 */
.more-options {
    position: absolute;
    top: 0;
    right: 0;
}

.more-options .el-dropdown-link {
    display: block;
    width: 26px;
    height: 26px;
    background: var(--lv4Background-color);
    border-radius: 50%;
}

.more-options .el-dropdown-link .iconfont {
    color: var(--main-color);
}

.more-options-list {
    width: 328px;
    /* height: 225px; */
    padding: 0 16px;
    margin: 0;
    border: none;
    top: 0 !important;
    background: var(--lv3Background-color);
    /* box-shadow: 0px 4px 30px 0px rgba(1, 13, 34, 0.18); */
    border-radius: 12px;
}

.more-options-list.poc-page {
    /* height: 282px; */
}

.more-options-list.poc-page .more-op-title {
    padding-bottom: 18px;
}

.more-op-title {
    font-size: 12px;
    color: var(--level3Font-color);
    line-height: 16px;
    padding-top: 11px;
    padding-bottom: 0;
}

.more-op-main {
    padding-left: 42px;
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 34px 34px;
}

.more-op-main-scan {
    /*background-image: url(../img/POCmanagement.png);*/
}

.more-op-main-web {
    /*background-image: url(../img/webfinder.png);*/
}

.more-op-main-poc {
    /*background-image: url(../img/vulnerabilityscanning.png);*/
}

.more-op-main-map {
    /*background-image: url(../img/icon-more-ipmatrix.png);*/
}

.more-op-main-structure {
    /*background-image: url(../img/icon-more-jiegoutu.png);*/
}

.more-op-main>div:first-child {
    font-size: 14px;
    color: var(--primaryFont-color);
    padding-bottom: 3px;
}

.more-options-list .line {
    width: 288px;
    height: 1px;
    margin: 0 auto;
    margin-top: 20px;
    background: var(--secondaryBorder-color);
}

.more-bot {
    font-size: 12px;
    line-height: 20px;
    padding: 10px 0;
    color: var(--primaryFont-color);
}

.more-bot .el-dropdown {
    font-size: 12px;
    color: var(--primaryFont-color);
}

.more-bot>div {
    width: 50%;
    cursor: pointer;
    text-align: center;
    border-right: 1px solid var(--secondaryBorder-color);
}

.more-bot>div:nth-child(2) {
    border-right-color: transparent;
}

.el-dropdown:focus,
.el-dropdown-link:focus,
.more-dropdown:focus {
    outline: none !important;
}

.more-dropdown .el-dropdown-menu__item {
    font-size: 12px;
    color: var(--primaryFont-color);
    line-height: 20px;
    padding: 0 13px;
    margin-bottom: 3px;
}

.more-dropdown .el-dropdown-menu__item:hover {
    color: var(--primaryFont-color);
}

.more-bot .iconfont {
    font-size: 13px;
    color: var(--level3Font-color);
    margin-left: 7px;
}

.more-options-list .el-dropdown-menu__item {
    font-size: 12px;
    padding: 0;
    line-height: 16px;
    margin-top: 19px;
    color: var(--level3Font-color);
}

.more-options-list .el-dropdown-menu__item:hover {
    background-color: var(--lv3Background-color) !important;
    color: var(--level3Font-color);
}

.more-options-list .el-dropdown-menu__item.btn:hover {
    background-color: var(--main-color) !important;
}

.more-options-list .popper__arrow {
    display: none !important;
}

.more-options-list .el-dropdown-menu__item.btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 26px;
    height: 26px;
    text-align: center;
    line-height: 26px;
    color: #fff;
    margin-top: 4px;
    background: var(--main-color);
    border-radius: 50%;
}

.dialog-scan-config-bot.rescan-vul .el-textarea__inner,
.dialog-scan-config-bot.rescan-vul .el-textarea__inner:focus {
    color: var(--level3Font-color);
    background: rgba(140, 158, 194, 0.08) !important;
    border: 1px solid var(--secondaryBorder-color);
    border-radius: 2px;
    height: 60px;
    max-height: 60px;
}

/* 消息通知 */
.notification {
    position: fixed;
    top: 33px;
    right: 11px;
    width: 293px;
    height: 80px !important;
    padding: 20px 40px 0 20px;
    background: var(--lv3Background-color);
    /* box-shadow: 0px 3px 17px 0px rgba(62, 103, 162, 0.15); */
    border-radius: 4px;
    z-index: 99999;
}

.notification .title {
    font-size: 14px;
    line-height: 16px;
    color: var(--primaryFont-color);
    margin-bottom: 3px;
}

.notification .title .iconfont {
    color: var(--level3Font-color);
}

.notification .main {
    font-size: 12px;
    line-height: 20px;
    color: var(--level3Font-color);
}

.notification .main a {
    /* color: var(--main-color);
    padding-bottom: 2px;
    border-bottom: 1px solid var(--main-color); */
}

.notification .iconguanbi {
    position: absolute;
    right: 6px;
    top: 6px;
    font-size: 10px;
    color: var(--secondaryFont-color);
    cursor: pointer;
}

.notification .iconguanbi:hover {
    color: var(--level3Font-color);
}

/* 环境检测 */
.goby-check {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 9999;
    background: rgba(0, 0, 0, 0.5);
}

.goby-check .main {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 466px;
    transform: translate(-50%, -50%);
    padding: 30px 20px;
    background: var(--lv3Background-color);
    border-radius: 4px;
}

.goby-check .main.large {
    padding-bottom: 60px;
}

.goby-check .main-top {
    font-size: 14px;
    line-height: 16px;
    color: var(--primaryFont-color);
    margin-bottom: 15px;
}

.goby-check .main-top .iconfont {
    color: #EC5048;
    font-size: 16px;
    margin-right: 7px;
}

.goby-check .main-text {
    color: var(--level3Font-color);
    font-size: 12px;
    line-height: 18px;
    padding-left: 22px;
    word-wrap: break-word;
    max-height: 274px;
    overflow: auto;
}

.goby-check .main.large .main-text {
    margin-bottom: 10px;
}

.goby-check .to-git {
    position: absolute;
    bottom: 30px;
    right: 30px;
    /* width: 124px; */
    height: 28px;
    line-height: 28px;
    padding: 0 14px;
    font-size: 12px;
    color: #fff;
    background: var(--main-color);
    /* box-shadow: 0px 4px 24px 0px rgba(67, 121, 198, 0.66); */
    border-radius: 4px;
}

.goby-check .to-git .iconfont {
    color: #D3E4FF;
    margin-left: 8px;
    font-size: 12px;
}

.goby-check .to-restart {
    position: absolute;
    top: 20px;
    right: 20px;
    color: var(--secondaryFont-color);
    font-size: 18px;
}

.goby-check .to-restart:hover {
    color: var(--level3Font-color);
}

.goby-check .main-text a {
    display: inline-block;
    color: var(--main-color);
    line-height: 17px;
    border-bottom: 1px solid var(--main-color);
}

.goby-check .main-text p {
    color: var(--level3Font-color);
    margin-top: 23px;
}

/* 关于我们 */
.about-goby {
    padding-left: 20px;
    padding-right: 30px;
    background-color: var(--secondaryBackground-color);
}

.about-goby .title {
    font-size: 20px;
    color: var(--primaryFont-color);
    line-height: 22px;
    padding-top: 26px;
    padding-bottom: 20px;
}

.about-goby img {
    margin-bottom: 13px;
}

.about-goby .vul-info-item span {
    color: var(--level3Font-color);
}

.about-goby .vul-info-item.last {
    height: 48px;
    line-height: 18px;
}

.about-goby .vul-info-item.last span {
    height: 48px;
    padding-top: 6px;
}

.about-goby .vul-info-item.last div {
    padding-top: 7px;
}

.seach-val {
    display: inline-block;
    line-height: 12px;
    color: #333 !important;
    background-color: #FFFF00 !important;
}

.bright-em {
    font-style: normal;
    color: #333 !important;
    background-color: #FFFF00 !important;
}

/* .port-span .bright-em {
  color: #333!important;
  background-color: #FFA200!important;
} */

/* webfinder */
.webfinder-main {
    margin-top: 20px;
}

.webfinder-main ul li {
    width: 100%;
    padding-top: 18px;
    padding-bottom: 13px;
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.webfinder-main ul li:hover {
    background: rgba(236, 241, 249, 1);
}

.webfinder-main div {
    display: inline-block;
    vertical-align: middle;
}

.web-ip {
    width: 140px;
    height: 19px;
    font-size: 12px;
    color: var(--primaryFont-color);
    line-height: 19px;
    /* margin-left: 10px; */
}

.web-port {
    /* width:88px; */
    height: 19px;
    padding: 0 6px;
    /* background:rgba(71,120,199,1); */
    border-radius: 4px;
    /* color: #FFFFFF; */
    font-size: 12px;
    /* text-align: center; */
    line-height: 19px;
    /* margin-left: 20px; */
    margin-right: 6px;
}

.web-Group {
    width: 20px;
    height: 19px;
    background: var(--main-color);
    border-radius: 4px;
    text-align: center;
    line-height: 19px;
    color: #FFFFFF;
    margin-right: 32px;
}

.web-server {
    max-width: 185px;
    margin-right: 6px;
}

.web-title {
    max-width: 430px;
    margin-right: 6px;
}

.web-server,
.web-title {
    height: 19px;
    line-height: 19px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 12px;
    color: var(--level3Font-color);
}

.web-server-copy {
    margin-right: 22px;
}

.web-server-copy i,
.web-title-copy i {
    font-size: 14px;
}

.web-server-copy,
.web-title-copy {
    width: 12px;
    height: 19px;
    line-height: 19px;
    font-size: 12px;
    color: rgba(171, 185, 211, 1);
}

.web-server-copy:hover,
.web-title-copy:hover {
    color: rgba(71, 120, 199, 1);
}

.vu-list-tab .el-icon-caret-top {
    left: 0px;
}

.history-modal {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: .5;
    z-index: 99;
    background: #000;
}

/* 端口more修改样式 */
table .iconDetails- {
    color: #26afea;
    font-size: 8px;
    margin-left: 3px;
    padding: 3px;
    cursor: pointer;
    border-radius: 50%;
    background-color: rgba(38, 175, 234, 0.12);
}

.all-ports-drop .el-dropdown-link {
    font-size: 12px;
    padding: 0 6px;
    height: 15px;
    line-height: 15px;
    cursor: pointer;
    background: rgba(38, 175, 234, 0.12);
    /* box-shadow: 0px 3px 13px 0px rgba(229, 235, 247, 0.88); */
    border-radius: 4px;
    color: rgba(38, 175, 234, 1);
}

.all-ports-drop .el-dropdown-link i {
    display: none !important;
}

.all-ports-drop .el-dropdown-link:hover {
    color: #fff;
    background: rgba(38, 175, 234, 1);
    /* box-shadow: 0px 3px 13px 0px rgba(229, 235, 247, 0.88); */
}

.all-ports-menu {
    width: 155px;
    z-index: 99;
    background: rgba(255, 255, 255, 1);
    /* box-shadow: 0px 1px 18px 0px rgba(140, 166, 208, 0.44); */
}

.all-ports-menu>div>div {
    max-height: 163px;
    overflow-y: auto;
    /* margin-right: 4px; */
}

.all-ports-menu>div>div>div {
    font-size: 12px;
    color: var(--primaryFont-color);
    line-height: 20px;
    /* margin-right: 5px; */
    margin-bottom: 5px;
    cursor: pointer;
    padding-left: 14px;
}

.all-ports-menu>div>div>div span {
    display: block;
    width: 100%;
}

.all-ports-menu>div>div>div:focus,
.all-ports-menu>div>div>div:hover {
    color: var(--primaryFont-color);
    background: var(--lv4Background-color);
}

/* 漏洞详情自定义table */
.info-table {
    width: 100%;
    margin-bottom: 17px;
    margin-top: 3px;
    border: 1px solid var(--secondaryBorder-color);
    border-bottom: none;
    border-collapse: separate;
    border-spacing: 0;
}

.info-table>tr>td:nth-child(1) {
    width: 110px;
    font-size: 12px;
    color: var(--primaryFont-color);
    background: var(--lv4Background-color) !important;
}

.info-table>tr>td:nth-child(2) {
    width: calc(100% - 120px);
}

.info-table>tr>td:nth-child(2),
.info-table>tr>td a {
    font-size: 12px;
    text-indent: 5px;
    color: var(--primaryFont-color);
}

.info-table>tr>td {
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.info-table>tr>td {
    padding: 5px 8px;
}

.info-table>tr>td:nth-child(2) div {
    line-height: 18px;
}

.hole-table .el-table__expanded-cell[class*=cell] {
    padding-left: 40px;
}

.vul-ip-table .el-table__expanded-cell[class*=cell] {
    padding: 10px 10px 20px 24px;
}

/* 历史任务弹窗删除划过 */
.iconfont.task-del:hover,
.iconreport-1:hover,
.export-icon:hover {
    /* color: var(--main-color) !important; */
}

/* 关于我们表格 */
.about-goby .info-table>tr>td:nth-child(1) {
    color: var(--primaryFont-color);
}

.about-goby .info-table>tr>td:nth-child(2) {
    text-indent: 0;
}

.about-goby .info-table>tr>td {
    padding: 5px 10px;
}

.about-goby .info-table>tr>td:nth-child(1) {
    width: 97px;
}

/* 无下拉框的搜索 */
.gb-com-two-cols-title .el-input.no-sel-input {
    width: 154px;
}

.gb-com-two-cols-title .el-input.no-sel-input input {
    border-radius: 12px;
    padding-left: 10px;
    padding-right: 25px;
}

/* 截图 */
.screenshots-item {
    position: relative;
    width: 60px;
    height: 45px;
    margin-top: 8px;
    /* border: 1px solid red; */
    background-position: center;
    background-repeat: no-repeat;
    /* background-image: url(../img/test.png); */
    background-size: 100%;
    overflow: hidden;
}

.screenshots-item img {
    width: 100%;
    height: 100%;
    border: 1px solid var(--secondaryBorder-color);
}

.screenshots-wrap.favicon .screenshots-item img {
    border: none;
}

.screenshots-item.icons {
    margin-top: 20px;
}

.screenshots-wrap {
    position: relative;
    height: 100%;
    border: 1px solid var(--secondaryBorder-color);
    border-top: none;
}

.screenshots-wrap.favicon {
    height: 100%;
}

.screenshots-wrap.favicon .screenshots-name {
    min-height: 34px;
    height: 100%;
}

.screenshots-wrap.favicon .screenshots-main {
    min-height: 34px;
    padding-left: 98px;
}

.screenshots-main {
    padding-left: 88px;
    min-height: 110px;
    padding-top: 10px;
}

.screenshots-main .screenshots-item {
    display: inline-block;
    width: 119px;
    height: 90px;
    margin-left: 9px;
    margin-bottom: 6px;
    margin-top: 0;
}

.screenshots-wrap.favicon .screenshots-main .screenshots-item {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: 0;
    margin-right: 6px;
    margin-top: 0;
}

.screenshots-name {
    position: absolute;
    top: 0;
    left: 0;
    width: 88px;
    min-height: 110px;
    height: 100%;
    color: var(--primaryFont-color);
    font-size: 12px;
    padding-top: 10px;
    padding-left: 10px;
    background: rgba(162, 186, 227, 0.16);
}

.export-weblist {
    position: absolute;
    top: 24px;
    right: 20px;
    padding: 0 10px;
    height: 26px;
    line-height: 26px;
    color: #fff;
    font-size: 12px;
    background: var(--main-color);
    /* box-shadow: 0px 4px 24px 0px rgba(67, 121, 198, 0.66); */
    border-radius: 4px;
    cursor: pointer;
;
}

.export-weblist:hover {
    background: var(--main-color);
}

.export-weblist .iconfont {
    font-size: 11px;
    color: #fff;
    margin-right: 5px;
}

/* bannerlist增加截屏 */
.banner-shot-wrap {
    position: relative;
    min-height: 110px;
}

.banner-shot-wrap .shot-left {
    margin-right: 120px;
}

.banner-shot-wrap .shot-right {
    position: absolute;
    top: 10px;
    right: 0;
}

.banner-shot-wrap .shot-right img {
    width: 120px;
    height: 90px;
    border: 1px solid var(--secondaryBorder-color);
}

/* webfinder增加截屏 */
.el-popover {
    padding: 0;
    min-width: 120px;
    border: none;
    margin-top: 5px !important;
}

.weblist-shot {
    position: relative;
}

.weblist-shot img {
    width: 120px;
    height: auto;
    border: 1px solid var(--secondaryBorder-color);
    transition: all 0.5s ease 0s;
    /* box-shadow: 0px 7px 26px 0px rgba(39, 65, 108, 0.42); */
}

.weblist-shot+.popper__arrow {
    display: none !important;
}

.web-ip .iconfont {
    color: var(--main-color) !important;
    font-size: 12px !important;
    margin-left: 3px;
}

/* weblist样式修改 */
.weblist-title span {
    color: var(--main-color);
    cursor: default;
}

/* 日语导航字体变小 */
.ja-small {
    font-size: 12px !important;
}

/* banner下显示证书 */
.cert {
    margin: 0 10px;
}

.cert .iconfont {
    color: var(--main-color);
    font-size: 12px;
    margin-right: 8px;
}

.cert>div:nth-child(1) {
    color: var(--main-color);
    font-size: 12px;
    cursor: pointer;
    padding: 8px 0;
    border-top: 1px solid var(--secondaryBorder-color);
}

.cert .el-textarea__inner {
    padding: 0 10px;
}

.cert div:last-child {
    padding: 0 20px;
}

.fav-img {
    width: 16px;
    height: 16px;
    margin: 14px 6px 0 10px;
}

/* 扫描说明 */
#scan-dia .iconchangjianwentixin,
#add-asset .iconchangjianwentixin {
    font-size: 12px !important;
    color: var(--level3Font-color) !important;
}

/* 列表页增加icon */
.fav-icon {
    width: 14px;
    height: 14px;
    cursor: default;
    margin-left: 2px;
}

/* 弹窗导入 */
.dia-import {
    position: absolute;
    bottom: 10px;
    right: 18px;
    height: 24px;
    line-height: 24px;
    padding: 0 10px;
    font-size: 12px;
    cursor: pointer;
    color: #fff;
    /* border: 1px solid rgba(255, 255, 255, 0.5); */
    border-radius: 4px;
    background: rgba(255, 255, 255, 0);
}

.dia-import .iconfont {
    font-size: 11px;
    margin-right: 8px;
}

.dia-import:hover {
    background: rgba(255, 255, 255, 0.1);
}

.clickAble {
    cursor: pointer;
}

.clickAble:hover {
    color: #26AFEA;
}

.default {
    cursor: default !important;
}

/* 截屏放大 */
.screenshot-cover {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 60px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.3);
}

.screenshot-pop {
    height: 356px;
}

.screenshot-pop img {
    width: 609px;
    height: 356px;
}

.screenshot-cover .iconfont.iconbig- {
    color: #fff;
    font-size: 18px;
}

.screenshot-pop .popper__arrow {
    display: none !important;
}

.screenshots-item:hover .screenshot-cover {
    display: block;
}

.weblist-shot .screenshot-cover {
    width: 120px;
    height: 90px;
    line-height: 90px;
}

.weblist-shot:hover .screenshot-cover {
    display: block;
}

.transparent {
    opacity: 0;
    z-index: -1;
}

/* 自定义端口分组 */
.add-port-group-btn {
    position: absolute;
    bottom: -1px;
    left: -1px;
    right: -1px;
    /* width: 100%; */
    font-size: 12px;
    height: 35px;
    color: var(--primaryFont-color);
    line-height: 35px;
    text-align: center;
    color: #fff;
    background: var(--main-color);
    border-radius: 0px 0px 2px 2px;
}

.add-port-group-btn i {
    font-size: 10px;
    color: #fff;
    margin-right: 6px;
}

.add-port-group-warp .gb-dia-form-item span.fl {
    width: 82px;
}

.add-port-group-warp .gb-task-name {
    width: 377px;
}

.add-port-group-warp .el-transfer-panel__list,
.add-port-group-warp .el-transfer-panel__body {
    height: 192px;
}

.add-port-group-warp .el-transfer-panel {
    width: 168px;
    border-radius: 2px;
    border: 1px solid var(--primaryBorder-color);
    background-color: transparent;
}

.add-port-group-warp .el-transfer__buttons {
    width: 40px;
    padding: 0 10px;
}

.add-port-group-warp .el-transfer__button {
    width: 20px;
    height: 20px;
    padding: 0;
    border-radius: 50%;
    box-shadow: none;
}

.add-port-group-warp .el-transfer-panel .el-transfer-panel__header {
    display: none;
}

.add-port-group-warp .el-checkbox__label {
    font-size: 12px;
    color: var(--primaryFont-color);
}

.add-port-group-warp .el-checkbox+.el-checkbox {
    margin-left: 0;
}

.add-port-group-warp .el-button--primary {
    background-color: var(--main-color);
}

.add-port-group-warp .el-button--primary i {
    color: #fff;
}

.add-port-group-warp .el-transfer__button.is-disabled,
.add-port-group-warp .el-transfer__button.is-disabled:hover {
    background-color: var(--lv4Background-color);
    border: none;
    cursor: default;
}

.add-port-group-warp .el-transfer__button.is-disabled i {
    font-size: 12px;
    color: var(--secondaryFont-color);
}

.add-port-group-warp .el-transfer-panel__item {
    padding-left: 10px;
}

.add-port-group-warp .el-transfer-panel__item.el-checkbox .el-checkbox__label {
    padding-left: 22px;
}

.add-port-group-warp .gb-dia-btn {
    margin-left: 88px;
}

.change-port-sel,
.change-vul-sel {
    position: relative;
    width: 375px;
    height: 287px;
    padding-bottom: 30px;
}

.change-vul-sel {
    height: 240px;
    padding-bottom: 0;
    padding-top: 40px;
}

.change-vul-sel .vul-sel-input {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    width: 355px;
}

.vul-sel-input .el-input__inner {
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    border-radius: 16px;
    border: 1px solid var(--primaryBorder-color);
}

.vul-sel-input .el-input__suffix {
    top: 2px;
    width: 18px;
    height: 18px;
    color: var(--main-color);
    border-radius: 50%;
    background-color: var(--lv4Background-color);
}

.vul-sel-input .el-input__icon {
    width: auto;
    height: auto;
    font-size: 10px;
    line-height: 18px;
}

.change-port-sel>div:first-child,
.change-vul-sel>div:first-child {
    height: 240px;
    overflow: auto;
}

.change-vul-sel>div:first-child {
    height: 180px;
}

.change-port-sel .popper__arrow,
.change-vul-sel .popper__arrow {
    left: 35px !important;
}

#scan-dia .el-dropdown-link,
#add-asset .el-dropdown-link {
    display: block;
    width: 375px;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    color: var(--primaryFont-color);
    border-radius: 4px;
    padding: 0 10px;
    border: solid 1px #ced9e5;
}

.change-port-sel .el-dropdown-menu__item:nth-child(11):before,
.change-vul-sel .el-dropdown-menu__item:nth-child(8):before {
    display: block;
    content: '';
    width: 100%;
    height: 1px;
    background-color: var(--secondaryBorder-color);
}

.change-port-sel .el-dropdown-menu__item,
.change-vul-sel .el-dropdown-menu__item {
    font-size: 12px;
    line-height: 30px;
    line-height: 30px;
    color: var(--primaryFont-color);
}

.change-port-sel .el-dropdown-menu__item:hover,
.change-vul-sel .el-dropdown-menu__item:hover {
    color: var(--primaryFont-color);
}

.change-port-sel .el-dropdown-menu__item.active,
.change-vul-sel .el-dropdown-menu__item.active {
    color: var(--primaryFont-color);
    font-weight: 600;
}

.change-port-sel .iconhacker-,
.change-vul-sel .iconhacker- {
    font-size: 14px;
    margin-right: 8px;
}

.change-port-sel .iconlajitong-,
.change-port-sel .iconedit-,
.change-vul-sel .iconlajitong-,
.change-vul-sel .iconedit- {
    float: right;
    font-size: 12px;
    margin-left: 8px;
    font-weight: normal !important;
    color: #4A7DD1;
}

.add-port-group-warp .el-transfer-panel__filter {
    margin: 10px;
    line-height: 22px;
}

.add-port-group-warp .el-transfer-panel__filter .el-input__inner {
    height: 22px;
    line-height: 22px;
    font-size: 12px;
}

.add-port-group-warp .el-input--small .el-input__icon {
    line-height: 22px;
}

.user-svg {
    width: 12px;
    height: 14px;

}

.add-port-group-warp .el-transfer-panel__body .el-input__prefix {
    width: 18px;
    height: 18px;
    left: 123px;
    top: 2px;
    color: var(--main-color);
    border-radius: 50%;
    background-color: var(--lv4Background-color);
}

.add-port-group-warp .el-transfer-panel__body .el-input__prefix .el-input__icon {
    width: 18px;
    height: 18px;
    margin-left: 0;
    line-height: 18px;
}

.add-port-group-warp .el-transfer-panel__filter .el-input__inner {
    padding-left: 10px;
    padding-right: 35px;
}

.add-port-group-warp .el-icon-search:before {
    font-size: 10px;
    position: relative;
    top: -2px;
}

/* 新增hostnames */
.hostnames,
.hostnames .screenshots-name,
.hostnames .screenshots-main {
    height: 100%;
    min-height: 34px;
    padding: 0 10px;
}

.hostnames .screenshots-name {
    padding-top: 10px;
}

.hostnames-item {
    padding-left: 10px;
    font-size: 12px;
    margin-bottom: 10px;
}

.hostnames .screenshots-main {
    padding-top: 10px;
    padding-left: 78px !important;
}

/* 关于我们致谢 */
.thank-you-title {
    margin-top: 23px;
}

.thank-you-subtitle {
    color: var(--primaryFont-color);
    font-size: 12px;
    line-height: 18px;
}

.thank-you-warp {
    width: 100%;
    height: 180px;
    /* overflow: hidden; */
    margin-top: 8px;
    padding: 6px 0;
    background: var(--secondaryBackground-color) !important;
}

.thank-you-main li {
    width: 100%;
    height: 36px;
    line-height: 36px;
    font-size: 12px;
    color: var(--primaryFont-color);
    margin-left: 12px;
}

.thank-you-main li>span:nth-child(1) {
    display: inline-block;
    min-width: 90px;
    color: var(--level3Font-color);
    margin-right: 8px;
}

.thank-you-main li>span:nth-child(n+2):nth-last-child(n+2) {
    color: var(--main-color);
    font-weight: 600;
}

.thank-you-main li>span:nth-child(1):before {
    content: '●';
    color: 'var(--main-color)';
    margin-right: 10px;
}

/*Plugin CSS*/
.str_wrap {
    overflow: hidden;
    width: 100%;
    position: relative;
    -moz-user-select: none;
    -khtml-user-select: none;
    user-select: none;
    white-space: nowrap;
}

.str_wrap.str_active {
    /* background: #f1f1f1; */
}

.str_move {
    white-space: nowrap;
    position: absolute;
    top: 0;
    left: 0;
    cursor: move;
}

.str_move_clone {
    display: inline-block;
    vertical-align: top;
    position: absolute;
    left: 100%;
    top: 0;
}

.str_vertical .str_move_clone {
    left: 0;
    top: 100%;
}

.str_down .str_move_clone {
    left: 0;
    bottom: 100%;
}

.str_vertical .str_move,
.str_down .str_move {
    white-space: normal;
    width: 100%;
}

.str_static .str_move,
.no_drag .str_move,
.noStop .str_move {
    cursor: inherit;
}

.str_wrap img {
    max-width: none !important;
}

/* 历史任务批量删除 */
.task-muldel {
    position: absolute;
    bottom: 18px;
    left: 30px;
    right: 20px;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    color: var(--level3Font-color);
    padding: 0 10px;
    background-color: var(--lv4Background-color);
    border-radius: 2px;
}

.task-muldel a {
    color: var(--main-color);
    margin-left: 17px;
}

.task-muldel i {
    float: right;
}

/* 新增服务器弹窗 */
#add-server .gb-dia-form-item span.fl {
    width: 74px;
    margin-right: 0;
}

#add-server .gb-task-name {
    width: 400px;
}

#add-server .tips {
    font-size: 14px;
    color: var(--secondaryFont-color);
    line-height: 16px;
    margin-bottom: 20px;
}

#add-server .tips a {
    font-size: 14px;
    color: var(--main-color);
    text-decoration: underline;
}

/* 设置 fofa账号 */
.subdomain-title {
    margin-bottom: 14px;
}

.subdomain-main {
    padding-left: 20px;
}

.subdomain-main p {
    margin-bottom: 9px;
}

.subdomain-main p a {
    color: var(--main-color);
    margin-left: 4px;
    text-decoration: underline;
}

.subdomain-main p span {
    color: var(--level3Font-color);
}

.subdomain-main .iconchangjianwentixin {
    position: absolute;
    top: 2px;
    left: 95px;
    font-size: 13px;
    color: var(--level3Font-color);
}

/* 网络结构图 */
#topo {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

#topo circle.node {
    fill: lightsteelblue;
    stroke: #fff;
    stroke-width: 3px;
}

#topo circle.leaf {
    stroke: #fff;
    stroke-width: 1px;
}

#topo path.hull {
    fill: lightsteelblue;
    fill-opacity: 0.3;
}

#topo line.link {
    /* stroke: red; */
    stroke: var(--level3Font-color);
    pointer-events: none;
}

#topo .legend {
    position: absolute;
    top: 20px;
    right: 20px;
}

#topo .legend>div {
    font-size: 12px;
    color: var(--level3Font-color);
    line-height: 12px;
    margin-bottom: 8px;
}

#topo .legend>div span {
    float: left;
    width: 12px;
    height: 12px;
    margin-right: 4px;
    border-radius: 50%;
}

#topo .groupname-tips {
    position: fixed;
    top: 0;
    left: 0;
    padding: 5px;
    background-color: #fff;
    border: 1px solid #000;
}

#topo.hover circle.node,
#topo.hover circle.leaf {
    opacity: 0.1;
}

#topo.hover path.hull {
    fill-opacity: 0.1;
}

#topo.hover line.link {
    stroke-opacity: 0.1;
}

#topo em {
    display: none !important;
}

#topo #goby-empty {
    top: 40px;
    left: 30px;
    right: 20px;
    bottom: 40px;
}

/* 扫描弹窗加引入IP库入口 */
.scan-dia-ips-opt {
    position: relative;
}

.ip-lib-enter {
    position: absolute;
    top: 0;
    right: 0;
    width: 54px;
    height: 54px;
    text-align: center;
    line-height: 52px;
    background: rgba(242, 247, 255, 1);
    border: 1px solid rgba(207, 217, 229, 1);
    border-left: none;
    border-radius: 0 2px 2px 0;
}

.ip-lib-enter img {
    width: 26px;
    height: 26px;
}

.scan-dia-ips-opt .gb-task-name {
    width: 321px;
}

#add-asset .scan-dia-ips-opt .gb-task-name {
    width: 375px;
}

/* 左侧导航添加登录按钮 */
.menu-login {
    height: 87px;
    text-align: center;
}

.menu-login img {
    width: 46px;
    height: 46px;
    margin-top: 16px;
    margin-bottom: 8px;
}

.menu-login a {
    display: inline-block;
    width: 55px;
    height: 22px;
    line-height: 22px;
    /* border: 1px solid rgba(255, 255, 255, 0.3); */
    font-size: 12px;
    color: var(--navOtherFont-color);
    border-radius: 2px;
}

.menu-user-dropdown-list {
    width: 206px;
    color: var(--primaryFont-color) !important;
}

.menu-user-dropdown-list .el-dropdown-menu__item {
    padding: 0;
}

.menu-user-dropdown-list li:hover {
    color: var(--primaryFont-color) !important;
}

.menu-user-dropdown .el-dropdown-link {
    font-size: 12px;
    color: var(--navOtherFont-color);
}

.menu-user-dropdown .el-dropdown-link p {
    display: inline-block;
    max-width: 42px;
}

.menu-user-dropdown .iconfont {
    position: relative;
    top: -3px;
    font-size: 8px;
    color: var(--navOtherFont-color);
    margin-left: 8px;
}

.user-opt {
    position: relative;
    height: 40px;
    /* height: 70px; */
    font-size: 16px;
    padding-left: 12px;
    line-height: 16px;
    padding: 0 12px;
    padding-top: 10px;
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.user-opt>div {
    /* height: 27px; */
    /* height: 57px; */
}

.user-opt p {
    font-size: 14px;
    color: var(--primaryFont-color);
    padding-left: 12px;
    padding-top: 10px;
    max-width: 280px;
}

.user-opt .score {
    font-size: 12px;
    color: var(--level3Font-color);
    padding-top: 5px;
    padding-left: 12px;
}

.user-opt .iconfont {
    position: absolute;
    top: 10px;
    right: 12px;
    font-size: 10px;
    -webkit-transform: rotate(-90deg);
    color: var(--primaryIcon-color) !important;
}

.lib-opt>div,
.news-opt>div {
    font-size: 12px;
    padding: 0 12px;
    height: 20px;
    line-height: 20px;
    margin-bottom: 10px;
}

.lib-opt .fr,
.news-opt .fr {
    position: absolute;
    top: 2px;
    right: 10px;
    padding: 0 5px;
    height: 16px;
    color: #fff;
    line-height: 16px;
    text-align: center;
    font-style: normal;
    font-size: 12px;
    border-radius: 8px;
    background: #EC5048;
}

.news-opt {
    position: relative;
    margin-top: 10px;
}

.lib-opt {
    border-top: 1px solid var(--secondaryBorder-color);
}

.lib-opt>div {
    margin-top: 10px;
}

.sign-out-opt {
    border-top: 1px solid var(--secondaryBorder-color);
    font-size: 12px;
    padding: 0 12px;
    height: 30px;
    line-height: 20px;
}

.sign-out-opt>div {
    padding-left: 12px;
    margin-top: 10px;
    color: var(--primaryFont-color);
}

.menu-user-dropdown-list .el-dropdown-menu__item:not(.is-disabled):hover {
    background-color: transparent;
}

.sign-out-opt>div:hover,
.lib-opt>div:hover,
.user-opt>div:hover,
.news-opt>div:hover {
    background: var(--lv4Background-color);
}

/* diff code */
.hljs {
    display: inline-block;
    padding: 0;
    background: transparent;
    vertical-align: middle
}

.d2h-file-header {
    display: none
}

/* 表单报错 */
.form-error .el-textarea__inner {
    border: 1px solid rgb(236, 80, 72) !important;
}

.form-error .el-textarea__inner:hover,
.form-error .el-textarea__inner:focus {
    border: 1px solid rgb(236, 80, 72) !important;
}

/* check环境新增按钮 */
.goby-check-btns {
    margin-top: 20px;
    padding-bottom: 6px;
}

.goby-check-btns .el-button--default,
.goby-check-btns .el-button--default:focus,
.goby-check-btns .el-button--default:hover {
    background: var(--main-color);
    /* box-shadow: 0px 4px 5px 0px rgba(67, 121, 198, 0.66); */
    border-radius: 4px;
    font-size: 12px;
    border: none;
    width: 84px;
    padding: 9px 0;
    color: #ffffff;
}

.goby-check-btns .el-button--default.exit,
.goby-check-btns .el-button--default.exit:focus,
.goby-check-btns .el-button--default.exit:hover {
    color: var(--secondaryFont-color) !important;
    background: var(--lv4Background-color);
    /* box-shadow:0px 2px 5px 0px rgba(67, 121, 198, 0.19); */
}

/* 重启和关闭按钮 */
.gb-lang-dropdown.exit .el-dropdown-menu__item {
    text-align: left;
}

.gb-lang-dropdown .el-dropdown-menu__item .iconfont {
    font-size: 12px;
    color: var(--primaryFont-color);
    margin-right: 5px;
}

.gb-lang-dropdown .el-dropdown-menu__item:hover .iconfont {
    color: #66b1ff;
}

.his-dot,
.update-dot {
    position: absolute;
    top: 0;
    left: 76px;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: #EC5048;
}

/* 扫描选择单个漏洞 */
.change-vul-sel .goby-empty {
    top: 164px;
}

.change-vul-sel .goby-empty div {
    top: -40px;
}

/* 内测标识 */
.beta-flag {
    /* position: absolute;
    top: 6px;
    left: 0;
    font-size: 12px; */
    /* padding: 0 8px;
    height: 19px;
    line-height: 19px;
    color: var(--navOtherFont-color);
    border: 1px solid var(--navOtherFont-color);
    border-left: none;
    border-radius: 0px 9px 9px 0px; */
}

/* 新增body弹窗 */
#body-dia .el-dialog {
    border-radius: 4px;
    /* box-shadow: 0px 5px 24px 0px rgba(71, 120, 199, 0.29); */
}

#body-dia .el-textarea.is-disabled .el-textarea__inner {
    background-color: var(--primaryBackground-color);
    border-color: transparent;
    color: var(--primaryFont-color);
    font-size: 12px;
    line-height: 22px;
    height: 313px;
}

#body-dia .bot-close {
    height: 54px;
    padding-top: 14px;
    background-color: var(--lv3Background-color);
    border-top: 1px solid var(--secondaryBorder-color);
}

#body-dia .bot-close a {
    padding: 5px 30px;
    color: #fff;
    font-size: 14px;
    margin-right: 20px;
    background: var(--main-color);
    /* box-shadow: 0px 2px 13px 0px rgba(67, 121, 198, 0.54); */
    border-radius: 4px;
}

#body-dia .dialog-scan-config-bot {
    padding: 20px;
}

#body-dia .dialog-scan-config-main {
    box-shadow: none;
}



/* 皮肤 */
.el-collapse-item__wrap,
.el-tree {
    background-color: var(--secondaryBackground-color) !important;
}

.el-tree-node:focus>.el-tree-node__content {
    background-color: transparent;
}

.el-dialog {
    background-color: transparent !important;
}

.el-select-dropdown,
.el-dropdown-menu,
.el-autocomplete-suggestion {
    background-color: var(--lv3Background-color);
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover,
.el-dropdown-menu__item:hover,
.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover,
.el-autocomplete-suggestion li:hover,
.el-autocomplete-suggestion li:focus,
.el-autocomplete-suggestion.is-loading li:hover {
    background-color: var(--lv4Background-color);
}

.el-popper[x-placement^=bottom] .popper__arrow {
    border-bottom-color: var(--primaryBorder-color);
}

.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: var(--lv3Background-color);
}

.navcur-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: auto !important;
    height: auto !important;
}

.gb-vul-empty-svg {
    position: absolute;
    top: 14px;
    left: 6px;
    width: 189px !important;
    height: 187px !important;
}

.report-empty-svg {
    position: absolute;
    top: 14px;
    left: 6px;
    width: 222px !important;
    height: 185px !important;
}

.gb-asset-empty-svg {
    position: absolute;
    top: 14px;
    left: 6px;
    width: 187px !important;
    height: 206px !important;
}

.bar-self-item span.fr {
    color: var(--level3Font-color);
}

.el-table--border::after,
.el-table--group::after,
.el-table::before {
    background-color: var(--secondaryBorder-color);
}

.el-input__inner,
.el-textarea__inner,
#scan-dia .el-dropdown-link,
#add-asset .el-dropdown-link {
    border-color: var(--primaryBorder-color);
}

.el-input__inner:hover,
.el-textarea__inner:hover,
#scan-dia .el-dropdown-link:hover,
#add-asset .el-dropdown-link:hover {
    border-color: var(--primaryBorder-color);
}

.el-input__inner:focus,
.el-textarea__inner:focus,
.el-select .el-input.is-focus .el-input__inner,
#scan-dia .el-dropdown-link:focus,
#add-asset .el-dropdown-link:focus {
    border-color: var(--main-color);
    color: var(--primaryFont-color);
}

.el-input-group__append,
.el-input-group__prepend {
    color: var(--primaryFont-color);
    /* background-color: var(--lv4Background-color); */
}

.el-collapse-item__header,
.el-collapse,
.add-poc-step-test .el-collapse-item__header,
.el-collapse-item__wrap {
    border: none !important;
}

.el-tabs--card>.el-tabs__header {

}

.el-tabs--card .el-tabs__nav-scroll{
    background: transparent;
}

.add-poc-step-test .el-tabs__item {
    border-left: none !important;
    border-bottom: none !important;
    background-color: #E6E9ED;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: #404040 !important;
    border-radius: 0 !important;
}

.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover,
.el-dropdown-menu__item:focus .iconfont,
.el-dropdown-menu__item:not(.is-disabled):hover .iconfont {
    color: var(--primaryFont-color);
}

.more-options-list .el-dropdown-menu__item:hover .more-op-main>div:last-child {
    color: var(--level3Font-color);
}

.more-options-list .el-dropdown-menu__item.btn:hover,
.more-options-list .el-dropdown-menu__item.btn:hover i {
    color: #ffffff !important;
}

#exp-dia .el-dialog {
    background-color: var(--lv3Background-color) !important;
}

.el-form-item__label {
    color: var(--primaryFont-color);
}

.el-message-box {
    background-color: var(--lv3Background-color) !important;
}

.el-message-box__btns .el-button--default:nth-child(1),
.el-message-box__btns .el-button--default:nth-child(1):hover {
    color: var(--secondaryFont-color) !important;
    background-color: var(--lv4Background-color) !important;
}

.el-message-box__title {
    color: var(--primaryFont-color) !important;
}

.el-message-box__message p {
    color: var(--secondaryFont-color) !important;
}

.el-message-box__headerbtn .el-message-box__close,
.el-dialog__headerbtn .el-dialog__close {
    color: var(--secondaryFont-color) !important
}

.el-message-box__headerbtn .el-message-box__close:hover,
.el-dialog__headerbtn .el-dialog__close:hover {
    color: var(--level3Font-color) !important;
}

/* 进度条 */
.el-progress-bar__outer {
    background-color: var(--primaryBorder-color);
}

.el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: var(--lv3Background-color);
}

.el-popper[x-placement^=top] .popper__arrow {
    border-top-color: var(--primaryBorder-color);
}

.el-progress__text {
    color: var(--level3Font-color);
}

/* 表格筛选 */
.el-table-filter {
    border-color: var(--primaryBorder-color);
    background-color: var(--lv3Background-color);
}

.el-table-filter__bottom {
    border-top-color: var(--primaryBorder-color);
}

.el-table-filter__bottom button {
    color: var(--primaryFont-color);
}

.el-table-filter__bottom button:hover,
.el-table th>.cell.highlight {
    color: var(--main-color);
}

.el-table-filter__bottom button.is-disabled {
    color: var(--level3Font-color);
}

/* 编辑器 */
.simditor .simditor-wrapper,
.simditor .simditor-toolbar,
.simditor {
    border-color: var(--primaryBorder-color);
    background-color: transparent !important;
}

.simditor-toolbar a {
    color: #404040 !important;
}

.simditor .simditor-body,
.editor-style,
.simditor .simditor-body p,
.simditor .simditor-body div,
.editor-style p,
.editor-style div {
    color: var(--primaryFont-color) !important;
    white-space: normal !important;
}

/* 偏移问题解决 */
.gb-com-two-cols-title .el-input-group__prepend .el-select {
    margin: 0 -20px;
}

/* 报错 */
.g-error {
    position: absolute;
    right: -12px;
    bottom: 20px;
    outline: none !important;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    z-index: 3;
    cursor: pointer;
    color: var(--navOtherFont-color);
    border-radius: 50%;
    background-color: var(--navBackground-color);
}

.g-error .iconjinggao {
    font-size: 14px;
    color: var(--navOtherFont-color);
}

.g-error span {
    position: absolute;
    top: 0;
    right: -4px;
    font-size: 12px;
    color: #fff;
    width: 15px;
    height: 15px;
    text-align: center;
    line-height: 15px;
    background: rgba(236, 80, 72, 1);
    border-radius: 50%;
}

.el-drawer {
    outline: none !important;
    box-shadow: none !important;
}

.el-drawer__wrapper {
    right: calc(100% );
    right: 0;
}

.el-drawer__open .el-drawer.rtl {
    width: 603px !important;
}

.error-drawer-header {
    position: relative;
    height: 38px;
    line-height: 38px;
    font-size: 12px;
    color: var(--secondaryFont-color);
    padding-left: 20px;
    background: var(--lv4Background-color);
}

.error-drawer-header i.icontanhao {
    position: relative;
    top: 2px;
    font-size: 18px;
    color: #FFA200;
    margin-right: 8px;
}

.error-drawer-header i.iconguanbi {
    position: absolute;
    top: 0;
    right: 20px;
    font-size: 12px;
    color: var(--level3Font-color);
    cursor: pointer;
}

.error-drawer-body {
    position: absolute;
    top: 38px;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 0 20px;
    overflow: auto;
}

.error-drawer-item {
    position: relative;
    padding: 20px 0;
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.error-time {
    width: 102px;
    font-size: 12px;
    line-height: 16px;
    color: var(--secondaryFont-color);
}

.error-content {
    width: 220px;
    font-size: 12px;
    line-height: 16px;
    color: var(--primaryFont-color);
}

.error-btns {
    margin-top: 20px;
    text-align: right;
}

.error-btns a {
    display: inline-block;
    font-size: 12px;
    color: #fff;
    padding: 3px 10px;
    margin-left: 4px;
    line-height: 1;
    background: var(--main-color);
    border-radius: 4px;
}

.error-btns a.gray {
    cursor: default;
    background-color: var(--level3Font-color);
}

.error-drawer-item .iconxialajiantou_huaban {
    position: absolute;
    top: 22px;
    right: 0;
    color: var(--level3Font-color);
    font-size: 12px;
    cursor: pointer;
}

.error-drawer-item .iconxialajiantou_huaban.rotate {
    transform: rotate(180deg);
}

/* 报错 - info */
.el-notification {
    border: none;
    box-shadow: none;
    padding-left: 15px;
    padding-right: 10px;
    border-radius: 4px;
}

.el-notification .el-notification__closeBtn {
    right: 10px;
    top: 14px;
}

.el-notification .el-icon-info,
.el-notification .el-icon-warning,
.el-notification .el-icon-error {
    position: absolute;
    top: 14px;
    left: 15px;
    color: var(--secondaryFont-color);
    font-size: 16px;
}

.el-notification .el-icon-warning {
    top: 13px;
    color: #E6A23C;
}

.el-notification .el-icon-error {
    color: #EC5048;
}

.el-notification .el-icon-arrow {
    position: absolute;
    top: 14px;
    right: 27px;
    font-size: 16px;
    cursor: pointer;
}

.el-notification__group {
    width: 100%;
    margin: 0;
    margin-left: 24px;
}

.el-notification__content {
    margin: 0;
}

.notify-main {
    max-height: 84px;
    font-size: 12px;
    line-height: 16px;
    color: var(--secondaryFont-color);
    padding-right: 36px;
    overflow: auto;
}

.el-notification .error-btns {
    margin-top: 15px;
}

.goby-warning {
    background-color: #FFFAF3;
}

.goby-error {
    background-color: #FFF6F6;
}

/* 组件版本号样式 */
.gb-com-td>div span i {
    position: relative;
    font-style: normal;
    margin-left: 5px;
}

.gb-com-td>div span>i::before {
    position: absolute;
    top: -1px;
    bottom: -1px;
    content: "";
    width: 1px;
    background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div:nth-child(2) span>i::before {
    background-color: rgba(144, 213, 243, 0.4);
}

.gb-com-td>div:nth-child(3) span>i::before {
    background-color: rgba(116, 202, 239, 0.6);
}

.gb-com-td>div:nth-child(4) span>i::before {
    background-color: rgba(91, 191, 234, 0.8);
}

.gb-com-td>div:nth-child(5) span>i::before {
    background-color: rgba(63, 176, 224, 1);
}

.gb-com-td>div span>i i {
    margin-left: 6px;
}

.mar10 {
    margin: 0 10px;
}

.marTop15 {
    margin-top: 15px;
}

.bold {
    font-weight: 600;
}

.marLeft10 {
    margin-left: 10px;
}

.marLeft0 {
    margin-left: 0px;
}

.marRight0 {
    margin-right: 0px !important;
}

.padBot10 {
    padding-bottom: 10px;
}

.wid440 {
    max-width: 440px;
}

/* 扫描弹窗报错修改 */
.scan-dia-error {
    position: absolute;
    bottom: -20px;
    font-size: 12px;
    color: #EC5048;
    line-height: 20px;
    margin-left: 91px;
}

.scan_port_int .scan-dia-error {
    margin-left: 0;
}

.wid440 {
    max-width: 440px;
}

/* 报错读取全部日志的样式 */
.all-logs {
    position: relative;
    padding-bottom: 18px;
    padding-right: 30px;
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.all-logs span {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    color: var(--main-color);
}

.all-logs i.open {
    display: inline-block;
    transform: rotate(180deg);
}

.logs-reason p {
    position: relative;
    color: var(--primaryFont-color) !important;
    margin-top: 4px !important;
    padding-left: 10px;
}

.logs-reason p:first-child {
    padding-left: 0;
    color: var(--level3Font-color) !important;
    margin-top: 13px !important;
}

.logs-reason p:last-child {
    color: var(--level3Font-color) !important;
    margin-top: 13px !important;
}

.logs-reason p::before {
    content: "";
    position: absolute;
    top: 7px;
    left: 0;
    bottom: 0;
    width: 3px;
    height: 3px;
    background: var(--primaryFont-color);
    border-radius: 50%;
}

.logs-reason p:first-child::before,
.logs-reason p:last-child::before {
    display: none;
}

/* 蜜罐样式 */
.honeypot td div,
.honeypot .gb-ip-td .iconjinggao,
.honeypot .iconDetails-,
.honeypot .iconlink-,
.honeypot .all-ports-drop .el-dropdown-link,
.honeypot .all-ports-drop .el-dropdown-link:hover {
    color: var(--level3Font-color) !important;
}

.honeypot .all-ports-drop .el-dropdown-link:hover {
    color: #fff !important;
}

.honeypot-tips {
    display: none;
    position: absolute;
    top: 5px;
    left: 0;
    width: 400px;
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    z-index: 10;
}

.honeypot-tips i {
    font-size: 10px;
    margin-right: 6px;
}

.honeypot .gb-com-td>div:nth-child(1) {
    background-color: rgba(160, 176, 205, 0.32);
}

.honeypot .gb-com-td>div:nth-child(2) {
    background-color: rgba(160, 176, 205, 0.4);
}

.honeypot .gb-com-td>div:nth-child(3) {
    background-color: rgba(160, 176, 205, 0.6);
}

.honeypot .gb-com-td>div:nth-child(4) {
    background-color: rgba(160, 176, 205, 0.8);
}

.honeypot .gb-com-td>div:nth-child(5) {
    background-color: rgba(160, 176, 205, 1);
}

.honeypot .honeypot-tips {
    display: block;
}

.honeypot .gb-com-td>div span {
    display: none;
}

.honeypot .gb-com-td>div span:nth-child(1),
.honeypot .gb-com-td>div span:nth-child(2),
.honeypot .gb-com-td>div span:nth-child(3) {
    display: inline-block;
}

.honeypot .gb-com-td>div.show-all-com span {
    display: inline-block;
}

.more-com {
    font-size: 12px;
    color: #1098d2 !important;
    padding: 0 6px;
    height: 16px;
    margin: 3px 5px 3px 0;
    line-height: 16px;
    border-radius: 7px;
    background-color: rgba(255, 255, 255, 0.75);
}

.honeypot .gb-com-td>div.show-all-com .more-com {
    display: none;
}

.plugData {
    padding: 10px 0 10px 10px;
    line-height: 1.5;
    border-left: 1px solid var(--secondaryBorder-color);
    border-right: 1px solid var(--secondaryBorder-color);
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.padBot10 {
    padding-bottom: 10px;
}

.marTop90 {
    margin-top: 90px;
}

.marBot30 {
    margin-bottom: 30px;
}

.marBot5 {
    margin-bottom: 5px;
}

/* 历史任务批量删除 */
.dialog-scan-config-main.history .el-table tr td:nth-child(1) {
    position: relative;
    left: 22px;
}

.dialog-scan-config-main.history .el-table tr td:nth-child(2) {
    position: relative;
    left: -11px;
}

.dialog-scan-config-main.history .el-table tr th:nth-child(2) {
    /* position: relative;
    left: 4px; */
}

/* 固定定位，水平垂直居中 */
.pos-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}

.success-text {
    color: #5DC166 !important;
}

.fail-text {
    color: #EC5048 !important;
}
