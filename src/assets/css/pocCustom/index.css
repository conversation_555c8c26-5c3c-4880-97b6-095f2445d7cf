/* hdd-漏洞详情 */
.gb-com-full-page-wrap {
    height: calc(100% - 15px);
}

.vul-detail {
    line-height: 30px;
}

.vul-detail .vul-level-div {
    margin-right: 7px;
    margin-top: 6px;
}

.vul-detail-title {
    font-size: 18px;
    /* font-weight: bold; */
    color: var(--primaryFont-color);
}

.vul-sub-title {
    font-size: 14px;
    line-height: 26px;
    color: var(--level3Font-color);
}

.vul-sub-text,
.vul-sub-text textarea {
    width: 100%;
    font-size: 12px !important;
    color: var(--primaryFont-color) !important;
    line-height: 18px !important;
    margin-bottom: 7px;
    outline: none;
    border: none;
    resize: none;
    padding: 0;
}

.vul-info-item {
    position: relative;
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.vul-info-item span {
    position: absolute;
    top: 0;
    left: 0;
    width: 120px;
    font-size: 12px;
    padding-left: 10px;
    color: var(--primaryFont-color);
    background: rgba(162, 186, 227, 0.16);
}

.vul-info-item div {
    font-size: 12px;
    margin-left: 120px;
    text-indent: 15px;
    color: var(--primaryFont-color);
}

.vul-info-item:last-child {
    border: none;
}

.vul-info {
    margin-bottom: 17px;
    margin-top: 3px;
    border: 1px solid rgba(227, 235, 246, 1);
}

/* 无数据状态 */
.gb-vul-empty {
    width: 624px;
    height: 250px;
    top: 27%;
    padding-left: 237px;
    background-image: none;
}

.gb-vul-empty .gb-asset-empty-item {
    /*background-image: url(../img/weak-password.png);*/
}

.gb-vul-empty .gb-asset-empty-ip-item {
    /* background-image: url(../img/gen-poc.png);*/
}

.gb-asset-empty-item {
    color: var(--secondaryFont-color);
}

.gb-asset-empty-item div {
    color: var(--level3Font-color);
    font-size: 12px;
}

.gb-asset-empty>p {
    color: var(--primaryFont-color);
    margin-top: 72px;
    margin-bottom: 15px;
}

.vul-btn.gb-primary-btn1,
.vul-btny.gb-primary-btn1:focus,
.vul-btn.gb-primary-btn1:hover {
    background-color: rgba(246, 143, 102, 1);
    /* box-shadow: 0px 4px 24px 0px rgba(246, 143, 102, 0.66); */
}

.vul-btn.gb-primary-btn1 {
    max-width: 230px;
}

/* poc管理 */
.poc-operate-td img {
    width: 15px;
    margin-top: -3.5px;
}

.poc-operate-td .iconfont {
    font-size: 15px;
    color: #F68F67
}

.poc-man {
    width: 210px;
    /* width: 189px; */
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    color: var(--level3Font-color);
    margin-bottom: 8px;
    padding-left: 5px;
    padding-right: 10px;
    cursor: pointer;
}

.poc-man>.fl {
    color: var(--secondaryFont-color);
}

.poc-man .iconfont {
    margin-right: 4px;
    font-size: 12px;
    color: var(--primaryIcon-color);
}

.poc-man.active,
.poc-man:hover {
    color: #fff;
    background: rgba(246, 143, 102, 1);
    /* box-shadow: 0px 3px 12px 0px rgba(246, 143, 102, 0.54); */
    border-radius: 4px;
}

.poc-man.active .iconfont,
.poc-man:hover .iconfont {
    color: rgba(255, 255, 255, 0.5);
}

.poc-man:hover .fl,
.poc-man.active .fl {
    color: #fff;
}

.poc-scan-time {
    color: var(--level3Font-color);
}

.iconyuanxingxuanzhongfill.poc-man-sel {
    color: var(--level3Font-color);
    font-size: 12px;
}

.poc-gray-td {
    color: var(--level3Font-color) !important;
}

.poc-vul-name-td .iconhacker- {
    font-size: 12px;
    margin-left: 8px;
    color: var(--level3Font-color);
}

.add-poc-btn {
    position: absolute;
    right: 90px;
    top: 0;
    color: #fff;
    height: 26px;
    line-height: 26px;
    font-size: 12px;
    padding: 0 10px;
    cursor: pointer;
    background: rgba(246, 143, 102, 1);
    /* box-shadow: 0px 2px 16px 0px rgba(255, 192, 167, 0.66); */
    border-radius: 4px;
}

.poc-man-top {
    position: relative;
}

.add-poc-btn .iconfont {
    font-size: 12px;
    margin-right: 8px;
}

.poc-man-top .el-select {
    position: absolute;
    right: 175px;
    /* right: 0; */
    top: 0;
    width: 118px;
}

.poc-man-top .el-input__inner {
    height: 26px;
    line-height: 26px;
}

.poc-man-top .el-input__icon {
    line-height: 26px;
}

.poc-man-top .el-select .el-input__suffix {
    top: 1px;
}

.poc-index .el-table--enable-row-hover .el-table__body tr:hover>td {
    /* background-color: rgba(236, 241, 249, 1); */
}

.poc-test-tab-main .el-input__icon{
    line-height: unset;
}

.poc-gray-td .ser-lv,
.poc-gray-td .high-lv,
.poc-gray-td .mid-lv,
.poc-gray-td .low-lv {
    color: var(--level3Font-color);
    /*background-image: url(../img/gray.svg);*/
}

.has-vul {
    color: #F68F67;
}

/* 自定义poc */
.add-poc-tab {
    height: 56px;
    font-size: 12px;
    line-height: 56px;
    border-bottom: 1px solid var(--primaryBorder-color);
    background: #f0f2f5;
    width: calc(100%);
    margin: 0;
}

.add-poc-tab a::before,.add-poc-tab a::after,.add-poc-tab a{
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.add-poc-tab a {
    position: relative;
    /* font-weight: 600; */
    margin: 0 20px;
    border-bottom: 1px solid transparent;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: #404040;
    display: inline-block;
    height: 100%;
}

.add-poc-tab a.active {
    color: #4285F4;
    border-bottom: 4px solid #4285F4;
    font-weight: 600;
}

.add-poc-tab a.active .el-icon-caret-top {
    display: block;
}

.add-poc-main {
    position: absolute;
    top: 180px !important;
    left: 0;
    bottom: 0;
    right: 0;
    background: var(--secondaryBackground-color);
    margin: 0 20px;
}


.add-poc-step {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 68px;
    right: 0;
    max-height: 100%;
    overflow: auto;
}

.add-poc-step.test {
    right: 0;
    max-height: unset;
}

.add-poc-step-info {
    margin-left: 20px;
    margin-right: 15px;
    margin-top: 20px;
}

.add-poc-step .el-form-item__label {
    font-size: 12px;
    line-height: 30px;
    padding: 0;
    color: var(--primaryFont-color);
}

.el-collapse-item__content .el-form-item__label {
    text-indent: 26px;
}

.add-poc-step .el-form-item__content {
    line-height: 30px;
    white-space: nowrap;
}

.add-poc-step .el-form-item {
    margin-bottom: 20px;
}

.add-poc-step .el-form-item .el-form-item__label{
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: #333333;
}

.add-poc-step .el-form-item .el-textarea .el-textarea__inner{
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: #333333;
}

.add-poc-step .el-form-item .el-input{
    height: 32px;
}

.add-poc-step .el-form-item .el-input .el-input__inner{
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: #333333;
}

.add-poc-step .el-form-item .el-input .el-input__icon{
    line-height: 32px;
}

.add-poc-step .el-form-item.query-rules {
    position: relative;
    margin-right: 32px;
}

.add-poc-step .el-form-item.query-rules .el-input{
    background: #F2F4F7;
}

.add-poc-step .el-form-item.query-rules .el-input__icon{
    color: #333;
}

.add-poc-step .el-form-item.query-rules .el-input__inner {
    padding-right: 35px;
    background: #F2F4F7;
    border-color: #F2F4F7;
}

.add-poc-step .el-form-item.query-rules .el-input__inner::placeholder{
    color: #999999;
}

.add-poc-step .el-form-item.query-rules .iconfont {
    color: #cccccc;
    cursor: pointer;
    width: 16px;
    height: 16px;
    font-size: 16px;
    margin-left: 9px;
}

.add-poc-step .el-form-item.query-rules .iconsousuo_huaban {
    position: absolute;
    top: 0;
    right: 11px;
}

.add-poc-step .el-form-item.query-rules .iconchangjianwentixin {
    position: absolute;
    top: 0;
    right: -23px;
}

.el-tag {
    height: 28px;
    line-height: 26px;
    color: var(--main-color);
    padding-left: 14px;
    border: 1px solid rgba(203, 223, 255, 1);
    background-color: rgba(241, 246, 255, 1);
}

.el-tag .el-icon-close {
    color: var(--main-color);
}

.el-tag .el-icon-close:hover {
    color: var(--main-color);
    background-color: rgba(241, 246, 255, 1);
}

.el-tag+.el-tag {
    margin-left: 10px;
}

.button-new-tag {
    padding: 6px 10px;
    font-size: 12px;
    color: var(--level3Font-color);
    background: rgba(241, 246, 255, 0);
    border: 1px solid rgba(207, 217, 229, 1);
    border-radius: 4px;
    margin-left: 5px;
}

.button-new-tag span {
    color: #8796B2;
    margin-right: 6px;
}

.input-new-tag {
    width: 90px;
    margin-left: 9px;
    vertical-align: bottom;
}

.input-new-tag.el-input--small .el-input__inner {
    font-size: 12px;
    height: 28px;
    line-height: 26px;
}

.add-poc-step .el-textarea__inner {
    /* resize: auto; */
}

/* 语法查询弹窗 */
.syn-refer {
    font-size: 12px;
    color: var(--level3Font-color);
    /* height: 335px; */
    height: auto;
    padding-right: 0;
}

.syn-refer>div {
    margin-right: 20px;
    /* height: 275px; */
    /* max-height: 375px; */
    overflow: auto;
}

.syn-refer-title {
    padding: 0 10px 10px 0;
    margin-right: 25px;
    background: rgba(236, 241, 249, 1);
}

.syn-refer-body{
    border: 1px solid #D9534F;
}

.syn-refer-empty{
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes round {
    from{
        transform: rotate(0);
    }
    to{
        transform: rotate(360deg);
    }
}

.syn-refer-empty .syn-refer-empty-loading{
    width: 216px;
    height: 108px;
    background: rgba(0,0,0,0.60);
    margin-top: -70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.syn-refer-empty .syn-refer-empty-loading img{
    width: 34px;
    height: 34px;
    animation: linear round infinite 2s;
}
.syn-refer-empty .syn-refer-empty-loading span{
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: rgba(255, 255, 255, 0.6);
    padding-top: 10px;
}

.syn-refer-item {
    position: relative;
    line-height: 18px;
    padding: 5px 0 5px 0;
    margin-right: 25px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: #333333;
}

.syn-refer-item > span{

}

.syn-refer-item .iconfont {
    position: absolute;
    top: 10px;
    left: 0;
    font-size: 14px;
    color: var(--level3Font-color);
}

.syn-refer-item a {
    color: #487BCC;
}

.syn-refer .syn-refer-item:last-child {
    border: none;
}

/* inquire 弹窗 */
.inquire-list {
    margin-right: 25px;
}

.inquire-list-title {
    position: relative;
    margin-bottom: 12px;
}

.inquire-list-title .el-input {
    width: 210px;
    margin-left: 10px;
}

.inquire-list-title .el-input__inner {
    height: 26px;
    line-height: 26px;
    padding-right: 30px;
}

.inquire-list-title .iconfont {
    position: absolute;
    top: 6px;
    left: 270px;
    font-size: 14px;
    cursor: pointer;
    color: var(--level3Font-color);
}

.inquire-test {
    font-size: 12px;
    color: #fff;
    padding: 3px 13px;
    background: rgba(246, 143, 102, 1);
    border-radius: 4px;
}

.inquire-list .el-table--enable-row-hover .el-table__body tr:hover>td {
    /* background-color: #ECF1F9; */
}

.inquire-res {
    margin-right: 25px;
}

.inquire-res-title {
    margin-bottom: 18px;
}

.inquire-res-title a {
    color: var(--level3Font-color);
    font-size: 14px;
}

.inquire-res-title a .iconfont {
    font-size: 14px;
}

.inquire-res-main {
    border: 1px solid var(--secondaryBorder-color);
    border-radius: 4px;
}

.inquire-res-main .el-textarea__inner {
    border: none;
    resize: none;
}

.inquire-res-main>div:nth-child(1) {
    /* background: rgba(236, 241, 249, 1); */
    border-bottom: 1px solid var(--secondaryBorder-color);
    border-radius: 4px 4px 0 0;
    padding: 0 10px;
    height: 21px;
    line-height: 20px;
}

.inquire-res-main>div:nth-child(1) .iconfont {
    font-size: 12px;
    color: var(--level3Font-color);
    margin-right: 8px;
}

.inquire-res-main .el-textarea.is-disabled .el-textarea__inner {
    color: var(--level3Font-color);
    line-height: 18px;
    padding: 16px 10px;
    font-size: 12px;
    border-radius: 4px;
    background-color: #fff;
}

.inquire-res-main.active>div:nth-child(1) .iconfont {
    color: #EC5048;
}

.inquire-res-main.active>div:nth-child(1) {
    color: #EC5048;
    border-color: #EC5048;
    background-color: rgba(255, 246, 246, 1);
}

.inquire-res-main.active {
    border-color: #EC5048;
}

/* poc口令字典弹窗 */
.pass-dict-main-wrap {
    /* margin-right: 25px; */
}

.pass-dict-main .el-textarea__inner {
    font-size: 12px;
    min-height: 50px !important;
}

.pass-dict-main .el-upload-list {
    display: none !important;
}

.pass-dict-main .dict-top-wrap {
    position: relative;
}

.pass-dict-main .dict-top-wrap .el-textarea {
    width: 415px;
}

.pass-dict-main .dict-top-wrap .el-upload {
    line-height: 50px;
}

.pass-dict-main .tool {
    margin-top: 12px;
    margin-bottom: 12px;
}

.pass-dict-main .el-form-item {
    position: relative;
    margin-bottom: 12px;
}

.pass-dict-main .el-form-item__label {
    font-size: 12px;
    line-height: 30px;
    color: var(--primaryFont-color);
}

.pass-dict-main .el-form-item .el-textarea {
    width: 336px;
}

.pass-dict-main .el-upload {
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 50px;
    background: var(--lv4Background-color);
    border: 1px solid var(--primaryBorder-color);
    border-left: none;
    border-radius: 0 2px 2px 0;
}

.pass-dict-main .el-form-item .el-upload>div {
    position: relative;
    width: 60px;
    height: 50px;
    text-align: center;
}

.pass-dict-main .el-form-item .el-upload img {
    margin-top: 8px;
}

.pass-dict-main .el-form-item .el-upload p {
    position: absolute;
    top: 27px;
    left: 0;
    width: 60px;
    font-size: 12px;
    color: var(--level3Font-color);
    line-height: 16px;
}

.pass-dict-main .tool a {
    color: var(--primaryFont-color);
    font-size: 14px;
    margin-right: 17px;
    padding: 12px 0;
}

.pass-dict-main .tool a .iconfont {
    color: var(--level3Font-color);
    margin-right: 8px;
    font-size: 14px;
}

.pass-dict-main .el-button--primary.start,
.pass-dict-main .el-button--primary.start:hover {
    padding: 12px 34px;
}

.pass-dict-main .tool a .iconfont.iconxialajiantou_huaban {
    display: inline-block;
    font-size: 10px;
    margin-right: 0;
    margin-left: 8px;
    color: #C7D3E7;
}

/* poc测试 */
.add-poc-step-test>div {
    background-color: var(--primaryBackground-color);
}

.add-poc-step-test .el-tabs__nav-wrap {
    margin-right: 150px;
}

.add-poc-step-test .el-tabs__item {
    height: 36px;
    line-height: 36px;
    padding: 0 10px;
    border-radius: 4px 4px 0 0;
    margin-right: 10px;
    border-top: 2px solid transparent;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: #404040 !important;
    width: 112px;
}

.add-poc-step-test .el-tabs__item.is-active {
    border-top-color: #2B83FC;
    background: var(--secondaryBackground-color) !important;
}

.add-poc-step-test .el-tab-pane {
    background-color: var(--secondaryBackground-color);
}

.add-poc-step-test .el-tabs--card>.el-tabs__header .el-tabs__item .el-icon-close {
    width: 22px !important;
    font-size: 22px !important;
}

.add-poc-step-test .el-tabs--card>.el-tabs__header {
    margin: 0;
    height: 36px;
    background: #f0f2f5 !important;
}

.add-poc-step-test .el-tabs--card>.el-tabs__header .el-tabs__nav {
    border: none;
}

.add-poc-step-test .el-tabs--card>.el-tabs__header .el-tabs__item.is-active.is-closable,
.add-poc-step-test .el-tabs--card>.el-tabs__header .el-tabs__item.is-closable,
.add-poc-step-test .el-tabs--card>.el-tabs__header .el-tabs__item.is-closable:hover {
    padding-left: 30px !important;
    padding-right: 30px !important;
}

.add-poc-step-test .el-tabs--top.el-tabs--card .el-tabs__item:nth-child(2) {
    padding-left: 30px !important;
    padding-right: 30px !important;
}

.add-poc-step-test .el-tabs__item .el-icon-close:hover {
    color: var(--level3Font-color);
    background-color: transparent;
}

.add-new-tab {
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    width: 26px;
    height: 36px;
    line-height: 36px;
    color: var(--primaryFont-color);
    background: #f0f2f5;
    border-radius: 4px;
    font-size: 22px;
}

.test-method-sel {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #fff;
    width: 200px;
}

.test-method-sel .el-input__inner {
    height: 28px;
    line-height: 28px;
}

.test-method-sel .el-input__icon {
    line-height: 28px;
}

.poc-test-tab-main {
    padding: 0 10px;
    padding-top: 30px;
}

.add-poc-step-test .el-collapse-item__header {
    height: 36px;
    line-height: 36px;
    padding: 0 10px;
    background-color: #F5F6F7;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    font-weight: 400;
    color: #333333;
}

.poc-test-tab-main .el-collapse-item__content {
    padding-top: 20px;
    padding-bottom: 30px;
}

.request-item .el-input {
    width: 190px;
    margin-right: 20px;
}

.variables-item {
    padding-left: 25px;
    padding-right: 30px;
}

.variables-item>div {
    display: flex;
    position: relative;
    height: 30px;
    margin-bottom: 15px;
}

.variables-item .iconfont {
    position: absolute;
    top: 5px;
    right: -18px;
    font-size: 12px;
    color: var(--secondaryFont-color);
    cursor: pointer;
}

.variables-item .iconfont:hover {
    color: var(--level3Font-color);
}

.variables-item>div>.el-input {
    width: 25%;
    margin-right: 10px;
}

.variables-item .el-select {
    width: 25%;
    margin-right: 10px;
}

/* 树形结构优化 */
.tree-tab {
    margin: 0 20px;
    height: 22px;
    line-height: 22px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    font-weight: bold !important;
    color: #333333;
    white-space: nowrap;

}

.tree-tab > span {
    padding-left: 10px;
    display: inline-block;
}
.tree-tab > span:nth-child(3){
    padding-left: 0;
}
.tree-tab > span:nth-child(4){
    padding-left: 0;
}
.tree-tab > span:nth-child(5){
    padding-left: 0;
}

.tree-tab .test-item {
    width: calc(18%);
}

.tree-tab .variable,
.tree-tab .operation {
    width: 20%;
}

.tree-tab .value,
.tree-tab .remarks {
    width: 17%;
}

.tree {
    font-size: 12px !important;
    color: var(--primaryFont-color) !important;
    margin: 0 20px;
}

.tree .iconzhedie {
    color: #7E8EAC;
    cursor: pointer;
    font-size: 14px;
    padding: 23px 0;
    padding-right: 5px;
    margin-left: 8px;
}

.tree-default>.tree-no-dots .tree-closed>.tree-ocl {
    /*background-image: url(../img/close-f.png);*/
    background-position: center !important;
}

.tree-default>.tree-no-dots .tree-open>.tree-ocl {
    /*background-image: url(../img/open-f.png);*/
    background-position: center !important;
}

.tree-default>.tree-no-dots .tree-leaf>.tree-ocl {
    background: none;
    /*background-image: url(../img/file.png) !important;*/
    background-position: center !important;
    background-repeat: no-repeat !important;
}

body .tree li {
    position: relative;
}

.tree-var-sel {
    position: absolute;
    top: 20px;
    right: 54%;
    width: 19%;
}

.tree-item-var-sel {
    position: absolute;
    top: 20px;
    width: 19%;
    right: 56%;
}

.tree-item-oper-sel {
    position: absolute;
    top: 20px;
    width: 19%;
    right: 36%;
}

.tree-item-val-inp {
    position: absolute;
    top: 20px;
    width: 18%;
    right: 17%;
}

.tree-item-bz-inp {
    position: absolute;
    top: 20px;
    width: 16%;
    right: 0;
}

.tree input,
.tree select,
.tree option {
    color: var(--level3Font-color);
    font-size: 12px;
    outline: none;
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    border: 1px solid rgba(207, 217, 229, 1);
    border-radius: 2px;
    background: rgba(33, 43, 66, 0);
}

.tree input:focus,
.tree select:focus,
.tree option:focus {
    color: var(--primaryFont-color);
    border-color: var(--main-color) !important;
}

body .tree-default .tree-icon:empty,
body .tree-default .tree-anchor,
.tree-default .tree-icon {
    height: 60px;
    line-height: 60px;
}

.tree-default .tree-selected,
.tree-default .tree-context,
.tree-default .tree-hovered {
    /* background-color: #ECF1F9!important; */
    background-color: transparent !important;
}

/* 新版标签样式 */
.goby-tags-wrap {
    border: 1px solid var(--primaryBorder-color);
    border-radius: 2px;
    min-height: 32px;
    clear: unset;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 5px 0 0 5px;
    box-sizing: border-box;
}
.goby-tags-wrap::before,.goby-tags-wrap::after{
    clear: unset;
}
.goby-tags-wrap .fl{
    height: 20px !important;
    line-height: 20px !important;
    margin: 0 5px 5px 0 !important;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.goby-tags-wrap .fl .el-input .el-input__inner:focus{
    border: none !important;
}

.goby-tags-item {
    padding: 6px 10px;
    margin: 0 5px 5px 0;
    background: #F5F5F5;
    border-radius: 2px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    color: #404040;
    line-height: 24px;
}

.goby-tags-item .iconfont.iconguanbi {
    font-size: 8px;
    margin-left: 8px;
    color: var(--secondaryFont-color);
}

.goby-tags-wrap .el-input__inner {
    width: 130px;
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    padding: 0 5px;
    border: none;
}

.goby-tags-wrap .el-input,
.goby-tags-wrap .fl {
    height: 20px;
    line-height: 20px;
}

.pre-tags-wrap {
    margin-top: 5px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.pre-tags-wrap span {
    cursor: pointer;
    padding: 3px 6px;
}

/* 右键菜单样式优化 */
.context-menu-list {
    background: rgba(255, 255, 255, 1) !important;
    border: 1px solid rgba(207, 217, 229, 1) !important;
    border-radius: 2px !important;
    box-shadow: none !important;
}

.context-menu-item {
    font-size: 12px !important;
    color: var(--primaryFont-color) !important;
    height: 20px !important;
    padding: 0 10px !important;
    line-height: 20px !important;
}

.context-menu-item:hover {
    background: rgba(236, 241, 249, 1) !important;
}

/* poc测试遮挡溢出滚动条 */
.cover-scroll {
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 36px;
    z-index: 5;
    background: #eee;
}

/* poc el-ui 树状结构 */
.goby-tree .el-tree-node__content {
    position: relative;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    margin: 15px 14px;
}

.goby-tree .el-tree-node__content:hover,
.goby-tree .el-tree-node:focus>.el-tree-node__content {
    background-color: transparent;
}

/* .goby-tree .iconzhedie {
  position: absolute;
  top: 0;
  left: 0;
} */

.tree-one-sel {
    position: absolute;
    top: 0;
    width: 18.5%;
    right: 63%;
}

.tree-two-sel {
    position: absolute;
    top: 0;
    width: 18.5%;
    right: 63%;
}

.tree-three-sel {
    position: absolute;
    top: 0;
    width: 19%;
    right: 43%;
}

.tree-one-inp {
    position: absolute;
    top: 0;
    width: 16%;
    right: 26.1%;
}

.tree-two-inp {
    position: absolute;
    top: 0;
    width: 16.4%;
    right: 8.8%;
}

.goby-tree-pop {
    padding: 10px 0;
    color: var(--primaryFont-color);
    font-size: 12px;
}

.goby-tree-pop>div {
    cursor: pointer;
    height: 20px;
    line-height: 20px;
    padding: 0 10px;
    margin-bottom: 5px;
    border-radius: 2px;
}

.goby-tree-pop>div:hover {
    background: var(--lv4Background-color);
}

.goby-tree-pop>div:last-child {
    margin-bottom: 0;
}



/* 单ip扫描 */
.single-ip-scan {
    float: right;
    padding-right: 20px;
}

.single-ip-scan .el-input {
    width: 170px;
}

.single-ip-scan .gb-dia-btn,
.single-ip-scan .gb-dia-btn:hover,
.single-ip-scan .gb-dia-btn:focus {
    margin-left: 10px;
    padding: 0 10px;
    width: auto;
    margin-top: 3px;
    height: 32px;
    box-shadow: none;
    /* padding: 0; */
}

/* exp */
#exp-dia .el-dialog__body {
    padding-top: 10px;
}

#exp-dia .el-dialog__header {
    display: block !important;
}

.exp-dia-main>div {
    max-height: 422px;
    overflow: auto;
}

.exp-title,
.exp-result {
    font-size: 18px;
    height: 22px;
    line-height: 22px;
    color: var(--primaryFont-color);
    margin-bottom: 10px;
}

.exp-dia-main .el-form-item__label {
    font-size: 12px;
    color: var(--primaryFont-color);
    line-height: 30px;
    text-align: left;
    padding-right: 7px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.exp-left,
.exp-right {
    width: 48%;
}

.exp-dia-main .el-form-item__content {
    line-height: 30px;
}

.exp-right textarea {
    height: 100% !important;
    margin-right: 10px;
}

.exp-dia-main .el-input.is-disabled .el-input__inner,
.exp-dia-main .el-textarea.is-disabled .el-textarea__inner {
    color: var(--level3Font-color);
    cursor: default !important;
}

.verify-icon {
    position: relative;
    top: -1px;
    margin-left: 8px;
}

.vu-info.exp {
    background: rgba(246, 143, 102, 1);
    color: rgba(255, 255, 255, 1);
    /* box-shadow: 0px 3px 13px 0px rgba(229, 235, 247, 0.88); */
}

.vu-info.exp:hover {
    color: #F68F66;
    background: #FFF1EB;
}

.exp-dia-main .el-progress {
    display: inline-block;
    width: 130px;
}

.exp-dia-main .el-form-item__content>.el-input,
.exp-dia-main .el-form-item__content>.el-select,
.exp-dia-main .el-form-item__content>.el-textarea {
    width: 100%;
}

.exp-dia-main .tips-item .el-form-item__content>.el-input,
.exp-dia-main .tips-item .el-form-item__content>.el-select,
.exp-dia-main .tips-item .el-form-item__content>.el-textarea {
    width: calc(100% - 25px);
}

.exp-dia-main .iconchangjianwentixin {
    float: right;
}

/* 漏洞单扫弹窗 */
#vul-batch-scan.min {
    height: 1px;
}

#vul-batch-scan .el-input__icon {
    line-height: 30px;
    height: 30px;
}

#vul-batch-scan .el-select .el-input__suffix {
    top: 0;
}

#vul-batch-scan .amount-total {
    font-size: 12px;
    color: var(--level3Font-color);
    /* text-indent: 112px; */
    line-height: 22px;
    margin-bottom: 15px;
}

#vul-batch-scan .amount-total i {
    color: #F68F66;
    font-style: normal;
    margin-right: 5px;
}

#vul-batch-scan .amount-total span {
    color: var(--level3Font-color);
    margin-right: 5px;
}

#vul-batch-scan .amount-total em {
    font-style: normal;
}

#vul-batch-scan .vul-batch-table-wrap {
    height: 320px;
    overflow: auto;
    padding-right: 20px;
}

#vul-batch-scan .el-progress {
    position: absolute;
    top: 86px;
    left: 0;
    right: 0;
}

#vul-batch-scan .el-progress-bar__inner,
#vul-batch-scan .el-progress-bar__outer {
    border-radius: 0;
}

#vul-batch-scan .el-progress-bar__outer {
    /* background-color: var(--lv4Background-color); */
}

#vul-batch-scan .dialog-scan-config-bot {
    height: 430px;
    background-color: var(--lv3Background-color);
}

#vul-batch-scan .option-sec {
    position: relative;
    height: 30px;
    margin-bottom: 30px;
}

#vul-batch-scan .option-sec>div {
    margin-right: 40px;
}

#vul-batch-scan .option-sec>div>span {
    margin-right: 15px;
}

#vul-batch-scan .option-sec .el-select.wid229 {
    width: 229px;
}

#vul-batch-scan .option-sec .el-select.wid537 {
    width: 537px;
}

#vul-batch-scan .dia-import {
    height: 30px;
    line-height: 28px;
    padding: 0 30px;
}

#vul-batch-scan .dia-import.stop {
    background: rgba(241, 89, 82, 1);
    border: 1px solid rgba(241, 89, 82, 1);
}

#vul-batch-scan .goby-empty {
    bottom: 210px;
}

#vul-batch-scan .hasVul {
    color: #EC5048;
}

#vul-batch-scan .iconfont.iconjinggao {
    margin-left: 6px;
    font-size: 12px;
}

/* poc列宽调整 */
#poc-list-table tr th:nth-child(2)>.cell,
#poc-list-table tr td:nth-child(2)>.cell,
#poc-list-table tr th:nth-child(3)>.cell,
#poc-list-table tr td:nth-child(3)>.cell {
    padding: 0 !important;
}

#poc-list-table tr th:nth-child(4)>.cell,
#poc-list-table tr td:nth-child(4)>.cell {
    padding-left: 0 !important;
}

/* 表格 */
.el-table__expand-icon {
    color: var(--secondaryFont-color);
}

/* 导出导入自定义poc */
.poc-ex-im {
    position: absolute;
    top: 0;
    right: 17px;
    width: 31px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
    background: rgba(246, 143, 102, 1);
    border-radius: 4px;
}

.poc-ex-im i {
    font-size: 12px;
    color: #fff;
}

.poc-ex-im.ex {
    right: 53px;
}

/* 自定义poc规则查询 */
.add-poc-main .el-autocomplete {
    width: 100%;
}
