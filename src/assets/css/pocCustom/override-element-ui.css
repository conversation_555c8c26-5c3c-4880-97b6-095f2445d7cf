/* 中英文切换下拉 */
.gb-icons .el-dropdown-link {
    font-size: 12px;
    letter-spacing: 1px;
    color: var(--navOtherFont-color);
}

.gb-lang-dropdown {
    border: none;
    box-shadow: none;
    padding: 0;
    margin: 0;
    background-color: #2d589e;
}

.gb-lang-dropdown .el-dropdown-menu__item {
    font-size: 12px;
    padding: 0 15px;
    line-height: 25px;
    text-align: center;
    color: var(--primaryFont-color);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.gb-lang-dropdown .el-dropdown-menu__item:last-child {
    border-bottom: none;
}

.gb-lang-dropdown .popper__arrow {
    display: none;
}

/* 开始扫描弹窗 */
.dialog-scan-config {
    /*position: absolute !important;*/
}

.dialog-scan-config .el-dialog__header {
    display: none !important;
}

.dialog-scan-config .el-dialog__body {
    padding: 0;
}

.gb-task-name {
    width: 375px;
}

.el-input__inner {
    height: 34px;
    line-height: 30px;
    font-size: 14px;
    color: #333;
    padding: 0 10px;
    border: solid 1px #D5D8DE;
}

.el-input__inner::placeholder{
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    font-weight: 400;
    color: #999999;
}

.el-checkbox__inner {
    /* border-radius: 50%; */
    background-color: transparent;
}

.el-checkbox__inner:hover,
.el-checkbox__input.is-focus .el-checkbox__inner {
    border-color: var(--main-color);
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: var(--main-color);
    border-color: var(--main-color);
}

.el-checkbox__input.is-checked+.el-checkbox__label {
    color: var(--secondaryFont-color);
}

.el-checkbox {
    color: var(--secondaryFont-color);
}

.el-checkbox__label {
    padding-left: 5px;
}

.el-button--primary,
.el-button--primary:focus,
.el-button--primary:hover {
    /* border: none; */
    /* box-shadow: 0px 4px 24px 0px rgba(246, 143, 102, 0.47); */
    box-shadow: none;
    border-radius: 4px;
    font-size: 14px;
    /* padding: 12px 42px; */
    color: #ffffff;
}

.el-button--primary.start,
.el-button--primary.start:focus,
.el-button--primary.start:hover {
    border: none;
    background: var(--main-color);
    /* box-shadow: 0px 4px 24px 0px rgba(67, 121, 198, 0.66); */
    border-radius: 4px;
    font-size: 14px;
    padding: 12px 42px;
    color: #ffffff;
}

.el-button--primary.cancel,
.el-button--primary.cancel:focus,
.el-button--primary.cancel:hover,
.el-message-box__btns .el-button--default:nth-child(1),
.el-message-box__btns .el-button--default:nth-child(1):focus,
.el-message-box__btns .el-button--default:nth-child(1):hover {
    border: none;
    background: var(--lv4Background-color) !important;
    /* box-shadow: 0px 4px 13px 0px rgba(67, 121, 198, 0.28); */
    border-radius: 4px;
    font-size: 14px;
    /* padding: 12px 42px; */
    color: var(--secondaryFont-color);
}

.el-button--default,
.el-button--default:focus,
.el-button--default:hover {
    /* background-color: var(--main-color); */
    /* border: none; */
    text-align: center;
    /* box-shadow: 0px 4px 24px 0px rgba(67, 121, 198, 0.66); */
    border-radius: 4px;
    font-size: 14px;
    /* padding: 12px 42px; */
    /* color: #ffffff; */
}



.el-button--primary.gb-primary-btn1,
.el-button--primary.gb-primary-btn1:focus,
.el-button--primary.gb-primary-btn1:hover {
    background-color: #26afea;
    /* box-shadow: 0px 4px 24px 0px rgba(54, 173, 225, 0.66); */
}

.el-button--primary.gb-primary-btn2,
.el-button--primary.gb-primary-btn2:focus,
.el-button--primary.gb-primary-btn2:hover {
    background-color: var(--main-color);
    /* box-shadow: 0px 4px 24px 0px rgba(67, 121, 198, 0.66); */
}

.el-button--primary.gb-primary-btn3,
.el-button--primary.gb-primary-btn3:focus,
.el-button--primary.gb-primary-btn3:hover {
    background-color: #5DC166;
    /* box-shadow: 0px 4px 24px 0px rgba(93, 193, 102, 0.66); */
}

.el-table th,
.el-table tr {
    background-color: var(--secondaryBackground-color);
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: var(--secondaryBackground-color);
}

.el-table--enable-row-hover .el-table__body tr:hover>td {
    background-color: var(--secondaryBackground-color);
}

.el-table,
.el-table__expanded-cell {
    background-color: var(--secondaryBackground-color);
}

.dialog-scan-config-bot .el-table th,
.dialog-scan-config-bot .el-table tr {
    background-color: var(--lv3Background-color);
}

.dialog-scan-config-bot .el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: var(--lv3Background-color);
}

.dialog-scan-config-bot .el-table--enable-row-hover .el-table__body tr:hover>td {
    background-color: var(--lv3Background-color);
}

.dialog-scan-config-bot .el-table,
.dialog-scan-config-bot .el-table__expanded-cell {
    background-color: var(--lv3Background-color);
}

/* 资产管理完成 */
.gb-scan-end-text .el-button--primary {
    font-size: 12px;
    color: #ffffff;
    /*width: 118px;*/
    padding: 0 15px;
    height: 26px;
    line-height: 26px;
    margin-top: 8px;
}

.gb-scan-over-dropdown {
    width: 118px;
}

.gb-scan-over-dropdown .el-dropdown-menu__item .iconfont {
    margin-right: 8px;
}

/* 三列布局搜索输入框 */
.gb-com-two-cols-title .el-input {
    width: 288px;
    transition: all 0.4s;
}

.gb-com-two-cols-title .fold .el-input,
.gb-sea-wrap.fold .el-select .el-input,
.gb-sea-wrap.fold .el-input-group__prepend+input,
.gb-sea-wrap.fold .el-input-group__append,
.gb-sea-wrap.fold .el-input-group__prepend {
    display: none;
    width: 0;
    padding: 0;
    transition: all 0.4s;
}

.gb-sea-wrap.fold .el-select .el-input__suffix {
    display: none;
}

.gb-com-two-cols-title .el-input input {
    border-radius: 12px;
    border: none;
    padding-left: 18px;
    padding-right: 42px;
}

.gb-com-two-cols-title .highlight .el-input input {
    border-radius: 14px;
}

/* table */
.el-table td {
    font-size: 12px;
    /* color: #5476ac; */
    color: var(--primaryFont-color);
    border-bottom: 1px solid var(--secondaryBorder-color);
}

.el-table td,
.el-table th {
    padding: 10px 0;
}

.el-table thead {
    font-size: 12px;
    color: var(--primaryFont-color);
}

.el-table th {
    padding: 0;
    font-weight: normal !important;
    border: none !important;
    background-color: var(--lv4Background-color);
}

/* tooltip */
.el-tooltip__popper.is-light {
    border: none;
    font-size: 12px;
    line-height: 18px;
    padding: 7px 10px;
    max-width: 259px;
    color: #333;
    /* box-shadow: 0px 1px 18px 0px rgba(140, 166, 208, 0.44); */
}

.el-tooltip__popper.is-dark {
    max-width: 380px;
    font-size: 12px;
    padding: 5px 10px;
    line-height: 16px;
    background-color: rgba(0, 0, 0, 0.7);
}

.el-tooltip__popper .popper__arrow {
    display: none;
}

/* 选择下拉 */
.gb-dia-form-item .el-select {
    width: 375px;
}



.el-select .el-input__suffix {
    top: 0;
}

.el-select .is-focus .el-input__suffix {
    top: -3px;
}

/* 文本域 */
.el-textarea__inner {
    resize: none;
    border: 1px solid var(--primaryBorder-color);
    border-radius: 2px;
}

/* 资产搜索框前添加下拉 */
.gb-sea-wrap .el-input__inner {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
}

.gb-sea-wrap .el-select .el-input {
    width: 120px;
    border-radius: 14px 0 0 14px;
    background-color: var(--lv4Background-color) !important;
}

.gb-sea-wrap .el-input-group__prepend+input {
    padding-left: 10px;
    padding-right: 28px;
}

.gb-sea-wrap .el-select .el-input__suffix {
    top: 7px;
}

.gb-sea-wrap .el-select .is-focus .el-input__suffix {
    top: -7px!important;
}

.gb-sea-wrap .el-input-group__prepend {
    border-radius: 14px 0 0 14px;
    border: none;
}

.gb-sea-wrap .el-select .el-input input {
    font-size: 12px;
    color: var(--secondaryFont-color);
    border-radius: 14px 0 0 14px;
    padding: 0 10px !important;
}

.gb-sea-wrap .el-select .el-input input:hover {
    color: var(--primaryFont-color);
}

/* 表格自动省略取消 */
.el-table .cell,
.el-table th div {
    text-overflow: unset;
}

.el-checkbox__label {
    padding-left: 8px;
}

.el-textarea__inner:focus,
.el-select .el-input__inner:focus {
    border-color: var(--main-color) !important;
    color: var(--primaryFont-color) !important;
}

/* table下拉折叠样式 */
.el-table__expand-column+td .cell {
    padding-left: 0;
}

.el-table__expand-column+th {
    position: relative;
    overflow: visible;
}

.el-table__expand-column+th .cell {
    padding-left: 0;
    position: absolute;
    top: 0;
    left: -11px;
}

.el-table__row.expanded td {
    border-bottom: none;
}

.el-table td.el-table__expanded-cell {
    padding-top: 5px;
}

/* banner-list 文本域 */
.ban-info .el-textarea__inner {
    padding: 0;
    color: var(--level3Font-color);
    font-size: 12px;
    line-height: 22px;
    border: none;
    resize: none;
    overflow: visible;
}

.ban-info .el-textarea__inner::-webkit-scrollbar {
    width: 0;
    /*竖向滚动条的宽度*/
}

/* weblist-server 维度表格 */
.weblist-server-table.el-table tbody,
.weblist-server-table.el-table td {
    border: none;
}

/* 禁止选中的表单手势取消 */
.el-textarea.is-disabled .el-textarea__inner,
.el-input.is-disabled .el-input__inner {
    /* cursor: default; */
    background-color: rgba(140, 158, 194, 0.08)!important;
}

/* 表格排序样式 */
.el-table .sort-caret.descending {
    border-top-color: var(--level3Font-color);
}

.el-table .sort-caret.ascending {
    border-bottom-color: var(--level3Font-color);
}

.el-table .ascending .sort-caret.ascending {
    border-bottom-color: var(--main-color);
}

.el-table .descending .sort-caret.descending {
    border-top-color: var(--main-color);
}

/* 不确定选择状态 */
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: transparent;
    border-color: var(--main-color);
}

.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
    background-color: var(--main-color);
    height: 8px;
    width: 8px;
    transform: scale(1);
    top: 50%;
    left: 50%;
    margin-top: -4px;
    margin-left: -4px;
    border-radius: 2px;
}

/* 表单 */
.el-select:hover .el-input__inner,
.el-select-dropdown,
.el-dropdown-menu,
.el-message-box,
.el-checkbox__inner,
.el-textarea.is-disabled .el-textarea__inner,
.el-input.is-disabled .el-input__inner,
.el-autocomplete-suggestion {
    border-color: var(--primaryBorder-color);
}

.el-input__suffix .el-input__clear,
.el-input__suffix .el-input__clear:hover {
    color: var(--lv4Background-color) !important;
}

.el-popover {
    background-color: var(--lv3Background-color);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2)
}

.el-popper[x-placement^=right] .popper__arrow,
.el-popper[x-placement^=right] .popper__arrow::after {
    border-right-color: var(--lv3Background-color);
}

.el-select .el-input .el-select__caret {
    color: var(--secondaryFont-color);
}

.gb-sea-wrap .el-select .el-input:hover .el-select__caret,
.gb-sea-wrap .el-select .el-input.is-focus .el-select__caret {
    color: var(--primaryFont-color);
}

/* el-tree */
.el-tree-node__expand-icon,
.custom-tree-node .iconzhedie {
    color: var(--secondaryFont-color);
}

.custom-tree-node {
    color: var(--primaryFont-color);
}
