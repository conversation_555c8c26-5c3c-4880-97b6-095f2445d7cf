<template>
  <el-dialog
    class="elDialogAdd"
    :close-on-click-modal="false"
    :visible="dialogVisible"
    @close="closeDialog"
    width="400px"
  >
    <template slot="title">
      <span>{{ title }}</span>
    </template>
    <div class="dialog-body">
      <p class="downloadClass" @click="downloadForbid()">
        <i class="el-icon-warning"></i>请点击下载
        <span>{{ downloadForbidUrlName }}</span>
      </p>
      <el-upload
        class="upload-demo"
        drag
        :action="uploadActionHd"
        :headers="uploadHeaders"
        accept=".xlsx,.csv"
        :before-upload="beforeIpUpload"
        :on-success="uploadSuccess"
        :on-remove="uploadRemove"
        :on-error="uploadError"
        :limit="uploadMaxCount"
        :on-exceed="handleExceed"
        :file-list="fileList"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip"> 支持上传xlsx 文件，且大小不超过20M </div>
      </el-upload>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="$emit('closeDialog')" id="account_check_cancel"
        >关闭</el-button
      >
      <el-button :loading="btnLoading" class="highBtn" @click="save" id="account_check_sure"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    btnLoading: {
      type: Boolean,
      default: false
    },
    downloadForbidUrl: {
      type: String,
      default: ''
    },
    downloadForbidUrlName: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    uploadAction: {
      type: String,
      default: `/domain_assets/upload`
    }
  },
  computed: {
    uploadActionHd() {
      return `${this.uploadSrcIp}${this.uploadAction}`
    }
  },
  data() {
    return {
      fileList: [],
      uploadMaxCount: 1,
      fileDataArray: [],
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      }
    }
  },
  methods: {
    closeDialog() {
      this.fileList = []
      this.fileDataArray = []
      this.$emit('closeDialog')
    },
    downloadForbid() {
      window.location.href = this.downloadForbidUrl
    },
    async save() {
      this.$emit('save', this.fileDataArray)
    },
    uploadError(err, file, fileList) {
      this.$message.error(JSON.parse(err.message).message)
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.fileDataArray = []
      } else {
        this.fileDataArray = res[0]
      }
    },
    beforeIpUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过 20MB!')
      }
      return isLt1M
    },
    uploadSuccess(response, file, fileList) {
      if (file.response.code == 0) {
        this.fileDataArray = []
        this.$message.success('上传成功')
        this.fileDataArray = response.data
      } else {
        this.$message.error(file.response.message)
      }
    }
  }
}
</script>

<style></style>
