<template>
  <div class="columnBox">
    <el-dropdown :hide-on-click="false" trigger="click" @visible-change="dropdownChange">
      <span class="el-dropdown-link">
        <i class="el-icon-setting" style="font-size: 20px"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="(item, i) in dataListChecked" :key="i">
          <el-checkbox
            class="checkboxAll"
            :checked="item.checked"
            @change="selectChange(item.label)"
            :disabled="i <= 1 && item.checked == true ? true : false"
            >{{ item.label }}</el-checkbox
          >
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { getColumn, setColumn } from '@/api/apiConfig/person.js'

export default {
  props: ['dataList', 'coumnId'],
  data() {
    return {
      dataListChecked: [],
      completeList: [],
      user: {
        role: ''
      }
    }
  },
  watch: {
    dataList: {
      handler: function (newVal, oldVal) {
        newVal.map((v) => {
          v.checked = false
        })
        getColumn({ operate_company_id: this.currentCompany }).then((res) => {
          if (res.code == 0) {
            sessionStorage.setItem('columnList', JSON.stringify(res.data))
            this.completeList = JSON.parse(sessionStorage.getItem('columnList'))
            let dataTmp = []
            if (this.completeList.length != 0) {
              this.completeList.map((item) => {
                if (item.type == Number(this.coumnId)) {
                  dataTmp = item.settings
                  if (this.coumnId == 1) {
                    dataTmp.push('最新解析域名')
                  } else if (item.type == 11) {
                    dataTmp.push('信任度')
                    dataTmp.push('资产分类')
                  }
                }
              })
            }
            //  代表匹配到了 有默认显示的列
            if (dataTmp.length !== 0) {
              newVal.map((v) => {
                dataTmp.map((item) => {
                  if (item == v.label) {
                    v.checked = true
                  }
                })
              })
            }
            //  代表没有匹配到 没有默认显示的列 则全部显示
            else {
              newVal.map((v) => {
                v.checked = true
              })
            }
            newVal.map((v) => {
              if (!v.checked) {
                v.checked = false
              }
            })
            newVal[0].checked = true
            this.dataListChecked = newVal
            this.setColumnFunc()
            //  this.$emit('columnChecked',this.setCheckedValue(2))
          }
        })
      },
      // deep:true,
      immediate: true
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    async dropdownChange(value) {
      //  value为false进行设置
      if (!value) {
        this.setColumnFunc()
      }
    },
    async setColumnFunc() {
      let obj = {}
      if (this.completeList.length != 0) {
        this.completeList.map((v) => {
          // 如果设置了需要id 没设置不需要
          if (v.type == Number(this.coumnId)) {
            obj.id = v.id
          }
        })
      }
      obj.type = this.coumnId
      obj.operate_company_id = this.currentCompany
      obj.settings = this.setCheckedValue(1)
      let res = await setColumn(obj)
      // 设置完之后更新存储数据
      if (res.code == 0) {
        let data = await getColumn({ operate_company_id: this.currentCompany })
        sessionStorage.setItem('columnList', JSON.stringify(data.data))
        this.completeList = JSON.parse(sessionStorage.getItem('columnList'))
        this.$emit('columnChecked', this.setCheckedValue(2))
      }
    },
    // 改变选择
    selectChange(val) {
      this.dataListChecked.map((v, key) => {
        if (val == v.label) {
          v.checked = !v.checked
        }
      })
    },
    // 处理选中数据
    setCheckedValue(val) {
      let dataTmp = []
      this.dataListChecked.map((v) => {
        if (v.checked) {
          if (val == 1) {
            dataTmp.push(v.label)
          } else {
            dataTmp.push(v)
          }
        }
      })
      return dataTmp
    }
  },
  created() {
    let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    this.user = userInfo.user
  }
}
</script>
<style lang="less" scoped>
.tableWrap {
  position: relative;
}
.columnBox {
  width: 49px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1px;
  height: 48px;
  box-shadow: -2px 0px 4px 0px rgba(0, 0, 0, 0.12);
  z-index: 33;
  position: absolute;
  background-color: #f2f3f5 !important;
  right: 21px;
}
</style>
