<template>
  <div class="conwrap">
    <span v-if="isShowMoreBtns" @click="changeLeft" style="cursor: pointer"
      ><i class="el-icon-arrow-left"></i
    ></span>
    <ul v-if="highlist" id="boxItem" ref="boxItem">
      <!-- <li v-if="flag"><span>结果总计：<i class="total">{{total}}</i></span></li>
      <li v-if="flag" class="shu"></li> -->
      <li v-for="(item, todoIndex) in highTabShow" :key="todoIndex">
        <div v-if="highlist[item.name] || highlist[item.name] == 0">
          <div v-if="item.type == 'input' && highlist[item.name]">
            <span>
              <i>{{ item.label }}</i
              >：{{ highlist[item.name] }}
              <i class="el-icon-close" @click="removeItem('input', item.name)"></i>
            </span>
          </div>
          <div
            v-if="
              item.type == 'select' &&
              highlist[`ch_${item.name}`] &&
              Array.isArray(highlist[`ch_${item.name}`]) &&
              highlist[`ch_${item.name}`].length > 0
            "
          >
            <span v-for="(ch, index) in highlist[`ch_${item.name}`]" :key="index">
              <i>{{ item.label }}</i
              >：{{ ch }}
              <i class="el-icon-close" @click="removeItem('select', item.name, ch)"></i>
            </span>
          </div>
          <div
            v-if="
              item.type == 'select' &&
              highlist[`ch_${item.name}`] &&
              !Array.isArray(highlist[`ch_${item.name}`])
            "
          >
            <span>
              <i>{{ item.label }}</i
              >：{{ highlist[`ch_${item.name}`] }}
              <i class="el-icon-close" @click="removeItem('select', item.name)"></i>
            </span>
          </div>
          <div v-if="item.type == 'date' && highlist[item.name] && highlist[item.name].length > 0">
            <span>
              <i>{{ item.label }}</i
              >：{{ highlist[item.name][0] + '至' + highlist[item.name][1] }}
              <i class="el-icon-close" @click="removeItem('select', item.name)"></i>
            </span>
          </div>
        </div>
      </li>
    </ul>
    <span v-if="isShowMoreBtn" @click="changeRight" style="cursor: pointer"
      ><i class="el-icon-arrow-right"></i
    ></span>
  </div>
</template>

<script>
export default {
  props: ['highTabShow', 'highlist', 'total'],
  data() {
    return {
      tabActive: '',
      isShowMoreBtn: false,
      isShowMoreBtns: false,
      highTabIsShow: true,
      conditionList: null,
      flag: false
    }
  },
  watch: {
    highlist: {
      //监听高级筛选条件
      handler: function (value) {
        setTimeout(() => {
          if (
            this.$refs.boxItem &&
            this.$refs.boxItem.clientWidth >= this.$refs.boxItem.parentElement.clientWidth * 0.9
          ) {
            this.isShowMoreBtn = true
          } else {
            this.isShowMoreBtn = false
          }
        }, 50)
      },
      deep: true
    }
  },
  methods: {
    changeRight() {
      //右移动
      let boxItem = document.getElementById('boxItem')
      boxItem.scrollLeft = boxItem.scrollLeft + 50
      if (boxItem.scrollLeft > 0) {
        this.isShowMoreBtns = true
      }
    },
    changeLeft() {
      //左移动
      let boxItem = document.getElementById('boxItem')
      if (boxItem.scrollLeft > 0) {
        boxItem.scrollLeft = boxItem.scrollLeft - 50
      }
      if (boxItem.scrollLeft <= 0) {
        this.isShowMoreBtns = false
      }
    },
    removeItem(type, name, ch) {
      if (type == 'input') {
        this.highlist[name] = ''
      } else {
        if (ch) {
          // 存在是下拉框多选标签删除
          this.highlist['ch_' + name].forEach((item, index) => {
            if (item == ch) {
              this.highlist['ch_' + name].splice(index, 1)
              this.highlist[name].splice(index, 1)
            }
          })
        } else {
          // ch不存  或者输入框标签删除
          this.highlist[name] = ''
          this.highlist['ch_' + name] = ''
        }
      }
      this.$emit('highcheck', this.highlist)
    }
  }
}
</script>

<style lang="less" scoped>
.conwrap /deep/ {
  padding: 0 20px;
  display: flex;
  background: #fff;
  & > span {
    display: inline-block;
    width: 28px;
    text-align: center;
    height: 28px;
    line-height: 28px;
    color: #62666c;
    background: #f1f3f8;
  }
  & > .el-icon-arrow-left {
    box-shadow: 2px 0px 4px 0px rgba(0, 0, 0, 0.08);
  }
  & > .el-icon-arrow-right {
    box-shadow: -2px 0px 4px 0px rgba(0, 0, 0, 0.08);
  }
  ul {
    // width: 94%;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: hidden;
    font-size: 12px;
    li {
      & > div > div {
        display: flex;
        flex-wrap: nowrap;
      }
      span {
        display: inline-block;
        padding: 5px;
        margin-right: 8px;
        margin-bottom: 16px;
        font-weight: 400;
        white-space: nowrap;
        color: #37393c;
        background: #f8f9fc;
        border-radius: 4px;
        border: 1px solid #dfe4ed;
      }
      i {
        display: inline-block;
        white-space: nowrap;
        font-weight: 400;
        color: #62666c;
      }
      .el-icon-close {
        cursor: pointer;
      }
    }
  }
}
.numBox {
  padding: 5px;
  border: 1px solid #dfe4ed;
  background: #f8f9fc;
  border-radius: 4px;
  color: #62666c;
  font-size: 12px;
}
.total {
  color: #2677ff !important;
}
.shu {
  background: #e9ebef;
  width: 1px;
  height: 24px;
  margin-right: 8px;
  margin-top: -15px;
}
</style>
