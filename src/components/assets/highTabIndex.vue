<template>
  <div class="conwrap">
    <span v-if="isShowMoreBtns" @click="changeLeft" style="cursor: pointer"
      ><i class="el-icon-arrow-left"></i
    ></span>
    <ul v-if="highlist" id="boxItem" ref="boxItem">
      <!-- <li v-if="flag"><span>结果总计：<i class="total">{{total}}</i></span></li>
      <li v-if="flag" class="shu"></li> -->
      <!-- !pageIcon || (pageIcon && item.pageIcon && item.pageIcon == pageIcon) || (pageIcon && !item.pageIcon) -->
      <!-- pageIcon:调用此组件的页面标识，因为一些字段name值一样，label不一样，所以用此字段区分，例如:状态status,有些label是任务状态 -->
      <li v-for="(item, todoIndex) in list" :key="todoIndex">
        <div v-if="highlist[item.name] || highlist[item.name] == 0">
          <div
            v-if="
              item.type == 'input' &&
              (!pageIcon ||
                (pageIcon && item.pageIcon && item.pageIcon == pageIcon) ||
                (pageIcon && !item.pageIcon)) &&
              highlist[item.name]
            "
          >
            <span>
              <i>{{ item.label }}</i
              >：{{ highlist[item.name] }}
              <i class="el-icon-close" @click="removeItem('input', item.name)"></i>
            </span>
          </div>
          <div
            v-if="
              item.type == 'select' &&
              (!pageIcon ||
                (pageIcon && item.pageIcon && item.pageIcon == pageIcon) ||
                (pageIcon && !item.pageIcon)) &&
              highlist[`ch_${item.name}`] &&
              Array.isArray(highlist[`ch_${item.name}`]) &&
              highlist[`ch_${item.name}`].length > 0
            "
          >
            <span v-for="(ch, index) in highlist[`ch_${item.name}`]" :key="index">
              <i>{{ item.label }}</i
              >：{{ ch }}
              <i class="el-icon-close" @click="removeItem('select', item.name, ch)"></i>
            </span>
          </div>
          <div
            v-if="
              item.type == 'select' &&
              (!pageIcon ||
                (pageIcon && item.pageIcon && item.pageIcon == pageIcon) ||
                (pageIcon && !item.pageIcon)) &&
              highlist[`ch_${item.name}`] &&
              !Array.isArray(highlist[`ch_${item.name}`])
            "
          >
            <span>
              <i>{{ item.label }}</i
              >：{{ highlist[`ch_${item.name}`] }}
              <i class="el-icon-close" @click="removeItem('select', item.name)"></i>
            </span>
          </div>
          <div
            v-if="
              item.type == 'date' &&
              (!pageIcon ||
                (pageIcon && item.pageIcon && item.pageIcon == pageIcon) ||
                (pageIcon && !item.pageIcon)) &&
              highlist[item.name] &&
              highlist[item.name].length > 0
            "
          >
            <span>
              <i>{{ item.label }}</i
              >：{{ highlist[item.name][0] + '至' + highlist[item.name][1] }}
              <i class="el-icon-close" @click="removeItem('select', item.name)"></i>
            </span>
          </div>
        </div>
      </li>
    </ul>
    <span v-if="isShowMoreBtn" @click="changeRight" style="cursor: pointer"
      ><i class="el-icon-arrow-right"></i
    ></span>
  </div>
</template>

<script>
export default {
  props: ['highlist', 'pageIcon', 'total'],
  data() {
    return {
      tabActive: '',
      isShowMoreBtn: false,
      isShowMoreBtns: false,
      highTabIsShow: true,
      conditionList: null,
      list: [
        // {
        //     label: '关键字',
        //     name: 'name',
        //     pageIcon: 'task',
        //     type: 'input'
        // },
        {
          label: '任务计划',
          name: 'type',
          pageIcon: 'task',
          type: 'select'
        },
        {
          label: '任务状态',
          name: 'status',
          pageIcon: 'task',
          type: 'select'
        },
        {
          label: '任务状态',
          name: 'status',
          pageIcon: 'recommend',
          type: 'select'
        },
        {
          label: '扩展状态',
          name: 'status',
          pageIcon: 'exportlog',
          type: 'select'
        },
        {
          label: '开始时间',
          name: 'created_at',
          pageIcon: 'exportlog',
          type: 'date'
        },
        {
          label: '结束时间',
          name: 'updated_at',
          pageIcon: 'exportlog',
          type: 'date'
        },
        {
          label: '开始时间',
          name: 'created_at',
          pageIcon: 'recommend',
          type: 'date'
        },
        {
          label: '状态',
          name: 'status',
          pageIcon: 'loginEntry',
          type: 'select'
        },
        {
          label: '更新时间',
          name: 'updated_at_range',
          pageIcon: 'loginEntry',
          type: 'date'
        },
        {
          label: '创建时间',
          name: 'created_at_range',
          pageIcon: 'user', // 相同name字段不同页面区分label
          type: 'date'
        },
        {
          label: '模板名称',
          name: 'name',
          pageIcon: 'report_template',
          type: 'input'
        },
        {
          label: '模板类型',
          name: 'type',
          pageIcon: 'report_template',
          type: 'select'
        },
        {
          label: '创建时间',
          name: 'created_at_range',
          pageIcon: 'report_template', // 相同name字段不同页面区分label
          type: 'date'
        },
        {
          label: '报告名称',
          name: 'name',
          pageIcon: 'report',
          type: 'input'
        },
        {
          label: '生成状态',
          name: 'create_status',
          pageIcon: 'report',
          type: 'select'
        },
        {
          label: '生成时间',
          name: 'created_at_range',
          pageIcon: 'report',
          type: 'date'
        },
        {
          label: '任务模式',
          name: 'modify',
          pageIcon: 'domainTask',
          type: 'select'
        },
        {
          label: '任务状态',
          name: 'status',
          pageIcon: 'domainTask',
          type: 'select'
        },
        {
          label: '创建时间',
          name: 'created_at_range',
          pageIcon: 'domainTask',
          type: 'date'
        },
        {
          label: '报告模板',
          name: 'template_name',
          pageIcon: 'report',
          type: 'input'
        },
        {
          label: '状态',
          name: 'status',
          pageIcon: 'keyword1',
          type: 'select'
        },
        {
          label: '域名类型',
          name: 'type',
          pageIcon: 'keyword1',
          type: 'select'
        },
        {
          label: '域名来源',
          name: 'source',
          pageIcon: 'keyword1',
          type: 'select'
        },
        {
          label: '域名名称',
          name: 'domain',
          pageIcon: 'keyword1',
          type: 'input'
        },
        {
          label: '企业名称',
          name: 'company_name',
          pageIcon: 'keyword1',
          type: 'input'
        },
        {
          label: '状态',
          name: 'status',
          pageIcon: 'poc',
          type: 'select'
        },
        {
          label: '更新时间',
          name: 'updated_at_range',
          pageIcon: 'keyword1',
          type: 'date'
        },
        {
          label: '添加时间',
          name: 'created_at_range',
          pageIcon: 'keyword',
          type: 'date'
        },
        {
          label: '开始时间',
          name: 'created_at_range',
          pageIcon: 'task',
          type: 'date'
        },
        {
          label: '发起人',
          name: 'op_id',
          type: 'select'
        },
        {
          label: '漏洞等级',
          name: 'level',
          pageIcon: 'repair',
          type: 'select'
        },
        {
          label: '漏洞验证',
          name: 'has_exp',
          pageIcon: 'repair',
          type: 'select'
        },
        {
          label: 'POC分组',
          name: 'poc_group_id',
          type: 'select'
        },
        {
          label: '漏洞类型',
          name: 'vul_type',
          pageIcon: 'repair',
          type: 'select'
        },
        {
          label: 'IP地址段',
          name: 'ip',
          pageIcon: 'repair',
          type: 'input'
        },
        {
          label: '发现时间',
          name: 'created_at_range',
          pageIcon: 'repair',
          type: 'date'
        },
        {
          label: '协议',
          name: 'protocol_id',
          pageIcon: 'portMange',
          type: 'select'
        },
        {
          label: '分组',
          name: 'port_group_id',
          pageIcon: 'portMange',
          type: 'select'
        },
        {
          label: '账号主体',
          name: 'owner',
          pageIcon: 'newAssets_notApp',
          type: 'input'
        },
        {
          label: '企业名称',
          name: 'owner',
          pageIcon: 'newAssets_isApp',
          type: 'input'
        },
        {
          label: '状态',
          name: 'status',
          pageIcon: 'newAssets_notApp',
          type: 'select'
        },
        {
          label: '首次发现时间',
          name: 'created_at_range',
          pageIcon: 'newAssets_notApp',
          type: 'date'
        },
        {
          label: '最新发现时间',
          name: 'updated_at_range',
          pageIcon: 'newAssets_notApp',
          type: 'date'
        },
        {
          label: '状态',
          name: 'status',
          pageIcon: 'newAssets_isApp',
          type: 'select'
        },
        {
          label: '首次发现时间',
          name: 'created_at_range',
          pageIcon: 'newAssets_isApp',
          type: 'date'
        },
        {
          label: '最新发现时间',
          name: 'updated_at_range',
          pageIcon: 'newAssets_isApp',
          type: 'date'
        },
        {
          label: '关键词',
          name: 'name',
          pageIcon: 'dataLeak',
          type: 'input'
        },
        {
          label: '平台',
          name: 'plat_name',
          pageIcon: 'dataLeak',
          type: 'input'
        },
        {
          label: '文件后缀',
          name: 'ext',
          pageIcon: 'dataLeak',
          type: 'input'
        },
        {
          label: '语言',
          name: 'language',
          pageIcon: 'dataLeak',
          type: 'select'
        },
        {
          label: '状态',
          name: 'status',
          pageIcon: 'dataLeak',
          type: 'select'
        },
        {
          label: '首次发现时间',
          name: 'created_at_range',
          pageIcon: 'dataLeak',
          type: 'date'
        },
        {
          label: '最新发现时间',
          name: 'updated_at_range',
          pageIcon: 'dataLeak',
          type: 'date'
        },
        {
          label: '线索类型',
          name: 'type',
          pageIcon: 'blackClue',
          type: 'select'
        },
        {
          label: '线索内容',
          name: 'name',
          pageIcon: 'blackClue',
          type: 'input'
        },
        {
          label: '线索内容',
          name: 'content',
          pageIcon: 'blackClue',
          type: 'input'
        },
        {
          label: '平台',
          name: 'platform',
          pageIcon: 'blackClue',
          type: 'input'
        },
        {
          label: '来源',
          name: 'source',
          pageIcon: 'blackClue',
          type: 'input'
        },
        {
          label: '企业名称',
          name: 'company_name',
          pageIcon: 'blackClue',
          type: 'input'
        },
        {
          label: '是否确认',
          name: 'confirmed',
          pageIcon: 'blackClue',
          type: 'select'
        },
        {
          label: '来源证书是否有效',
          name: 'cert_valid',
          pageIcon: 'blackClue',
          type: 'select'
        },
        {
          label: '添加时间',
          name: 'created_at_range',
          pageIcon: 'blackClue',
          type: 'date'
        },
        {
          label: '创建时间',
          name: 'created_at',
          pageIcon: 'blackClue',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          pageIcon: 'blackClue',
          type: 'date'
        },
        {
          label: '用户姓名',
          name: 'username',
          pageIcon: 'log',
          type: 'input'
        },
        {
          label: 'IP',
          name: 'ip',
          pageIcon: 'log',
          type: 'input'
        },
        {
          label: '用户类型',
          name: 'role',
          pageIcon: 'log',
          type: 'select'
        },
        {
          label: '操作模块',
          name: 'model',
          pageIcon: 'log',
          type: 'select'
        },
        {
          label: '操作时间',
          name: 'created_at_range',
          pageIcon: 'log',
          type: 'date'
        },
        {
          label: '用户名',
          name: 'name',
          pageIcon: 'user',
          type: 'input'
        },
        {
          label: '手机',
          name: 'mobile',
          type: 'input'
        },
        {
          label: '邮箱地址',
          name: 'email',
          type: 'input'
        },
        {
          label: '用户类型',
          name: 'role',
          type: 'select'
        },
        {
          label: '企业名称',
          name: 'company',
          type: 'input'
        },
        {
          label: '事件名称',
          name: 'keyword',
          pageIcon: 'eventwarning',
          type: 'input'
        },
        {
          label: '端口号',
          name: 'keyword',
          pageIcon: 'portMange',
          type: 'input'
        },
        {
          label: '状态',
          name: 'status',
          pageIcon: 'eventwarning',
          type: 'select'
        },
        {
          label: '首次发现时间',
          name: 'created_at_range',
          pageIcon: 'eventwarning',
          type: 'date'
        },
        {
          label: '最新发现时间',
          name: 'updated_at_range',
          pageIcon: 'eventwarning',
          type: 'date'
        },
        // 证书资产
        {
          label: '颁发者',
          name: 'issuer_cn',
          pageIcon: 'certAssets',
          type: 'input'
        },
        {
          label: '使用者',
          name: 'subject_cn',
          pageIcon: 'certAssets',
          type: 'input'
        },
        {
          label: '使用组织',
          name: 'subject_org',
          pageIcon: 'certAssets',
          type: 'input'
        },
        {
          label: '关联IP',
          name: 'ip',
          pageIcon: 'certAssets',
          type: 'input'
        },
        {
          label: '关联域名',
          name: 'domain',
          pageIcon: 'certAssets',
          type: 'input'
        },
        {
          label: '版本',
          name: 'version',
          pageIcon: 'certAssets',
          type: 'input'
        },
        {
          label: '证书状态',
          name: 'subject_cn',
          pageIcon: 'certAssets',
          type: 'select'
        },
        {
          label: '有效期',
          name: 'subject_cn',
          pageIcon: 'certAssets',
          type: 'select'
        },
        {
          label: '发现时间',
          name: 'created_at',
          pageIcon: 'certAssets',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          pageIcon: 'certAssets',
          type: 'date'
        },
        {
          label: '发起时间',
          name: 'created_at_range',
          pageIcon: 'checkDetect',
          type: 'date'
        },
        {
          label: '发起人',
          name: 'user_name',
          pageIcon: 'checkDetect',
          type: 'input'
        }
      ],
      flag: false
    }
  },
  watch: {
    highlist: {
      //监听高级筛选条件
      handler: function (value) {
        setTimeout(() => {
          if (
            this.$refs.boxItem &&
            this.$refs.boxItem.clientWidth >= this.$refs.boxItem.parentElement.clientWidth * 0.9
          ) {
            this.isShowMoreBtn = true
          } else {
            this.isShowMoreBtn = false
          }
        }, 50)
        // for(let key in value){
        //   if(value[key]){
        //     if(Array.isArray(value[key])){
        //       if(value[key].length != 0){
        //         return this.flag = true
        //       }else {
        //         this.flag = false
        //       }
        //     }else {
        //       return this.flag = true
        //     }
        //   }else {
        //     this.flag = false
        //   }
        // }
      },
      deep: true
    }
  },
  methods: {
    changeRight() {
      //右移动
      let boxItem = document.getElementById('boxItem')
      boxItem.scrollLeft = boxItem.scrollLeft + 50
      if (boxItem.scrollLeft > 0) {
        this.isShowMoreBtns = true
      }
    },
    changeLeft() {
      //左移动
      let boxItem = document.getElementById('boxItem')
      if (boxItem.scrollLeft > 0) {
        boxItem.scrollLeft = boxItem.scrollLeft - 50
      }
      if (boxItem.scrollLeft <= 0) {
        this.isShowMoreBtns = false
      }
    },
    removeItem(type, name, ch) {
      if (type == 'input') {
        this.highlist[name] = ''
      } else {
        if (ch) {
          // 存在是下拉框多选标签删除
          this.highlist['ch_' + name].forEach((item, index) => {
            if (item == ch) {
              this.highlist['ch_' + name].splice(index, 1)
              this.highlist[name].splice(index, 1)
            }
          })
        } else {
          // ch不存是下拉框单选或者输入框标签删除
          this.highlist[name] = ''
          this.highlist['ch_' + name] = ''
        }
      }
      this.$emit('highcheck', this.highlist)
    }
  }
}
</script>

<style lang="less" scoped>
.conwrap /deep/ {
  padding: 0 20px;
  display: flex;
  background: #fff;
  & > span {
    display: inline-block;
    width: 28px;
    height: 36px;
    line-height: 36px;
    color: #62666c;
    background: #ffffff;
  }
  & > .el-icon-arrow-left {
    box-shadow: 2px 0px 4px 0px rgba(0, 0, 0, 0.08);
  }
  & > .el-icon-arrow-right {
    box-shadow: -2px 0px 4px 0px rgba(0, 0, 0, 0.08);
  }
  ul {
    // width: 94%;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: hidden;
    font-size: 12px;
    li {
      & > div > div {
        display: flex;
        flex-wrap: nowrap;
      }
      span {
        display: inline-block;
        padding: 5px;
        margin-right: 8px;
        margin-bottom: 16px;
        font-weight: 400;
        white-space: nowrap;
        color: #37393c;
        background: #f8f9fc;
        border-radius: 4px;
        border: 1px solid #dfe4ed;
      }
      i {
        display: inline-block;
        white-space: nowrap;
        font-weight: 400;
        color: #62666c;
      }
      .el-icon-close {
        cursor: pointer;
      }
    }
  }
}
.numBox {
  padding: 5px;
  border: 1px solid #dfe4ed;
  background: #f8f9fc;
  border-radius: 4px;
  color: #62666c;
  font-size: 12px;
}
.total {
  color: #2677ff !important;
}
.shu {
  background: #e9ebef;
  width: 1px;
  height: 24px;
  margin-right: 8px;
  margin-top: -15px;
}
</style>
