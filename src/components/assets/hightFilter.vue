<template>
  <div class="hightFilterBox">
    <span v-if="isShowMoreBtns" @click="changeLeft" style="cursor: pointer"
      ><i class="el-icon-arrow-left"></i
    ></span>
    <div id="boxItem" ref="boxItem">
      <span class="numBox"
        ><span>结果总计：</span><span class="total">{{ total }}</span></span
      >
      <div class="shu"></div>
      <div
        v-for="(item, index) in list"
        :key="item.name + index"
        class="boxItemBox"
        ref="boxItemBox"
        :style="{
          display:
            (Array.isArray(conditionList[item.name]) && conditionList[item.name].length != 0) ||
            (!Array.isArray(conditionList[item.name]) &&
              (conditionList[item.name] || conditionList[item.name] === 0))
              ? ''
              : 'none'
        }"
      >
        <div v-if="item.way == 'input'" class="filterBox">
          <div>
            <span class="label">{{ item.label }}：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="conditionList[item.name]"
              placement="top"
            >
              <span class="right">{{ conditionList[item.name] }}</span>
            </el-tooltip>
            <span @click="cancel(item.name)" style="cursor: pointer"
              ><i class="el-icon-close"></i
            ></span>
          </div>
        </div>
        <div v-if="item.way == 'select'">
          <div v-for="(v, i) in conditionList[item.name]" :key="index + '' + i" class="filterBox">
            <div>
              <span class="label">{{ item.label }}：</span>
              <el-tooltip
                class="item"
                effect="dark"
                :content="changeStatus(v)"
                placement="top"
                v-if="item.name == 'online_state'"
              >
                <span class="right">
                  {{ changeStatus(v) }}
                </span>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                :content="getTagsName(v)"
                placement="top"
                v-else-if="item.name == 'tags'"
              >
                <span class="right">
                  {{ getTagsName(v) }}
                </span>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                :content="getThreatTypeName(v)"
                placement="top"
                v-else-if="item.name == 'threaten_type_arr'"
              >
                <span class="right">
                  {{ getThreatTypeName(v) }}
                </span>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                :content="getAssetsSourceName(v)"
                placement="top"
                v-else-if="item.name == 'assets_source'"
              >
                <span class="right">
                  {{ getAssetsSourceName(v) }}
                </span>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" :content="getString(v)" placement="top" v-else>
                <span class="right">{{ v }}</span>
              </el-tooltip>
              <span @click="deleteItem(item.name, v)" style="cursor: pointer"
                ><i class="el-icon-close"></i
              ></span>
            </div>
          </div>
        </div>
        <!-- <div v-if="item.way == 'selectOne'&& conditionList.ip_match && (item.name == 'ip_match')" class="filterBox" :key="index">
                  <div>
                      <span class="label">{{item.label}}：</span>
                      <el-tooltip class="item" effect="dark" :content="ipMatchMap[conditionList[item.name]]"  placement="top">
                          <span class="right">
                            {{ipMatchMap[conditionList[item.name]]}}
                          </span>
                      </el-tooltip>
                      <span @click="cancel(item.name)" style="cursor:pointer"><i class="el-icon-close"></i></span>
                  </div>
              </div> -->
        <!-- <div v-if="item.way == 'selectOne'&& conditionList.company_match && (item.name == 'company_match')" class="filterBox" :key="index">
                  <div>
                      <span class="label">{{item.label}}：</span>
                      <el-tooltip class="item" effect="dark" :content="ipMatchMap[conditionList[item.name]]"  placement="top">
                          <span class="right">
                            {{ipMatchMap[conditionList[item.name]]}}
                          </span>
                      </el-tooltip>
                      <span @click="cancel(item.name)" style="cursor:pointer"><i class="el-icon-close"></i></span>
                  </div>
              </div> -->
        <div v-if="item.way == 'selectOne-yesOrNot'" class="filterBox" :key="index">
          <div>
            <span class="label">{{ item.label }}：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="ipMatchMap[conditionList[item.name]]"
              placement="top"
            >
              <span class="right">
                {{ ipMatchMap[conditionList[item.name] + 1] }}
              </span>
            </el-tooltip>
            <span @click="cancel(item.name)" style="cursor: pointer"
              ><i class="el-icon-close"></i
            ></span>
          </div>
        </div>
        <div
          v-if="item.way == 'selectOne' && item.name == 'reason_type'"
          class="filterBox"
          :key="index"
        >
          <div>
            <span class="label">{{ item.label }}：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="reasonDataMap[conditionList[item.name]]"
              placement="top"
            >
              <span class="right">
                {{ reasonDataMap[conditionList[item.name]] }}
              </span>
            </el-tooltip>
            <span @click="cancel(item.name)" style="cursor: pointer"
              ><i class="el-icon-close"></i
            ></span>
          </div>
        </div>
        <div
          v-if="item.way == 'selectOne' && item.name == 'cloud_name'"
          class="filterBox"
          :key="index"
        >
          <div>
            <span class="label">{{ item.label }}：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="conditionList[item.name]"
              placement="top"
            >
              <span class="right">
                {{ conditionList[item.name] }}
              </span>
            </el-tooltip>
            <span @click="cancel(item.name)" style="cursor: pointer"
              ><i class="el-icon-close"></i
            ></span>
          </div>
        </div>
        <div v-if="item.way == 'other' && item.name == 'reason'" class="filterBox">
          <div>
            <span class="label">{{ item.label }}：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="getReason(conditionList[item.name])"
              placement="top"
            >
              <span class="right">
                {{ getReason(conditionList[item.name]) }}
              </span>
            </el-tooltip>
            <span @click="cancel(item.name)" style="cursor: pointer"
              ><i class="el-icon-close"></i
            ></span>
          </div>
        </div>
        <div v-if="item.way == 'other' && item.name == 'score_type'" class="filterBox">
          <div v-if="conditionList[item.name] == 4">
            <span class="label">{{ item.label }}：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="conditionList[item.names]"
              placement="top"
            >
              <span class="right">
                {{ conditionList[item.names] }}
              </span>
            </el-tooltip>
            <span @click="quexiaoScore()" style="cursor: pointer"
              ><i class="el-icon-close"></i
            ></span>
          </div>
          <div v-else>
            <span class="label">{{ item.label }}：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="getScore(conditionList[item.name])"
              placement="top"
            >
              <span class="right">
                {{ getScore(conditionList[item.name]) }}
              </span>
            </el-tooltip>
            <span @click="quexiaoScore()" style="cursor: pointer"
              ><i class="el-icon-close"></i
            ></span>
          </div>
        </div>
        <div
          v-if="
            item.way == 'other' &&
            item.name == 'created_at' &&
            conditionList['created_at'] &&
            conditionList['created_at'].length &&
            conditionList['created_at'].length > 1
          "
          class="filterBox"
        >
          <div>
            <span class="label">{{ item.label }}：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="getTime(conditionList[item.name])"
              placement="top"
            >
              <span class="right">
                {{ getTime(conditionList[item.name]) }}
              </span>
            </el-tooltip>
            <span @click="cancelTime(item.name)" style="cursor: pointer"
              ><i class="el-icon-close"></i
            ></span>
          </div>
        </div>
        <div
          v-if="
            item.way == 'other' &&
            item.name == 'updated_at' &&
            conditionList['updated_at'] &&
            conditionList['updated_at'].length &&
            conditionList['updated_at'].length > 1
          "
          class="filterBox"
        >
          <div>
            <span class="label">{{ item.label }}：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="getTime(conditionList[item.name])"
              placement="top"
            >
              <span class="right">
                {{ getTime(conditionList[item.name]) }}
              </span>
            </el-tooltip>
            <span @click="cancelTime(item.name)" style="cursor: pointer"
              ><i class="el-icon-close"></i
            ></span>
          </div>
        </div>
        <!-- </div> -->
      </div>
    </div>
    <span v-if="isShowMoreBtn" @click="changeRight" style="cursor: pointer"
      ><i class="el-icon-arrow-right"></i
    ></span>
  </div>
</template>

<script>
export default {
  name: 'hightFilter',
  props: {
    formInline: {
      type: Object,
      default: {}
    },
    total: Number,
    activeName: String, //ip+端口维度的标识
    threaten_type_arr: {
      type: Array,
      default: () => []
    },
    assetsSourceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      reasonDataMap: {
        0: '根域',
        1: '证书',
        2: 'ICP',
        3: 'ICON',
        4: '关键词',
        6: 'IP段'
      },
      ipMatchMap: {
        1: '否',
        2: '是'
      },
      isShowMoreBtns: false,
      isShowMoreBtn: false,
      conditionList: {}, //高级筛选条件
      listDate: [
        // ip筛选
        {
          label: '关键字',
          name: 'keyword',
          way: 'input',
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/unitIndex'
          ]
        },
        {
          label: 'IP地址',
          name: 'ip',
          way: 'input',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '端口',
          name: 'port',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '协议',
          name: 'protocol',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '网站标题',
          name: 'title',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '状态码',
          name: 'http_status_code',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: 'host',
          name: 'hosts',
          way: 'input',
          path: ['/assetsLedger', '/riskAssets']
        },
        {
          label: '子域名',
          name: 'subdomain',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '域名',
          name: 'domain',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '非企业线索库域名',
          name: 'not_in_clue_domain',
          way: 'select',
          path: ['/assetsLedger', '/unclaimCloud']
        },
        {
          label: '数据来源',
          name: 'assets_source',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '组件信息',
          name: 'rule_tags',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '地理位置',
          name: 'province',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '资产状态',
          name: 'online_state',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '威胁类型',
          name: 'threaten_type_arr',
          way: 'select',
          path: ['/threatAssets']
        },
        {
          label: '创建时间',
          name: 'created_at',
          way: 'other',
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '更新时间',
          name: 'updated_at',
          way: 'other',
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '证据链',
          name: 'reason_type',
          way: 'selectOne',
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: 'CDN',
          name: 'is_cdn',
          way: 'selectOne-yesOrNot',
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '泛解析',
          name: 'open_parse',
          way: 'selectOne-yesOrNot',
          path: ['/assetsLedger']
        },
        {
          label: '云厂商',
          name: 'cloud_name',
          way: 'selectOne',
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '是否ip匹配',
          name: 'ip_match',
          way: 'selectOne-yesOrNot',
          path: ['/assetsLedger']
        },
        {
          label: '是否企业匹配',
          name: 'company_match',
          way: 'selectOne-yesOrNot',
          path: ['/assetsLedger']
        },
        {
          label: '资产标签',
          name: 'tags',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '自定义标签',
          name: 'customer_tags',
          way: 'select',
          path: ['/assetsLedger']
        }
      ]
    }
  },
  watch: {
    formInline: {
      //监听高级筛选条件
      handler: function (value) {
        this.conditionList = value
        this.$nextTick(() => {
          this.changeBoxItemStyle()
        })
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.conditionList = (this.formInline && JSON.parse(JSON.stringify(this.formInline))) || {}
    this.conditionList.reason = String(this.conditionList.reason)
    // ip+端口
    if (this.activeName == 'first' && this.$route.path != '/scanReg') {
      this.listDate = [
        {
          label: '关键字',
          name: 'keyword',
          way: 'input',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: 'IP地址',
          name: 'ip',
          way: 'input',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '端口',
          name: 'port',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '协议',
          name: 'protocol',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: 'URL',
          name: 'url',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '域名',
          name: 'domain',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '子域名',
          name: 'subdomain',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '数据来源',
          name: 'assets_source',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '网站标题',
          name: 'title',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '组件信息',
          name: 'rule_tags',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '状态码',
          name: 'http_status_code',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '威胁类型',
          name: 'threaten_type_arr',
          way: 'select',
          path: ['/threatAssets']
        },
        {
          label: '运营商',
          name: 'isp',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '是否ip匹配',
          name: 'ip_match',
          way: 'selectOne-yesOrNot',
          path: ['/assetsLedger']
        },
        {
          label: '是否企业匹配',
          name: 'company_match',
          way: 'selectOne-yesOrNot',
          path: ['/assetsLedger']
        },
        {
          label: '地理位置',
          name: 'province',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        // {
        //     label: 'ASN',
        //     name: 'asn',
        //     way: 'select',
        //     path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        // },
        // {
        //   label: '经度',
        //   name: 'lon',
        //   way: 'input',
        //   path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        // },
        // {
        //   label: '纬度',
        //   name: 'lat',
        //   way: 'input',
        //   path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        // },
        {
          label: '资产状态',
          name: 'online_state',
          way: 'select',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '探测方式',
          name: 'reason',
          way: 'other',
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        // {
        //     label: '可信度值',
        //     name: 'score_type',
        //     names: 'score',
        //     way: 'other',
        //     path: ['/unclaimCloud']
        // },
        {
          label: '更新时间',
          name: 'updated_at',
          way: 'other',
          path: ['/assetsLedger']
        },
        {
          label: '推荐时间',
          name: 'created_at',
          way: 'other',
          path: ['/unclaimCloud']
        },
        {
          label: '忽略时间',
          name: 'updated_at',
          way: 'other',
          path: ['/ignoreAssets']
        },
        {
          label: '标记威胁时间',
          name: 'updated_at',
          way: 'other',
          path: ['/threatAssets']
        }
      ]
    }
    // 推荐记录
    if (this.$route.path == '/scanReg') {
      this.listDate = [
        {
          label: '关键字',
          name: 'keyword',
          way: 'input',
          path: ['/scanReg']
        },
        {
          label: 'IP地址',
          name: 'ip',
          way: 'input',
          path: ['/scanReg']
        },
        {
          label: '端口',
          name: 'port',
          way: 'select',
          path: ['/scanReg']
        },
        {
          label: '协议',
          name: 'protocol',
          way: 'select',
          path: ['/scanReg']
        },
        {
          label: '根域',
          name: 'domain',
          way: 'select',
          path: ['/scanReg']
        },
        {
          label: '子域名',
          name: 'subdomain',
          way: 'select',
          path: ['/scanReg']
        },
        {
          label: '网站标题',
          name: 'title',
          way: 'select',
          path: ['/scanReg']
        },
        {
          label: '证书',
          name: 'cert',
          way: 'select',
          path: ['/scanReg']
        },
        {
          label: 'ICP',
          name: 'icp',
          way: 'select',
          path: ['/scanReg']
        }
      ]
    }

    // 推荐记录IP+端口、ip维度
  },
  computed: {
    list() {
      let arr = this.listDate.filter((item) => {
        return item.path.indexOf(this.$route.path) != -1
      })
      return arr
    }
  },
  mounted() {
    //右按钮显示、隐藏
    this.changeBoxItemStyle()
  },
  methods: {
    changeBoxItemStyle() {
      if (this.$refs.boxItem.clientWidth >= this.$refs.boxItem.parentElement.clientWidth * 0.9) {
        this.isShowMoreBtn = true
      } else {
        this.isShowMoreBtn = false
      }
    },
    getAssetsSourceName(value) {
      let typeName = ''
      this.assetsSourceList.forEach((item) => {
        if (value == item.value) {
          typeName = item.label
        }
      })
      return typeName
    },
    getThreatTypeName(typeId) {
      let typeName = ''
      this.threaten_type_arr.forEach((item) => {
        if (typeId == item.id) {
          typeName = item.type_name
        }
      })
      return typeName
    },
    getTagsName(tags) {
      //资产标签转换
      let tagname = ''
      switch (tags) {
        case 0:
          tagname = '用户-扫描'
          break
        case 1:
          tagname = '安服-扫描'
          break
        case 2:
          tagname = '用户-推荐'
          break
        case 3:
          tagname = '安服-推荐'
          break
        case 4:
          tagname = '安服-导入'
          break
      }
      return tagname
    },
    changeStatus(val) {
      //资产状态转换
      if (val == '0') {
        return '离线'
      } else {
        return '在线'
      }
    },
    cancel(name) {
      //单个删除
      this.$set(this.conditionList, name, '')
      this.highCheck()
    },
    deleteItem(name, data) {
      //数组多个删除
      this.conditionList[name] = this.conditionList[name].filter(function (value) {
        return value != data
      })
      this.highCheck()
      // this.$emit('highCheck', this.conditionList)
    },
    cancelTime(data) {
      //时间删除
      this.conditionList[data] = []
      this.highCheck()
    },
    highCheck() {
      //调用父类用法搜索
      this.$emit('highCheck', this.conditionList)
    },
    changeRight() {
      //右移动
      let boxItem = document.getElementById('boxItem')
      boxItem.scrollLeft = boxItem.scrollLeft + 50
      if (boxItem.scrollLeft > 0) {
        this.isShowMoreBtns = true
      }
    },
    changeLeft() {
      //左移动
      let boxItem = document.getElementById('boxItem')
      if (boxItem.scrollLeft > 0) {
        boxItem.scrollLeft = boxItem.scrollLeft - 50
      }
      if (boxItem.scrollLeft <= 0) {
        this.isShowMoreBtns = false
      }
    },
    getString(data) {
      //转换成字符串
      return String(data)
    },
    getTime(data) {
      //时间转换
      if (data.length && data.length > 1) {
        return data[0] + '至' + data[1]
      }
    },
    getReason(data) {
      //探测方式转换
      if (data == '0') {
        return '根域'
      } else if (data == '1') {
        return '证书'
      } else if (data == '2') {
        return 'ICP'
      } else if (data == '3') {
        return 'ICON'
      } else if (data == '4') {
        return '关键词'
      } else if (data == '5') {
        return '子域名'
      } else if (data == '6') {
        return '已知资产IP'
      }
    },
    getScore(data) {
      //可信度值转化
      if (data == 1) {
        return '中可信度'
      } else if (data == 2) {
        return '高可信度'
      } else {
        return '低可信度'
      }
    },
    quexiaoScore() {
      //删除分数
      this.conditionList.score_type = ''
      this.conditionList.score = ''
      this.highCheck()
    }
  }
}
</script>

<style lang="less" scoped>
.hightFilterBox {
  width: 100%;
  // padding: 0px 20px 16px 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
#boxItem {
  max-width: 98%;
  display: inline-flex;
  align-items: center;
  overflow: auto;
  white-space: nowrap;
  text-overflow: ellipsis;
}
#boxItem::-webkit-scrollbar {
  display: none;
}
.boxItemBox {
  margin-left: 8px;
  font-size: 12px;
}
.boxItemBox:first-child {
  margin-left: 0px;
}
.filterBox {
  margin-left: 8px;
}
.filterBox:first-child {
  margin-left: 0px;
}
.filterBox {
  display: inline-block;
  padding: 5px;
  border: 1px solid #dfe4ed;
  background: #f8f9fc;
  border-radius: 4px;
  div {
    display: flex;
    align-content: center;
  }
}
.numBox {
  padding: 5px;
  border: 1px solid #dfe4ed;
  background: #f8f9fc;
  border-radius: 4px;
  color: #62666c;
  font-size: 12px;
}
/deep/.el-button--mini {
  padding: 0 !important;
  border: none;
}
/deep/.el-tabs__item {
  border: none !important;
}
/deep/.el-tabs__nav {
  border: none !important;
  padding-left: 0px !important;
}
/deep/.is-active {
  color: #62666c !important;
  background: none !important;
  font-weight: normal !important;
}
.shu {
  margin-left: 8px;
  width: 1px;
  height: 24px;
  background: #e9ebef;
}
/deep/.right {
  margin-right: 5px;
  max-width: 140px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  color: #37393c;
}
.label {
  color: #62666c;
}
.total {
  color: #2677ff;
}
</style>
