<template>
  <el-dialog
    class="elDialogAdd elDialogAddCompany"
    @open="open"
    @close="$emit('close')"
    :close-on-click-modal="false"
    :visible="visible"
    :width="width"
  >
    <template slot="title"> 标记关键词到黑名单 </template>
    <div class="dialog-body">
      <div class="dialog-item">
        <div class="label"> 威胁类型：</div>
        <div class="value">
          <el-select v-model="typeItem" placeholder="请选择威胁类型" value-key="id">
            <el-option
              v-for="item in typeSelectList"
              :label="item.type_name"
              :value="item"
              :key="item.id"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="dialog-item">
        <div class="label"> 关键词：</div>
        <div class="value">
          <el-input
            v-model="inputValue"
            style="font-size: 16px; font-weight: bold; color: #8c939d"
            placeholder="请输入需要标记关键词"
            @keyup.enter.native="saveBtn"
          >
          </el-input>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="$emit('close')">取消</el-button>
      <el-button class="highBtn" type="primary" v-loading="loading" @click="saveBtn"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import { pgdTypeList } from '@/api/apiConfig/api.js'
import { setBlackList } from '@/api/apiConfig/asset.js'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '500px'
    },
    selectTitle: {
      type: String,
      default: ''
    },
    // inputType: {
    //   type: String,
    //   default:'text'
    // },
    label: {
      type: String,
      default: '企业名称'
    },
    rows: {
      type: Number,
      default: 2
    }
  },
  watch: {
    selectTitle(val) {
      this.inputValue = val
    }
  },
  data() {
    return {
      typeItem: '',
      typeSelectList: [],
      inputValue: '',
      newCompanyLoading: false
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  methods: {
    async getTypeList() {
      let res = await pgdTypeList()
      if (res.code == 0) {
        this.typeSelectList = res.data.items
        this.typeSelectListString = res.data.items.map((item) => item.type_name).join('\n')
      }
    },
    open() {
      this.typeItem = {}
      this.typeId = ''
      this.getTypeList()
    },
    async saveBtn() {
      if (!this.typeItem.id) {
        this.$message.error('请选择威胁类型！')
        return
      }
      if (!this.inputValue) {
        this.$message.error('请输入需要标记关键词！')
        return
      }
      let res = await setBlackList({
        operate_company_id: this.currentCompany,
        keyword: this.inputValue,
        type_id: this.typeItem.id,
        threaten_type_name: this.typeItem.type_name
      })
      if (res.code == 0) {
        this.$emit('close')
        this.$message.success('标记成功')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.elDialogAddCompany {
  /deep/.el-dialog__body {
    min-height: 138px !important;
    .el-textarea {
      height: auto !important;
    }
  }
  .dialog-body {
    height: 38px;

    .dialog-item {
      display: flex;
      justify-content: start;
      align-items: start;
      margin-top: 10px;
      .label {
        width: 80px;
        line-height: 30px;
      }
      .value {
        flex: 1;
        /deep/.el-select {
          width: 100%;
        }
      }
    }
  }

  &.elDialogAddTextarea {
    /deep/.el-dialog__body {
      min-height: 278px !important;
    }
  }
}
</style>
