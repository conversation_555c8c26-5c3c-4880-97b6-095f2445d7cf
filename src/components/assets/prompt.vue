<template>
  <el-dialog
    class="elDialogAdd elDialogAddCompany"
    :class="{ elDialogAddTextarea: inputType == 'textarea' }"
    @close="close"
    @open="open"
    :close-on-click-modal="false"
    :visible="visible"
    :width="width"
  >
    <template slot="title"> {{ title }} </template>
    <div class="dialog-body">
      <div class="dialog-item">
        <div class="label" :style="{ width: labelWidth + 'px' }">
          {{ label }}<slot name="labelIcon"></slot>：</div
        >
        <div class="value">
          <el-input
            v-if="inputType == 'textarea'"
            :rows="rows"
            :type="inputType"
            v-model="inputValue"
            style="font-size: 16px; font-weight: bold; color: #8c939d"
            :placeholder="placeholder"
          >
          </el-input>
          <el-select v-else-if="inputType == 'select'" v-model="inputValue" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.name"
              :label="item.label"
              :value="item.name"
            >
            </el-option>
          </el-select>
          <el-input
            v-else
            :type="inputType"
            v-model="inputValue"
            style="font-size: 16px; font-weight: bold; color: #8c939d"
            :placeholder="placeholder"
            @keyup.enter.native="saveBtn"
          >
          </el-input>
        </div>
      </div>
      <slot name="tip"></slot>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="close">取消</el-button>
      <el-button class="highBtn" type="primary" v-loading="loading" @click="saveBtn"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '500px'
    },
    inputType: {
      type: String,
      default: 'text'
    },
    label: {
      type: String,
      default: '企业名称'
    },
    rowValue: {
      type: String,
      default: ''
    },
    rows: {
      type: Number,
      default: 2
    },
    labelWidth: {
      type: String,
      default: '80'
    },
    inputValueBack: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      inputValue: '',
      newCompanyLoading: false,
      options: [
        { name: '低危', label: '低危' },
        { name: '中危', label: '中危' },
        { name: '高危', label: '高危' },
        { name: '严重', label: '严重' }
      ]
    }
  },
  watch: {
    inputValueBack(val) {
      this.inputValue = val
    },
    rowValue: {
      handler(val) {
        this.inputValue = val
      },
      immediate: true
    }
  },
  methods: {
    open() {
      if (this.inputValueBack) {
        this.inputValue = this.inputValueBack
      } else {
        this.inputValue = ''
      }
    },
    saveBtn() {
      this.$emit('save', this.inputValue)
    },
    close() {
      this.$emit('close')
      this.inputValue = ''
    }
  }
}
</script>

<style lang="less" scoped>
.elDialogAddCompany {
  /deep/.el-dialog__body {
    min-height: 58px !important;
  }
  .dialog-body {
    // height: 38px;

    .dialog-item {
      display: flex;
      justify-content: start;
      align-items: start;
      margin-bottom: 20px;
      .label {
        // width: 80px;
        line-height: 30px;
      }
      .value {
        flex: 1;
      }
    }
  }

  &.elDialogAddTextarea {
    /deep/.el-dialog__body {
      min-height: 178px !important;
    }
  }
}
</style>
