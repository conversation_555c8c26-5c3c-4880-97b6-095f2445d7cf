<template>
  <el-dialog
    class="elDialogAdd elDialogAddValidate"
    :close-on-click-modal="false"
    @close="$emit('close')"
    :visible="dialogVisible"
    width="500px"
  >
    <template slot="title">
      <slot name="title"></slot>
    </template>
    <div class="dialog-body">
      <div class="dialog-item">
        <!-- 以下输入错误格式的资产,可以复制并在输入框中修改 -->
        <slot name="tip"></slot>
      </div>
      <div
        ><div class="dialog-item" v-for="(item, k) in list" :key="k">{{ item }}</div></div
      >
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="$emit('close')">关闭</el-button>
      <el-button class="highBtn" type="primary" @click="confirm"
        ><slot name="confirmBtn"></slot
      ></el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    },
    validateType: {
      type: String,
      default: 'textarea'
    }
  },
  methods: {
    confirm() {
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="less" scoped>
.highBtn {
  width: 174px;
}
</style>
