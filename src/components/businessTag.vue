<template>
  <el-tooltip
    class="item"
    effect="dark"
    placement="top"
    :open-delay="500"
    :disabled="!descriptions"
  >
    <span slot="content">{{ descriptions }}</span>
    <div
      class="business-tag"
      :style="{ backgroundColor: color, color: '#fff', height: '20px', lineHeight: '20px' }"
      >{{ reg_status }}<i class="el-icon-warning-outline" v-if="!!descriptions"></i
    ></div>
  </el-tooltip>
</template>

<script>
import { type, typeMap } from '@/utils/commonData.js'
export default {
  name: 'businessTagVue',
  props: {
    reg_status: {
      type: String,
      default: ''
    }
  },
  computed: {
    descriptions() {
      return type[typeMap[this.reg_status]]?.descriptions || ''
    },
    color() {
      return type[typeMap[this.reg_status]]?.color || '#b32625'
    }
  }
}
</script>

<style lang="less" scoped>
.business-tag {
  display: inline-block;
  padding: 0 8px;
  text-align: center;
  border-radius: 12.5px;
  font-size: 12px;
  color: #fff;
}
.el-icon-warning-outline {
  margin-left: 2px;
}
</style>
