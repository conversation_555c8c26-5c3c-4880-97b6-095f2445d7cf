<template>
  <div>
    <div v-if="tipData">
      <el-tooltip placement="top" :open-delay="500">
        <div slot="content" style="display: flex">
          <span style="color: #62666c">任务记录：</span>
          <span>{{ `${tipData.name}任务` }}</span>
          <span class="myStatus mygrayStatus" v-if="tipData.status == 0">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myblueStatus" v-else-if="tipData.status == 1">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus mygreenStatus" v-else-if="tipData.status == 2">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myredStatus" v-else-if="tipData.status == 3">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myyellowStatus" v-else>{{ getTableStatus(tipData.status) }}</span
          >，
          <span v-if="tipData.status == 1 || tipData.status == 4" style="color: #2677ff"
            >{{ `${tipData.progress}%` }}<span class="tipDataContent">，</span></span
          >
          <span class="tipDataContent" v-if="tipData.status == 2"
            >{{ `${getResult(tipData)}` }}，</span
          >
          <span @click="goAssetsScan" class="taskBlue"
            >可点击跳转查看详情<i class="el-icon-arrow-right"></i
          ></span>
        </div>
        <div style="display: flex !important; align-items: center">
          <span style="color: #62666c">任务记录：</span>
          <span class="task">{{ `${tipData.name}任务` }}</span>
          <span class="myStatus mygrayStatus" v-if="tipData.status == 0">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myblueStatus" v-else-if="tipData.status == 1">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus mygreenStatus" v-else-if="tipData.status == 2">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myredStatus" v-else-if="tipData.status == 3">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myyellowStatus" v-else>{{ getTableStatus(tipData.status) }}</span
          >，
          <span v-if="tipData.status == 1 || tipData.status == 4"
            >{{ `${tipData.progress}%` }}<span class="tipDataContent">，</span></span
          >
          <span @click="goAssetsScan" class="taskBlue"
            >详情<i class="el-icon-arrow-right"></i
          ></span>
        </div>
      </el-tooltip>
    </div>
    <div v-else>暂无任务</div>
  </div>
</template>

<script>
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import { mapState, mapGetters } from 'vuex'
import { getAllTaskList } from '@/api/apiConfig/discovery.js'

export default {
  props: {
    typeName: {
      type: String,
      default: ''
    },
    wsTypeName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentId: '',
      loading: false,
      tipData: {},
      user: {
        role: ''
      },
      userInfo: {}
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  watch: {
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getMergeTakList()
      }
    }
  },
  mounted() {
    if (sessionStorage.getItem('userMessage')) {
      this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (this.userInfo) {
        this.user = this.userInfo.user
      }
    }
    if (this.user.role == 2 && !this.currentCompany) return
    this.getMergeTakList() // 获取顶部任务记录信息
  },
  methods: {
    getResult(row) {
      // asset_num、rule_num都不存在或者都等于0，显示【未发现网络资产】
      // threat_num不存在或者都等于0，显示【未发现漏洞】
      let str = ''
      let strSp = row.asset_num && row.asset_num / 1 > 0 ? ',' : ''
      if (row.asset_num && row.asset_num / 1 > 0) {
        str += '发现IP：' + row.asset_num
      }
      if (row.rule_num && row.rule_num / 1 > 0) {
        str += strSp + '发现组件：' + row.rule_num
      }
      if ((!row.asset_num || row.asset_num / 1 == 0) && (!row.rule_num || row.rule_num / 1 == 0)) {
        str = '未发现网络资产'
      }
      return str
    },
    goAssetsScan() {
      //跳转
      // sessionStorage.setItem('menuId', '2-1')
      this.$router.push('/assetsScan')
      // this.changeMenuId('2-1')
    },
    getTableStatus(status) {
      let statusLabel = ''
      if (status == 0) {
        statusLabel = '等待扫描'
      } else if (status == 1) {
        statusLabel = '扫描中'
      } else if (status == 2) {
        statusLabel = '扫描完成'
      } else if (status == 3) {
        statusLabel = '扫描失败'
      } else if (status == 4) {
        statusLabel = '暂停扫描'
      }
      return statusLabel
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (this.$route.path == '/domainAsset' || this.$route.path == '/domainDetails') {
        let isCurrentTypeName = res.data.name && res.data.name.indexOf(this.typeName) >= 0
        if (
          res.cmd == 'scan_task_progress' &&
          isCurrentTypeName &&
          res.data.type != 1 &&
          this.tipData &&
          this.tipData.id &&
          this.tipData.id == res.data.task_id
        ) {
          this.runningFunc(res)
        }
      }
    },
    // websocket执行
    runningFunc(res) {
      if (res.data.status == 2) {
        resetMessage.success('扫描成功！')
        this.currentId = '' // 控制推送结束后仅执行一次
        this.loading = true
        setTimeout(() => {
          this.$emit('updateList')
          this.getMergeTakList()
          // this.getknownAssetsList(1) // 台账,is_sleep：1代表是删除后请求列表接口，后端需要延时 重新获取列表数据
        }, 2000)
      } else if (res.data.status == 1) {
        // 正在扫描
        this.currentId = res.data.task_id
        // 推送数据渲染到列表
        this.$set(this.tipData, 'status', res.data.status)
        this.$set(this.tipData, 'target', res.data.target)
        this.$set(this.tipData, 'progress', res.data.progress)
        this.$set(this.tipData, 'use_seconds', res.data.use_seconds)
        this.$set(this.tipData, 'start_at', res.data.start_at)
      } else if (res.data.status == 4) {
        // 暂停扫描
      } else {
        // 3 扫描失败
        this.getMergeTakList() //获取顶部任务记录信息
      }
    },
    async getMergeTakList() {
      let obj = {
        page: 1,
        per_page: 10,
        type: '',
        task_type: 1, // 任务分类 1 资产扫描 2 漏洞扫描,
        name: this.typeName,
        status: '', // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
        created_at_range: [],
        dispatched_at_range: [],
        sortField: 'status',
        sortOrder: 'asc',
        is_schedule: 0,
        op_id: '',
        operate_company_id: this.currentCompany
      }
      let res = await getAllTaskList(obj).catch(() => {
        this.$emit('updateScanStatus')
      })
      if (res.data) {
        this.tipData = res.data.items && res.data.items.length > 0 ? res.data.items[0] : ''
        this.$emit('updateScanStatus')
      } else {
        this.$emit('updateScanStatus')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.myStatus {
  display: flex;
  align-items: center;
}
.mygreenStatus {
  color: #10d58c !important;
}
.myyellowStatus {
  color: #ffbc00 !important;
}
.mygrayStatus {
  color: gray !important;
}
.myblueStatus {
  color: #2677ff !important;
}
.myredStatus {
  color: #de3737 !important;
}
.tipDataContent {
  color: #62666c !important;
}
</style>
