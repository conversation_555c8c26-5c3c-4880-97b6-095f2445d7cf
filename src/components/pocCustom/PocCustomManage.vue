<template>
  <div class="poc-custom-manage">
    <section class="top-breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          <span @click="handleClose" style="color: #4285f4; cursor: pointer"
            ><i class="el-icon-arrow-left" style="margin-right: 4px"></i>返回上一层</span
          >
        </el-breadcrumb-item>
        <el-breadcrumb-item>{{ title }}</el-breadcrumb-item>
      </el-breadcrumb>
    </section>
    <el-card class="bottom-card" :body-style="{ padding: '0px' }">
      <div class="bottom">
        <div class="right">
          <div class="rules-right-bottom">
            <Vulnerability
              :ruleScanForm="ruleScanForm"
              :pocCustomId="pocCustomId"
              ref="Vulnerability"
              @close="handleClose"
            />
          </div>
        </div>
      </div>
    </el-card>
    <!-- <GetshellModal @submit="loadData" ref="GetshellModal" />
    <AdvancedFilterGetshell @search="loadData" ref="AdvancedFilterGetshell" /> -->
  </div>
</template>
<script>
import { number } from 'echarts'
import Vulnerability from './Vulnerability.vue'
export default {
  components: {
    Vulnerability
  },
  props: {
    pocCustomId: {
      type: [Object, Number],
      require: false
    },
    ruleScanForm: {
      type: Object,
      require: false
    }
  },
  data() {
    return {}
  },
  computed: {
    title() {
      if (this.pocCustomId) {
        return '编辑PoC'
      } else {
        return '新建PoC'
      }
    }
  },
  methods: {
    handleClose() {
      // 调用父组件传入的方法
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="less">
.poc-custom-manage {
  height: calc(100%);
  overflow: hidden;
  width: 100%;

  /deep/ .top-breadcrumb {
    box-shadow: 0 1px 0 1px #e6e6e6;
    font-family: PingFangSC-Regular, sans-serif;
    font-weight: 400;
    font-size: 14px;
    width: calc(100% + 48px);
    background: #fff;
    padding: 18px 24px;
    margin-top: -55px;
    margin-left: -21px;
    margin-bottom: 22px;
    position: fixed;
    z-index: 999;

    .ant-breadcrumb {
      > span {
        color: #4285f4;
        cursor: default;

        .ant-breadcrumb-link {
          cursor: pointer;
        }

        &:last-child {
          color: #333;

          .ant-breadcrumb-link {
            cursor: default;
          }
        }
      }
    }
  }

  .shell-title {
    padding-top: 50px;

    .left {
      .title-text {
        display: flex;
        align-items: center;

        span:nth-child(1) {
          color: #333333;
          font-size: 22px;
          font-family: PingFangSC-Semibold, sans-serif;
          font-weight: 600;
        }

        .title-view {
          display: inline-block;
          margin-left: 24px;
          margin-right: 8px;
          cursor: pointer;
          color: #4285f4;

          .title-view-icon {
            margin-right: 2px;
          }
        }

        .title-tips-icon {
          width: 14px;
          height: 14px;
          cursor: default;
          fill: #999;
        }
      }

      .title-statistic {
        font-size: 12px;
        color: #333333;
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;

        span {
          padding-right: 12px;
        }
      }
    }
  }

  /deep/ .bottom-card {
    background: transparent;
    .ant-card-body {
      background: transparent;
      padding: 0;
    }
  }

  .rules-top {
    background: #fff;
    width: 100%;

    /deep/ .ant-tabs {
      .ant-tabs-bar {
        margin-bottom: 0 !important;
      }

      .ant-tabs-tab {
        font-size: 18px;
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;

        &:hover {
          color: #4285f4;
        }

        &.ant-tabs-tab-active {
          font-family: PingFangSC-Semibold, sans-serif;
          font-weight: 600;
          color: #4285f4;
        }
      }

      .ant-tabs-ink-bar {
        background: #4285f4;
      }
    }
  }

  /deep/ .bottom {
    width: 100%;
    .right {
      padding: 0 !important;
      background: transparent !important;
      width: 100%;

      .getshell-right-top {
        background: #fff;
        padding: 24px;
        display: flex;
        justify-content: space-between;

        .ant-input-affix-wrapper {
          width: 360px;

          .ant-input {
            background: #f2f4f7;
            border-color: #f2f4f7;

            &:hover {
              border-color: #4285f4;
            }
          }
        }

        .right-top-title {
          font-family: PingFangSC-Semibold, sans-serif;
          color: #333333;
          font-weight: 600;
          padding-right: 12px;
        }

        .getshell-right-top-inner-right {
          .ant-btn {
            padding: 0 6px;
            margin-left: 24px;
            border-color: #4285f4;
            color: #4285f4;

            &.ant-btn-primary {
              background: #4285f4;
              color: #fff;
            }

            .getshell-btn-icon {
              width: 14px;
              height: 14px;
              transform: translateY(2px);
              fill: #4285f4;
              margin-right: 8px;

              &.white {
                fill: #fff;
              }

              &.list-row {
                margin-right: 2px;
                margin-left: 2px;
              }
            }
          }
        }
      }

      .rules-right-bottom {
        // margin-top: 34px;
        padding: 0;
        background: transparent;
        height: calc(100vh - 122px);
        width: 100%;
      }

      .table-action-slot {
        svg {
          height: 14px;
          width: 14px;
          fill: #333;
          cursor: pointer;
          margin-right: 24px;
          transition: all 0.3s;

          &:focus {
            outline: none;
          }

          &:hover {
            fill: #4285f4;
          }
        }
      }
    }
  }
}
</style>
