<template>
  <el-drawer
    class="dialog-scan-config"
    id="syntax-reference-dia"
    :append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="visible"
    :title="'查询语法列表'"
    :size="586"
    @close="hideRefDia"
  >
    <div class="dialog-scan-config-main">
      <div class="dialog-scan-config-bot syn-refer">
        <div>
          <div class="syn-refer-title"
            >直接输入查询语句，将从标题，html内容，http头信息，url字段中搜索</div
          >
          <div class="syn-refer-item" v-for="(item, index) in synArr" :key="index">
            <i class="iconfont iconInquire-"></i>
            <span>○</span>
            <span v-if="item.rules !== '海康威视-视频监控'" style="padding-left: 4px">{{
              item.rules
            }}</span>
            <span v-else style="padding-left: 4px">app="{{ item.rules }}"</span>
            {{ item.text }}
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'SyntaxReferenceDia',
  props: {
    visible: {
      default: false,
      type: Boolean
    }
  },
  data() {
    return {
      synArr: [
        {
          rules: 'title="abc"',
          text: '从标题中搜索abc'
        },
        {
          rules: 'header="abc"',
          text: '从http头中搜索abc'
        },
        {
          rules: 'body="abc"',
          text: '从html正文中搜索abc'
        },
        {
          rules: 'port="443"',
          text: '查找对应443端口的资产'
        },
        {
          rules: 'ip="*******"',
          text: '从ip中搜索包含*******的网站,注意搜索要用ip作为名称'
        },
        {
          rules: 'protocol="https"',
          text: '搜索制定协议类型'
        },
        {
          rules: '',
          text: 'banner=users && protocol=ftp 搜索FTP协议中带有users文本的资产'
        },
        {
          rules: 'server=="Microsoft-IIS/7.5"',
          text: '搜索IIS 7.5服务器'
        },
        {
          rules: 'base_protocol="udp"',
          text: '搜索指定udp协议的资产'
        },
        {
          rules: 'cert="google"',
          text: '搜索证书(https或者imaps等)中带有google的资产'
        },
        {
          rules: '海康威视-视频监控',
          text: '搜索海康威视设备'
        }
      ]
    }
  },
  methods: {
    hideRefDia() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped></style>
