<template>
  <el-drawer
    class="dialog-scan-config"
    id="syntax-reference-dia"
    :append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="toDebugPocShow"
    @close="hideRefDia"
    :title="'查询'"
    width="586px"
    direction="rtl"
  >
    <div class="dialog-scan-config-main" v-loading="loading">
      <div class="dialog-scan-config-bot syn-refer">
        <div style="max-height: unset">
          <div class="syn-refer-title debug-title">
            <i
              class="iconfont iconzhongyaoloudongdeIP"
              :style="
                state && vulnerable
                  ? 'color: #F5222D;background: rgba(245, 34, 45, 0.1);'
                  : 'color: #4285F4;background: rgba(66, 133, 244, 0.1);'
              "
              style="border-radius: 50%; padding: 6px; font-size: 12px"
            ></i>
            <span
              style="padding-left: 8px; font-weight: bold"
              :style="state && vulnerable ? 'color: #F5222D' : 'color: #333;'"
              >{{ vulnerable ? '存在漏洞' : '不存在漏洞' }}</span
            >
          </div>
          <div class="syn-refer-body" style="height: 450px; overflow: auto; border-color: #d5d8de">
            <!-- <div v-if="synArr && synArr.length" class="syn-refer-item" v-for="(item, index) in synArr" :key="index">
                <span>{{item}}</span>
              </div> -->
            <div v-if="synArr && synArr.length" v-html="synArr" class="syn-refer-item"></div>
            <div
              v-else
              style="height: 100%; display: flex; justify-content: center; align-items: center"
            >
              <span
                style="
                  font-family:
                    PingFangSC-Regular,
                    PingFang SC,
                    sans-serif;
                  color: #333333;
                "
                >暂未发现漏洞</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'ToDebugPocDialog',
  data() {
    return {
      synArr: [],
      state: false,
      loading: false,
      loadingImg: require('@/assets/images/pocCustom/loading.png'),
      vulnerable: false
    }
  },
  props: {
    toDebugPocShow: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    hideRefDia() {
      this.$emit('close')
      setTimeout(() => {
        this.synArr = []
      }, 300)
    },
    getReadyData(data) {
      let finallyResponse = data
        .map((item) => {
          return item.last_response
        })
        .join('\n<br>')
      console.log(finallyResponse)

      if (finallyResponse) {
        // 扫描到漏洞  vulnerable
        this.state = true
        this.vulnerable = true
        this.synArr = finallyResponse || []
      } else {
        this.state = false
        this.vulnerable = false
        this.synArr = []
      }

      this.loading = false
      // setTimeout(()=>{
      //   this.loading = false
      // },1000)
    }
  },
  watch: {
    toDebugPocShow: {
      handler() {
        if (this.toDebugPocShow) {
          // this.loading = true
          /*setTimeout(()=>{
              this.loading = false
              this.synArr = [
                  'GET/test.phh Http/1.1',
                  'Host:127.0.0.1',
                  'redis_git_dirty:0',
                  'redis_build_id:8cb6c87fddee19c2',
                  'redis_mode:standalone',
                  'os:Linux 3.10.0-327.el7.x86_64 x86_64',
                  'arch_bits:64',
                  'multiplexing_api:epoll',
                  'gcc_version:4.8.5',
                  'redis_build_id:8cb6c87fddee19c2',
                  'redis_mode:standalone',
                  'os:Linux 3.10.0-327.el7.x86_64 x86_64',
                  'arch_bits:64',
                  'multiplexing_api:epoll',
                  'gcc_version:4.8.5',
                  'redis_build_id:8cb6c87fddee19c2',
                  'redis_mode:standalone',
                  'os:Linux 3.10.0-327.el7.x86_64 x86_64',
                  'arch_bits:64',
                  'multiplexing_api:epoll',
                  'gcc_version:4.8.5'
              ]
          },2000)*/
        }
      },
      immediate: true
    }
  }
}
</script>

<style scoped></style>
