<template>
  <div id="add-poc" style="margin: 0 20px">
    <!-- <file-name-dia
      v-if="isAddFileName"
      :save-file-name.sync="saveFileName"
    ></file-name-dia> -->

    <syntax-reference-dia :visible.sync="synReferenceDia" />
    <to-debug-poc-dialog
      ref="ToDebugDialog"
      :toDebugPocShow="toDebugPocShow"
      @close="closeBugPoc"
    />

    <div class="gb-com-two-cols-wrap">
      <div class="gb-com-two-cols-text ac">
        <div class="gb-com-two-cols-text-left"> {{ title }} </div>
        <div class="gb-com-two-cols-text-right">
          <el-input
            placeholder="请输入IP信息"
            v-model="singleIp"
            @keyup.native.enter="singleIpScan"
          ></el-input>
          <el-button type="primary" :loading="loading" @click="singleIpScan">单IP扫描</el-button>
        </div>
      </div>
      <div class="gb-list-tab add-poc-tab">
        <a @click="changeStep(0)" :class="{ active: curStep === 0 }"> 漏洞信息 </a>
        <a @click="changeStep(1)" :class="{ active: curStep === 1 }"> 测试 </a>
        <a @click="changeStep(2)" :class="{ active: curStep === 2 }"> 编辑器 </a>
      </div>
      <div class="add-poc-main">
        <span v-if="curStep === 1 || curStep === 3" class="cover-scroll"></span>
        <div class="add-poc-step" :class="{ test: curStep === 1 }">
          <div class="add-poc-step-info" v-show="curStep === 0">
            <el-form label-position="left" label-width="100px" :model="vulInfo">
              <el-form-item :label="'名称'" class="custom-label-height">
                <el-input v-model="vulInfo.title"></el-input>
              </el-form-item>
              <el-form-item :label="'查询规则'" class="query-rules custom-label-height">
                <el-input
                  v-model="vulInfo.rules"
                  prefix-icon="el-icon-search"
                  placeholder="请输入关键字进行搜索"
                ></el-input>
                <i @click="toShowRefDia" class="iconForPoc iconbangzhu"></i>
              </el-form-item>
              <el-form-item :label="'等级'" style="white-space: nowrap" class="custom-label-height">
                <el-select v-model="vulInfo.level" style="width: 193px">
                  <el-option :label="'严重'" value="3"></el-option>
                  <el-option :label="'高危'" value="2"></el-option>
                  <el-option :label="'中危'" value="1"></el-option>
                  <el-option :label="'低危'" value="0"></el-option>
                </el-select>
                <span class="scan-zk-open" @click="isAdvanced">
                  高级配置
                  <i
                    :class="[
                      advanced
                        ? 'iconForPoc iconxiajiang scan-zkicon-go'
                        : 'iconForPoc iconxiajiang scan-zkicon'
                    ]"
                  ></i>
                </span>
              </el-form-item>
              <div class="scan-zk"></div>
              <div v-show="advanced">
                <el-form-item :label="'描述'">
                  <textarea id="editor-description"></textarea>
                </el-form-item>
                <el-form-item :label="'产品'">
                  <el-input v-model="vulInfo.product"></el-input>
                </el-form-item>
                <el-form-item :label="'产品主页'">
                  <el-input v-model="vulInfo.productHomepage"></el-input>
                </el-form-item>
                <el-form-item :label="'作者'">
                  <el-input v-model="vulInfo.author"></el-input>
                </el-form-item>
                <el-form-item :label="'发现日期'">
                  <el-date-picker
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    v-model="vulInfo.disclosureDate"
                  >
                  </el-date-picker>
                </el-form-item>

                <el-form-item :label="'来源'">
                  <el-input type="textarea" v-model="vulInfo.source"></el-input>
                </el-form-item>
                <el-form-item :label="'漏洞类型'">
                  <div class="goby-tags-wrap clearfix">
                    <div
                      class="goby-tags-item fl"
                      v-for="(item, index) in dynamicVulTypeJson"
                      :key="index"
                    >
                      {{ item
                      }}<i @click="toDelVulType(index)" class="iconForPoc iconguanbi pointer"></i>
                    </div>
                    <div
                      class="fl placeholder"
                      style="margin: 5px"
                      v-if="!dynamicVulTypeJson.length"
                    >
                      <el-input
                        :placeholder="'漏洞类型'"
                        v-model="vulInfo.vulType"
                        readonly
                      ></el-input>
                    </div>
                  </div>
                  <div class="pre-tags-wrap clearfix">
                    <span
                      v-for="(item, index) in vulInfo.preVulType"
                      :key="index"
                      class="goby-tags-item"
                      @click="toAddVulType(item)"
                      >{{ item }}</span
                    >
                  </div>
                </el-form-item>
                <el-form-item :label="'Is 0day'">
                  <el-checkbox v-model="vulInfo.is0day"></el-checkbox>
                </el-form-item>
                <el-form-item :label="'CVE编号'">
                  <el-input v-model="vulInfo.cveIds"></el-input>
                </el-form-item>

                <el-form-item :label="'漏洞危害'">
                  <textarea id="editor-harm"></textarea>
                </el-form-item>
                <el-form-item :label="'修复建议'">
                  <textarea id="editor-suggestion"></textarea>
                </el-form-item>
              </div>
            </el-form>
          </div>

          <div class="add-poc-step-test scan-step" v-show="curStep === 1">
            <div>
              <!-- {{testSteps}} -->
              <el-tabs v-model="testStepsValue" type="card" closable @tab-remove="removeTab">
                <el-tab-pane
                  v-for="(item, index) in testSteps"
                  :key="item.name"
                  :label="`Test ${index + 1}`"
                  :name="item.name"
                >
                  <div class="poc-test-tab-main">
                    <el-collapse v-model="item.activeNames">
                      <el-collapse-item :name="item.name + 1">
                        <template slot="title"> 请求 </template>
                        <div>
                          <el-form label-position="left" label-width="165px" :model="item.Request">
                            <el-form-item :label="'请求方法'" class="request-item">
                              <el-select
                                v-model="item.Request.method"
                                style="width: auto !important"
                              >
                                <el-option :label="'GET'" value="GET"></el-option>
                                <el-option :label="'POST'" value="POST"></el-option>
                                <el-option :label="'PUT'" value="PUT"></el-option>
                                <el-option :label="'HEAD'" value="HEAD"></el-option>
                                <el-option :label="'PUSH'" value="PUSH"></el-option>
                                <el-option :label="'DELETE'" value="DELETE"></el-option>
                                <el-option :label="'OPTION'" value="OPTION"></el-option>
                                <el-option :label="'CUSTOM'" value="user_define"></el-option>
                              </el-select>
                              <el-input
                                v-model="item.Request.method"
                                v-if="item.Request.method === 'user_define'"
                              ></el-input>
                              <el-checkbox
                                :label="'支持跳转'"
                                v-model="item.Request.follow_redirect"
                              ></el-checkbox>
                            </el-form-item>
                            <el-form-item :label="'URL'">
                              <el-input v-model="item.Request.uri"></el-input>
                            </el-form-item>
                            <el-form-item :label="'Header'">
                              <el-input type="textarea" v-model="item.Request.header"></el-input>
                            </el-form-item>
                            <el-form-item :label="'Post Data'">
                              <el-input type="textarea" v-model="item.Request.data"></el-input>
                              <el-select
                                v-if="false"
                                v-model="item.Request.data_type"
                                style="margin-top: 20px"
                              >
                                <el-option :label="'Text'" value="text"></el-option>
                                <el-option :label="'Hexstring'" value="hexstring"></el-option>
                                <el-option :label="'Base64'" value="base64"></el-option>
                              </el-select>
                            </el-form-item>
                          </el-form>
                        </div>
                      </el-collapse-item>
                      <el-collapse-item :name="item.name + 2">
                        <template slot="title"> 响应测试 </template>
                        <div>
                          <div class="tree-tab clearfix">
                            <span class="test-item" style="font-weight: bold !important"
                              >测试项</span
                            >
                            <span class="variable" style="font-weight: bold !important">变量</span>
                            <span class="operation" style="font-weight: bold !important">操作</span>
                            <span class="value" style="font-weight: bold !important">值</span>
                            <span class="remarks" style="font-weight: bold !important">备注</span>
                          </div>
                          <el-tree
                            :props="defaultProps"
                            :data="item.ResponseTest"
                            node-key="id"
                            class="goby-tree"
                            default-expand-all
                            :expand-on-click-node="false"
                          >
                            <span class="custom-tree-node" slot-scope="{ node, data, indexItem }">
                              <span>{{ data.type }}</span>
                              <el-popover
                                v-if="data.type === 'group'"
                                placement="right-start"
                                width="210"
                                trigger="click"
                                v-model="data.visible"
                              >
                                <div class="goby-tree-pop">
                                  <div v-if="data.id !== 0" @click="() => remove(node, data)">
                                    '删除'
                                  </div>
                                  <div @click="() => append(data, 0)"> '新建分组' </div>
                                  <div @click="() => append(data, 1)"> '新建项' </div>
                                </div>
                                <i
                                  slot="reference"
                                  class="iconForPoc iconvertical goby-tree-menu"
                                ></i>
                              </el-popover>
                              <el-popover
                                v-if="data.type === 'item'"
                                placement="right-start"
                                width="210"
                                trigger="click"
                                v-model="data.visible"
                              >
                                <div class="goby-tree-pop">
                                  <div @click="() => remove(node, data)"> '删除' </div>
                                </div>
                                <i
                                  slot="reference"
                                  class="iconForPoc iconvertical goby-tree-menu"
                                ></i>
                              </el-popover>
                              <el-select
                                class="tree-one-sel"
                                v-model="data.operation"
                                v-if="data.type === 'group'"
                              >
                                <el-option :label="'AND'" value="AND"></el-option>
                                <el-option :label="'OR'" value="OR"></el-option>
                              </el-select>
                              <el-select
                                class="tree-two-sel"
                                v-model="data.variable"
                                v-if="data.type === 'item'"
                              >
                                <el-option :label="'Code'" value="$code"></el-option>
                                <el-option :label="'Header'" value="$head"></el-option>
                                <el-option :label="'Body'" value="$body"></el-option>
                              </el-select>
                              <el-select
                                class="tree-three-sel"
                                v-model="data.operation"
                                v-if="data.type === 'item'"
                              >
                                <el-option :label="'Contains'" value="contains"></el-option>
                                <el-option :label="'Not Contains'" value="not contains"></el-option>
                                <el-option :label="'Regex'" value="regex"></el-option>
                                <el-option :label="'Start With'" value="start_with"></el-option>
                                <el-option :label="'End With'" value="end_with"></el-option>
                                <el-option :label="'=='" value="=="></el-option>
                                <el-option :label="'!='" value="!="></el-option>
                                <el-option :label="'>'" value=">"></el-option>
                                <el-option :label="'<'" value="<"></el-option>
                                <el-option :label="'>='" value=">="></el-option>
                                <el-option :label="'<='" value="<="></el-option>
                              </el-select>
                              <el-input
                                class="tree-one-inp"
                                v-model="data.value"
                                v-if="data.type === 'item'"
                              ></el-input>
                              <el-input
                                class="tree-two-inp"
                                v-model="data.bz"
                                v-if="data.type === 'item'"
                              ></el-input>
                            </span>
                          </el-tree>
                        </div>
                      </el-collapse-item>
                      <el-collapse-item :name="item.name + 3">
                        <template slot="title"> 变量 </template>
                        <div class="variables-item">
                          <div v-for="(itemVar, indexVar) in item.SetVariable" :key="indexVar">
                            <el-input
                              @click.native="addVarItem(indexVar, item.SetVariable.length, 1)"
                              v-model="itemVar.name"
                              :placeholder="'名称'"
                            ></el-input>
                            <el-input
                              @click.native="addVarItem(indexVar, item.SetVariable.length, 1)"
                              v-model="itemVar.value"
                              :placeholder="'值'"
                            ></el-input>
                            <el-select
                              @click.native="addVarItem(indexVar, item.SetVariable.length, 1)"
                              v-model="itemVar.selOne"
                            >
                              <el-option :label="'lastbody'" value="lastbody"></el-option>
                              <el-option :label="'lastheader'" value="lastheader"></el-option>
                              <el-option :label="'statusline'" value="statusline"></el-option>
                            </el-select>
                            <el-select
                              @click.native="addVarItem(indexVar, item.SetVariable.length, 1)"
                              v-model="itemVar.selTwo"
                            >
                              <el-option :label="'regex'" value="regex"></el-option>
                              <el-option :label="'sub'" value="sub"></el-option>
                            </el-select>
                            <i
                              class="iconForPoc iconguanbi"
                              @click="removeVarItem(indexVar, 1)"
                              v-if="indexVar !== item.SetVariable.length - 1"
                            ></i>
                          </div>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </el-tab-pane>
              </el-tabs>
              <div
                :style="{ left: addLeftDis + 'px' }"
                @click="addTab(testStepsValue, 1)"
                class="add-new-tab ac"
              >
                +
              </div>
              <el-select v-model="testMethod" class="test-method-sel">
                <el-option label="AND" value="AND"></el-option>
                <el-option label="OR" value="OR"></el-option>
              </el-select>
            </div>
          </div>
          <div
            class="add-poc-step-editor"
            :class="{ 'index-hidden': curStep !== 2 }"
            v-show="curStep === 2"
          >
            <editor
              :value="initPocFileContent"
              v-if="true"
              ref="editor"
              style="line-height: 1.5"
            ></editor>
          </div>

          <!-- <div class="add-poc-step-test exp-step" v-show="curStep === 3 && vulInfo.hasExp">
            <div>
              <el-tabs v-model="testStepsValueExp" type="card" closable @tab-remove="removeTab">
                <el-tab-pane
                  v-for="(item, index) in testStepsExp"
                  :key="item.name"
                  :label="item.title"
                  :name="item.name"
                >
                  <div class="poc-test-tab-main">
                    <el-collapse v-model="item.activeNames">
                      <el-collapse-item :name="item.name + 1">
                        <template slot="title">
                          {{ $t('Request') }}
                        </template>
                        <div>
                          <el-form label-position="left" label-width="165px" :model="item.Request">
                            <el-form-item :label="$t('HTTP Request Method')" class="request-item">
                              <el-select v-model="item.Request.method">
                                <el-option :label="$t('GET')" value="GET"></el-option>
                                <el-option :label="$t('POST')" value="POST"></el-option>
                                <el-option :label="$t('PUT')" value="PUT"></el-option>
                                <el-option :label="$t('HEAD')" value="HEAD"></el-option>
                                <el-option :label="$t('PUSH')" value="PUSH"></el-option>
                                <el-option :label="$t('DELETE')" value="DELETE"></el-option>
                                <el-option :label="$t('OPTION')" value="OPTION"></el-option>
                                <el-option :label="$t('CUSTOM')" value="user_define"></el-option>
                              </el-select>
                              <el-input
                                v-model="item.Request.method"
                                v-if="item.Request.method === 'user_define'"
                              ></el-input>
                              <el-checkbox
                                :label="$t('Follow Redirect')"
                                v-model="item.Request.follow_redirect"
                              ></el-checkbox>
                            </el-form-item>
                            <el-form-item :label="$t('URL')">
                              <el-input v-model="item.Request.uri"></el-input>
                            </el-form-item>
                            <el-form-item :label="$t('Header')">
                              <el-input type="textarea" v-model="item.Request.header"></el-input>
                            </el-form-item>
                            <el-form-item :label="'Post Data'">
                              <el-input type="textarea" v-model="item.Request.data"></el-input>
                              <el-select
                                v-if="false"
                                v-model="item.Request.data_type"
                                style="margin-top: 20px"
                              >
                                <el-option :label="$t('Text')" value="text"></el-option>
                                <el-option :label="$t('Hexstring')" value="hexstring"></el-option>
                                <el-option :label="$t('Base64')" value="base64"></el-option>
                              </el-select>
                            </el-form-item>
                          </el-form>
                        </div>
                      </el-collapse-item>
                      <el-collapse-item :name="item.name + 2">
                        <template slot="title">
                          {{ $t('Response Test') }}
                        </template>
                        <div>
                          <div class="tree-tab clearfix">
                            <span class="test-item" style="font-weight: bold !important">{{
                              $t('Test Item')
                            }}</span>
                            <span class="variable" style="font-weight: bold !important">{{
                              $t('Variable')
                            }}</span>
                            <span class="operation" style="font-weight: bold !important">{{
                              $t('Operate')
                            }}</span>
                            <span class="value" style="font-weight: bold !important">{{
                              $t('Value')
                            }}</span>
                            <span class="remarks" style="font-weight: bold !important">{{
                              $t('Notes')
                            }}</span>
                          </div>
                          <el-tree
                            :props="defaultProps"
                            :data="item.ResponseTest"
                            node-key="id"
                            class="goby-tree"
                            default-expand-all
                            :expand-on-click-node="false"
                          >
                            <span class="custom-tree-node" slot-scope="{ node, data }">
                              <span>{{ data.type }}</span>
                              <el-popover
                                v-if="data.type === 'group'"
                                placement="right-start"
                                width="210"
                                trigger="click"
                                v-model="data.visible"
                              >
                                <div class="goby-tree-pop">
                                  <div v-if="data.id !== 0" @click="() => remove(node, data)">
                                    {{ $t('Delete') }}
                                  </div>
                                  <div @click="() => append(data, 0)">
                                    {{ $t('New Group') }}
                                  </div>
                                  <div @click="() => append(data, 1)">
                                    {{ $t('New Item') }}
                                  </div>
                                </div>
                                <i
                                  slot="reference"
                                  class="iconfont iconvertical goby-tree-menu"
                                ></i>
                              </el-popover>
                              <el-popover
                                v-if="data.type === 'item'"
                                placement="right-start"
                                width="210"
                                trigger="click"
                                v-model="data.visible"
                              >
                                <div class="goby-tree-pop">
                                  <div @click="() => remove(node, data)">
                                    {{ $t('Delete') }}
                                  </div>
                                </div>
                                <i
                                  slot="reference"
                                  class="iconfont iconvertical goby-tree-menu"
                                ></i>
                              </el-popover>
                              <el-select
                                class="tree-one-sel"
                                v-model="data.operation"
                                v-if="data.type === 'group'"
                              >
                                <el-option :label="$t('AND')" value="AND"></el-option>
                                <el-option :label="$t('OR')" value="OR"></el-option>
                              </el-select>
                              <el-select
                                class="tree-two-sel"
                                v-model="data.variable"
                                v-if="data.type === 'item'"
                              >
                                <el-option :label="$t('Code')" value="$code"></el-option>
                                <el-option :label="$t('Header')" value="$head"></el-option>
                                <el-option :label="$t('Body')" value="$body"></el-option>
                              </el-select>
                              <el-select
                                class="tree-three-sel"
                                v-model="data.operation"
                                v-if="data.type === 'item'"
                              >
                                <el-option :label="$t('Contains')" value="contains"></el-option>
                                <el-option
                                  :label="$t('Not Contains')"
                                  value="not contains"
                                ></el-option>
                                <el-option :label="$t('Regex')" value="regex"></el-option>
                                <el-option :label="$t('Start With')" value="start_with"></el-option>
                                <el-option :label="$t('End With')" value="end_with"></el-option>
                                <el-option :label="$t('==')" value="=="></el-option>
                                <el-option :label="$t('!=')" value="!="></el-option>
                                <el-option :label="$t('>')" value=">"></el-option>
                                <el-option :label="$t('<')" value="<"></el-option>
                                <el-option :label="$t('>=')" value=">="></el-option>
                                <el-option :label="$t('<=')" value="<="></el-option>
                              </el-select>
                              <el-input
                                class="tree-one-inp"
                                v-model="data.value"
                                v-if="data.type === 'item'"
                              ></el-input>
                              <el-input
                                class="tree-two-inp"
                                v-model="data.bz"
                                v-if="data.type === 'item'"
                              ></el-input>
                            </span>
                          </el-tree>
                        </div>
                      </el-collapse-item>
                      <el-collapse-item :name="item.name + 3">
                        <template slot="title">
                          {{ $t('Variables') }}
                        </template>
                        <div class="variables-item">
                          <div v-for="(itemVar, indexVar) in item.SetVariable" :key="indexVar">
                            <el-input
                              @click.native="addVarItem(indexVar, item.SetVariable.length, 2)"
                              v-model="itemVar.name"
                              :placeholder="$t('Name')"
                            ></el-input>
                            <el-input
                              @click.native="addVarItem(indexVar, item.SetVariable.length, 2)"
                              v-model="itemVar.value"
                              :placeholder="$t('Value')"
                            ></el-input>
                            <el-select
                              @click.native="addVarItem(indexVar, item.SetVariable.length, 2)"
                              v-model="itemVar.selOne"
                            >
                              <el-option :label="$t('lastbody')" value="lastbody"></el-option>
                              <el-option :label="$t('lastheader')" value="lastheader"></el-option>
                              <el-option :label="$t('statusline')" value="statusline"></el-option>
                            </el-select>
                            <el-select
                              @click.native="addVarItem(indexVar, item.SetVariable.length, 2)"
                              v-model="itemVar.selTwo"
                            >
                              <el-option value=""></el-option>
                              <el-option :label="$t('regex')" value="regex"></el-option>
                              <el-option :label="$t('sub')" value="sub"></el-option>
                            </el-select>
                            <i
                              class="iconfont iconguanbi"
                              @click="removeVarItem(indexVar, 2)"
                              v-if="indexVar !== item.SetVariable.length - 1"
                            ></i>
                          </div>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </el-tab-pane>
              </el-tabs>
              <div
                :style="{ left: addLeftDisExp + 'px' }"
                @click="addTab(testStepsValueExp, 2)"
                class="add-new-tab ac"
              >
                +
              </div>
              <el-select v-model="testMethodExp" class="test-method-sel">
                <el-option label="AND" value="AND"></el-option>
                <el-option label="OR" value="OR"></el-option>
              </el-select>
            </div>
          </div> -->
        </div>

        <div class="add-poc-save pos-rel">
          <el-button
            @click="savePocFn"
            type="primary"
            class="gb-dia-btn start"
            style="margin-top: 0"
            >保存</el-button
          >
          <el-checkbox v-model="save_and_publish">保存并发布</el-checkbox>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import '@/components/pocCustom/core_bmh/Poc'
import $ from 'jquery'
import SyntaxReferenceDia from '@/components/pocCustom/SyntaxReferenceDia.vue'
import ToDebugPocDialog from '@/components/pocCustom/ToDebugPocDialog.vue'
import editor from '@/components/pocCustom/editor.vue'
import '@/assets/css/iconfont.css'
import '@/assets/css/pocCustom/index.css'
import '@/assets/css/pocCustom/base.css'
import '@/assets/css/pocCustom/override-element-ui.css'
import '@/assets/css/pocCustom/theme.css'
import Simditor from 'simditor'
import 'simditor/styles/simditor.css'
import { mapState } from 'vuex'
import { isArray } from 'highcharts'
import { addPocCustom, infoPocCustom, updatePocCustom } from '@/api/apiConfig/poc'
import { singlePocScan } from '@/api/apiConfig/api'
export default {
  name: 'Vulnerability',
  props: {
    pocCustomId: {
      type: [Object, Number],
      require: false
    }
  },
  components: {
    SyntaxReferenceDia,
    editor,
    ToDebugPocDialog
  },
  data() {
    return {
      title: '新建PoC',
      ifNew: true,
      readySaved: false,
      TreeId: 0,
      editingItem: {},
      loading: false,
      editingNode: null,
      curStep: 0,
      singleIp: '',
      risk_count: 0,
      synReferenceDia: false,
      toDebugPocShow: false,
      advanced: false,
      singleScanData: {
        poc_id: '',
        ip: ''
      },
      vulInfo: {
        title: '',
        rules: '',
        level: '3',
        tag: '',
        dynamicTags: '[]',
        preTags: [
          'SQL Injection',
          'File Inclusion',
          'Disclosure of Sensitive Information',
          'File Upload',
          'RCE',
          'Xss',
          'CSRF'
        ],
        dynamicVulType: '[]',
        preVulType: [
          '命令执行',
          '代码执行',
          '文件写入',
          '文件上传',
          '后门',
          '默认口令',
          '弱口令',
          '未授权访问',
          'XXE漏洞',
          '权限绕过',
          'SQL注入',
          '文件读取',
          '文件下载',
          '文件包含',
          '文件删除',
          '目录遍历',
          '信息泄漏',
          '任意账户操作',
          'XSS漏洞',
          'SSRF漏洞',
          'CSRF漏洞',
          '其它'
        ],
        description: '',
        product: '',
        productHomepage: '',
        author: '',
        disclosureDate: '',
        source: '',
        harm: '',
        suggestion: '',
        editorHarm: '',
        editorSuggestion: '',
        hasExp: false,
        expParams: [],
        cveIds: '',
        cnnvd: '',
        cnvd: '',
        cvssScore: '',
        is0day: false,
        vulType: ''
      },
      save_and_publish: false,
      addLeftDis: 120,
      testMethod: 'AND',
      testStepsIndex: 1,
      testStepsValue: '1',
      testSteps: [
        {
          name: '1',
          title: 'Test 1',
          activeNames: ['11', '12', '13'],
          Request: {
            method: '',
            uri: '',
            follow_redirect: true,
            header: '',
            data_type: 'text',
            data: ''
          },
          ResponseTest: [
            {
              id: 0,
              type: 'group',
              visible: false,
              operation: 'AND',
              checks: [
                {
                  id: 1,
                  type: 'item',
                  visible: false,
                  variable: '$code',
                  operation: '==',
                  value: '200',
                  bz: ''
                }
              ]
            }
          ],
          SetVariable: [
            {
              name: '',
              value: '',
              selOne: 'lastbody',
              selTwo: 'regex'
            }
          ]
        }
      ],
      isAddFileName: false,
      saveFileName: {
        name: ''
      },
      isDebugPoc: false,
      debugHost: '',
      debugRes: '',
      id: 1000,
      itemId: 0,
      defaultProps: {
        children: 'checks',
        label: 'type'
      },
      initPocFileContent: '',
      showSubmitPocNot: false,
      pocAgreement: false,
      showAgreement: false,
      // exp 步骤
      addLeftDisExp: 120,
      testMethodExp: 'AND',
      testStepsIndexExp: 1,
      testStepsValueExp: '1',
      testStepsExp: [
        {
          name: '1',
          title: 'Test 1',
          activeNames: ['11', '12', '13'],
          Request: {
            method: '',
            uri: '',
            follow_redirect: true,
            header: '',
            data_type: 'text',
            data: ''
          },
          ResponseTest: [
            {
              id: 0,
              type: 'group',
              visible: false,
              operation: 'AND',
              checks: [
                {
                  id: 1,
                  type: 'item',
                  visible: false,
                  variable: '$code',
                  operation: '==',
                  value: '200',
                  bz: ''
                }
              ]
            }
          ],
          SetVariable: [
            {
              name: '',
              value: '',
              selOne: 'lastbody',
              selTwo: 'regex'
            }
          ]
        }
      ],
      vulType: [
        {
          value: '命令执行'
        },
        {
          value: '代码执行'
        },
        {
          value: '文件写入'
        },
        {
          value: '文件上传'
        },
        {
          value: '后门'
        },
        {
          value: '默认口令'
        },
        {
          value: '弱口令'
        },
        {
          value: '未授权访问'
        },
        {
          value: 'XXE漏洞'
        },
        {
          value: '权限绕过'
        },
        {
          value: 'SQL注入'
        },
        {
          value: '文件读取'
        },
        {
          value: '文件下载'
        },
        {
          value: '文件包含'
        },
        {
          value: '文件删除'
        },
        {
          value: '目录遍历'
        },
        {
          value: '信息泄漏'
        },
        {
          value: '任意账户操作'
        },
        {
          value: 'XSS漏洞'
        },
        {
          value: 'SSRF漏洞'
        },
        {
          value: 'CSRF漏洞'
        },
        {
          value: '其它'
        }
      ],
      inputProxy: ''
    }
  },

  watch: {
    // pocCustomId: {
    //   handler(newVal, oldVal) {
    //     this.init(newVal)
    //   }
    // },
    'vulInfo.title': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.rules': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.level': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.tag': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.dynamicTags': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.dynamicVulType': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.description': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.product': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.productHomepage': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.author': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.disclosureDate': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.source': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.harm': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.suggestion': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.hasExp': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.expParams': {
      handler() {
        this.visSyncEditor()
      },
      deep: true
    },
    'vulInfo.is0day': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.vulType': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.cveIds': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.cnnvd': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.cnvd': {
      handler() {
        this.visSyncEditor()
      }
    },
    'vulInfo.cvssScore': {
      handler() {
        this.visSyncEditor()
      }
    },
    testSteps: {
      handler(newVal, oldVal) {
        let middleValue = newVal
        middleValue &&
          middleValue.length &&
          middleValue.forEach((res) => {
            if (res && res.ResponseTest) {
              let test = JSON.stringify(res.ResponseTest)
              if (test[0] && test[0] == '[' && test[1] && test[1] == '[') {
                res.ResponseTest = res.ResponseTest[0]
              }
            }
          })
        this.testSteps = middleValue
        this.visSyncEditor()
      },
      deep: true
    },
    scanSteps() {
      this.visSyncEditor()
    },
    scanStepsExp() {
      this.visSyncEditor()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    dynamicVulTypeJson() {
      try {
        const parsed = JSON.parse(this.vulInfo.dynamicVulType)
        if (Array.isArray(parsed)) {
          return parsed.filter(Boolean)
        }
      } catch (e) {
        // ignore error
      }
      return []
    },
    dynamicTagsJson() {
      return JSON.parse(this.vulInfo.dynamicTags)
    },
    // poc扫描步骤
    scanSteps() {
      let steps = [this.testMethod]
      let testStepsArr = JSON.parse(JSON.stringify(this.testSteps))
      testStepsArr &&
        testStepsArr.length &&
        testStepsArr.forEach((val, key) => {
          val.ResponseTest = val.ResponseTest[0]
          steps.push(val)
        })
      return JSON.stringify(steps)
    },
    // poc exp步骤
    scanStepsExp() {
      let steps = [this.testMethodExp]
      let testStepsArr = JSON.parse(JSON.stringify(this.testStepsExp))
      testStepsArr &&
        testStepsArr.length &&
        testStepsArr.forEach((val, key) => {
          delete val.name
          delete val.title
          delete val.activeNames
          val.ResponseTest = val.ResponseTest[0]
          val.SetVariableArr = []
          val.SetVariable &&
            val.SetVariable.length &&
            val.SetVariable.forEach((v, k) => {
              if (v.name || v.value) {
                let item = `${v.name}|${v.selOne}|${v.selTwo}|${v.value}`
                val.SetVariableArr.push(item)
              }
            })
          val.SetVariable = val.SetVariableArr
          delete val.SetVariableArr
          steps.push(val)
        })
      return JSON.stringify(steps)
    }
  },
  methods: {
    changeStep(val) {
      this.curStep = val
    },
    judgeCheck(Data) {
      let Deep, T, F
      for (F = Data.length; F; ) {
        T = Data[--F]
        if (T.id == '[object Undefined]' || !T.id) {
          T.id = toString(Number(this.itemId) + 1)
        }
        T.visible = false
        if (T.checks && T.checks.length) {
          Deep = judgeCheck(T.checks)
          if (Deep) {
            return Deep
          }
        }
      }
    },
    //单ip扫描
    singleIpScan() {
      if (this.singleIp.trim()) {
        if (!this.singleScanData.poc_id) {
          this.$message.error('请先发布后再扫描')
          return
        }
        if (this.risk_count === 0) {
          this.$message.error('poc风险资产数为0,无法执行扫描')
          return
        }
        this.singleScanData.ip = this.singleIp
        let obj = Object.assign({}, this.singleScanData)
        console.log('@@@@', obj)

        this.loading = true
        singlePocScan(obj)
          .then((res) => {
            console.log('单ip扫描', res)
            this.toDebugPocShow = true
            if (res.data.code == '12009') {
              this.$message.error(`${res.data.message || '无法扫描'}`)
            } else if (res.code === 0) {
              this.$refs.ToDebugDialog.getReadyData(res.data)
              this.singleIp = ''
              this.$message.success(`${res.data.message || '扫描成功'}`)
            }
          })
          .catch((err) => {
            this.$message.error(`${err.data.message || '无法扫描'}`)
          })
          .finally(() => {
            this.loading = false
          })
      } else {
        this.$message.error('Ip不能为空!')
      }
    },
    isAdvanced() {
      this.advanced = !this.advanced
    },
    toShowRefDia() {
      this.synReferenceDia = true
    },
    reset() {
      this.vulInfo.title = ''
      this.vulInfo.rules = ''
      this.vulInfo.level = '3'
      this.vulInfo.tag = ''
      this.singleIp = ''
      this.vulInfo.dynamicTags = '[]'
      this.vulInfo.preTags = [
        'SQL Injection',
        'File Inclusion',
        'Disclosure of Sensitive Information',
        'File Upload',
        'RCE',
        'Xss',
        'CSRF'
      ]
      this.vulInfo.description.setValue('')
      this.vulInfo.cveIds = ''
      this.vulInfo.product = ''
      this.vulInfo.productHomepage = ''
      this.vulInfo.author = this.userEmail
      this.vulInfo.disclosureDate = ''
      this.vulInfo.source = ''
      this.vulInfo.editorHarm.setValue('')
      this.vulInfo.editorSuggestion.setValue('')
      this.vulInfo.hasExp = false
      this.vulInfo.expParams = []
      this.vulInfo.dynamicVulType = '[]'
      this.testSteps = [
        {
          name: '1',
          title: 'Test 1',
          activeNames: ['11', '12', '13'],
          Request: {
            method: 'GET',
            uri: '',
            follow_redirect: true,
            header: '',
            data_type: 'text',
            data: ''
          },
          ResponseTest: [
            {
              id: 0,
              type: 'group',
              visible: false,
              operation: 'AND',
              checks: [
                {
                  id: 1,
                  type: 'item',
                  visible: false,
                  variable: '$code',
                  operation: '==',
                  value: '200',
                  bz: ''
                }
              ]
            }
          ],
          SetVariable: [
            {
              name: '',
              value: '',
              selOne: 'lastbody',
              selTwo: 'regex'
            }
          ]
        }
      ]
      this.testStepsExp = [
        {
          name: '1',
          title: 'Test 1',
          activeNames: ['11', '12', '13'],
          Request: {
            method: 'GET',
            uri: '',
            follow_redirect: true,
            header: '',
            data_type: 'text',
            data: ''
          },
          ResponseTest: [
            {
              id: 0,
              type: 'group',
              visible: false,
              operation: 'AND',
              checks: [
                {
                  id: 1,
                  type: 'item',
                  visible: false,
                  variable: '$code',
                  operation: '==',
                  value: '200',
                  bz: ''
                }
              ]
            }
          ],
          SetVariable: [
            {
              name: '',
              value: '',
              selOne: 'lastbody',
              selTwo: 'regex'
            }
          ]
        }
      ]
      let params = {
        Name: this.vulInfo.title,
        Level: this.vulInfo.level,
        FofaQuery: this.vulInfo.rules,
        // Description: this.vulInfo.description,
        Product: this.vulInfo.product,
        Homepage: this.vulInfo.productHomepage,
        Author: this.vulInfo.author,
        DisclosureDate: this.vulInfo.disclosureDate,
        Impact: '',
        Recommendation: '',
        References: this.vulInfo.source,
        Tags: this.dynamicTagsJson,
        ScanSteps: this.calScenteps(this.scanSteps),
        HasExp: this.vulInfo.hasExp,
        ExpParams: this.vulInfo.expParams,
        ExploitSteps: this.calScenteps(this.scanStepsExp),
        CVEIDs: this.vulInfo.cveIds.split(','),
        CNNVD: this.vulInfo.cnnvd,
        CNVD: this.vulInfo.cnvd,
        CVSSScore: this.vulInfo.cvssScore,
        Is0day: this.vulInfo.is0day,
        VulType: this.dynamicVulTypeJson
      }
      this.addLeftDis = 120
      this.addLeftDisExp = 120
      this.initPocFileContent = syncEditor(0, params)
    },
    //初始化编辑器
    async initEditor() {
      this.vulInfo.editorHarm = new Simditor({
        textarea: '#editor-harm',
        toolbar: [
          'title',
          'bold',
          'italic',
          'fontScale',
          'color',
          'blockquote',
          'code',
          'link',
          'hr',
          'indent',
          'outdent',
          'alignment'
        ]
        //optional options
      })
      this.vulInfo.editorSuggestion = new Simditor({
        textarea: '#editor-suggestion',
        toolbar: [
          'title',
          'bold',
          'italic',
          'fontScale',
          'color',
          'blockquote',
          'code',
          'link',
          'hr',
          'indent',
          'outdent',
          'alignment'
        ]
        //optional options
      })
      this.vulInfo.description = new Simditor({
        textarea: '#editor-description',
        toolbar: [
          'title',
          'bold',
          'italic',
          'fontScale',
          'color',
          'blockquote',
          'code',
          'link',
          'hr',
          'indent',
          'outdent',
          'alignment'
        ]
        //optional options
      })
      if (this.initPocJson) {
        console.log('进入json')
        // 检测是go还是json格式
        let pocJson = ''
        if (window.FileName) {
          if (window.FileName.indexOf('.go') !== -1) {
            pocJson = JSON.parse(syncVis(1, this.initPocFileContent))
          }
          if (window.FileName.indexOf('.json') !== -1) {
            pocJson = JSON.parse(this.initPocJson)
          }
        } else {
          pocJson = JSON.parse(syncVis(1, this.initPocFileContent))
        }
        this.vulInfo.title = pocJson.Name
        this.vulInfo.rules = pocJson.FofaQuery
        this.vulInfo.level = pocJson.Level
        this.vulInfo.dynamicTags = JSON.stringify(pocJson.Tags) || '[]'
        this.vulInfo.description.setValue(pocJson.Description)
        this.vulInfo.product = pocJson.Product
        this.vulInfo.productHomepage = pocJson.Homepage
        this.vulInfo.author = pocJson.Author
        this.vulInfo.disclosureDate = pocJson.DisclosureDate
        this.vulInfo.source = to_parseRefer(pocJson.References) || ''
        this.vulInfo.editorHarm.setValue(pocJson.Impact)
        this.vulInfo.editorSuggestion.setValue(pocJson.Recommendation)
        this.addLeftDis = this.testSteps.length * 120.6
        this.vulInfo.hasExp = pocJson.HasExp
        this.vulInfo.expParams = pocJson.ExpParams ? pocJson.ExpParams : []
        this.vulInfo.cveIds = pocJson.CVE
        this.vulInfo.cnnvd = pocJson.CNNVD
        this.vulInfo.cnvd = pocJson.CNVD
        this.vulInfo.cvssScore = pocJson.CVSSScore
        this.vulInfo.is0day = pocJson.Is0day
      } else {
        console.log('进入else', this.vulInfo)
        let params = {
          Name: this.vulInfo.title,
          Level: this.vulInfo.level,
          FofaQuery: this.vulInfo.rules,
          Description: '',
          Product: this.vulInfo.product,
          Homepage: this.vulInfo.productHomepage,
          Author: this.vulInfo.author,
          DisclosureDate: this.vulInfo.disclosureDate,
          Impact: '',
          Recommendation: '',
          References: this.vulInfo.source,
          Tags: this.dynamicTagsJson,
          ScanSteps: this.calScenteps(this.scanSteps),
          HasExp: this.vulInfo.hasExp,
          ExpParams: this.vulInfo.expParams,
          ExploitSteps: this.calScenteps(this.scanStepsExp),
          CVEIDs: this.vulInfo.cveIds.split(','),
          CNNVD: this.vulInfo.cnnvd,
          CNVD: this.vulInfo.cnvd,
          CVSSScore: this.vulInfo.cvssScore,
          Is0day: this.vulInfo.is0day,
          VulType: this.dynamicVulTypeJson
        }
        this.initPocFileContent = syncEditor(0, params)
      }

      // 监听编辑器值的变化
      this.vulInfo.editorHarm.on('valuechanged', (e, src) => this.visSyncEditor())
      this.vulInfo.editorSuggestion.on('valuechanged', (e, src) => this.visSyncEditor())
      this.vulInfo.description.on('valuechanged', (e, src) => this.visSyncEditor())
    },
    append(data, type) {
      let newChild
      if (type === 0) {
        newChild = {
          id: this.TreeId++,
          type: 'group',
          visible: false,
          operation: 'AND',
          checks: []
        }
      } else {
        newChild = {
          id: this.TreeId++,
          type: 'item',
          visible: false,
          variable: '$body',
          operation: 'contains',
          value: 'test',
          bz: ''
        }
      }
      if (!data.checks) {
        this.$set(data, 'checks', [])
      }
      data.checks.push(newChild)
      data.visible = false
    },
    remove(node, data) {
      if (data.id !== 0) {
        let parent = node.parent
        let children = parent.data.checks || parent.data
        let index = children.findIndex((d) => d.id === data.id)
        children.splice(index, 1)
      }
      data.visible = false
    },

    async init(pocId, companyId) {
      this.title = '新建PoC'
      this.initEditor()
      this.singleScanData.poc_id = ''
      if (pocId) {
        this.title = '编辑PoC'
        // 根据ID获取POC详情

        const res = await infoPocCustom({
          id: pocId,
          operate_company_id: companyId
        })
        console.log('poc详情', res)

        let data = res.data
        console.log('获取到的详情数据===》', data)

        this.TreeId = this.id
        this.risk_count = data.risk_count || 0
        this.save_and_publish = data.save_and_publish || false
        this.singleScanData.poc_id = data.poc_id
        let scanSteps = JSON.parse(data.scan_steps)
        scanSteps &&
          scanSteps.length &&
          scanSteps.map((item, index) => {
            if (index != 0 && item) {
              item.SetVariable =
                item.SetVariable &&
                item.SetVariable.length &&
                item.SetVariable.map((itemInner) => {
                  return `${itemInner.name}|${itemInner.selOne}|${itemInner.selTwo}|${itemInner.value}`
                }).filter((item) => item)
            }
          })
        const setVisibleFalseRecursively = (nodes) => {
          if (!Array.isArray(nodes)) return
          nodes.forEach((node) => {
            if (node) {
              node.visible = false
              if (node.checks && Array.isArray(node.checks)) {
                setVisibleFalseRecursively(node.checks)
              }
            }
          })
        }
        scanSteps &&
          scanSteps.length &&
          scanSteps.forEach((step, index) => {
            if (index !== 0 && typeof step === 'object' && step.ResponseTest) {
              const responseTest = Array.isArray(step.ResponseTest)
                ? step.ResponseTest
                : [step.ResponseTest]
              setVisibleFalseRecursively(responseTest)
            }
          })
        let json = {
          Name: data.name,
          Description: data.description || '',
          Product: data.product,
          Homepage: data.homepage,
          DisclosureDate: data.disclosure_date || '',
          Author: data.author,
          FofaQuery: data.fofa_query,
          GobyQuery: '',
          Level: String(data.level),
          Impact: data.impact || '',
          VulType: data.vul_type || [],
          CVEIDs: data.cve,
          CNNVD: [],
          CNVD: [],
          CVSSScore: '',
          Is0day: Boolean(data.is0day),
          Recommendation: data.recommendation || '',
          Translation: {
            CN: {
              Name: 'SINDOH N600 存在默认口令漏洞 (CNVD-2021-45733)',
              Description:
                '<p>SINDOH N600是以用户为创新智能的打印机系统。</p><p>SINDOH N600系统存在默认口令漏洞，攻击者可通过默认口令：12345678控制整个平台，使用管理员权限操作核心的功能</p>',
              Recommendation:
                '<p>1、修改默认口令，密码最好包含大小写字母、数字和特殊字符等，且位数大于8位。</p><p>2、如非必要，禁止公网访问该系统。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>',
              Impact:
                '<p>SINDOH N600系统存在默认口令漏洞，攻击者可通过默认口令漏洞控制整个平台，使用管理员权限操作核心的功能。<p>',
              VulType: ['默认口令']
            }
          },
          References: [data.references || ''],
          HasExp: false,
          ExpParams: [],
          ScanSteps: scanSteps,
          ExploitSteps: [
            'AND',
            {
              Request: {
                method: 'GET',
                uri: '',
                follow_redirect: true,
                header: {},
                data_type: 'text',
                data: ''
              },
              ResponseTest: {
                type: 'group',
                operation: 'AND',
                checks: [
                  {
                    type: 'item',
                    variable: '$code',
                    operation: '==',
                    value: '200',
                    bz: ''
                  }
                ]
              },
              SetVariable: []
            }
          ],
          AttackSurfaces: {
            Application: null,
            Support: null,
            Service: null,
            System: null,
            Hardware: null
          },
          Tags: []
        }

        this.editorSyncVis('`' + JSON.stringify(json) + '`', true)
        this.isAdvanced()
        this.ifNew = false
        this.id = pocId
        // this.pocCustomId = null
      } else {
        this.ifNew = true
        this.reset()
      }
    },
    // 可视化同步到编辑器
    visSyncEditor() {
      let tags = this.dynamicTagsJson
      let vulType = this.dynamicVulTypeJson
      let nArr = vulType.reduce((total, prev) => {
        if (!tags.includes(prev)) {
          total.push(prev)
        }
        return total
      }, [])
      if (nArr.length > 0) {
        this.$set(this.vulInfo, 'dynamicTags', JSON.stringify(tags.concat(nArr)))
      }
      let params = {
        Name: this.vulInfo.title,
        Level: this.vulInfo.level,
        FofaQuery: this.vulInfo.rules,
        Description: this.vulInfo.description.getValue(),
        Product: this.vulInfo.product,
        Homepage: this.vulInfo.productHomepage,
        Author: this.vulInfo.author,
        DisclosureDate: this.vulInfo.disclosureDate,
        Impact: this.vulInfo.editorHarm.getValue(),
        Recommendation: this.vulInfo.editorSuggestion.getValue(),
        References: this.vulInfo.source,
        Tags: [],
        ScanSteps: this.calScenteps(this.scanSteps),
        initPocFileContent: this.initPocFileContent,
        HasExp: this.vulInfo.hasExp,
        ExpParams: this.vulInfo.expParams,
        ExploitSteps: this.calScenteps(this.scanStepsExp),
        CVEIDs: this.vulInfo.cveIds.split(','),
        CNNVD: this.vulInfo.cnnvd,
        CNVD: this.vulInfo.cnvd,
        CVSSScore: this.vulInfo.cvssScore,
        Is0day: this.vulInfo.is0day,
        VulType: this.dynamicVulTypeJson
      }
      this.initPocFileContent = syncEditor(1, params)
    },
    async savePocFn() {
      let content = this.initPocFileContent
      let s = content.indexOf('`{')
      let e = content.indexOf('}`')
      let jsonStr = content.substring(s + 1, e + 1)
      let tempJson = ''
      try {
        tempJson = JSON.parse(jsonStr)
      } catch (err) {
        let errPosition = err.message.replace(/[^0-9]/gi, '')
        let errField = jsonStr.substr(0, errPosition) || jsonStr
        this.$message.error(`JSON格式错误: ${errField}`)
        return false
      }
      if (!tempJson) return

      Object.defineProperty(tempJson, 'editor', {
        value: this.initPocFileContent,
        configurable: true,
        enumerable: true,
        writable: true
      })
      let arr0 = tempJson.ScanSteps[0]
      tempJson.ScanSteps = JSON.parse(JSON.stringify(this.testSteps))
      tempJson.ScanSteps.unshift(arr0)

      let JsonQuery = {
        name: tempJson.Name, // POC名称
        fofa_query: tempJson.FofaQuery, // 查询规则
        level: Number(tempJson.Level), // 等级
        product: tempJson.Product, // 产品
        homepage: tempJson.Homepage, // 产品主页
        author: tempJson.Author, // 作者
        disclosure_date: tempJson.DisclosureDate, //发现日期
        cve: Array.isArray(tempJson.CVEIDs) ? tempJson.CVEIDs : tempJson.CVEIDs.split(','), // CVE编号
        references: isArray(tempJson.References) ? tempJson.References[0] : tempJson.References, // 来源
        is0day: tempJson.Is0day, // 是否是0day
        vul_type: Array.isArray(tempJson.VulType) ? tempJson.VulType : tempJson.VulType.split(','), // 漏洞类型
        poc_id: this.singleScanData.poc_id || 0, // poc_id
        description: tempJson.Description, // POC描述
        impact: tempJson.Impact, // 漏洞危害
        recommendation: tempJson.Recommendation, // 修复建议
        editor: tempJson.editor, //编辑器部分
        scan_steps: JSON.stringify(tempJson.ScanSteps), // 测试内容
        // disclosure_date: tempJson.DisclosureDate
        save_and_publish: this.save_and_publish,
        operate_company_id: this.currentCompany
      }
      console.log('@@@@@', JsonQuery)

      if (!this.ifNew) JsonQuery.id = this.id
      if (JsonQuery.name && JsonQuery.fofa_query) {
        if (JsonQuery.references && !this.isURL(JsonQuery.references)) {
          this.$message.error('请正确填写来源地址')
          return false
        }
        const res = (await JsonQuery.id) ? updatePocCustom(JsonQuery) : addPocCustom(JsonQuery)
        res
          .then((res) => {
            console.log('addPocCustom', res)

            if (res.code == 0) {
              this.$message.success('保存成功')
              this.readySaved = true
              this.$emit('close')
            }
            // else {
            //   this.$message.error(res.data.message)
            // }
          })
          .catch((err) => {
            console.log(err)
            this.$message.error(err.data.message)
          })
      } else {
        this.$message.error('请填写PoC基本信息')
      }
    },
    //移除标签
    removeTab(targetName) {
      // curStep等于1时，扫描测试，等于3时，exp测试
      let testSteps = this.curStep === 1 ? 'testSteps' : 'testStepsExp',
        addLeftDis = this.curStep === 1 ? 'addLeftDis' : 'addLeftDisExp',
        testStepsValue = this.curStep === 1 ? 'testStepsValue' : 'testStepsValueExp',
        domStr = this.curStep === 1 ? '.scan-step .el-tabs__nav' : '.exp-step .el-tabs__nav'
      if (this[testSteps].length > 1) {
        let tabs = this[testSteps]
        let activeName = this[testStepsValue]
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if (tab.name === targetName) {
              let nextTab = tabs[index + 1] || tabs[index - 1]
              if (nextTab) {
                activeName = nextTab.name
              }
            }
          })
        }

        this[testStepsValue] = activeName
        this[testSteps] = tabs.filter((tab) => tab.name !== targetName)
        this[addLeftDis] = $(domStr).width() - 120
      }
    },
    //添加标签
    addTab(targetName, type) {
      // type等于1时，扫描测试，等于2时，exp测试
      let testSteps = type === 1 ? 'testSteps' : 'testStepsExp',
        testStepsValue = type === 1 ? 'testStepsValue' : 'testStepsValueExp',
        testStepsIndex = type === 1 ? 'testStepsIndex' : 'testStepsIndexExp',
        addLeftDis = type === 1 ? 'addLeftDis' : 'addLeftDisExp',
        domStr = type === 1 ? '.scan-step .el-tabs__nav' : '.exp-step .el-tabs__nav'
      if (this[testSteps].length < 9) {
        this[testStepsIndex] = Number(this[testSteps][this[testSteps].length - 1].name)
        let newTabName = ++this[testStepsIndex] + ''
        let activeNames = [newTabName + '1', newTabName + '2', newTabName + '3']
        this[testSteps].push({
          name: newTabName,
          title: 'Test ' + newTabName,
          activeNames: activeNames,
          Request: {
            method: 'GET',
            uri: '',
            follow_redirect: true,
            header: '',
            data_type: 'text',
            data: ''
          },
          ResponseTest: [
            {
              id: 0,
              type: 'group',
              visible: false,
              operation: 'AND',
              checks: [
                {
                  id: 1,
                  type: 'item',
                  visible: false,
                  variable: '$code',
                  operation: '==',
                  value: '200',
                  bz: ''
                }
              ]
            }
          ],
          SetVariable: [
            {
              name: '',
              value: '',
              selOne: 'lastbody',
              selTwo: 'regex'
            }
          ]
        })
        this[testStepsValue] = newTabName
        console.log('Before update - domStr width:', $(domStr).width())
        this[addLeftDis] = $(domStr).width() + 120
        console.log('After update - addLeftDis:', this[addLeftDis])
      }
    },
    addVarItem(index, len, type) {
      // type等于1时，扫描测试，等于2时，exp测试
      let testSteps = type === 1 ? 'testSteps' : 'testStepsExp',
        testStepsValue = type === 1 ? 'testStepsValue' : 'testStepsValueExp'
      if (index === len - 1) {
        // console.log('if1', this[testSteps])
        // console.log('if2', this[testStepsValue])
        this[testSteps].forEach((val, key) => {
          if (key + 1 === Number(this[testStepsValue])) {
            this[testSteps][key].SetVariable.push({
              name: '',
              value: '',
              selOne: 'lastbody',
              selTwo: 'regex'
            })
          }
        })
      }
    },
    removeVarItem(index, type) {
      // type等于1时，扫描测试，等于2时，exp测试
      let testSteps = type === 1 ? 'testSteps' : 'testStepsExp',
        testStepsValue = type === 1 ? 'testStepsValue' : 'testStepsValueExp'
      this[testSteps].forEach((val, key) => {
        if (key + 1 === Number(this[testStepsValue])) {
          this[testSteps][key].SetVariable.splice(index, 1)
        }
      })
    },
    // 添加标签
    toAddVulType(val) {
      let vulType = JSON.parse(this.vulInfo.dynamicVulType)
      if (vulType.indexOf(val) === -1) {
        vulType.push(val)
      } else {
        this.$message.warning('这个标签已经存在')
      }

      // this.vulInfo.vulType = ''
      // this.vulInfo.dynamicVulType = JSON.stringify(vulType);
      this.$set(this.vulInfo, 'dynamicVulType', JSON.stringify(vulType))
    },
    // 删除标签
    toDelVulType(index) {
      let vulType = JSON.parse(this.vulInfo.dynamicVulType)
      vulType.splice(index, 1)
      this.vulInfo.dynamicVulType = JSON.stringify(vulType)
    },
    // 计算测试步骤
    calScenteps(scanSteps) {
      let scanStepsNew = JSON.parse(JSON.stringify(JSON.parse(scanSteps)))
      scanStepsNew &&
        scanStepsNew.length &&
        scanStepsNew.map((item, index) => {
          if (index != 0 && item) {
            item.SetVariable =
              item.SetVariable &&
              item.SetVariable.length &&
              item.SetVariable.map((itemInner) => {
                if (itemInner.name || itemInner.value) {
                  return `${itemInner.name}|${itemInner.selOne}|${itemInner.selTwo}|${itemInner.value}`
                }
                return ''
              }).filter((item) => item)
            if (!item.Request.header || !this.isJSON_test(item.Request.header)) {
              item.Request.header = {}
            } else {
              item.Request.header = JSON.parse(item.Request.header || '{}')
            }
          }
        })
      return scanStepsNew
    },
    //编辑器同步到可视化
    async editorSyncVis(content, ifInit) {
      if (!ifInit) {
        this.initPocFileContent = content
      }
      let s = content.indexOf('`{')
      let e = content.indexOf('}`')
      let tempJson = JSON.parse(content.substring(s + 1, e + 1))
      // tempJson.ScanSteps = await this.judgeJSON(tempJson.ScanSteps)
      // tempJson.ScanSteps = this.handleScanSteps(tempJson.ScanSteps)
      tempJson.ScanSteps &&
        tempJson.ScanSteps.length &&
        tempJson.ScanSteps.forEach((item, index) => {
          if (index !== 0 && item.ResponseTest && item.ResponseTest.length) {
            let types = typeof item.ResponseTest
            if (types == 'Object' || types == 'object') {
              item.ResponseTest = [item.ResponseTest]
            }
            item.ResponseTest.forEach((childItem) => {
              childItem.forEach((item) => {
                if (item.type == 'group') {
                  item.id = 0
                }
              })
              if (childItem.id == '[object Undefined]' || !childItem.id) {
                childItem.id = this.itemId + 1
              }
              if (childItem.checks && childItem.checks.length) {
                childItem = this.judgeCheck(childItem)
              }
            })
          }
          if (index !== 0 && item.Request) {
            if (item.Request.header && this.isObj_test(item.Request.header)) {
              item.Request.header = JSON.stringify(item.Request.header)
            }
          }
        })
      this.vulInfo.title = tempJson.Name
      this.vulInfo.rules = tempJson.FofaQuery
      this.vulInfo.level = tempJson?.Level
      this.vulInfo.dynamicTags = JSON.stringify(tempJson?.Tags) || '[]'
      this.vulInfo.description.setValue(tempJson?.Description)
      this.vulInfo.product = tempJson?.Product
      this.vulInfo.productHomepage = tempJson?.Homepage
      this.vulInfo.author = tempJson?.Author
      this.vulInfo.disclosureDate = tempJson?.DisclosureDate
      this.vulInfo.source = to_parseRefer(tempJson?.References)
      this.vulInfo.editorHarm.setValue(tempJson?.Impact)
      this.vulInfo.editorSuggestion.setValue(tempJson?.Recommendation)
      this.vulInfo.hasExp = tempJson?.HasExp
      this.vulInfo.expParams = tempJson?.ExpParams ? tempJson?.ExpParams : []
      this.vulInfo.cveIds = tempJson?.CVEIDs.join(',')
      this.vulInfo.cnnvd = tempJson?.CNNVD
      this.vulInfo.cnvd = tempJson?.CNVD
      this.vulInfo.cvssScore = tempJson?.CVSSScore
      this.vulInfo.is0day = tempJson?.Is0day
      this.vulInfo.dynamicTags = JSON.stringify(tempJson?.VulType) || '[]'
      this.vulInfo.dynamicVulType = JSON.stringify(tempJson?.VulType) || '[]'

      this.syncTest(tempJson)
      this.$nextTick(() => {
        this.addLeftDis = this.testSteps.length * 120.6
      })
      this.testStepsValue = this.testSteps[0]?.name ?? ''
      this.$nextTick(() => {
        this.addLeftDisExp = this.testStepsExp.length * 120.6
      })
      this.testStepsValueExp = this.testStepsExp[0]?.name ?? ''
    },
    isObj_test(obj) {
      if (typeof obj == 'object') {
        try {
          var obj = JSON.stringify(obj)
          return true
        } catch (e) {
          return false
        }
      }
    },
    isURL(s) {
      return /^http[s]?:\/\/.*/.test(s)
    },
    isJSON_test(str) {
      if (typeof str == 'string') {
        try {
          var obj = JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
    },
    async syncTest(content) {
      let types = typeof content.ScanSteps[0]
      if (types == 'Object' || types == 'Array' || types == 'object' || types == 'array') {
        this.testMethod = 'AND'
        content.ScanSteps.unshift('AND')
      } else {
        this.testMethod = content.ScanSteps[0]
      }

      this.testSteps = []
      content.ScanSteps &&
        content.ScanSteps.length &&
        content.ScanSteps.forEach((val, key) => {
          if (key !== 0) {
            val.name = String(key)
            val.title = 'Test ' + key
            val.activeNames = [key + '1', key + '2', key + '3']
            if (!val.Request) {
              val.Request = {
                method: '',
                uri: '',
                follow_redirect: true,
                header: '',
                data_type: 'text',
                data: ''
              }
            }
            val.Request.header = header_json(val.Request?.header || '')
            let arr1 = []
            if (!val.ResponseTest) {
              val.ResponseTest = {
                type: 'group',
                operation: 'AND',
                checks: [
                  {
                    type: 'item',
                    variable: '$code',
                    operation: '==',
                    value: '200',
                    bz: ''
                  }
                ]
              }
            }
            arr1.push(val?.ResponseTest)
            this.itemId = 0
            this.handleScanSteps(arr1)
            val.ResponseTest = arr1
            if (!val.SetVariable || val.SetVariable.length === 0) {
              let obj = {
                name: '',
                value: '',
                selOne: 'lastbody',
                selTwo: 'regex'
              }
              val.SetVariable = []
              val.SetVariable.push(obj)
            } else {
              val.SetVariable &&
                val.SetVariable.length &&
                val.SetVariable.forEach((v, k) => {
                  let scanStepsArr = v.split('|')
                  console.log(scanStepsArr, 'scanStepsArr')
                  let obj = {}
                  obj.name = scanStepsArr[0]
                  obj.selOne = scanStepsArr[1]
                  obj.selTwo = scanStepsArr[2]
                  obj.value = scanStepsArr[3]
                  val.SetVariable[k] = obj
                })
            }
            this.testSteps.push(val)
          }
        })

      console.log('testSteps', this.testSteps)
      // this.testSteps = this.handleScanSteps(this.testSteps)
      // this.testSteps = await this.judgeJSON(this.testSteps)
      this.testSteps &&
        this.testSteps.length &&
        this.testSteps.forEach((item, index) => {
          if (index !== 0 && item.ResponseTest && item.ResponseTest.length) {
            let types = typeof item.ResponseTest
            if (types == 'Object' || types == 'object') {
              item.ResponseTest = [item.ResponseTest]
            }
            item.ResponseTest.forEach((childItem) => {
              if (childItem.id == '[object Undefined]' || !childItem.id) {
                childItem.id = this.itemId + 1
              }
              if (childItem.checks && childItem.checks.length) {
                childItem = this.judgeCheck(childItem)
              }
            })
          }
        })
      this.testMethodExp = (content.ExploitSteps && content.ExploitSteps[0]) || ''
      this.testStepsExp = []
      content.ExploitSteps &&
        content.ExploitSteps.length &&
        content.ExploitSteps.forEach((val, key) => {
          if (key !== 0) {
            val.name = String(key)
            val.title = 'Test ' + key
            val.activeNames = [key + '1', key + '2', key + '3']
            val.Request.header = header_json(val.Request.header)
            let arr1 = []
            arr1.push(val.ResponseTest)
            this.itemId = 0
            this.handleScanSteps(arr1)
            val.ResponseTest = arr1
            if (val.SetVariable.length === 0) {
              let obj = {
                name: '',
                value: '',
                selOne: 'lastbody',
                selTwo: 'regex'
              }
              val.SetVariable.push(obj)
            } else {
              val.SetVariable &&
                val.SetVariable.length &&
                val.SetVariable.forEach((v, k) => {
                  let obj = {}
                  obj.name = v.name
                  obj.selOne = v.selOne
                  obj.selTwo = v.selTwo
                  obj.value = v.value
                  val.SetVariable[k] = obj
                })
            }
            this.testStepsExp.push(val)
          }
        })
    },
    randomNum(m, n) {
      let num = Math.floor(Math.random() * (m - n) + n)
      return String(num)
    },
    handleScanSteps(Data) {
      let Deep, T, F
      for (F = Data.length; F; ) {
        T = Data[--F]
        T.id = this.randomNum(1, 10000)
        T.visible = false
        if (T.checks) {
          Deep = this.handleScanSteps(T.checks)
          if (Deep) {
            return Deep
          }
        }
      }
    },
    closeBugPoc() {
      this.toDebugPocShow = false
    }
  },

  mounted() {
    this.initEditor()
  }
}
</script>
<style scoped lang="less">
.add-poc-save {
  display: flex;
  align-items: center;
}
/deep/ .el-date-editor.el-input {
  width: 100% !important;
}
/deep/.el-tabs {
  .el-tabs__nav {
    padding: 0;
  }
  .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
    height: 37px !important;
    text-align: center;
    line-height: 37px !important;
    font-weight: bold;
    color: #2677ff;
  }
}

.add-poc-tab a.active {
  color: #4285f4;
  border-bottom: 4px solid #4285f4;
  font-weight: 600;
}

.add-poc-tab a.active .el-icon-caret-top {
  display: block;
}

.add-poc-save {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  height: 68px;
  border-top: 1px solid var(--secondaryBorder-color);
}

.add-poc-save .start {
  float: left;
  width: auto;
  margin-top: 14px;
  margin-left: 14px !important;
  margin-right: 25px;
  padding: 12px 34px;
}

.add-poc-save .start:hover,
.add-poc-save .start:focus {
  padding: 12px 34px;
}

.add-poc-step {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 68px;
  right: 0;
  max-height: 100%;
  overflow: auto;
}

.add-poc-step.test {
  right: 0;
  max-height: unset;
}

.add-poc-step-info {
  margin-left: 20px;
  margin-right: 15px;
  margin-top: 20px;
}

.add-poc-step .el-form-item__label {
  font-size: 12px;
  line-height: 30px;
  padding: 0;
  color: var(--primaryFont-color);
}

.el-collapse-item__content .el-form-item__label {
  text-indent: 26px;
}

.add-poc-step .el-form-item__content {
  line-height: 30px;
  white-space: nowrap !important;
}
.el-checkbox + .el-checkbox {
  margin-left: 17px;
}
.add-poc-step .el-form-item {
  margin-bottom: 20px;
}
.custom-label-height {
  /deep/ .el-form-item__label {
    line-height: 40px !important;
    height: 40px;
  }
  .scan-zk-open {
    float: right;
  }
}
.add-poc-step .el-form-item .el-form-item__label {
  font-size: 14px;
  font-family:
    PingFangSC-Regular,
    PingFang SC,
    sans-serif;
  color: #333333;
}

.add-poc-step .el-form-item .el-textarea .el-textarea__inner {
  font-size: 14px;
  font-family:
    PingFangSC-Regular,
    PingFang SC,
    sans-serif;
  color: #333333;
}

.add-poc-step .el-form-item .el-input {
  height: 32px;
}

.add-poc-step .el-form-item .el-input .el-input__inner {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  font-family:
    PingFangSC-Regular,
    PingFang SC,
    sans-serif;
  color: #333333;
}

.add-poc-step .el-form-item .el-input .el-input__icon {
  line-height: 32px;
}

.add-poc-step .el-form-item.query-rules {
  position: relative;
  margin-right: 32px;
  white-space: nowrap;
}

.add-poc-step .el-form-item.query-rules .el-input {
  background: #f2f4f7;
}

.add-poc-step .el-form-item.query-rules .el-input__icon {
  color: #333;
}

.add-poc-step .el-form-item.query-rules .el-input__inner {
  padding-right: 35px;
  background: #f2f4f7;
  border-color: #f2f4f7;
}
.el-select-dropdown__item.selected,
.el-autocomplete-suggestion li.selected {
  color: var(--primaryFont-color);
}
.add-poc-step .el-form-item.query-rules .el-input__inner::placeholder {
  color: #999999;
}

.add-poc-step .el-form-item.query-rules .iconForPoc {
  color: #cccccc;
  cursor: pointer;
  width: 16px;
  height: 16px;
  margin-left: 10px;
  font-size: 16px;
}
.iconbangzhu:before {
  content: '\e67d';
}
.add-poc-step .el-form-item.query-rules .iconsousuo_huaban {
  position: absolute;
  top: 0;
  right: 11px;
}
.el-select input {
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  color: var(--primaryFont-color);
  border-radius: 4px;
}
.el-select-dropdown__item {
  font-size: 12px;
  height: 30px;
  line-height: 30px;
}
.el-select-dropdown__item,
.el-autocomplete-suggestion li {
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  color: var(--primaryFont-color);
}
.add-poc-step .el-form-item.query-rules .iconchangjianwentixin {
  position: absolute;
  top: 0;
  right: -23px;
}
.gb-com-two-cols-text {
  position: relative;
  font-size: 22px;
  font-family:
    PingFangSC-Semibold,
    PingFang SC,
    sans-serif;
  font-weight: 600;
  color: #333333;
  height: 98px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}
.gb-com-two-cols-text .gb-com-two-cols-text-left {
  font-size: 22px;
  font-family:
    PingFangSC-Regular,
    PingFang SC,
    sans-serif;
  color: #404040;
}
.gb-com-two-cols-text .gb-com-two-cols-text-right {
  display: flex;
  align-items: center;
}
.gb-com-two-cols-text .gb-com-two-cols-text-right .el-input {
  width: 386px;
  height: 34px;
  line-height: 34px;
  background: #fff;
}

.gb-com-two-cols-title .gb-sea-wrap {
  position: absolute;
  top: -1px;
  right: 25px;
}

.gb-com-two-cols-title .gb-sea-wrap.no-login {
  right: 0;
}

.gb-sea-wrap > span,
.gb-export {
  position: absolute;
  top: 0;
  right: 4px;
  bottom: 0;
  margin: auto;
  width: 20px;
  height: 20px;
  line-height: 18px;
  text-align: center;
  border-radius: 50%;
  border-radius: 12px;
  color: var(--main-color);
  background-color: var(--lv4Background-color);
}

.gb-sea-wrap > span .iconfonticonForPoc,
.gb-export .iconForPoc {
  font-size: 10px;
  color: var(--main-color);
}

.gb-sea-wrap.fold > span,
.gb-sea-wrap > a.fold.none {
  display: none;
}

.gb-sea-wrap > a.fold {
  float: right;
  width: 26px;
  height: 26px;
  text-align: center;
  line-height: 24px;
  margin-top: 1px;
  background: var(--lv4Background-color);
  border: 1px solid var(--secondaryBorder-color);
  border-radius: 50%;
}

.gb-sea-wrap > a.fold span {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  background: var(--lv4Background-color);
  border-radius: 50%;
}

.gb-sea-wrap > a.fold:hover span {
  background-color: var(--main-color);
}

.gb-sea-wrap > a.fold:hover span i {
  color: #fff;
}

.gb-sea-wrap > a.fold span i {
  font-size: 12px;
  color: var(--main-color);
}

.gb-export {
  top: 3px;
  right: 0;
  margin: 0;
}

.gb-com-two-cols-main {
  position: absolute;
  top: 54px;
  bottom: 40px;
  /* right: 30px; */
  right: 20px;
  left: 30px;
}

.gb-com-two-cols-left {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 240px;
  /* width: 205px; */
  padding-right: 15px;
}

.gb-com-two-cols-right {
  position: absolute;
  top: 0;
  bottom: 0;
  /* left: 220px; */
  left: 240px;
  right: 0;
  padding: 0 5px 10px 10px;
  background-color: var(--secondaryBackground-color);
  /* box-shadow: 0px 2px 16px 0px rgba(71, 120, 199, 0.12); */
}

.gb-com-two-cols-left-item > div:first-child {
  font-size: 14px;
  height: 34px;
  line-height: 28px;
  color: var(--primaryFont-color);
}

.gb-com-two-cols-left-item > div:first-child.active {
  color: var(--main-color);
}

.gb-com-two-cols-left-item > div:first-child .iconzhankai1,
.gb-com-two-cols-left-item > div:first-child .iconjianhao_huaban {
  cursor: pointer;
  font-size: 13px;
}

.gb-com-two-cols-left-item > div:first-child .iconzhankai1:hover,
.gb-com-two-cols-left-item > div:first-child .iconjianhao_huaban:hover {
  color: var(--main-color);
}

.gb-com-two-cols-left-item > div:first-child span .iconForPoc {
  font-size: 13px;
  margin-right: 9px;
}

.gb-com-two-cols-left-item .gb-list-wrap {
  padding-top: 0;
  margin-right: 4px;
  margin-bottom: 0;
}

.gb-list-no-drop-item,
.gb-list-drop-item {
  position: relative;
  display: block;
  font-size: 12px;
  height: 22px;
  line-height: 22px;
  margin-bottom: 8px;
  margin-right: 10px;
  padding-left: 23px;
  padding-right: 10px;
  color: var(--level3Font-color);
}

.gb-list-drop-item {
  padding: 0;
  height: auto;
}

.gb-list-drop-item i.el-icon-caret-bottom,
.gb-list-no-drop-item i.el-icon-caret-bottom {
  top: 3px;
}

.gb-list-drop-item i,
.gb-list-no-drop-item i {
  position: absolute;
  top: 4px;
  left: 4px;
  color: var(--primaryIcon-color);
  font-size: 14px;
}

.gb-list-no-drop-item p {
  color: var(--level3Font-color);
}

.gb-list-no-drop-item:hover,
.gb-list-no-drop-item.active,
.gb-list-drop-item > a:hover,
.gb-list-drop-item li:hover {
  background-color: #26afea;
  /* box-shadow: 0px 3px 12px 0px rgba(54, 173, 225, 0.54); */
  border-radius: 4px;
  color: #ffffff !important;
}

.gb-list-no-drop-item.active p,
.gb-list-no-drop-item:hover p,
.gb-list-drop-item li:hover span,
.gb-list-drop-item a:hover p,
.gb-list-no-drop-item.active .ellipise {
  color: #ffffff !important;
}

.gb-list-drop-item.active i,
.gb-list-drop-item:hover i,
.gb-list-no-drop-item.active i,
.gb-list-no-drop-item:hover i {
  color: var(--primaryIcon-color) !important;
}

.gb-list-no-drop-item .ellipise {
  max-width: 70%;
  color: var(--secondaryFont-color);
}

.gb-list-no-drop-item .ellipise + p {
  color: var(--level3Font-color);
}

.gb-list-no-drop-item:hover .ellipise,
.gb-list-no-drop-item:hover .ellipise,
.gb-list-no-drop-item.active:hover .ellipise + p,
.gb-list-no-drop-item.active:hover .ellipise + p {
  color: #ffffff !important;
}

.gb-list-drop-item i {
  position: absolute;
  top: 5px;
  left: 9px;
  color: var(--main-color);
  opacity: 0.5;
}

.gb-list-drop-item a {
  display: block;
  color: #333;
  padding-left: 25px;
  padding-right: 10px;
}

.gb-list-drop-item a p {
  color: #85a1ce;
}

.gb-list-drop-item li {
  margin: 8px 0;
  margin-left: 9px;
}

.gb-list-drop-item li a {
  padding-left: 16px;
}
</style>
