import $ from 'jquery'

window.json_str = ''
window.th = 0
window.pindex = 0
window.FileName = ''

window.syncEditor = function syncEditor(type, params) {
  // type为0，新增，type为1，编辑
  // console.log(params)
  let newParams = JSON.parse(JSON.stringify(params)),
    Name = newParams.Name,
    Level = newParams.Level,
    FofaQuery = newParams.FofaQuery,
    Description = newParams.Description,
    Product = newParams.Product,
    Homepage = newParams.Homepage,
    Author = newParams.Author,
    DisclosureDate = newParams.DisclosureDate,
    Impact = newParams.Impact,
    Recommendation = newParams.Recommendation,
    References = newParams.References,
    Tags = newParams.Tags,
    ScanSteps = newParams.ScanSteps,
    initPocFileContent = newParams.initPocFileContent,
    HasExp = newParams.HasExp,
    ExpParams = newParams.ExpParams,
    ExploitSteps = newParams.ExploitSteps,
    CVEIDs = newParams.CVEIDs,
    CNNVD = newParams.CNNVD,
    CNVD = newParams.CNVD,
    CVSSScore = newParams.CVSSScore,
    Is0day = newParams.Is0day,
    VulType = newParams.VulType
  if (!Description) {
    Description = ''
  }
  if (!Product) {
    Product = ''
  }
  if (!Homepage) {
    Homepage = ''
  }
  if (!Author) {
    Author = ''
  }
  if (!DisclosureDate) {
    DisclosureDate = ''
  }
  if (!Impact) {
    Impact = ''
  }
  if (!Recommendation) {
    Recommendation = ''
  }
  if (!References) {
    References = ''
  }

  if (type === 0) {
    let json = {}
    let baseInfoJson =
      '{' +
      '"Name":"' +
      quoteValue(Name) +
      '",' +
      '"Description":"' +
      textNewLine(quoteValue(Description)) +
      '",' +
      '"Product":"' +
      quoteValue(Product) +
      '",' +
      '"Homepage":"' +
      quoteValue(Homepage) +
      '",' +
      // '"DisclosureDate":"' + new Date().FormatData("yyyy-MM-dd") + '",' +
      '"Author":"' +
      quoteValue(Author) +
      '",' +
      '"DisclosureDate":"' +
      DisclosureDate +
      '",' +
      '"FofaQuery":"' +
      quoteValue(FofaQuery) +
      '",' +
      '"Level":"' +
      quoteValue(Level) +
      '",' +
      '"Impact":"' +
      quoteValue(Impact) +
      '",' +
      '"Recommendation":"' +
      quoteValue(Recommendation) +
      '",' +
      '"References":' +
      to_writeRefer(quoteValue(References)) +
      '}'
    let BaseInfo = JSON.parse(baseInfoJson)
    let copyScanSteps = JSON.parse(JSON.stringify(ScanSteps))
    let ScanStepsRet = copyScanSteps
    // let ScanStepsRet = toScanSteops(copyScanSteps);
    let copyScanStepsExp = JSON.parse(JSON.stringify(ExploitSteps))
    let ScanStepsRetExp = copyScanStepsExp
    // let ScanStepsRetExp = toScanSteops(copyScanStepsExp);

    // 新增字段
    let newField = {
      Is0day: false,
      HasExp: false,
      ExpParams: null,
      ExpTips: {
        Type: '',
        Content: ''
      },
      ScanSteps: ScanStepsRet,
      ExploitSteps: ScanStepsRetExp,
      Tags: Tags,
      VulType: '',
      CVEIDs: [],
      CNNVD: '',
      CNVD: '',
      CVSSScore: '0.0',
      AttackSurfaces: {
        Application: null,
        Support: null,
        Service: null,
        System: null,
        Hardware: null
      }
    }

    BaseInfo = Object.assign(BaseInfo, newField)
    console.log('BaseInfo------', BaseInfo)
    json = JSON.stringify(BaseInfo, null, 2)
    let content = ''
    content =
      'package exploits\n\n' +
      'import (\n\t"git.gobies.org/goby/goscanner/goutils"\n)\n\n' +
      'func init() {\n' +
      '\texpJson := `' +
      json +
      '`\n\n' +
      '\tExpManager.AddExploit(NewExploit(\n' +
      '\t\tgoutils.GetFileName(),\n' +
      '\t\texpJson,\n' +
      '\t\tnil,\n' +
      '\t\tnil,\n' +
      '\t))\n' +
      '}'

    // console.log(content, "go content");
    return content
  } else if (type === 1) {
    let copyScanSteps = JSON.parse(JSON.stringify(ScanSteps))
    // let ScanStepsRet = toScanSteops(copyScanSteps);
    let ScanStepsRet = copyScanSteps
    let copyScanStepsExp = JSON.parse(JSON.stringify(ExploitSteps))
    // let ScanStepsExpRet = toScanSteops(copyScanStepsExp);
    let ScanStepsExpRet = copyScanStepsExp
    let s = initPocFileContent.indexOf('`{')
    let e = initPocFileContent.indexOf('}`')
    let tempJson = JSON.parse(initPocFileContent.substring(s + 1, e + 1))
    tempJson.Name = Name
    tempJson.Description = textNewLine(Description)
    tempJson.Product = Product
    tempJson.Homepage = Homepage
    // tempJson.DisclosureDate = new Date().FormatData("yyyy-MM-dd");
    tempJson.Author = Author
    tempJson.DisclosureDate = DisclosureDate.substring(0, 10)
    tempJson.FofaQuery = FofaQuery
    tempJson.Level = Level
    tempJson.Impact = Impact
    tempJson.Recommendation = Recommendation
    tempJson.References = JSON.parse(to_writeRefer(References))
    tempJson.ScanSteps = ScanStepsRet
    tempJson.Tags = Tags
    tempJson.HasExp = HasExp
    tempJson.ExpParams = ExpParams
    tempJson.ExploitSteps = ScanStepsExpRet
    tempJson.CVEIDs = CVEIDs
    tempJson.CNNVD = CNNVD
    tempJson.CNVD = CNVD
    tempJson.CVSSScore = CVSSScore
    tempJson.Is0day = Is0day
    tempJson.VulType = VulType
    // console.log(tempJson, "tempJson before");
    tempJson = JSON.stringify(tempJson, null, 2)

    initPocFileContent =
      initPocFileContent.substring(0, s) +
      '`' +
      tempJson +
      '`' +
      initPocFileContent.substring(e + 2, initPocFileContent.length + 1)
    return initPocFileContent
  }
}

window.toScanSteops = function toScanSteops(ScanSteps_arr) {
  for (let i = 0; i < ScanSteps_arr.length; i++) {
    if (i > 0) {
      //console.log(ScanSteps_arr[i],"=======");
      //ScanSteps_arr[i].Request.header=header_split(ScanSteps_arr[i].Request.header);
      var header_str = header_split(ScanSteps_arr[i].Request.header)
      var header = '{' + header_str + '}'
      ScanSteps_arr[i].Request.header = JSON.parse(header)
      changejson2(ScanSteps_arr[i].ResponseTest)
      var last_str = json_str.substr(json_str.length - 1, 1)
      if (last_str === ',') {
        json_str = json_str.substr(0, json_str.length - 1)
      }
      json_str = json_str.replace(/\}\,\]/g, '}]')
      ScanSteps_arr[i].ResponseTest = json_str ? JSON.parse(json_str) : json_str
      //console.log(json_str,"======="+i);
    }
    json_str = ''
  }
  return ScanSteps_arr
}

window.header_split = function header_split(text) {
  if (text == null || text === '') {
    return ''
  }
  if (JSON.stringify(text) === '{}') {
    return ''
  }
  let ret = '' //行的拼接
  let strs = text.split('\n') //字符分割

  for (let i = 0; i < strs.length; i++) {
    //console.log("第"+i+"值"+strs[i]);

    if (strs[i].indexOf(':') === -1) {
      continue
    }

    var strs_all = strs[i].split(':')

    var strs_l = strs_all[0]
    //var strs_r = strs_all[1];
    if (strs_all.length > 2) {
      var strs_r = ''
      for (var n = 1; n < strs_all.length; n++) {
        if (n == strs_all.length - 1) {
          if (n == 1) {
            var strs_r = strs_r + $.trim(strs_all[n])
          } else {
            var strs_r = strs_r + rtrim(strs_all[n])
          }
        } else {
          if (n == 1) {
            var strs_r = strs_r + ltrim(strs_all[n]) + ':'
          } else {
            var strs_r = strs_r + strs_all[n] + ':'
          }
        }
      }
    } else {
      var strs_r = $.trim(strs_all[1])
    }

    if (strs_l == null) {
      strs_l = ''
    }
    if (strs_r == null) {
      strs_r = ''
    }
    if (strs_l.length > 0) {
      strs_l = $.trim(strs_l)
    }
    ret += '"' + strs_l + '"' + ':' + '"' + strs_r + '"'

    if (i == strs.length - 1) {
      ret += ''
    } else {
      ret += ','
    }
  }

  ret = ret.split(',')
  var retnew = new Array()
  for (let i = 0; i < ret.length; i++) {
    if (ret[i] != '"":""' && ret[i] != '') {
      //retnow=ret[i].substr(1,ret[i].length-1);
      //retnow=retnow.substr(0,retnow.length-1);
      retnew.push(ret[i])
    }
  }

  var retnewover = ''
  //retnewover += '{';
  for (let i = 0; i < retnew.length; i++) {
    retnewover = retnewover + retnew[i]
    if (i == retnew.length - 1) {
      retnewover += ''
    } else {
      retnewover += ','
    }
  }
  //retnewover += '}';
  //console.log(retnewover);
  return retnewover
}

window.changejson2 = function changejson2(json_data) {
  //json转化json   ResponseTest对象
  if (json_data.type === 'group') {
    //是文件夹
    json_str +=
      '{"type": "' + json_data.type + '","operation": "' + json_data.operation + '","checks": ['

    $.each(json_data.checks, function (idx, subnode) {
      //遍历json数组
      if (subnode.type === 'group') {
        th = json_data.checks.length
        //alert(JSON.stringify(subnode.checks));
        //return;
        pindex = idx
        changejson2(subnode)
      } else {
        if (idx + 1 === json_data.checks.length) {
          json_str +=
            '{"type": "' +
            quoteValue(subnode.type) +
            '","variable":"' +
            quoteValue(subnode.variable) +
            '","operation": "' +
            quoteValue(subnode.operation) +
            '","value": "' +
            quoteValue(subnode.value) +
            '","bz": "' +
            quoteValue(subnode.bz) +
            '"}'
        } else {
          json_str +=
            '{"type": "' +
            quoteValue(subnode.type) +
            '","variable":"' +
            quoteValue(subnode.variable) +
            '","operation": "' +
            quoteValue(subnode.operation) +
            '","value": "' +
            quoteValue(subnode.value) +
            '","bz": "' +
            quoteValue(subnode.bz) +
            '"},'
        }
      }
    })
    //alert(pindex);
    if (pindex + 1 === th) {
      json_str += ']}'
    } else {
      json_str += ']},'
    }
  }
  return json_str
}

window.quoteValue = function quoteValue(str) {
  if (str == null) {
    return str
  }
  if (/^[0-9]*$/.test(str)) {
    str = str.toString()
  }
  //str = xssfilter_new(str);
  //str = str.replace(/\\"/g, 'fofavalue');
  //str = str.replace(/"/g, '\\"');
  //str = str.replace(/fofavalue/g, '\\\"');
  str = str.replace(/\\/g, '\\\\')
  //str = str.replace(/\\"/g, '\\\\"');
  str = str.replace(/"/g, '\\"')
  //str = str.replace(/</g, '&lt;').replace(/>/g, '&gt;');
  //str = xss(str);
  return str
}

window.textNewLine = function textNewLine(text, flag) {
  if (!flag) {
    flag = 0
  }
  var ret = ''
  var strs = text.split('\n') //字符分割
  for (let i = 0; i < strs.length; i++) {
    ret += strs[i] + '\\n'
  }
  ret = ret.substr(0, ret.length - 2)
  //ret = ret.toString();
  //console.log(ret);
  return ret
}

window.to_writeRefer = function to_writeRefer(text) {
  //text = JSON.stringify(text);
  //alert(text);
  if (text == '' || !text) {
    return '[]'
  }
  var ret = '['
  var strs = text.split('\n') //字符分割
  for (let i = 0; i < strs.length; i++) {
    if (strs.length - 1 == i) {
      ret += '"' + quoteValue(strs[i]) + '"'
    } else {
      ret += '"' + quoteValue(strs[i]) + '",'
    }
  }
  ret += ']'
  //ret = JSON.stringify(ret);
  // console.log(ret);
  return ret
}

window.to_parseRefer = function to_parseRefer(text) {
  try {
    var restmp = JSON.parse(text)
    var res = ''
    for (let i = 0; i < restmp.length; i++) {
      if (restmp.length - 1 == i) {
        res += restmp[i]
      } else {
        res += restmp[i] + '\n'
      }
    }
  } catch (ex) {
    var restmp = text
    var res = ''
    for (let i = 0; i < restmp.length; i++) {
      if (restmp.length - 1 == i) {
        res += restmp[i]
      } else {
        res += restmp[i] + '\n'
      }
    }
  }
  return res
}

window.header_json = function header_json(text) {
  //组装str
  if (text == null || text == '') {
    return ''
  }

  if (typeof text == 'string') {
    return text
  }

  var strs_long = ''
  var strs_all = text

  for (var key in strs_all) {
    strs_long += key + ': ' + strs_all[key] + '\n'
  }

  strs_long = strs_long.replace(/"+/g, '')

  //console.log(strs_arr.length);
  return strs_long
}

window.savePocBaseGo = function savePocBaseGo(fileNameBase, data, callback) {
  if (globalConfig.debug) {
    var filepath = rootPathGoby + '/exploits/user/'
  } else {
    var filepath = goblibPath + '/exploits/user/'
  }
  if (process.platform == 'win32') {
    filepath = filepath.replace(/\//g, '\\')
  }
  var fs = require('fs')
  var pathd = require('path')
  var extname = pathd.extname(fileNameBase)
  if (extname == '.go') {
    var FileName1 = fileNameBase
  } else if (extname == '.json') {
    let ind = fileNameBase.indexOf('.json')
    var FileName1 = fileNameBase.substring(0, ind) + '.go'
  } else {
    var FileName1 = fileNameBase + '.go'
  }
  var tmpFileName = FileName
  if (ChackclientName(FileName1)) {
    FileName = FileName1.replace(/[^a-zA-Z0-9_.]/g, '_')
    if (tmpFileName == '' && fs.existsSync(filepath + FileName)) {
      FileName = ''
      callback({ statusCode: 453, messages: 'File exist!' })
    } else {
      // verifyPoc(callback,JSON.stringify(BaseInfo));
      savePocContent = data
      // console.log("fin", data, filepath+FileName);
      savePocPath = filepath + FileName
      fs.writeFileSync(filepath + FileName, data)
      callback({ statusCode: 200, messages: 'Success!' })
    }
    //callback({ "state": "yes", "msg": "Save Success!" });
  } else {
    callback({ statusCode: 453, messages: '文件名允许包含字母、数字、下划线。' })
  }
}
