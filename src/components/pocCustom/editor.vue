<template>
  <div id="editor">
    <code-mirror-component :value="code" :options="options" @input="onCodeChange" ref="codeDom" />
  </div>
</template>

<script>
import CodeMirrorComponent from '@/components/pocCustom/CodeMirrorComponent.vue'

export default {
  name: 'Editor',
  components: {
    CodeMirrorComponent
  },
  props: {
    // 外部传入的内容，用于实现双向绑定
    value: String
  },
  data() {
    return {
      // 内部真实的内容
      code: '',
      // 默认配置
      options: {
        mode: 'text/x-go',
        // 缩进格式
        tabSize: 4,
        // 主题，对应主题库 JS 需要提前引入
        theme: 'monokai',
        // 显示行号
        lineNumbers: true,
        line: true,
        foldGutter: true,
        lineWrapping: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers']
      }
    }
  },
  methods: {
    init() {
      // console.log('初始化编辑器',this.value)
      this.codemirror.setValue(this.value)
      this.$nextTick(() => {
        this.codemirror.refresh()
      })
    },
    onCodeChange() {
      this.code = this.codemirror.getValue()
      // 编辑器同步到可视化
      // console.log(this.$parent)
      // console.log('编辑器onCodeChange',this.code)
      this.$parent.editorSyncVis(this.code)
    }
  },
  watch: {
    curStep() {
      if (this.curStep === 2) {
        this.init()
      }
    }
  },
  computed: {
    codemirror() {
      return this.$refs.codeDom.codemirror
    },
    curStep() {
      return this.$parent.curStep
    }
  }
}
</script>

<style scoped></style>
