<template>
  <ul class="triangle_step">
    <li
      :class="setBarClass(item.id)"
      v-for="(item, index) in stepArr"
      :key="index"
      :style="{ width: width }"
    >
      <!-- 已完成步骤的对勾 -->
      <i :class="taskStep > item.id ? 'el-icon-success' : ''"></i>
      <!-- 步骤序号 -->
      <i
        v-if="taskStep <= item.id"
        :class="taskStep == item.id ? 'indexClass curIndex' : 'indexClass noIndex'"
        >{{ index + 1 }}</i
      >
      <!-- 步骤名称 -->
      <span>{{ item.title }}</span>
      <!-- 灰色三角指引，最后一步没有 -->
      <div v-if="index < stepArr.length - 1" :class="setClass(item.id)"></div>
      <!-- 白色箭头指引，最后一步没有，通过overflow:hidden隐藏了 -->
      <div class="interval"></div>
    </li>
  </ul>
</template>
<script>
export default {
  props: {
    taskStep: {
      default: 1
    },
    stepArr: {
      default: []
    },
    width: {
      default: '100%'
    }
  },
  methods: {
    setBarClass(id) {
      let cur = ''
      if (this.taskStep > id) {
        cur = 'curPass'
      } else if (this.taskStep == id) {
        cur = 'cur'
      } else {
        cur = 'noPass'
      }
      return cur
    },
    setClass(id) {
      let cur = ''
      if (this.taskStep > id) {
        cur = 'passJiao'
      } else if (this.taskStep == id) {
        cur = 'jiaoActive'
      } else {
        cur = 'jiao'
      }
      return cur
    }
  }
}
</script>
<style lang="less" scoped>
* {
  margin: 0;
  padding: 0;
  list-style: none !important;
  font-size: 12px;
}
.triangle_step {
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto !important;
  margin-bottom: 6px !important;
  padding: 20px 20px 16px 20px;
  overflow: hidden;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  li {
    height: 40px;
    background: #d7d8da;
    color: #62666c;
    font-size: 14px;
    line-height: 40px;
    padding-left: 4%;
    position: relative;
    span {
      font-size: 14px;
    }
    .el-icon-success {
      vertical-align: middle;
      font-size: 20px;
      margin-right: 8px;
    }
    .indexClass {
      display: inline-block;
      width: 22px;
      height: 22px;
      line-height: 22px;
      text-align: center;
      border-radius: 50%;
      margin-right: 8px;
    }
    .curIndex {
      color: #2677ff;
      background: #ffffff;
    }
    .noIndex {
      color: #fff;
      background: #b2bdd4;
    }
  }
  li.cur {
    background: #2677ff;
    color: #fff;
  }
  li.curPass {
    background: rgba(38, 119, 255, 0.12);
    color: #2677ff;
  }
  li.noPass {
    background: rgba(235, 238, 245, 1);
    color: #62666c;
  }
  /*三角形绘制*/
  .jiao {
    width: 0;
    height: 0;
    border-top: 20px solid transparent; /*高度一半*/
    border-left: 20px solid rgba(235, 238, 245, 1); /*调整宽度*/
    border-bottom: 20px solid transparent; /*高度一半*/
    position: absolute;
    right: -20px; /*跟宽度保持一致*/
    top: 0;
    z-index: 3;
  }
  .jiaoActive {
    width: 0;
    height: 0;
    border-top: 20px solid transparent; /*高度一半*/
    border-left: 20px solid #2677ff; /*调整宽度*/
    border-bottom: 20px solid transparent; /*高度一半*/
    position: absolute;
    right: -20px; /*跟宽度保持一致*/
    top: 0;
    z-index: 2;
  }
  .passJiao {
    width: 0;
    height: 0;
    border-top: 20px solid transparent; /*高度一半*/
    border-left: 20px solid rgba(38, 119, 255, 0.12); /*调整宽度*/
    border-bottom: 20px solid transparent; /*高度一半*/
    position: absolute;
    right: -20px; /*跟宽度保持一致*/
    top: 0;
    z-index: 2;
  }
  /*大3个px的边 26-20/2*/
  .interval {
    width: 0;
    height: 0;
    border-top: 26px solid transparent; /*高度一半*/
    border-left: 26px solid #fff; /*调整宽度*/
    border-bottom: 26px solid transparent; /*高度一半*/
    position: absolute;
    right: -26px; /*跟宽度保持一致*/
    top: -6px;
    z-index: 1;
  }
}
</style>
