import Vue from 'vue'
import store from './store'
import ELEMENT from 'element-ui'
// import 'hsxa-ui/lib/theme-chalk/index.css';
import 'element-ui/lib/theme-chalk/index.css'
// import '@/style/huxa/index.css';
import './utils/china'
import App from './App.vue'
import { router } from './router'
import derective from './utils/repeatClick'
import { Message } from 'element-ui'
import basurl from './utils/request'
import './utils/punycode' // punycode编码
import iconMatchFunc from './utils/iconMatch'
import sha1 from 'js-sha1'
// 分片上传
import uploader from 'vue-simple-uploader'

Vue.use(uploader)
// 滚动无限加载
import infiniteScroll from 'vue-infinite-scroll'
Vue.use(infiniteScroll)
import ZmTreeOrg from 'zm-tree-org'
import 'zm-tree-org/lib/zm-tree-org.css'
Vue.use(ZmTreeOrg)

Vue.prototype.uploadSrcIp = basurl.defaults.baseURL
Vue.prototype.wsIp = basurl.defaults.wsIp
Vue.prototype.wsIpGolang = basurl.defaults.wsIpGolang
Vue.prototype.golangUploadSrcIp = basurl.defaults.targetGolang // golang上传
Vue.prototype.showSrcIp = basurl.defaults.ipURL // 导出、图片显示不带api/v1
Vue.prototype.imgBaseUrl = basurl.defaults.imgBaseUrl // 带有http路径会跨域的需要使用//images.weserv.nl/?url=

// 匹配组件icon
Vue.prototype.iconMatchFunc = iconMatchFunc
// 引入jq
import jquery from 'jquery'
Vue.prototype.$ = jquery
// 引入sha1加密
Vue.prototype.sha1 = sha1
Vue.prototype.$punyCode = new idnMapping()
import 'leaflet/dist/leaflet.css'
//引入echarts
import * as echarts from 'echarts'
Vue.prototype.$echarts = echarts
// 下载
Vue.prototype.download = (url, fileName) => {
  if (!url) {
    return
  }
  let link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', fileName)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url) // 释放URL 对象
}
// 时间格式转换:2022-12-17T04:07:53+08:00
Vue.prototype.transferTime = (cTime) => {
  var dates = new Date(cTime).toJSON()
  return new Date(+new Date(dates) + 8 * 3600 * 1000)
    .toISOString()
    .replace(/T/g, ' ')
    .replace(/\.[\d]{3}Z/, '')
}

// 表格使用fixed后错位
Vue.prototype.doLayout = (that, refName) => {
  that.$nextTick(() => {
    that.$refs[refName].doLayout()
  })
}
// 剪切板
import VueClipboard from 'vue-clipboard2'
VueClipboard.config.autoSetContainer = true
Vue.use(VueClipboard)

Vue.prototype.changeColor = (str, key) => {
  let keyCopy = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // 转义正则表达式特殊字符
  const reg = new RegExp(keyCopy, 'ig')
  return String(str).replace(reg, (val) => {
    return `<i style="padding: 0;background: yellow">${val}</i>`
  })
}
// 删除末页后页码更新
Vue.prototype.updateCurrenPage = (thisTotal, thisCheckedArr, thisCurrentPage, thisPageSize) => {
  let totalPage = Math.ceil((thisTotal - thisCheckedArr.length) / thisPageSize)
  let currentPage = thisCurrentPage > totalPage ? totalPage : thisCurrentPage
  return currentPage < 1 ? 1 : currentPage
}
Vue.prototype.secondsFormat = (s) => {
  // s是毫秒
  if (!s) {
    return
  }
  const day = Math.floor(s / (24 * 3600)) // Math.floor()向下取整
  const hour = Math.floor((s - day * 24 * 3600) / 3600)
  const minute = Math.floor((s - day * 24 * 3600 - hour * 3600) / 60)
  const second = Math.floor(s - day * 24 * 3600 - hour * 3600 - minute * 60)

  let result
  if (day != 0) {
    result = day + ' 天 ' + hour + ' 时 ' + minute + ' 分 ' + second + ' 秒'
    return result
  }
  if (day == 0 && hour != 0) {
    result = hour + ' 时 ' + minute + ' 分' + second + ' 秒'
    return result
  }
  if (day == 0 && hour == 0 && minute != 0) {
    result = minute + ' 分 ' + second + ' 秒'
    return result
  }
  if (day == 0 && hour == 0 && minute == 0) {
    result = second + ' 秒'
    return result
  }
}
Vue.prototype.secondsFormatValid = (s) => {
  // s是毫秒
  if (!s) {
    return
  }
  const day = Math.floor(s / (24 * 3600)) // Math.floor()向下取整
  const hour = Math.floor((s - day * 24 * 3600) / 3600)
  const minute = Math.floor((s - day * 24 * 3600 - hour * 3600) / 60)
  const second = Math.floor(s - day * 24 * 3600 - hour * 3600 - minute * 60)

  let result
  if (day != 0) {
    result = day + ' 天' + hour + ' 时' + minute + ' 分'
    return result
  }
  if (day == 0 && hour != 0) {
    result = hour + ' 时' + minute + ' 分'
    return result
  }
  if (day == 0 && hour == 0 && minute != 0) {
    result = minute + ' 分'
    return result
  }
  if (day == 0 && hour == 0 && minute == 0) {
    result = second + ' 秒'
    return result
  }
}
Vue.prototype.setProgress = (oldData, pushPro) => {
  // 解决推送进度回退；status==1未成功状态下progress却达到100,status==2成功状态下progress!=100,前端处理，以status：1/2-正在进行/推送结束为准
  // oldData：上次推送的进度
  // pushPro：最新推送的进度
  let curpro = '' // 处理后的进度
  let oldProgress = oldData
  if (pushPro == '100') {
    // status:1,未完成，此时进度显示在99
    curpro = 99
  } else {
    if (pushPro / 1 >= oldProgress) {
      curpro = pushPro / 1
    } else {
      // 当前推送的进度小于上次的进度，就不更新进度，避免出现进度条回退现象
      curpro = oldProgress
    }
  }
  return curpro
}
Vue.prototype.tabKeywordArr = [
  'domain_keyword',
  'cert_keyword',
  'icp_keyword',
  '',
  'key_keyword',
  '',
  'ip_keyword'
]
Vue.prototype.checkedAllArr = [
  'domain_checked',
  'cert_checked',
  'icp_checked',
  'icon_checked',
  'key_checked',
  'subdomain_checked',
  'ip_checked',
  'fid_checked'
]
Vue.prototype.copyFun = (data) => {
  let url = data
  let oInput = document.createElement('input')
  oInput.value = url
  document.body.appendChild(oInput)
  oInput.select() // 选择对象;
  document.execCommand('Copy') // 执行浏览器复制命令
  Message({
    message: '复制成功',
    type: 'success'
  })
  oInput.remove()
}
// html内容标签化渲染
Vue.prototype.transferred = function (val) {
  if (!val) return ''
  return val.toString().replace(/&(amp|gt|lt|quot|#39|nbsp);/g, (a) => {
    return {
      '&lt;': '<',
      '&amp;': '&',
      '&quot;': '"',
      '&gt;': '>',
      '&#39;': "'",
      '&nbsp;': ' ',
      '&copy;': ''
    }[a]
  })
}
router.beforeEach((to, from, next) => {
  let whitePath = [] // 设置路由白名单
  if (sessionStorage.getItem('is_service') && sessionStorage.getItem('is_service') == 1) {
    // 售后管理
    whitePath = [
      '/serviceManage',
      '/personalCenter',
      '/personalCenterInfo',
      '/wxLogin',
      '/noticePage'
    ]
  } else {
    whitePath = [
      '/riskDetails',
      '/viewDetails',
      '/workbench',
      '/index',
      '/assetsLedger',
      '/assetsTaskView',
      '/home',
      '/personalCenter',
      '/personalCenterInfo',
      '/assetsScan',
      '/alreadyTask',
      '/alreadyTask_viewlist',
      '/alreadyTask_viewlist_ipinfo',
      '/assetsCloud',
      '/scanReg',
      '/unclaimCloud',
      '/ignoreAssets',
      '/threatAssets',
      '/leakScan',
      '/repairLeakScan',
      '/taskRepairLeakScan',
      '/expandLog',
      '/assets/account/files',
      '/portManage',
      '/pocManage',
      '/ipManage',
      '/userManage',
      '/loginLog',
      '/newAssets',
      '/dataLeak',
      '/keywordManage',
      '/zhouqiDetails',
      '/wxLogin',
      '/reportManage',
      '/userOnline',
      '/cronTask',
      '/cronTaskHistory',
      '/clueExpansionManage',
      '/unitIndex',
      '/clueBlack',
      // '/ipBanningManage',
      '/eventWarning',
      '/eventInfo',
      '/loginEntry',
      '/eventRuleManage',
      '/riskAssets',
      '/domainAsset',
      '/domainDetails',
      '/certAsset',
      '/publicClueBank',
      '/supplyChainBank',
      '/companyBank',
      '/domainTask',
      '/domainTaskResult',
      '/checkTask',
      '/statusTask',
      '/screenAssets',
      '/recommentAssets',
      '/noticeManage',
      '/noticePage',
      '/serviceManage',
      '/pocAudit',
      '/upgradeCenter',
      '/upgradeCenterUpload',
      '/upgradeCenterUploadForadar',
      '/upgrading',
      '/businessSystem',
      '/clueStore',
      '/phishingTask',
      '/phishingTaskRecordInfo',
      '/tApiRisk',
      '/settingManage',
      '/assetsTanzhi',
      '/defaultRule',
      '/dataLeakStore',
      '/groupAssets',
      '/organization',
      '/organization-clue',
      '/organization-assets',
      '/groupAssets-recommend',
      '/yellowGambling',
      '/digitalAssets-task',
      '/digitalAssets-data',
      '/digitalAssets-task-detail',
      '/icpDatabase',
      '/taskBriefing',
      '/spatialRetrieval',
      '/urlAsset',
      '/taskViewList',
      '/intelligenceCenterv1',
      '/intelligenceManage',
      '/hotCheckResList',
      '/orgOperation',
      '/stationMsg',
      '/taskOverview',
      '/dataSync',
      '/intelligenceBrief',
      '/intelligenceRelated',
      '/microServe',
      '/clueOverview'
      //'/docsApi'
    ]
  }

  if (to.path) {
    // 百度站长统计
    if (window._hmt) {
      window._hmt.push(['_trackPageview', '/#' + to.fullPath])
    }
  }

  if (localStorage.getItem('tokenTime')) {
    if (to.path == '/login' || to.path == '/') {
      // 直接改变路径回登录页限制
      router.replace('/workbench')
      sessionStorage.setItem('menuId', '9')
    } else if (whitePath.indexOf(to.path) == -1) {
      // 跳转不存在的路由
      router.replace(whitePath[0])
      if (whitePath[0] == '/workbench') {
        // 首页
        sessionStorage.setItem('menuId', '9')
      } else {
        // 售后
        sessionStorage.setItem('menuId', '7-10')
      }
    } else if (whitePath.indexOf(to.path) != -1) {
      // 路由存在白名单内
      if (to.meta && to.meta.requiredQuery && (!to.query || Object.keys(to.query).length == 0)) {
        // 禁止删除部分参数跳转
        router.replace({ path: '/workbench' })
        sessionStorage.setItem('menuId', '9')
      } else {
        next()
      }
    } else if (to.path == '/wxLogin') {
      router.replace('/workbench')
      sessionStorage.setItem('menuId', '9')
    }
    next()
  } else {
    sessionStorage.clear()
    localStorage.clear()
    if (to.path == '/login') {
      next()
    } else if (to.path == '/wxLogin') {
      // 微信登录
      next()
    } else if (to.path == '/noticePage') {
      // 升级页面
      next()
    } else if (to.path == '/upgrading') {
      // 升级中心
      next()
    } else {
      next({ path: '/login' })
    }
    next()
  }
  next()
})
// loading chunk 出错处理
router.onError((error) => {
  const pattern = /Loading chunk (\d)+ failed/g
  const isChunkLoadFailed = error.message.match(pattern)
  const targetPath = router.history.pending.fullPath
  if (isChunkLoadFailed) {
    router.replace(targetPath)
  }
})
Vue.config.devtools = true
Vue.use(ELEMENT)
Vue.use(derective)
// Vue.use(VueClipboards);
Object.defineProperties(Vue.prototype, {
  // 注册不同环境的HOST为vue的原型对象属性，名称为baseURL
  baseURL: {
    value: process.env.API_ROOT,
    writable: false
  }
})

new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#app')
