import Vue from 'vue'
import VueRouter from 'vue-router'
import login from '@/router/login.js'
import newIndex from '@/router/newIndex.js'
import logins from '../views/login/login.vue'
// import { nextTick } from 'vue/types/umd';
const originalPush = VueRouter.prototype.push
const originalReplace = VueRouter.prototype.replace

VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err)
}
VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch((err) => err)
}

Vue.use(VueRouter)

export const routes = [
  ...login.routers,
  {
    path: '',
    component: logins
  },
  {
    path: '/home', //主页
    name: 'home',
    component: () => import('../views/home.vue'),
    // redirect: '/home',
    children: [...newIndex.routers]
  }
]
export const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export let route = [...login.routers]
