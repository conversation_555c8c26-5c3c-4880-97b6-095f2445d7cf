import logins from '../views/login/login.vue'
export default {
  routers: [
    {
      path: "/login",
      name: "login",
      component: logins
    },
    {
      path: '/forgetPsd',
      name: "forgetPsd",
      component: () => import('../views/login/forgetPsd.vue')
    },
    {
      path: '/wxLogin',
      name: "wxlogin",
      component: () => import('../views/login/wxLogin.vue')
    },
    {
      path: '/noticePage', // 公告升级
      name: 'noticePage',
      component: () => import('../views/systemManage/noticePage.vue')
    },
    {
      path: '/upgrading', // 离线升级
      name: '升级中',
      component: () => import('../views/systemManage/upgrading.vue')
    },
  ]
}