export default {
  routers: [
    {
      path: '/workbench', //工作台
      meta: {
        bodyClassName: 'main_body_specail'
      },
      component: () => import('../views/workbench/workbench.vue')
    },
    {
      path: '/index', // 资产概览
      meta: {
        bodyClassName: 'main_body_specail'
      },
      component: () => import('../views/assetsViewIndex/index.vue')
    },

    {
      path: '/assetsLedger', // 台账
      name: 'assetsLedger',
      component: () => import('../views/assetsView/assetsLedger.vue')
    },
    {
      path: '/aaaa', // 台账
      component: () => import('../views/clueBank/clueMap.vue')
    },
    {
      path: '/personalCenter', // 个人中心
      component: () => import('../views/login/personalCenter.vue')
    },
    {
      path: '/personalCenterInfo', // 个人中心编辑
      component: () => import('../views/login/personalCenter_info.vue'),
      meta: {
        requiredQuery: true
      }
    },
    {
      path: '/assetsTaskView', // 资产探知任务概览
      component: () => import('../views/taskView/assetsTaskView.vue'),
      name: '任务概览'
    },
    {
      path: '/assetsTanzhi', // 资产探知
      name: '资产探知',
      component: () => import('../views/taskView/assetsTanzhi.vue')
    },
    {
      path: '/assetsScan', // 通过扫描发现资产
      component: () => import('../views/leakManage/leakScan.vue'),
      meta: {
        task_type: 1
      }
    },
    {
      path: '/viewDetails', //接口风险识别查看详情
      name: '查看详情',
      component: () => import('../views/apiRisk/viewDetails.vue'),
      meta: {
        requiredQuery: true
      }
    },
    {
      path: '/riskDetails', //接口风险识别查看详情
      name: '查看风险详情',
      component: () => import('../views/apiRisk/riskDetails.vue'),
      meta: {
        requiredQuery: true
      }
    },
    {
      path: '/alreadyTask_viewlist', // 通过扫描发现资产-已完成任务-查看列表
      name: '资产数据管理',
      component: () => import('../views/leakManage/task_viewlist.vue'),
      meta: {
        requiredQuery: true
      }
    },
    {
      path: '/alreadyTask_viewlist_ipinfo', // 通过扫描发现资产-已完成任务-查看列表-IP详情
      name: 'IP详情',
      component: () => import('../views/assetsView/ipinfo.vue'),
      meta: {
        requiredQuery: true,
        bodyClassName: 'main_body_unit'
      }
    },
    {
      path: '/expandLog', // 云端推荐未知资产-扩展记录
      component: () => import('../views/cloudRecommend/expandLog.vue'),
      meta: {
        requiredQuery: true
      }
    },
    {
      path: '/assetsCloud', // 云端推荐任务
      // component: () => import('../views/cloudRecommend/cloudIndex.vue'),
      component: () => import('../views/newCloud/cloudIndex.vue'),
      meta: {
        expand_source: 1 // 用于区分云端推荐和单位资产测绘的任务简报
      }
    },
    {
      path: '/scanReg', // 云端推荐未知资产-扫描核对资产
      component: () => import('../views/assetsView/assetsLedger.vue'),
      meta: {
        requiredQuery: true
      }
    },
    {
      path: '/riskAssets', // poc管理风险资产数
      component: () => import('../views/assetsView/assetsLedger.vue'),
      meta: {
        requiredQuery: true
      }
    },
    {
      path: '/unclaimCloud', // 疑似资产
      name: 'unclaimCloud',
      component: () => import('../views/assetsView/assetsLedger.vue'),
      meta: {
        scan_type: 1
      }
    },
    {
      path: '/ignoreAssets', // 已忽略资产
      component: () => import('../views/assetsView/assetsLedger.vue')
    },
    {
      path: '/threatAssets', // 标记威胁资产
      component: () => import('../views/assetsView/assetsLedger.vue')
    },
    {
      path: '/newAssets', // 数字资产
      component: () => import('../views/assets_handle/assetsLeak.vue'),
      name: 'newAssets'
    },
    {
      path: '/leakScan', // 漏洞扫描
      component: () => import('../views/leakManage/leakScan.vue'),
      meta: {
        task_type: 2
      }
    },
    {
      path: '/eventWarning', // 事件告警
      component: () => import('../views/eventWarning/eventWarning.vue'),
      name: 'eventWarning'
    },
    {
      path: '/eventInfo', // 事件告警-详情
      component: () => import('../views/eventWarning/eventInfo.vue'),
      name: 'eventInfo',
      meta: {
        requiredQuery: true
      }
    },
    {
      path: '/dataLeak', // 数据泄露
      // component: () => import('../views/assets_handle/assetsLeak.vue'),
      component: () => import('../views/assets_handle/dataLeak.vue'),
      name: 'dataLeak'
    },
    {
      path: '/repairLeakScan', // 修复漏洞管理
      component: () => import('../views/leakManage/repairLeak.vue')
    },
    {
      path: '/taskRepairLeakScan', // 已完成列表-查看列表
      component: () => import('../views/leakManage/repairLeak.vue'),
      meta: {
        requiredQuery: true
      }
    },
    {
      path: '/reportManage', // 报告管理
      component: () => import('../views/reportManage/newReport.vue') // newReport.vue：新版报告管理
    },
    {
      path: '/portManage', // 端口管理
      name: '端口管理',
      component: () => import('../views/systemManage/portManage.vue')
    },
    {
      path: '/pocManage', // poc管理
      name: 'poc管理',
      component: () => import('../views/systemManage/pocManage.vue')
    },
    {
      path: '/eventRuleManage', // 事件规则管理
      name: '事件规则管理',
      component: () => import('../views/systemManage/eventRuleManage.vue')
    },
    {
      path: '/userManage', // 用户管理
      name: '用户管理',
      component: () => import('../views/systemManage/userManage.vue')
    },
    {
      path: '/noticeManage', // 公告管理
      name: '公告管理',
      component: () => import('../views/systemManage/noticeManage.vue')
    },
    {
      path: '/ipManage', // IP段管理
      name: 'IP段管理',
      component: () => import('../views/systemManage/ipManage.vue')
    },
    // {
    //   path: '/ipBanningManage', // 黑IP封禁管理
    //   name: '黑IP封禁管理',
    //   component: () => import('../views/ipBanning/ipBanning.vue')
    // },
    {
      path: '/keywordManage', // 关键词管理
      name: '关键词管理',
      component: () => import('../views/systemManage/keywordManage.vue')
    },
    {
      path: '/loginLog', // 登录日志
      name: '登录日志',
      component: () => import('../views/systemManage/logManage.vue')
    },
    {
      path: '/zhouqiDetails', //已知资产周期详情
      name: 'zhouqiDetails',
      component: () => import('../views/leakManage/zhouqiDetails.vue')
    },
    {
      path: '/unitIndex', //单位测绘首页
      name: '单位测绘首页',
      component: () => import('../views/unit_surveying/unitIndex.vue'),
      meta: {
        bodyClassName: 'main_body_unit',
        expand_source: 0 // 用于区分云端推荐和单位资产测绘的任务简报
      }
    },
    {
      path: '/clueBlack', // 线索黑名单
      name: '线索黑名单',
      component: () => import('../views/systemManage/clueBlack.vue')
    },
    {
      path: '/loginEntry', // 线索黑名单
      name: 'loginEntry',
      component: () => import('../views/loginEntry/loginEntry.vue')
    },
    {
      path: '/certAsset', // 证书资产
      name: 'certAsset',
      component: () => import('../views/certAsset/certAssetList.vue')
    },
    {
      path: '/domainAsset', // 域名资产
      name: 'domainAsset',
      component: () => import('../views/domainAsset/domainList.vue')
    },
    {
      path: '/domainDetails', // 域名资产详情
      name: '域名详情',
      component: () => import('../views/domainAsset/domainDetails.vue')
    },
    {
      path: '/publicClueBank', // 公共线索库
      name: '公共线索库',
      meta: {
        bodyClassName: 'main_body_specail'
      },
      component: () => import('../views/clueBank/publicClueBank.vue')
    },
    {
      path: '/companyBank', // 企业线索库
      name: '企业线索库',
      component: () => import('../views/clueBank/companyBank.vue')
    },
    {
      path: '/supplyChainBank', // 企业线索库
      name: '供应链线索库',
      component: () => import('../views/clueBank/supplyChainBank.vue')
    },
    {
      path: '/checkTask', // 核查任务
      name: '核查任务',
      component: () => import('../views/checkAndStatus/checkIndex.vue')
    },
    {
      path: '/statusTask', // 状态检测
      name: '状态检测',
      component: () => import('../views/checkAndStatus/checkIndex.vue')
    },
    {
      path: '/domainTask', // 域名任务
      name: '域名发现任务',
      component: () => import('../views/domainAsset/domainTask.vue')
    },
    {
      path: '/domainTaskResult', // 域名任务
      name: '域名任务结果',
      component: () => import('../views/domainAsset/domainTaskResult.vue')
    },
    {
      path: '/upgradeCenterUploadForadar', // 升级中心foradar
      name: '升级中心',
      component: () => import('../views/upgradeCenterForadar/upgradeCenterUpload.vue')
    },
    {
      path: '/upgradeCenterUpload', // 升级中心FD01
      name: '升级中心',
      component: () => import('../views/upgradeCenter/upgradeCenterUpload.vue')
    },
    {
      path: '/screenAssets', // 资产大屏
      name: '资产大屏',
      component: () => import('../views/screenAssets/screenAssets.vue')
    },
    {
      path: '/upgradeCenter', // 域名任务
      name: '升级中心1',
      component: () => import('../views/upgradeCenter/upgradeCenter.vue')
    },
    {
      path: '/serviceManage', // 售后管理
      name: '售后管理',
      component: () => import('../views/systemManage/serviceManage.vue')
    },
    {
      path: '/pocAudit', // 漏洞审核
      name: '漏洞审核',
      component: () => import('../views/systemManage/pocAudit.vue')
    },
    {
      path: '/businessSystem', // 业务系统
      name: 'businessSystem',
      component: () => import('../views/assetsView/businessSystem.vue')
    },
    {
      path: '/clueStore', // 线索总库
      name: '线索总库',
      component: () => import('../views/clueStore/clueStore.vue')
    },
    {
      path: '/dataLeakStore', // 数据泄露总库
      name: '数据泄露总库',
      component: () => import('../views/dataLeakStore/dataLeakStore.vue')
    },
    {
      path: '/userOnline', // 在线用户
      name: '在线用户',
      component: () => import('../views/systemManage/userOnline.vue')
    },
    {
      path: '/internetManage', // 网口管理
      name: '网口管理',
      component: () => import('../views/systemManage/internetManage.vue')
    },
    {
      path: '/phishingTask', // 钓鱼仿冒任务
      name: '钓鱼仿冒任务',
      meta: {
        bodyClassName: 'main_body_unit'
      },
      component: () => import('../views/phishingTask/unitIndex.vue')
    },
    {
      path: '/tApiRisk', // 钓鱼仿冒任务
      name: '接口风险识别',
      meta: {
        bodyClassName: 'main_body_unit'
      },
      component: () => import('../views/apiRisk/unitIndex.vue')
    },
    {
      path: '/phishingTaskRecordInfo', // 钓鱼仿冒任务记录
      name: '钓鱼仿冒任务记录',
      component: () => import('../views/phishingTask/unitRecordInfo.vue')
    },
    {
      path: '/cronTask', // 定时任务
      name: '定时任务',
      component: () => import('../views/systemManage/cronTask.vue')
    },
    {
      path: '/cronTaskHistory', // 任务执行记录
      component: () => import('../views/systemManage/cronTaskHistory.vue'),
      meta: {
        requiredQuery: true
      }
    },
    {
      path: '/clueExpansionManage', // 线索扩展任务管理
      name: '线索扩展任务管理',
      component: () => import('../views/systemManage/clueExpansionManage.vue')
    },
    {
      path: '/settingManage', // 配置管理
      name: '配置管理',
      component: () => import('../views/systemManage/settingManage.vue')
    },
    {
      path: '/defaultRule', // 自定义规则
      name: '自定义规则',
      component: () => import('../views/systemManage/ruleList.vue')
    },
    {
      path: '/spatialRetrieval', // 空间检索
      name: 'spatialRetrieval',
      component: () => import('../views/spatialRetrieval')
    },
    {
      path: '/groupAssets', // 集团资产
      name: '集团资产',
      component: () => import('../views/groupAssets/groupTask'),
      meta: {
        bodyClassName: 'main_body_unit'
      }
    },
    {
      path: '/organization', // 企业架构管理
      name: 'organization',
      component: () => import('../views/groupAssets/organization/orgManagement.vue'),
      meta: {
        keepAlive: true
      }
    },
    {
      path: '/organization-clue', // 企业架构管理下的线索
      name: 'organization-clue',
      component: () => import('../views/groupAssets/organization/clueTable.vue')
    },
    {
      path: '/organization-assets', // 企业架构管理下的线索
      name: 'organization-assets',
      // keepAlive: true,
      component: () => import('../views/groupAssets/organization/assetsTable.vue')
    },
    {
      path: '/groupAssets-recommend', // 企业架构管理下的线索
      name: '集团架构梳理任务中的推荐记录',
      component: () => import('../views/groupAssets/groupTask/recommendRecord.vue')
    },
    {
      path: '/yellowGambling', // 威胁词库
      name: '威胁词库',
      component: () => import('../views/systemManage/yellowGambling.vue')
    },
    {
      path: '/icpDatabase', // ICP备案总库
      name: 'ICP备案总库',
      component: () => import('../views/systemManage/icpDatabase.vue')
    },
    {
      path: '/digitalAssets-data', // 数字资产总库的数据管理
      name: '数字资产总库的数据管理',
      component: () => import('../views/digitalAssets/data.vue')
    },
    {
      path: '/digitalAssets-task', // 数字资产总库的任务管理
      name: '数字资产总库的任务管理',
      component: () => import('../views/digitalAssets/task.vue'),
      meta: {
        keepAlive: true
      }
    },
    {
      path: '/digitalAssets-task-detail', // 数字资产总库的任务管理的详情
      name: '数字资产总库的任务管理的详情',
      component: () => import('../views/digitalAssets/taskDetailList.vue')
    },
    {
      path: '/urlAsset', // URL(API)资产
      name: 'urlAsset',
      component: () => import('../views/urlAsset/index.vue')
    },
    {
      path: '/taskBriefing', // 单位资产测绘任务简报
      name: '单位资产测绘任务简报',
      component: () => import('../views/unit_surveying/taskBriefing.vue')
    },
    {
      path: '/taskOverview', // 任务概览
      name: '任务概览',
      component: () => import('../views/taskOverview')
    },
    {
      path: '/taskViewList', // 扫描任务列表
      name: '任务列表',
      component: () => import('../views/systemManage/taskViewList.vue')
    },
    {
      path: '/intelligenceCenterv1', // 情报总览
      name: 'intelligenceCenterv1',
      component: () => import('../views/intelligenceCenterv1/index.vue')
    },
    {
      path: '/intelligenceManage', // 情报中心管理
      name: 'intelligenceManage',
      component: () => import('../views/intelligenceCenterv1/index.vue'),
      meta: {
        type: 'manage'
      }
    },
    {
      path: '/intelligenceBrief', // 情报中心概览
      name: 'intelligenceBrief',
      component: () => import('../views/intelligenceBrief/index.vue')
    },
    {
      path: '/intelligenceRelated', // 关联情报
      name: 'intelligenceRelated',
      component: () => import('../views/intelligenceRelated/index.vue')
    },
    {
      path: '/hotCheckResList', // 热点漏洞检测结果详情
      name: 'hotCheckResList',
      component: () => import('../views/intelligenceCenterv1/hotCheckResList.vue')
    },

    {
      path: '/orgOperation', // 操作管理组织结构
      name: 'orgOperation',
      component: () => import('../views/groupAssets/organization/operation.vue')
    },
    {
      path: '/stationMsg', // 站内信
      name: 'stationMsg',
      component: () => import('../views/systemManage/stationMsg.vue')
    },
    {
      path: '/dataSync', // 数据同步
      name: 'dataSync',
      component: () => import('../views/systemManage/dataSync.vue')
    },
    {
      path: '/microServe', // 站内信
      name: 'microServe',
      component: () => import('../views/systemManage/microServe.vue')
    },
    {
      path: '/clueOverview', // 线索总览
      name: 'clueOverview',
      meta: {
        bodyClassName: 'main_body_unit'
      },
      component: () => import('../views/clueOverview/clueOverview.vue')
    },
    {
      path: '/docsApi', // API 文档
      name: 'docsApi',
      component: () => import('../views/apiDocs/apiDocs.vue')
    }
  ]
}
