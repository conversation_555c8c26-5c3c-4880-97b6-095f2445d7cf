import basurl from '@/utils/request'
const messageText = (data) => {
  if (!data.message) return
  Vue.prototype.$message({
    type: data.status == 200 || data.status ? 'success' : 'warning',
    message: data.message
  })
}
var actions = {
  initWebSocket({ state, commit, dispatch, rootState }) {
    if (!localStorage.getItem('websocketToken')) return
    console.log('初始化websocket连接成功')
    // 登录后初始化websocket连接 暴露出去
    console.log("basurl.defaults.wsIpGolang", basurl.defaults.wsIpGolang)
    commit(
      'setWebSocket',
      new WebSocket(basurl.defaults.wsIpGolang + localStorage.getItem('websocketToken'))
    )
    state.websocket.onopen = () => {
      console.log('websocket连接成功')
    }
    state.websocket.onerror = () => {
      dispatch('initWebSocket')
    }
    state.websocket.onmessage = (e) => {
      if (!e.data) {
        return
      }
      let res = JSON.parse(e.data)
      if (res.status == 200 && res.data) {
        commit('setSocketMessage', JSON.parse(e.data)) //把信息存到vuex里面，再分发到对应页面
      }
    }
    state.websocket.onclose = (e) => {
      // 关闭连接
      console.log('websocket连接关闭')
    }
    state.sendTimeoutObj && clearInterval(state.sendTimeoutObj)
    state.sendTimeoutObj = setInterval(() => {
      if (state.websocket.readyState != 1) {
        // 链接ws服务器，e.target.readyState = 0/1/2/3   0 CONNECTING ,1 OPEN, 2 CLOSING, 3 CLOSED
        dispatch('initWebSocket')
      } else {
        state.websocket.send(JSON.stringify({ data: '', cmd: 'ping' }))
      }
    }, 60 * 1000)
  }
}
export default actions
