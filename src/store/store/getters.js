var getters = {
  // routes:state=>state.routes,
  getterWebsocketMessage(state) {
    return state.websocketMessage;
  },
  getterSocketTimestamp(state) {
    return state.socketTimestamp;
  },
  getterCurrentCompany(state) {
    return state.currentCompany;
  },
  getterCompanyChange(state) {
    return state.companyChange;
  },
  getterCompanyId(state) {
    return state.currentCompanyId;
  },
  getterSettime(state) {
    return state.setTimeData;
  },
  getterSettimeBox(state) {
    return state.setTimeBox;
  },
  getterChangeAfterCompany(state) {
    return state.changeAfterCompany;
  },
  getterChangeTime(state) {
    return state.time;
  },
  getterRecommentFlag(state) {
    return state.recommentFlag;
  },
  getterMenuId(state) {
    return state.menuId;
  },
  getterRecommentData(state) {
    return state.recommentData;
  },
  getterScrollHeight(state) {
    return state.scrollHeight;
  }
};
export default getters;
