var mutations = {
  setRoutes(state, routers) {
    state.routes = routers
  }, //放入他的权限的路由
  //websocket
  setSocketMessage(state, data) {
    //一旦获取到端端返回的信息，就改变时间戳
    state.socketTimestamp = new Date().getTime()
    state.websocketMessage = data
  },
  setCurrentCompany(state, data) {
    if( data == '' && typeof data == 'String' ){
      state.currentCompany = 0
    }else {
      state.currentCompany = data
    }
  },
  setCompanyChange(state, data) {
    state.companyChange = data
  },
  // 存储企业本身id以及安服切换企业的id
  setOwnerIdChange(state, data) {
    state.currentCompanyId = data
  },
  setTimeChange(state, data) {
    state.setTimeData = data
  },
  setTimeChangeBox(state, data) {
    state.setTimeBox = data
  },
  changeCompany(state, data) {
    state.changeAfterCompany = data
  },
  changeTime(state, data) {
    state.time = data
  },
  recommentFlagChange(state, data) {
    state.recommentFlag = data
  },
  changeMenuId(state, data) {
    state.menuId = data
  },
  saveRecommentData(state, data) {
    state.recommentData = data
  },
  toggleSidebar(state, data) {
    state.sidebarOpened = data
  },
  setJumpScrollHeight(state, data) {
    // 资产概览页面 跳转其他列表页面返回上一层 重置滚动位置
    state.scrollHeight = data
  },
  setWebSocket(state, data) {
    state.websocket = data
  },
  // 停止心跳
  stopHeartbeat(state) {
    state.timeoutInterval = null
    clearTimeout(state.timeoutInterval) // 清除超时计时器
  }
}

export default mutations
