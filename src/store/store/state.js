var state = {
  routes: {}, //路由
  websocketMessage: '', // 端返回的信息
  socketTimestamp: '', // 时间戳去监听数据的改变
  currentCompany: 0, // 安服角色选择的企业
  currentCompanyItem: '', // 安服角色选择的企业
  companyChange: '', // 切换了企业
  currentCompanyId: '', // 切换企业id
  setTimeData: false, //禁扫重新请求接口参数
  setTimeBox: false, //禁扫弹框
  changeAfterCompany: '', // 修改后的企业名称
  time: '', //监控ip、端口、组件总数
  recommentFlag: '', // 单位资产测绘云端推荐flag
  menuId: '', //左菜单栏
  recommentData: '', // 企业线索库要下发推荐的资产
  sidebarOpened: true,
  scrollHeight: 0, // 资产概览页面 跳转其他列表页面返回上一层 重置滚动位置
  websocket: null // websocket连接
}

export default state
