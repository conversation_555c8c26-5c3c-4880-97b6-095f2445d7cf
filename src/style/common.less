body,
div,
span,
header,
footer,
nav,
section,
aside,
article,
ul,
dl,
dt,
dd,
li,
a,
p,
h1,
h2,
h3,
h4,
h5,
h6,
i,
b,
textarea,
button,
input,
select,
figure,
figcaption {
  padding: 0;
  margin: 0;
  list-style: none !important;
  font-style: normal;
  text-decoration: none;
  border: none;
  // font-family: "Microsoft Yahei",sans-serif;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Helvetica Neue,
    PingFang SC,
    Microsoft YaHei,
    Source Han <PERSON>s SC,
    Noto Sans CJK SC,
    WenQuanYi Micro Hei,
    sans-serif;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;
  &:focus {
    outline: none;
  }
}
* {
  box-sizing: content-box;
}
input[type='button'],
input[type='submit'],
input[type='search'],
input[type='reset'] {
  appearance: none;
  -webkit-appearance: none;
}

textarea {
  appearance: none;
  -webkit-appearance: none;
}
img {
  image-rendering: -moz-crisp-edges; /* Firefox */
  image-rendering: -o-crisp-edges; /* Opera */
  image-rendering: -webkit-optimize-contrast; /*Webkit (non-standard naming) */
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor; /* IE (non-standard property) */
}
.loginClass input::-webkit-input-placeholder {
  color: #98a8c2 !important;
}

.loginClass input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #98a8c2 !important;
}

.loginClass input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #98a8c2 !important;
}

.loginClass input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #98a8c2 !important;
}

html,
body {
  height: 100%;
  width: 100%;
}
.el-button--primary.is-disabled,
.el-button--primary.is-disabled:active,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:hover {
  background-color: #a0cfff;
  border-color: transparent;
  color: #fff;
}

.normalBtn {
  min-width: 76px;
  height: 32px;
  background: #2677ff;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  // line-height: 32px;
  text-align: center;
  padding: 0 12px;
  border: 0;
  margin: 0;
  margin-left: 12px;
  border: 1px solid #2677ff;
  &:hover {
    background-color: #4389ff !important;
    border-color: #2677ff !important;
    color: #fff;
  }
  &:focus {
    background-color: #2677ff;
    border-color: #2677ff;
    color: #fff;
  }
}
.normalBtnAA {
  height: 32px;
  background: #ccd2db !important;
  border-radius: 4px;
  border: 1px solid;
  border-color: #ccd2db !important;
  font-weight: 400;
  color: #fff !important;
  // line-height: 32px;
  text-align: center;
  padding: 0 12px;
}
.normalBtnRe {
  min-width: 76px;
  height: 32px;
  background: #ffffff !important;
  border-radius: 4px;
  border: 1px solid;
  border-color: #d1d5dd !important;
  font-weight: 400;
  color: #62666c !important;
  // line-height: 32px;
  text-align: center;
  padding: 0 12px;
  &:hover {
    background-color: #f0f3f8 !important;
    border-color: #c0c4cc !important;
    color: #62666c !important;
  }
  &:focus {
    background-color: #ffffff;
    border-color: #d1d5dd;
    color: #62666c;
  }
}
.highBtn {
  width: 68px;
  height: 32px;
  background: #2677ff;
  border-radius: 4px;
  color: #fff;
  // line-height: 32px;
  text-align: center;
  padding: 0;
  border: 0;
  margin: 0;
  margin-left: 12px;
  border: 1px solid #2677ff;
  &:hover {
    background-color: #4389ff !important;
    border-color: #2677ff !important;
    color: #fff;
  }
  &:focus {
    background-color: #2677ff;
    border-color: #2677ff;
    color: #fff;
  }
}
.highBtnRe {
  width: 68px;
  height: 32px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #d1d5dd;
  font-weight: 400;
  color: #62666c;
  // line-height: 32px;
  text-align: center;
  padding: 0;
  // margin-right: 12px;
  &:hover {
    background-color: #f0f3f8;
    border-color: #c0c4cc !important;
    color: #62666c;
  }
  &:focus {
    background-color: #ffffff;
    border-color: #d1d5dd;
    color: #62666c;
  }
}
.checkboxAll {
  margin-right: 15px !important;
}
.el-notification__content .el-button {
  margin-left: 184px !important;
}

.headerTitle {
  position: absolute;
  top: -39px;
  left: 0;
  font-weight: 700;
  // font-size: 16px;
  font-size: 14px;
  color: #37393c;
  .headerShow {
    display: inline-block;
    padding: 0 20px;
    height: 28px;
    line-height: 28px;
    color: rgba(98, 102, 108, 1);
    font-size: 12px;
    font-weight: normal;
    background: url(../assets/images/headerShow.png);
    background-size: 100% 100%;
    i {
      color: rgba(146, 187, 255, 1);
      font-size: 16px;
      margin-right: 5px;
    }
  }
  .goback {
    font-weight: 700;
    color: #2677ff;
    font-size: 14px;
    cursor: pointer;
    .el-icon-arrow-left {
      font-weight: 700;
    }
  }
  .spline {
    margin: 0 8px;
    color: #62666c;
  }
  & > p {
    display: inline-block;
    margin-left: 16px;
    font-size: 12px;
    color: #62666c;
    span {
      margin-right: 12px;
    }
  }
}
.tipImgs {
  width: 40px;
  height: 40px;
}
.tips {
  width: 105px;
  height: 104px;
  background: linear-gradient(180deg, rgba(20, 60, 127, 0.2) 0%, rgba(20, 60, 127, 0.5) 100%);
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.12);
  border: 1px solid
    linear-gradient(0deg, rgba(210, 227, 255, 1), rgba(66, 137, 255, 1), rgba(38, 119, 255, 1)) 1 1;
  backdrop-filter: blur(4px);
  color: #e3eeff;
  p {
    margin-left: 12px;
    span {
      color: #ffffff;
    }
  }
  .tips-title {
    font-size: 16px;
    margin-top: 8px;
  }
  .tips-title1 {
    font-size: 14px;
    margin-top: 12px;
  }
  .tips-title2 {
    font-size: 14px;
    margin-top: 12px;
  }
}
.tips1 {
  height: 30px;
  box-sizing: border-box;
  // line-height: 30px;
  display: flex;
  align-items: center;
  padding: 10px;
  color: #ffff;
  background: linear-gradient(180deg, rgba(20, 60, 127, 0.2) 0%, rgba(20, 60, 127, 0.5) 100%);
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.12);
  border: 1px solid;
  border-image: linear-gradient(
      0deg,
      rgba(210, 227, 255, 1),
      rgba(66, 137, 255, 1),
      rgba(38, 119, 255, 1)
    )
    1 1;
  backdrop-filter: blur(4px);
}
.el-input__inner {
  width: 100%;
  height: 32px;
  line-height: 32px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #d1d5dd;
}
.el-form {
  .el-select {
    width: 100%;
    // height: 32px;
    line-height: 32px;
    background: #ffffff;
    border-radius: 4px;
    /deep/.el-input .el-input__inner {
      width: 100%;
      // height: 100% !important;
      height: 100%;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d1d5dd;
    }
  }
  .el-input__icon {
    line-height: 32px;
  }
}
.dividerLine {
  width: 1px;
  height: 100%;
  margin-left: 20px;
  background: #e9ebef;
}
.el-dialog {
  .el-form-item {
    margin-bottom: 16px !important;
    .el-form-item__label {
      line-height: 40px !important;
    }
  }
  .spideLine {
    display: block;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ebef;
  }
}
.el-input-group--append {
  border: 1px solid #d1d5dd;
  border-radius: 4px;
  .el-input__inner {
    border: 0;
  }
}
.el-input-group__prepend {
  padding: 0 5px;
  border: 0;
  border-radius: none;
  .el-icon-question {
    font-size: 16px;
    color: rgb(177, 189, 209);
  }
}
.el-input-group__append {
  padding: 0 5px;
  background: #ffffff;
  border: 0;
  .el-button {
    padding: 0 !important;
  }
}
.overTaskClass {
  color: #62666c;
  cursor: pointer;
}
// label的灰框
.grayBox {
  padding: 0px 20px;
  padding-top: 10px;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  justify-content: space-between;
  .graySan {
    margin-left: 14px;
    width: 12px;
  }
  .graylabel {
    background-color: #ebeef5;
    font-size: 12px;
    color: #62666c;
    padding: 6px 8px;
    margin-left: -3px;
  }
}
.grayBox > div:first-child {
  display: flex;
  align-items: center;
}
.confirmBox {
  display: flex;
  align-items: center;
  background: #e1e7f1;
  border-radius: 4px;
  margin-left: 16px;
  height: 32px;
  padding: 0px !important;
  overflow: hidden;
  .el-radio-group {
    display: flex !important;
  }
  .el-radio-button__inner {
    border: 0 !important;
    height: 32px !important;
    line-height: 32px !important;
    text-align: center;
    background: #e1e7f1;
    color: #62666c;
    font-size: 14px;
    border-radius: 0px !important;
    padding: 0px 16px !important;
    // width: 60px;
  }
  .is-active {
    .el-radio-button__inner {
      background: #2677ff !important;
      color: #ffffff !important;
      font-weight: 500 !important;
      margin-left: 0px !important;
    }
  }
}
::-webkit-scrollbar-thumb {
  border-radius: 4px !important;
}
// 系统按钮标签样式
.blueLine {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
  box-sizing: border-box;
  background: rgba(38, 119, 255, 0.12);
  color: rgba(38, 119, 255, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.greenLine {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
  box-sizing: border-box;
  background: rgba(16, 213, 149, 0.12);
  color: #10d595;
  margin-left: 8px;
  vertical-align: middle;
}
.grayLine {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
  box-sizing: border-box;
  background: rgba(233, 235, 239, 1);
  color: rgba(98, 102, 108, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.originLine {
  display: inline-block !important;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
  box-sizing: border-box;
  background: rgba(255, 121, 0, 0.12);
  color: rgba(255, 121, 0, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.yellowLine {
  display: inline-block !important;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
  box-sizing: border-box;
  background: rgba(248, 193, 54, 0.16);
  color: rgba(248, 193, 54, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.redLine {
  display: inline-block !important;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
  box-sizing: border-box;
  background: rgba(255, 70, 70, 0.12);
  color: rgba(255, 70, 70, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.deepRedLine {
  display: inline-block !important;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
  box-sizing: border-box;
  background: rgba(153, 5, 4, 0.12);
  color: rgba(153, 5, 4, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.blackBorderLine {
  display: inline-block !important;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
  box-sizing: border-box;
  // background: black;
  border: 3px solid black;
  color: black;
  margin-left: 8px;
  vertical-align: middle;
}
.deepRedRadiusBorder {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 12px;
  box-sizing: border-box;
  background: rgba(153, 5, 4, 0.12);
  color: rgba(153, 5, 4, 1);
  border: 1px solid rgba(153, 5, 4, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.originRadiusBorder {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 12px;
  box-sizing: border-box;
  background: rgba(255, 121, 0, 0.12);
  color: rgba(255, 121, 0, 1);
  border: 1px solid rgba(255, 121, 0, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.deepDeepRadiusBorder {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 12px;
  box-sizing: border-box;
  background: rgba(255, 60, 0, 0.12);
  color: rgba(255, 121, 0, 1);
  border: 1px solid rgba(255, 121, 0, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.redRadiusBorder {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 12px;
  box-sizing: border-box;
  background: rgba(255, 70, 70, 0.12);
  color: rgba(255, 70, 70, 1);
  border: 1px solid rgba(255, 70, 70, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.blueRadiusBorder {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 12px;
  box-sizing: border-box;
  background: rgba(38, 119, 255, 0.12);
  color: rgba(38, 119, 255, 1);
  border: 1px solid rgba(38, 118.99999999999994, 255, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.greenRadiusBorder {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 12px;
  box-sizing: border-box;
  background: rgba(16, 213, 149, 0.12);
  color: #10d595;
  border: 1px solid rgba(16, 213, 149, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.grayRadiusBorder {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 12px;
  box-sizing: border-box;
  background: rgba(233, 235, 239, 1);
  color: rgba(98, 102, 108, 1);
  border: 1px solid rgba(209, 213, 221, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.yellowRadiusBorder {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 12px;
  box-sizing: border-box;
  background: rgba(248, 193, 54, 0.16);
  color: rgba(248, 193, 54, 1);
  border: 1px solid rgba(248, 193, 54, 1);
  margin-left: 8px;
  vertical-align: middle;
}
.maxWidthContent {
  display: inline-block;
  max-width: 80%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}

.clickPointer {
  &:hover {
    cursor: pointer;
  }
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.numWrap {
  position: absolute;
  right: 0;
  top: -45px;

  .waitNum {
    display: inline-block;
    font-size: 14px;
    font-weight: 400;
    color: #62666c;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 4px 10px;
    margin-left: 10px;
    border-left: 2px solid #2677ff;

    .num {
      font-weight: 500;
      color: #2677ff;
      margin-right: 0px;
    }
  }
}
