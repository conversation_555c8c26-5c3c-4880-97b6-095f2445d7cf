// 详情返回操作样式
.navigationBack {
  width: 100%;
  height: 52px;
  line-height: 52px;
  margin-bottom: 0.05rem;
  background: #f3f3f3;
  box-shadow: 4px 4px 16px 0px rgba(6, 33, 64, 0.08);
  .iconfont {
    padding-left: 28px;
    padding-right: 6px;
    font-size: 14px;
    font-weight: 800;
    color: #676767;
    cursor: pointer;
  }
  .title {
    // margin-left: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #3d3d3d;
    cursor: pointer;
  }
}
// .el-select__tags {
//   max-width: 100% !important;
//   display: flex;
//   left: 10px;
// }
.el-tag.el-tag--info {
  max-width: 70% !important;
}
.unitCompany {
  display: inline-block;
  max-width: 290px;
  margin-right: 15px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
img {
  image-rendering: -moz-crisp-edges; /* Firefox */
  image-rendering: -o-crisp-edges; /* Opera */
  image-rendering: -webkit-optimize-contrast; /*Webkit (non-standard naming) */
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor; /* IE (non-standard property) */
}
.placeholderClass {
  color: #c0c4cc;
}
.el-image-viewer__mask {
  background: rgba(0, 0, 0, 0.7) !important;
  opacity: 1;
}
.myruleItemBox {
  margin-top: 12px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.myruleItem {
  height: 22px;
  line-height: 20px;
  padding: 0 8px;
  border-radius: 12px;
  box-sizing: border-box;
  line-height: 16px;
  margin-bottom: 10px;
  background: #ffffff;
  border: 1px solid #d1d5dd;
  white-space: pre-wrap;
  margin-right: 8px;
  // cursor: pointer;
}
.v-modal {
  background: rgba(0, 0, 0, 0.7) !important;
}
.el-tooltip__popper {
  max-width: 70%;
  max-height: 500px;
  overflow: auto;
  background: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(2px);
  color: #37393c !important;
  border: 0 !important;
  .popper__arrow {
    display: none;
    bottom: 0 !important;
  }
}
// .el-tooltip__popper[x-placement^=top] .popper__arrow,
// .el-tooltip__popper[x-placement^=top] .popper__arrow:after {
//   bottom: 0px !important; /* 把三角形往上移动 */
// }
.imgwrap {
  display: inline-block;
  padding: 5px;
  background: rgb(245, 247, 250);
}
.rulePopover {
  max-height: 172px;
  overflow: auto;
  line-height: normal !important;
  box-sizing: border-box !important;
  .popper__arrow {
    display: none;
    bottom: 0 !important;
  }
}
.el-message-box .el-message-box__content {
  padding-bottom: 16px !important;
}
.el-message-box__message p {
  max-height: 300px;
  overflow: auto;
}
.ruleItemBox {
  display: flex;
  flex-wrap: wrap !important;
  padding-right: 12px !important;
  padding-left: 12px !important;
}
.ruleItem,
.ruleItemNum {
  max-width: 120px;
  line-height: 16px;
  padding: 2px 10px;
  margin: 5px 8px 5px 0px;
  background: #ffffff;
  border-radius: 14px;
  border: 1px solid #d1d5dd;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.ruleItemm {
  cursor: unset !important;
}
.ruleItemNum {
  display: inline-block;
  background: #f0f3f8;
  border: 1px solid #dce5f3;
}
.reasonClass {
  img {
    width: 20px !important;
    height: 20px !important;
  }
}
.chainClass {
  max-height: 500px;
  min-width: 20%;
  overflow: auto;
  border: 1px solid #dce5f3 !important;
  .userInfo {
    p {
      line-height: 24px;
      font-size: 14px;
    }
  }
  .popper__arrow {
    display: none;
    bottom: 0 !important;
  }
  .iconRight {
    color: #2677ff;
    font-weight: bold;
  }
  span {
    display: inline-block;
    line-height: 24px;
    .el-image {
      vertical-align: middle;
    }
  }
  .recordClass {
    li {
      padding-bottom: 10px;
      p {
        line-height: 24px;
        font-weight: bold;
        color: #2677ff;
      }
      .recordItem {
        display: inline-block;
        width: 19%;
        min-width: 120px;
        text-overflow: inherit;
        padding: 2px 2px;
        margin: 1px 1px;
        border: 1px solid #ebeef5;
        background: rgb(245, 247, 250);
        span {
          display: inline-block;
          width: 100%;
          white-space: pre-wrap;
        }
      }
    }
  }
  img {
    margin-bottom: 5px !important;
  }
  i {
    display: inline-block;
    line-height: 30px;
  }
}
.infoTitle {
  height: 54px;
  line-height: 54px;
  padding-left: 20px;
  font-weight: bold;
  color: #37393c;
  border-bottom: 1px solid #e9ebef;
  background: #fff;
}
.el-table__body {
  width: 100%;
  // 使表格兼容safari，不错位
  table-layout: fixed !important;
}
.dialog-body {
  .downloadClass {
    width: 100%;
    height: 40px;
    line-height: 40px;
    margin-bottom: 16px;
    background: rgba(38, 119, 255, 0.18);
    border-radius: 2px;
    border: 1px solid rgba(38, 119, 255, 0.44);
    cursor: pointer;
    i {
      font-size: 14px;
      color: #2677ff;
      margin: 0 8px 0 16px;
    }
    span {
      color: #2677ff;
    }
  }
}
.el-image {
  width: 30px;
  height: 30px;
  margin: 5px 0;
  img {
    vertical-align: top !important;
  }
  .el-icon-picture-outline {
    font-size: 30px;
    color: #d2d7e0;
    font-weight: 200;
  }
}
.el-select-dropdown {
  max-width: 50%;
}
.el-textarea .el-input__count {
  background: transparent;
  bottom: -4px;
  right: 7px;
}
// 折叠面板样式
.formCollapse {
  .el-collapse-item__header {
    padding: 0 30px !important;
    border-bottom: 1px solid #e6e8f0 !important;
  }
  .el-collapse-item__wrap {
    padding: 30px 62px !important;
  }
  // 所有表单的高度等通用设置
  .el-form-item__content {
    // margin-left: 60px !important;
    .el-input__inner {
      // height: 32px;
      height: 32px !important;
      line-height: 32px !important;
    }
    .el-select {
      width: 100%;
    }
    .el-date-editor.el-input {
      width: 100%;
    }
    .el-cascader {
      width: 100%;
    }
  }
  .el-table {
    width: 100%;
    /deep/ th {
      padding: 0 !important;
    }
    /deep/ td {
      padding: 0 !important;
    }
  }
}
.successStatus {
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-right: 5px;
  border-radius: 50%;
  background: #0ac986;
}
.errorStatus {
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-right: 5px;
  border-radius: 50%;
  background: #f74b4b;
}
.waitStatus {
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-right: 5px;
  border-radius: 50%;
  background: #fc7d2f;
}
// 企业填报高级查询
.el-drawer.rtl {
  width: 398px !important;
  .el-drawer__header {
    height: 52px !important;
    line-height: 52px !important;
    padding: 0 16px !important;
    margin-bottom: 0;
    color: #37393c;
    font-weight: 400;
    background: #f5f7fa !important;
  }
  .el-drawer__body {
    height: 100%;
  }
  .demo-drawer__content {
    //height: 93%;
    height: calc(100% - 65px);
    overflow-y: auto;
  }
  .demo-drawer__footer {
    position: absolute;
    bottom: 0;
    width: calc(100% - 32px);
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #e9ebef;
    padding: 16px;
    background: #fff;
  }
  .el-form {
    padding: 24px !important;
    .el-form-item {
      width: 100%;
      margin-bottom: 20px;
      .el-form-item__label {
        //width: 100%;
        //text-align: left;
        line-height: 32px;
      }
      .el-form-item__content {
        //margin-left: 0 !important;
        line-height: 28px !important;
        .el-input__inner {
          height: 32px;
          // height: 32px !important;
          line-height: 32px;
        }
        .el-input__icon {
          line-height: 32px;
        }
        .el-range__close-icon {
          line-height: 26px;
        }
        .el-cascader {
          width: 100%;
        }
        .el-range-editor.el-input__inner {
          width: 100%;
        }
        .el-date-picker {
          width: 100% !important;
        }
        .el-radio-group {
          width: 100%;
        }
        .el-checkbox-group {
          width: 100%;
        }
        .el-select {
          width: 100%;
        }
        .el-date-editor.el-input {
          width: 100%;
        }
        .el-date-editor .el-range__icon {
          height: 32px;
          line-height: 32px;
        }
        .el-date-editor .el-range-separator {
          height: auto;
        }
        .el-cascader .el-input__inner {
          height: 32px !important;
        }
      }
    }
  }
}
.el-form {
  padding: 0 24px !important;
  .el-form-item {
    width: 100%;
    margin-bottom: 24px;
    .el-form-item__label {
      line-height: 32px;
    }
    .el-form-item__content {
      // line-height: 32px;
      .el-input__inner {
        height: 32px;
        line-height: 32px;
      }
      .el-input__icon {
        line-height: 32px;
      }
      .el-range__close-icon {
        line-height: 26px;
      }
      .el-cascader {
        width: 100%;
      }
      .el-input-number {
        width: 100%;
        .el-input-number__decrease,
        .el-input-number__increase {
          top: 4px;
          height: 32px;
          line-height: 32px;
        }
      }
      .el-range-editor.el-input__inner {
        width: 100%;
      }
      .el-date-picker {
        width: 100% !important;
      }
      .el-radio-group {
        width: 100%;
      }
      .el-checkbox-group {
        width: 100%;
      }
      .el-select {
        width: 100%;
      }
      .el-date-editor.el-input {
        width: 100%;
      }
      .el-date-editor .el-range__icon {
        height: 32px;
        line-height: 32px;
      }
    }
  }
}
.iconStatus {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 4px;
}
.greenStatus {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 4px;
  background: #1bec9e;
}
.grayStatus {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 4px;
  background: #acb4c0;
}
.redStatus {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 4px;
  background: #e94747;
}
.yellowStatus {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 4px;
  background: #e6a23c;
}
.blueStatus {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 4px;
  background: #2677ff;
}
.dwstatus {
  background: #ffc003;
}
.gwstatus {
  background: #ff0000;
}
.zwstatus {
  background: #f79645;
}
.yzstatus {
  background: #990504;
}
.radioClass {
  .el-form-item__label {
    width: 100px !important;
    text-align: left;
    line-height: 32px;
  }
}
.el-transfer-panel__list.is-filterable {
  height: 87%;
}
.el-tabs__nav {
  padding-left: 4px;
}
.el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
  // min-width: 60px;
  padding: 0 16px;
  height: 44px;
  text-align: center;
  line-height: 44px;
  font-weight: bold;
  color: #2677ff;
  background: rgba(38, 119, 255, 0.08);
}
.el-tabs__active-bar {
  left: 4px;
  width: 100%;
  padding: 0 16px;
  background: #2677ff;
}
.el-tabs__header {
  margin: 0;
  background: #fff;
}
.el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #e9ebef;
}
.el-tabs__item {
  // min-width: 72px;
  padding: 0 16px;
  height: 44px;
  text-align: center;
  line-height: 44px;
  // padding: 0;
  font-size: 14px;
  font-weight: 400;
  color: #62666c;
}
.dialog_topcontent {
  height: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  & > div {
    height: 32px;
    .el-select {
      width: 240px;
      // height: 32px;
      line-height: 32px;
      background: #ffffff;
      border-radius: 4px;
      border-color: #d1d5dd;
      .el-input__suffix {
        line-height: 32px;
        .el-input__icon {
          line-height: 32px;
        }
      }
    }
    .checkboxAll {
      margin-right: 8px !important;
    }
    .el-radio-button:nth-child(1) {
      margin: 0 12px 0 20px;
    }
    .el-radio-button {
      .el-radio-button__inner {
        display: inline-block;
        padding: 0;
        width: 94px;
        height: 32px;
        color: #62666c;
        line-height: 32px;
        border: 1px solid transparent;
        background: #f5f7fa;
        border-radius: 2px;
      }
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: rgba(38, 119, 255, 0.08);
      border-radius: 2px;
      color: #2677ff;
      box-shadow: none;
      border-color: #2677ff;
    }
  }
}
.elDialogAddLiji {
  display: flex;
  align-items: center;
  .el-dialog {
    margin-top: 0px !important;
    border-radius: 4px;
    .el-dialog__header {
      height: 44px;
      background: #f5f7fa;
      display: flex;
      align-items: center;
      padding: 0 16px !important;
      color: #37393c !important;
      font-size: 14px !important;
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid #e9ebef;
      .el-dialog__headerbtn {
        top: 12px;
      }
    }
    .el-dialog__footer {
      padding: 16px;
      background: #fff;
      border-top: 1px solid #e9ebef;
    }
  }
}
// 初次登录弹层
.elDialogAddFirstLogin {
  .el-dialog__header {
    height: 44px;
    background: #f5f7fa;
    display: flex;
    align-items: center;
    padding: 0 14px !important;
    color: #37393c !important;
    font-size: 14px !important;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #e9ebef;
    .el-dialog__headerbtn {
      top: 12px;
    }
    .el-dialog__title {
      font-size: 14px;
      color: #37393c;
    }
  }
}
// 所有新增弹出层样式
.elDialogAdd {
  .el-dialog {
    // width: 53%;
    // height: 73%;
    border-radius: 4px;
    .el-form {
      .el-input__inner {
        height: 32px;
        // height: 32px !important;
        line-height: 32px !important;
      }
      .el-form-item {
        width: 100%;
        margin-bottom: 20px !important;
        .el-input__icon {
          line-height: 32px;
        }
        .el-select {
          width: 100%;
        }
        .el-date-editor.el-input {
          width: 100%;
        }
        .el-form-item__label,
        .el-form-item__content {
          line-height: 32px !important;
        }
      }
    }
    .el-dialog__body {
      padding: 28px 28px 0 28px;
      min-height: 300px;
      max-height: 500px;
      overflow: auto;
      background: #fff;
      .dialog-body {
        .el-tabs__nav {
          height: 33px;
          border-radius: 4px 4px 0px 0px;
          border: 1px solid #e4e7ed;
          margin-bottom: 16px;
        }
        .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
          // width: 72px;
          text-align: center;
          background: rgba(38, 119, 255, 0.1);
          border-radius: 0;
          border: 1px solid #2677ff;
          font-weight: bold;
          color: #2677ff;
          background: rgba(38, 119, 255, 0.08);
        }
        .el-tabs__active-bar {
          left: 4px;
          width: 100%;
          padding: 0 16px;
          background: #2677ff;
        }
        .el-tabs__header {
          margin: 0;
        }
        .el-tabs__nav-wrap::after {
          height: 1px;
          background-color: #e9ebef;
        }
        .el-tabs__item {
          padding: 0 16px;
          height: 32px;
          text-align: center;
          line-height: 32px;
          font-size: 14px;
          font-weight: 400;
          color: #62666c;
          transition: none;
        }
      }
    }
    .el-dialog__header {
      height: 44px;
      background: #f5f7fa;
      display: flex;
      align-items: center;
      padding: 0 14px !important;
      color: #37393c !important;
      font-size: 14px !important;
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid #e9ebef;
      .el-dialog__headerbtn {
        top: 12px;
      }
      .el-dialog__title {
        font-size: 14px;
        color: #37393c;
      }
    }
    .el-transfer {
      // height: 379px;
      // height: 100%;
      display: flex;
      justify-content: center;
    }
    .el-transfer-panel {
      width: 226px;
      // height: 379px;
      height: 100%;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d1d5dd;
    }
    // .el-transfer-panel .el-transfer-panel__header {
    //   display: none;
    // }
    .el-transfer-panel__body {
      height: calc(100% - 40px);
      .el-transfer-panel__filter {
        margin: 12px;
        .el-input__inner {
          // width: 202px;
          height: 32px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #d1d5dd;
        }
      }
    }
    .el-transfer__buttons {
      padding: 0 8px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .el-button + .el-button {
        margin: 0;
      }
      .el-button--primary {
        width: 24px;
        height: 24px;
        // color: #62666c;
        text-align: center;
        line-height: 24px;
        // background: rgba(209, 213, 221, 0.28);
        border-radius: 4px;
        // border: 1px solid rgba(209, 213, 221, 0.53);
        &.is-disabled {
          color: #62666c;
          background: rgba(209, 213, 221, 0.28);
          border: 1px solid rgba(209, 213, 221, 0.53);
        }
      }
      .el-button {
        padding: 0;
      }
    }
    .el-dialog__footer {
      padding: 16px;
      background: #fff;
      border-top: 1px solid #e9ebef;
    }
    .shuxian {
      display: inline-block;
      width: 4px;
      height: 20px;
      background: #2677ff;
      margin-right: 5px;
    }
  }
}
.elDialogAdd-claim {
  z-index: 10000 !important;
}
// 矫正之前弹窗设置最小宽高缺陷
.elDialogAddNewV1 {
  .el-dialog {
    // width: 53%;
    // height: 73%;
    border-radius: 4px;
    .el-form {
      .el-input__inner {
        height: 32px;
        // height: 32px !important;
        line-height: 32px !important;
      }
      .el-form-item {
        width: 100%;
        margin-bottom: 20px !important;
        .el-input__icon {
          line-height: 32px;
        }
        .el-select {
          width: 100%;
        }
        .el-date-editor.el-input {
          width: 100%;
        }
        .el-form-item__label,
        .el-form-item__content {
          line-height: 32px !important;
        }
      }
    }
    .el-dialog__body {
      padding: 28px 28px 0 28px;
      min-height: 300px;
      max-height: 500px;
      overflow: auto;
      background: #fff;
      .dialog-body {
        .el-tabs__nav {
          height: 33px;
          border-radius: 4px 4px 0px 0px;
          border: 1px solid #e4e7ed;
          margin-bottom: 16px;
        }
        .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
          text-align: center;
          background: rgba(38, 119, 255, 0.1);
          border-radius: 0;
          border: 1px solid #2677ff;
          font-weight: bold;
          color: #2677ff;
          background: rgba(38, 119, 255, 0.08);
        }
        .el-tabs__active-bar {
          left: 4px;
          width: 100%;
          padding: 0 16px;
          background: #2677ff;
        }
        .el-tabs__header {
          margin: 0;
        }
        .el-tabs__nav-wrap::after {
          height: 1px;
          background-color: #e9ebef;
        }
        .el-tabs__item {
          padding: 0 16px;
          height: 32px;
          text-align: center;
          line-height: 32px;
          font-size: 14px;
          font-weight: 400;
          color: #62666c;
          transition: none;
        }
      }
    }
    .el-dialog__header {
      height: 44px;
      background: #f5f7fa;
      display: flex;
      align-items: center;
      padding: 0 14px !important;
      color: #37393c !important;
      font-size: 14px !important;
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid #e9ebef;
      .el-dialog__headerbtn {
        top: 12px;
      }
      .el-dialog__title {
        font-size: 14px;
        color: #37393c;
      }
    }
    .el-transfer {
      // height: 379px;
      // height: 100%;
      display: flex;
      justify-content: center;
    }
    .el-transfer-panel {
      width: 226px;
      // height: 379px;
      height: 100%;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d1d5dd;
    }
    .el-transfer-panel .el-transfer-panel__header {
      display: none;
    }
    .el-transfer-panel__body {
      height: calc(100% - 40px);
      .el-transfer-panel__filter {
        margin: 12px;
        .el-input__inner {
          // width: 202px;
          height: 32px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #d1d5dd;
        }
      }
    }
    .el-transfer__buttons {
      padding: 0 8px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .el-button + .el-button {
        margin: 0;
      }
      .el-button--primary {
        width: 24px;
        height: 24px;
        color: #62666c;
        text-align: center;
        line-height: 24px;
        background: rgba(209, 213, 221, 0.28);
        border-radius: 4px;
        border: 1px solid rgba(209, 213, 221, 0.53);
      }
      .el-button {
        padding: 0;
      }
    }
    .el-dialog__footer {
      padding: 16px;
      background: #fff;
      border-top: 1px solid #e9ebef;
    }
    .shuxian {
      display: inline-block;
      width: 4px;
      height: 20px;
      background: #2677ff;
      margin-right: 5px;
    }
  }
}
.elDialogAddClue {
  .el-dialog {
    .el-dialog__body {
      min-height: 100px !important;
    }
  }
}
.updateType {
  .el-dialog {
    .el-dialog__header {
      height: 44px;
      background: #f5f7fa;
      display: flex;
      align-items: center;
      padding: 0 14px !important;
      color: #37393c !important;
      font-size: 14px !important;
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid #e9ebef;
      .el-dialog__headerbtn {
        top: 12px;
      }
      .el-dialog__title {
        font-size: 14px;
        color: #37393c;
      }
    }
    .el-dialog__footer {
      padding: 16px;
      background: #fff;
      border-top: 1px solid #e9ebef;
    }
    .el-dialog__body {
      height: 90px !important;
    }
  }
}
.el-notification__title {
  padding-right: 10px !important;
}
.el-notification__content {
  p {
    text-align: left;
    margin-bottom: 5px;
  }
  u {
    display: inline-block;
    color: #2677ff;
    text-underline-offset: 3px;
    line-height: 20px;
    margin: 0px 5px;
    cursor: pointer;
  }
}
.noticeClass {
  width: 440px;
}
.recommentViewClass {
  position: absolute;
  right: 10px;
  top: 12px;
  color: #2677ff;
  font-weight: bold;
  cursor: pointer;
  i {
    margin: 0 5px;
  }
}
.el-message-box {
  padding: 0;
  .el-message-box__header {
    background: #f5f7fa;
    padding: 13px 16px;
    border-bottom: 1px solid #e9ebef;
    .el-message-box__headerbtn {
      top: 12px;
    }
  }
  .el-button--primary {
    background-color: #2677ff;
    border-color: #2677ff;
    &:hover {
      background-color: #2677ff;
      border-color: #2677ff;
      color: #fff;
    }
  }
  .el-message-box__content {
    padding: 21px 16px 68px;
  }

  .el-message-box__btns {
    padding: 16px;
    border-top: 1px solid #e9ebef;
    .el-button {
      background-color: rgba(255, 255, 255, 0);
      border-color: #d1d5dd;
      color: #62666c;
      &:hover {
        background-color: #f0f3f8;
        border-color: #b9c5dd;
        color: #62666c;
      }
      &:focus {
        background-color: #f1f4f8;
        border-color: #a4b1cb;
        color: #62666c;
      }
    }
    .el-button--primary {
      background-color: #2677ff;
      border-color: #2677ff;
      color: #fff;
      &:hover {
        background-color: #2677ff !important;
        border-color: #2677ff;
        color: #fff;
      }
      &:focus {
        background-color: #2677ff;
        border-color: #2677ff;
        color: #fff;
      }
    }
  }
  .el-button--small {
    &:hover {
      background-color: transparent;
      border-color: #dcdfe6;
      color: #606266;
    }
  }
}
.el-table {
  border: 1px solid #e2e2e2 !important;
  font-size: 12px !important;
  color: #37393c !important;
  .el-table__fixed,
  .el-table__fixed-right {
    height: 98.2% !important;
  }
  th.gutter {
    display: table-cell !important;
  }
}
.el-table .cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  .el-button--small {
    padding: 0;
  }
}
.el-table::before {
  background-color: transparent;
}
.el-table--border::after {
  background-color: transparent;
}
.el-table__fixed-right::before,
.el-table__fixed::before {
  background-color: transparent;
  height: 0;
}
.el-table--border::after,
.el-table--group::after,
.el-table::before {
  background: transparent;
}
.el-pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  text-align: right;
  padding: 0;
  padding: 16px 0;
  z-index: 99;
  .el-pager li {
    // width: 28px;
    height: 28px;
    background: rgba(255, 255, 255, 0);
    border-radius: 4px;
    border: 1px solid transparent;
    margin-right: 8px;
    font-weight: 400;
    color: #62666c;
  }
  li.active + li {
    border-left: 1px solid transparent;
  }
  .el-pager li.active {
    // width: 28px;
    height: 28px;
    background: #f1f3f8;
    border-radius: 4px;
    // border: 1px solid #2677FF;
    border: none;
    font-weight: 400;
    color: #2677ff;
  }
  .el-pagination button {
    height: 28px !important;
    // line-height: 32px !important;
  }
  .el-input--mini .el-input__inner {
    height: 28px !important;
    // line-height: 32px !important;
    border: none !important;
    background: #f1f3f8;
  }
  button,
  .el-pagination__sizes {
    height: 28px !important;
  }
  .el-pagination__total {
    // position: absolute;
    // left: 20px;
    // top: 22px;
    height: 28px !important;
    // line-height: 32px;
    color: #2677ff;
  }
  .el-pagination__jump {
    margin-right: 20px;
  }
  .el-pagination__editor > .el-input__inner {
    border: none !important;
    background: #f1f3f8;
  }
  .el-pagination__editor.el-input .el-input__inner {
    height: 28px;
  }
}
.el-input.is-disabled .el-input__inner {
  background: transparent;
  color: #777;
}
.el-textarea.is-disabled .el-textarea__inner {
  background: transparent;
  color: #777;
}
.myPoperClass {
  max-width: 70% !important;
  min-height: 30px !important;
  max-height: 40% !important;
  overflow-y: auto !important;
}
.el-tree-node > .el-tree-node__content {
  position: relative;
  height: 40px;
  span > i {
    position: absolute;
    right: 0;
    top: 0;
    display: block;
    // width: 4px;
    height: 100%;
    background: transparent;
  }
}
// .el-tree .el-tree-node__expand-icon.expanded {
//   -webkit-transform: rotate(0deg);
//   transform: rotate(0deg);
// }
// .el-tree .el-icon-caret-right:before {
//   content: "\E602";
//   font-size: 18px;
// }
// .el-tree .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
//   content: "\E63c";
//   font-size: 18px;
// }
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #efefef !important;
  color: rgba(191, 6, 14, 1) !important;
  font-size: 16px;
  span > i {
    background: rgba(191, 6, 14, 1);
  }
}
/*去除upload组件过渡效果*/
.el-upload-list__item {
  transition: none !important;
}
// .el-tree--highlight-current
//   .el-tree-node.is-current
//   > .el-tree-node__content
//   .el-icon-caret-right:before {
//   // 三角
//   // color: rgba(191, 6, 14, 1) !important;
// }
::-webkit-scrollbar {
  height: 10px;
  width: 10px;
  background-color: #edf0f5;
}
::-webkit-scrollbar-thumb {
  height: 10px;
  width: 10px;
  background: #d2d7e0;
  border-radius: 3px;
}
::-webkit-scrollbar-track {
  width: 10px;
  background-color: transparent;
}

::-moz-scrollbar {
  height: 10px;
  width: 6px;
  background-color: #edf0f5;
}
::-moz-scrollbar-thumb {
  height: 10px;
  width: 6px;
  background: #d2d7e0;
  border-radius: 3px;
}
::-moz-scrollbar-track {
  width: 6px;
  background-color: transparent;
}

::scrollbar-thumb {
  height: 10px;
  width: 6px;
  // border-radius: 4px;
  background-color: rgba(61, 61, 61, 0.2);
}
.huohudegundongtiao-div {
  // 火狐
  /* 滑块颜色 滑块的背景颜色 */
  scrollbar-color: #e5e5e5 #f7f7f9;
  /* 火狐浏览器滚动条宽度有三种 thin auto none*/
  /* thin 很细 */
  /* auto 默认 */
  /* none 将滚动条隐藏 */
  scrollbar-width: thin;
  scrollbar-arrow-color: #f4ae21; /*三角箭头的颜色*/
  scrollbar-face-color: #333; /*立体滚动条的颜色*/
  scrollbar-3dlight-color: #666; /*立体滚动条亮边的颜色*/
  scrollbar-highlight-color: #666; /*滚动条空白部分的颜色*/
  scrollbar-shadow-color: #999; /*立体滚动条阴影的颜色*/
  scrollbar-darkshadow-color: #666; /*立体滚动条强阴影的颜色*/
  scrollbar-track-color: #666; /*立体滚动条背景颜色*/
  scrollbar-base-color: #f8f8f8; /*滚动条的基本颜色*/
}
.iedegundongtiao-div {
  // IE
  scrollbar-arrow-color: #f4ae21; /*三角箭头的颜色*/
  scrollbar-face-color: #333; /*立体滚动条的颜色*/
  scrollbar-3dlight-color: #666; /*立体滚动条亮边的颜色*/
  scrollbar-highlight-color: #666; /*滚动条空白部分的颜色*/
  scrollbar-shadow-color: #999; /*立体滚动条阴影的颜色*/
  scrollbar-darkshadow-color: #666; /*立体滚动条强阴影的颜色*/
  scrollbar-track-color: #666; /*立体滚动条背景颜色*/
  scrollbar-base-color: #f8f8f8; /*滚动条的基本颜色*/
}
::scrollbar-track {
  width: 6px;
  background-color: transparent;
}
.el-tabs__active-bar {
  background: #bf060eff;
}
.el-tabs__nav.is-top {
  .el-tabs__item.is-top {
    color: #979797ff;
    &.is-active {
      color: #3d3d3dff;
    }
  }
}

.el-checkbox__label {
  padding-left: 4px;
}

//操作列文字按钮
.el-table .el-button--text {
  color: #2677ff;
}
.el-button.is-disabled.el-button--text {
  color: #c0c4cc;
}
//上传
.upload-demo {
  width: 100%;
  .el-upload {
    width: 100%;
  }
  .el-upload-dragger {
    width: 100%;
    height: 110px;
    .el-upload__text em {
      color: #2677ff;
    }
  }
  .el-icon-upload {
    margin: 10px 0 10px;
  }
  .el-upload__tip {
    margin-top: 0;
  }
  .el-upload-list {
    max-height: 80px;
    min-height: 70px;
    overflow-y: auto;
    .el-upload-list__item {
      height: 60px;
      margin-top: 0;
    }
    .el-upload-list__item-thumbnail {
      width: 40px;
      height: 40px;
    }
    .el-upload-list__item-name {
      line-height: 40px !important;
    }
  }
}

.el-textarea .el-textarea__inner {
  // 然后找到对应的类名，在这里将拉伸去掉即可
  resize: none;
}
.el-table th.el-table__cell {
  background-color: #f2f3f5 !important;
}
// .el-table{
//   &:not(:last-child){
//     .el-table__cell::after {
//       content: '';
//       display: block;
//       position: absolute;
//       top: 13px;
//       right: 0;
//       height: 23px;
//       width: 1px;
//       background-color: #E1E5ED;
//     }
//   }
// }

.el-table__header-wrapper .is-leaf:nth-last-child(n + 3) {
  &::after {
    content: '';
    display: block;
    position: absolute;
    top: 13px;
    right: 0;
    height: 23px;
    width: 1px;
    background-color: #e1e5ed;
  }
}
.el-table th.el-table__cell:first-child::after,
.el-table th.el-table__cell:last-child::after {
  display: none;
}
.el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: none;
}
/deep/.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: none;
}
.el-table th .el-table__cell {
  border-right: 1px solid #ebeef5 !important;
}
.el-pagination .el-pager li {
  border: 0 !important;
}
.el-icon-cloudy {
  color: rgba(255, 121, 0, 1);
  font-weight: bold;
}
.el-icon-plus,
.el-icon-minus {
  margin-right: 4px;
}
.accountTimeAfter {
  margin-left: 15px;
  font-size: 12px;
  color: #fff;
  padding: 2px 2px;
  background: #ff7900;
  border-radius: 3px;
}
.globalCopy {
  color: #2677ff;
  // font-weight: bold;
  font-size: 16px;
  cursor: pointer;
}
.unitError {
  text-align: center;
  .el-icon-warning {
    font-size: 14px;
    color: #e6a23c !important;
    margin: 0 8px 0 0 !important;
  }
  .unitText {
    color: #e6a23c;
    font-size: 16px;
    height: 40px;
    line-height: 40px;
    margin-bottom: 16px;
  }
}
.conWrap {
  // padding: 0 20px;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  background: #fff;
  .headerEcharts {
    padding: 20px 0;
    font-weight: 500;
    font-size: 16px;
    color: rgba(55, 57, 60, 1);
    display: flex;
    align-items: center;
    i {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 5px;
      border-radius: 1px;
      transform: rotate(135deg);
      background: rgba(38, 119, 255, 1);
    }
  }
  ul {
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    align-items: center;
    li {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .title {
    border-radius:
      0px 4px,
      4px,
      0px;
    background: rgba(242, 244, 247, 1);
    color: rgba(98, 102, 108, 1);
    li {
      height: 40px;
      padding: 14px 20px 14px 20px;
      box-sizing: border-box;
      font-size: 12px;
    }
  }
  .content {
    background: #fff;
    li {
      border-bottom: 1px solid #ebeef5;
      height: 44px;
      padding: 14px 20px 14px 20px;
      box-sizing: border-box;
      font-size: 12px;
    }
  }
}

.el-checkbox .el-checkbox__inner {
  border: 1px solid #dcdfe6;
}

.inputNumber {
  .el-input {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 0 50px;
    .el-input__inner {
      width: 92%;
      padding: 0;
    }
  }
}

.el-input-number__decrease,
.el-input-number__increase {
  top: 1px !important;
}
.checkboxAll {
  // &:hover .el-checkbox__input .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
  //   border-color: #2677FF;
  // }
  .el-checkbox__inner:hover {
    border-color: #2677ff;
  }
  &.is-checked {
    .el-checkbox__label {
      color: #2677ff;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background: #2677ff;
      border-color: #2677ff;
    }
  }
}

.el-input {
  &.is-focus {
    .el-input__inner {
      // border: 1px solid #2677FF;
      border-color: #2677ff !important;
    }
  }
}
.el-input__inner:focus {
  border-color: #2677ff;
}
.el-radio {
  &.is-checked {
    .el-radio__label {
      color: #2677ff;
    }

    .el-radio__input {
      &.is-checked {
        .el-radio__inner {
          border-color: #2677ff;
          background-color: #2677ff;
        }
      }
    }
  }
  .el-radio__inner:hover {
    border-color: #2677ff;
  }
}
.el-checkbox {
  &.is-checked {
    .el-checkbox__label {
      color: #2677ff;
    }
    .el-checkbox__input {
      &.is-checked {
        .el-checkbox__inner {
          border-color: #2677ff;
          background-color: #2677ff;
        }
        &.is-disabled {
          .el-checkbox__inner {
            border-color: #dcdfe6;
            background-color: #edf2fc;
          }
        }
      }
    }
  }
}
.el-textarea__inner:focus {
  border-color: #2677ff;
}
.el-upload-dragger {
  &:hover {
    border-color: #2677ff;
  }
  &:focus {
    border-color: #2677ff;
  }
}

.el-select .el-input__inner:focus {
  border-color: #2677ff;
}

// /deep/
.el-input__suffix {
  .el-select__caret {
    line-height: 100% !important;
  }
}

// .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #2677ff !important;
  border-color: #2677ff !important;
}
// .el-button--text{
//   color: #2677FF;
// }

.el-menu {
  background: #062c6b;
  .el-submenu.is-active > .el-submenu__title > span > span,
  .el-submenu.is-active > .el-submenu__title > span > i {
    color: #fff;
    font-weight: 700 !important;
  }
}
