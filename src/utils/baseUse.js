export default {
    //生成32位随机数
    getNum(){
        var chars = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z'];
        var nums="";
        for(var i=0;i<32;i++){
            var id = parseInt(Math.random()*61);
            nums+=chars[id];
        }
        return nums;
    },
    getModificationDate(date = new Date()) {
        const buffer = date.getUTCFullYear().toString()+'-'+(date.getUTCMonth() + 1).toString().padStart(2, "0")+'-'+date.getUTCDate().toString().padStart(2, "0")+" "+date.getUTCHours().toString().padStart(2, "0")+":"+date.getUTCMinutes().toString().padStart(2, "0")+":"+date.getUTCSeconds().toString().padStart(2, "0");
        return buffer;
    },
     toChineseNumber (n)  {
        if (!Number.isInteger(n) && n < 0) {
          throw Error('请输入自然数');
        }
        const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
        const positions = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿', '十亿', '百亿', '千亿'];
        const charArray = String(n).split('');
        let result = '';
        let prevIsZero = false;
        //处理0  deal zero
        for (let i = 0; i < charArray.length; i++) {
          const ch = charArray[i];
          if (ch !== '0' && !prevIsZero) {
            result += digits[parseInt(ch)] + positions[charArray.length - i - 1];
          } else if (ch === '0') {
            prevIsZero = true;
          } else if (ch !== '0' && prevIsZero) {
            result += '零' + digits[parseInt(ch)] + positions[charArray.length - i - 1];
          }
        }
        //处理十 deal ten
        if (n < 100) {
          result = result.replace('一十', '十');
        }
        return result;
      },
}

