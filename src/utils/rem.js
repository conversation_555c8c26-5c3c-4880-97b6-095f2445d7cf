const px2remLoader = {
    loader: 'px2rem-loader',
    options: {
      remUnit: 192   // 这个值是根据UI设计的尺寸宽度/10,1920*1080设计稿，这个值就是1920/10 = 192
    }
  }
  function generateLoaders (loader, loaderOptions) {
    const loaders = options.usePostCSS ? [cssLoader, postcssLoader, px2remLoader] : [cssLoader, px2remLoader] // 将px2remLoader 添加到loaders数组中
 
    if (loader) {
      loaders.push({
        loader: loader + '-loader',
        options: Object.assign({}, loaderOptions, {
          sourceMap: options.sourceMap
        })
      })
    }
}