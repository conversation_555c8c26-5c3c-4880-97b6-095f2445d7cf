import Vue from "vue"
// 防止重复点击
export default {
    install (vue) {
        Vue.directive('repeatClick', {
            inserted (el, binding) {
                el.addEventListener('click', () => {
                    if (!el.disabled) {
                        el.disabled = true
                        setTimeout(() => {
                            el.disabled = false
                        }, binding.value || 5000)
                    }
                })
            }
        })
    }
}