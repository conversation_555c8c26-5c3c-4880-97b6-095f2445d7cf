import axios from 'axios'
import qs from 'qs'
import { router } from '../router'
import { Notification, MessageBox, Message } from 'element-ui'
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次

const eamPathDev = window.location.protocol + '//' + window.location.host + '/'
const eamPath = window.location.protocol + '//' + window.location.host + '/'

export { eamPath, eamPathDev }
const env = process.env.NODE_ENV
let target = ''
let targetIp = '' // 用于下载文件、显示图片的只是不带/api的地址
let targetGolang = ''
let wsIp = '' // websocket地址
let wsIpGolang = '' // websocket地址
let imgBaseUrl = '' // 带有http路径会跨域的需要使用//images.weserv.nl/?url=
console.log("当前环境：", env)
// 默认是本地环境
if (env === 'production') {
  // 生产环境
  console.log('生产环境')
  target = window.location.protocol + '//' + window.location.host + '/api/v1'
  targetGolang = window.location.protocol + '//' + window.location.host + '/hack/api/v1'
  targetIp = window.location.protocol + '//' + window.location.host
  wsIp = 'wss://' + window.location.host + '/ws?token='
  wsIpGolang = 'wss://' + window.location.host + '/hack/api/v1/ws?access_token='
  imgBaseUrl = ''
} else if (env === 'dev') {
  console.log('测试环境')
  target = window.location.protocol + '//' + window.location.host + '/api/v1' // php接口
  targetGolang = window.location.protocol + '//' + window.location.host + '/hack/api/v1' // golang接口
  targetIp = window.location.protocol + '//' + window.location.host
  wsIp = 'wss://' + window.location.host + '/ws?token='
  wsIpGolang = 'wss://' + window.location.host + '/hack/api/v1/ws?access_token='
  imgBaseUrl = '//images.weserv.nl/?url='
} else {
  // development
  console.log('本地环境')
  console.log(env)
  target = '/pc' // 用于php系统接口和上传
  targetGolang = '/golang' // 用于golang系统接口和上传
  targetIp = '/showImg' // 用于系统图片展示,不要后面的/api/v1
  // wsIp = 'wss://***********/ws?token=' // ws推送
  // wsIpGolang = 'wss://***********/hack/api/v1/ws' // ws推送
  // wsIp = 'wss://***********/ws?token=' // ws推送
  // wsIpGolang = 'wss://***********/hack/api/v1/ws' // ws推送
  wsIp = 'wss://************/ws?token=' // ws推送
  wsIpGolang = 'wss://************/hack/api/v1/ws?access_token=' // ws推送
  imgBaseUrl = '//images.weserv.nl/?url='
}
//创建axios实例y router.history._startLocation == '/statusTask' ? targetGolang :
const service = axios.create({
  baseURL: target,
  ipURL: targetIp,
  targetGolang: targetGolang,
  wsIp: wsIp,
  wsIpGolang: wsIpGolang,
  imgBaseUrl: imgBaseUrl,
  changeOrigin: true, // 可跨域
  withCredentials: true // 此设置为线上图片上传，否则cros
  // headers: { "Content-Type": "multipart/form-data" }
})

let errorCode = {
  401: '认证失败，无法访问系统资源',
  403: '当前操作没有权限',
  404: '访问资源不存在',
  default: '系统未知错误，请反馈给管理员'
}
// 添加请求拦截器
service.interceptors.request.use(
  async (config) => {
    if (config.is_golang && config.is_golang == 1) {
      // golang接口
      config.baseURL = targetGolang
      // is_new_params代表 golang接口的get请求数组如格式为id=aaaa&id=bbbbb
      if (config.method === 'get' && config.is_new_params == 1) {
        config.paramsSerializer = function (params) {
          return qs.stringify(params, { arrayFormat: 'repeat' })
        }
      } else if (config.method === 'get' && config.is_new_params != 1) {
        config.paramsSerializer = function (params) {
          return qs.stringify(params, { arrayFormat: 'indices' })
        }
      }
    } else {
      // php接口
      config.baseURL = target
      if (config.method === 'get') {
        config.paramsSerializer = function (params) {
          return qs.stringify(params, { arrayFormat: 'indices' })
        }
      }
    }
    config.headers.Authorization = localStorage.getItem('token')
    if (config.responseType) {
      config.headers.responseType = config.responseType
    }
    return config
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

// 添加响应拦截器
service.interceptors.response.use(
  (res) => {
    // 对响应数据做点什么
    const code = res.data.code
    // 获取提示信息，部分接口有warn_message
    if (code && code != 0 && code != 401) {
      const msg = res.data.message
      resetMessage.error(msg)
    }
    if(code == 400){
      const msg = res.data.message
      resetMessage.error(msg)
      return Promise.reject(res.data)
    }
    return res.data
  },
  (error) => {
    // 对响应数据做点什么
    const code = error.response.status
    // 获取错误信息
    const msg = error.response.data.message
    if (code == 401) {
      sessionStorage.clear()
      localStorage.clear()
      location.href = '/login'
      resetMessage.error('登录状态已过期，请重新登录')
    } else if (code == 502) {
      resetMessage.error('正在部署，请稍后……')
    } else {
      resetMessage.error(msg)
      return Promise.reject(error.response.data)
    }
    return Promise.reject(error)
  }
)
// service.all = axios.all
// service.spread = axios.spread

export default service
