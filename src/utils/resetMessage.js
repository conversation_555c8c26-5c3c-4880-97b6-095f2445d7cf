import { Message, MessageBox } from 'element-ui'
import store from '@/store'
let messageInstance = null
const resetMessage = (options) => {
  if (messageInstance) {
    messageInstance.close()
  }
  messageInstance = Message(options)
}
;['error', 'success', 'info', 'warning'].forEach((type) => {
  resetMessage[type] = (options) => {
    if (typeof options === 'string') {
      options = {
        message: options
      }
    }
    options.type = type
    return resetMessage(options)
  }
})
const noteBlackList = (inputValue) => {
  let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
  let role = null
  if (userInfo) {
    role = userInfo.user.role
  }
  if (role != '2') return
  MessageBox.prompt('', '标记关键词到黑名单', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue,
    inputValidator: (value) => {
      if (value) {
        return true
      } else {
        return false
      }
    },
    inputErrorMessage: '请输入',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        let res = await store
          .dispatch('setBlackList', {
            operate_company_id: store.state.currentCompany,
            keyword: instance.inputValue
          })
          .catch(() => {
            instance.confirmButtonLoading = false
          })
        if (res.code == 0) {
          done()
        }
        setTimeout(() => {
          instance.confirmButtonLoading = false
        }, 300)
      } else {
        instance.confirmButtonLoading = false
        done()
      }
    }
  }).then(({ value }) => {
    Message({
      type: 'success',
      message: '标记成功'
    })
  })
}
export { resetMessage, noteBlackList }
