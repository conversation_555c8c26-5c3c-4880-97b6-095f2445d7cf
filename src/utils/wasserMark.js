'use strict'
 
let watermark = {}
 
let setWatermark = (str) => {
  let id = '1.23452384164.123412415'
 
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id))
  }
 
  let can = document.createElement('canvas')
  can.width = 0
  can.height = 160
 
  let cans = can.getContext('2d')
  cans.rotate(-20 * Math.PI / 500)
  cans.font = '16px Vedana'
  cans.fillStyle = 'rgba(0, 0, 0, 0.10)'
  // cans.textAlign = 'center'
  // cans.textBaseline = 'Middle'
  cans.fillText(str, can.width / 10, can.height / 2)
 
  let div = document.createElement('div')
  div.id = id
  div.style.margin = '0px'
  div.style.padding = '0px'
  div.style.pointerEvents = 'none'
  div.style.top = '0px'
  div.style.left = '0px'
  div.style.position = 'fixed'
  div.style.zIndex = '10000'
  div.style.width = document.documentElement.clientWidth - 0 + 'px'
  div.style.height = document.documentElement.clientHeight - 0 + 'px'
  div.style.background = 'url(' + can.toDataURL('image/png') + ')   '
  document.body.appendChild(div)
  return id
}
 
// 该方法只允许调用一次
watermark.set = (str) => {
  let id = setWatermark(str)
  setInterval(() => {
    if (document.getElementById(id) === null) {
      id = setWatermark(str)
    }
  }, 500)
  window.onresize = () => {
    setWatermark(str)
  }
}
 
export default watermark