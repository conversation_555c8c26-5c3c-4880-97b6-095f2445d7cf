<template>
  <div class="container">
    <div class="headerTitle">API文档</div>
    <div class="filterTab">
      <div>
        <el-input
          v-model="keyword"
          placeholder="请输入关键字检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
      </div>
    </div>
    <div class="content">
      <div class="left">
        <el-tree :data="apiTypeListTree" :props="defaultProps" accordion>
          <span
            @click="handleNodeClick(node)"
            class="sideItem"
            :class="{
              isSelected:
                selectedSideItem == node.label ||
                selectedSideItem == node.data.value ||
                (selectedSidePreItem &&
                  (selectedSidePreItem == node.label || selectedSidePreItem == node.data.value))
            }"
            slot-scope="{ node }"
          >
            <el-tooltip
              slot="prepend"
              class="item"
              effect="dark"
              :content="node.label"
              placement="top"
              :open-delay="100"
            >
              <span>{{ node.label.replace('（已规范）', '').replace('（已经规范）', '') }}</span>
            </el-tooltip>
          </span>
        </el-tree>
      </div>
      <div class="right" v-if="isShowDetail && selectedMenuList.length !== 0">
        <div class="rightTop">
          <el-tabs v-model="activeName" @tab-click="handleTabClick($event)">
            <el-tab-pane name="doc" label="API文档"> </el-tab-pane>
            <el-tab-pane name="example" label="示例代码"> </el-tab-pane>
            <el-tab-pane name="code" label="状态码参照"> </el-tab-pane>
          </el-tabs>
        </div>
        <div class="rightBottom">
          <div class="leftModel">
            <div
              class="leftModelItem"
              :class="{ isSelected: selectedMenuItem == item.key }"
              v-for="(item, index) in selectedMenuList"
              :key="index"
              @click="handleSelectedClick(item)"
            >
              <el-tooltip class="item" effect="dark" :content="item.summary" placement="top">
                <span class="right">{{ item.summary }}</span>
              </el-tooltip>
            </div>
          </div>
          <div class="middleLine"></div>
          <div class="rightModel">
            <div class="detailDoc" v-if="activeName == 'doc'">
              <div class="detailDocTop">
                <div> 接口地址：{{ 'https://$DEVICE_IP' + apiDocInfo.path }} </div>
                <div>请求方式：{{ apiDocInfo.method }}</div>
                <div>返回格式：{{ apiDocInfo.type }}</div>
                <div>接口备注：{{ apiDocInfo.description }}</div>
              </div>
              <div class="detailDocMiddle">
                <div class="title">请求参数说明</div>
                <el-table
                  row-key="id"
                  :data="requestList"
                  :header-cell-style="{
                    background: '#F5F7FA',
                    color: '#62666C'
                  }"
                  ref="eltable"
                  height="564px"
                  :tree-props="{ children: 'children' }"
                  @cell-mouse-enter="showTooltip"
                  @cell-mouse-leave="hiddenTooltip"
                >
                  <el-table-column
                    v-for="item in columnsDocSend"
                    :key="item.dataIndex"
                    :label="item.title"
                    :prop="item.dataIndex"
                  >
                    <template slot-scope="{ row }">
                      {{ row[item.dataIndex] }}
                    </template>
                  </el-table-column>
                </el-table>
                <!-- <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip> -->
              </div>
              <div class="detailDocBottom">
                <div class="title">返回参数说明</div>
                <el-table
                  row-key="id"
                  :data="responseList"
                  :header-cell-style="{
                    background: '#F5F7FA',
                    color: '#62666C'
                  }"
                  ref="eltable"
                  height="564px"
                  default-expand-all
                  :tree-props="{ children: 'children' }"
                >
                  <el-table-column
                    v-for="item in columnsDocBack"
                    :key="item.dataIndex"
                    :label="item.title"
                    :prop="item.dataIndex"
                  >
                    <template slot-scope="{ row }">
                      <!-- <div v-if="item.dataIndex == 'type'">{{ row.schema.type }}</div> -->
                      <!-- <span v-else>{{ row[item.dataIndex] }}</span> -->
                      <span>{{ row[item.dataIndex] }}</span>
                    </template>
                  </el-table-column>
                </el-table>
                <!-- <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip> -->
              </div>
            </div>
            <div class="detailDoc" v-if="activeName == 'example'">
              <div class="title">请求代码示例</div>
              <el-input
                class="textarea"
                type="textarea"
                v-model="curlString"
                rows="9"
                readonly
              ></el-input>
              <div class="title">返回示例</div>
              <pre class="textarea pre-textarea" v-html="exampleString"></pre>
            </div>
            <div class="detailDoc" v-if="activeName == 'code'">
              <el-table
                :data="httpCodeList"
                :header-cell-style="{
                  background: '#F5F7FA',
                  color: '#62666C'
                }"
                ref="eltable"
                height="564px"
              >
                <el-table-column
                  v-for="item in columnsCode"
                  :key="item.dataIndex"
                  :label="item.title"
                  :prop="item.dataIndex"
                >
                  <template slot-scope="{ row }">
                    {{ row[item.dataIndex] }}
                  </template>
                </el-table-column>
              </el-table>
              <!-- <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip> -->
            </div>
          </div>
        </div>
      </div>
      <div class="right" v-else-if="selectedMenuList.length !== 0">
        <div class="menu">
          <div
            class="menuItem"
            v-for="(item, index) in selectedMenuList"
            :key="index"
            @click="handleOuterClick(item)"
          >
            <div class="img">
              <svg class="icon svg-icon" aria-hidden="true">
                <use xlink:href="#icon-file"></use>
              </svg>
            </div>
            <div class="summary">
              <el-tooltip class="item" effect="dark" :content="item.summary" placement="top">
                <span class="right">{{ item.summary }}</span>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
      <div class="right" v-else>
        <div class="emptyClass">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-kong"></use>
          </svg>
          <p>暂无数据</p>
        </div>
      </div>
    </div>
    <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
  </div>
</template>

<script>
import swaggerList from '@/utils/foradarOpenApi.json'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'

export default {
  components: {
    tableTooltip
  },
  data() {
    return {
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      keyword: '',
      // apiTypeList:[],
      isShowDetail: false,
      activeName: 'doc',
      apiDocInfo: {},
      menuList: [],
      swaggerList: '',
      selectedSideItem: '全部API',
      selectedSidePreItem: '全部API',
      selectedMenuList: [],
      selectedMenuOriginList: [],
      selectedMenuItem: '',
      requestList: [],
      columnsDocSend: [
        {
          title: '名称',
          align: 'left',
          width: 200,
          dataIndex: 'name',
          scopedSlots: { customRender: 'name' }
        },
        {
          title: '必填',
          align: 'left',
          width: 120,
          dataIndex: 'required',
          scopedSlots: { customRender: 'required' }
        },
        {
          title: '类型',
          align: 'left',
          width: 70,
          dataIndex: 'type',
          scopedSlots: { customRender: 'type' }
        },
        {
          title: '位置',
          align: 'left',
          width: 160,
          dataIndex: 'position',
          scopedSlots: { customRender: 'position' }
        },
        {
          title: '简介',
          align: 'left',
          width: 160,
          dataIndex: 'title',
          scopedSlots: { customRender: 'title' }
        },
        {
          title: '说明',
          align: 'left',
          width: 420,
          ellipsis: true,
          dataIndex: 'description',
          scopedSlots: { customRender: 'description' }
        }
      ],
      responseList: [],
      columnsDocBack: [
        {
          title: '名称',
          align: 'left',
          width: 440,
          ellipsis: true,
          dataIndex: 'name',
          scopedSlots: { customRender: 'name' }
        },
        {
          title: '类型',
          align: 'left',
          width: 160,
          dataIndex: 'type',
          scopedSlots: { customRender: 'type' }
        },
        {
          title: '简介',
          align: 'left',
          width: 160,
          dataIndex: 'title',
          scopedSlots: { customRender: 'title' }
        },
        {
          title: '说明',
          align: 'left',
          width: 420,
          ellipsis: true,
          dataIndex: 'description',
          scopedSlots: { customRender: 'description' }
        }
      ],
      curlString: '',
      exampleString: '',
      columnsCode: [
        {
          title: '状态码',
          align: 'left',
          width: 280,
          ellipsis: true,
          dataIndex: 'code',
          scopedSlots: { customRender: 'code' }
        },
        {
          title: '说明',
          align: 'left',
          width: 480,
          ellipsis: true,
          dataIndex: 'description',
          scopedSlots: { customRender: 'description' }
        }
      ],
      allMenuList: [],
      allMenuListData: [],
      apiTypeListTree: []
    }
  },
  computed: {
    // apiTypeList() {
    //   let tagsList = swaggerList.tags
    //   return tagsList;
    // },
  },
  mounted() {
    this.swaggerList = JSON.parse(JSON.stringify(swaggerList))
    this.swaggerList.tags.unshift({ name: '全部API' })
    this.handleTagTree1(this.swaggerList.tags)
    let allMenuList = []
    let pathsList = Object.keys(swaggerList.paths)
    for (let index = 0; index < pathsList.length; index++) {
      const element = pathsList[index]
      let elKeys = Object.keys(swaggerList.paths[element])
      for (let index1 = 0; index1 < elKeys.length; index1++) {
        const element1 = elKeys[index1]
        allMenuList[element + '_' + element1] = {
          path: element,
          method: element1,
          ...swaggerList.paths[element][element1]
        }
      }
    }
    this.allMenuList = allMenuList
    let allMenuListData = []
    let allMenuListKeyData = Object.keys(allMenuList)
    for (let index2 = 0; index2 < allMenuListKeyData.length; index2++) {
      const element2 = allMenuListKeyData[index2]
      allMenuListData.push({
        key: element2,
        path: allMenuList[element2],
        ...allMenuList[element2]
      })
    }
    this.allMenuListData = allMenuListData
    this.selectedMenuList = allMenuListData
    this.selectedMenuOriginList = allMenuListData
  },
  methods: {
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleNodeClick(node) {
      let data = node.data
      if (data.children) {
        return
      }
      this.selectedSidePreItem = node.parent.data.name

      this.handleClick(data.value || data.name)
    },
    handleTagTree1(arr) {
      // 使用递归
      let firstLevel = []
      for (let i = 0; i < arr.length; i++) {
        const element = arr[i]
        let currentArrItem = element.name.split('/')
        if (currentArrItem.length == 1) {
          firstLevel.push(element)
        }
      }
      for (let i = 0; i < firstLevel.length; i++) {
        const element = firstLevel[i]
        for (let j = 0; j < arr.length; j++) {
          const element1 = arr[j]
          let currentArrItem = element1.name.split('/')
          if (currentArrItem.length > 1 && element1.name.indexOf(element.name) !== -1) {
            if (!element.children) {
              element.children = []
            }
            element1.value = currentArrItem.join('/')
            element1.name = currentArrItem.slice(1).join('/')
            element.children.push(element1)
          }
        }
      }
      this.apiTypeListTree = firstLevel
    },
    checkFuncList() {
      this.isShowDetail = false
      this.selectedMenuList = this.selectedMenuOriginList.filter(
        (key) => key.summary.indexOf(this.keyword) > -1
      )
    },
    syntaxHighlight(json) {
      if (typeof json != 'string') {
        json = JSON.stringify(json, undefined, 2)
      }
      json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>')
      return json.replace(
        /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
        function (match) {
          let cls = 'color:#D19A66'
          if (/^"/.test(match)) {
            if (/:$/.test(match)) {
              cls = 'color:#F92A0F'
            } else {
              cls = 'color:#44C91B'
            }
          } else if (/true|false/.test(match)) {
            cls = 'color:#1B73C9'
          } else if (/null/.test(match)) {
            cls = 'color:#C586C0'
          }
          return `<span style=${cls}>${match}</span>`
        }
      )
    },
    handleTabClick() {
      let originPath = this.apiDocInfo.path
      let originMethod = this.apiDocInfo.method
      const ifLogin = originPath.indexOf('/user-auth/login') > -1
      const ifGet = originMethod === 'get'
      this.requestExample = {}
      this.apiDocInfo.parameters &&
        this.apiDocInfo.parameters.map((parameter) => {
          if (parameter.schema && parameter.schema.model && parameter.schema.model.properties) {
            const properties = parameter.schema.model.properties
            Object.keys(properties).map((key) => {
              this.requestExample[key] = properties[key].example
            })
          }
          // this.requestExample[parameter.name] = parameter.example
        })
      // 处理 curl 示例
      let getString = '?'
      Object.keys(this.requestExample).map((key, index) => {
        getString +=
          index === Object.keys(this.requestExample).length - 1
            ? `${key}=${this.requestExample[key]}`
            : `${key}=${this.requestExample[key]}&`
      })
      this.curlString = `curl -X ${originMethod.toUpperCase()} "https://[device-ip]${originPath}${Object.keys(this.requestExample).length && ifGet ? getString : ''}" -H "accept: application/json" ${!ifLogin ? '-H "X-Access-Token: xxx" ' : ''}${!ifGet ? '-H "Content-Type: application/json" -d ' + '"' + JSON.stringify(this.requestExample).replace(/\"/g, '\\"') + '"' : ''}`
      if (originMethod == 'post') {
        let postAction = []
        let postStr = ''
        this.apiDocInfo.parameters &&
          this.apiDocInfo.parameters.map((parameter) => {
            if (parameter.schema && parameter.schema.model && parameter.schema.model.properties) {
              const properties = parameter.schema.model.properties
              Object.values(properties).map((item) => {
                if (item.model) {
                  postAction.push({
                    name: item.$ref.split('/')[2].toLowerCase() || '',
                    example: '',
                    description: item.model.description,
                    value: Object.values(item.model.properties).map((u1, index, ss) => {
                      if (u1 && u1['description']) {
                        return {
                          // @ts-ignore
                          name: u1['x-go-name'].toLowerCase() || '',
                          example: u1.example,
                          description: u1.description
                        }
                      }
                    })
                  })
                } else {
                  postAction.push({
                    // @ts-ignore
                    name: item['x-go-name'].toLowerCase() || '',
                    example: item.example,
                    description: item.description
                  })
                }
              })
            }
          })
        postAction.forEach((item, index) => {
          if (item) {
            if (item && item.value) {
              postStr += `\ \ \ \ \ \"${item.name}" { \n`
              item.value.forEach((u1) => {
                if (u1 && u1.name && u1.example) {
                  postStr += `\ \ \ \ \ \ \ \ \ \ ${u1?.name || ''} : ${u1?.example || ''} \ \ \ \ \ ${u1?.description ? '#' + u1?.description : ''}\n`
                }
              })
              postStr += '\ \ \ \ \ \ \ }'
            } else {
              postStr += `\ \ \ \ \ \ \ ${item.name} : ${item.example} \ \ \ \ \  ${item.description ? '#' + item.description : ''}`
            }
          }
        })
        this.curlString = `curl -X ${originMethod.toUpperCase()} "https://[device-ip]${originPath}${Object.keys(this.requestExample).length}" -H "accept: application/json" ${!ifLogin ? '-H "X-Access-Token: xxx" ' : ''}${!ifGet ? '-H "Content-Type: application/json" -d ' : ''}`
        this.curlString += ' \n --data-binary  { \n' + postStr + '\n}'
      }
      // 处理 response example 示例
      this.responseExample = {}
      if (
        this.apiDocInfo.responses &&
        this.apiDocInfo.responses['200'] &&
        this.apiDocInfo.responses['200'].content &&
        this.apiDocInfo.responses['200'].content['application/json'] &&
        this.apiDocInfo.responses['200'].content['application/json'].schema &&
        this.apiDocInfo.responses['200'].content['application/json'].schema.properties
      ) {
        const properties =
          this.apiDocInfo.responses['200'].content['application/json'].schema.properties
        this.responseExample = this.recursionData(properties)
      }
      // this.exampleString += '}';
      let exampleString = ''
      if (
        this.apiDocInfo.responses &&
        this.apiDocInfo.responses['200'] &&
        this.apiDocInfo.responses['200'].content &&
        this.apiDocInfo.responses['200'].content['application/json'] &&
        this.apiDocInfo.responses['200'].content['application/json'].examples &&
        this.apiDocInfo.responses['200'].content['application/json'].examples['1'] &&
        this.apiDocInfo.responses['200'].content['application/json'].examples['1'].value
      ) {
        exampleString =
          this.apiDocInfo.responses['200'].content['application/json'].examples['1'].value
      }

      this.exampleString = this.syntaxHighlight(exampleString)
      const keys = Object.keys(this.apiDocInfo.responses)
      this.httpCodeList = keys.map((key) => {
        return {
          id: key,
          code: key,
          description: this.apiDocInfo.responses[key].description
        }
      })
    },
    recursionData(data) {
      let newData = {}
      Object.keys(data).map((item) => {
        if (data[item].type === 'array' && data[item].items.$ref) {
          newData[item] = {
            description: data[item].description,
            value: data[item].items.$ref
              ? this.contentObj(this.recursion(data[item].items.$ref.split('/')[2]).properties)
              : (data[item].example || '').toString(),
            type: data[item].type
          }
        } else {
          newData[item] = {
            description: data[item].description,
            value: (data[item].example || '').toString(),
            type: data[item].type
          }
        }
      })
      return newData
    },
    getRequestList1() {
      this.requestList = []
      ;(this.apiDocInfo.parameters || []).map((parameter) => {
        // body单独处理
        if (
          parameter.in === 'body' &&
          parameter.schema &&
          parameter.schema.model &&
          parameter.schema.model.properties
        ) {
          Object.keys(parameter.schema.model.properties).map((key) => {
            this.requestList.push({
              id: key + Math.random().toString(),
              type: parameter.schema.model.properties[key].type,
              position: parameter.in,
              description: parameter.schema.model.properties[key].description,
              required: parameter.required,
              name: key
            })
            // 嵌套对象数据处理
            if (parameter.schema.model.properties[key].model) {
              const innerModelProperties = parameter.schema.model.properties[key].model.properties
              Object.keys(innerModelProperties).map((keyIn) => {
                this.requestList.push({
                  id: keyIn + Math.random().toString(),
                  type: innerModelProperties[keyIn].type,
                  position: parameter.in,
                  description: innerModelProperties[keyIn].description,
                  required: innerModelProperties[keyIn].required,
                  name: keyIn,
                  child: true
                })
              })
            }
          })
        } else {
          this.requestList.push({
            id: parameter.name + Math.random().toString(),
            type: parameter.type,
            position: parameter.in,
            description: parameter.description,
            required: parameter.required,
            name: parameter.name
          })
        }
      })
    },
    getRequestList() {
      this.requestList = []
      ;(this.apiDocInfo.parameters || []).map((parameter) => {
        this.requestList.push({
          id: parameter.name + Math.random().toString(),
          type: parameter.schema.type,
          position: parameter.in,
          description: parameter.description,
          required: parameter.required,
          name: parameter.name
        })
      })
      let requestBodyList = []
      if (
        this.apiDocInfo.requestBody &&
        this.apiDocInfo.requestBody.content &&
        this.apiDocInfo.requestBody.content['application/json'] &&
        this.apiDocInfo.requestBody.content['application/json'].schema &&
        this.apiDocInfo.requestBody.content['application/json'].schema.type == 'object'
      ) {
        const schema = this.apiDocInfo.requestBody.content['application/json'].schema
        requestBodyList = this.handleResProperties(schema.properties, schema.required, true)
      }
      this.requestList = this.requestList.concat(requestBodyList)
    },
    getResponseList() {
      this.responseList = []
      if (
        this.apiDocInfo.responses['200'] &&
        this.apiDocInfo.responses['200'].content &&
        this.apiDocInfo.responses['200'].content['application/json']
      ) {
        const properties =
          this.apiDocInfo.responses['200'].content['application/json'].schema.properties
        if (properties) {
          this.responseList = this.handleResProperties(properties)
        }
      }
    },
    // 递归处理参数
    handleResProperties(properties, requiredArr, isRequest = false) {
      // properties遍历循环属性 只遍历properties 的 data属性
      // key值的type == 'object' 取值 properties
      // key值的type == 'array' 取值 items
      // children表示子节点 如果type为object或者array 则放入当前节点的children属性
      const result = []
      Object.keys(properties).forEach((key) => {
        let isRequired = requiredArr ? requiredArr.includes(key) : false
        let item = {}
        if (properties[key] && properties[key].type == 'object') {
          item = {
            name: key,
            title: properties[key].title,
            description: properties[key].description,
            id: key + Math.random().toString(),
            type: properties[key].type,
            children: this.handleResProperties(
              properties[key].properties,
              properties[key].required
            ),
            position: 'body',
            required: isRequired
          }
          // result.push(item)
        } else if (properties[key] && properties[key].type == 'array') {
          if (properties[key].items.type == 'object') {
            item = {
              name: key,
              title: properties[key].title,
              description: properties[key].description,
              id: key + Math.random().toString(),
              type: properties[key].type,
              children: this.handleResProperties(
                properties[key].items.properties,
                properties[key].items.required
              ),
              position: 'body',
              required: isRequired
            }
            // result.push(item)
          } else {
            item = {
              name: key,
              title: properties[key].title,
              description: properties[key].description,
              id: key + Math.random().toString(),
              type: properties[key].type,
              position: 'body',
              required: isRequired
            }
          }
        } else {
          item = {
            name: key,
            title: properties[key].title,
            description: properties[key].description,
            id: key + Math.random().toString(),
            type: properties[key].type,
            position: 'body',
            required: isRequired
          }
          // result.push(item)
        }
        result.push(item)
      })
      return result
    },
    handleOuterClick(val) {
      this.selectedMenuList = this.selectedMenuOriginList
      this.isShowDetail = true
      this.handleSelectedClick(val)
    },
    handleSelectedClick(val) {
      this.activeName = 'doc'
      this.selectedMenuItem = val.key
      this.apiDocInfo = val // api每项的详情
      this.getRequestList()
      this.getResponseList()
    },
    handleClick(val) {
      this.isShowDetail = false
      this.activeName = 'doc'
      this.selectedSideItem = val
      this.getMenuList()
    },
    getMenuList() {
      let { allMenuList } = this
      this.selectedMenuOriginList = []
      let allMenuListKeyData = Object.keys(allMenuList)
      if (this.selectedSideItem == '全部API') {
        this.selectedMenuOriginList = this.allMenuListData
      } else {
        for (let index2 = 0; index2 < allMenuListKeyData.length; index2++) {
          const element2 = allMenuListKeyData[index2]
          if (allMenuList[element2].tags.includes(this.selectedSideItem)) {
            this.selectedMenuOriginList.push({
              key: element2,
              path: allMenuList[element2],
              ...allMenuList[element2]
            })
          }
        }
      }
      this.selectedMenuList = this.selectedMenuOriginList
      this.selectedMenuList[0] && this.handleSelectedClick(this.selectedMenuList[0])
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 100%;
  .content {
    height: calc(100% - 80px);
    display: flex;
    flex-direction: row;
    // justify-content: space-between;
    .left {
      width: 245px;
      height: 100%;
      overflow: auto;
      margin-right: 16px;
      background-color: #fff;
      .sideItem {
        box-sizing: border-box;
        // width: calc(100% - 40px);
        width: 100%;
        padding: 0 24px 0 16px;
        height: 48px;
        line-height: 48px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &:hover {
          cursor: pointer;
        }
        &.isSelected {
          color: rgb(66, 133, 244);
          // background-color: #ecf3fe;
          // border-left: 5px solid #4285f4;
        }
      }
    }
    .right {
      flex: 1;
      width: 0;
      background-color: #fff;
      overflow: auto;
      .menu {
        padding: 64px;
        display: flex;
        flex-wrap: wrap;
      }
      .menuItem {
        margin: 0 64px;
        width: 110px;
        height: 180px;

        // line-height: 88px;
        text-align: center;
        .summary {
          overflow: hidden;
          // white-space: nowrap;
          text-overflow: ellipsis;
          display: -webkit-box; /*内容不换行*/
          -webkit-line-clamp: 3; /*超出几行*/
          -webkit-box-orient: vertical; /*从上到下垂直排列子元素*/
        }
        .img {
          width: 110px;
          height: 110px;
          border-radius: 10px;
          background: url('../../assets/images/apiBack.png');
          background-size: contain;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 10px;
          .icon {
            width: 66px;
            height: 66px;
          }
        }
      }

      .rightTop {
        height: 43px;
        /deep/.el-tabs {
          .el-tabs__nav {
            padding-left: 20px;
          }

          .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
            padding: 0 16px;
            height: 44px;
            text-align: center;
            line-height: 44px;
            font-weight: bold;
            color: #2677ff;
            background: rgba(38, 119, 255, 0.08);
          }
          .el-tabs__active-bar {
            left: 4px;
            width: 100%;
            padding: 0 16px;
            background: #2677ff;
          }
          .el-tabs__header {
            margin: 0;
          }
          .el-tabs__nav-wrap::after {
            height: 1px;
            background-color: #e9ebef;
          }
          .el-tabs__item {
            padding: 0 16px;
            height: 44px;
            text-align: center;
            line-height: 44px;
            font-size: 14px;
            font-weight: 400;
            color: #62666c;
          }
        }
      }
      .rightBottom {
        margin-top: 16px;
        display: flex;
        flex-direction: row;
        height: calc(100% - 59px);
      }
      .leftModel {
        width: 245px;
        margin-right: 10px;
        overflow: auto;

        .leftModelItem {
          box-sizing: border-box;
          width: 100%;
          height: 48px;
          line-height: 48px;
          padding: 0 16px 0 24px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          &:hover {
            cursor: pointer;
          }
          &.isSelected {
            color: rgb(66, 133, 244);
            border-left: 5px solid #4285f4;
          }
        }
      }
      .middleLine {
        width: 4px;
        height: 100%;
        background-color: #d2d7e0;
      }
      .rightModel {
        margin-left: 10px;
        flex: 1;
        width: 0;
        height: 100%;
        overflow: auto;
      }
    }
  }
}
.emptyClass {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #ffffff 0%, #f0f6ff 100%);
  backdrop-filter: blur(8px);
  svg {
    display: inline-block;
    font-size: 90px;
  }
  p {
    margin-top: 12px;
    color: #62666c;
  }
}
.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #fff;
  & > div {
    display: flex;
    align-items: center;
    .normalBtnRe {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    /deep/.el-input {
      width: 500px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      margin-left: 16px;
      cursor: pointer;
    }
  }
}
.detailDoc {
  padding: 16px;
  .title {
    font-size: 16px;
    font-family:
      PingFangSC-Medium,
      PingFang SC,
      sans-serif;
    font-weight: 600;
    color: #333333;
    margin-bottom: 16px;
  }
  .textarea {
    // border: 1px solid #f2f4f7;
    &.pre-textarea {
      padding: 5px 15px;
      border: 1px solid #dcdfe6;
      background-color: #f2f4f7 !important;
    }
    /deep/.el-textarea__inner {
      background-color: #f2f4f7 !important;
    }
  }
  .detailDocTop {
    font-size: 14px;
    font-family:
      PingFangSC-Regular,
      PingFang SC,
      sans-serif;
    font-weight: 400;
    color: #333333;

    > div {
      margin-bottom: 10px;
    }
  }
  .detailDocMiddle,
  .detailDocBottom {
    margin-top: 32px;

    .title {
      font-size: 16px;
      font-family:
        PingFangSC-Medium,
        PingFang SC,
        sans-serif;
      font-weight: 600;
      color: #333333;
      margin-bottom: 16px;
    }
  }
}
</style>
