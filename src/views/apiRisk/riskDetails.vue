<template>
  <div class="container">
    <div class="headerTitle">
      <span>
        <span class="goback" @click="$router.go(-1)"
          ><i class="el-icon-arrow-left"></i>返回上一层</span
        >
        <span class="spline">/</span>
        <span>{{ headerTitle }}</span>
      </span>
    </div>
    <div class="home_header">
      <!-- 高级筛选条件 -->
      <div :class="hightFilterIsShow()" v-loading="loading" style="padding: 0px 20px">
        <div class="leftTab">
          <el-tree
            :data="treeData"
            accordion
            :props="{
              children: 'children',
              label: 'name'
            }"
          >
            <span class="custom-tree-node" slot-scope="{ data }">
              <el-tooltip
                placement="top"
                :open-delay="500"
                effect="dark"
              >
                <!-- 使用具名插槽来自定义 tooltip 内容 -->
                <div slot="content">
                  <div style="display: flex; align-items: center;">
                    <i
                      v-if="data.name !== '外部域名' && data.name !== '外部URL'"
                      class="el-icon-document-copy"
                      @click="copyTextText(data.name)"
                      style="color: #2677ff; cursor: pointer; margin-right: 6px"
                    ></i>
                    <span>{{ data.name }}</span>
                  </div>
                </div>

                <div class="labelText">
                  <a
                    v-if="data.name && String(data.name).includes('http')"
                    :href="data.name"
                    class="title"
                    target="_blank"
                    style="color: #2677ff; text-decoration: none;"
                    >{{ data.name }}</a
                  >
                  <span  v-else class="title">{{ data.name }}</span>
                  <el-tooltip
                    v-if="data.name === '外部域名' || data.name === '外部URL'"
                    class="item1"
                    effect="dark"
                    placement="top"
                    :open-delay="500"
                    style="margin-left: 2px"
                  >
                    <p slot="content" style="line-height: 24px">
                      {{
                        data.name === '外部域名'
                          ? '当前网站所使用的外部服务/资源站点地址'
                          : '当前网站所使用的外部服务/资源的完整地址'
                      }}
                    </p>
                    <i class="el-icon-question searchIcon"></i>
                  </el-tooltip>
                </div>
              </el-tooltip>
            </span>
          </el-tree>
        </div>
        <div class="rightWrap">
          <!-- @selection-change="handleSelectionChange" -->
          <div class="filterTab">
            <div>
              <el-input
                v-model="formInline.url"
                @keyup.enter.native="checkFuncList"
                placeholder="请输入URL进行搜索"
                id="scan_keycheck"
              >
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
              </el-input>
              <span v-if="userIsOpen" @click="highCheckIsShow" id="scan_filter" style="width: 80px"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <!-- <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="scan_all"
            >选择全部</el-checkbox
          >
          <el-button
            :disabled="!userIsOpen"
            class="normalBtnRe"
            type="primary"
            @click="removeOne('more')"
            id="scan_more_del"
            >删除</el-button
          >
        </div> -->
          </div>
          <el-table
            border
            :data="tableData"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            @cell-mouse-enter="showTooltip"
            @cell-mouse-leave="hiddenTooltip"
            ref="eltable"
            height="100%"
            style="width: 100%"
          >
            <!-- <el-table-column
              type="selection"
              align="center"
              :reserve-selection="true"
              :show-overflow-tooltip="true"
              :selectable="handleSelectable"
              width="55"
            >
            </el-table-column> -->
            <el-table-column
              v-for="item in tableHeaderIsShow"
              :key="item.id"
              :prop="item.name"
              align="left"
              :min-width="item.minWidth"
              :fixed="item.fixed"
              :label="item.label"
            >
              <template slot-scope="scope">
                <span v-if="item.name == 'risk_type'">{{
                  getRiskTypeName(scope.row.risk_type)
                }}</span>
                <span v-else-if="item.name == 'url' && scope.row[item.name]">
                  <el-tooltip :open-delay="500" class="item" effect="dark" placement="top">
                    <div slot="content">
                      <i
                        class="el-icon-document-copy"
                        @click="copyTextText(scope.row['url'])"
                        style="color: #2677ff; cursor: pointer; margin-right: 6px"
                      ></i>
                      {{ scope.row[item.name] }}
                    </div>
                    <a
                      v-if="scope.row[item.name] && String(scope.row[item.name]).includes('http')"
                      :href="scope.row[item.name]"
                      target="_blank"
                      style="color: #2677ff;"
                      >{{ scope.row[item.name] }}</a
                    >
                    <p class="ellipsis" v-else>{{
                      scope.row[item.name] ? scope.row[item.name] : '-'
                    }}</p>
                  </el-tooltip>
                </span>
                <div v-else>
                  {{ scope.row[item.name] }}
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip> -->
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            v-if="currentPageIsShow"
            :current-page="currentPage"
            :page-sizes="pageSizeArr"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="85px">
          <!-- <el-form-item label="URL：" prop="url">
            <el-input v-model="formInline.url" placeholder="请输入URL进行检索"></el-input>
          </el-form-item> -->
          <el-form-item label="风险类型：" prop="type">
            <el-select filterable clearable v-model="formInline.type" placeholder="请选择">
              <el-option
                v-for="item in riskTypes"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="状态码：" prop="status_code">
            <el-input v-model="formInline.status_code" placeholder="请输入状态码"></el-input>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="scan_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="scan_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
const throttle = (func, wait = 50) => {
  // 上一次执行该函数的时间
  let lastTime = 0
  return function (...args) {
    // 当前时间
    let now = +new Date()
    // 将当前时间和上一次执行函数时间对比
    // 如果差值大于设置的等待时间就执行函数
    if (now - lastTime > wait) {
      lastTime = now
      func.apply(this, args)
    }
  }
}
import assetsValidate from '../assetsView/assetsValidate.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import taskAddEditDialog from './taskAddEditDialog.vue'
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  nowStartRunningTask,
  orderTask,
  apiAnalysisTaskResult,
  getVulnerability,
  remind,
  apiAnalysisTaskDetail,
  deleteVulnerability
} from '@/api/apiConfig/api.js'

import {
  startRunningTask,
  pauseRunningTask,
  getAllUser,
  scanTaskDetail,
  deleteTask,
  getAllTaskList
} from '@/api/apiConfig/discovery.js'

export default {
  components: { taskAddEditDialog, tableTooltip, hightFilter, assetsValidate },
  props: ['task_type'],
  data() {
    return {
      againRun: null,
      UpdatePocRiskBtnLoading: false,
      showValidateList: [],
      correctList: [],
      errorList: [],
      assetsValidDialogVisible: false,
      validateType: '',
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      assets: [],
      currentPercent: 0,
      progressBar: false,
      wait_num: 0,
      last_rist_time: 0,
      loading: false,
      checkedAll: false,
      highCheckdialog: false,
      pauseAndStart: true,
      finshed_num: 0,
      wtLength: 0,
      changtingFlag: false,
      transferProp: {},
      transferData: [],
      checkedArr: [],
      userArr: [],

      scanId: '', //全部常用端口的id
      isdisabled: false,
      formInline: {
        url: '',
        status_code: '',
        type: ''
      },
      dialogFormVisible: false,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      currentPage: 1,
      data: [],
      filterMethod(query, item) {
        return item.pinyin.indexOf(query) > -1
      },

      dialogFormVisibleFenzu: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      tableData: [],
      currentPageIsShow: true,
      tableHeader: [
        {
          label: 'URL',
          name: 'url',
          fixed: 'left',
          minWidth: '120'
        },
        {
          label: '风险类型',
          name: 'risk_type',
          minWidth: '50'
        },
        {
          label: '风险指向',
          name: 'risk_point',
          minWidth: '70'
        },
        {
          label: '描述',
          name: 'description',
          minWidth: '100'
        },
        {
          label: '状态码',
          name: 'status_code',
          minWidth: '50'
        },
        {
          label: '大小',
          name: 'size',
          minWidth: '50'
        },
        {
          label: '数据类型',
          name: 'response_type',
          minWidth: '100'
        }
      ],
      ww: 0, //下面为扫描动画
      wh: 0,
      c: '',
      ctx: {},
      center: {},
      color_gold: '',
      deg_to_pi: '',
      time: 0,
      enemies: [],
      runningTask: null,
      user: {
        role: ''
      },
      currentId: '',
      runningFuncTimer: null,
      highlist: null,
      userIsOpen: true, // 权限控制
      riskTypes: ['可疑文件', '可疑API', '敏感信息', '可构造包'],
      treeData: [
        {
          name: '外部域名',
          children: []
        },
        {
          name: '外部URL',
          children: []
        }
      ],
      expandedKeys: ['任务参数']
    }
  },
  computed: {
    ...mapState([
      'socketTimestamp',
      'websocketMessage',
      'currentCompany',
      'setTimeBox',
      'currentCompanyId'
    ]),
    ...mapGetters([
      'getterCurrentCompany',
      // 'getterWebsocketMessage',
      'getterSocketTimestamp',
      'getterSettime'
    ]),
    tableHeaderIsShow() {
      let arr = []
      arr = this.tableHeader.filter((item, index) => {
        return !item.path || (item.path && item.path.indexOf(this.$route.path) != -1)
      })
      return arr
    },
    getRiskType(risk_type) {
      return (risk_type) => {
        // 1-可疑文件，2-可疑API
        // 3-敏感信息，4-可构造包
        if (risk_type == 1) {
          return '可疑文件'
        } else if (risk_type == 2) {
          return '可疑API'
        } else if (risk_type == 3) {
          return '敏感信息'
        } else if (risk_type == 4) {
          return '可构造包'
        }
      }
    }
  },
  watch: {
    getterSettime(val) {
      // 监听禁扫时间
      setTimeout(() => {
        this.getMergeTakList()
      }, 3000)
    },
    // // 监听到端有返回信息

    getterCurrentCompany(val) {
      setTimeout(() => {
        // 权限控制
        if (sessionStorage.getItem('companyInfo')) {
          let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
          this.userIsOpen = true
        } else {
          this.userIsOpen = true
        }
      }, 1000)
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getMergeTakList()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  created() {
    this.headerTitle = this.$route.name
    if (sessionStorage.getItem('queryParam')) {
      // 从查看列表页面返回的保留筛选条件等
      let queryParam = JSON.parse(sessionStorage.getItem('queryParam'))
      this.currentPageIsShow = false
      this.currentPageIsShow = true
      this.currentPage = queryParam.page
      this.pageSize = queryParam.per_page
      this.formInline.url = queryParam.name
      this.formInline.id = queryParam.task_id
    }
    if (sessionStorage.getItem('companyInfo')) {
      // 权限控制
      let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      this.userIsOpen = true
    } else {
      this.userIsOpen = true
    }
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      if (this.user.role == 2) {
        if (!this.currentCompany) {
          return
        }
        this.getMergeTakList()
        this.getapiAnalysisTaskDetailFn()
      } else {
        this.getMergeTakList()
        this.getapiAnalysisTaskDetailFn()
      }
    }
  },
  async mounted() {
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
  },
  methods: {
    ...mapMutations(['setTimeChangeBox', 'changeMenuId']),
    getRiskTypeName(type) {
      return this.riskTypes[type - 1]
    },
    copyTextText(text) {
      this.$copyText(text).then(
        (res) => {
          this.$message.success('复制成功')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    async getapiAnalysisTaskDetailFn() {
      let obj = {
        operate_company_id: this.currentCompany,
        task_id: this.formInline.id
      }
      let res = await apiAnalysisTaskDetail(obj)
      this.treeData = [
        {
          name: '外部域名',
          children:
            res.data?.external_list?.domain.map((item) => {
              return { name: item }
            }) || []
        },
        {
          name: '外部URL',
          children:
            res.data?.external_list?.url.map((item) => {
              return { name: item }
            }) || []
        }
      ]
      console.log('treeData---', this.treeData)
      // this.expandedKeys = ['domain', 'url']
      this.$forceUpdate()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },

    handleSelectable(row, index) {
      return !this.checkedAll
    },

    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选查询
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getMergeTakList()
    },

    async highCheckIsShow() {
      this.highCheckdialog = true
      let res = await getAllUser()
      this.userArr = res.data
    },
    async getMergeTakList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      let typeIndex = this.riskTypes.findIndex((item) => item == this.formInline.type)
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        url: this.formInline.url,
        status_code: this.formInline.status_code,
        type: typeIndex == -1 ? null : typeIndex + 1,
        task_id: this.formInline.id,
        operate_company_id: this.currentCompany
      }

      this.loading = true
      let res
      res = await apiAnalysisTaskResult(obj).catch((err) => {
        console.error('API请求错误:', err)
        this.loading = false
        this.tableData = []
        this.total = 0
        this.wait_num = 0
      })

      this.loading = false
      this.tableData = res.data && res.data.results ? res.data.results : []
      this.total = res.data.total

      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    dialogFormVisibleClose() {
      console.log('关闭')
      this.dialogFormVisible = false
    },

    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getMergeTakList(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMergeTakList(true)
    },
    goToView(res) {},
    async addTaskDialog() {
      this.isdisabled = false
      this.$forceUpdate()
      this.dialogFormVisible = true
    },
    async insertSaveAfter() {
      this.dialogFormVisible = false
      this.getMergeTakList()
    },
    resetForm() {
      this.formInline = {
        url: '',
        status_code: '',
        type: ''
      }
    },

    removeOne(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
      }
      // 0：等待扫描 1：扫描中 2：扫描完成 3：扫描失败 4：暂停扫描
      this.$confirm('确定删除任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'scan_del_cancel',
        confirmButtonClass: 'scan_del_sure',
        customClass: 'scan_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = null
          if (icon == 'more') {
            obj = {
              id: this.checkedAll
                ? []
                : this.checkedArr.map((item) => {
                    return item.id
                  }),
              url: this.formInline.url,
              status: this.formInline.status, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
              created_at_range: this.formInline.created_at_range
                ? this.formInline.created_at_range
                : [],
              end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
              is_schedule: 0,
              user_id: this.formInline.user_id,
              operate_company_id: this.currentCompany
            }
          } else {
            obj = {
              id: [id],
              task_type: this.task_type, // 任务分类 1 资产扫描 2 漏洞扫描
              url: this.formInline.url,
              status: this.formInline.status, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
              created_at_range: this.formInline.created_at_range
                ? this.formInline.created_at_range
                : [],
              end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
              is_schedule: 0,
              user_id: this.formInline.user_id,
              operate_company_id: this.currentCompany
            }
          }
          let res
          if (this.$route.path == '/assetsScan') {
            res = await deleteTask(obj)
          } else {
            res = await deleteVulnerability(obj)
          }
          if (res.code == 0) {
            this.$message.success('删除成功!')
            this.currentPage = this.updateCurrenPage(
              this.total,
              icon == 'more' ? this.checkedArr : [1],
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getMergeTakList()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.scan_del>.el-message-box__btns')
        del.children[0].id = 'scan_del_cancel'
        del.children[1].id = 'scan_del_sure'
      }, 50)
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;

  /deep/.home_header {
    position: relative;
    height: 100%;

    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 16px;

      & > div {
        display: flex;
        align-items: center;

        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .el-input {
          width: 240px;
        }

        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }

    .labelText {
      display: flex;
      align-items: center;

      i {
        margin-left: 5px;
      }
    }

    .result {
      position: relative;
      padding: 47px 0 0 24px;
      flex: 0 0 auto;

      .normalBtn {
        position: absolute;
        top: 16px;
        right: 20px;
      }

      .handle {
        padding-top: 30px;
        padding-bottom: 29px;

        .line1 {
          overflow: hidden;

          .progress {
            float: left;
            width: 80%;
            height: 24px;
            background: #e5ebf4;
            border-radius: 12px;

            .el-progress.is-success .el-progress-bar__inner {
              top: 4px;
              left: 4px;
              height: 16px;
              background: -webkit-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -o-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -moz-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              border-radius: 12px;
            }

            ::v-deep .el-progress-bar__outer {
              background-color: #e5ebf4;
              border-radius: 0;
            }

            ::v-deep .el-progress.is-warning .el-progress-bar__inner {
              border-radius: 0;
            }
          }

          .btns {
            display: inline-block;
            padding-left: 16px;
            font-weight: 400;
            color: #2677ff;
            cursor: pointer;

            span {
              margin-right: 12px;

              i {
                margin-right: 3px;
              }
            }
          }
        }

        .line2 {
          width: 80%;
          font-size: 14px;
          color: #999;
          margin-top: 15px;
          display: flex;
          justify-content: space-between;

          .txt3 {
            width: 179px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 114px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt5 {
            display: inline-block;
            width: 86px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }
    }

    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }

    .tableWrap {
      height: calc(100%);
      display: flex;
      justify-content: space-between;
      flex-direction: row-reverse;
      padding: 0 !important;

      .leftTab {
        height: 100%;
        width: 20%;
        overflow-y: auto;
        overflow-x: hidden;

        .el-tree {
          height: 100%;
          background-color: transparent !important;

          & > .el-tree-node {
            box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
            border-radius: 4px;
            color: #37393c;
            overflow-y: auto;

            & > .el-tree-node__content {
              background-color: #fff;
              justify-content: space-around;
              position: relative;

              .el-tree-node__expand-icon {
                position: absolute;
                top: 28%;
                right: 4px;
              }

              .is-leaf {
                display: none !important;
              }
            }
          }

          .is-expanded {
            background: #ffffff linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #f3f7ff 100%);
          }
        }

        .el-tree
          > .el-tree-node:first-child
          > .el-tree-node__children
          > .el-tree-node:last-child
          > .el-tree-node__content {
          height: auto;
        }

        .el-tree-node__children > .el-tree-node > .el-tree-node__content {
          justify-content: space-around !important;
          padding-left: 0px !important;
          cursor: auto;
          height: 40px !important;
        }

        .el-tree-node > .el-tree-node__content {
          justify-content: space-around !important;
          padding-left: 0px !important;
          cursor: auto;
          height: 40px !important;
        }

        .custom-tree-node {
          width: 89%;
          height: 40px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;

          .title {
            display: inline-block;
            width: 80%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .rightWrap {
        padding: 16px 20px;
        position: relative;
        width: 79%;
        box-sizing: border-box;
        background: #fff;

        .el-pagination {
          position: absolute;
          bottom: 0;
          left: 0;
          padding-right: 20px;
        }

        .rightInfo {
          height: calc(100% - 179px);
        }

        .emptyClass {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #909399;
          font-size: 12px;
        }

        .asset-list {
          width: 100%;
          height: 100%;
          padding-right: 20px;
          box-sizing: border-box;
          overflow: auto;
          display: flex;
          flex-wrap: wrap;

          // justify-content:space-between;
          & > li {
            width: 24%;
            height: 311px;
            margin-left: 1%;
            background: #ffffff;
            box-shadow: 0px 4px 20px 0px rgba(16, 33, 62, 0.08);
            border-top: 4px solid #e4ecf8;
            border-radius: 4px;
            margin-bottom: 20px;

            & > div {
              // height: 53%;
              margin-bottom: 12px;
            }

            p {
              padding: 12px 12px 0 12px;
              color: #62666c;
              display: flex;
              align-items: center;

              .el-checkbox {
                .el-checkbox__label {
                  font-size: 16px;
                  color: #37393c;
                  font-weight: 500;
                }
              }
            }

            p:nth-child(1) {
              padding-top: 16px;
              padding-bottom: 12px;
              border-bottom: 1px solid #e9ebef;
              word-break: keep-all;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            & > ul {
              height: calc(100% - 155px);
              overflow: auto;
              display: flex;
              flex-direction: column;
              width: 100%;
              border-radius: 0px 0px 4px 4px;

              li {
                display: flex;
                align-items: center;
                height: 25%;
                color: #fff;
                font-size: 12px;
                width: 100%;

                & > .icon {
                  // position: absolute;
                  // top: 6px;
                  // left: 12px;
                  font-size: 20px;
                  margin-left: 12px;
                  margin-right: 8px;
                }

                & > div {
                  width: calc(100% - 40px);
                  height: 100%;
                  position: relative;
                }
              }

              li:nth-child(1) {
                background: #d4e4ff;
              }

              li:nth-child(2) {
                background: #a8c9ff;
              }

              li:nth-child(3) {
                background: #7dadff;
              }

              li:nth-child(4) {
                background: #5192ff;
              }

              li:nth-child(5) {
                background: #2677ff;
              }
            }
          }
        }
      }

      .el-progress-bar__innerText {
        vertical-align: top !important;
      }
    }

    .el-table {
      border: 0;
      height: calc(100% - 88px) !important;

      td {
        border-right: transparent !important;
      }
    }

    // 定义单元格文本超出不换行
    .el-table .cell {
      overflow: hidden !important;
      white-space: nowrap !important;
    }
  }
}

.searchIcon {
  color: #999;
  cursor: pointer;
  font-size: 14px;
  margin-left: 4px;
}

.taskContentName {
  margin-top: 16px;
  width: 100%;

  .taskContent {
    color: #62666c;
  }
}

/deep/.el-tree > .el-tree-node > .el-tree-node__content,
/deep/.el-tree > .el-tree-node > {
  border-radius: 4px;
}

/deep/.el-tree > .el-tree-node ~ .el-tree-node {
  margin-top: 16px;
}

/deep/.el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: 1px solid #e1e5ed !important;
}

/deep/ th.el-table__cell::after {
  display: none;
}
/deep/.el-tree
  > .el-tree-node
  > .el-tree-node__children
  > .el-tree-node
  > .el-tree-node__content
  > .custom-tree-node
  > .labelText {
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  .title {
    width: 100% !important;
  }
}
.title-esp {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
