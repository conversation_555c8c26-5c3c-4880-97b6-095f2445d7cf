<template>
  <el-dialog
    class="elDialogAdd"
    @opened="open"
    :close-on-click-modal="false"
    :visible.sync="dialogFormVisible"
    :before-close="dialogFormVisibleClose"
    :width="'585px'"
    :height="'720px'"
  >
    <template slot="title">
      {{ ruleForm.id ? '编辑任务' : '添加任务' }}
    </template>
    <div class="dialog-body" v-loading="loading">
      <el-form
        :model="ruleForm"
        :rules="rules"
        style="padding: 0 !important"
        ref="addRuleForm"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入任务名称"></el-input>
        </el-form-item>
        <el-form-item label="扫描目标" prop="ip_type">
          <!-- 扫描目标有三种下拉选择（输入URL信息、上传URL信息文件、IP台账扫描） -->
          <el-select v-model="scan_type" @change="scan_type_change" placeholder="请选择">
            <el-option label="输入URL信息" :value="1"></el-option>
            <el-option v-if="false" label="上传URL信息文件" :value="2"></el-option>
            <el-option v-if="false" label="IP台账扫描" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模糊测试">
          <div slot="label" class="labelText">
            <div>模糊测试：</div>
            <el-tooltip
              class="item1"
              effect="dark"
              placement="top"
              :open-delay="500"
            >
              <p slot="content" style="line-height: 24px">
                向目标发送随机或精心构造的数据作为计算机输入，来触发非常规反馈以达到检测风险的目的
              </p>
              <i class="el-icon-question searchIcon"></i>
            </el-tooltip>
          </div>
          <el-radio v-model="ruleForm.fuzzy" :label="false">否</el-radio>
          <el-radio v-model="ruleForm.fuzzy" :label="true">是</el-radio>
        </el-form-item>

        <el-form-item label="user_agent" >
          <div slot="label" class="labelText">
            <div>user_agent：</div>
            <el-tooltip
              class="item1"
              effect="dark"
              placement="top"
              :open-delay="500"
            >
              <p slot="content" style="line-height: 24px">
                使得服务器能够识别客户使用的操作系统及浏览器版本信息，<br/>填写示例为Mozilla/5.0 (Windows NT 6.1; Win64; x64) <br/> AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.68 Safari/537.36
              </p>
              <i class="el-icon-question searchIcon"></i>
            </el-tooltip>
          </div>
          <el-input v-model="ruleForm.user_agent" placeholder="请输入user_agent"></el-input>
        </el-form-item>

        <el-form-item
          label="URL信息"
          prop=""
          style="position: relative; height: 210px"
          v-if="scan_type == 1"
        >
          <el-input
            type="textarea"
            v-model="ruleForm.url"
            :autosize="{ minRows: 10, maxRows: 16 }"
            class="placeholderIdBox"
          ></el-input>
          <div class="placeholderId" v-show="!ruleForm.url">
            <div></div>
            <div>请输入URL，URL支持格式如下</div>
            <div>https://foradar.baimaohui.net</div>
            <div>https://foradar.baimaohui.net/login</div>
            <div>https://foradar.baimaohui.net:8443/dashboard</div>
            <div>http://***************</div>
            <!-- <div>多个连续网段支持格式：</div>
            <div>192.168.1-10.*（代表***********-************共10个网段）</div> -->
            <!-- <div>最多输入1000个ip，分号或换行分隔</div> -->
          </div>
        </el-form-item>

        <el-form-item label="" prop="" v-if="scan_type == 2">
          <p class="downloadClass" @click="downloadForbidIpsExcel">
            <i class="el-icon-warning"></i>请点击下载
            <span>IP信息导入模板</span>
          </p>
          <el-upload
            class="upload-demo"
            drag
            :action="golangUploadSrcIp + '/assets/task/import_ip_file'"
            :headers="uploadHeaders"
            accept=".xlsx"
            :on-success="uploadSuccess"
            :on-error="uploadError"
            :on-remove="uploadRemove"
            :show-file-list="true"
            :before-upload="beforeUpload"
            :limit="uploadMaxCount"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过3M</div>
          </el-upload>
          <p v-if="ruleForm.ips.length > 0" style="color: #2677ff">上传的IP信息</p>
          <p
            v-if="ruleForm.ips.length > 0"
            class="uploadIps"
            v-html="ruleForm.ips.length > 0 ? ruleForm.ips.join('\n') : ''"
          ></p>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="dialogFormVisibleClose" id="scan_cancel">关闭</el-button>
      <el-button class="highBtn" :loading="btnLoading" @click="insertSave" id="scan_sure"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import newInputNumber from '@/components/input-number/index'
import { mapGetters, mapState } from 'vuex'
import { apiAnalysisTask } from '@/api/apiConfig/api.js'

export default {
  props: ['dialogFormVisible','operate_company_id'],
  name: 'taskAddEditDialog',
  components: {
    newInputNumber
  },
  data() {
    return {
      loading: false,
      btnLoading: false,
      btnReLoading: false,
      scan_type: 1,
      uploadHeaders: { Authorization: localStorage.getItem('token') },
      uploadMaxCount: 1,
      fileList: [],

      ruleForm: {
        name: '',
        fuzzy: false,
        user_agent: '',
        url: '',
        ips: []
      },
      rules: {
        name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.open()
  },
  methods: {
    ...mapGetters(['getterWebsocketMessage']),
    ...mapState(['currentCompany']),
    scan_type_change(val) {
      console.log(val)
    },
    open() {
      // 初始化的时候
      this.ruleForm = {
        name: '',
        fuzzy: false,
        user_agent: '',
        url: '',
        ips: []
      }
    },
    insertSave() {
      // this.$emit('insertSave')
      console.log(this.ruleForm)
      this.btnLoading = true;
      this.$refs['addRuleForm'].validate((valid) => {
        if (valid) {
          let obj = {
            name: this.ruleForm.name,
            scan_type: this.scan_type,
            fuzzy: this.ruleForm.fuzzy,
            user_agent: this.ruleForm.user_agent,
            url: this.ruleForm.url.split('\n'),
            scan_type: [1, 2].includes(this.scan_type) ? 2 : 1,
            operate_company_id: this.operate_company_id,
            // end_time: null
      }        
      
      // let obj = {
      //   name: this.ruleForm.name,
      //   scan_type: this.scan_type,
      //   fuzzy: this.ruleForm.fuzzy,
      //   user_agent: this.ruleForm.user_agent,
      //   url: this.ruleForm.url.split('\n'),
      //   scan_type: [1, 2].includes(this.scan_type) ? 2 : 1,
      //   operate_company_id: this.operate_company_id,
      //   // end_time: null
      // }
      console.log("------------------------", obj)
      apiAnalysisTask(obj)
        .then((res) => {
          console.log(res)

          if (res.code === 0) {
            this.$message.success('添加成功')
            this.$emit('reload')
            this.dialogFormVisibleClose()
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.$message.error(err.message)
        })
        .finally(() => {
          this.btnLoading = false
          this.dialogFormVisibleClose()
        })
      }else {
        this.$message.error('添加失败，请检查输入内容');  
        this.btnLoading = false      
        return false;
      }
    })
    },
    dialogFormVisibleClose() {
      this.open()
      this.$emit('dialogFormVisibleClose', false)
    },
    uploadSuccess(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.ruleForm.ips = []
        this.file_name = file.name
        this.ruleForm.ips = response.data.data_list
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadError(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.ruleForm.ips = []
      }
    },
    beforeUpload(file, limit = 3) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    },
    downloadForbidIpsExcel() {
      window.location.href = '/downloadTemplate/IP信息导入模板.xlsx'
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    }
  }
}
</script>

<style lang="less" scoped>
.placeholderIdBox {
  position: absolute;
  top: 0px;
  background-color: transparent !important;

  /deep/.el-textarea__inner {
    background-color: transparent !important;
  }
}

.placeholderId {
  margin-top: 10px;

  div {
    min-height: 22px;
    line-height: 22px;
    color: #c0c4cc;
    padding-left: 15px;
  }
}

.downloadClass {
  margin-top: 10px;
  margin: 0px 0px 16px !important;
}

/deep/.upload-demo {
  width: 100%;

  .el-upload {
    width: 100%;
  }

  .el-upload-dragger {
    width: 100%;
  }

  .el-upload-list {
    height: 50px !important;
    min-height: 50px;
    overflow-y: auto;
  }

  .el-upload-list__item {
    height: 50px !important;
  }
}

.uploadIps {
  height: 118px;
  overflow: auto;
  border: 1px solid #e9ebef;
  border-radius: 5px;
  padding: 10px;
  white-space: pre-line;
}
.labelText {
  display: flex;
  align-items: center;
  div {
    width: 100px;
  }
  i {
    margin-left: 5px;
  }
}
</style>
