<template>
  <div class="container">
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.name"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入任务名称进行搜索"
            id="scan_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span v-if="userIsOpen" @click="highCheckIsShow" id="scan_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="scan_all"
            >选择全部</el-checkbox
          >
          <el-button
            :disabled="!userIsOpen"
            class="normalBtnRe"
            type="primary"
            @click="removeOne('more')"
            id="scan_more_del"
            >删除</el-button
          >
          <el-button
            :disabled="!userIsOpen"
            class="normalBtn"
            type="primary"
            @click="addTaskDialog"
            id="scan_add"
            >新建任务</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="task"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()" v-loading="loading" style="padding: 0px 20px">
        <el-table
          border
          :data="tableData"
          row-key="user_task_id"
          current-row-key="user_task_id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p
                >当前暂无扫描任务<i>，您可以<span @click="addTaskDialog">新建任务</span></i></p
              >
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :show-overflow-tooltip="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeaderIsShow"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <!-- 0：等待扫描 1：扫描中 2：扫描完成 3：扫描失败 4：暂停扫描 -->
              <span v-if="item.name == 'name'">{{ scope.row[item.name] }}</span>
              <span v-else-if="item.name == 'status'">
                <!-- 资产扫描 -->
                <span>
                  <span v-if="scope.row['status'] == 0" class="grayLine">等待扫描</span>
                  <span v-if="scope.row['status'] == 1" class="blueLine">扫描中</span>
                  <span v-if="scope.row['status'] == 2" class="greenLine">扫描完成</span>
                  <span v-if="scope.row['status'] == 3" class="redLine">扫描失败</span>
                  <span v-if="scope.row['status'] == 4" class="yellowLine">
                    <el-tooltip placement="top" v-if="scope.row['status'] == 4" :open-delay="500">
                      <div slot="content">
                        <span v-if="Math.floor(scope.row['progress']) <= 50">
                          <span>当前处于禁扫时间内，如需继续执行任务，请重新</span
                          ><span @click="changSetTimeBox" style="color: #409eff; cursor: pointer"
                            >设置禁扫时间</span
                          >
                        </span>
                        <span v-else>扫描已完成，正在进行入库操作，不会对资产造成影响</span>
                      </div>
                      <i class="el-icon-question" style="cursor: pointer">暂停扫描</i>
                    </el-tooltip>
                  </span>
                  <el-progress
                    v-if="scope.row['status'] == 1 || scope.row['status'] == 4"
                    :text-inside="true"
                    :stroke-width="12"
                    :percentage="parseFloat(scope.row['progress'])"
                    :status="setProgressColor(scope.row['status'])"
                  ></el-progress>
                </span>
              </span>
              <span v-else-if="item.name == 'end_time'">{{
                scope.row['status'] == 2 ? scope.row['end_time'] : '-'
              }}</span>

              <span v-else-if="item.name == 'used_time'">{{
                scope.row['status'] == 2 ? secondsFormat(scope.row['used_time']) : '-'
              }}</span>
              <span v-else-if="item.name == 'total_risks'">{{ getResult(scope.row) }}</span>
              <span v-else-if="item.name == 'operator'">{{ scope.row[item.name] }}</span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="180">
            <template slot-scope="scope">
              <span>
                <el-button
                  type="text"
                  size="small"
                  v-if="scope.row['status'] == 2"
                  @click="againRun(scope.row.user_task_id)"
                  id="scan_again"
                  >再次执行</el-button
                >
                <el-button
                  v-if="scope.row['status'] == 2"
                  type="text"
                  size="small"
                  @click="viewList(scope.row.user_task_id, scope.row.status)"
                  id="scan_info"
                  >查看详情</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  @click="removeOne('one', scope.row.user_task_id)"
                  id="scan_del"
                  >删除</el-button
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        v-if="currentPageIsShow"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <taskAddEditDialog
      @validateConfirm="validateConfirm"
      :showList="showValidateList"
      :dialogFormVisible="dialogFormVisible"
      @insertSaveAfter="insertSaveAfter"
      :rules="rules"
      @dialogFormVisibleClose="dialogFormVisibleClose"
      :isdisabled="isdisabled"
      @reload="reload"
      :operate_company_id="currentCompany"
    />
    <assetsValidate
      :dialogVisible="assetsValidDialogVisible"
      :validateType="validateType"
      @copyText="copyText"
      :list="errorList"
      @close="assetsValidDialogVisible = false"
    />
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="85px">
          <el-form-item label="任务名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入任务名称"></el-input>
          </el-form-item>
          <el-form-item label="任务状态：" prop="status">
            <el-select
              filterable
              clearable
              v-model="formInline.status"
              @change="selectChange($event, 'status', statusArr, true, false)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="发起人：" prop="op_id">
            <el-select filterable clearable v-model="formInline.op_id"
              @change="selectChange($event, 'op_id', userArr, true, false)" placeholder="请选择">
              <el-option v-for="item in userArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="开始时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间：" prop="end_at_range">
            <el-date-picker
              v-model="formInline.end_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="scan_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="scan_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
const throttle = (func, wait = 50) => {
  // 上一次执行该函数的时间
  let lastTime = 0
  return function (...args) {
    // 当前时间
    let now = +new Date()
    // 将当前时间和上一次执行函数时间对比
    // 如果差值大于设置的等待时间就执行函数
    if (now - lastTime > wait) {
      lastTime = now
      func.apply(this, args)
    }
  }
}
import assetsValidate from '../assetsView/assetsValidate.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import taskAddEditDialog from './taskAddEditDialog.vue'
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  nowStartRunningTask,
  orderTask,
  apiAnalysisTaskList,
  getVulnerability,
  remind,
  getVulnerabilityDetails,
  deleteAnalysisTask,
  apiAnalysisTask
} from '@/api/apiConfig/api.js'

import {
  startRunningTask,
  pauseRunningTask,
  getAllUser,
  scanTaskDetail,
  deleteTask,
  getAllTaskList
} from '@/api/apiConfig/discovery.js'

export default {
  components: { taskAddEditDialog, tableTooltip, hightFilter, assetsValidate },
  props: ['task_type'],
  data() {
    return {
      againRun: null,
      UpdatePocRiskBtnLoading: false,
      showValidateList: [],
      correctList: [],
      errorList: [],
      assetsValidDialogVisible: false,
      validateType: '',
      highTabShow: [
        {
          label: '任务名称',
          name: 'name',
          type: 'input'
        },
        {
          label: '任务状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '发起人',
          name: 'op_id',
          type: 'select'
        },
        {
          label: '开始时间',
          name: 'created_at_range',
          type: 'date'
        },
        {
          label: '结束时间',
          name: 'end_at_range',
          type: 'date'
        }
      ],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      currentPercent: 0,
      progressBar: false,
      wait_num: 0,
      last_rist_time: 0,
      loading: false,
      checkedAll: false,
      highCheckdialog: false,
      pauseAndStart: true,
      finshed_num: 0,
      wtLength: 0,
      changtingFlag: false,
      transferProp: {},
      transferData: [],
      checkedArr: [],
      userArr: [],
      statusArr: [
        {
          id: 1,
          name: '扫描中'
        },
        {
          id: 0,
          name: '等待扫描'
        },
        {
          id: 2,
          name: '扫描完成'
        },
        {
          id: 3,
          name: '扫描失败'
        }
        // {
        //   id: 4,
        //   name: '暂停扫描'
        // }
      ],
      scanId: '', //全部常用端口的id
      isdisabled: false,
      formInline: {
        name: '',
        type_name: '',
        status: '',
        created_at_range: [],
        end_at_range: [],
        user_id: '',
        op_id: '',
        task_id: ''
      },
      dialogFormVisible: false,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      currentPage: 1,
      data: [],
      filterMethod(query, item) {
        return item.pinyin.indexOf(query) > -1
      },
      rules: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        ips: [{ required: true, message: '请输入或上传ip信息', trigger: 'change' }]
      },

      dialogFormVisibleFenzu: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      tableData: [],
      currentPageIsShow: true,
      tableHeader: [
        {
          label: '任务名称',
          name: 'name',
          fixed: 'left',
          minWidth: '90'
        },
        {
          label: '任务状态',
          name: 'status',
          minWidth: '90'
        },
        {
          label: '开始时间',
          name: 'start_time',
          minWidth: '120'
        },
        {
          label: '结束时间',
          name: 'end_time',
          minWidth: '120'
        },
        {
          label: '任务耗时',
          // name: 'use_seconds',
          name: 'used_time',
          minWidth: '80'
        },
        {
          label: '扫描结果',
          // name: 'result',
          name: 'total_risks',
          minWidth: '90'
        },
        {
          label: '发起人',
          // name: 'op',
          name: 'operator',
          minWidth: '120'
        }
      ],
      ww: 0, //下面为扫描动画
      wh: 0,
      c: '',
      ctx: {},
      center: {},
      color_gold: '',
      deg_to_pi: '',
      time: 0,
      enemies: [],
      runningTask: null,
      user: {
        role: ''
      },
      currentId: '',
      runningFuncTimer: null,
      highlist: null,
      userIsOpen: true, // 权限控制
      timers: null
    }
  },
  computed: {
    ...mapState([
      'socketTimestamp',
      'websocketMessage',
      'currentCompany',
      'setTimeBox',
      'currentCompanyId',
      'currentCompany'
    ]),
    ...mapGetters([
      'getterCurrentCompany',
      // 'getterWebsocketMessage',
      'getterSocketTimestamp',
      'getterSettime'
    ]),
    tableHeaderIsShow() {
      let arr = []
      arr = this.tableHeader.filter((item, index) => {
        return !item.path || (item.path && item.path.indexOf(this.$route.path) != -1)
      })
      return arr
    }
  },
  watch: {
    getterSettime(val) {
      // 监听禁扫时间
      setTimeout(() => {
        this.getMergeTakList()
      }, 3000)
    },
    // // 监听到端有返回信息

    getterCurrentCompany(val) {
      setTimeout(() => {
        // 权限控制
        if (sessionStorage.getItem('companyInfo')) {
          let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
          this.userIsOpen = true
        } else {
          this.userIsOpen = true
        }
      }, 1000)
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getMergeTakList()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  created() {
    if (sessionStorage.getItem('queryParam')) {
      // 从查看列表页面返回的保留筛选条件等
      let queryParam = JSON.parse(sessionStorage.getItem('queryParam'))
      this.currentPageIsShow = false
      this.currentPageIsShow = true
      this.currentPage = queryParam.page
      this.pageSize = queryParam.per_page
      this.formInline.name = queryParam.name
      this.formInline.status = queryParam.status
      this.formInline.created_at_range = queryParam.created_at_range
      this.formInline.end_at_range = queryParam.end_at_range
      this.formInline.user_id = queryParam.user_id
      this.formInline.task_id = queryParam.task_id
    }
    if (sessionStorage.getItem('companyInfo')) {
      // 权限控制
      let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      this.userIsOpen = true
    } else {
      this.userIsOpen = true
    }
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      if (this.user.role == 2) {
        if (!this.currentCompany) {
          return
        }
        this.getMergeTakList()
      } else {
        this.getMergeTakList()
      }
    }
  },
  async mounted() {
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
    this.againRun = throttle(function (id) {
      this.againRunFun(id)
    }, 3000)
    this.timers = setInterval(() => {
      this.getMergeTakList(false, false)
    }, 5000)
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
    this.timers && clearInterval(this.timers)
  },

  methods: {
    ...mapMutations(['setTimeChangeBox', 'changeMenuId']),

    validateConfirm(errorList, correctList, validType) {
      this.assetsValidDialogVisible = true
      this.errorList = errorList
      this.correctList = correctList

      this.validateType = validType
      if (validType == 'upload') {
        this.showValidateList = errorList.concat(correctList)
      } else {
        this.showValidateList = errorList.concat(correctList).join('\n')
      }
    },

    copyText(val) {
      // 点击确定移除并复制按钮
      // 需要将输入框中的数量更新
      if (this.validateType == 'upload') {
        this.showValidateList = this.correctList
      } else {
        this.showValidateList = this.correctList.join('\n')
      }
      this.$copyText(val.join('\r')).then(
        (res) => {
          this.$message.success('复制成功')
          this.assetsValidDialogVisible = false
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    changSetTimeBox() {
      //禁扫时间弹框
      if (this.setTimeBox) {
        this.setTimeChangeBox(false)
      } else {
        this.setTimeChangeBox(true)
      }
    },

    handleSelectable(row, index) {
      return !this.checkedAll
    },

    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选查询
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      console.log('高级筛选：', this.highlist)
      this.hightFilterIsShow()
      this.getMergeTakList()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    setProgressColor(status) {
      if (status == 4) {
        return 'warning'
      }
    },
    async highCheckIsShow() {
      this.highCheckdialog = true
      let res = await getAllUser()
      this.userArr = res.data
      console.log('this.userArr:', this.userArr)
    },
    reload() {
      this.getMergeTakList()
    },
    async getMergeTakList(tmp, loading = true) {
      if (!tmp && loading) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        name: this.formInline.name,
        operate_company_id: this.currentCompany,
        created_at_range: this.formInline.created_at_range ? this.formInline.created_at_range : [],
        end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
        name: this.formInline.name,
        status: this.formInline.status
        // user_id: this.formInline.user_id,
      }
      loading && (this.loading = true)
      let res
      res = await apiAnalysisTaskList(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
        this.wait_num = 0
      })

      this.loading = false
      const handleData = () => {
        this.tableData = res.data && res.data.task_list ? res.data.task_list : []
        console.log('this.checkedArr', this.checkedArr)
        this.total = res.data.total
        this.wait_num = res.data.wait_num
        this.last_rist_time = res.data.last_finish_count_risk_ip_time
      }

      if (!loading && this.total == res.data.total) {
        // 如果数量一样 但是 内容的中的 某一对象的 user_task_id 或者 status 字段发生了变化 就进行赋值 刷新
        handleData()
      } else {
        handleData()
      }

      console.log('this.--------------------checkedArr', this.checkedArr)
      // if (!loading && this.checkedArr.length > 0) {

      //   this.$nextTick(() => {
      //     console.log('要进行请求完的处理', this.checkedArr)
      //     this.checkedArr.forEach((row) => {
      //       this.$refs.eltable.toggleRowSelection(row, true)
      //     })
      //   })
      // }
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
      this.$forceUpdate()
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
      this.checkedArr = this.$refs.eltable.selection
    },
    getResult(row) {
      return `总风险数量${row.total_risks}个`
    },
    // websocket执行
    runningFunc(res) {
      if (!('scan_poc_num' in res.data) || res.data.user_id == this.currentCompany) {
        // 漏洞核查与资产、漏洞扫描区分，scan_poc_num存在是漏洞核查任务
        if (res.data.status == 2) {
          if (res.cmd == 'scan_task_progress') {
            resetMessage.success('扫描成功！')
          }
          this.currentId = '' // 控制推送结束后仅执行一次
          this.getMergeTakList()
        } else if (res.data.status == 1) {
          // 正在扫描
          this.currentId = res.data.user_id
          if (res.data.progress == 100) {
            // 避免状态为1时进度已经达到100但是页面不提示完成信息，可手动变成99.9，状态为2时正常提示
            res.data.progress = 99.9
          }
          // 推送数据渲染到列表
          this.tableData.forEach((item, index) => {
            if (item.id == res.data.task_id) {
              this.$set(this.tableData[index], 'is_audit', 1) // 漏洞扫描审核通过后，审核状态要改变
              this.$set(this.tableData[index], 'status', res.data.status)
              this.$set(this.tableData[index], 'progress', res.data.progress)
              this.$set(this.tableData[index], 'used_time', res.data.used_time)
              this.$set(this.tableData[index], 'start_time', res.data.start_time)
            }
          })
        } else if (res.data.status == 4) {
          // 暂停扫描
          this.currentId = res.data.user_id
          // 推送数据渲染到列表
          this.tableData.forEach((item, index) => {
            if (item.id == res.data.task_id) {
              this.$set(this.tableData[index], 'status', res.data.status)
              this.$set(this.tableData[index], 'progress', res.data.progress)
              this.$set(this.tableData[index], 'used_time', res.data.used_time)
              this.$set(this.tableData[index], 'start_time', res.data.start_time)
            }
          })
        } else {
          // 3 扫描失败
          this.getMergeTakList()
        }
      }
    },
    // socket 关闭
    socketClose() {
      if (this.socket) {
        this.socket.close()
        this.socket = null
      }
    },
    dialogFormVisibleClose() {
      console.log('关闭')
      this.dialogFormVisible = false
    },
    async tContinue(id) {
      let obj = {
        id: id,
        operate_company_id: this.currentCompany
      }
      let res = await startRunningTask(obj)
      if (res.code == 0) {
        this.getMergeTakList()
      }
    },
    async pause(id) {
      let obj = {
        id: id,
        operate_company_id: this.currentCompany
      }
      let res = await pauseRunningTask(obj)
      if (res.code == 0) {
        this.getMergeTakList()
      }
    },
    handleSelectionChange(val) {
      const uniqueMap = new Map();

      // 遍历选中的数据，以user_task_id为键存入Map
      val.forEach(item => {
        uniqueMap.set(item.user_task_id, item);
      });
      console.log('选中数据:', val)
      // 将Map中的值转换回数组
      this.checkedArr = Array.from(uniqueMap.values());
      // this.checkedArr = val

    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getMergeTakList(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMergeTakList(true)
    },
    goToView(res) {},
    async addTaskDialog() {
      this.isdisabled = false
      this.$forceUpdate()
      this.dialogFormVisible = true
    },
    async insertSaveAfter() {
      this.dialogFormVisible = false
      this.getMergeTakList()
    },
    resetForm() {
      this.formInline = {
        name: '',
        status: '',
        created_at_range: [],
        user_id: ''
      }
    },
    // 立即执行
    async nowStartRun(id) {
      let res = await nowStartRunningTask({ id: id, operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.$message.success('操作成功!')
        this.getMergeTakList()
      }
    },
    async upDownOne(id, action) {
      let obj = {
        id: id,
        action: action,
        operate_company_id: this.currentCompany
      }
      let res = await orderTask(obj)
      if (res.code == 0) {
        this.getMergeTakList()
      }
    },
    removeOne(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
      }
      // 0：等待扫描 1：扫描中 2：扫描完成 3：扫描失败 4：暂停扫描
      this.$confirm('确定删除任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'scan_del_cancel',
        confirmButtonClass: 'scan_del_sure',
        customClass: 'scan_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = null
          if (icon == 'more') {
            obj = {
              task_id: this.checkedAll
                ? []
                : this.checkedArr.map((item) => {
                    return item.user_task_id
                  }),
              name: this.formInline.name ? this.formInline.name : null,
              status: [0, 1, 2, 3].includes(this.formInline.status) ? this.formInline.status : null, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
              created_at_range: this.formInline.created_at_range
                ? this.formInline.created_at_range
                : [],
              end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
              user_id: this.formInline.user_id ? this.formInline.user_id : null,
              operate_company_id: this.currentCompany
            }
          } else {
            obj = {
              task_id: [id],
              task_type: this.task_type, // 任务分类 1 资产扫描 2 漏洞扫描
              name: this.formInline.name ? this.formInline.name : null,
              status: [0, 1, 2, 3].includes(this.formInline.status) ? this.formInline.status : null, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
              created_at_range: this.formInline.created_at_range
                ? this.formInline.created_at_range
                : [],
              end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
              user_id: this.formInline.user_id ? this.formInline.user_id : null,
              operate_company_id: this.currentCompany
            }
          }
          let res
          if (this.$route.path == '/assetsScan') {
            res = await deleteTask(obj)
          } else {
            res = await deleteAnalysisTask(obj)
          }
          if (res.code == 0) {
            this.$message.success('删除成功!')
            this.currentPage = this.updateCurrenPage(
              this.total,
              icon == 'more' ? this.checkedArr : [1],
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getMergeTakList()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.scan_del>.el-message-box__btns')
        del.children[0].id = 'scan_del_cancel'
        del.children[1].id = 'scan_del_sure'
      }, 50)
    },
    async againRunFun(id) {
      // this.dialogFormVisible = true
      let res
      let data = {
        task_id: id,
        scan_type: 2,
        operate_company_id: this.currentCompany
      }
      res = await apiAnalysisTask(data).catch((err) => {
        this.$message.error('操作失败!')
      })
      if (res.code == 0) {
        this.$message.success('操作成功!')
        this.getMergeTakList()
      }
    },

    viewList(id, status) {
      console.log('id:', id)

      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        // name: this.formInline.name,
        // status: this.formInline.status, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
        // created_at_range: this.formInline.created_at_range,
        // end_at_range: this.formInline.end_at_range,
        // user_id: this.formInline.user_,
        operate_company_id: this.currentCompany,
        user_task_id: id
        // task_id: this.formInline.task_id
      }
      console.log('obj:', obj)

      sessionStorage.setItem('queryParam', JSON.stringify(obj))

      sessionStorage.setItem('queryParam', JSON.stringify(obj))
      this.$router.push({
        path: '/viewDetails',
        query: {
          id: id
          // task_type: this.task_type,
          // status: status
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;

  /deep/.home_header {
    position: relative;
    height: 100%;

    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;

      & > div {
        display: flex;
        align-items: center;

        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .el-input {
          width: 240px;
        }

        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }

    .result {
      position: relative;
      padding: 47px 0 0 24px;
      flex: 0 0 auto;

      .normalBtn {
        position: absolute;
        top: 16px;
        right: 20px;
      }

      .scanning {
        display: flex;
        align-items: center;

        .scanning-img {
          width: 56px;
          height: 56px;

          svg {
            font-size: 56px;
            color: #ddd;
          }
        }

        .scanning-txt {
          margin-left: 15px;
          line-height: 1;

          .txt1 {
            color: #62666c;
            font-size: 16px;
            font-weight: 400;

            .name {
              font-weight: 500;
              color: #37393c;
            }
          }

          .txt2 {
            font-size: 12px;
            font-weight: 400;
            color: #62666c;
            padding-top: 10px;

            .time {
              margin-left: 21px;
            }
          }

          .txt3 {
            width: 248px;
            height: 16px;
            margin-bottom: 14px;
            background: #e5ebf4;
          }

          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 180px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt5 {
            display: inline-block;
            width: 132px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }

      .handle {
        padding-top: 30px;
        padding-bottom: 29px;

        .line1 {
          overflow: hidden;

          .progress {
            float: left;
            width: 80%;
            height: 24px;
            background: #e5ebf4;
            border-radius: 12px;

            .el-progress.is-success .el-progress-bar__inner {
              top: 4px;
              left: 4px;
              height: 16px;
              background: -webkit-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -o-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -moz-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              border-radius: 12px;
            }

            ::v-deep .el-progress-bar__outer {
              background-color: #e5ebf4;
              border-radius: 0;
            }

            ::v-deep .el-progress.is-warning .el-progress-bar__inner {
              border-radius: 0;
            }
          }

          .btns {
            display: inline-block;
            padding-left: 16px;
            font-weight: 400;
            color: #2677ff;
            cursor: pointer;

            span {
              margin-right: 12px;

              i {
                margin-right: 3px;
              }
            }
          }
        }

        .line2 {
          width: 80%;
          font-size: 14px;
          color: #999;
          margin-top: 15px;
          display: flex;
          justify-content: space-between;

          .txt3 {
            width: 179px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 114px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt5 {
            display: inline-block;
            width: 86px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }
    }

    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }

    .tableWrap {
      height: calc(100% - 129px);

      .emptyClass {
        height: 100%;
        text-align: center;
        vertical-align: middle;

        svg {
          display: inline-block;
          font-size: 120px;
        }

        p {
          line-height: 25px;
          color: #d1d5dd;

          span {
            margin-left: 4px;
            color: #2677ff;
            cursor: pointer;
          }
        }
      }

      .el-progress-bar__innerText {
        vertical-align: top !important;
      }
    }

    .el-table {
      border: 0;

      td {
        border-right: transparent !important;
      }
    }

    // 定义单元格文本超出不换行
    .el-table .cell {
      overflow: hidden !important;
      white-space: nowrap !important;
    }
  }
}
</style>
