<template>
  <div class="container">
    <div class="headerTitle">
      <div>
        <span class="goback" v-if="activeName == 'first'" @click="goBack"
          ><i class="el-icon-arrow-left"></i>返回<span class="spline">/</span></span
        >
        接口风险识别
        <span v-if="activeName == 'second'"><span class="spline">/</span>测绘记录</span>
        <span class="headerShow" v-if="activeName == 'first'"
          ><i class="el-icon-warning"></i>支持对目标域名智能提取潜在API风险</span
        >
      </div>

    </div>
    <div class="home_header">
      <div class="tab_content" style="height: 100%">
        <taskScan
          :task_type="1"
          :wait_confirm="0"
        />
      </div>
    </div>
  </div>
</template>

<script>
// import unitTask from './unitTask.vue'
// import unitRecord from './unitRecord.vue'
// import ipAssets from '../assetsView/ipAssets.vue'
import taskScan from './taskLeakScan.vue'

import { mapGetters, mapState, mapMutations } from 'vuex'
export default {
  components: { taskScan },
  data() {
    return {
      recommentIsShow: false,
      activeName: 'first',
      taskId: ''
    }
  },
  watch: {
    getterWebsocketMessage(msg) {}
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage'])
  },
  mounted() {
    // 发现记录切换企业跳转回来的
    if (this.$route.query.isrecordBack) {
      this.activeName = 'second'
    }
    if (this.$route.query.activeName) {
      this.activeName = this.$route.query.activeName
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goBack() {
      sessionStorage.setItem('menuId', '2-1')
      this.changeMenuId('2-1')
      this.$router.push('/assetsTanzhi')
    },
    // 用于任务记录查看详情
    sonData(val) {
      this.activeName = 'first'
      this.taskId = val
    },
    handleClick(tab) {
      this.$router.push({
        query: {
          activeName: tab.name || 'first'
        }
      })
      this.taskId = ''
    }
  }
}
</script>

<style lang="less" scoped>
.headerTitle {
  width: calc(100% - 32px);
  // width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  .btn {
    font-weight: 400;
    color: #62666c;
    margin-left: 16px;
    &.el-button {
      padding: 0;
    }
    img {
      height: 14px;
    }
    &:hover {
      cursor: pointer;
      color: #2677ff;
    }
  }
}
.container {
  position: relative;
  width: 100%;
  height: 100%;
  // background: #FFFFFF;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  // background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    & > .tabsWrap {
      position: relative;
      .el-tabs__nav {
        padding-left: 20px;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        // min-width: 60px;
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        // padding: 0;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
      }
    }
  }
}
/deep/.tableWrap {
  padding: 0 20px;
}
/deep/.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  & > div {
    .el-input {
      width: 240px;
    }
    .el-select {
      width: 240px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      // margin-left: 16px;
      cursor: pointer;
    }
  }
}
</style>
