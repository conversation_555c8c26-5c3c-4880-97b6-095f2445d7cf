<template>
  <div class="container">
    <div class="headerTitle">
      <span>
        <span class="goback" @click="$router.go(-1)"
          ><i class="el-icon-arrow-left"></i>返回上一层</span
        >
        <span class="spline">/</span>
        <span>{{ headerTitle }}</span>
      </span>
    </div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.url"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入URL进行搜索"
            id="scan_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="scan_all"
            >选择全部</el-checkbox
          >
          <el-button
            :disabled="!userIsOpen"
            class="normalBtnRe"
            type="primary"
            @click="exportList"
            :loading="exportLoadingBtn"
            id="scan_more_export"
            >导出</el-button
          >
          <el-button
            :disabled="!userIsOpen"
            class="normalBtnRe"
            type="primary"
            @click="removeOne('more')"
            id="scan_more_del"
            >删除</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <div :class="hightFilterIsShow()" v-loading="loading" style="padding: 0px 20px">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :show-overflow-tooltip="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeaderIsShow"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'url'">
                <el-tooltip
                  effect="dark"
                  placement="top"
                  :open-delay="500"
                  style="margin-left: 2px"
                >
                  <div slot="content">
                    <i
                      class="el-icon-document-copy"
                      @click="copyTextText(scope.row['url'])"
                      style="color: #2677ff; cursor: pointer; margin-right: 6px"
                    ></i>
                    {{ scope.row[item.name] }}
                  </div>
                  <a
                    v-if="scope.row[item.name] && String(scope.row[item.name]).includes('http')"
                    :href="scope.row[item.name]"
                    target="_blank"
                    style="color: #2677ff"
                    >{{ scope.row[item.name] }}</a
                  >
                  <p class="ellipsis" v-else>{{
                    scope.row[item.name] ? scope.row[item.name] : '-'
                  }}</p>
                </el-tooltip>
              </span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="180">
            <template slot-scope="scope">
              <span>
                <el-button type="text" size="small" @click="checkDetails(scope.row.task_id)"
                  >查看风险详情</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  @click="removeOne('one', scope.row.task_id)"
                  id="scan_del"
                  >删除</el-button
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        v-if="currentPageIsShow"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
const throttle = (func, wait = 50) => {
  // 上一次执行该函数的时间
  let lastTime = 0
  return function (...args) {
    // 当前时间
    let now = +new Date()
    // 将当前时间和上一次执行函数时间对比
    // 如果差值大于设置的等待时间就执行函数
    if (now - lastTime > wait) {
      lastTime = now
      func.apply(this, args)
    }
  }
}
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex'
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  nowStartRunningTask,
  orderTask,
  apiAnalysisUserTask,
  getVulnerability,
  remind,
  getVulnerabilityDetails,
  deleteAnalysisTaskResult,
  exportAnalysisTask
} from '@/api/apiConfig/api.js'

import {
  startRunningTask,
  pauseRunningTask,
  getAllUser,
  scanTaskDetail,
  deleteTask,
  getAllTaskList
} from '@/api/apiConfig/discovery.js'

export default {
  components: { tableTooltip, hightFilter },
  props: ['task_type'],
  data() {
    return {
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      wait_num: 0,
      loading: false,
      checkedAll: false,
      checkedArr: [],
      formInline: {
        url: '',
        type_name: '',
        status: '',
        created_at_range: [],
        end_at_range: [],
        user_id: '',
        op_id: '',
        task_id: ''
      },
      highCheckdialog: false,
      pageSizeArr: [10, 30, 50],
      pageSize: 50,
      total: 0,
      currentPage: 1,
      filterMethod(query, item) {
        return item.pinyin.indexOf(query) > -1
      },

      tableData: [],
      currentPageIsShow: true,
      tableHeader: [
        {
          label: 'URL资产',
          name: 'url',
          fixed: 'left',
          minWidth: '100'
        },
        {
          label: '发现风险',
          name: 'risk_count',
          minWidth: '100'
        }
      ],
      user: {
        role: ''
      },
      highlist: null,
      userArr: [],
      userIsOpen: true, // 权限控制
      exportLoadingBtn: false
    }
  },
  computed: {
    ...mapState([
      'socketTimestamp',
      'websocketMessage',
      'currentCompany',
      'setTimeBox',
      'currentCompanyId',
    ]),
    ...mapGetters([
      'getterCurrentCompany',
      // 'getterWebsocketMessage',
      'getterSocketTimestamp',
      'getterSettime'
    ]),
    tableHeaderIsShow() {
      let arr = []
      arr = this.tableHeader.filter((item, index) => {
        return !item.path || (item.path && item.path.indexOf(this.$route.path) != -1)
      })
      return arr
    }
  },
  watch: {
    getterSettime(val) {
      // 监听禁扫时间
      setTimeout(() => {
        this.getMergeTakList()
      }, 3000)
    },
    // // 监听到端有返回信息

    getterCurrentCompany(val) {
      setTimeout(() => {
        // 权限控制
        if (sessionStorage.getItem('companyInfo')) {
          let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
          this.userIsOpen = true
        } else {
          this.userIsOpen = true
        }
      }, 1000)
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getMergeTakList()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  created() {
    this.headerTitle = this.$route.name
    if (sessionStorage.getItem('queryParam')) {
      // 从查看列表页面返回的保留筛选条件等
      let queryParam = JSON.parse(sessionStorage.getItem('queryParam'))
      this.currentPageIsShow = false
      this.currentPageIsShow = true
      // this.currentPage = queryParam.page
      // this.pageSize = queryParam.per_page
      this.formInline.url = queryParam.url
      this.formInline.status = queryParam.status
      this.formInline.created_at_range = queryParam.created_at_range
      this.formInline.end_at_range = queryParam.end_at_range
      this.formInline.id = queryParam.user_task_id
    }
    if (sessionStorage.getItem('companyInfo')) {
      // 权限控制
      let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      this.userIsOpen = true
    } else {
      this.userIsOpen = true
    }
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      if (this.user.role == 2) {
        if (!this.currentCompany) {
          return
        }
        this.getMergeTakList()
      } else {
        this.getMergeTakList()
      }
    }
  },
  async mounted() {
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
  },
  methods: {
    ...mapMutations(['setTimeChangeBox', 'changeMenuId']),
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    async highCheckIsShow() {
      this.highCheckdialog = true
      let res = await getAllUser()
      this.userArr = res.data
      console.log('this.userArr:', this.userArr)
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    copyTextText(text) {
      this.$copyText(text).then(
        (res) => {
          this.$message.success('复制成功')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选查询
    checkFuncList() {
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getMergeTakList()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },

    async getMergeTakList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        // created_at_range: this.formInline.created_at_range ? this.formInline.created_at_range : [],
        // end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
        operate_company_id: this.currentCompany,
        user_task_id: this.formInline.id,
        url: this.formInline.url
      }
      this.loading = true
      let res
      res = await apiAnalysisUserTask(obj).catch((err) => {
        console.error('API请求错误:', err)
        this.loading = false
        this.tableData = []
        this.total = 0
        this.wait_num = 0
      })
      console.log('res', res)

      this.loading = false
      this.tableData = res.data && res.data.task_list ? res.data.task_list : []
      console.log('tableData', this.tableData)

      this.total = res.data.total
      this.wait_num = res.data.wait_num
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
      this.checkedArr = this.$refs.eltable.selection
    },

    handleSelectionChange(val) {
      console.log('val:', val)
      this.checkedArr = val
      // this.checkedAll = this.tableData.length && this.tableData.length > 0
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getMergeTakList(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMergeTakList(true)
    },
    goToView(res) {},

    resetForm() {
      this.formInline = {
        url: '',
        status: '',
        created_at_range: [],
        user_id: ''
      }
    },
    exportList() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      let obj = {}
      obj = {
        task_ids:  this.checkedArr.map((item) => {
              return item.task_id
            }),
        operate_company_id: this.currentCompany,
      }
      this.exportLoadingBtn = true
      exportAnalysisTask(obj).then((res) => {
        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.path)
        }
      }).finally(() => {
        this.checkedAll = false
        this.exportLoadingBtn = false
      })
    },
    removeOne(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
      }
      // 0：等待扫描 1：扫描中 2：扫描完成 3：扫描失败 4：暂停扫描
      this.$confirm('确定删除任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'scan_del_cancel',
        confirmButtonClass: 'scan_del_sure',
        customClass: 'scan_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = null
          if (icon == 'more') {
            obj = {
              task_id: this.checkedAll
                ? []
                : this.checkedArr.map((item) => {
                    return item.task_id
                  }),
              url: this.formInline.url ? this.formInline.url : '',
              status: this.formInline.status ? this.formInline.status : null, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
              created_at_range: this.formInline.created_at_range
                ? this.formInline.created_at_range
                : [],
              end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
              is_schedule: 0,
              user_id: this.formInline.user_id ? this.formInline.user_id : null,
              operate_company_id: this.currentCompany,
              user_task_id: this.formInline.id
            }
          } else {
            obj = {
              task_id: [id],
              task_type: this.task_type, // 任务分类 1 资产扫描 2 漏洞扫描
              url: this.formInline.url ? this.formInline.url : '', // Ensure url is an empty string if not provided
              status: this.formInline.status ? this.formInline.status : null, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
              created_at_range: this.formInline.created_at_range
                ? this.formInline.created_at_range
                : [],
              end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
              is_schedule: 0,
              user_id: this.formInline.user_id ? this.formInline.user_id : null,
              operate_company_id: this.currentCompany,
              user_task_id: this.formInline.id
            }
          }
          let res
          if (this.$route.path == '/assetsScan') {
            res = await deleteTask(obj)
          } else {
            res = await deleteAnalysisTaskResult(obj)
          }
          if (res.code == 0) {
            this.$message.success('删除成功!')
            this.currentPage = this.updateCurrenPage(
              this.total,
              icon == 'more' ? this.checkedArr : [1],
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getMergeTakList()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.scan_del>.el-message-box__btns')
        del.children[0].id = 'scan_del_cancel'
        del.children[1].id = 'scan_del_sure'
      }, 50)
    },
    checkDetails(id) {
      console.log('id:', id)
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        url: this.formInline.url,
        status: this.formInline.status,
        created_at_range: this.formInline.created_at_range,
        end_at_range: this.formInline.end_at_range,
        user_id: this.formInline.user_,
        operate_company_id: this.currentCompany,
        task_id: id,
        user_task_id: this.formInline.id
      }
      console.log('obj', obj)
      sessionStorage.setItem('queryParam', JSON.stringify(obj))
      this.$router
        .push({
          path: '/riskDetails',
          // path: '/alreadyTask_viewlist',
          // name: 'viewDetails',
          query: {
            id: id,
            task_type: this.task_type,
            status: status
          }
        })
        .catch((err) => {
          console.error('路由跳转错误:', err)
        })
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;

  /deep/.home_header {
    position: relative;
    height: 100%;

    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;

      & > div {
        display: flex;
        align-items: center;

        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .el-input {
          width: 240px;
        }

        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }

    .result {
      position: relative;
      padding: 47px 0 0 24px;
      flex: 0 0 auto;

      .normalBtn {
        position: absolute;
        top: 16px;
        right: 20px;
      }

      .scanning {
        display: flex;
        align-items: center;

        .scanning-img {
          width: 56px;
          height: 56px;

          svg {
            font-size: 56px;
            color: #ddd;
          }
        }

        .scanning-txt {
          margin-left: 15px;
          line-height: 1;

          .txt1 {
            color: #62666c;
            font-size: 16px;
            font-weight: 400;

            .name {
              font-weight: 500;
              color: #37393c;
            }
          }

          .txt2 {
            font-size: 12px;
            font-weight: 400;
            color: #62666c;
            padding-top: 10px;

            .time {
              margin-left: 21px;
            }
          }

          .txt3 {
            width: 248px;
            height: 16px;
            margin-bottom: 14px;
            background: #e5ebf4;
          }

          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 180px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt5 {
            display: inline-block;
            width: 132px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }

      .handle {
        padding-top: 30px;
        padding-bottom: 29px;

        .line1 {
          overflow: hidden;

          .progress {
            float: left;
            width: 80%;
            height: 24px;
            background: #e5ebf4;
            border-radius: 12px;

            .el-progress.is-success .el-progress-bar__inner {
              top: 4px;
              left: 4px;
              height: 16px;
              background: -webkit-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -o-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -moz-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              border-radius: 12px;
            }

            ::v-deep .el-progress-bar__outer {
              background-color: #e5ebf4;
              border-radius: 0;
            }

            ::v-deep .el-progress.is-warning .el-progress-bar__inner {
              border-radius: 0;
            }
          }

          .btns {
            display: inline-block;
            padding-left: 16px;
            font-weight: 400;
            color: #2677ff;
            cursor: pointer;

            span {
              margin-right: 12px;

              i {
                margin-right: 3px;
              }
            }
          }
        }

        .line2 {
          width: 80%;
          font-size: 14px;
          color: #999;
          margin-top: 15px;
          display: flex;
          justify-content: space-between;

          .txt3 {
            width: 179px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 114px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt5 {
            display: inline-block;
            width: 86px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }
    }

    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }

    .tableWrap {
      height: calc(100% - 129px);

      .emptyClass {
        height: 100%;
        text-align: center;
        vertical-align: middle;

        svg {
          display: inline-block;
          font-size: 120px;
        }

        p {
          line-height: 25px;
          color: #d1d5dd;

          span {
            margin-left: 4px;
            color: #2677ff;
            cursor: pointer;
          }
        }
      }

      .el-progress-bar__innerText {
        vertical-align: top !important;
      }
    }

    .el-table {
      border: 0;

      td {
        border-right: transparent !important;
      }
    }

    // 定义单元格文本超出不换行
    .el-table .cell {
      overflow: hidden !important;
      white-space: nowrap !important;
    }
  }
}
</style>
