<template>
  <!-- 此页面是【资产台账-IP资产/疑似资产/威胁资产/忽略资产-主页面】，【单位测绘/云端推荐-推荐记录】公用 -->
  <div class="container">
    <div class="headerTitle">
      <span v-if="$route.path == '/assetsLedger'" class="myHeader">
        <span
          class="goback"
          v-if="$route.query.is_cdn || $route.query.rule_tags"
          @click="goBackFromBrief()"
          ><i class="el-icon-arrow-left"></i>返回上一层 / &nbsp;&nbsp;</span
        >
        IP资产
        <span v-if="notifyFilterId">
          / {{ notifyFilterMsg }}
          <span style="color: #2677ff; cursor: pointer" @click="clearNotifyFilter">恢复默认</span>
        </span>
      </span>
      <span v-else-if="$route.path == '/unclaimCloud'" class="myHeader">
        疑似资产
        <span v-if="notifyFilterId">
          / {{ notifyFilterMsg }}
          <span style="color: #2677ff; cursor: pointer" @click="clearNotifyFilter">恢复默认</span>
        </span>
      </span>
      <span v-else-if="$route.path == '/threatAssets'" class="myHeader"> 威胁资产 </span>
      <span v-else-if="$route.path == '/ignoreAssets'" class="myHeader"> 忽略资产 </span>
      <span v-else>
        <span
          v-if="$route.path == '/scanReg' || $route.path == '/riskAssets'"
          class="goback"
          @click="$router.go(-1)"
          ><i class="el-icon-arrow-left"></i>返回上一层</span
        >
        <span v-if="$route.path == '/scanReg' || $route.path == '/riskAssets'" class="spline"
          >/</span
        >
        <span>查看详情</span>
      </span>
      <!-- 统计 -->
      <p
        v-if="$route.path != '/scanReg' && activeName == 'second' && $route.path != '/riskAssets'"
        class="statisticnumBox"
      >
        <el-tooltip
          :open-delay="500"
          v-if="$route.path == '/assetsLedger' || $route.path == '/unclaimCloud'"
          content="对资产进行删除或处置操作后，将会在一定的时间内重新计算数量"
          placement="top"
          effect="dark"
        >
          <i
            class="el-icon-question"
            style="color: #b1bdd1; margin-right: 8px; font-size: 16px"
          ></i>
        </el-tooltip>
        <span v-for="(item, index) in statisticsList" :key="index" class="statisticnum"
          >{{ item.label }}：<span class="num">{{ statisticsData[item.name] }}</span></span
        >
        <span
          v-if="$route.path == '/threatAssets'"
          @click="$refs.threatDescription.dialogVisible = true"
          class="textBtn"
          >威胁类型说明</span
        >
      </p>
    </div>
    <div class="box">
      <ul
        v-if="$route.path == '/scanReg'"
        class="recommendlLeft"
        :style="$route.path == '/scanReg' ? 'width: 20%;' : 'width: 0'"
      >
        <div class="recommendTitle">任务参数</div>
        <div class="recommendBox">
          <li v-for="item in recommendTitle" :key="item.name">
            <p class="rightTitle">{{ item.label }}：</p>
            <span class="rightContent">
              <span v-if="item.name == 'op'">{{ recommendData[item.name].name }}</span>
              <span class="clueClass" v-else-if="item.name == 'clue'">
                <span v-for="ch in recommendData[item.name]" :key="ch.id">
                  <!-- <el-tooltip v-if="ch.type != 3 && ch.content" class="item" effect="dark" :content="ch.content" placement="top"> -->
                  <span v-if="ch.type != 3 && ch.content">{{ ch.content }}</span>
                  <!-- </el-tooltip> -->
                  <el-image
                    v-else
                    :src="ch.content.includes('http') ? ch.content : showSrcIp + ch.content"
                    class="myImg"
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </span>
              </span>
              <span v-else-if="item.name == 'use_seconds'">
                <span>{{ secondsFormat(recommendData[item.name]) }}</span>
              </span>
              <span v-else>
                <span>{{ recommendData[item.name] }}</span>
              </span>
            </span>
          </li>
        </div>
      </ul>
      <div class="home_header" :style="$route.path == '/scanReg' ? 'width: 79%;' : 'width: 100%'">
        <span v-if="$route.path == '/scanReg'" class="taskResults">任务推荐结果</span>
        <el-tabs
          v-if="$route.path != '/riskAssets'"
          v-model="activeName"
          @tab-click="handleClick"
          :style="$route.path == '/scanReg' ? 'margin-left:100px' : ''"
        >
          <el-tab-pane
            name="second"
            v-if="!notifyFilterId || (notifyFilterId && activeName == 'second')"
          >
            <span slot="label">IP</span>
          </el-tab-pane>
          <el-tab-pane
            label="IP+端口"
            name="first"
            v-if="!notifyFilterId || (notifyFilterId && activeName == 'first')"
          >
            <span slot="label">IP+端口</span>
          </el-tab-pane>
        </el-tabs>
        <div class="bot" :style="changeHeight()">
          <ipAssets
            :notifyFilterId="notifyFilterId"
            style="height: 100%"
            v-if="activeName == 'second'"
          />
          <div style="height: 100%" v-if="activeName == 'first'">
            <div class="filterTab">
              <!-- ip+端口资产台账 -->
              <div v-if="$route.path != '/scanReg'" style="display: flex; align-items: center">
                <el-input
                  v-model="formInline.keyword"
                  @keyup.enter.native="highCheck"
                  placeholder="请输入关键字进行搜索"
                  id="account_keycheck"
                >
                  <el-button slot="append" icon="el-icon-search" @click="highCheck"></el-button>
                  <el-tooltip
                    slot="prepend"
                    class="item"
                    effect="dark"
                    content="支持检索字段：IP地址、网站标题、域名、企业名称"
                    placement="top"
                    :open-delay="100"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </el-input>
                <span
                  @click="highCheckClick"
                  id="account_filter"
                  style="width: 80px; display: inline-block"
                  ><img
                    src="../../assets/images/filter.png"
                    alt=""
                    style="width: 16px; vertical-align: middle; margin-right: 3px"
                  />高级筛选</span
                >
              </div>
              <!-- ip+端口推荐记录 -->
              <div v-else style="display: flex; align-items: center">
                <el-input
                  v-model="formInlineScan.keyword"
                  @keyup.enter.native="highCheckScan('')"
                  clearable
                  placeholder="请输入关键字检索"
                  id="account_keycheck"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-search"
                    @click="highCheckScan('')"
                  ></el-button>
                  <el-tooltip
                    slot="prepend"
                    class="item"
                    effect="dark"
                    content="支持检索字段：IP地址、网站标题、域名、企业名称"
                    placement="top"
                    :open-delay="100"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </el-input>
                <span
                  @click="highCheckDialogScan = true"
                  id="account_filter"
                  style="width: 80px; display: inline-block"
                  ><img
                    src="../../assets/images/filter.png"
                    alt=""
                    style="width: 16px; vertical-align: middle; margin-right: 3px"
                  />高级筛选</span
                >
              </div>
              <div>
                <el-checkbox class="checkboxAll" v-model="checkedAll" id="account_all"
                  >选择全部</el-checkbox
                >
                <!-- <el-button class="normalBtnRe" type="primary" @click="removeMore">删除</el-button> -->
                <!-- <el-button class="normalBtnRe" type="primary" @click="importTz">导入</el-button> -->
                <el-button
                  v-if="$route.path == '/assetsLedger'"
                  class="normalBtnRe"
                  type="primary"
                  :loading="exportLoadingBtn"
                  :disabled="exportLoadingBtn"
                  @click="exportList"
                  id="account_export"
                  >导出</el-button
                >
                <el-button
                  v-if="
                    $route.path == '/unclaimCloud' ||
                    $route.path == '/ignoreAssets' ||
                    $route.path == '/threatAssets' ||
                    $route.path == '/scanReg'
                  "
                  class="normalBtnRe"
                  type="primary"
                  @click="exportAssets"
                  :loading="exportLoadingBtn"
                  :disabled="exportLoadingBtn"
                  id="account_export"
                  >导出</el-button
                >
                <el-button
                  v-if="$route.path == '/assetsLedger'"
                  class="normalBtn"
                  type="primary"
                  @click="heduiTzFun"
                  id="account_check"
                  >核对台账</el-button
                >
              </div>
            </div>
            <!-- 高级筛选条件 -->
            <hightFilter
              :formInline="filterCondition"
              :activeName="activeName"
              :total="total"
              @highCheck="highCheckCancel"
              v-if="hightFilterShow == 1 && $route.path != '/scanReg'"
              style="padding: 0px 20px 16px 20px"
            ></hightFilter>
            <hightFilter
              :formInline="filterCondition"
              :total="total"
              @highCheck="highCheckCancelScan"
              v-if="hightFilterShow == 1 && $route.path == '/scanReg'"
              style="padding: 0px 20px 16px 20px"
            ></hightFilter>
            <div
              :class="hightFilterShow == 1 ? 'tableWrap-with-filter' : 'tableWrap'"
              v-loading="loading"
              ref="tableWrap"
            >
              <tableList
                :notifyFilterId="notifyFilterId"
                ref="tableList"
                :tableData="tableData"
                :checkedAll="checkedAll"
                :radioParams="radioParams"
                :clearSelectionTrigger="shouldClearSelection"
                @selection-cleared="handleSelectionCleared"
                @handleSelectionChange="handleSelectionChange"
              />
            </div>
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="pageSizeArr"
              :page-size="pageSize"
              layout="total, prev, pager, next, sizes, jumper"
              :total="total"
              v-if="!($route.path == '/index')"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleHedui"
      width="400px"
    >
      <template slot="title"> 核对台账 </template>
      <div class="dialog-body">
        <p class="downloadClass" @click="downloadForbidIpsHd">
          <i class="el-icon-warning"></i>请点击下载
          <span>核对模板</span>
        </p>
        <el-upload
          class="upload-demo"
          drag
          :action="uploadActionHd"
          :headers="uploadHeaders"
          accept=".xlsx"
          :before-upload="beforeIpUpload"
          :on-success="ipUploadSuccessHd"
          :on-remove="uploadRemoveHd"
          :on-error="ipUploadErrorHd"
          :limit="uploadMaxCount"
          :on-exceed="handleExceedHd"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过20M</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleHedui = false"
          id="account_check_cancel"
          >关闭</el-button
        >
        <el-button
          :loading="btnLoading"
          class="highBtn"
          @click="heduiTzFunSave"
          id="account_check_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleImport"
      width="400px"
    >
      <template slot="title"> 导入 </template>
      <div class="dialog-body">
        <p class="downloadClass" @click="downloadForbidIpsExcel">
          <i class="el-icon-warning"></i>请点击下载
          <span>台账导入模板</span>
        </p>
        <el-upload
          class="upload-demo"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          accept=".xlsx"
          :before-upload="beforeIpUpload"
          :on-success="ipUploadSuccess"
          :on-remove="uploadRemove"
          :on-error="ipUploadError"
          :limit="uploadMaxCount"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过20M</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleImport = false">关闭</el-button>
        <el-button class="highBtn" :loading="btnLoading" @click="uploadSave">确定</el-button>
      </div>
    </el-dialog>
    <threatDescription ref="threatDescription" />
    <!-- 资产台账等 ip+端口 -->
    <highCheckDrawer
      :highCheckdialog="highCheckDialog"
      :selectArr="selectArr"
      :formInline="formInline"
      @highCheck="highCheck"
      @highIsClose="highIsClose"
    />
    <!-- 推荐记录 ip+端口 -->
    <highCheckDrawerScan
      :highCheckdialog="highCheckDialogScan"
      :selectArr="cluesList"
      :formInline="formInlineScan"
      @highCheck="highCheckScan"
      @highIsClose="highIsCloseScan"
    />
  </div>
</template>

<script>
import sha1 from '@/utils/sha1Encrypt'
import { mapGetters, mapState, mapMutations } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import tableList from '../home_set/indexTable.vue'
import ipAssets from './ipAssets.vue'
import highCheckDrawer from '../home_set/highCheck.vue'
import highCheckDrawerScan from '../home_set/highCheck.vue'
import hightFilter from '../../components/assets/hightFilter.vue'
import threatDescription from './threatDescription.vue'
import { area, getCondition } from '@/api/apiConfig/api.js'
import {
  exportHandleScan,
  regRecommendRecords,
  regRecommendClues
} from '@/api/apiConfig/recommend.js'
import { getMyCompany } from '@/api/apiConfig/person.js'
import {
  exportThreatenV1,
  getThreatenListV1,
  exportNeglectV1,
  filterNeglectListV1,
  getNeglectListV1,
  exportSuspectedV1,
  filterSuspectedListV1,
  getSuspectedListV1,
  heduiKnownAssets,
  importSureIpData,
  exportData,
  deleteStandingData,
  ansysDataIndex,
  countStandingBook,
  getstatistics,
  recommandScanTaskDetail
} from '@/api/apiConfig/asset.js'
export default {
  components: {
    threatDescription,
    tableTooltip,
    tableList,
    highCheckDrawer,
    ipAssets,
    highCheckDrawerScan,
    hightFilter
  },
  data() {
    return {
      exportLoadingBtn: false,
      hightFilterShow: 3,
      filterCondition: {}, //高级设置状态值
      highCheckDialogScan: false,
      activeName: 'second',
      btnLoading: false,
      radioParams: false,
      shouldClearSelection: false, // 用于触发子组件清除选择的标志
      user: {
        role: ''
      },
      identifier: '',
      regUrlArr: [], // 核对台账数组
      uploadActionHd: `${this.uploadSrcIp}/assets/account/files?encode=0`,
      uploadHeaders: { Authorization: localStorage.getItem('token') },
      forbidIpsSelect: [],
      fileList: [],
      companyArr: [],
      checkedArr: [],
      provinceData: [],
      selectArr: {},
      fileData: [],
      uploadMaxCount: 1,
      checkedAll: false,
      highCheckDialog: false,
      dialogFormVisibleHedui: false,
      dialogFormVisibleImport: false,
      loading: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      formInline: {
        ip: '',
        clue_company_name: '',
        province: '', // 省份名称（传汉字）,
        keyword: '', // 123123,
        asn: '', // 123123,
        icp: '', // icp,
        url: '',
        title: '', // title,
        protocol: '', // protocol,
        logo: '', // logo,
        port: '', // port,
        cname: '', // cname,
        domain: [], // domain
        subdomain: [],
        online_state: [],
        lat: '',
        lon: '',
        state: '',
        reason: '',
        updated_at: []
      },
      tongjiArr: [
        {
          id: 1,
          name: 'IP总数',
          num: 'ip',
          icon: '#icon-a-bianzu8'
        },
        {
          id: 2,
          name: '端口总数',
          num: 'port_num',
          icon: '#icon-a-bianzu81'
        },
        {
          id: 3,
          name: '组件总数',
          num: 'rule_num',
          icon: '#icon-a-bianzu82'
        }
      ],
      tongjiNum: {
        ip: '',
        port_num: '',
        rule_num: ''
      },

      cascaderProps: {
        lazy: true,
        lazyLoad: this.lazyLoad
      },
      conditionFlag: 3, // 导出：已认领标识3
      conditionFlagList: 1, // 台账已认领资产（已认领标识1）
      recommendData: {
        //扫描资产核对任务参数
        name: '',
        clue: '',
        start_at: '',
        end_at: '',
        use_seconds: '',
        op: ''
      },
      recommendTitle: [
        // {
        //   label: '任务参数',
        //   name: ''
        // },
        {
          label: '任务名称',
          name: 'name'
        },
        {
          label: '开始时间',
          name: 'start_at'
        },
        {
          label: '结束时间',
          name: 'end_at'
        },
        {
          label: '任务耗时',
          name: 'use_seconds'
        },
        {
          label: '发起人',
          name: 'op'
        },
        {
          label: '推荐线索',
          name: 'clue'
        }
      ],
      cluesList: {}, // 线索数据
      ruleFormScan: {
        id: [],
        bandwidth: 300,
        port_group_ids: '',
        scan_type: 1,
        ip_type: 1,
        ping_switch: false,
        web_logo_switch: false, // 0不开 1开启
        flag: '', // 如果此字段有值表示是全选，如果此字段为空，则读取id数据",
        keyword: '',
        operate_company_id: ''
      },
      formInlineScan: {
        ip: '',
        clue_company_name: '',
        province: '', // 省份名称（传汉字）,
        keyword: '', // 123123,
        asn: '', // 123123,
        icp: '', // icp,
        url: '',
        title: '', // title,
        protocol: '', // protocol,
        port: '', // port,
        cname: '', // cname,
        domain: '', // domain
        lat: '',
        lon: '',
        state: '',
        reason: '',
        score: '',
        updated_at: []
      },
      statisticsList: [
        //统计
        {
          label: 'IP总数',
          name: 'ip_num'
        },
        {
          label: '端口总数',
          name: 'port_num'
        },
        {
          label: '组件总数',
          name: 'rule_num'
        }
      ],
      statisticsData: {
        ip_num: '',
        port_num: '',
        rule_num: ''
      },
      notifyFilterId: '',
      notifyFilterMsg: ''
    }
  },
  watch: {
    getterCompanyChange(val) {
      if (this.notifyFilterId) {
        this.clearNotifyFilter()
      }
      this.currentPage = 1
      if (this.user.role == 2) {
        if (this.$route.path == '/scanReg') {
          this.$router.go(-1)
          return
        }
        // 切换账号去除全选
        this.checkedAll = false
        // this.getCountStandingBook()
        if (this.$route.path == '/unclaimCloud') {
          this.filterSuspected()
        } else if (this.$route.path == '/ignoreAssets') {
          this.filterSuspected()
        } else if (this.$route.path == '/threatAssets') {
          this.filterSuspected()
        } else if (this.$route.path == '/scanReg') {
          if (Object.keys(this.cluesList).length == 0) {
            this.getRecommendCluesData(this.$route.query.flag)
          }
          this.getRecommendRecordsList()
        } else {
          this.getknownAssetsList()
        }
        if (this.$route.path != '/scanReg') {
          // this.statistics()
        }
      }
    },
    getterCurrentCompany(val) {
      // 安服账号刷新页面加载
      if (this.user.role != 2) return
      this.currentPage = 1
      this.getCountStandingBook()
      if (this.$route.path == '/unclaimCloud') {
        this.filterSuspected()
      } else if (this.$route.path == '/ignoreAssets') {
        this.filterSuspected()
      } else if (this.$route.path == '/threatAssets') {
        this.filterSuspected()
      } else if (this.$route.path == '/scanReg') {
        if (Object.keys(this.cluesList).length == 0) {
          this.getRecommendCluesData(this.$route.query.flag)
        }
        this.getDetails()
        this.getRecommendRecordsList()
      } else {
        this.getknownAssetsList()
      }
    },
    getterChangeTime() {
      this.statistics()
    }
  },
  created() {
    this.notifyFilterId = this.$route.query.notifyFilterId
    this.notifyFilterMsg = this.$route.query.notifyFilterMsg
    this.formInline.website_message_id = this.notifyFilterId || ''
    this.activeName = this.$route.query.notifyFilterTab || 'second'
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.notifyFilterId) {
      this.init()
    }
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    if (this.user.role == 2 && !this.currentCompany) return
    if (this.$route.path == '/scanReg') {
      setTimeout(() => {
        this.getDetails()
      }, 500)
    }
    // if (this.$route.path != '/scanReg') {
    //   setTimeout(()=>{
    //     this.statistics()
    //   },500)
    // }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange', 'getterChangeTime']),

    uploadAction() {
      let url = ''
      if (this.user.role == 2) {
        // operate_company_id: 操作的企业id（安服角色需要，是单个值，不是数组），如果是普通用户，不需要这个值
        url = `${this.uploadSrcIp}/assets/account/import_sure_ip_file?operate_company_id=${this.currentCompany}`
      } else {
        url = `${this.uploadSrcIp}/assets/account/import_sure_ip_file`
      }
      return url
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    clearNotifyFilter() {
      this.$router.replace({ query: {} })
    },
    // 从资产概览过来返回的上一层
    goBackFromBrief() {
      sessionStorage.setItem('menuId', '1-1')
      this.changeMenuId('1-1')
      this.$router.go(-1)
    },
    // 统计
    async statistics() {
      let obj
      if (this.$route.path == '/assetsLedger') {
        obj = {
          type: 0, //1推荐的资产 0、非推荐的资产
          status: 1, //1/代表台账的数据  2/忽略资产   3/威胁
          operate_company_id: this.currentCompany
        }
      } else if (this.$route.path == '/ignoreAssets') {
        obj = {
          type: 0, //1推荐的资产 0、非推荐的资产
          status: 2, //1/代表台账的数据  2/忽略资产   3/威胁
          operate_company_id: this.currentCompany
        }
      } else if (this.$route.path == '/threatAssets') {
        obj = {
          type: 0, //1推荐的资产 0、非推荐的资产
          status: 3, //1/代表台账的数据  2/忽略资产   3/威胁
          operate_company_id: this.currentCompany
        }
      } else {
        obj = {
          type: 1, //1推荐的资产 0、非推荐的资产
          status: 0, //1/代表台账的数据  2/忽略资产   3/威胁
          operate_company_id: this.currentCompany
        }
      }
      let res = await getstatistics(obj)
      if (res.code == 0) {
        this.statisticsData = res.data
      }
    },
    changeHeight() {
      if (this.$route.path == '/riskAssets') {
        return { height: '100%' }
      } else {
        if (this.activeName == 'first') {
          return { height: 'calc(100% - 45px)' }
        } else {
          return { height: 'calc(100% - 45px)' }
        }
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleClick() {
      if (this.activeName == 'first') {
        this.currentPage = 1
        this.formInline = {
          ip: '',
          clue_company_name: '',
          province: '', // 省份名称（传汉字）,
          keyword: '', // 123123,
          asn: '', // 123123,
          icp: '', // icp,
          url: '',
          title: '', // title,
          protocol: '', // protocol,
          logo: '', // logo,
          port: '', // port,
          cname: '', // cname,
          domain: [], // domain
          subdomain: [],
          lat: '',
          lon: '',
          state: '',
          reason: '',
          updated_at: []
        }
        this.init()
        this.hightFilterShow = 2
      } else if (this.activeName == 'second') {
      }
    },
    init() {
      if (this.user.role == 2) {
        // operate_company_id: 操作的企业id（安服角色需要，是单个值，不是数组），如果是普通用户，不需要这个值
        if (!this.currentCompany) {
          return
        }
        this.getCountStandingBook()
      } else {
        this.getCountStandingBook()
      }
      if (this.$route.path == '/unclaimCloud') {
        this.filterSuspected()
      } else if (this.$route.path == '/ignoreAssets') {
        this.filterSuspected()
      } else if (this.$route.path == '/threatAssets') {
        this.filterSuspected()
      } else if (this.$route.path == '/scanReg') {
        if (Object.keys(this.cluesList).length == 0) {
          this.getRecommendCluesData(this.$route.query.flag)
        }
        this.getRecommendRecordsList()
      } else {
        this.getknownAssetsList()
      }
    },
    // 高级筛选条件
    async getAssetsCluesData() {
      let data
      if (this.$route.path == '/unclaimCloud') {
        data = {
          operate_company_id: this.currentCompany,
          type: 1,
          status: [0]
        }
      } else if (this.$route.path == '/ignoreAssets') {
        data = {
          operate_company_id: this.currentCompany,
          type: '',
          status: [2]
        }
      } else if (this.$route.path == '/threatAssets') {
        data = {
          operate_company_id: this.currentCompany,
          type: '',
          status: [3]
        }
      } else {
        data = {
          operate_company_id: this.currentCompany,
          type: '',
          status: [1, 4]
        }
      }
      let res = await getCondition(data)
      this.selectArr = res.data
    },
    highCheckClick() {
      this.highCheckDialog = true
      this.getAssetsCluesData()
    },
    async lazyLoad(node, resolve) {
      let level = node.level
      let res
      if (!node.data) {
        res = await area('')
      } else {
        res = await area(node.data.value)
      }
      if (res.code == 0) {
        const nodes = Array.from(res.data).map((item) => ({
          value: item.adcode,
          label: item.name,
          leaf: level >= 2
        }))
        resolve(nodes)
      }
    },
    async getMyCompanyData() {
      let res = await getMyCompany()
      this.companyArr = res.data
    },
    // 获取统计数量
    async getCountStandingBook() {
      let obj = {
        operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
        type: '', // 统计接口直接传''
        status: [1, 4],
        ...this.formInline
      }
      if (this.user.role != 2) {
        // 非安服角色不传operateCompanyId
        delete obj.operate_company_id
      }
      let res = await countStandingBook(obj)
      this.tongjiNum = res.data
    },
    downloadForbidIpsExcel() {
      window.location.href = '/downloadTemplate/台账导入模板.xlsx'
    },
    downloadForbidIpsHd() {
      window.location.href = '/downloadTemplate/核对台账导入模板.xlsx'
    },
    // 已知资产列表
    async getknownAssetsList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      this.tableData = []
      this.identifier = sha1(this.user.id + 'sha1' + new Date().getTime())
      let obj = {
        identifier: this.identifier,
        operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
        type: '', // 统计接口直接传''
        status: [1, 4],
        ...this.formInline
      }
      this.loading = true
      let res = await ansysDataIndex(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
        this.loading = false
      }
    },
    // 已知资产核对台账
    heduiTzFun() {
      this.fileList = []
      this.regUrlArr = []
      this.dialogFormVisibleHedui = true
    },
    async removeMore() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let obj = null
          this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
          obj = {
            identifier: this.identifier,
            operate_company_id: this.currentCompany,
            is_all: this.checkedAll ? 1 : 0,
            status: [1, 4],
            type: '',
            id: this.checkedAll
              ? []
              : this.checkedArr.map((item) => {
                  return item.id
                }),
            ...this.formInline
          }
          this.loading = true
          let res = await deleteStandingData(obj)
          if (res.code == 0) {
            // this.$refs.eltable.clearSelection();
            // setTimeout(() => {
            this.$message.success('删除成功！')
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.init()
            // }, 500)
          }
        })
        .catch(() => {})
    },
    goToView(url) {
      this.download(this.showSrcIp + url)
    },
    async heduiTzFunSave() {
      this.btnLoading = true
      let res = await heduiKnownAssets({
        identifier: this.identifier,
        file: this.regUrlArr,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.btnLoading = false
      })
      this.btnLoading = false
      if (res.code == 0) {
        this.dialogFormVisibleHedui = false
        this.$confirm(
          `台账资产共计${res.data.assets_count}个，通过核对，其中${res.data.not_assets_count}个资产未纳入管理，您可以查看或者下载核对结果`,
          '核对结果',
          {
            confirmButtonText: '下载核对结果',
            cancelButtonText: '关闭',
            type: 'success'
          }
        )
          .then(() => {
            this.goToView(res.data.url)
          })
          .catch(() => {})
      }
    },
    importTz() {
      this.dialogFormVisibleImport = true
      this.fileList = []
    },
    exportList() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      let obj = {
        menu: '资产台账', // 用于导出后文件名称显示
        is_all: this.checkedAll ? 1 : 0,
        status: [1, 4],
        id: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            }),
        select_ip: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.ip
            }),
        operate_company_id: this.currentCompany,
        ...this.formInline
      }
      this.exportLoadingBtn = true
      exportData(obj)
        .then((res) => {
          if (res.code == 0) {
            this.download(this.showSrcIp + res.data.url)
            this.shouldClearSelection = true // 清空选择项
            this.exportLoadingBtn = false
            this.checkedAll = false
            this.$refs.eltable.clearSelection()
          }
        })
        .catch(() => {
          this.exportLoadingBtn = false
        })
    },
    ipUploadSuccessHd(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.regUrlArr.push(response.data.url)
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadRemoveHd(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.regUrlArr = []
      } else {
        this.regUrlArr = res[0]
      }
    },
    ipUploadErrorHd(err, file, fileList) {
      this.$message.error(JSON.parse(err.message).message)
    },
    handleExceedHd() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    async uploadSave() {
      if (this.fileData.length == 0) {
        this.$message.error('请上传文件')
        return
      }
      this.btnLoading = true
      let res = await importSureIpData({
        data: this.fileData,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        // setTimeout(() => {
        this.btnLoading = false
        this.dialogFormVisibleImport = false
        this.getknownAssetsList()
        this.getCountStandingBook()
        // }, 2000)
      }
    },
    beforeIpUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过 20MB!')
      }
      return isLt1M
    },
    ipUploadSuccess(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.fileData = response.data
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.fileData = []
      }
    },
    ipUploadError(err, file, fileList) {
      this.$message.error(JSON.parse(err.message).message)
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      if (this.$route.path == '/unclaimCloud') {
        this.filterSuspected()
      } else if (this.$route.path == '/ignoreAssets') {
        this.filterSuspected()
      } else if (this.$route.path == '/threatAssets') {
        this.filterSuspected()
      } else if (this.$route.path == '/scanReg') {
        this.getRecommendRecordsList(true)
      } else {
        this.getknownAssetsList(true)
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      if (this.$route.path == '/unclaimCloud') {
        this.filterSuspected()
      } else if (this.$route.path == '/ignoreAssets') {
        this.filterSuspected()
      } else if (this.$route.path == '/threatAssets') {
        this.filterSuspected()
      } else if (this.$route.path == '/scanReg') {
        this.getRecommendRecordsList(true)
      } else {
        this.getknownAssetsList(true)
      }
    },
    highIsClose() {
      this.highCheckDialog = false
    },
    highCheck() {
      // this.formInline = data
      this.filterCondition = Object.assign({}, this.formInline)
      this.highCheckDialog = false
      this.currentPage = 1
      if (
        this.$route.path == '/unclaimCloud' ||
        this.$route.path == '/ignoreAssets' ||
        this.$route.path == '/threatAssets'
      ) {
        this.filterSuspected()
      } else {
        this.getknownAssetsList()
      }
      // if(this.formInline.created_at){
      //   var created_at = this.formInline.created_at.filter ( item => ![ "", null, undefined].includes(item));
      // }
      // if(this.formInline.updated_at){
      //   var updated_at = this.formInline.updated_at.filter ( item => ![ "", null, undefined].includes(item));
      // }
      this.formInline.created_at = this.formInline.created_at ? this.formInline.created_at : []
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      let obj = {
        ...this.formInline
      }
      delete obj.page
      delete obj.per_page
      for (let key in obj) {
        if (
          (Array.isArray(obj[key]) && obj[key].length != 0) ||
          (!Array.isArray(obj[key]) && (obj[key] || obj[key] === 0))
        ) {
          return (this.hightFilterShow = 1)
        } else {
          this.hightFilterShow = 2
        }
      }
    },
    highCheckCancel(data) {
      this.formInline = {
        keyword: this.formInline.keyword,
        ...data
      }
      this.currentPage = 1
      if (
        this.$route.path == '/unclaimCloud' ||
        this.$route.path == '/ignoreAssets' ||
        this.$route.path == '/threatAssets'
      ) {
        this.filterSuspected()
      } else {
        this.getknownAssetsList()
      }
      this.formInline.created_at = this.formInline.created_at ? this.formInline.created_at : []
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      let obj = {
        // keyword: this.formInline.keyword,
        // ip: this.formInline.ip,
        // clue_company_name: this.formInline.clue_company_name,
        // port: this.formInline.port,
        // url: this.formInline.url,
        // protocol: this.formInline.protocol,
        // domain: this.formInline.domain,
        // subdomain: this.formInline.subdomain,
        // title:this.formInline.title,
        // rule_tags: this.formInline.rule_tags,
        // http_status_code:this.formInline.http_status_code,
        // isp: this.formInline.isp,
        // province:this.formInline.province,
        // asn:this.formInline.asn,
        // lat:this.formInline.lat,
        // lon:this.formInline.lon,
        // online_state:this.formInline.online_state,
        // reason:String(this.formInline.reason),
        // updated_at:updated_at,
        // created_at:created_at,
        // score_type:this.formInline.score_type,
        // score:this.formInline.score
        ...this.formInline
      }

      delete obj.page
      delete obj.per_page
      for (let key in obj) {
        if (
          (Array.isArray(obj[key]) && obj[key].length != 0) ||
          (!Array.isArray(obj[key]) && (obj[key] || obj[key] === 0))
        ) {
          return (this.hightFilterShow = 1)
        } else {
          this.hightFilterShow = 2
        }
      }
    },
    // 获取疑似资产列表/忽略资产列表
    async getSuspected() {
      this.tableData = []
      this.loading = true
      let data = {
        page: this.currentPage,
        per_page: this.pageSize,
        operate_company_id: this.currentCompany
      }
      let res
      if (this.$route.path == '/ignoreAssets') {
        res = await getNeglectListV1(data).catch(() => {
          this.loading = false
        })
      } else {
        data.sort = 1
        res = await getSuspectedListV1(data).catch(() => {
          this.loading = false
        })
      }
      if (res.code == 0) {
        this.tableData = res.data.data
        this.total = res.data.total
        this.loading = false
      }
    },
    // 高级筛选资产列表/忽略资产列表
    async filterSuspected() {
      this.checkedAll = false
      if (this.$refs.eltable != undefined) {
        this.$refs.eltable.clearSelection()
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.last_update_time = this.formInline.last_update_time
        ? this.formInline.last_update_time
        : []
      this.tableData = []
      // this.identifier = sha1(this.user.id + 'sha1' + new Date().getTime())
      let obj
      if (this.$route.path == '/unclaimCloud') {
        obj = {
          status: [0],
          type: 1,
          operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
          type: '', // 统计接口直接传''
          ...this.formInline
        }
      } else if (this.$route.path == '/threatAssets') {
        //威胁
        obj = {
          status: [3],
          type: '',
          operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
          type: '', // 统计接口直接传''
          ...this.formInline
        }
      } else {
        obj = {
          status: [2],
          type: '',
          operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
          type: '', // 统计接口直接传''
          ...this.formInline
        }
      }
      this.loading = true
      let res
      if (this.$route.path == '/unclaimCloud') {
        res = await filterSuspectedListV1(obj).catch(() => {
          this.loading = false
          this.tableData = []
          this.total = 0
        })
      } else if (this.$route.path == '/threatAssets') {
        res = await getThreatenListV1(obj).catch(() => {
          this.loading = false
          this.tableData = []
          this.total = 0
        })
      } else {
        res = await filterNeglectListV1(obj).catch(() => {
          this.loading = false
          this.tableData = []
          this.total = 0
        })
      }
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
        this.loading = false
      }
    },

    // 子组件选择后，重置标志
    handleSelectionCleared() {
      this.shouldClearSelection = false
    },

    // 导出资产
    async exportAssets() {
      let obj
      if (this.$route.path == '/unclaimCloud') {
        obj = {
          menu: '疑似资产', // 用于导出后文件名称显示
          status: [0],
          is_all: this.checkedAll ? 1 : 0,
          type: 1,
          id: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          select_ip: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.ip
              }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else if (this.$route.path == '/threatAssets') {
        obj = {
          menu: '威胁资产', // 用于导出后文件名称显示
          status: [3],
          is_all: this.checkedAll ? 1 : 0,
          type: '',
          id: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          select_ip: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.ip
              }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else if (this.$route.path == '/ignoreAssets') {
        obj = {
          menu: '已忽略资产', // 用于导出后文件名称显示
          status: [2],
          is_all: this.checkedAll ? 1 : 0,
          type: '',
          id: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          select_ip: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.ip
              }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else {
        obj = {
          flag: this.$route.query.flag,
          query: {
            operate_company_id: this.currentCompany,
            id: this.checkedArr.map((item) => {
              return item.id
            }),
            whole: this.checkedAll ? 1 : '', // 传值代表全部
            ...this.formInlineScan
          }
        }
      }
      try {
        this.exportLoadingBtn = true
        let res
        if (this.$route.path == '/unclaimCloud') {
          res = await exportSuspectedV1(obj)
        } else if (this.$route.path == '/threatAssets') {
          res = await exportThreatenV1(obj)
        } else if (this.$route.path == '/ignoreAssets') {
          res = await exportNeglectV1(obj)
        } else {
          res = await exportHandleScan(obj)
        }
        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.url)
          this.shouldClearSelection = true //清空选择项状态
          this.exportLoadingBtn = false
          this.checkedAll = false
          this.$refs.eltable.clearSelection()
        }
      } catch (error) {
        this.exportLoadingBtn = false
      }
    },
    // 扫描资产核对任务参数
    async getDetails() {
      let obj = {
        id: this.$route.query.flag,
        operate_company_id: this.currentCompany
      }
      let res = await recommandScanTaskDetail(obj).catch(() => {
        this.loading = false
      })
      this.recommendData = res.data
    },
    async getRecommendCluesData() {
      // 线索
      let res = await regRecommendClues({
        flag: this.$route.query.flag,
        data: {
          operate_company_id: this.currentCompany
        }
      }).catch(() => {
        this.cluesList = []
      })
      if (res.data && res.data.logo) {
        res.data.logo.forEach((item, index) => {
          item.id = index + 1
        })
      }
      this.cluesList = res.data ? res.data : []
    },
    async getRecommendRecordsList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.tableData = []
      let obj = {
        flag: this.$route.query.flag,
        query: {
          audit: [3], // 扫描核对页面只展示已审核通过的数据
          operate_company_id: this.currentCompany,
          page: this.currentPage,
          per_page: this.pageSize,
          ...this.formInlineScan
        }
      }
      this.loading = true
      let res = await regRecommendRecords(obj).catch(() => {
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      res.data.items.forEach((item) => {
        item['id'] = item.id
      })
      this.tableData = res.data.items
      this.total = res.data.total
    },
    //高级筛选
    highIsCloseScan() {
      this.highCheckDialogScan = false
    },
    highCheckScan(val) {
      if (val) {
        // 高级筛选条件
        this.formInlineScan = val
      }
      this.highCheckDialogScan = false
      this.currentPage = 1
      this.init()
      this.filterCondition = Object.assign({}, this.formInlineScan)
      let obj = {
        keyword: this.formInlineScan.keyword,
        ip: this.formInlineScan.ip,
        port: this.formInlineScan.port,
        protocol: this.formInlineScan.protocol,
        domain: this.formInlineScan.domain,
        subdomain: this.formInlineScan.subdomain,
        title: this.formInlineScan.title,
        cert: this.formInlineScan.cert,
        icp: this.formInlineScan.icp
      }

      for (let key in obj) {
        if (
          (Array.isArray(obj[key]) && obj[key].length != 0) ||
          (!Array.isArray(obj[key]) && (obj[key] || obj[key] === 0))
        ) {
          return (this.hightFilterShow = 1)
        } else {
          this.hightFilterShow = 2
        }
      }
    },
    highCheckCancelScan(data) {
      this.formInlineScan = {
        keyword: this.formInlineScan.keyword,
        ...data
      }
      this.currentPage = 1
      this.init()
      let obj = {
        keyword: this.formInlineScan.keyword,
        ip: this.formInlineScan.ip,
        port: this.formInlineScan.port,
        protocol: this.formInlineScan.protocol,
        domain: this.formInlineScan.domain,
        subdomain: this.formInlineScan.subdomain,
        title: this.formInlineScan.title,
        cert: this.formInlineScan.cert,
        icp: this.formInlineScan.icp
      }
      for (let key in obj) {
        if (
          (Array.isArray(obj[key]) && obj[key].length != 0) ||
          (!Array.isArray(obj[key]) && (obj[key] || obj[key] === 0))
        ) {
          return (this.hightFilterShow = 1)
        } else {
          this.hightFilterShow = 2
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  .dialog-body {
    /deep/.el-tree {
      width: 100%;
      height: 382px;
      overflow: auto;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d1d5dd;
      margin-bottom: 27px;
      .el-tree-node {
        width: 100%;
        height: 40px;
        background: #ffffff;
        border-bottom: 1px solid #e9ebef;
        .el-tree-node__content {
          height: 40px;
          line-height: 40px;
        }
      }
    }
  }
  .dialog-footer {
    position: relative;
    .resetClass {
      position: absolute;
      left: 16px;
      top: 5px;
      color: #2677ff;
      cursor: pointer;
    }
  }
  /deep/.home_header {
    width: 100%;
    height: 100%;
    // .el-tabs__nav {
    //   padding-left: 20px;
    // }
    // .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
    //   padding: 0 16px;
    //   height: 44px;
    //   text-align: center;
    //   line-height: 44px;
    //   font-weight: bold;
    //   color: #2677ff;
    //   background: rgba(38, 119, 255, 0.08);
    // }
    // .el-tabs__active-bar {
    //   left: 4px;
    //   width: 100%;
    //   padding: 0 16px;
    //   background: #2677ff;
    // }
    // .el-tabs__header {
    //   margin: 0;
    //   background: #fff;
    // }
    // .el-tabs__nav-wrap::after {
    //   height: 1px;
    //   background-color: #e9ebef;
    // }
    // .el-tabs__item {
    //   // min-width: 60px;
    //   padding: 0 16px;
    //   height: 44px;
    //   text-align: center;
    //   line-height: 44px;
    //   // padding: 0;
    //   font-size: 14px;
    //   font-weight: 400;
    //   color: #62666c;
    // }
    .el-tabs__nav.is-top {
      margin-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      padding: 0;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: none;
      & > span {
        margin-left: 0 !important;
        background: rgba(38, 119, 255, 0.08);
      }
    }
    .el-tabs__active-bar {
      width: 100%;
      padding: 0;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      padding: 0;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
      & > span {
        display: inline-block;
        height: 100%;
        padding: 0 16px;
      }
    }
    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        width: 32.5%;
        height: 140px;
        padding: 36px 26px;
        background: #fff;
        p {
          b {
            display: block;
            font-size: 28px;
            font-weight: 500;
            color: #37393c;
            line-height: 40px;
          }
          span {
            color: #62666c;
            font-size: 16px;
          }
        }
        svg {
          font-size: 64px;
        }
      }
    }
    .bot {
      width: 100%;
      height: calc(100% - 160px);
      background: #fff;
      .filterTab {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        & > div {
          .el-input {
            width: 240px;
          }
          .el-select {
            width: 240px;
          }
          & > span {
            font-weight: 400;
            color: #2677ff;
            line-height: 20px;
            // margin-left: 16px;
            cursor: pointer;
          }
        }
      }
      .tableWrap {
        height: calc(100% - 131px);
        padding: 0px 20px;
        .autoHeader {
          font-size: 16px;
          color: #62666c;
          box-shadow: 2px 0px 8px 0px rgba(0, 0, 0, 0.08);
          cursor: pointer;
        }
        &-with-filter {
          height: calc(100% - 175px);
        }
      }
      .el-table {
        width: 99%;
        border: 0;
        .detail {
          padding: 0 0;
          height: 40px;
          line-height: 40px;
          border-bottom: 1px solid #ebeef5;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          img {
            vertical-align: middle;
          }
          p {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          div {
            div {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
        /deep/.el-table__body td.el-table__cell {
          padding: 0 !important;
        }
        /deep/.el-table__body .cell {
          padding: 0 !important;
        }
        .detail:last-child {
          border-bottom: 0;
        }
        .cell-other {
          padding: 0 0;
        }
      }
    }
  }
  // 定义单元格文本超出不换行
  /deep/.cell-other {
    // width: 140px !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
  }
  .box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;
  }
  .recommendlLeft {
    // padding-top: 20px;
    // width: 24%;
    // background: #fff;
    // overflow: auto;
    background: #ffffff linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #f3f7ff 100%);
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    // position: relative;
    .recommendTitle {
      font-weight: 500;
      color: #37393c;
      margin: 12px 16px 0px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e9ebef;
    }
    .recommendBox {
      height: calc(100% - 44px);
      overflow: auto;
    }
    li {
      padding: 16px 16px 0px;
      color: #62666c;
      // display: flex;
      .rightTitle {
        width: 100%;
        padding-bottom: 4px;
        font-size: 14px;
        color: #111;
      }
      .rightContent {
        span {
          display: inline-block;
          // width: 100%;
          padding: 0;
        }
        .clueClass {
          // max-height: 400px;
          // overflow-y: auto;
          .el-icon-picture-outline {
            font-size: 16px;
          }
        }
      }
    }
  }
}
.clueClass > span {
  display: inline-block;
  color: #37393c;
  margin-bottom: 12px;
  margin-right: 10px;
  padding: 3px 12px !important;
  border-radius: 14px;
  border: 1px solid #d1d5dd;
}
.clueClass > span > .myImg {
  width: 16px;
  height: 16px;
  font-size: 16px;
}
.clueClass > span > .myImg > img {
  width: 100%;
  height: 100%;
}
// /deep/.el-tooltip {
//   display: inline !important;
// }
.myHeader {
  display: flex;
  align-items: center;
  i {
    margin-right: 5px;
  }
}
.headerTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.headerTitle > span:first-child {
  margin-right: 12px;
}
.statisticnum {
  margin-right: 12px;
  font-size: 14px;
  font-weight: 400;
  color: #62666c;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  padding: 4px 10px;
  border-left: 2px solid #2677ff;
  .num {
    font-weight: 500;
    color: #2677ff;
    margin-right: 0px;
  }
}
.statisticnumBox > .statisticnum:last-child {
  margin-right: 0px;
}
#account_filter {
  margin-left: 16px;
}
.taskResults {
  position: absolute;
  height: 44px;
  line-height: 44px;
  padding-left: 16px;
  top: 0;
  left: 0;
  z-index: 10;
  font-weight: 500;
  color: #37393c;
  background-color: #fff;
  border-bottom: 1px solid #e9ebef;
  box-sizing: border-box;
}
.textBtn {
  font-size: 14px;
  color: #2677ff;
  cursor: pointer;
  &:hover {
    color: #4389ff;
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
