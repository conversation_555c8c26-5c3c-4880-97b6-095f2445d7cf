<template>
  <el-dialog class="elDialogAdd elDialogAddValidate" :close-on-click-modal="false" @close="$emit('close')" :visible="dialogVisible" width="500px">
    <template slot="title">
      不符合格式的资产
    </template>
    <div class="dialog-body">
      <div class="dialog-item" v-if="validateType == 'upload'">以下资产格式错误/不可解析,可以复制到文件修改 并重新上传</div>
      <div class="dialog-item" v-if="validateType == 'textarea'">以下资产格式错误/不可解析，可以复制并在输入框中修改</div>
      <div><div class="dialog-item" v-for="(item,k) in list" :key="k">{{ item }}</div></div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="$emit('close')">关闭</el-button>
      <el-button class="highBtn" type="primary" @click="confirm">移除并复制错误格式资产</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    },
    validateType: {
      type: String,
      default:'textarea'
    }
  },
  watch: {
    
  },
  data () {
    return {
      // dialogVisible: false,
      // list: []
    }
  },
  methods: {
    confirm() { 
      let list = this.list
      list.unshift(' ')
      this.$emit('copyText',list)
    },
  }
}
</script>

<style lang="less" scoped>
.highBtn {
  width: 174px;
}
</style>