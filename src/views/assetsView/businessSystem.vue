<template>
  <div class="container" :class="{ index: $route.path == '/index' }">
    <div class="headerTitle" v-if="!($route.path == '/index')">
      <span>
        业务系统
        <span v-if="notifyFilterId">
          / {{ notifyFilterMsg }}
          <span style="color: #2677ff; cursor: pointer" @click="clearNotifyFilter">恢复默认</span>
        </span>
      </span>
      <el-tooltip
        class="item"
        effect="dark"
        content="支持更新业务系统列表的url资产解析的ip、端口、状态码等信息"
        placement="top"
      >
        <el-button
          type="primary"
          class="normalBtn"
          :loading="updateInfoLoading"
          @click="handleAssetsUpdate"
          >更新资产</el-button
        >
      </el-tooltip>
    </div>

    <div class="home_header">
      <div v-if="percentage > 0" class="nowNum">
        正在检测：<el-progress
          :text-inside="true"
          :percentage="percentage ? percentage : 0"
          :stroke-width="15"
        ></el-progress>
      </div>
      <div class="nowNum" v-if="download_path">
        有检测并导出的任务已完成，<span class="clickDownload" @click="downloadLast">点击下载</span>
      </div>
      <p class="updateClass" v-if="!($route.path == '/index')" v-loading="updateInfoLoading">
        <i class="el-icon-warning myGray"></i>
        <template v-if="!updateInfo">暂无资产更新任务</template>
        <template v-else-if="updateInfo.status == 1">{{
          `最新更新资产任务于${updateInfo.created_at}开始，正在更新中...`
        }}</template>
        <template v-else-if="updateInfo.status == 2">{{
          `最新更新资产任务于${updateInfo.created_at}开始，并于${updateInfo.finish_at}完成，更新完成后共有 ${updateInfo.online_num} 个URL地址在线， ${updateInfo.offline_num} 个URL地址离线。`
        }}</template>
        <template v-else-if="updateInfo.status == 3">{{
          `最新更新资产任务于${updateInfo.created_at}开始，更新失败，请重新更新资产。`
        }}</template>
      </p>
      <div class="filterTab" v-if="!($route.path == '/index')">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
            id="keyword_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            <el-tooltip
              slot="prepend"
              class="item"
              effect="dark"
              content="支持检索字段：访问地址、系统名称、IP地址、域名"
              placement="top"
              :open-delay="100"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-input>
          <span @click="highCheckdialog = true" id="keyword_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="keyword_all"
            >选择全部</el-checkbox
          >

          <!-- <el-tooltip
            class="item"
            effect="dark"
            content="支持拉取IP台账的信息，补齐列表缺失字段"
            placement="top"
          >
            <el-button class="normalBtn" type="primary" @click="goHandle('1')"
              >一键关联IP台账</el-button
            >
          </el-tooltip> -->
          <!-- <el-tooltip
            class="item"
            effect="dark"
            content="支持将业务系统资产一键同步至IP资产台账"
            placement="top"
          >
            <el-button class="normalBtn" type="primary" @click="goHandle('2')"
              >一键同步至IP台账</el-button
            >
          </el-tooltip> -->
          <el-button
            style="margin-left: 10px"
            class="normalBtnRe"
            type="primary"
            @click="remove('more')"
            id="keyword_more_del"
            >删除</el-button
          >

          <el-button
            class="normalBtn"
            type="primary"
            :disabled="isImportExport"
            @click="importTz"
            id="keyword_more_open"
            >导入</el-button
          >
          <el-dropdown>
            <el-button class="normalBtn" type="primary" id="account_deal"
              >资产标记<i class="el-icon-arrow-down el-icon--right"></i
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handleAssets(2)">标记忽略</el-dropdown-item>
              <el-dropdown-item @click.native="handleAssets(1)">标记认领</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-tooltip
            class="item"
            effect="dark"
            content="支持将台账的URL资产拉取到业务系统"
            placement="top"
          >
            <el-button
              class="normalBtn"
              type="primary"
              :loading="pullLoading"
              @click="goPullAssets()"
              >一键拉取IP台账</el-button
            >
          </el-tooltip>
          <el-button
            class="normalBtnRe"
            type="primary"
            :disabled="isImportExport || exportLoadingBtn"
            :loading="exportLoadingBtn"
            @click="exportExcel"
            id="keyword_more_open"
            >导出</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        v-if="!($route.path == '/index')"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="certAssets"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :fit="true"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            v-if="!($route.path == '/index')"
            type="selection"
            align="center"
            :reserve-selection="true"
            default-expand-all
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :label="item.label"
            :fixed="item.fixed"
          >
            <template slot="header">
              <template v-if="item.labelTip">
                <el-tooltip class="item" effect="dark" :content="item.labelTip" placement="top">
                  <span>{{ item.label }} <i class="el-icon-info"></i></span>
                </el-tooltip>
              </template>
              <template v-else>
                {{ item.label }}
              </template>
            </template>
            <template slot-scope="scope">
              <span v-if="item.name == 'system_status'">
                <span class="greenLine" v-if="scope.row[item.name] == 1">在线</span>
                <span class="grayLine" v-else-if="scope.row[item.name] == 2">离线</span>
                <span v-else>-</span>
              </span>
              <!-- <span v-else-if="item.name == 'status'">
                  <span v-if="scope.row[item.name] == 0">待认领</span>
                  <span v-else-if="scope.row[item.name]">
                    {{ scope.row[item.name] == 1 ? '已认领' : '已忽略' }}
                  </span>
                  <span v-else>-</span>
                </span> -->
              <span v-else-if="item.name == 'source'">{{
                scope.row[item.name] == 1 ? '手动导入' : '台账同步'
              }}</span>
              <template v-else-if="item.name == 'address'">
                <span class="addressContent">
                  <span class="address ellipsis">
                    <el-tooltip
                      :open-delay="500"
                      class="item"
                      effect="dark"
                      :content="String(scope.row[item.name])"
                      placement="top"
                    >
                      <a
                        v-if="
                          String(scope.row[item.name]) &&
                          String(scope.row[item.name]).includes('http')
                        "
                        style="color: #409eff"
                        :href="scope.row[item.name]"
                        target="_blank"
                        >{{ scope.row[item.name] }}</a
                      >
                      <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
                    </el-tooltip>
                  </span>
                </span>
              </template>
              <template v-else-if="item.name == 'status'">
                <span class="status grayLine" v-if="scope.row.status == 0">待认领</span>
                <span
                  class="status"
                  :class="scope.row.status == 1 ? 'greenLine' : 'originLine'"
                  v-else-if="scope.row.status"
                >
                  {{ scope.row.status == 1 ? '已认领' : '已忽略' }}
                </span>
              </template>
              <el-tooltip
                v-else-if="scope.row[item.name]"
                class="item"
                effect="dark"
                :content="scope.row[item.name]"
                placement="top"
              >
                <span>{{ scope.row[item.name] }}</span>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="left"
            width="80"
            v-if="$route.path != '/index'"
          >
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="editOne(scope.row)" id="user_edit"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-if="!($route.path == '/index')"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleHedui"
      width="415px"
    >
      <template slot="title"> 导入 </template>
      <div class="dialog-body">
        <p class="downloadClass" @click="downloadForbidIpsHd('')">
          <i class="el-icon-warning"></i>请点击下载
          <span>业务系统导入模板</span>
        </p>
        <el-upload
          class="upload-demo"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          accept=".xlsx,.csv"
          :before-upload="beforeIpUpload"
          :on-success="uploadSucc"
          :on-remove="uploadRemove"
          :on-change="uploadChange"
          :on-error="uploadErr"
          :limit="uploadMaxCount"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            支持上传xlsx 文件，单次最多导入5000条资产，且大小不超过20M
          </div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleHedui = false"
          id="account_check_cancel"
          >关闭</el-button
        >
        <el-button
          :loading="btnLoading"
          class="highBtn"
          @click="heduiTzFunSave"
          id="account_check_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      v-if="!($route.path == '/index')"
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="550px"
    >
      <template slot="title"> 编辑 </template>
      <div class="dialog-body">
        <el-form
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="ruleForm"
          label-width="90px"
          class="demo-ruleForm"
        >
          <el-form-item label="系统名称" prop="system_name">
            <el-input v-model="ruleForm.system_name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="访问地址：" prop="address">
            <el-input
              :disabled="true"
              v-model="ruleForm.address"
              placeholder="请输入访问地址"
            ></el-input>
          </el-form-item>
          <el-form-item label="IP：" prop="ip">
            <el-input :disabled="true" v-model="ruleForm.ip" placeholder="请输入IP"></el-input>
          </el-form-item>
          <el-form-item label="端口：" prop="port">
            <el-input :disabled="true" v-model="ruleForm.port" placeholder="请输入端口"></el-input>
          </el-form-item>
          <el-form-item label="子域名：" prop="domain">
            <el-input
              :disabled="true"
              v-model="ruleForm.domain"
              placeholder="请输入子域名"
            ></el-input>
          </el-form-item>
          <el-form-item label="协议：" prop="protocol">
            <el-input
              :disabled="true"
              v-model="ruleForm.protocol"
              placeholder="请输入协议"
            ></el-input>
          </el-form-item>
          <el-form-item label="归属：" prop="belongs">
            <el-input v-model="ruleForm.belongs" placeholder="请输入归属"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="note">
            <el-input
              type="textarea"
              :rows="2"
              maxlength="50"
              show-word-limit
              v-model="ruleForm.note"
              placeholder="请输入备注信息，50字以内"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="otherLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer
      v-if="!($route.path == '/index')"
      title="高级筛选"
      :visible.sync="highCheckdialog"
      direction="rtl"
      ref="drawer"
    >
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="110px">
          <el-form-item label="系统名称：" prop="system_name">
            <el-input v-model="formInline.system_name" placeholder="请输入系统名称"></el-input>
          </el-form-item>
          <el-form-item label="访问地址：" prop="address">
            <el-input v-model="formInline.address" placeholder="请输入访问地址"></el-input>
          </el-form-item>
          <el-form-item label="IP：" prop="ip">
            <el-input v-model="formInline.ip" placeholder="请输入IP"></el-input>
          </el-form-item>
          <el-form-item label="端口：" prop="port">
            <el-select
              filterable
              v-model="formInline.port"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'port', condition.port, false, true)"
            >
              <el-option
                v-for="item in condition.port"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态码：" prop="status_code">
            <el-select
              filterable
              v-model="formInline.status_code"
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'status_code', condition.status_code, false, false)"
            >
              <el-option
                v-for="item in condition.status_code"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="主域名：" prop="top_domain">
            <!-- <el-input v-model="formInline.top_domain" placeholder="请输入主域名"></el-input> -->
            <el-select
              filterable
              v-model="formInline.top_domain"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'top_domain', condition.top_domain, false, true)"
            >
              <el-option
                v-for="item in condition.top_domain"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="子域名：" prop="domain">
            <el-input v-model="formInline.domain" placeholder="请输入子域名"></el-input>
          </el-form-item>
          <el-form-item label="协议：" prop="protocol">
            <el-select
              filterable
              v-model="formInline.protocol"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'protocol', condition.protocol, false, true)"
            >
              <el-option
                v-for="item in condition.protocol"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资产状态：" prop="system_status">
            <el-select
              v-model="formInline.system_status"
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'system_status', certStatusArr, true, false)"
            >
              <el-option label="在线" :value="1"></el-option>
              <el-option label="离线" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="处理状态：" prop="status">
            <el-select
              v-model="formInline.status"
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'status', statusArr, true, false)"
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="来源：" prop="source">
            <el-select
              v-model="formInline.source"
              placeholder="请选择"
              filterable
              multiple
              clearable
              @change="selectChange($event, 'source', sourceArr, true, true)"
            >
              <el-option
                v-for="item in sourceArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属：" prop="belongs">
            <el-input v-model="formInline.belongs" placeholder="请输入归属"></el-input>
          </el-form-item>
          <el-form-item label="导入时间：" prop="created_at">
            <el-date-picker
              v-model="formInline.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="更新时间：" prop="updated_at">
            <el-date-picker
              v-model="formInline.updated_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="最后存活时间：" prop="last_live_at">
            <el-date-picker
              v-model="formInline.last_live_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import hightFilter from '../../components/assets/highTab.vue'
import {
  businessAssetsSync,
  businessProgress,
  businessAssociation,
  businessSyncAssets,
  businessInsert,
  businessEdit,
  businessList,
  businessExport,
  businessDelPhp,
  businessDel,
  businessAssetsUpdateInfo,
  businessAssetsUpdate,
  businessHandleStatus
} from '@/api/apiConfig/api.js'
export default {
  components: {
    hightFilter
  },
  data() {
    return {
      updateInfoLoading: false,
      updateInfo: {},
      exportLoadingBtn: false,
      notifyFilterMsg: '',
      notifyFilterId: '',
      highTabShow: [
        {
          label: '系统名称',
          name: 'system_name',
          type: 'input'
        },
        {
          label: '访问地址',
          name: 'address',
          type: 'input'
        },
        {
          label: 'IP',
          name: 'ip',
          type: 'input'
        },
        {
          label: '端口',
          name: 'port',
          type: 'select'
        },
        {
          label: '主域名',
          name: 'top_domain',
          type: 'input'
        },
        {
          label: '域名',
          name: 'domain',
          type: 'input'
        },
        {
          label: '协议',
          name: 'protocol',
          type: 'select'
        },
        {
          label: '状态码',
          name: 'status_code',
          type: 'select'
        },
        {
          label: '资产状态',
          name: 'system_status',
          type: 'select'
        },
        {
          label: '状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '来源',
          name: 'source',
          type: 'select'
        },
        {
          label: '归属',
          name: 'belongs',
          type: 'input'
        },
        {
          label: '最后一次存活时间',
          name: 'last_live_at',
          type: 'date'
        },
        {
          label: '导入时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      condition: {},
      isImportExport: false,
      percentage: 0,
      updateType: 0,
      dialogFormVisibleHedui: false,
      dialogFormVisibleInsert: false,
      updateTypeDialog: false,
      highlist: null,
      formInline: {
        page: 1,
        per_page: 10,
        keyword: '',
        ip: '',
        port: [],
        domain: '',
        address: '',
        system_name: '',
        protocol: [],
        top_domain: [],
        belongs: '',
        system_status: '',
        source: '',
        created_at: [],
        updated_at: [],
        last_live_at: [],
        operate_company_id: ''
      },
      sourceArr: [
        {
          id: 1,
          name: '手动导入'
        },
        {
          id: 2,
          name: '台账同步'
        }
        // {
        //   id: 0,
        //   name: '-'
        // }
      ],
      checkedAll: false,
      highCheckdialog: false,
      certStatusArr: [
        {
          name: '在线',
          id: 1
        },
        {
          name: '离线',
          id: 2
        }
      ],
      statusArr: [
        {
          name: '待认领',
          id: 3
        },
        {
          name: '已认领',
          id: 1
        },
        {
          name: '已忽略',
          id: 2
        }
      ],
      tableHeader: [
        {
          label: '访问地址',
          name: 'address',
          fixed: 'left',
          minWidth: '200'
        },
        {
          label: '系统名称',
          name: 'system_name',
          minWidth: '120'
        },
        {
          label: 'IP地址',
          name: 'ip',
          minWidth: '120'
        },
        {
          label: '端口',
          name: 'port',
          minWidth: '80'
        },
        {
          label: '主域名',
          name: 'top_domain',
          minWidth: '120'
        },
        {
          label: '子域名',
          name: 'domain',
          minWidth: '120'
        },
        {
          label: '协议',
          name: 'protocol',
          minWidth: '120'
        },
        {
          label: '状态码',
          name: 'status_code',
          minWidth: '100'
        },
        {
          label: '资产状态',
          name: 'system_status',
          labelTip:
            '资产状态表示数据的IP端口是否在线，可通过telnet IP端口进行验证，若连接成功则判定为在线，否则为离线。',
          minWidth: '100'
        },
        {
          label: '归属',
          name: 'belongs',
          minWidth: '120'
        },
        {
          label: '处理状态',
          name: 'status',
          minWidth: '80'
        },
        {
          label: '来源',
          name: 'source',
          minWidth: '120'
        },
        {
          label: '最后一次存活时间',
          name: 'last_live_at',
          minWidth: '150'
        },
        {
          label: '导入时间',
          name: 'created_at',
          minWidth: '150'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: '150'
        },
        {
          label: '备注',
          name: 'note',
          minWidth: '120'
        }
      ],
      typeArr: [
        {
          name: '全部',
          id: 0
        },
        {
          name: '数字资产',
          id: 1
        },
        {
          name: '数据泄露',
          id: 2
        }
      ],
      // statusArr: [
      //   // 状态 0/1 禁用/启用
      //   {
      //     name: '禁用',
      //     id: 0
      //   },
      //   {
      //     name: '启用',
      //     id: 1
      //   }
      // ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      uploadAction: `${this.golangUploadSrcIp}/public/upload`,
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      btnLoading: false,
      certInfoDialog: false,
      file_path: '',
      ruleForm: {
        system_name: '',
        belongs: '',
        note: '',
        ip: '',
        port: '',
        domain: '',
        address: '',
        protocol: '',
        system_status: '',
        source: ''
      },
      rules: {
        ip: [{ required: true, message: '请输入IP', trigger: 'blur' }],
        port: [{ required: true, message: '请输入端口', trigger: 'blur' }]
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      user: {
        role: ''
      },
      download_path: '',
      setTimeoutTimer: null,
      pullLoading: false
    }
  },
  mounted() {
    this.notifyFilterId = this.$route.query.notifyFilterId
    this.notifyFilterMsg = this.$route.query.notifyFilterMsg
    this.formInline.website_message_id = Number(this.notifyFilterId) || null

    if (sessionStorage.getItem('userMessage')) {
      this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (this.userInfo) {
        this.user = this.userInfo.user
      }
    }

    if (this.user.role == 2 && !this.currentCompany) return
    this.getData()
    this.getUpdateInfo()
    this.getTaskInfo()
  },
  watch: {
    getterCompanyChange(val) {
      if (this.notifyFilterId) {
        this.clearNotifyFilter()
      }
    },
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      if (this.user.role == 2) {
        this.getData()
        this.getTaskInfo()
        this.getUpdateInfo()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  beforeDestroy() {
    this.setTimeoutTimer = null
    clearTimeout(this.setTimeoutTimer)
  },
  methods: {
    getUpdateInfo() {
      this.updateInfoLoading = true
      businessAssetsUpdateInfo({
        operate_company_id: this.currentCompany
      }).then((res) => {
        if (res.code == 0) {
          this.updateInfo = res.data ? res.data : false
          this.updateInfoLoading = false
        }
      })
    },
    handleAssetsUpdate() {
      businessAssetsUpdate({
        operate_company_id: this.currentCompany
      }).then((res) => {
        if (res.code == 0) {
          this.$message.success('操作成功')
          this.updateInfoLoading = true
          setTimeout(() => {
            this.getUpdateInfo()
            this.getData()
          }, 2000)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    async goPullAssets() {
      this.pullLoading = true
      let res = await businessAssetsSync({ operate_company_id: this.currentCompany }).catch(() => {
        this.pullLoading = false
      })
      if (res.code == 0) {
        this.$message.success('操作成功')

        setTimeout(() => {
          this.getData()
          this.pullLoading = false
        }, 2000)
      }
    },
    handleAssets(setStatus) {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      this.$confirm(`确定要 ${setStatus == 1 ? '认领' : '忽略'} 所选资产吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formInline.page = this.currentPage
        this.formInline.per_page = this.pageSize
        this.formInline.ids = this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            })
        let obj = Object.assign({}, this.formInline)
        businessHandleStatus({
          ids: this.checkedArr,
          set_status: setStatus,
          ...obj
        }).then((res) => {
          if (res.code == 0) {
            this.$message.success('操作成功')
            this.checkedAll = false
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.$refs.eltable.clearSelection()
            this.getData()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    clearNotifyFilter() {
      this.$router.replace({ path: '/businessSystem', query: {} })
    },
    downloadForbidIpsHd(icon) {
      window.location.href = '/downloadTemplate/业务系统导入模板.xlsx'
    },
    // 下载上次检测并导出的数据
    downloadLast() {
      this.download(this.showSrcIp + this.download_path)
      this.download_path = ''
    },
    async getTaskInfo(isInfo) {
      // 获取资产探测详情数据
      let obj = {
        operate_company_id: this.currentCompany
      }
      let res = await businessProgress(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        if (res.data) {
          if (res.data.status && res.data.status == 1) {
            // 正在探活
            this.percentage = res.data.progress / 1
            this.setTimeoutTimer = null
            this.isImportExport = true // 有任务正在探活，不可导入或导出
            clearTimeout(this.setTimeoutTimer)
            this.setTimeoutTimer = setTimeout(() => {
              this.getTaskInfo('isInfo')
            }, 5000)
          } else {
            // 探测任务已完成
            if (isInfo) {
              // 需要探测成功提示
              this.percentage = 100
              setTimeout(() => {
                // 页面停留100%一秒
                this.percentage = 0
                this.isImportExport = false // 可导入或导出
                this.$message.success('资产状态检测完成！')
                this.getData() // 进度完成后刷新列表
                this.setTimeoutTimer = null
                clearTimeout(this.setTimeoutTimer)
                if (res.data.download_path) {
                  // 导出的探活已完成，自动下载
                  this.download(this.showSrcIp + res.data.download_path)
                }
              }, 1000)
            } else {
              // 刷新页面或者从其他模块进度页面不需要提示信息
              this.percentage = 0
              // 如果是导出的探测任务，download_path有值代表探测完还没有下载，提示用户下载
              if (res.data && res.data.type == 2 && res.data.download_path) {
                this.download_path = res.data.download_path
              }
              this.isImportExport = false // 可导入或导出
              this.setTimeoutTimer = null
              clearTimeout(this.setTimeoutTimer)
            }
          }
        } else {
          // 返回数据为空
          this.setTimeoutTimer = null
          clearTimeout(this.setTimeoutTimer)
        }
      }
    },
    async goHandle(icon) {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.ids = this.checkedAll
        ? []
        : this.checkedArr.map((item) => {
            return item.id
          })
      let obj = Object.assign({}, this.formInline)
      let res = null
      if (icon == 1) {
        // 关联
        res = await businessAssociation(obj)
      } else {
        // 同步
        res = await businessSyncAssets(obj)
      }
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.checkedAll = false
        this.$refs.eltable.clearSelection()
        this.getData()
      }
    },
    async importTz() {
      this.fileList = []
      this.uploadIsreading = '' // 文件是否读取成功
      this.dialogFormVisibleHedui = true
    },
    async heduiTzFunSave() {
      if (this.uploadIsreading == '' && this.fileList.length == 0) {
        // 文件是否读取成功
        this.$message.error('请上传文件！')
        return
      } else if (this.uploadIsreading == 'ready' || this.uploadIsreading == 'uploading') {
        // 文件是否读取成功
        this.$message.error('文件正在读取，请稍后…')
        return
      } else if (this.uploadIsreading == 'success' && this.file_path == 0) {
        this.$message.error('文件未读取到信息，请重新上传')
        return
      }
      this.btnLoading = true
      let res = await businessInsert({
        file_path: this.file_path,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.btnLoading = false
        this.dialogFormVisibleHedui = false
        this.getData() // 进度完成后刷新列表
        this.getTaskInfo('isInfo')
      } else {
        this.btnLoading = false
      }
    },
    async editOne(row) {
      this.dialogLoading = true
      this.dialogFormVisibleInsert = true
      for (let i in this.ruleForm) {
        this.ruleForm[i] = row[i]
      }
      this.ruleForm.id = row.id
    },
    async insertSave(formName) {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.ruleForm.operate_company_id = this.currentCompany
          let res = await businessEdit({ ...this.ruleForm })
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.dialogFormVisibleInsert = false
            this.ruleForm = {
              system_name: '',
              belongs: '',
              note: '',
              ip: '',
              port: '',
              domain: '',
              address: '',
              protocol: '',
              system_status: '',
              source: ''
            }
            this.getData()
          }
        }
      })
    },
    getValid(type) {
      let str = ''
      switch (type) {
        case 0:
          str = '不可信'
          break
        case 1:
          str = '可信'
          break
        default:
      }
      return str
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else if (this.$route.path == '/index') {
        bol = 'tableIndex'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    beforeIpUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过 20MB!')
      }
      return isLt1M
    },
    uploadSucc(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.file_path = response.data.path
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadErr(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    uploadChange(file, fileList) {
      this.uploadIsreading = file.status
    },
    uploadRemove(file, fileList) {},
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过20MB!')
      }
      return isLt1M
    },
    async getData() {
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at = this.formInline.created_at ? this.formInline.created_at : []
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      this.formInline.last_live_at = this.formInline.last_live_at
        ? this.formInline.last_live_at
        : []
      let res = await businessList(this.formInline).catch((error) => {
        this.tableData = []
        this.total = 0
        this.loading = false
      })
      this.loading = false
      if (res.code == 0) {
        this.tableData = res.data.items ? res.data.items : []
        this.total = res.data.total ? res.data.total : 0
        this.condition = res.data && res.data.condition ? res.data.condition : {}
        // 全选操作
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
          })
        }
        if ((!this.total || this.total == 0) && this.notifyFilterId) {
          this.$message.error('该批新增业务系统已被删除')
        }
      }
    },
    async exportExcel() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      let params = {
        ...this.formInline,
        redetect: false,
        ids: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            })
      }
      try {
        this.exportLoadingBtn = true
        let res = await businessExport(params)
        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.path)
          this.$refs.eltable.clearSelection()
          this.checkedAll = false
          this.exportLoadingBtn = false
          this.$message.success('导出成功')
        }
      } catch (error) {
        this.exportLoadingBtn = false
      }
      // this.$confirm('是否需要对导出数据进行状态检测?', '提示', {
      //   distinguishCancelAndClose: true, // 设置关闭和取消独立
      //   confirmButtonText: '检测并导出',
      //   cancelButtonText: '直接导出',
      //   cancelButtonClass: 'keyword_del_cancel',
      //   confirmButtonClass: 'keyword_del_sure',
      //   customClass: 'keyword_del',
      //   type: 'warning'
      // })
      //   .then(async () => {
      //     // 检测并导出
      //     let params = {
      //       ...this.formInline,
      //       redetect: true,
      //       ids: this.checkedAll
      //         ? []
      //         : this.checkedArr.map((item) => {
      //             return item.id
      //           })
      //     }
      //     let res = await this.businessExport(params)
      //     if (res.code == 0) {
      //       this.getTaskInfo('isInfo') // 获取资产检测进度
      //     }
      //   })
      //   .catch(async (action) => {
      //     if (action == 'cancel') {
      //       // 取消事件（直接导出），关闭close时不执行
      //       let params = {
      //         ...this.formInline,
      //         redetect: false,
      //         ids: this.checkedAll
      //           ? []
      //           : this.checkedArr.map((item) => {
      //               return item.id
      //             })
      //       }
      //       try {
      //         this.exportLoadingBtn = true
      //         let res = await this.businessExport(params)
      //         if (res.code == 0) {
      //           this.download(this.showSrcIp + res.data.path)
      //           this.exportLoadingBtn = false
      //         }
      //       } catch (error) {
      //         this.exportLoadingBtn = false
      //       }
      //     }
      //   })
    },
    async remove(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'keyword_del_cancel',
        confirmButtonClass: 'keyword_del_sure',
        customClass: 'keyword_del',
        type: 'warning'
      })
        .then(async () => {
          this.formInline.page = this.currentPage
          this.formInline.per_page = this.pageSize
          this.formInline.ids = this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              })
          let obj = Object.assign({}, this.formInline)

          let resPhp = await businessDelPhp(obj)
          if (resPhp.code == 0) {
            let res = await businessDel(obj)
            if (res.code == 0) {
              this.$message.success('操作成功！')
              this.checkedAll = false
              this.currentPage = this.updateCurrenPage(
                this.total,
                this.checkedArr,
                this.currentPage,
                this.pageSize
              ) // 更新页码
              this.$refs.eltable.clearSelection()
              this.getData()
            }
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.keyword_del>.el-message-box__btns')
        del.children[0].id = 'keyword_del_cancel'
        del.children[1].id = 'keyword_del_sure'
      }, 50)
    },
    downloadcluesExcel() {
      window.location.href = '/downloadTemplate/关键词管理模板.xlsx'
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        page: 1,
        per_page: 10,
        keyword: '',
        ip: '',
        port: [],
        domain: '',
        address: '',
        system_name: '',
        top_domain: [],
        protocol: [],
        belongs: '',
        system_status: '',
        source: '',
        created_at: [],
        updated_at: [],
        last_live_at: [],
        operate_company_id: ''
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .validClassNormal {
    color: #2677ff;
  }
  .validClassNot {
    color: #ff7900;
  }
  .dialog-body {
    .certTitle {
      font-weight: bold;
      padding: 3px 0 3px 8px;
      margin-bottom: 10px;
      border-left: 4px solid #2677ff;
    }
    /deep/.certInfoList {
      .el-form-item .el-form-item__label {
        font-weight: bold !important;
      }
    }
    /deep/.certInfoTable {
      margin-bottom: 10px;
      th.el-table__cell::after {
        height: 0;
      }
      td {
        border-right: 1px solid #ebeef5;
      }
      tbody tr:hover > td {
        background-color: unset !important;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    overflow: hidden;
    // padding-top: 16px;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .nowNum {
      position: absolute;
      right: 0;
      top: -42px;
      display: flex;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      border-radius: 2px;
      padding: 4px 10px;
      border-left: 2px solid #2677ff;
      .el-progress {
        width: 150px;
      }
      .num {
        font-weight: 500;
        color: #2677ff;
        margin-right: 0px;
      }
      .clickDownload {
        cursor: pointer;
        text-decoration: underline;
        text-decoration-color: #2677ff;
        text-underline-offset: 2px;
      }
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 230px) !important;
    }
    .tableIndex {
      height: 100%;
    }
    .tableWrap {
      height: calc(100% - 185px);
      padding: 0px 20px;
    }
    .ruleItemBox {
      display: flex;
      flex-wrap: wrap !important;
    }
    .ruleItem,
    .ruleItemNum {
      line-height: 16px;
      padding: 2px 10px;
      margin: 5px 8px 5px 0px;
      background: #ffffff;
      border-radius: 14px;
      border: 1px solid #d1d5dd;
      white-space: pre-wrap;
    }
    .ruleItemNum {
      display: inline-block;
      background: #f0f3f8;
      border: 1px solid #dce5f3;
      cursor: pointer;
    }
    .el-table {
      border: 0;
      .detailDiv {
        border-bottom: 1px solid #ebeef5;
      }
      .detailDiv:last-child {
        border-bottom: 0;
      }
      .detail {
        padding: 0;
        height: 40px;
        line-height: 40px;
        border-bottom: 1px solid #ebeef5;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .detail:last-child {
        border-bottom: 0;
      }
      /deep/.el-table__body td.el-table__cell div {
        padding: 0 !important;
        .cell {
          padding: 0 !important;
        }
      }
      // /deep/.el-table__body .cell {
      //   padding: 0 !important;
      // }
    }
  }
}
.index {
  height: 90% !important;
}

.headerTitle {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.updateClass {
  position: relative;
  height: 40px;
  line-height: 40px;
  margin: 16px 20px 0;
  background: #f0f3f8;
  color: #62666c;
  border-radius: 4px;
  // border: 1px solid rgba(38, 119, 255, 0.44);
  cursor: pointer;
  i {
    font-size: 14px;
    color: #2677ff;
    margin: 0 8px 0 16px;
  }
  .el-icon-close {
    position: absolute;
    right: 10px;
    top: 12px;
    font-size: 16px;
    color: #9fa6af;
    &:hover,
    &:focus {
      background-color: transparent;
      color: rgba(159, 166, 175, 1) !important;
    }
  }
  span {
    color: #2677ff;
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
.addressContent {
  width: 100%;
  display: flex;
  align-items: center;
  .status {
    width: 30px;
  }
  .address {
    flex: 1;
    width: 0;
  }
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
