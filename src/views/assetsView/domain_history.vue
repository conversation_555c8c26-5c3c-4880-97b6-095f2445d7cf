<template>
  <div class="moduleCard rightWrap" v-loading="allLoading">
    <div class="infoTitle searchInfoTitle">
      <div class="title">历史绑定域名</div>
      <!-- <el-input
          v-model="formInline.keyword"
          @keyup.enter.native="highCheck"
          placeholder="请输入关键字进行搜索"
          id="account_keycheck"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="highCheck"
          ></el-button>
          <el-tooltip
            slot="prepend"
            class="item"
            effect="dark"
            content="支持检索字段：IP地址、网站标题、域名、企业名称"
            placement="top"
            :open-delay="100"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-input>
        <span
          @click="highCheckClick"
          class="advancedFilter"
          ><img
            src="../../assets/images/filter.png"
            alt=""
            style="width: 16px; vertical-align: middle; margin-right: 3px"
          />高级筛选</span
        > -->
      <!-- </div> -->
    </div>
    <div class="cardContent">
      <el-timeline>
        <el-timeline-item v-for="item in timeLineData" :key="item.id">
          <span class="dot" slot="dot"></span>
          <div class="tiemBox">
            {{ item.found_time }}&nbsp;<span class="tiemContent">绑定域名:{{ item.domain }}</span
            >&nbsp;<span class="tiemContent">域名备案：{{ item.icp_company }}</span
            >&nbsp;
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    <hightFilter
      id="hightFilter"
      :highTabShow="highTabShow"
      :highlist="highlist"
      pageIcon="eventwarning"
      @highcheck="highCheck"
    ></hightFilter>
  </div>
</template>
<script>
import hightFilter from '../../components/assets/highTab.vue'

export default {
  props: ['timeLineData', 'domainHistoryLoading'],
  components: { hightFilter },
  data() {
    return {
      sourceMap: {
        1: 'FOFA',
        2: 'IP138'
      },
      highlist: null,
      highTabShow: [
        {
          label: '事件名称',
          name: 'keyword',
          type: 'input'
        },
        {
          label: '状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '首次发现时间',
          name: 'created_at_range',
          type: 'date'
        },
        {
          label: '最新发现时间',
          name: 'updated_at_range',
          type: 'date'
        }
      ],
      formInline: {}
    }
  },
  computed: {
    allLoading() {
      return this.domainHistoryLoading
    }
  },
  methods: {
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    highCheckClick() {}
  }
}
</script>
<style lang="less" scoped>
.rightWrap {
  height: 300px !important;
  overflow: auto;
  margin-bottom: 12px;
  .searchInfoTitle {
    // display: flex;
    // align-items: center;
    .title {
      width: 190px;
    }
    #account_keycheck {
      flex: 1;
    }
    .advancedFilter {
      width: 175px;
    }
  }

  // .el-timeline-item__node {
  //   background: #ffffff;
  //   border: 2px solid #4285f4;
  // }
  // .el-timeline-item__node--normal {
  //   left: 0px;
  // }
  // .el-timeline-item__tail {
  //   left: 7px;
  //   border-left: 2px solid #edf0f5;
  // }
  // .el-timeline-item__wrapper {
  //   padding-right: 20px;
  // }
  // .el-card.is-always-shadow {
  //   box-shadow: none;
  // }
}

::v-deep .el-timeline {
  .el-timeline-item__tail {
    left: 7px;
  }
  .el-timeline-item__dot {
    top: -1px;
    .dot {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(38, 119, 255, 1);
      border-radius: 50%;
      box-sizing: border-box;
      background-color: #fff;
    }
  }
}
</style>
