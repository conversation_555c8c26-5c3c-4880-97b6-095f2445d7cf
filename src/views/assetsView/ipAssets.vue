<template>
  <!-- 此页面是【资产台账-IP资产-IP维度】，【单位测绘/云端推荐-推荐资产库】公用 -->
  <div class="container" v-loading="loading">
    <div v-if="$route.path == '/unitIndex' || $route.path == '/assetsCloud'" class="headerTitle">
      <div>
        <span class="goback" @click="$emit('closeLog')"
          ><i class="el-icon-arrow-left"></i>返回上一层</span
        >
        <span class="spline">/</span>
        <span>推荐资产库</span>
      </div>
    </div>
    <p class="downloadClass1" v-if="tipData && $route.path == '/assetsLedger'">
      <el-tooltip placement="top" :open-delay="500">
        <div slot="content" style="display: flex">
          <span style="color: #62666c">任务记录：</span>
          <span>{{ `${tipData.name}任务` }}</span>
          <span class="myStatus mygrayStatus" v-if="tipData.status == 0">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myblueStatus" v-else-if="tipData.status == 1">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus mygreenStatus" v-else-if="tipData.status == 2">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myredStatus" v-else-if="tipData.status == 3">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myyellowStatus" v-else>{{ getTableStatus(tipData.status) }}</span
          >，
          <span v-if="tipData.status == 1 || tipData.status == 4" style="color: #2677ff"
            >{{ `${tipData.progress}%` }}<span class="tipDataContent">，</span></span
          >
          <span class="tipDataContent" v-if="tipData.status == 2"
            >{{ `${getResult(tipData)}` }}，</span
          >
          <span @click="goAssetsScan" class="taskBlue"
            >详情<i class="el-icon-arrow-right"></i
          ></span>
        </div>
        <div style="display: flex !important; align-items: center">
          <span style="color: #62666c">任务记录：</span>
          <span class="task">{{ `${tipData.name}任务` }}</span>
          <span class="myStatus mygrayStatus" v-if="tipData.status == 0">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myblueStatus" v-else-if="tipData.status == 1">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus mygreenStatus" v-else-if="tipData.status == 2">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myredStatus" v-else-if="tipData.status == 3">{{
            getTableStatus(tipData.status)
          }}</span>
          <span class="myStatus myyellowStatus" v-else>{{ getTableStatus(tipData.status) }}</span
          >，
          <span v-if="tipData.status == 1 || tipData.status == 4"
            >{{ `${tipData.progress}%` }}<span class="tipDataContent">，</span></span
          >
          <span @click="goAssetsScan" class="taskBlue"
            >详情<i class="el-icon-arrow-right"></i
          ></span>
        </div>
      </el-tooltip>
    </p>
    <div
      class="filterTab"
      v-if="
        $route.path == '/unitIndex' || ($route.path == '/assetsCloud' && !($route.path == '/index'))
      "
    >
      <div>
        <el-input
          v-model="formInline.keyword"
          @keyup.enter.native="highCheck"
          placeholder="请输入关键字进行搜索"
          v-if="$route.path != '/scanReg'"
          id="account_keycheck"
        >
          <el-button slot="append" icon="el-icon-search" @click="highCheck"></el-button>
        </el-input>
      </div>
      <div>
        <el-checkbox
          class="checkboxAll"
          v-model="checkedAll"
          @change="checkAllChange"
          id="account_all"
          >选择全部</el-checkbox
        >

        <el-button
          class="normalBtnRe"
          type="primary"
          :loading="exportLoadingBtn"
          :disabled="exportLoadingBtn"
          @click="exportList"
          id="account_export"
          >导出</el-button
        >
      </div>
    </div>
    <div
      class="filterTab"
      v-if="
        $route.path != '/assetsCloud' && $route.path != '/unitIndex' && !($route.path == '/index')
      "
    >
      <div style="display: flex; align-items: center">
        <div class="confirmBox" v-if="$route.path == '/assetsLedger'" style="margin-left: 0px">
          <el-radio-group v-model="second_confirm" @change="getConfirmList">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="0">
              未确权
              <el-tooltip
                :open-delay="500"
                v-if="$route.path == '/assetsLedger'"
                content="推荐资产需要进行确权才可下发漏洞扫描任务，开启资产确权后，系统将自动为您筛选出待确认的推荐数据"
                placement="top"
                effect="dark"
              >
                <i
                  class="el-icon-question"
                  style="color: #b1bdd1; margin: 1px 0px 0px 4px; font-size: 16px"
                ></i>
              </el-tooltip>
            </el-radio-button>
            <el-radio-button label="1">已确权</el-radio-button>
          </el-radio-group>
        </div>
        <el-input
          v-model="formInline.keyword"
          @keyup.enter.native="highCheck"
          placeholder="请输入关键字进行搜索"
          v-if="$route.path != '/scanReg'"
          id="account_keycheck"
        >
          <el-button slot="append" icon="el-icon-search" @click="highCheck"></el-button>
          <el-tooltip
            slot="prepend"
            class="item"
            effect="dark"
            content="支持检索字段：IP地址、网站标题、域名、企业名称"
            placement="top"
            :open-delay="100"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-input>
        <span
          @click="highCheckClick"
          v-if="$route.path != '/scanReg'"
          id="account_filter"
          style="width: 80px"
          ><img
            src="../../assets/images/filter.png"
            alt=""
            style="width: 16px; vertical-align: middle; margin-right: 3px"
          />高级筛选</span
        >

        <el-input
          v-model="formInlineScan.keyword"
          @keyup.enter.native="highCheckScan('')"
          clearable
          placeholder="请输入关键字检索"
          v-if="$route.path == '/scanReg'"
          id="account_keycheck"
        >
          <el-button slot="append" icon="el-icon-search" @click="highCheckScan('')"></el-button>
          <el-tooltip
            slot="prepend"
            class="item"
            effect="dark"
            content="支持检索字段：IP地址、网站标题、域名、企业名称"
            placement="top"
            :open-delay="100"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-input>
        <span
          @click="highCheckDialogScan = true"
          v-if="$route.path == '/scanReg'"
          id="account_filter"
          style="width: 80px"
          ><img
            src="../../assets/images/filter.png"
            alt=""
            style="width: 16px; vertical-align: middle; margin-right: 3px"
          />高级筛选</span
        >
      </div>
      <div>
        <el-checkbox
          v-if="$route.path != '/riskAssets'"
          class="checkboxAll"
          v-model="checkedAll"
          @change="checkAllChange"
          id="account_all"
          >选择全部</el-checkbox
        >

        <el-button
          v-if="$route.path == '/scanReg'"
          class="normalBtn"
          type="primary"
          :loading="scanBtnLoading"
          @click="scanIp()"
          >扫描入账IP资产</el-button
        >
        <el-button
          v-if="$route.path != '/scanReg' && $route.path != '/riskAssets'"
          class="normalBtnRe"
          type="primary"
          @click="removeMore"
          id="account_del"
          >删除</el-button
        >
        <el-button
          v-if="$route.path == '/assetsLedger'"
          class="normalBtnRe"
          type="primary"
          :disabled="btnLoading"
          @click="importScan"
          id="account_import"
          >导入并扫描</el-button
        >
        <!-- 疑似资产，威胁资产，台账的导入只有安服自己才有 -->
        <el-button
          v-if="
            $route.path == '/unclaimCloud' ||
            $route.path == '/threatAssets' ||
            ($route.path == '/assetsLedger' && user.role == 2)
          "
          class="normalBtnRe"
          type="primary"
          :disabled="btnLoading"
          @click="importTz"
          id="account_import"
          >导入
        </el-button>
        <!-- <el-dropdown class="dropdownClass import" v-if="$route.path == '/unclaimCloud' || $route.path == '/threatAssets' || ($route.path == '/assetsLedger' && user.role == 2)" trigger="click">
          <el-button class="normalBtnRe" type="primary"  :disabled="btnLoading" id="account_import">导入<i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="importTz" id="account_deal_place">文件导入</el-dropdown-item>
            <el-dropdown-item @click.native="importByHandle" id="account_deal_place">手动导入</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->
        <el-button
          v-if="$route.path == '/assetsLedger'"
          class="normalBtnRe"
          type="primary"
          :loading="exportLoadingBtn"
          :disabled="exportLoadingBtn"
          @click="exportList"
          id="account_export"
          >导出</el-button
        >
        <el-button
          v-if="
            $route.path == '/unclaimCloud' ||
            $route.path == '/ignoreAssets' ||
            $route.path == '/threatAssets' ||
            $route.path == '/scanReg'
          "
          class="normalBtnRe"
          :loading="exportLoadingBtn"
          :disabled="exportLoadingBtn"
          type="primary"
          @click="exportAssets"
          id="account_export"
          >导出</el-button
        >
        <el-button
          v-if="$route.path == '/assetsLedger'"
          class="normalBtnRe"
          type="primary"
          @click="setDefaultTags('more')"
          id="account_default_tags"
          >自定义标签</el-button
        >

        <el-button
          v-if="$route.path == '/scanReg' && !$route.query.pageIocn"
          class="normalBtn"
          type="primary"
          @click="handleScanFun"
          id="account_scan"
          >执行扫描</el-button
        >
        <el-tooltip
          class="item"
          effect="dark"
          content="自动核对系统中已管理的资产，并标记未纳入管理的资产"
          placement="top"
        >
          <el-button
            v-if="$route.path == '/assetsLedger'"
            class="normalBtn"
            type="primary"
            @click="heduiTzFun"
            id="account_check"
            >核对台账</el-button
          >
        </el-tooltip>
        <el-button
          v-if="$route.path == '/assetsLedger' && second_confirm == '0'"
          class="normalBtn"
          type="primary"
          :loading="confirmLoading"
          @click="againComifrm(1)"
          id="account_confirm"
          >资产确权</el-button
        >
        <el-button
          v-if="$route.path == '/assetsLedger' && second_confirm == '1'"
          class="normalBtn"
          type="primary"
          :loading="confirmLoading"
          @click="againComifrm(0)"
          id="account_confirm"
          >取消确权</el-button
        >
        <el-button
          v-if="$route.path == '/unclaimCloud'"
          class="normalBtn"
          type="primary"
          @click="batchMatch"
          id="account_confirm"
          :loading="batchMatchLoading"
          >一键匹配威胁词库</el-button
        >
        <el-button
          v-if="$route.path == '/threatAssets'"
          class="normalBtn"
          type="primary"
          @click="goSign('wait', 3, '标记威胁')"
          >批量编辑威胁类型</el-button
        >

        <el-dropdown
          class="dropdownClass"
          trigger="click"
          v-if="
            $route.path == '/assetsLedger' ||
            $route.path == '/unclaimCloud' ||
            $route.path == '/ignoreAssets' ||
            $route.path == '/threatAssets'
          "
        >
          <el-button class="normalBtn" type="primary" id="account_deal"
            >资产处置<i class="el-icon-arrow-down el-icon--right"></i
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-if="
                $route.path == '/unclaimCloud' ||
                $route.path == '/ignoreAssets' ||
                $route.path == '/threatAssets'
              "
              @click.native="goSign('wait', 1, '认领资产')"
              id="account_deal_place"
              >认领资产</el-dropdown-item
            >
            <el-dropdown-item
              v-if="
                $route.path == '/assetsLedger' ||
                $route.path == '/ignoreAssets' ||
                $route.path == '/threatAssets'
              "
              @click.native="goSign('wait', 0, '标记疑似')"
              id="account_deal_place"
              >标记疑似</el-dropdown-item
            >
            <el-dropdown-item
              v-if="
                $route.path == '/assetsLedger' ||
                $route.path == '/unclaimCloud' ||
                $route.path == '/threatAssets'
              "
              @click.native="goSign('wait', 2, '忽略资产')"
              id="account_deal_ignore"
              >忽略资产</el-dropdown-item
            >
            <el-dropdown-item
              v-if="
                $route.path == '/assetsLedger' ||
                $route.path == '/unclaimCloud' ||
                $route.path == '/ignoreAssets'
              "
              @click.native="goSign('wait', 3, '标记威胁')"
              id="account_deal_threat"
              >标记威胁</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          class="normalBtn"
          type="primary"
          v-if="(user.role == 2 || user.role == 3) && $route.path == '/assetsLedger'"
          @click="editCompanyName('more')"
          >批量编辑</el-button
        >
        <template v-if="$route.path == '/assetsLedger' && user.role == 2">
          <el-tooltip
            class="item"
            effect="dark"
            content="针对所有的资产，去FOFA更新资产信息，更新后资产信息会更准确。 "
            placement="top"
          >
            <el-button class="normalBtn" type="primary" @click="updateAssetsInfoByFOFA"
              >去FOFA更新</el-button
            >
          </el-tooltip>
        </template>
      </div>
    </div>
    <!-- 高级筛选条件tabs--多个资产页面 -->
    <hightFilter
      ref="hightFilter"
      :assetsSourceList="assetsSourceList"
      :threaten_type_arr="threaten_type_arr"
      :formInline="filterCondition"
      :total="total"
      @highCheck="highCheckCancel"
      v-if="hightFilterShow == 1 && $route.path != '/scanReg'"
      style="padding: 0px 20px 16px 20px"
    ></hightFilter>
    <!-- 高级筛选条件tabs--推荐记录 -->
    <hightFilter
      :formInline="filterCondition"
      :total="total"
      @highCheck="highCheckCancelScan"
      v-if="hightFilterShow == 1 && $route.path == '/scanReg'"
      style="padding: 0px 20px 16px 20px"
    ></hightFilter>

    <div
      class="tableWrap"
      :class="{
        tableIndex: $route.path == '/index',
        tableWrap: hightFilterShow != 1,
        'tableWrap-with-filter': hightFilterShow == 1
      }"
      ref="tableWrap"
    >
      <!-- 推荐资产库暂时不要自定义 -->
      <customColumn
        v-if="
          $route.path != '/unitIndex' && $route.path != '/assetsCloud' && $route.path != '/index'
        "
        :dataList="tableDataList"
        :key="componentKey"
        :coumnId="columnId"
        @columnChecked="columnChecked"
      ></customColumn>
      <el-table
        border
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        :header-cell-style="{ background: '#F5F7FA', color: '#62666C' }"
        ref="eltable"
        height="100%"
        style="width: 100%"
      >
        <template slot="empty">
          <div class="emptyClass">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-kong"></use>
            </svg>
            <p v-if="has_data == 'false' && second_confirm != 0 && $route.path == '/assetsLedger'">
              资产台账目前无数据，可以<span @click="importTz">导入已知资产</span
              >，系统会自动为您进行扫描，也可以<span @click="$router.push('/assetsCloud')"
                >下发未知资产推荐任务</span
              >。
            </p>
            <p v-else>暂无数据</p>
          </div>
        </template>
        <el-table-column
          v-if="!($route.path == '/index')"
          type="selection"
          align="center"
          :reserve-selection="checkedAll"
          :show-overflow-tooltip="true"
          :selectable="handleSelectable"
          width="55"
        >
        </el-table-column>
        <el-table-column
          v-for="(item, itemIndex) in tableHeaderIsShow"
          :key="itemIndex"
          align="left"
          :prop="item.name"
          :label="item.label"
          :fixed="item.fixed"
          :min-width="item.minWidth"
        >
          <template slot="header">
            <template v-if="item.labelTip">
              <el-tooltip class="item" effect="dark" :content="item.labelTip" placement="top">
                <span>{{ item.label }} <i class="el-icon-info"></i></span>
              </el-tooltip>
            </template>
            <template v-else>
              {{ item.label }}
            </template>
          </template>
          <template slot-scope="scope">
            <!-- detailIs == 1 ，信息在一级,==2，需要循环detail数据 -->
            <div v-if="item.detailIs == 1" class="cell-other">
              <div
                v-if="item.name == 'clue_company_name'"
                style="padding-right: 12px !important; padding-left: 12px !important"
              >
                <el-tooltip
                  :open-delay="500"
                  v-if="scope.row[item.name] && scope.row[item.name].length != 0"
                  class="item"
                  effect="dark"
                  :content="String(getCompany(scope.row[item.name]).join('，'))"
                  placement="top"
                >
                  <p class="detail">
                    {{ getCompany(scope.row[item.name]).join('，') }}
                  </p>
                </el-tooltip>
                <p class="detail" v-else>-</p>
              </div>
              <!-- <div v-else-if="item.name == 'hosts'" style="padding: 0">
                <div v-if="scope.row[item.name] && scope.row[item.name].length != 0">
                  <p class="detail" v-for="(nameItem, nameIndex) in getExpandData(scope.row[item.name], scope.$index)" :key="nameIndex" style="padding-right: 12px !important;padding-left: 12px !important;" @click="getHost(nameItem)">
                    <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                      <span slot="content">{{$punyCode.toUnicode(String(nameItem))}}{{scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''}}</span>
                      <span>
                        <span :class="scope.row['host_reflect'] && scope.row['host_reflect'][nameItem] == true ? 'hostDetail hostDetailHas': 'hostDetail'">{{$punyCode.toUnicode(nameItem)}}</span>
                        <span v-if="scope.row['host_reflect'] && scope.row['host_reflect'][nameItem] == true" class="blueRadiusBorder">泛解析</span>
                      </span>
                    </el-tooltip>
                  </p>
                </div>
                <span style="padding-right: 12px;padding-left: 12px;" v-else>-</span>
              </div> -->
              <div v-else-if="item.name == 'threaten_type'">
                <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                  <span slot="content">{{
                    scope.row.threaten_type_name || getTheatTypeName(scope.row[item.name])
                  }}</span>
                  <span>{{
                    scope.row.threaten_type_name || getTheatTypeName(scope.row[item.name])
                  }}</span>
                </el-tooltip>
              </div>
              <div v-else-if="item.name == 'ip'">
                <span v-if="scope.row[item.name]" class="ipContent">
                  <!-- <span> -->
                  <!-- 折叠按钮：端口，域名，组件信息折叠 -->
                  <i
                    v-if="
                      ((scope.row.detail && scope.row.detail.length > 3) ||
                        (scope.row.rule_tags && scope.row.rule_tags.length > 3)) &&
                      !scope.row.isExpand
                    "
                    id="expandClass"
                    class="el-icon-plus"
                    @click="
                      getExpand(
                        scope.$index,
                        scope.row,
                        scope.row.id,
                        scope.row.port_list_total_page
                      )
                    "
                  ></i>
                  <i v-if="scope.row.isExpandLoading" class="el-icon-loading"></i>
                  <i
                    v-if="
                      ((scope.row.detail && scope.row.detail.length > 3) ||
                        (scope.row.rule_tags && scope.row.rule_tags.length > 3)) &&
                      scope.row.isExpand
                    "
                    id="expandClass"
                    class="el-icon-minus"
                    @click="getExpands(scope.$index, scope.row.id, scope.row.port_list_total_page)"
                  ></i>
                  <!-- </span> -->
                  <!-- 台账的IP渲染 -->
                  <template
                    v-if="
                      $route.path != '/scanReg' &&
                      $route.path != '/assetsCloud' &&
                      $route.path != '/unitIndex'
                    "
                  >
                    <span
                      style="
                        color: #409eff;
                        cursor: pointer;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      "
                    >
                      <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                        <span slot="content" style="position: relative">
                          <span
                            @click="infoCheck(scope.row.id)"
                            @dblclick="dblclick(scope.row['ip'])"
                            style="cursor: pointer"
                          >
                            <i v-if="scope.row.online_state == 1" class="greenStatus"></i>
                            <i v-else class="grayStatus"></i>
                            <span>{{ scope.row[item.name] }}</span>
                          </span>
                          <el-tooltip
                            effect="light"
                            class="item"
                            placement="top"
                            content="一键复制"
                            :open-delay="500"
                          >
                            <!-- position: absolute;right:-6px;top:0 -->
                            <i
                              class="el-icon-document-copy"
                              @click="copyTextText(scope.row['ip'])"
                              style="color: #2677ff; cursor: pointer; margin-left: 6px"
                            ></i>
                          </el-tooltip>
                        </span>
                        <span @click="infoCheck(scope.row.id)">{{ scope.row[item.name] }}</span>
                      </el-tooltip>
                    </span>
                    <span class="greenLine" v-if="scope.row.online_state == 1">在线</span>
                    <span class="grayLine" v-else>离线</span>
                  </template>
                  <!-- /scanReg 推荐记录详情 /assetsCloud 云端推荐-推荐资产库 /unitIndex 单位资产测绘-推荐资产库  的IP渲染 -->
                  <template v-else>
                    <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                      <span slot="content">
                        <span>{{ scope.row[item.name] }}</span>
                      </span>
                      <span>{{ scope.row[item.name] }}</span>
                    </el-tooltip>
                  </template>
                  <!-- 以下内容是公共内容；ip后面都要有CDN，证据链，云厂商 -->
                  <span v-if="scope.row['is_cdn']" class="originLine">CDN</span>
                  <el-tooltip effect="light" class="item" placement="top" content="">
                    <div slot="content"> 云厂商：{{ getCloudName(scope.row['cloud_name']) }} </div>
                    <span v-if="getCloudName(scope.row['cloud_name'])" class="originLine"
                      ><i class="el-icon-cloudy"></i
                    ></span>
                  </el-tooltip>
                  <span
                    class="blueLine"
                    v-if="
                      scope.row['chain_list'] &&
                      scope.row['chain_list'].length &&
                      scope.row['chain_list'].length > 0
                    "
                  >
                    <el-tooltip
                      placement="top"
                      :disabled="!scope.row['chain_list']"
                      class="item"
                      effect="light"
                      popper-class="chainClass"
                      :open-delay="500"
                    >
                      <div slot="content" style="position: relative">
                        <el-tooltip
                          effect="light"
                          class="item"
                          placement="top"
                          content="一键复制"
                          v-if="scope.row['chain_list'] && scope.row['chain_list'].length != 0"
                          :open-delay="500"
                        >
                          <i
                            class="el-icon-document-copy"
                            @click="copyClick(scope.row['chain_list'])"
                            style="
                              color: #2677ff;
                              cursor: pointer;
                              position: absolute;
                              right: -6px;
                              top: 0;
                            "
                          ></i>
                        </el-tooltip>
                        证据链:{{ scope.row['chain_list'].length }}条

                        <div v-for="(item, index) in scope.row['chain_list']" :key="index">
                          <span>{{ index + 1 }}、</span>
                          <span v-for="(v, i) in getChains(item)" :key="i">
                            <span v-if="v.type && v.type == 3">
                              <el-image
                                :src="
                                  v.content.includes('http') ? v.content : showSrcIp + v.content
                                "
                                alt=""
                              >
                                <div slot="error" class="image-slot">
                                  <i class="el-icon-picture-outline"></i>
                                </div>
                              </el-image>
                            </span>
                            <span v-else>{{ v.content }}</span>
                            <span
                              v-if="i < item.length - 1 && v"
                              class="el-icon-right iconRight"
                            ></span>
                          </span>
                        </div>
                      </div>
                      <span>
                        <img
                          src="../../assets/images/chain.svg"
                          alt=""
                          style="width: 12px; vertical-align: middle"
                        />
                      </span>
                    </el-tooltip>
                  </span>
                  <span v-if="$route.path == '/assetsLedger'">
                    <span class="grayLine" v-if="scope.row.is_shadow == 1">影子资产</span>
                  </span>

                  <span
                    v-if="
                      $route.path == '/assetsLedger' &&
                      scope.row.customer_tags &&
                      scope.row.customer_tags.length != 0
                    "
                    class="grayLine defaultTags"
                  >
                    <el-tooltip placement="top" :open-delay="500">
                      <span slot="content"
                        >自定义标签：{{
                          scope.row.customer_tags && scope.row.customer_tags.join('; ')
                        }}</span
                      >
                      <span class=""
                        >{{ scope.row.customer_tags[0] }}
                        <template v-if="scope.row.customer_tags.length > 1">...</template>
                      </span>
                    </el-tooltip>
                  </span>
                  <el-tooltip
                    placement="top"
                    :open-delay="500"
                    content="可点击查看以及编辑自定义标签"
                  >
                    <span
                      v-if="$route.path == '/assetsLedger'"
                      @click="setDefaultTags('one', scope.row)"
                      style="cursor: pointer; margin-left: 8px"
                    >
                      <svg class="icon svg-icon" aria-hidden="true">
                        <use xlink:href="#icon-tag"></use>
                      </svg>
                    </span>
                  </el-tooltip>
                </span>
                <span v-else>-</span>
              </div>
              <div
                v-else-if="item.name == 'tags'"
                class="tagBox"
                :class="{ indexTag: $route.path == '/index' }"
              >
                <!-- 当前数据已确权second_confirm == 1，或者只要包含扫描标签（用户扫描0、安服扫描1）就为绿色，其余情况为蓝色 -->
                <span
                  :class="
                    second_confirm == 1 ||
                    scope.row[item.name].includes(0) ||
                    scope.row[item.name].includes(1)
                      ? 'greenLine assetsTag'
                      : 'blueLine assetsTag'
                  "
                  v-for="tags in scope.row[item.name]"
                  :key="tags"
                >
                  <span
                    v-if="
                      second_confirm == 1 ||
                      scope.row[item.name].includes(0) ||
                      scope.row[item.name].includes(1)
                    "
                  >
                    <img
                      v-if="tags == 0 || tags == 1"
                      :class="{ indexImg: $route.path == '/index' }"
                      src="../../assets/images/scanTagGreen.png"
                      alt=""
                    />
                    <img
                      :class="{ indexImg: $route.path == '/index' }"
                      v-else
                      src="../../assets/images/recommendTagGreen.png"
                      alt=""
                    />
                  </span>
                  <img
                    :class="{ indexImg: $route.path == '/index' }"
                    v-else
                    src="../../assets/images/recommendTagBlue.png"
                    alt=""
                  />
                  {{ getTagsName(tags) }}
                </span>
              </div>
              <div
                v-else-if="item.name == 'rule_tags' && $route.path != '/index'"
                class="ruleItemBox"
              >
                <span
                  class="ruleItem"
                  @click="ruleCheck(ch.cn_product)"
                  v-for="ch in getExpandData(scope.row[item.name], scope.$index)"
                  :key="ch.rule_id"
                >
                  <img
                    :class="{ indexImg: $route.path == '/index' }"
                    :src="showimg(ch.cn_product).result ? showimg(ch.cn_product).url : ''"
                    alt=""
                    class="productImg"
                  />
                  <el-tooltip effect="light" class="item" placement="top" :content="ch.cn_product">
                    <span>{{ ch.cn_product }}</span>
                  </el-tooltip>
                </span>
                <el-popover
                  placement="top"
                  width="315"
                  style="padding-right: 0px !important; padding-left: 0px !important"
                  popper-class="rulePopover"
                  trigger="click"
                >
                  <div style="font-size: 12px">
                    <div @click="infoCheck(scope.row.id)" style="cursor: pointer">
                      <i :class="scope.row.online_state == 1 ? 'greenStatus' : 'grayStatus'"></i
                      >{{ scope.row['ip'] }}
                    </div>
                    <div class="myruleItemBox">
                      <span
                        class="myruleItem"
                        @click="ruleCheck(v.cn_product)"
                        v-for="(v, i) in scope.row[item.name]"
                        :key="i"
                      >
                        <img
                          :src="showimg(v.cn_product).result ? showimg(v.cn_product).url : ''"
                          alt=""
                          class="productImg"
                        />
                        {{ v.cn_product }}
                      </span>
                    </div>
                  </div>
                  <div
                    slot="reference"
                    v-if="
                      scope.row['rule_tags'] &&
                      scope.row['rule_tags'].length > 3 &&
                      scope.row.myPopoverFlag
                    "
                    class="ruleItemNum"
                  >
                    共{{ scope.row['rule_tags'] ? scope.row['rule_tags'].length : '' }}条
                  </div>
                </el-popover>
              </div>
              <div
                v-else-if="item.name == 'rule_tags' && $route.path == '/index'"
                class="ruleItemBox"
              >
                <span
                  class="ruleItem ruleItemm"
                  v-for="ch in getExpandData(scope.row[item.name], scope.$index)"
                  :key="ch.rule_id"
                >
                  <img
                    :class="{ indexImg: $route.path == '/index' }"
                    :src="showimg(ch.cn_product).result ? showimg(ch.cn_product).url : ''"
                    alt=""
                    class="productImg"
                  />
                  <el-tooltip effect="light" class="item" placement="top" :content="ch.cn_product">
                    <span>{{ ch.cn_product }}</span>
                  </el-tooltip>
                </span>
                <el-popover
                  placement="top"
                  width="315"
                  style="padding-right: 0px !important; padding-left: 0px !important"
                  popper-class="rulePopover"
                  trigger="click"
                >
                  <div style="font-size: 12px">
                    <div @click="infoCheck(scope.row.id)" style="cursor: pointer">
                      <i :class="scope.row.online_state == 1 ? 'greenStatus' : 'grayStatus'"></i
                      >{{ scope.row['ip'] }}
                    </div>
                    <div class="myruleItemBox">
                      <span
                        class="myruleItem"
                        @click="ruleCheck(v.cn_product)"
                        v-for="(v, i) in scope.row[item.name]"
                        :key="i"
                      >
                        <img
                          :src="showimg(v.cn_product).result ? showimg(v.cn_product).url : ''"
                          alt=""
                          class="productImg"
                        />
                        {{ v.cn_product }}
                      </span>
                    </div>
                  </div>
                  <div
                    slot="reference"
                    v-if="
                      scope.row['rule_tags'] &&
                      scope.row['rule_tags'].length > 3 &&
                      scope.row.myPopoverFlag
                    "
                    class="ruleItemNum"
                  >
                    共{{ scope.row['rule_tags'] ? scope.row['rule_tags'].length : '' }}条
                  </div>
                </el-popover>
              </div>
              <div v-else-if="item.name == 'lateast_parse_domain'" class="blockBox">
                <el-tooltip
                  v-if="scope.row[item.name]"
                  class="item"
                  effect="dark"
                  :content="String(scope.row[item.name])"
                  placement="top"
                  :open-delay="500"
                >
                  <span class="ellipsis">{{ scope.row[item.name] }}</span>
                </el-tooltip>
                <span v-else>-</span>
                <el-tooltip
                  v-if="scope.row.ip_match && scope.row.ip_match == 2"
                  class="item"
                  effect="dark"
                  content="最新域名解析与当前IP资产不一致"
                  placement="top"
                  :open-delay="500"
                >
                  <div style="" class="redBlock first">IP不匹配</div>
                </el-tooltip>
                <el-tooltip
                  v-if="scope.row.company_match && scope.row.company_match == 2"
                  class="item"
                  effect="dark"
                  content="最新域名备案企业与当前资产企业不一致"
                  placement="top"
                  :open-delay="500"
                >
                  <div style="" class="redBlock second">企业不匹配</div>
                </el-tooltip>
              </div>
              <div v-else>
                <el-tooltip
                  v-if="scope.row[item.name]"
                  class="item"
                  effect="dark"
                  :content="String(scope.row[item.name])"
                  placement="top"
                  :open-delay="500"
                >
                  <span>{{ scope.row[item.name] }}</span>
                </el-tooltip>
                <span v-else>-</span>
              </div>
            </div>
            <!-- 拆分单元格，渲染detail 的数据 -->
            <div v-else>
              <!-- style="padding-right: 8px ;padding-left: 12px;"  -->
              <div
                class="detail"
                :class="{
                  detailThree:
                    $route.path != '/scanReg' &&
                    (item.name == 'title' ||
                      item.name == 'url' ||
                      item.name == 'hosts' ||
                      item.name == 'assets_source' ||
                      item.name == 'domain' ||
                      item.name == 'http_status_code') &&
                    $route.path != '/assetsCloud' &&
                    $route.path != '/unitIndex'
                }"
                v-for="(detailItem, detailindex) in getExpandData(
                  scope.row.detailPort,
                  scope.$index
                )"
                :key="detailindex"
                style="padding-right: 8px; padding-left: 12px"
                :style="{
                  height: `${
                    !detailItem.hostList || detailItem.hostList.length <= 1
                      ? 40
                      : detailItem.hostList.length * 40
                  }px`
                }"
              >
                <div
                  v-if="
                    item.name == 'url' &&
                    $route.path != '/assetsCloud' &&
                    $route.path != '/unitIndex' &&
                    $route.path != '/scanReg'
                  "
                >
                  <div
                    v-if="detailItem.hostList && detailItem.hostList.length == 0"
                    class="detailThreeItem"
                  >
                    <el-tooltip
                      :open-delay="500"
                      effect="dark"
                      :content="detailItem[item.name] ? String(detailItem[item.name]) : '-'"
                      placement="top"
                    >
                      <div class="portBox">
                        <a
                          class="ellipsis"
                          v-if="
                            detailItem[item.name] &&
                            (String(detailItem[item.name]).includes('http') ||
                              ((detailItem.protocol == 'tls' || detailItem.protocol == 'unknown') &&
                                (/[a-zA-Z]+/.test(detailItem[item.name]) ||
                                  String(detailItem[item.name]).includes(':'))))
                          "
                          style="color: #409eff"
                          :href="
                            detailItem[item.name].includes('http')
                              ? detailItem[item.name]
                              : `http://${detailItem[item.name]}`
                          "
                          target="_blank"
                          >{{ detailItem[item.name] }}</a
                        >
                        <p class="ellipsis" v-else>
                          {{ detailItem[item.name] ? detailItem[item.name] : '-' }}
                        </p>
                      </div>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    <div
                      v-for="(itemDetailOne, i) in detailItem.hostList"
                      :key="i"
                      class="detailThreeItem"
                    >
                      <el-tooltip
                        :open-delay="500"
                        effect="dark"
                        :content="itemDetailOne[item.name] ? String(itemDetailOne[item.name]) : '-'"
                        placement="top"
                      >
                        <div class="portBox" :tabindex="scope.$index">
                          <a
                            class="ellipsis"
                            v-if="
                              itemDetailOne[item.name] &&
                              (String(itemDetailOne[item.name]).includes('http') ||
                                ((itemDetailOne.protocol == 'tls' ||
                                  itemDetailOne.protocol == 'unknown') &&
                                  (/[a-zA-Z]+/.test(itemDetailOne[item.name]) ||
                                    String(itemDetailOne[item.name]).includes(':'))))
                            "
                            style="color: #409eff"
                            :href="
                              itemDetailOne[item.name].includes('http')
                                ? itemDetailOne[item.name]
                                : `http://${itemDetailOne[item.name]}`
                            "
                            target="_blank"
                            >{{ itemDetailOne[item.name] }}</a
                          >
                          <p class="ellipsis" v-else>{{
                            itemDetailOne[item.name] ? itemDetailOne[item.name] : '-'
                          }}</p>
                          <span
                            class="delBtn"
                            v-if="detailItem.hostList.length != 1"
                            @click="
                              deleteAssetsURL(scope.row, itemDetailOne[item.name], detailItem)
                            "
                            style="cursor: pointer"
                            ><i class="el-icon-close"></i
                          ></span>
                          <!-- <span
                            class="tagName"
                            @click="setTag(scope.row, itemDetailOne, detailItem)"
                            style="cursor: pointer; margin-left: 8px"
                          >
                            <svg class="icon svg-icon" aria-hidden="true">
                              <use xlink:href="#icon-tag"></use>
                            </svg>
                          </span> -->
                        </div>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
                <div
                  v-else-if="
                    item.name == 'url' &&
                    ($route.path == '/scanReg' ||
                      $route.path == '/assetsCloud' ||
                      $route.path == '/unitIndex')
                  "
                >
                  <el-tooltip
                    :open-delay="500"
                    effect="dark"
                    :content="detailItem[item.name] ? String(detailItem[item.name]) : '-'"
                    placement="top"
                  >
                    <div class="portBox">
                      <a
                        class="ellipsis"
                        v-if="
                          detailItem[item.name] && String(detailItem[item.name]).includes('http')
                        "
                        style="color: #409eff"
                        :href="detailItem[item.name]"
                        target="_blank"
                        >{{ detailItem[item.name] }}</a
                      >
                      <p class="ellipsis" v-else>
                        {{ detailItem[item.name] ? detailItem[item.name] : '-' }}
                      </p>
                    </div>
                  </el-tooltip>
                </div>

                <!-- <div v-else-if="item.name == 'http_status_code'" class="portBox">
                  <el-tooltip :open-delay="500" class="item"  effect="dark" :content="detailItem[item.name] ? String(detailItem[item.name]) : '-'" placement="top">
                    <span v-if="detailItem[item.name]">
                      <i class="greenStatus" v-if="detailItem[item.name] == 200"></i>
                      <i class="redStatus" v-else></i>
                      {{detailItem[item.name]}}
                    </span>
                    <span v-else>{{'-'}}</span>
                  </el-tooltip>
                </div> -->
                <div v-else-if="item.name == 'http_status_code'">
                  <div
                    v-if="detailItem.hostList && detailItem.hostList.length == 0"
                    class="detailThreeItem"
                  >
                    <el-tooltip
                      :open-delay="500"
                      effect="dark"
                      :content="detailItem[item.name] ? String(detailItem[item.name]) : '-'"
                      placement="top"
                    >
                      <span v-if="detailItem[item.name]">
                        <i class="greenStatus" v-if="detailItem[item.name] == 200"></i>
                        <i class="redStatus" v-else></i>
                        {{ detailItem[item.name] }}
                      </span>
                      <span v-else>{{ '-' }}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    <div
                      v-for="(itemDetailOne, i) in detailItem.hostList"
                      :key="i"
                      class="detailThreeItem"
                    >
                      <el-tooltip
                        :open-delay="500"
                        effect="dark"
                        :content="itemDetailOne[item.name] ? String(itemDetailOne[item.name]) : '-'"
                        placement="top"
                      >
                        <span v-if="itemDetailOne[item.name]">
                          <i class="greenStatus" v-if="itemDetailOne[item.name] == 200"></i>
                          <i class="redStatus" v-else></i>
                          {{ itemDetailOne[item.name] }}
                        </span>
                        <span v-else>{{ '-' }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
                <div
                  v-else-if="
                    item.name == 'title' &&
                    ($route.path == '/scanReg' ||
                      $route.path == '/assetsCloud' ||
                      $route.path == '/unitIndex')
                  "
                  @click="
                    () => {
                      noteBlackList(detailItem.title)
                    }
                  "
                  class="detailThreeItemSpecial clickPointer"
                >
                  <el-image
                    v-if="detailItem['logo'] && detailItem['logo']['content']"
                    :src="
                      detailItem['logo']['content'].includes('http')
                        ? detailItem['logo']['content']
                        : showSrcIp + detailItem['logo']['content']
                    "
                    lazy
                  >
                    <div slot="error">
                      <i class="el-icon-picture-outline" style="font-size: 20px"></i>
                    </div>
                  </el-image>
                  <el-tooltip
                    :open-delay="500"
                    effect="dark"
                    :content="detailItem[item.name] ? String(detailItem[item.name]) : '-'"
                    placement="top"
                  >
                    <p style="max-width: 77%" v-if="detailItem[item.name]">
                      {{ detailItem[item.name] }}
                    </p>
                    <span v-else>{{ '-' }}</span>
                  </el-tooltip>
                </div>
                <div v-else-if="item.name == 'title'">
                  <div
                    v-if="detailItem.hostList && detailItem.hostList.length == 0"
                    class="detailThreeItem clickPointer"
                    @click="
                      () => {
                        noteBlackList(detailItem.title)
                      }
                    "
                  >
                    <el-image
                      v-if="detailItem['logo'] && detailItem['logo']['content']"
                      :src="
                        detailItem['logo']['content'].includes('http')
                          ? imgUrl + detailItem['logo']['content']
                          : showSrcIp + detailItem['logo']['content']
                      "
                      lazy
                    >
                      <div slot="error">
                        <i class="el-icon-picture-outline" style="font-size: 20px"></i>
                      </div>
                    </el-image>
                    <el-tooltip
                      :open-delay="500"
                      effect="dark"
                      :content="detailItem[item.name] ? String(detailItem[item.name]) : '-'"
                      placement="top"
                    >
                      <p style="max-width: 77%" v-if="detailItem[item.name]">
                        {{ detailItem[item.name] }}
                      </p>
                      <span v-else>{{ '-' }}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    <div
                      v-for="(itemDetailOne, i) in detailItem.hostList"
                      :key="i"
                      class="detailThreeItem clickPointer"
                      @click="
                        () => {
                          noteBlackList(itemDetailOne.title)
                        }
                      "
                    >
                      <el-image
                        v-if="itemDetailOne['logo'] && itemDetailOne['logo']['content']"
                        :src="
                          itemDetailOne['logo']['content'].includes('http')
                            ? imgUrl + itemDetailOne['logo']['content']
                            : showSrcIp + itemDetailOne['logo']['content']
                        "
                        lazy
                      >
                        <div slot="error">
                          <i class="el-icon-picture-outline" style="font-size: 20px"></i>
                        </div>
                      </el-image>
                      <el-tooltip
                        :open-delay="500"
                        effect="dark"
                        :content="itemDetailOne[item.name] ? String(itemDetailOne[item.name]) : '-'"
                        placement="top"
                      >
                        <p style="max-width: 77%" v-if="itemDetailOne[item.name]">
                          {{ itemDetailOne[item.name] }}
                        </p>
                        <span v-else>{{ '-' }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
                <p v-else-if="item.name == 'logo'">
                  <el-image
                    v-if="detailItem[item.name] && detailItem[item.name].content"
                    :src="
                      String(detailItem[item.name].content).includes('http')
                        ? detailItem[item.name].content
                        : showSrcIp + detailItem[item.name].content
                    "
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                  <span v-else>-</span>
                </p>
                <div v-else-if="item.name == 'screenshot'">
                  <el-image
                    v-if="scope.row['screenshot']"
                    :preview-src-list="[
                      scope.row['screenshot'].includes('http')
                        ? scope.row['screenshot']
                        : showSrcIp + scope.row['screenshot']
                    ]"
                    :src="
                      scope.row['screenshot'].includes('http')
                        ? scope.row['screenshot']
                        : showSrcIp + scope.row['screenshot']
                    "
                    lazy
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                  <span v-else>-</span>
                </div>
                <div v-else-if="item.name == 'port'" class="portBox">
                  <i
                    v-if="
                      detailItem.hostList &&
                      detailItem.hostListLength > 3 &&
                      !detailItem.isURLExpand
                    "
                    id="expandClass"
                    class="el-icon-plus"
                    @click="getExpandUrl(scope.$index, detailindex, true)"
                  ></i>
                  <i
                    v-if="
                      detailItem.hostList && detailItem.hostListLength > 3 && detailItem.isURLExpand
                    "
                    id="expandClass"
                    class="el-icon-minus"
                    @click="getExpandUrl(scope.$index, detailindex, false)"
                  ></i>
                  <template
                    v-if="
                      $route.path != '/scanReg' &&
                      $route.path != '/assetsCloud' &&
                      $route.path != '/unitIndex'
                    "
                  >
                    <el-tooltip
                      :open-delay="500"
                      class="item"
                      effect="dark"
                      :content="detailItem.is_open == 1 ? '在线' : '离线'"
                      placement="top"
                    >
                      <i class="greenStatus" v-if="detailItem.is_open == 1"></i>
                      <i class="redStatus" v-else></i>
                    </el-tooltip>
                  </template>

                  <span>{{ getTableItem(detailItem[item.name]) }}</span>
                  <!-- 推荐资产库显示数据来源，assets_from： 1 时显示hunter,只有安服有权限-->
                  <span
                    v-if="
                      ($route.path == '/scanReg' ||
                        $route.path == '/unitIndex' ||
                        $route.path == '/assetsCloud') &&
                      detailItem['assets_from'] == 1 &&
                      user.role == 2
                    "
                    style="color: #ff6700; font-weight: bold; margin-left: 5px"
                    >Hunter</span
                  >
                  <i
                    @click="deleteAssets(scope.row.id, detailItem[item.name], scope.row.detail)"
                    class="el-icon-close deleteClass"
                    v-if="
                      $route.path != '/scanReg' &&
                      $route.path != '/assetsCloud' &&
                      $route.path != '/unitIndex'
                    "
                  ></i>
                </div>
                <div v-else-if="item.name == 'protocol'" class="portBox">
                  <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                    <span slot="content">{{
                      $punyCode.toUnicode(getTableItem(detailItem[item.name]))
                    }}</span>
                    <p>
                      {{ $punyCode.toUnicode(getTableItem(detailItem[item.name])) }}
                    </p>
                  </el-tooltip>
                </div>
                <!-- 安服显示数据来源 -->
                <div v-else-if="item.name == 'assets_source'">
                  <div
                    v-if="
                      !detailItem.hostList ||
                      (detailItem.hostList && detailItem.hostList.length == 0)
                    "
                    class="detailThreeItem"
                  >
                    <el-tooltip
                      v-if="detailItem['assets_source']"
                      :open-delay="500"
                      effect="dark"
                      placement="top"
                    >
                      <span slot="content">
                        {{
                          detailItem.assets_source
                            ? getAssetsSource[detailItem.assets_source]
                            : '-'
                        }}{{
                          detailItem['source_updated_at']
                            ? `（最新发现时间：${detailItem.source_updated_at}）`
                            : ''
                        }}{{
                          (detailItem.assets_source == '5' || detailItem.assets_source == '7') &&
                          detailItem.assets_source_domain
                            ? `(由${
                                detailItem.assets_source_domain
                                  ? detailItem.assets_source_domain
                                  : '---'
                              }实时解析出)`
                            : ''
                        }}
                      </span>
                      <p>{{
                        detailItem.assets_source ? getAssetsSource[detailItem.assets_source] : '-'
                      }}</p>
                    </el-tooltip>
                    <span v-else>-</span>
                  </div>
                  <div v-else>
                    <div
                      v-for="(itemDetailOne, i) in detailItem.hostList"
                      :key="i"
                      class="detailThreeItem"
                    >
                      <el-tooltip
                        v-if="itemDetailOne['assets_source']"
                        :open-delay="500"
                        effect="dark"
                        placement="top"
                      >
                        <span slot="content">
                          {{
                            detailItem.assets_source
                              ? getAssetsSource[detailItem.assets_source]
                              : '-'
                          }}{{
                            itemDetailOne['source_updated_at']
                              ? `（最新发现时间：${itemDetailOne['source_updated_at']}）`
                              : ''
                          }}{{
                            (itemDetailOne['assets_source'] == '5' ||
                              itemDetailOne['assets_source'] == '7') &&
                            itemDetailOne['assets_source_domain']
                              ? `(由${
                                  itemDetailOne['assets_source_domain']
                                    ? itemDetailOne['assets_source_domain']
                                    : '---'
                                }实时解析出)`
                              : ''
                          }}
                        </span>
                        <p>
                          {{
                            detailItem.assets_source
                              ? getAssetsSource[detailItem.assets_source]
                              : '-'
                          }}
                        </p>
                      </el-tooltip>
                      <span v-else>-</span>
                    </div>
                  </div>
                </div>
                <div v-else-if="item.name == 'reason'">
                  <el-tooltip
                    :transition="'0'"
                    v-if="detailItem[item.name]"
                    class="item"
                    effect="dark"
                    :content="detailItem[item.name]"
                    placement="top"
                    :open-delay="500"
                  >
                    <div v-html="getReason(detailItem[item.name])" class="ellipsis"></div>
                  </el-tooltip>
                  <span v-else>-</span>
                </div>
                <!-- 推荐记录资产分类 -->
                <div v-else-if="item.name == 'ip_status'">
                  <el-tooltip
                    :transition="'0'"
                    v-if="String(detailItem[item.name])"
                    class="item"
                    effect="dark"
                    :content="getAssetsType(detailItem[item.name])"
                    placement="top"
                    :open-delay="500"
                  >
                    <div class="ellipsis">
                      {{ getAssetsType(detailItem[item.name]) }}
                    </div>
                  </el-tooltip>
                  <span v-else>-</span>
                </div>
                <div v-else-if="item.name == 'hosts'">
                  <div
                    v-if="detailItem.hostList && detailItem.hostList.length == 0"
                    class="detailThreeItem"
                  >
                    <div v-if="detailItem.subdomain">
                      <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                        <span slot="content"
                          >{{ $punyCode.toUnicode(String(detailItem.subdomain))
                          }}{{
                            scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                          }}</span
                        >
                        <span>
                          <span
                            :class="
                              scope.row['host_reflect'] &&
                              scope.row['host_reflect'][detailItem.subdomain] == true
                                ? 'hostDetail hostDetailHas'
                                : 'hostDetail'
                            "
                            >{{ $punyCode.toUnicode(detailItem.subdomain)
                            }}<span>{{
                              detailItem['source_updated_at']
                                ? `（最新发现时间：${detailItem['source_updated_at']}）`
                                : ''
                            }}</span></span
                          >
                          <span
                            v-if="
                              scope.row['host_reflect'] &&
                              scope.row['host_reflect'][detailItem.subdomain] == true
                            "
                            class="blueRadiusBorder"
                            >泛解析</span
                          >
                          <span>{{
                            detailItem['source_updated_at']
                              ? `（最新发现时间：${detailItem['source_updated_at']}）`
                              : ''
                          }}</span>
                        </span>
                      </el-tooltip>
                    </div>
                    <span style="padding-right: 12px; padding-left: 12px" v-else>-</span>
                  </div>
                  <div v-else>
                    <div
                      v-for="(itemDetailOne, i) in detailItem.hostList"
                      :key="i"
                      class="detailThreeItem"
                    >
                      <div v-if="itemDetailOne.subdomain">
                        <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                          <span slot="content"
                            >{{ $punyCode.toUnicode(String(itemDetailOne.subdomain))
                            }}{{
                              scope.row.punycode_domain
                                ? '(' + scope.row.punycode_domain + ')'
                                : ''
                            }}<span>{{
                              itemDetailOne['source_updated_at']
                                ? `（最新发现时间：${itemDetailOne['source_updated_at']}）`
                                : ''
                            }}</span></span
                          >
                          <span>
                            <span
                              :class="
                                scope.row['host_reflect'] &&
                                scope.row['host_reflect'][itemDetailOne.subdomain] == true
                                  ? 'hostDetail hostDetailHas'
                                  : 'hostDetail'
                              "
                              >{{ $punyCode.toUnicode(itemDetailOne.subdomain) }}</span
                            >
                            <span
                              v-if="
                                scope.row['host_reflect'] &&
                                scope.row['host_reflect'][itemDetailOne.subdomain] == true
                              "
                              class="blueRadiusBorder"
                              >泛解析</span
                            >
                            <span>{{
                              itemDetailOne['source_updated_at']
                                ? `（最新发现时间：${itemDetailOne['source_updated_at']}）`
                                : ''
                            }}</span>
                          </span>
                        </el-tooltip>
                      </div>
                      <span style="padding-right: 12px; padding-left: 12px" v-else>-</span>
                    </div>
                  </div>
                </div>
                <div v-else-if="item.name == 'domain'">
                  <div
                    v-if="
                      (detailItem.hostList && detailItem.hostList.length == 0) ||
                      !detailItem.hostList
                    "
                    class="detailThreeItem"
                  >
                    <div v-if="detailItem.domain">
                      <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                        <span slot="content">{{
                          $punyCode.toUnicode(String(detailItem.domain))
                        }}</span>
                        <span>
                          <span>{{ $punyCode.toUnicode(detailItem.domain) }}</span>
                        </span>
                      </el-tooltip>
                    </div>
                    <span style="padding-right: 12px; padding-left: 12px" v-else>-</span>
                  </div>
                  <div v-else>
                    <div
                      v-for="(itemDetailOne, i) in detailItem.hostList"
                      :key="i"
                      class="detailThreeItem"
                    >
                      <div v-if="itemDetailOne.domain">
                        <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                          <span slot="content"
                            >{{ $punyCode.toUnicode(String(itemDetailOne.domain)) }}
                          </span>
                          <span>
                            {{ $punyCode.toUnicode(String(itemDetailOne.domain)) }}
                          </span>
                        </el-tooltip>
                      </div>
                      <span style="padding-right: 12px; padding-left: 12px" v-else>-</span>
                    </div>
                  </div>
                </div>
                <div v-else-if="item.name == 'assets_confidence_level'">
                  <el-tooltip
                    v-if="detailItem['assets_confidence_level']"
                    class="item"
                    effect="dark"
                    placement="top"
                    :open-delay="500"
                  >
                    <span slot="content">{{ levelDataMap[detailItem[item.name]] }}</span>
                    <span>{{ levelDataMap[detailItem[item.name]] }}</span>
                  </el-tooltip>
                  <span v-else>-</span>
                </div>
                <div
                  v-else-if="
                    item.name != 'url' && item.name != 'http_status_code' && item.name != 'title'
                  "
                >
                  <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                    <span slot="content">{{
                      $punyCode.toUnicode(getTableItem(detailItem[item.name]))
                    }}</span>
                    <p>
                      {{ $punyCode.toUnicode(getTableItem(detailItem[item.name])) }}
                    </p>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      v-if="!($route.path == '/index')"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="pageSizeArr"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
    >
    </el-pagination>
    <!-- <span style="position: absolute; left: 0; top: 0">已选择{{checkedAll ? total : checkedArr.length}}条</span> -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleHedui"
      width="400px"
    >
      <template slot="title">
        <span v-if="isHeduiDialog">核对台账</span>
        <span v-if="!isHeduiDialog">导入</span>
      </template>
      <div class="dialog-body">
        <p class="downloadClass" @click="downloadForbidIpsHd('')">
          <i class="el-icon-warning"></i>请点击下载
          <span v-if="isHeduiDialog">核对模板</span>
          <span v-if="!isHeduiDialog">资产导入模板.xlsx</span>
        </p>
        <p v-if="!isHeduiDialog" class="downloadClass" @click="downloadForbidIpsHd('csv')">
          <i class="el-icon-warning"></i>请点击下载
          <span>资产导入模板.csv</span>
        </p>
        <el-upload
          class="upload-demo"
          drag
          :action="uploadActionHd"
          :headers="uploadHeaders"
          accept=".xlsx,.csv"
          :before-upload="beforeIpUpload"
          :on-success="ipUploadSuccessHd"
          :on-remove="uploadRemoveHd"
          :on-change="uploadChange"
          :on-error="ipUploadErrorHd"
          :limit="uploadMaxCount"
          :on-exceed="handleExceedHd"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip"> 支持上传xlsx,csv 文件，且大小不超过20M </div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleHedui = false"
          id="account_check_cancel"
          >关闭</el-button
        >
        <el-button
          :loading="btnLoading"
          class="highBtn"
          @click="heduiTzFunSave"
          id="account_check_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleImport"
      width="950px"
    >
      <template slot="title"> 导入并扫描 </template>
      <div class="dialog-body importDialog" v-loading="portLoading">
        <div class="left">
          <el-form
            :model="importRuleForm"
            :rules="rules"
            style="padding: 0 !important"
            ref="addRuleForm"
            label-width="80px"
            class="demo-ruleForm"
          >
            <el-form-item label="IP类型" prop="ip_type">
              <el-select
                v-model="importRuleForm.ip_type"
                @change="iptype_change"
                placeholder="请选择"
              >
                <el-option label="IPV4" :value="1"></el-option>
                <el-option label="IPV6" :value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="扫描目标">
              <el-select v-model="source" placeholder="请选择">
                <el-option label="输入IP信息" value="input"></el-option>
                <el-option label="上传IP信息文件" value="file"></el-option>
                <el-option label="输入域名信息" value="domain"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="扫描端口">
              <el-select filterable v-model="port_group_ids" placeholder="请选择">
                <el-option
                  v-for="item in portGroupsNoPageArr"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  @click.native="myChange(item.name, item.id)"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="扫描带宽" prop="bandwidth">
              <newInputNumber
                class="inputNumber"
                :min="100"
                :max="5000"
                :step="100"
                v-model.number="importRuleForm.bandwidth"
                placeholder="大于100，小于5000的整数"
              >
                <template slot="append">kb</template>
              </newInputNumber>
              <!-- <p class="tishi">带宽设置高可以提高扫描速度，但是过高可能会影响到网络正常使用</p> -->
            </el-form-item>
            <el-form-item label="识别并发" prop="protocol_concurrency">
              <el-select
                filterable
                v-model="importRuleForm.protocol_concurrency"
                placeholder="请选择"
              >
                <el-option label="根据带宽动态分配协议识别并发数" :value="0" :key="0"></el-option>
                <el-option
                  v-for="item in protocol_concurrency_arr"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="扫描类型" prop="scan_type" style="margin-bottom: 0px !important">
              <el-select v-model="importRuleForm.scan_type" placeholder="请选择">
                <el-option
                  v-for="item in scan_type_arr"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label=""
              prop="ping_switch"
              style="margin-bottom: 0px !important; margin-top: 12px"
            >
              <el-checkbox v-model="importRuleForm.ping_switch">开启ping识别资产</el-checkbox>
            </el-form-item>
            <!-- <el-form-item  label="" prop="web_logo_switch">
              <el-checkbox v-model="importRuleForm.web_logo_switch">开启网站首页截图</el-checkbox>
            </el-form-item> -->
          </el-form>
        </div>
        <div class="right">
          <el-tabs v-if="port_group_ids == 0" v-model="activeNameScan">
            <el-tab-pane label="IP" name="first" v-if="source == 'input'">
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
              >
                <el-form-item label="" prop="" style="position: relative; height: 442px">
                  <el-input type="textarea" v-model="textarea" class="placeholderIdBox"></el-input>
                  <div class="placeholderId" v-show="!textarea">
                    <div></div>
                    <div>IP段支持格式如下</div>
                    <div>**********</div>
                    <div>**********-100</div>
                    <div>**********/24</div>
                    <div></div>
                    <!-- <div>多个连续网段支持格式：</div>
                    <div>
                      192.168.1-10.*（代表***********-************共10个网段）
                    </div> -->
                    <div>最多输入1000个ip，分号或换行分隔</div>
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="域名" name="first" v-else-if="source == 'domain'">
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
              >
                <el-form-item label="" prop="" style="position: relative; height: 442px">
                  <el-input type="textarea" v-model="textarea" class="placeholderIdBox"></el-input>
                  <div class="placeholderId" v-show="!textarea" style="margin-top: 15px">
                    <div>支持填写域名</div>
                    <div></div>
                    <div>域名格式如下：</div>
                    <div>foradar.baimaohui.net</div>
                    <div>最多输入200个，分号或换行分隔</div>
                    <div>
                      IP类型选择IPV4的话，域名就只解析IPV4的ip数据，同样，选择IPV6类型，域名就只解析IPV6格式的ip数据。
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="上传IP信息文件" name="first" v-else>
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
              >
                <p
                  class="downloadClass"
                  @click="downloadForbidIpsExcel"
                  style="margin: 0px 0px 16px 0px !important"
                >
                  <i class="el-icon-warning"></i>请点击下载
                  <span>IP导入模板</span>
                </p>
                <el-upload
                  class="upload-demo"
                  drag
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :before-upload="beforeIpUpload"
                  accept=".xlsx"
                  :on-success="ipUploadSuccess"
                  :on-remove="uploadRemove"
                  :on-error="ipUploadError"
                  :limit="uploadMaxCount"
                  :on-exceed="handleExceed"
                  :file-list="fileList"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text"> 将文件拖到此处，或<em>点击上传</em> </div>
                  <div class="el-upload__tip" slot="tip">
                    支持上传xlsx格式文件，且大小不超过20M
                  </div>
                </el-upload>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="端口/协议" name="second">
              <el-form ref="addRuleForm" label-width="10px" class="demo-ruleForm">
                <div style="margin-left: 10px; margin-top: 10px">端口号：</div>
                <el-form-item label="" prop="">
                  <el-input
                    type="textarea"
                    style="height: 60px"
                    v-model="define_ports"
                    placeholder="请输入端口号,多个值请使用分号或换行分隔，单次最多可以添加20个端口号。如果不输入端口，将默认扫描0-65535端口。"
                  ></el-input>
                </el-form-item>
              </el-form>
              <el-form ref="addRuleForm" label-width="10px" class="demo-ruleForm">
                <div style="margin-left: 10px; margin-top: 10px">协议：</div>
                <el-form-item label="" prop="define_port_protocols">
                  <new-transfer
                    :titles="['未选择', '已选择']"
                    v-loading="protocoldiaLoading"
                    filterable
                    :filter-method="filterMethodProtocol"
                    filter-placeholder="请输入关键字"
                    v-model="define_port_protocols"
                    :props="transferPropProtocol"
                    ref="reserve"
                    @left-check-change="handleChangeLeft"
                    @right-check-change="handleChangeRight"
                    :data="protocolTransferData"
                  >
                  </new-transfer>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
          <el-form
            :model="ruleForm"
            :rules="rules"
            ref="addRuleForm"
            label-width="10px"
            class="demo-ruleForm"
            v-else
          >
            <el-form-item label="" prop="" v-if="source == 'input'" style="position: relative">
              <el-input type="textarea" v-model="textarea" class="placeholderIdBox"></el-input>
              <div class="placeholderId" v-show="!textarea">
                <div></div>
                <div>IP段支持格式如下</div>
                <div>**********</div>
                <div>**********-100</div>
                <div>**********/24</div>
                <div></div>
                <!-- <div>多个连续网段支持格式：</div>
                <div>
                  192.168.1-10.*（代表***********-************共10个网段）
                </div> -->
                <div>最多输入1000个ip，分号或换行分隔</div>
              </div>
            </el-form-item>
            <el-form-item
              label=""
              prop=""
              v-else-if="source == 'domain'"
              style="position: relative"
            >
              <el-input type="textarea" v-model="textarea" class="placeholderIdBox"></el-input>
              <div class="placeholderId" v-show="!textarea" style="margin-top: 15px">
                <div>支持填写域名</div>
                <div></div>
                <div>域名格式如下：</div>
                <div>foradar.baimaohui.net</div>
                <div>最多输入200个，分号或换行分隔</div>
                <div>
                  IP类型选择IPV4的话，域名就只解析IPV4的ip数据，同样，选择IPV6类型，域名就只解析IPV6格式的ip数据。
                </div>
              </div>
            </el-form-item>
            <el-form-item v-else>
              <p
                class="downloadClass"
                @click="downloadForbidIpsExcel"
                style="margin: 0px 0px 16px 0px !important"
              >
                <i class="el-icon-warning"></i>请点击下载
                <span>IP导入模板</span>
              </p>
              <el-upload
                class="upload-demo"
                drag
                accept=".xlsx"
                :action="uploadAction"
                :headers="uploadHeaders"
                :before-upload="beforeIpUpload"
                :on-success="ipUploadSuccess"
                :on-change="uploadChange"
                :on-remove="uploadRemove"
                :on-error="ipUploadError"
                :limit="uploadMaxCount"
                :on-exceed="handleExceed"
                :file-list="fileList"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text"> 将文件拖到此处，或<em>点击上传</em> </div>
                <div class="el-upload__tip" slot="tip"> 支持上传xlsx格式文件，且大小不超过20M </div>
              </el-upload>
              <p style="color: #2677ff">上传的IP信息</p>
              <p
                class="uploadIps"
                v-html="
                  fileData.length > 0
                    ? fileData
                        .map((item) => {
                          return item.ip
                        })
                        .join('\n')
                    : ''
                "
              ></p>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleImport = false"
          id="account_import_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          :loading="btnLoading"
          @click="uploadSave"
          id="account_import_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 高级筛选抽屉表单 -->
    <highCheckDrawer
      :loading="checkDialogLoading"
      :user="user"
      :highCheckdialog="highCheckDialog"
      :threaten_type_arr="threaten_type_arr"
      :selectArr="selectArr"
      :formInlineOutput="formInline"
      @highCheck="highCheck"
      @highIsClose="highIsClose"
    />
    <!-- 高级筛选抽屉表单 推荐任务资产扫描核对高级筛选 -->
    <highCheckDrawerScan
      :highCheckdialog="highCheckDialogScan"
      :selectArr="cluesList"
      :formInline="formInlineScan"
      @highCheck="highCheckScan"
      @highIsClose="highIsCloseScan"
    />
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleScan"
      :width="isPortScan ? '950px' : '567px'"
    >
      <template slot="title"> 执行资产扫描 </template>
      <div class="dialog-body importDialogs">
        <div class="left">
          <el-form
            :model="ruleFormScan"
            style="padding: 0 !important"
            ref="ruleFormScan"
            label-width="80px"
            class="demo-ruleForm"
          >
            <el-form-item label="任务名称" prop="name">
              <el-input
                v-model="ruleFormScan.name"
                placeholder="请输入任务名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="扫描带宽" prop="bandwidth">
              <newInputNumber
                class="inputNumber"
                :min="100"
                :max="5000"
                :step="100"
                v-model.number="ruleFormScan.bandwidth"
                placeholder="大于100，小于5000的整数"
              >
                <template slot="append">kb</template>
              </newInputNumber>
              <!-- <p class="tishi">带宽设置高可以提高扫描速度，但是过高可能会影响到网络正常使用</p> -->
            </el-form-item>
            <el-form-item label="扫描端口" prop="port_group_ids">
              <el-select filterable v-model="ruleFormScan.port_group_ids" placeholder="请选择">
                <el-option
                  v-for="item in selectData.portGroupsNoPageArr"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  @click.native="changePort(item.id)"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="IP类型" prop="ip_type">
              <el-select
                v-model="ruleFormScan.ip_type"
                @change="iptype_change"
                placeholder="请选择"
              >
                <el-option label="IPV4" :value="1"></el-option>
                <el-option label="IPV6" :value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="扫描模式" prop="scan_type">
              <el-select v-model="ruleFormScan.scan_type" placeholder="请选择">
                <el-option
                  v-for="item in selectData.scan_type_arr"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="ping_switch">
              <el-checkbox v-model="ruleFormScan.ping_switch">开启ping识别资产</el-checkbox>
            </el-form-item>
            <!-- <el-form-item label="" prop="web_logo_switch">
              <el-checkbox v-model="ruleFormScan.web_logo_switch">开启网站首页截图</el-checkbox>
            </el-form-item> -->
          </el-form>
        </div>
        <!-- <p style="font-weight: 400;color: #62666C;padding-bottom: 8px">共计1个需要扫描的IP，预计扫描时间0天0小时5分</p> -->
        <div class="right" v-if="isPortScan">
          <el-form ref="addRuleForm" label-width="10px" class="demo-ruleForm">
            <div style="margin-left: 10px; margin-top: 10px">端口号：</div>
            <el-form-item label="" prop="">
              <el-input
                type="textarea"
                style="height: 120px"
                v-model="ruleFormScan.define_ports"
                placeholder="请输入端口号,多个值请使用分号或换行分隔，单次最多可以添加20个端口号。"
              ></el-input>
            </el-form-item>
          </el-form>
          <el-form ref="addRuleForm" label-width="10px" class="demo-ruleForm">
            <div style="margin-left: 10px; margin-top: 10px">协议：</div>
            <el-form-item label="" prop="define_port_protocols">
              <new-transfer
                :titles="['未选择', '已选择']"
                v-loading="protocoldiaLoadings"
                filterable
                :filter-method="filterMethodProtocols"
                filter-placeholder="请输入关键字"
                v-model="ruleFormScan.define_port_protocols"
                :props="transferPropProtocol"
                ref="reserve"
                @left-check-change="handleChangeLeft"
                @right-check-change="handleChangeRight"
                :data="protocolTransferDatas"
              >
              </new-transfer>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleScan = false" id="account_scan_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          :loading="btnLoadingScan"
          @click="handleScanFunSave"
          id="account_scan_sure"
          >开始扫描</el-button
        >
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" :visible.sync="labeledthreatVisible" width="567px">
      <template slot="title"> 标记威胁 </template>
      <div class="dialog-body importDialogs">
        <!-- <p style="font-weight: 400;color: #62666C;padding-bottom: 8px">共计1个需要扫描的IP，预计扫描时间0天0小时5分</p> -->
        <el-form ref="addRuleForm" label-width="80px" class="demo-ruleForm">
          <el-form-item label="威胁类型" prop="">
            <el-select filterable v-model="labeledValue" placeholder="请选择" value-key="id">
              <el-option
                v-for="item in threaten_type_arr"
                :key="item.id"
                :label="item.type_name"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="labeledthreatVisible = false" id="account_scan_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          :loading="labeledBtnLoading"
          @click="labeledBtnClick"
          id="account_scan_sure"
          >确认</el-button
        >
      </div>
    </el-dialog>
    <noteBlack
      :selectTitle="currentTitle"
      :visible="noteDialogVisible"
      ref=""
      @close="noteDialogVisible = false"
    />
    <assetsValidate
      :dialogVisible="assetsValidDialogVisible"
      :validateType="validateType"
      @copyText="copyText"
      :list="errorList"
      @close="assetsValidDialogVisible = false"
    />
    <prompt
      label="系统标签"
      title="标记系统标签"
      placeholder="请输入系统标签"
      :visible="newTagVisible"
      @save="onSubmitBtn"
      @close="newTagVisible = false"
      :loading="newTagLoading"
    />
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="defaultTagsVisible"
      width="567px"
      @close="closeDefaultTagDialog()"
    >
      <template slot="title">自定义标签</template>

      <div class="dialog-body tagDialogs">
        <div class="diaTip" v-if="currentTagsSetType == 'more'">
          <i class="el-icon-warning"></i> 此操作将覆盖所勾选项的已标记标签，请谨慎操作
        </div>
        <div class="dialog-item">
          <div class="label"> 自定义标签：</div>
          <div class="value">
            <tagsInput
              ref="tagsInput"
              :parentTags="parentTags"
              @onChange="changeDefaultTag"
            ></tagsInput>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="closeDefaultTagDialog()" id="account_scan_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          id="account_scan_sure"
          :loading="setTagLoading"
          @click="setDefaultTagsConfirm"
          >确认</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import prompt from '@/components/assets/prompt'

import assetsValidate from './assetsValidate.vue'
import sha1 from '@/utils/sha1Encrypt'
import { mapGetters, mapState, mapMutations } from 'vuex'
import highCheckDrawer from '../home_set/highCheckIpAssets.vue'
import highCheckDrawerScan from '../home_set/highCheck.vue'
import customColumn from '../../components/assets/customColumn.vue'
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import newTransfer from '../../components/transfer/src/main'
import hightFilter from '../../components/assets/hightFilter.vue'
import newInputNumber from '@/components/input-number/index'
import noteBlack from '@/components/assets/noteBlack.vue'
import tagsInput from './tagsInput.vue'
import {
  getConditionV1,
  getConditionV2,
  area,
  getScanList,
  delScanList,
  exportScanLis,
  couldIpListAll,
  exportCouldIpList,
  pgdTypeList,
  assetsBacthMatch,
  ipPortListData,
  updateDomainAssetsByFOFA
} from '@/api/apiConfig/api.js'
import { portGroupsNoPage, getPortProList } from '@/api/apiConfig/port.js'
import { pocRiskAssets } from '@/api/apiConfig/poc.js'
import { getAllTaskList } from '@/api/apiConfig/discovery.js'
import { handleScan, regRecommendClues } from '@/api/apiConfig/recommend.js'
import {
  editThreatenType,
  changeThreaten,
  deleteThreaten,
  exportThreaten,
  getThreatenList,
  filterNeglectList,
  changeNeglect,
  deleteNeglect,
  exportNeglect,
  filterSuspectedList,
  changeSuspected,
  deleteSuspected,
  exportSuspected,
  setBusinessTag,
  scanAssets,
  deleteAssetsURLV1,
  deleteAssetsV1,
  setAssetsDefaultTags,
  importKnownAssets,
  importSureIpDataV1,
  heduiKnownAssetsV1,
  ipAssetsHandle,
  ansysdataAgainConfirm,
  deleteStandingDataV1,
  ansysDataIndexV1,
  exportDataV1,
  tableIpCompanyName
} from '@/api/apiConfig/asset.js'
import { assetsSourceList } from '@/utils/commonData.js'
export default {
  components: {
    tagsInput,
    newInputNumber,
    highCheckDrawer,
    highCheckDrawerScan,
    newTransfer,
    hightFilter,
    customColumn,
    assetsValidate,
    noteBlack,
    prompt
  },
  props: {
    notifyFilterId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      checkDialogLoading: true,
      setTagLoading: false,
      parentTags: [],
      currentTagsSetType: '',
      currentRowId: {},
      defaultTags: [],
      defaultTagsVisible: false,
      imgUrl: '',
      exportLoadingBtn: false,
      // assetsSourceList: [
      //   { label: '-', value: '-' },
      //   { label: 'FOFA的search/all接口返回', value: 1 },
      //   { label: 'IP138', value: 2 },
      //   { label: 'chaziyu', value: 3 },
      //   { label: 'hunter', value: 4 },
      //   { label: '线索库域名实时解析', value: 5 },
      //   { label: 'FOFA纯解析域名数据', value: 6 },
      //   { label: '非线索库的域名实时解析', value: 7 },
      //   { label: '基础扫描', value: 8 },
      //   { label: '单位测绘任务下的全端口扫描', value: 9 },
      //   { label: '单位测绘下的常用端口扫描', value: 10 },
      //   { label: 'oneforall', value: 11 }
      // ],
      currentIndex: '',
      isShowBtn: false,
      tagParams: {
        id: '',
        ip: '',
        port: '',
        protocol: '',
        domain: '',
        url: '',
        system_name: '',
        company_name: ''
      },
      newTagVisible: false,
      newTagLoading: false,
      batchMatchLoading: false,
      clickTimer: null,
      threaten_type_arr: [],
      currentTitle: '',
      noteDialogVisible: false,
      validateType: '',
      correctList: [],
      errorList: [],
      assetsValidDialogVisible: false,
      scanBtnLoading: false,
      levelDataMap: {
        1: '高可信度',
        2: '中可信度',
        3: '低可信度'
      },
      // levelDataMap: {
      //   1:'A级',
      //   2:'B级',
      //   3:'C级',
      //   4:'D级',
      // },
      link: '',
      // 标记威胁弹窗
      labeledthreatVisible: false,
      labeledValue: {},
      labeledValueObj: {},
      labeledBtnLoading: false,
      labeledValueList: [
        // 钓鱼仿冒、ICP盗用、黄赌毒网站、其他类型
        {
          name: '其他类型',
          value: '0'
        },
        {
          name: '钓鱼仿冒',
          value: '1'
        },
        {
          name: '黄赌毒网站',
          value: '2'
        },
        {
          name: 'ICP盗用',
          value: '3'
        },
        {
          name: '域名混淆',
          value: '4'
        }
      ],
      isScanId: '', //0-65535端口的id
      hightFilterShow: 3,
      filterCondition: {}, //高级设置状态值
      has_data: '', // 台账不加筛选条件的总数据，用于区分表格为空时的提示信息（暂无数据）
      second_confirm: '',
      highCheckDialogScan: false,
      portGroupsNoPageArr: [], //扫描端口选择
      port_group_ids: '', //扫描端口
      namePort: '',
      source: 'input',
      textarea: '',
      dialogVisible: false, //控制编辑弹框
      selectArr: {},
      tableHeaderCopy: [],
      ruleForm: {
        ip: '',
        clue_company_name: '',
        port: '',
        protocol: '',
        subdomain: '',
        url: '',
        rule_infos: '',
        province: '',
        state: '',
        last_update_time: [],
        title: '',
        http_status_code: '',
        isp: '',
        asn: '',
        lat: '',
        lon: '',
        reason: ''
      },
      rules: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        ips: [{ required: true, message: '请输入或上传ip信息', trigger: 'change' }]
      },
      btnLoading: false,
      portLoading: false,
      confirmLoading: false,
      radioParams: {},
      user: {
        role: ''
      },
      identifier: '',
      regUrlArr: [], // 核对台账数组,导入
      uploadActionHd: `${this.uploadSrcIp}/assets/account/files?encode=0`,
      uploadHeaders: { Authorization: localStorage.getItem('token') },
      forbidIpsSelect: [],
      fileList: [],
      checkedArr: [],
      provinceData: [],
      stateData: [
        {
          name: '离线',
          value: 0
        },
        {
          name: '在线',
          value: 1
        }
      ],
      reasonData: [
        {
          name: '根域',
          value: 0
        },
        {
          name: '证书',
          value: 1
        },
        {
          name: 'ICP',
          value: 2
        },
        {
          name: 'ICON',
          value: 3
        },
        {
          name: '关键词',
          value: 4
        },
        {
          name: '已知资产IP',
          value: 6
        }
      ],
      fileData: [],
      uploadMaxCount: 1,
      checkedAll: false,
      highCheckDialog: false,
      dialogFormVisibleHedui: false,
      isHeduiDialog: false,
      dialogFormVisibleColumn: false,
      dialogFormVisibleImport: false,
      loading: false,
      warploading: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      formInline: {
        ip: '',
        clue_company_name: [],
        province: '', // 省份名称（传汉字）,
        keyword: '', // 123123,
        asn: '', // 123123,
        cert: '', // cert,
        icp: '', // icp,
        url: '',
        title: '', // title,
        protocol: '', // protocol,
        logo: '', // logo,
        port: '', // port,
        cname: '', // cname,
        domain: [], // domain
        latitude: '',
        longitude: '',
        state: '',
        reason: '',
        reason_type: '', // 证据链
        tags: '',
        hosts: '',
        rule_tags: [],
        last_update_time: [],
        second_confirm: '', // second_confirm  0/待确认 1/已经确认
        ip_match: '',
        company_match: '',
        not_in_clue_domain: [],
        customer_tags: null
      },
      formInlineScan: {
        ip: '',
        clue_company_name: [],
        province: '', // 省份名称（传汉字）,
        keyword: '', // 123123,
        asn: '', // 123123,
        icp: '', // icp,
        url: '',
        title: '', // title,
        protocol: '', // protocol,
        port: '', // port,
        cname: '', // cname,
        domain: '', // domain
        lat: '',
        lon: '',
        state: '',
        reason: '',
        score: '',
        updated_at: []
      },
      tongjiArr: [
        {
          id: 1,
          name: 'IP总数',
          num: 'ip',
          icon: '#icon-a-bianzu8'
        },
        {
          id: 2,
          name: '端口总数',
          num: 'portNum',
          icon: '#icon-a-bianzu81'
        },
        {
          id: 3,
          name: '组件总数',
          num: 'ruleNum',
          icon: '#icon-a-bianzu82'
        }
      ],
      autoColumn: [
        {
          id: 1,
          label: '一级 1'
        },
        {
          id: 2,
          label: '一级 2'
        },
        {
          id: 5,
          label: '二级 2-1'
        }
      ],
      tableDataList: [],
      componentKey: 0,
      tableHeader: [
        {
          label: 'IP地址',
          name: 'ip',
          icon: 'input',
          minWidth: '320',
          detailIs: 1,
          fixed: 'left',
          path: [
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/scanReg',
            '/assetsCloud',
            '/riskAssets',
            '/index'
          ]
        },
        {
          label: '威胁类型',
          name: 'threaten_type',
          minWidth: '90',
          detailIs: 1,
          path: ['/threatAssets']
        },
        {
          label: '端口',
          name: 'port',
          icon: 'input',
          minWidth: '90',
          detailIs: 2,
          path: [
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/scanReg',
            '/assetsCloud',
            '/index'
          ]
        },
        {
          label: '协议',
          name: 'protocol',
          icon: 'input',
          minWidth: '70',
          detailIs: 2,
          path: [
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/scanReg',
            '/assetsCloud',
            '/index'
          ]
        },

        {
          label: 'URL',
          name: 'url',
          icon: 'input',
          minWidth: '120',
          detailIs: 2,
          path: [
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/scanReg',
            '/assetsCloud',
            '/index'
          ]
        },
        // {
        //   label: '网站截图',
        //   name: 'screenshot',
        //   icon: 'input',
        //   minWidth: '80',
        //   detailIs: 2,
        //   path:['/threatAssets']
        // },
        {
          label: '网站标题',
          name: 'title',
          icon: 'select',
          minWidth: '120',
          detailIs: 2,
          path: [
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/scanReg',
            '/assetsCloud',
            '/index'
          ]
        },
        {
          label: '域名',
          name: 'hosts',
          icon: 'select',
          minWidth: '180',
          detailIs: 2,
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '主域名',
          name: 'domain',
          icon: 'select',
          minWidth: '120',
          detailIs: 2,
          path: ['/assetsLedger', '/unclaimCloud', '/index']
        },
        {
          label: '数据来源',
          name: 'assets_source',
          icon: 'input',
          minWidth: '120',
          detailIs: 2,
          path: [
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/scanReg',
            '/index'
          ]
        },
        {
          label: '状态码',
          name: 'http_status_code',
          icon: 'select',
          minWidth: '70',
          detailIs: 2,
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '历史最新解析域名',
          name: 'lateast_parse_domain',
          icon: 'select',
          labelTip: '资产的最近一条历史绑定域名',
          minWidth: '150',
          detailIs: 1,
          path: ['/assetsLedger', '/index']
        },
        {
          label: '组件信息',
          name: 'rule_tags',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '资产分类',
          name: 'ip_status',
          minWidth: '100',
          detailIs: 2,
          path: ['/scanReg']
        },
        {
          label: '根域',
          name: 'domain',
          icon: 'select',
          minWidth: '120',
          detailIs: 2,
          path: ['/scanReg', '/assetsCloud']
        },

        {
          label: '子域名',
          name: 'subdomain',
          icon: 'select',
          minWidth: '120',
          detailIs: 2,
          path: ['/scanReg', '/assetsCloud']
        },
        {
          label: 'ICP',
          name: 'icp',
          minWidth: '120',
          detailIs: 2,
          path: ['/scanReg', '/assetsCloud']
        },
        {
          label: '证书',
          name: 'cert',
          minWidth: '120',
          detailIs: 2,
          path: ['/scanReg', '/assetsCloud']
        },
        {
          label: 'ICON',
          name: 'logo',
          minWidth: '80',
          detailIs: 2,
          path: ['/scanReg', '/assetsCloud']
        },

        {
          label: '企业名称',
          name: 'clue_company_name',
          icon: 'input',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/scanReg',
            '/assetsCloud',
            '/index'
          ]
        },
        {
          label: '地理位置',
          name: 'province',
          icon: 'select',
          minWidth: '80',
          detailIs: 1,
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '资产标签',
          name: 'tags',
          minWidth: '107',
          icon: 'select',
          detailIs: 1,
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        // {
        //   label: "首次发现时间",
        //   name: "created_at",
        //   minWidth: "120",
        //   detailIs: 2,
        //   path: ["/assetsCloud"],
        // },
        {
          label: '首次发现时间',
          name: 'source_updated_at',
          minWidth: '120',
          detailIs: 2,
          path: ['/scanReg', '/assetsCloud']
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: '120',
          detailIs: 2,
          path: ['/scanReg', '/assetsCloud']
        },
        {
          label: '创建时间',
          name: 'created_at',
          icon: 'date',
          labelTip: '资产首次入账的时间',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '更新时间',
          name: 'last_update_time',
          icon: 'date',
          labelTip: '资产最近一次在系统上更新的时间',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '信任度',
          name: 'assets_confidence_level',
          icon: 'select',
          minWidth: '120',
          detailIs: 2,
          path: ['/scanReg']
        }
      ],
      cascaderProps: {
        lazy: true,
        lazyLoad: this.lazyLoad
      },
      tipData: null,
      conditionFlag: 3, // 导出：已认领标识3
      conditionFlagList: 1, // 台账已认领资产（已认领标识1）
      protocol_concurrency_arr: [
        2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 100, 200, 300
      ], //并发
      scan_type_arr: [
        //扫描类型
        {
          name: '极速扫描',
          id: 1
        },
        {
          name: '深度扫描',
          id: 0
        }
      ],

      highLightKeyword: '', // 高亮字符
      importRuleForm: {
        bandwidth: '1000', // 扫描带宽
        task_type: 1, // 任务分类 1 资产扫描 2 漏洞扫描,
        ip_type: 1, // ipv4: 1; ipv6:2
        protocol_concurrency: 0, // 协议识别并发数，整型，0,2,4,8,10,12,14,16,18,20,22,24,26,28,30,100,200,300,
        scan_type: 1, // 扫描类型 0为精准 1为极速,
        type: 6, // 类型 默认6 2周期扫描 3月 4周 5天 6一次,
        ping_switch: false, // 开启ping资产识别 默认0 关闭 1 打开,
        web_logo_switch: false, // 0不开启 1开启
        port_group_ids: ''
      },
      is_define_port: 0,
      define_port_protocols: [], //自定义协议
      define_ports: '', //自定义端口
      cluesList: {}, // 线索数据
      dialogFormVisibleScan: false, //执行扫描弹框
      selectData: {
        scan_type_arr: [
          {
            name: '极速扫描',
            id: 1
          },
          {
            name: '深度扫描',
            id: 0
          }
        ],
        portGroupsNoPageArr: []
      },
      ruleFormScan: {
        id: [],
        name: '',
        bandwidth: 1000,
        port_group_ids: '',
        scan_type: 1,
        ip_type: 1,
        ping_switch: false,
        web_logo_switch: false, // 0不开 1开启
        flag: '', // 如果此字段有值表示是全选，如果此字段为空，则读取id数据",
        keyword: '',
        operate_company_id: '',
        is_define_port: 0,
        define_ports: '',
        define_port_protocols: []
      },
      btnLoadingScan: false,
      protocolTransferData: [], //协议列表
      transferPropProtocol: {
        key: 'id',
        label: 'protocol'
      },
      protocoldiaLoading: false,
      filterMethodProtocol(query, item) {
        return item.protocol.indexOf(query) > -1
      },
      activeNameScan: 'first',
      protocolTransferDatas: [],
      protocoldiaLoadings: false,
      filterMethodProtocols(query, item) {
        return item.protocol.indexOf(query) > -1
      },
      isPortScan: false,
      index: 0,
      isBtnVisible: false,
      loadingInstance: null
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.user.role == 2) {
        // 切换账号去除全选
        this.checkedAll = false
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, false)
        })
        if (this.$route.path == '/unclaimCloud') {
          this.filterSuspected()
        } else if (this.$route.path == '/ignoreAssets') {
          this.filterSuspected()
        } else if (this.$route.path == '/threatAssets') {
          this.filterSuspected()
        } else if (this.$route.path == '/scanReg') {
          // 推荐查看详情
          this.getRecommendRecordsList()
        } else if (this.$route.path == '/riskAssets') {
          // poc管理
          this.$router.go(-1)
        } else if (this.$route.path == '/assetsCloud' || this.$route.path == '/unitIndex') {
          // 推荐资产库
          this.getIPTaskAllList()
        } else {
          this.getknownAssetsList()
          this.getMergeTakList()
        }

        this.tableDataList = this.tableHeaderCopy
        this.componentKey += 1
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    },
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    source() {
      this.textarea = ''
      this.fileData = []
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.imgUrl = this.userInfo.is_local ? '//images.weserv.nl/?url=' : this.imgBaseUrl
    // let timestamp = new Date().getTime();
    // this.changeTime(timestamp);
    this.tableHeaderCopy = [...this.tableHeaderIsShow]
    this.tableDataList = [...this.tableHeaderIsShow]
    // 以下为 ip 资产台账新增列默认展示使用
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage']),
    assetsSourceList() {
      return assetsSourceList
    },
    inputStyle() {
      let style = {}
      style.width = `${this.inputLength}px`
      return style
    },
    uploadAction() {
      let url = ''
      if (this.user.role == 2) {
        // operate_company_id: 操作的企业id（安服角色需要，是单个值，不是数组），如果是普通用户，不需要这个值
        url = `${this.uploadSrcIp}/assets/account/upload?operate_company_id=${this.currentCompany}`
      } else {
        url = `${this.uploadSrcIp}/assets/account/upload`
      }
      return url
    },
    tableHeaderIsShow(path) {
      let arr = []
      if (this.$route.path == '/scanReg') {
        // 推荐资产库
        this.tableHeader.forEach((item) => {
          if (item.name == 'ip') {
            item.minWidth = '200'
          }
          if (item.name == 'port') {
            item.minWidth = '110'
          }
        })
        arr = this.tableHeader.filter((item) => {
          return item.path.indexOf(this.$route.path) != -1
        })
      } else if (this.$route.path == '/unitIndex' || this.$route.path == '/assetsCloud') {
        // 推荐资产库
        this.tableHeader.forEach((item) => {
          if (item.name == 'ip') {
            item.minWidth = '200'
          }
          if (item.name == 'port') {
            item.minWidth = '110'
          }
        })
        arr = this.tableHeader.filter((item) => {
          return item.path.indexOf('/assetsCloud') != -1
        })
      } else {
        if (this.user.role == 1 || this.user.role == 2) {
          arr = this.tableHeader.filter((item) => {
            return item.path.indexOf(this.$route.path) != -1
          })
        } else {
          // 普通企业去掉assets_source字段
          arr = this.tableHeader
            .filter((item) => {
              return item.path.indexOf(this.$route.path) != -1
            })
            .filter((item) => {
              return item.name != 'assets_source'
            })
        }
      }
      return arr
    },
    columnId() {
      let id = ''
      if (this.$route.path == '/assetsLedger') {
        id = '1'
      } else if (this.$route.path == '/unclaimCloud') {
        id = '3'
      } else if (this.$route.path == '/ignoreAssets') {
        id = '5'
      } else if (this.$route.path == '/threatAssets') {
        id = '7'
      } else if (this.$route.path == '/assetsCloud') {
        id = '10'
      } else if (this.$route.path == '/scanReg') {
        id = '11'
      } else if (this.$route.path == '/riskAssets') {
        id = '12'
      }
      return id
    },
    getAssetsSource() {
      let data = {}
      assetsSourceList.forEach((v) => {
        if (v.value != '-') {
          data[v.value] = v.label
        }
      })
      return data
    }
    // detailItemHight() {
    //    let height = !detailItem.hostList||detailItem.hostList.length  <= 1 ? 40 : (detailItem.hostList.length<15?detailItem.hostList.length*40:detailItem.hostList.length*4125/100)
    //  }
  },
  async mounted() {
    if (
      this.$route.path == '/assetsLedger' ||
      this.$route.path == '/unclaimCloud' ||
      this.$route.path == '/ignoreAssets' ||
      this.$route.path == '/threatAssets'
    ) {
      let res1 = {}
      res1 = await pgdTypeList({ operate_company_id: this.currentCompany })
      if (res1.code == 0) {
        this.threaten_type_arr = res1.data.items
      }
    }
    if (this.user.role == 2 && !this.currentCompany) return

    if (
      this.$route.path == '/assetsLedger' ||
      this.$route.path == '/unclaimCloud' ||
      this.$route.path == '/threatAssets'
    ) {
      if (this.$route.query.monthRange) {
        this.formInline.created_at = this.$route.query.monthRange
        this.filterCondition.created_at = this.$route.query.monthRange
        this.hightFilterShow = 1
      }
    }

    if (this.$route.path == '/assetsLedger') {
      if (this.$route.query.is_cdn) {
        this.formInline.is_cdn = 1
        this.filterCondition.is_cdn = 1
        this.hightFilterShow = 1
      }
      if (this.$route.query.createdTimeRange) {
        let createdTimeRange = this.$route.query.createdTimeRange
        this.formInline.created_at = createdTimeRange
        this.filterCondition.created_at = createdTimeRange
        this.hightFilterShow = 1
      }
      this.getMergeTakList() // 获取顶部任务记录信息
    }

    if (this.$route.query.confirm && this.$route.query.confirm == 1) {
      // 漏洞任务页面跳转过来的，需要默认开启资产确权
      this.second_confirm = 0
      this.getConfirmList()
    } else if (this.$route.query && this.$route.query.ip) {
      //从域名详情跳转过来 查询ip地址
      this.formInline.ip = this.$route.query && this.$route.query.ip
      this.highCheck()
    } else {
      // 正常台账数据，不开启资产确权

      this.init()
    }
  },

  methods: {
    updateAssetsInfoByFOFA() {
      updateDomainAssetsByFOFA({
        operate_company_id: this.currentCompany,
        type: 1
      }).then((res) => {
        if (res.code == 0) {
          this.$message({
            type: 'success',
            message: '已成功下发fofa更新资产信息任务!'
          })
        }
      })
    },
    closeDefaultTagDialog() {
      this.defaultTagsVisible = false
      this.$refs.tagsInput.inputValue = ''
    },
    changeDefaultTag(val) {
      this.defaultTags = val
    },
    setDefaultTags(type, row) {
      if (type == 'more' && !this.checkedAll && this.checkedArr.length == 0) {
        this.$message.error('请选择操作数据')
        return
      }
      this.currentTagsSetType = type
      this.currentRowId = row && row.id
      this.parentTags =
        (row && row.customer_tags && JSON.parse(JSON.stringify(row.customer_tags))) || []
      this.defaultTagsVisible = true
    },
    editCompanyName(type) {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要编辑的数据！')
        return
      }
      if ((this.checkedAll && this.total > 100) || this.checkedArr.length > 100) {
        this.$message.error('每次最多可选择100条IP记录进行编辑! ')
        return
      }
      this.$prompt('请输入新的企业名称', `${type == 'more' ? '批量' : ''}编辑企业名称`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'cloud_info_del_sure',
        cancelButtonClass: 'cloud_info_del_cancel',
        inputValidator: (value) => {
          if (value) {
            return true
          } else {
            return false
          }
        },
        inputErrorMessage: '请输入',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let obj = {
              status: this.conditionFlag,
              is_all: this.checkedAll ? '1' : '',
              select_ip: this.checkedAll
                ? []
                : this.checkedArr.map((item) => {
                    return item.ip
                  }),
              operate_company_id: this.currentCompany,
              set_clue_company_name: instance.inputValue.trim(),
              ...this.formInline
            }
            let res = await tableIpCompanyName(obj)
            if (res.code == 0) {
              done()
              this.getknownAssetsList()
            }
            setTimeout(() => {
              instance.confirmButtonLoading = false
            }, 300)
          } else {
            done()
          }
        }
      }).then(({ value }) => {
        this.$message({
          type: 'success',
          message: '修改成功'
        })
      })
    },
    async setDefaultTagsConfirm(type, id) {
      // if(!this.defaultTags || this.defaultTags.length == 0){
      //   this.$message.error('请输入自定义标签')
      //   return
      // }
      let ids
      let is_all
      if (this.currentTagsSetType == 'more') {
        ids = this.checkedArr.map((item) => {
          return item.id
        })
        is_all = this.checkedAll ? 1 : 0
      } else {
        ids = [this.currentRowId]
        is_all = 0
      }
      let obj = {
        ...this.formInline,
        status: '',
        type: '',
        id: ids,
        operate_company_id: this.currentCompany,
        customer_tags: this.defaultTags,
        is_all
      }
      this.setTagLoading = true
      let res = await setAssetsDefaultTags(obj).catch(() => {
        this.setTagLoading = false
      })
      if (res.code == 0) {
        this.defaultTagsVisible = false
        this.getknownAssetsList()
        this.$message.success('设置自定义标签成功')
        this.setTagLoading = false
      }
    },
    dblclick(ip) {
      clearTimeout(this.clickTimer)
      this.copyTextText(ip)
    },
    copyTextText(text) {
      this.$copyText(text).then(
        (res) => {
          this.$message.success('复制成功')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    noteBlackList(title) {
      this.noteDialogVisible = true
      this.currentTitle = title
    },
    ...mapMutations(['changeTime', 'changeMenuId']),
    setTag(row, itemDetailOne, detailItem) {
      this.newTagVisible = true
      this.tagParams = {
        id: row.id,
        ip: row.ip,
        port: itemDetailOne.port,
        protocol: itemDetailOne.protocol,
        domain: itemDetailOne.subdomain,
        url: itemDetailOne.url,
        system_name: '',
        company_name: row.clue_company_name[0],
        operate_company_id: this.currentCompany
      }
    },
    async onSubmitBtn(val) {
      if (!val) {
        this.$message.error('请输入系统标签')
        return
      }
      this.tagParams.system_name = val
      this.newTagLoading = true
      let res = await setBusinessTag(this.tagParams)
      if (res.code == 0) {
        this.newTagVisible = false
        this.newTagLoading = false
        this.$message({
          type: 'success',
          message: '操作成功'
        })
      }
    },
    // 一键匹配黄赌毒词库
    async batchMatch() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      this.formInline.last_update_time = this.formInline.last_update_time
        ? this.formInline.last_update_time
        : []
      let obj = {
        menu: '威胁资产', // 用于导出后文件名称显示
        status: [3],
        is_all: this.checkedAll ? 1 : 0,
        type: '',
        select_ip: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.ip
            }),
        operate_company_id: this.currentCompany,
        ...this.formInline
      }
      this.batchMatchLoading = true
      let res = await assetsBacthMatch(obj).catch(() => {
        this.batchMatchLoading = false
      })
      if (res.code == 0) {
        this.batchMatchLoading = false
        this.$confirm(`一共匹配到威胁词库数量为${res.data.num}`, '提示', {
          distinguishCancelAndClose: true, // 设置关闭和取消独立
          confirmButtonText: '确定',
          confirmButtonClass: 'keyword_del_sure',
          customClass: 'keyword_del',
          showCancelButton: false,
          type: 'success'
        })
        this.filterSuspected()
        this.checkedAll = false
        this.$refs.eltable.clearSelection()
      }
    },
    copyText(val) {
      this.textarea = this.correctList.join('\r')
      this.fileData = this.correctList
      this.$copyText(val.join('\r')).then(
        (res) => {
          this.$message.success('复制成功')
          this.assetsValidDialogVisible = false
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    async scanIp() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      this.scanBtnLoading = true
      // this.formInline.last_update_time = this.formInline.last_update_time ? this.formInline.last_update_time : []
      let obj = {
        flag: this.$route.query.flag,
        operate_company_id: this.currentCompany,
        id: this.checkedArr.map((item) => {
          return item.ip
        }),
        whole: this.checkedAll ? 1 : '', // 传值代表全部
        ...this.formInlineScan
      }
      let res = await scanAssets(obj).catch(() => {
        this.scanBtnLoading = false
      })
      // this.scanBtnLoading = false
      if (res && res.code == 0) {
        if (Object.keys(this.cluesList).length == 0) {
          this.getRecommendCluesData(this.$route.query.flag)
        }
        this.getRecommendRecordsList()
        setTimeout(() => {
          this.scanBtnLoading = false
          // 操作成功 已下发扫描任务 请稍后
          this.$message.success('操作成功,已下发扫描任务,请稍后...')
          this.checkedAll = false
          this.checkAllChange()
        }, 2000)
      } else {
        this.scanBtnLoading = false
      }
    },
    goAssetsScan() {
      //跳转
      sessionStorage.setItem('menuId', '2-1')
      this.$router.push('/assetsScan')
      this.changeMenuId('2-1')
    },
    // 组件选择发生改变
    columnChecked(value) {
      this.tableHeader = value
      // this.doLayout(this, 'eltable')
    },
    getTableStatus(status) {
      let statusLabel = ''
      if (status == 0) {
        statusLabel = '等待扫描'
      } else if (status == 1) {
        statusLabel = '扫描中'
      } else if (status == 2) {
        statusLabel = '扫描完成'
      } else if (status == 3) {
        statusLabel = '扫描失败'
      } else if (status == 4) {
        statusLabel = '暂停扫描'
      }
      return statusLabel
    },
    closeTip() {
      this.tipData = null
      if (this.hightFilterShow == 1) {
        setTimeout(() => {
          var tableWrap = document.getElementsByClassName('tableWrap')[0]
          tableWrap.style.height = 'calc(100% - 174px)'
        }, 100)
      }
    },
    handleChangeLeft(value, direction) {
      this.$nextTick(() => {
        this.$refs.reserve.addToRight() //直接执行到右事件
      })
    },
    handleChangeRight(value, direction) {
      this.$nextTick(() => {
        this.$refs.reserve.addToLeft() //直接执行到右事件
      })
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (
        res.cmd == 'scan_task_progress' &&
        res.data.type != 1 &&
        this.tipData &&
        this.tipData.id &&
        this.tipData.id == res.data.task_id
      ) {
        this.runningFunc(res)
      }
    },
    // 点击组件查询
    ruleCheck(val) {
      var tableWrap = document.getElementsByClassName('tableWrap')[0]
      if (this.formInline.rule_tags.indexOf(val) != -1) {
        this.formInline.rule_tags = this.formInline.rule_tags.filter((item) => {
          return item != val
        })
      } else {
        this.formInline.rule_tags.push(val) // 现在是追加查询的
      }
      if (this.formInline.rule_tags.length == 0) {
        this.hightFilterShow = 2
        tableWrap.style.height = 'calc(100% - 130px)'
      }
      this.highLightKeyword = val
      this.highCheck()
    },
    // websocket执行
    runningFunc(res) {
      if (res.data.status == 2) {
        resetMessage.success('扫描成功！')
        this.currentId = '' // 控制推送结束后仅执行一次
        this.loading = true
        setTimeout(() => {
          this.getknownAssetsList(1) // 台账,is_sleep：1代表是删除后请求列表接口，后端需要延时
        }, 2000)
      } else if (res.data.status == 1) {
        // 正在扫描
        this.currentId = res.data.task_id
        // 推送数据渲染到列表
        this.$set(this.tipData, 'status', res.data.status)
        this.$set(this.tipData, 'target', res.data.target)
        this.$set(this.tipData, 'progress', res.data.progress)
        this.$set(this.tipData, 'use_seconds', res.data.use_seconds)
        this.$set(this.tipData, 'start_at', res.data.start_at)
      } else if (res.data.status == 4) {
        // 暂停扫描
      } else {
        // 3 扫描失败
        this.getMergeTakList()
      }
    },
    beforeExpandData(data) {
      return data
    },
    translatePortFormat(data, hostList) {
      if (Boolean(data && hostList)) {
        for (let j = 0; j < data.length; j++) {
          data[j].hostList = []
          for (let i = 0; i < hostList.length; i++) {
            if (data[j].port == hostList[i].port) {
              // details平级的host_list中存在与detail中的port的相同项
              data[j].hostList.push(hostList[i])
            }
          }
        }
      }
      return data
    },
    getExpandData(dataOrigin, index) {
      let arr = []
      let data = dataOrigin && JSON.parse(JSON.stringify(dataOrigin))
      if (Boolean(data)) {
        for (let j = 0; j < data.length; j++) {
          if (data[j] && Array.isArray(data[j].hostList)) {
            data[j].hostListLength = dataOrigin[j].hostList.length
            if (!data[j].isURLExpand && data[j].hostListLength > 3) {
              // 收齐
              data[j].hostList =
                data[j].hostList && data[j].hostList.length > 0 ? data[j].hostList.slice(0, 3) : []
            } else {
              // 展开
              data[j].hostList = data[j].hostList
            }
          }
        }
      }
      if (!this.tableData[index].isExpand) {
        arr = data && data.length > 0 ? data.slice(0, 3) : []
      } else {
        arr = data
      }
      return arr
    },
    getExpandUrl(index, detailIndex, isURLExpand) {
      this.tableData[index].detail[detailIndex].isURLExpand = isURLExpand
      this.doLayout(this, 'eltable')
    },
    // 展开
    async getExpand(index, row, id, isMore = 1) {
      this.tableData[index].isExpandLoading = true
      if (isMore > 1) {
        let res = await ipPortListData({ id, operate_company_id: this.currentCompany })
        let data = res.data.items[0].detail
        let hostList = res.data.items[0].host_list
        data &&
          data.forEach((v) => {
            v.isURLExpand = false
          })
        let detailPort = this.translatePortFormat(data, hostList)
        this.tableData[index].detailPort.push(...detailPort)
      }
      this.tableData[index].isExpand = true
      this.tableData[index].myPopoverFlag = false
      this.doLayout(this, 'eltable')
      this.tableData[index].isExpandLoading = false
    },
    // 收缩
    getExpands(index) {
      this.tableData[index].isExpand = false
      this.tableData[index].myPopoverFlag = true
      this.doLayout(this, 'eltable')
    },
    getChains(row) {
      let arr = row.filter((item) => {
        return item.content
      })
      return arr
    },
    async myChange(data, val) {
      this.namePort = data
      if (val == '') {
        this.activeNameScan = 'second'
        this.protocoldiaLoading = true
        let prores = await getPortProList({
          operate_company_id: this.currentCompany
        })
        this.protocoldiaLoading = false
        this.protocolTransferData = prores.data
      }
    },
    getResult(row) {
      // asset_num、rule_num都不存在或者都等于0，显示【未发现网络资产】
      // threat_num不存在或者都等于0，显示【未发现漏洞】
      let str = ''
      let strSp = row.asset_num && row.asset_num / 1 > 0 ? ',' : ''
      if (row.asset_num && row.asset_num / 1 > 0) {
        str += '发现IP：' + row.asset_num
      }
      if (row.rule_num && row.rule_num / 1 > 0) {
        str += strSp + '发现组件：' + row.rule_num
      }
      if ((!row.asset_num || row.asset_num / 1 == 0) && (!row.rule_num || row.rule_num / 1 == 0)) {
        str = '未发现网络资产'
      }
      return str
    },
    async getMergeTakList() {
      let obj = {
        page: 1,
        per_page: 10,
        type: '',
        task_type: 1, // 任务分类 1 资产扫描 2 漏洞扫描,
        name: '',
        status: '', // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
        created_at_range: [],
        dispatched_at_range: [],
        sortField: 'status',
        sortOrder: 'asc',
        is_schedule: 0,
        op_id: '',
        operate_company_id: this.currentCompany
      }
      let res = await getAllTaskList(obj)
      if (res.data) {
        this.tipData = res.data.items && res.data.items.length > 0 ? res.data.items[0] : ''
      }
    },
    // 编辑
    edit(data) {
      this.dialogVisible = true
      this.ruleForm = data
    },
    infoCheck(id) {
      const _that = this
      clearTimeout(this.clickTimer) // 清除第一个单击事件
      this.clickTimer = setTimeout(function () {
        // 单击事件的代码执行区域
        window.open(
          `/alreadyTask_viewlist_ipinfo?id=${id}&fromIcon=0&preList=${_that.$route.path}`,
          '_blank'
        ) // fromIcon: 默认'0' 已完成任务列查看ip详情，传1
      }, 500)
    },
    compile() {
      this.dialogVisible = false
      // this.$message({
      //   message: '修改成功',
      //   type: 'success'
      // });
    },
    async init(sleep) {
      this.formInline.website_message_id = this.notifyFilterId || ''

      if (this.$route.path == '/unclaimCloud') {
        // 疑似资产
        this.filterSuspected()
      } else if (this.$route.path == '/ignoreAssets') {
        // 忽略资产
        this.filterSuspected()
      } else if (this.$route.path == '/threatAssets') {
        //威胁资产
        this.filterSuspected()
      } else if (this.$route.path == '/scanReg') {
        // 云端推荐详情
        if (Object.keys(this.cluesList).length == 0) {
          this.getRecommendCluesData(this.$route.query.flag)
        }
        this.getRecommendRecordsList()
      } else if (this.$route.path == '/riskAssets') {
        // poc管理风险资产数
        this.getRistAssetsList()
      } else if (this.$route.path == '/assetsCloud' || this.$route.path == '/unitIndex') {
        // 云端推荐、单位资产测绘跳转资产库
        this.getIPTaskAllList()
      } else {
        this.getknownAssetsList(sleep) // 台账,is_sleep：1代表是删除后请求列表接口，后端需要延时
      }
    },
    // 推荐资产库
    async getIPTaskAllList() {
      let obj = {
        keyword: this.formInline.keyword,
        operate_company_id: this.currentCompany,
        page: this.currentPage,
        per_page: this.pageSize
      }
      this.loading = true
      let res = await couldIpListAll(obj).catch(() => {
        this.loading = false
      })
      this.loading = false
      let arr = []
      let that = this
      if (res.code == 0) {
        for (let key in res.data.items) {
          let chain_list = []
          if (res.data.items[key]) {
            // 端口的chain_list聚合去重
            res.data.items[key].forEach((item) => {
              if (item.chain_list) {
                chain_list.push(...item.chain_list)
              }
              item.detail &&
                item.detail.forEach((v) => {
                  v.isURLExpand = false
                })
            })
          }
          let resetChainList = that.uniqChainArr(chain_list) // 多条线索链去重
          arr.push({
            is_cdn: res.data.items[key][0].is_cdn ? res.data.items[key][0].is_cdn : false,
            ip: key,
            id: res.data.items[key][0].id,
            chain_list: resetChainList,
            clue_company_name: res.data.items[key][0].clue_company_name,
            detail: res.data.items[key],
            rule_tags: [],
            hosts: [],
            isExpand: false,
            myPopover: false,
            myPopoverFlag: true,
            cloud_name: res.data.items[key][0].cloud_name,
            detailPort: res.data.items[key]
          })
        }
      }
      this.tableData = arr.reverse()
      this.total = res.data.total
    },
    // 多条线索链去重
    uniqChainArr(arr) {
      let newarr = [] //盛放去重后数据的新数组
      for (let el of arr) {
        //循环arr数组对象的内容, i是数组
        let flag = true //建立标记，判断数据是否重复，true为不重复
        for (let elk of newarr) {
          //循环新数组的内容
          let reg =
            String(
              el.map((item) => {
                return item.content
              })
            ) ==
            String(
              elk.map((item) => {
                return item.content
              })
            )
          if (reg) {
            flag = false
          }
        }
        if (flag) {
          //判断是否重复
          newarr.push(el) //不重复的放入新数组。  新数组的内容会继续进行上边的循环。
        }
      }
      return newarr
    },
    getTagsName(tags) {
      let tagname = ''
      switch (tags) {
        case 0:
          tagname = '用户-扫描'
          break
        case 1:
          tagname = '安服-扫描'
          break
        case 2:
          tagname = '用户-推荐'
          break
        case 3:
          tagname = '安服-推荐'
          break
        case 4:
          tagname = '安服-导入'
          break
      }
      return tagname
    },
    getTheatTypeName(name) {
      // 钓鱼仿冒、ICP盗用、黄赌毒网站、其他类型
      let type = ''
      if (name == 1) {
        type = '钓鱼仿冒'
      } else if (name == 2) {
        type = '黄赌毒网站'
      } else if (name == 3) {
        type = 'ICP盗用'
      } else if (name == 4) {
        type = '域名混淆'
      } else if (name == 0) {
        type = '其他类型'
      }
      return type
    },
    // getAssetsSource(name) {
    //   let source = ''
    //   if (!name) return '-'
    //   assetsSourceList.forEach((v) => {
    //     if (v.value == name) {
    //       source = v.label
    //       return
    //     }
    //   })
    //   return source
    // },
    // 云厂商单独处理
    getCloudName(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length == '0') {
            return ''
          } else {
            return item.join(',')
          }
        } else {
          // 字符串的数组需要单独处理
          if (Array.isArray(JSON.parse(JSON.stringify(item)))) {
            return JSON.parse(JSON.stringify(item)).join(',')
          } else {
            return String(item)
          }
        }
      } else {
        return ''
      }
    },
    getTableItem(item) {
      if (item) {
        if (Array.isArray(item)) {
          return item.join(',')
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    getReason(item) {
      // 推荐理由默认展示两个，鼠标移入显示更多
      let str = item.split(/;|；|\s+/).splice(0, 2)
      return str.join(';') + '…'
    },
    getAssetsType(type) {
      // 资产分类
      let str = ''
      if (type == 0) {
        str = '疑似资产'
      } else if (type == 1 || type == 4) {
        str = '资产台账'
      } else if (type == 2) {
        str = '忽略资产'
      } else if (type == 3) {
        str = '威胁资产'
      }
      return str
    },
    getCompany(item) {
      if (item) {
        if (Array.isArray(item)) {
          return item
        } else {
          return [item]
        }
      } else {
        return []
      }
    },
    // 高级筛选条件
    async getAssetsCluesData() {
      let data
      if (this.$route.path == '/unclaimCloud') {
        // 疑似
        data = {
          operate_company_id: this.currentCompany,
          type: 1,
          status: [0]
        }
      } else if (this.$route.path == '/ignoreAssets') {
        // 忽略
        data = {
          operate_company_id: this.currentCompany,
          type: '',
          status: [2]
        }
      } else if (this.$route.path == '/threatAssets') {
        // 威胁
        data = {
          operate_company_id: this.currentCompany,
          type: '',
          status: [3]
        }
      } else if (this.$route.path == '/riskAssets') {
        // poc管理风险资产数
        data = {
          operate_company_id: this.currentCompany,
          poc_id: this.$route.query.poc_id
        }
      } else if (this.$route.path == '/assetsLedger') {
        data = {
          operate_company_id: this.currentCompany,
          type: '',
          status: [1, 4],
          second_confirm: this.second_confirm
        }
      } else {
        data = {
          operate_company_id: this.currentCompany,
          type: '',
          status: [1, 4]
        }
      }
      if (this.$route.path == '/riskAssets') {
        let res = await getConditionV2(data)
        this.selectArr = res.data
        this.loadingInstance.close()
      } else {
        let res = await getConditionV1(data)
        this.selectArr = res.data
        this.loadingInstance.close()
      }
    },
    highCheckClick() {
      this.highCheckDialog = true
      this.loadingInstance = this.$loading({
        target: '.el-drawer'
      })
      this.getAssetsCluesData()
    },
    async lazyLoad(node, resolve) {
      let level = node.level
      let res
      if (!node.data) {
        res = await area('')
      } else {
        res = await area(node.data.value)
      }
      if (res.code == 0) {
        const nodes = Array.from(res.data).map((item) => ({
          value: item.adcode,
          label: item.name,
          leaf: level >= 2
        }))
        resolve(nodes)
      }
    },
    downloadForbidIpsExcel() {
      window.location.href = '/downloadTemplate/IP导入模板.zip'
    },
    downloadForbidIpsHd(icon) {
      if (this.isHeduiDialog) {
        window.location.href = '/downloadTemplate/核对IP台账导入模板.xlsx'
      } else {
        if (this.$route.path == '/threatAssets') {
          if (icon) {
            window.location.href = '/downloadTemplate/威胁资产导入模板.csv'
          } else {
            window.location.href = '/downloadTemplate/威胁资产导入模板.xlsx'
          }
        } else {
          if (icon) {
            window.location.href = '/downloadTemplate/资产导入模板.csv'
          } else {
            window.location.href = '/downloadTemplate/资产导入模板.xlsx'
          }
        }
      }
    },
    async getRistAssetsList() {
      this.loading = true
      this.tableData = []
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      let res = await pocRiskAssets({
        poc_id: this.$route.query.poc_id,
        operate_company_id: this.currentCompany,
        ...this.formInline
      }).catch(() => {
        this.loading = false
      })
      this.loading = false
      res.data.items.map((v) => {
        v.isExpand = false
        v.myPopoverFlag = true
        v.detail &&
          v.detail.forEach((n) => {
            n.isURLExpand = false
          })
        v.detailPort = this.translatePortFormat(v.detail, v.host_list)
      })
      this.tableData = res.data.items
      this.total = res.data.total
    },
    // 台账ip资产列表
    async getknownAssetsList(sleep, tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.is_sleep = sleep // sleep有值代表删除或导入成功后去请求列表，需要延时
      this.formInline.website_message_id = this.notifyFilterId || ''
      this.tableData = []
      this.identifier = sha1(this.user.id + 'sha1' + new Date().getTime())
      let obj = {
        operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
        type: '', // 统计接口直接传''
        ...this.formInline,
        sort: 1
      }
      this.loading = true
      let res = await ansysDataIndexV1(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        if (res.data.items) {
          // alert(11111)

          // 证据链去重
          res.data.items.forEach((value, index) => {
            value.isExpand = false
            value.myPopover = false
            value.myPopoverFlag = true
            if (value.chain_list) {
              //证据链去重
              let resetChainList = this.uniqChainArr(value.chain_list) // 多条线索链去重
              res.data.items[index].chain_list = resetChainList
            }
            value.detail &&
              value.detail.forEach((v, n) => {
                v.isURLExpand = false
              })
            value.detailPort = this.translatePortFormat(value.detail, value.host_list)
          })
        }
        this.loading = false
        this.btnLoading = false
        this.tableData = res.data.items
        this.total = res.data.total
        this.has_data = res.has_data // 不加筛选条件台账是否有数据
        if (sleep) {
          // 台账导入列表请求成功后再请求顶部消息提示
          this.getMergeTakList()
        }
        if (
          (!this.total || this.total == 0) &&
          this.notifyFilterId &&
          this.$route.path == '/assetsLedger'
        ) {
          this.$message.error('该批新增IP资产已被删除')
        }
        // 全选操作
        this.$nextTick(() => {
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        let timestamp = new Date().getTime()
        this.changeTime(timestamp)
      }
    },
    // 获取待确权列表
    getConfirmList() {
      this.formInline.second_confirm = this.second_confirm
      this.getknownAssetsList()
    },
    // 二次确认
    againComifrm(push_second_confirm) {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let string =
        push_second_confirm == 1 ? '只对用户推荐或者安服推荐的数据进行二次确认' : '确定取消确权吗'
      this.$confirm(string, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let obj = null
          this.formInline.last_update_time = this.formInline.last_update_time
            ? this.formInline.last_update_time
            : []
          obj = {
            operate_company_id: this.currentCompany,
            id: this.checkedAll
              ? []
              : this.checkedArr.map((item) => {
                  return item.id
                }),
            is_all: this.checkedAll ? '1' : '',
            ...this.formInline,
            push_second_confirm
          }
          this.loading = true
          this.confirmLoading = true
          let res = await ansysdataAgainConfirm(obj).catch(() => {
            this.confirmLoading = false
          })
          if (res.code == 0) {
            this.confirmLoading = false // 资产确权的按钮loading
            this.loading = true // 列表的loading
            this.$message.success('操作成功！')
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.$refs.eltable.clearSelection()
            setTimeout(() => {
              this.getConfirmList()
            }, 2000)
          }
        })
        .catch(() => {})
    },
    async removeMore() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'account_del_cancel',
        confirmButtonClass: 'account_del_sure',
        customClass: 'account_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = null
          this.formInline.created_at =
            this.formInline.created_at &&
            this.formInline.created_at[0] &&
            this.formInline.created_at[1]
              ? this.formInline.created_at
              : []
          this.formInline.last_update_time = this.formInline.last_update_time
            ? this.formInline.last_update_time
            : []
          if (this.$route.path == '/scanReg') {
            obj = {
              flag: this.$route.query.flag,
              data: {
                id: this.checkedArr.map((item) => {
                  return item.ip
                }),
                whole: this.checkedAll ? 1 : '',
                operate_company_id: this.currentCompany,
                ...this.formInlineScan
              }
            }
          } else {
            obj = {
              operate_company_id: this.currentCompany,
              id: this.checkedAll
                ? []
                : this.checkedArr.map((item) => {
                    return item.id
                  }),
              is_all: this.checkedAll ? '1' : '',
              ...this.formInline
            }
          }
          this.loading = true
          let res
          // return
          if (this.$route.path == '/unclaimCloud') {
            // 疑似资产
            res = await deleteSuspected(obj).catch(() => {
              this.loading = false
            })
          } else if (this.$route.path == '/ignoreAssets') {
            // 忽略
            res = await deleteNeglect(obj).catch(() => {
              this.loading = false
            })
          } else if (this.$route.path == '/threatAssets') {
            // 威胁
            res = await deleteThreaten(obj).catch(() => {
              this.loading = false
            })
          } else if (this.$route.path == '/scanReg') {
            // 扫描核对
            res = await delScanList(obj).catch(() => {
              this.loading = false
            })
          } else {
            // 台账
            res = await deleteStandingDataV1(obj).catch(() => {
              this.loading = false
            })
          }
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.$refs.eltable.clearSelection()
            if (this.second_confirm == '0') {
              // 代表开启了资产确权，要刷新资产确权列表
              this.getConfirmList()
            } else {
              if (this.$route.path == '/assetsLedger') {
                setTimeout(() => {
                  this.init(1) // is_sleep：1代表是台账页面导入或删除后请求列表接口，后端需要延时
                }, 2000)
              } else {
                this.init()
              }
            }
            if (this.$route.path == '/scanReg') {
              this.getRecommendCluesData() // 重新获取线索
            }
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.account_del>.el-message-box__btns')
        del.children[0].id = 'account_del_cancel'
        del.children[1].id = 'account_del_sure'
      }, 50)
    },
    goToView(url) {
      this.download(this.showSrcIp + url)
    },
    // 导入、核对台账
    async heduiTzFunSave() {
      if (this.uploadIsreading == '' && this.fileList.length == 0) {
        // 文件是否读取成功
        this.$message.error('请上传文件！')
        return
      } else if (this.uploadIsreading == 'ready' || this.uploadIsreading == 'uploading') {
        // 文件是否读取成功
        this.$message.error('文件正在读取，请稍后…')
        return
      } else if (this.uploadIsreading == 'success' && this.regUrlArr.length == 0) {
        this.$message.error('文件未读取到IP信息，请重新上传')
        return
      }
      this.btnLoading = true
      if (this.isHeduiDialog) {
        // 资产核对确认
        let res = await heduiKnownAssetsV1({
          file: this.regUrlArr,
          operate_company_id: this.currentCompany
        }).catch(() => {
          this.btnLoading = false
        })
        this.btnLoading = false
        if (res.code == 0) {
          this.dialogFormVisibleHedui = false
          this.$confirm(
            `上传资产共计${res.data.assets_count}个，通过核对，其中${res.data.not_assets_count}个资产未纳入管理，您可以查看或者下载核对结果`,
            '核对结果',
            {
              confirmButtonText: '下载核对结果',
              cancelButtonText: '关闭',
              type: 'success'
            }
          )
            .then(() => {
              this.goToView(res.data.url)
            })
            .catch(() => {})
        }
      } else {
        // 导入确认
        let assets_type = ''
        if (this.$route.path == '/unclaimCloud') {
          // 疑似
          assets_type = 'unsure_assetss'
        } else if (this.$route.path == '/threatAssets') {
          // 威胁
          assets_type = 'threaten_asset'
        } else {
          // 台账
          assets_type = 'account_assets'
        }
        let obj = {
          file: this.regUrlArr,
          operate_company_id: this.currentCompany,
          assets_type: assets_type
        }
        let res = await importKnownAssets(obj).catch(() => {
          this.btnLoading = false
        })
        this.dialogFormVisibleHedui = false
        this.loading = true
        setTimeout(() => {
          this.btnLoading = false
          this.init(1) // is_sleep：1代表是台账页面导入或删除后请求列表接口，后端需要延时
        }, 2000)
      }
    },
    // 导入并扫描
    async importScan() {
      this.source = 'input'
      this.textarea = ''
      this.namePort = ''
      this.dialogFormVisibleImport = true
      this.fileList = []
      this.fileData = []
      this.uploadIsreading = '' // 文件是否读取成功
      this.portLoading = true
      let groupres = await portGroupsNoPage({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.portLoading = false
      })
      this.portLoading = false
      this.portGroupsNoPageArr = groupres.data
      if (groupres.data) {
        this.portGroupsNoPageArr.forEach((item) => {
          if (item.name == '全部常用端口') {
            this.port_group_ids = item.id
          }
          if (item.name == '0-65535') {
            this.isScanId = item.id
          }
        })
        this.portGroupsNoPageArr.unshift({ id: '', name: '自定义' })
      }
    },
    // 导入
    async importTz() {
      this.fileList = []
      this.uploadIsreading = '' // 文件是否读取成功
      this.isHeduiDialog = false // 与资产核对共用区分
      this.dialogFormVisibleHedui = true
    },
    importByHandle() {
      const h = this.$createElement
      let ips = ''
      this.$msgbox('请输入导入的数据', `导入`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        message: h(
          'div',
          {
            attrs: {
              class: 'el-textarea'
            }
          },
          [
            h('textarea', {
              attrs: {
                class: 'el-textarea__inner',
                autocomplete: 'off',
                rows: 4,
                id: 'commentContent'
              },
              value: ips
              // on: { input: ips }
            })
          ]
        ),
        // inputValidator: (value) => {
        //   if (value) {
        //     return true
        //   } else {
        //     return false
        //   }
        // },
        // inputErrorMessage:'请输入',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let res = await heduiKnownAssetsV1({
              operate_company_id: this.currentCompany,
              file: instance.inputValue
            })
            if (res.code == 0) {
              done()
            }
            setTimeout(() => {
              instance.confirmButtonLoading = false
            }, 300)
          } else {
            done()
          }
        }
      }).then(({ value }) => {
        this.$message({
          type: 'success',
          message: '修改成功'
        })
      })
    },
    // 判断图片是否存在
    showimg(url) {
      let imgUrl = ''
      url = url.replace('/', '')
      url = url.replace('.', '')
      url = url.replace(' ', '')
      let result = false
      url = './' + url + '.png'
      let urlTmp = url
      urlTmp = urlTmp.replace('./', '').replace('.png', '')
      // 目录下所有文件名
      const files = require.context('../../assets/images/componentIcon/', false, /\.png$/)
      const filedata = files.keys()
      filedata.forEach((item) => {
        if (item === url) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp + '.png')
        } else if (item === url.toLowerCase()) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp.toLowerCase() + '.png')
        }
      })
      // 返回结果
      return {
        result,
        url: imgUrl
      }
    },
    // 已知资产核对台账
    heduiTzFun() {
      this.fileList = []
      this.isHeduiDialog = true // 与资产核对共用区分
      this.dialogFormVisibleHedui = true
    },
    exportList() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      this.formInline.last_update_time = this.formInline.last_update_time
        ? this.formInline.last_update_time
        : []
      let funcName = ''
      let obj = null
      this.$refs.eltable.clearSelection()
      this.checkedArr = []
      this.checkedAll = false
      if (this.$route.path == '/unitIndex' || this.$route.path == '/assetsCloud') {
        // 推荐资产库
        obj = {
          is_all: this.checkedAll ? '1' : '',
          id: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.ip
              }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
        funcName = exportCouldIpList
      } else {
        obj = {
          status: this.conditionFlag, // 3
          is_all: this.checkedAll ? '1' : '',
          select_ip: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.ip
              }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
        funcName = exportDataV1
      }
      this.exportLoadingBtn = true
      funcName(obj)
        .then((res) => {
          if (res.code == 0) {
            this.download(this.showSrcIp + res.data.url)
            this.$refs.eltable.clearSelection()
            this.exportLoadingBtn = false
            this.checkedAll = false
          }
        })
        .catch(() => {
          this.exportLoadingBtn = false
        })
    },
    ipUploadSuccessHd(response, file, fileList) {
      if (file.response.code == 0) {
        this.regUrlArr = []
        this.$message.success('上传成功')
        this.regUrlArr.push(response.data.url)
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadRemoveHd(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.regUrlArr = []
      } else {
        this.regUrlArr = res[0]
      }
    },
    ipUploadErrorHd(err, file, fileList) {
      this.$message.error(JSON.parse(err.message).message)
    },
    handleExceedHd() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    iptype_change(val) {
      // 资产下发时
      if (val == 2) {
        // 选择IPV6时，扫描类型自动切换为深度扫描
        this.scan_type_arr = [
          //扫描类型
          {
            name: '深度扫描',
            id: 0
          }
        ]
        this.importRuleForm.scan_type = 0
      } else {
        this.scan_type_arr = [
          //扫描类型
          {
            name: '极速扫描',
            id: 1
          },
          {
            name: '深度扫描',
            id: 0
          }
        ]
        this.importRuleForm.scan_type = 1
      }
    },
    // 导入并扫描保存
    async uploadSave() {
      if (
        this.importRuleForm.bandwidth &&
        /^(?:[1-9]\d*)$/.test(this.importRuleForm.bandwidth) == false
      ) {
        this.$message.error('扫描带宽请输入整数')
        return
      }
      if (this.source == 'file') {
        if (this.uploadIsreading == '' && this.fileList.length == 0) {
          // 文件是否读取成功
          this.$message.error('请上传文件！')
          return
        } else if (this.uploadIsreading == 'ready' || this.uploadIsreading == 'uploading') {
          // 文件是否读取成功
          this.$message.error('文件正在读取，请稍后…')
          return
        } else if (this.uploadIsreading == 'success' && this.fileData.length == 0) {
          this.$message.error('文件未读取到IP信息，请重新上传')
          return
        }
      }
      if (this.textarea == '' && this.source == 'input') {
        this.$message.error('请输入IP信息')
        return
      }
      if (this.textarea == '' && this.source == 'domain') {
        this.$message.error('请输入域名信息')
        return
      }
      let ips = []
      if (this.textarea) {
        ips = this.textarea
          .split(/[；|;|\r\n]/)
          .filter((item) => {
            return item.trim()
          })
          .map((item) => {
            return item.trim()
          })
      }
      let ipsObj = {}
      for (let key in ips) {
        ipsObj[key] = ips[key]
      }
      let newObj = Object.keys(ipsObj).map((val) => ({
        ip: ipsObj[val]
      }))
      if (this.port_group_ids == '') {
        this.is_define_port = 1
      } else {
        this.is_define_port = 0
      }
      let define_ports = this.define_ports
        ? this.define_ports
            .split(/[；|;|\r\n]/)
            .filter((item) => {
              return item.trim()
            })
            .map((item) => {
              return item.trim()
            })
        : []
      if (this.is_define_port == 1 && define_ports.length == 0) {
        // this.$message.error("请输入端口信息");
        // return;
        define_ports = ['65536']
      }
      if (this.is_define_port == 1 && this.define_port_protocols.length == 0) {
        this.$message.error('请选择协议')
        return
      }
      if (this.is_define_port == 1 && define_ports.length > 20) {
        this.$message.error('输入的端口信息过多，最多输入20个端口信息')
        return
      }
      if (this.user.level == 0 && this.user.role == 3 && this.namePort == '0-65535') {
        //扫描端口是全部端口和用户是测试用户时
        this.$message.error('如需下发全部端口扫描，请升级为正式用户！')
        return
      }
      // 是否开启ping识别
      this.importRuleForm.ping_switch = this.importRuleForm.ping_switch ? 1 : 0
      // 是否开启首页截图
      this.importRuleForm.web_logo_switch = this.importRuleForm.web_logo_switch ? 1 : 0
      this.importRuleForm.port_group_ids = this.port_group_ids
      let obj = {}
      if (this.source == 'input') {
        this.validateType = 'upload'
        obj = {
          port_group_ids: this.port_group_ids,
          source: this.source,
          data: newObj,
          operate_company_id: this.currentCompany,
          task_param: this.importRuleForm,
          is_define_port: this.is_define_port,
          define_ports: define_ports,
          define_port_protocols: this.define_port_protocols
        }
      } else if (this.source == 'domain') {
        this.validateType = 'textarea'
        obj = {
          port_group_ids: this.port_group_ids,
          source: this.source,
          data: newObj,
          operate_company_id: this.currentCompany,
          task_param: this.importRuleForm,
          is_define_port: this.is_define_port,
          define_ports: define_ports,
          define_port_protocols: this.define_port_protocols
        }
      } else {
        this.validateType = 'textarea'
        obj = {
          port_group_ids: this.port_group_ids,
          source: this.source,
          data: this.fileData,
          operate_company_id: this.currentCompany,
          task_param: this.importRuleForm,
          is_define_port: this.is_define_port,
          define_ports: define_ports,
          define_port_protocols: this.define_port_protocols
        }
      }
      if (this.namePort == '0-65535') {
        this.$confirm('扫描全部端口耗时过长，确定下发扫描任务？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            this.handleSaveRes(obj)
          })
          .catch(() => {})
      } else {
        this.handleSaveRes(obj)
      }
    },
    async handleSaveRes(obj) {
      this.btnLoading = true
      this.loading = true
      let res = await importSureIpDataV1(obj).catch(() => {
        this.btnLoading = false
        this.loading = false
      })
      if (res.code == 0) {
        this.uploadAfter(res)
        this.define_ports = ''
        this.define_port_protocols = []
      }
    },

    // 导入成功后操作
    uploadAfter(res) {
      // this.$message.success(res.data.error_list && res.data.error_list.length > 0 ? `导入成功！${res.data.warn_message ? '(' + res.data.warn_message + ');' : ''}解析错误数据已过滤：` + res.data.error_list.join(';') : `导入成功！${res.data.warn_message ? '(' + res.data.warn_message + ')' : ''}`)
      const h = this.$createElement
      // res.data.data_list.length && ()
      if (!res.data.error_list || (res.data.error_list && res.data.error_list.length == 0)) {
        this.$message({
          type: 'success',
          message: h('div', null, [
            h('span', { style: 'color: #67C23A' }, `操作成功！已下发扫描任务，请稍后`),
            h(
              'span',
              { style: 'color: #F8C136' },
              `${res.data.warn_message ? '(' + res.data.warn_message + ')' : ''}`
            )
          ])
        })
        this.dialogFormVisibleImport = false
      } else if (res.data.error_list && res.data.error_list.length > 0) {
        // 上传文件 或者 填写保存时的校验 存在错误格式的资产 支持用户复制粘贴修改 重新上传
        this.assetsValidDialogVisible = true
        this.errorList = res.data.error_list
        this.correctList = res.data.data_list
        // this.textarea
        this.btnLoading = false
      }

      setTimeout(() => {
        // 延时2秒，刚导入拿不到任务数据
        this.getknownAssetsList(1) // is_sleep：1代表是台账页面导入或删除后请求列表接口，后端需要延时
      }, 3000)
    },
    beforeIpUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过 20MB!')
      }
      return isLt1M
    },
    ipUploadSuccess(response, file, fileList) {
      if (file.response.code == 0) {
        this.fileData =
          response.data.data_list &&
          response.data.data_list.concat(response.data.error_list.map((item) => ({ ip: item })))
        const h = this.$createElement
        if (
          response.data.data_list.length &&
          (!response.data.error_list || response.data.error_list.length == 0)
        ) {
          this.$message({
            type: response.data.data_list.length == 0 ? 'warning' : 'success',
            message: h('div', null, [
              h(
                'p',
                { style: 'color: #67C23A' },
                `${
                  response.data.data_list.length == 0
                    ? '文件未读取到IP信息!请重新上传'
                    : '上传成功！'
                }`
              )
            ])
          })
        } else if (response.data.error_list && response.data.error_list.length > 0) {
          // this.$message({
          //   type: 'warning',
          //   message: h('div', null, [
          //     h('p', { style: 'color: #F8C136' }, `${response.data.error_list && response.data.error_list.length > 0 ? '以下解析错误数据已过滤：' + response.data.error_list.join('；') : ''}`)
          //   ])
          // });
          this.validateType = 'upload'
          // 上传文件后的校验
          this.assetsValidDialogVisible = true
          this.errorList = response.data.error_list
          this.correctList = response.data.data_list
        }
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadChange(file, fileList) {
      this.uploadIsreading = file.status
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.fileData = []
      }
    },
    ipUploadError(err, file, fileList) {
      this.$message.error(JSON.parse(err.message).message)
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    allowDrop(draggingNode, dropNode, type) {
      if (dropNode.data.label === '二级 3-1') {
        return type !== 'inner'
      } else {
        return true
      }
    },
    allowDrag(draggingNode) {
      return draggingNode.data.label.indexOf('三级 3-2-2') === -1
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      if (this.$route.path == '/unclaimCloud') {
        this.filterSuspected(true)
      } else if (this.$route.path == '/ignoreAssets') {
        this.filterSuspected(true)
      } else if (this.$route.path == '/threatAssets') {
        this.filterSuspected(true)
      } else if (this.$route.path == '/scanReg') {
        // 推荐结果
        this.getRecommendRecordsList(true)
      } else if (this.$route.path == '/riskAssets') {
        // poc管理
        this.getRistAssetsList()
      } else if (this.$route.path == '/assetsCloud' || this.$route.path == '/unitIndex') {
        // 推荐资产库
        this.getIPTaskAllList()
      } else {
        this.getknownAssetsList('', true)
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      if (this.$route.path == '/unclaimCloud') {
        this.filterSuspected(true)
      } else if (this.$route.path == '/ignoreAssets') {
        this.filterSuspected(true)
      } else if (this.$route.path == '/threatAssets') {
        this.filterSuspected(true)
      } else if (this.$route.path == '/scanReg') {
        this.getRecommendRecordsList(true)
      } else if (this.$route.path == '/riskAssets') {
        // poc管理
        this.getRistAssetsList()
      } else if (this.$route.path == '/assetsCloud' || this.$route.path == '/unitIndex') {
        this.getIPTaskAllList()
      } else {
        this.getknownAssetsList('', true)
      }
    },
    // 高级筛选的筛选确定
    highCheck(formInline) {
      Object.assign(this.formInline, formInline)
      this.formInline.created_at =
        formInline.created_at && formInline.created_at[0] && formInline.created_at[1]
          ? formInline.created_at
          : []
      this.formInline.updated_at =
        formInline.updated_at && formInline.updated_at[0] && formInline.updated_at[1]
          ? formInline.updated_at
          : []

      this.filterCondition = Object.assign({}, this.formInline)
      this.highCheckDialog = false
      this.currentPage = 1
      if (
        this.$route.path == '/unclaimCloud' ||
        this.$route.path == '/ignoreAssets' ||
        this.$route.path == '/threatAssets'
      ) {
        this.filterSuspected()
      } else if (this.$route.path == '/riskAssets') {
        // poc管理
        this.getRistAssetsList()
      } else if (this.$route.path == '/assetsCloud' || this.$route.path == '/unitIndex') {
        // 推荐资产库
        this.getIPTaskAllList()
      } else {
        this.getknownAssetsList()
      }

      let obj = {
        keyword: this.formInline.keyword,
        ip: this.formInline.ip,
        clue_company_name: this.formInline.clue_company_name,
        port: this.formInline.port,
        protocol: this.formInline.protocol,
        hosts: this.formInline.hosts,
        rule_tags: this.formInline.rule_tags,
        province: this.formInline.province,
        tags: this.formInline.tags,
        title: this.formInline.title,
        http_status_code: this.formInline.http_status_code,
        ip_match: this.formInline.ip_match,
        company_match: this.formInline.company_match,
        threaten_type_arr: this.formInline.threaten_type_arr,
        assets_source: this.formInline.assets_source,
        domain: this.formInline.domain,
        subdomain: this.formInline.subdomain,
        created_at: this.formInline.created_at,
        updated_at: this.formInline.updated_at,
        reason_type: this.formInline.reason_type,
        is_cdn: this.formInline.is_cdn,
        open_parse: this.formInline.open_parse,
        cloud_name: this.formInline.cloud_name,
        online_state: this.formInline.online_state,
        not_in_clue_domain: this.formInline.not_in_clue_domain,
        customer_tags: this.formInline.customer_tags
      }

      for (let key in obj) {
        if (
          (Array.isArray(obj[key]) && obj[key].length != 0) ||
          (!Array.isArray(obj[key]) && (obj[key] || obj[key] === 0))
        ) {
          return (this.hightFilterShow = 1)
        } else {
          this.hightFilterShow = 2
        }
      }
    },
    // 条件方框展示 的check方法 取消筛选项事件
    highCheckCancel(data) {
      this.formInline = {
        keyword: this.formInline.keyword,
        ...data
      }
      this.currentPage = 1
      if (
        this.$route.path == '/unclaimCloud' ||
        this.$route.path == '/ignoreAssets' ||
        this.$route.path == '/threatAssets'
      ) {
        this.filterSuspected()
      } else if (this.$route.path == '/riskAssets') {
        // poc管理
        this.getRistAssetsList()
      } else {
        this.getknownAssetsList()
      }
      // this.hightFilterShow = 1
      this.$nextTick(() => {
        let boxItemBox = this.$refs.hightFilter.$refs.boxItemBox
        for (let key in boxItemBox) {
          if (boxItemBox[key].style.display != 'none') {
            return (this.hightFilterShow = 1)
          } else {
            this.hightFilterShow = 2
          }
        }
      })

      // let obj = {
      //   keyword: this.formInline.keyword,
      //   ip: this.formInline.ip,
      //   clue_company_name: this.formInline.clue_company_name,
      //   port: this.formInline.port,
      //   protocol: this.formInline.protocol,
      //   hosts: this.formInline.hosts,
      //   rule_tags: this.formInline.rule_tags,
      //   province: this.formInline.province,
      //   tags: this.formInline.tags,
      //   title: this.formInline.title,
      //   http_status_code: this.formInline.http_status_code,
      //   threaten_type_arr: this.formInline.threaten_type_arr,
      //   assets_source: this.formInline.assets_source,
      //   domain: this.formInline.domain,
      //   subdomain: this.formInline.subdomain,
      //   created_at: this.formInline.created_at,
      //   updated_at: this.formInline.updated_at,
      //   reason_type: this.formInline.reason_type,
      //   is_cdn: this.formInline.is_cdn,
      //   open_parse: this.formInline.open_parse,
      //   cloud_name: this.formInline.cloud_name,
      //   online_state: this.formInline.online_state,
      //   not_in_clue_domain: this.formInline.not_in_clue_domain,
      //   customer_tags: this.formInline.customer_tags
      // }
      // for (let key in obj) {
      //   if (
      //     obj[key] &&
      //     ((Array.isArray(obj[key]) && obj[key].length != 0) || !Array.isArray(obj[key]))
      //   ) {
      //     return (this.hightFilterShow = 1)
      //   } else {
      //     this.hightFilterShow = 2
      //   }
      // }
    },
    highIsClose() {
      this.highCheckDialog = false
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    // 导出资产
    async exportAssets() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      let obj
      this.$refs.eltable.clearSelection()
      this.checkedArr = []
      this.checkedAll = false
      if (this.$route.path == '/unclaimCloud') {
        obj = {
          menu: '疑似资产', // 用于导出后文件名称显示
          status: [0],
          is_all: this.checkedAll ? 1 : 0,
          type: 1,
          select_ip: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.ip
              }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else if (this.$route.path == '/threatAssets') {
        obj = {
          menu: '威胁资产', // 用于导出后文件名称显示
          status: [3],
          is_all: this.checkedAll ? 1 : 0,
          type: '',
          select_ip: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.ip
              }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else if (this.$route.path == '/ignoreAssets') {
        obj = {
          menu: '已忽略资产', // 用于导出后文件名称显示
          status: [2],
          is_all: this.checkedAll ? 1 : 0,
          type: '',
          select_ip: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.ip
              }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else {
        obj = {
          flag: this.$route.query.flag,
          query: {
            operate_company_id: this.currentCompany,
            id: this.checkedArr.map((item) => {
              return item.ip
            }),
            whole: this.checkedAll ? 1 : '', // 传值代表全部
            ...this.formInlineScan
          }
        }
      }
      try {
        this.exportLoadingBtn = true
        let res
        if (this.$route.path == '/unclaimCloud') {
          res = await exportSuspected(obj)
        } else if (this.$route.path == '/threatAssets') {
          res = await exportThreaten(obj)
        } else if (this.$route.path == '/ignoreAssets') {
          res = await exportNeglect(obj)
        } else {
          res = await exportScanLis(obj)
        }
        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.url)
          this.$refs.eltable.clearSelection()
          this.checkedAll = false
          this.exportLoadingBtn = false
        }
      } catch (error) {
        this.exportLoadingBtn = false
      }
    },
    copyClick(data) {
      let text = ''
      data.map((item) => {
        this.getChains(item).map((v, i) => {
          if (v.type && v.type == 3) {
            if (i == item.length - 1 && v) {
              text += 'icon' + '\n'
            } else {
              text += 'icon' + '-->'
            }
          } else {
            if (i < item.length - 1 && v) {
              text += v.content + '-->'
            } else if (i == item.length - 1 && v) {
              text += v.content + '\n'
            } else {
              text += v.content
            }
          }
        })
      })
      this.$copyText(text).then(
        (res) => {
          this.$message.success('复制成功')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    // 标记威胁
    async labeledBtnClick() {
      this.labeledthreatVisible = false
      let obj = this.labeledValueObj
      obj.threaten_type = this.labeledValue.id + ''
      obj.threaten_type_name = this.labeledValue.type_name
      this.loading = true
      this.labeledBtnLoading = true
      let res
      if (this.$route.path == '/unclaimCloud') {
        res = await changeSuspected(obj).catch(() => {})
      } else if (this.$route.path == '/threatAssets') {
        res = await editThreatenType(obj).catch(() => {})
      } else if (this.$route.path == '/ignoreAssets') {
        res = await changeNeglect(obj).catch(() => {})
      } else {
        // 台账
        res = await ipAssetsHandle(obj).catch(() => {
          this.loading = false
        })
      }
      if (res.code == 0) {
        this.$message.success('操作成功！')
      }
      this.checkedAll = false
      if (this.$refs.eltable != undefined) {
        this.$refs.eltable.clearSelection()
      }
      this.loading = false
      this.labeledBtnLoading = false
      this.currentPage = this.updateCurrenPage(
        this.total,
        this.checkedArr,
        this.currentPage,
        this.pageSize
      ) // 更新页码
      this.filterSuspected()
    },
    // 标记
    async goSign(wait, status, showInfo) {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      let obj
      if (this.$route.path == '/unclaimCloud') {
        // 疑似资产列表
        obj = {
          status: [0],
          type: 1,
          set_status: status,
          is_all: this.checkedAll ? 1 : 0,
          id: this.checkedArr.map((item) => {
            return item.id
          }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else if (this.$route.path == '/threatAssets') {
        // 威胁资产列表
        obj = {
          status: [3],
          type: '',
          set_status: status,
          is_all: this.checkedAll ? 1 : 0,
          id: this.checkedArr.map((item) => {
            return item.id
          }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else if (this.$route.path == '/ignoreAssets') {
        // 忽略资产列表
        obj = {
          status: [2],
          type: '',
          set_status: status,
          is_all: this.checkedAll ? 1 : 0,
          id: this.checkedArr.map((item) => {
            return item.id
          }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else {
        // 台账
        obj = {
          status: '',
          type: '',
          set_status: status,
          is_all: this.checkedAll ? 1 : 0,
          id: this.checkedArr.map((item) => {
            return item.id
          }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      }
      if (showInfo == '标记威胁') {
        this.labeledValue = this.threaten_type_arr.length == 0 ? '' : this.threaten_type_arr[0]
        this.labeledthreatVisible = true
        this.labeledValueObj = obj
        return
      }

      this.$confirm(`确定${showInfo}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          this.loading = true
          let res = null
          if (this.$route.path == '/unclaimCloud') {
            // 疑似资产列表
            res = await changeSuspected(obj).catch(() => {
              this.loading = false
            })
          } else if (this.$route.path == '/threatAssets') {
            // 威胁资产列表
            res = await changeThreaten(obj).catch(() => {
              this.loading = false
            })
          } else if (this.$route.path == '/ignoreAssets') {
            // 忽略资产列表
            res = await changeNeglect(obj).catch(() => {
              this.loading = false
            })
          } else {
            // 台账
            res = await ipAssetsHandle(obj).catch(() => {
              this.loading = false
            })
          }
          if (res.code == 0) {
            this.$message.success('操作成功！')
          }
          this.checkedAll = false
          if (this.$refs.eltable != undefined) {
            this.$refs.eltable.clearSelection()
          }
          this.loading = false
          this.currentPage = this.updateCurrenPage(
            this.total,
            this.checkedArr,
            this.currentPage,
            this.pageSize
          ) // 更新页码
          this.filterSuspected()
        })
        .catch(() => {})
    },
    // 高级筛选资产列表/忽略资产列表
    async filterSuspected(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.last_update_time = this.formInline.last_update_time
        ? this.formInline.last_update_time
        : []
      this.tableData = []
      let obj
      if (this.$route.path == '/unclaimCloud') {
        obj = {
          status: [0],
          type: 1,
          operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
          type: '', // 统计接口直接传''
          sort: 1,
          ...this.formInline
        }
      } else if (this.$route.path == '/threatAssets') {
        //威胁资产
        obj = {
          status: [3],
          type: '',
          operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
          type: '', // 统计接口直接传''
          ...this.formInline
        }
      } else if (this.$route.path == '/ignoreAssets') {
        obj = {
          status: [2],
          type: '',
          operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
          type: '', // 统计接口直接传''
          ...this.formInline
        }
      }
      this.loading = true
      let res = null
      if (this.$route.path == '/unclaimCloud') {
        // 疑似
        res = await filterSuspectedList(obj).catch(() => {
          this.loading = false
          this.tableData = []
          this.total = 0
        })
      } else if (this.$route.path == '/threatAssets') {
        // 威胁
        res = await getThreatenList(obj).catch(() => {
          this.loading = false
          this.tableData = []
          this.total = 0
        })
      } else if (this.$route.path == '/ignoreAssets') {
        // 忽略
        res = await filterNeglectList(obj).catch(() => {
          this.loading = false
          this.tableData = []
          this.total = 0
        })
      } else {
        // 台账
        this.getknownAssetsList()
        // res = await this.getknownAssetsList().catch(() => {
        //   this.loading = false
        //   this.tableData = []
        //   this.total = 0
        // })
      }
      if (res && res.code == 0) {
        if (res.data.items) {
          // 证据链去重
          res.data.items.forEach((value, index) => {
            value.isExpand = false //控制展开字段
            value.myPopover = false
            value.myPopoverFlag = true
            if (value.chain_list) {
              let resetChainList = this.uniqChainArr(value.chain_list) // 多条线索链去重
              res.data.items[index].chain_list = resetChainList
            }
            value.detail &&
              value.detail.forEach((v) => {
                v.isURLExpand = false
              })
            value.detailPort = this.translatePortFormat(value.detail, value.host_list)
          })
        }
        this.tableData = res.data.items
        this.total = res.data.total
        if (
          (!this.total || this.total == 0) &&
          this.notifyFilterId &&
          this.$route.path == '/unclaimCloud'
        ) {
          this.$message.error('该批新增疑似资产已被删除')
        }
        this.has_data = res.has_data // 不加筛选条件台账是否有数据
        // 全选操作
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
          })
        }
        this.loading = false
        let timestamp = new Date().getTime()
        this.changeTime(timestamp)
      }
    },
    // 删除端口
    async deleteAssets(id, port, portList) {
      var selectArr = []
      if (portList.length == 1) {
        selectArr = [1]
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            id: id,
            port: String(port),
            operate_company_id: this.currentCompany
          }
          this.loading = true
          let res = await deleteAssetsV1(obj).catch(() => {
            this.loading = false
          })
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.currentPage = this.updateCurrenPage(
              this.total,
              selectArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            if (this.$route.path == '/assetsLedger') {
              setTimeout(() => {
                this.init(1) // is_sleep：1代表是台账页面导入或删除后请求列表接口，后端需要延时
              }, 2000)
            } else {
              this.init()
            }
          }
        })
        .catch(() => {})
    },
    // 删除URL单条信息
    deleteAssetsURL(row, url, detail) {
      const { ip } = row
      var selectArr = []
      if (detail.length == 1) {
        selectArr = [1]
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            ip,
            url,
            port: String(detail.port),
            operate_company_id: this.currentCompany
          }
          this.loading = true
          let res = await deleteAssetsURLV1(obj).catch(() => {
            this.loading = false
          })
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.currentPage = this.updateCurrenPage(
              this.total,
              selectArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            if (this.$route.path == '/assetsLedger') {
              setTimeout(() => {
                this.init(1) // is_sleep：1代表是台账页面导入或删除后请求列表接口，后端需要延时
              }, 2000)
            } else {
              this.init()
            }
          }
        })
        .catch(() => {})
    },
    // 获取任务推荐结果列表
    async getRecommendRecordsList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.tableData = []
      let obj = {
        flag: this.$route.query.flag,
        query: {
          audit: [3], // 扫描核对页面只展示已审核通过的数据
          operate_company_id: this.currentCompany,
          page: this.currentPage,
          per_page: this.pageSize,
          ...this.formInlineScan
        }
      }
      this.loading = true
      let res = await getScanList(obj)
      this.loading = false
      let arr = []
      if (res.code == 0) {
        let that = this
        for (let key in res.data.items) {
          let chain_list = []
          if (res.data.items[key]) {
            // 端口的chain_list聚合去重
            res.data.items[key].forEach((item) => {
              if (item.chain_list) {
                chain_list.push(...item.chain_list)
              }
              item.detail &&
                item.detail.forEach((v) => {
                  v.isURLExpand = false
                })
            })
          }
          let resetChainList = that.uniqChainArr(chain_list) // 多条线索链去重
          arr.push({
            is_cdn: res.data.items[key][0].is_cdn ? res.data.items[key][0].is_cdn : false,
            ip: key,
            id: res.data.items[key][0].id,
            chain_list: resetChainList,
            clue_company_name: res.data.items[key][0].clue_company_name,
            detail: res.data.items[key],
            rule_tags: [],
            hosts: [],
            isExpand: false,
            myPopover: false,
            myPopoverFlag: true,
            cloud_name: res.data.items[key][0].cloud_name,
            detailPort: res.data.items[key]
            // assets_source:res.data.assets_source
          })
        }
      }
      this.tableData = arr.reverse()
      this.total = res.data.total
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
    },
    async getRecommendCluesData() {
      // 线索
      this.loading = true
      let res = await regRecommendClues({
        flag: this.$route.query.flag,
        data: {
          operate_company_id: this.currentCompany
        }
      }).catch(() => {
        this.loading = false
      })
      this.loading = false
      if (res.data && res.data.logo) {
        res.data.logo.forEach((item, index) => {
          item.id = index + 1
        })
      }
      this.cluesList = res.data ? res.data : []
    },
    //高级筛选
    highIsCloseScan() {
      this.highCheckDialogScan = false
    },
    highCheckScan(val) {
      if (val) {
        // 高级筛选条件
        this.formInlineScan = val
      }
      this.filterCondition = Object.assign({}, this.formInlineScan)
      this.highCheckDialogScan = false
      this.currentPage = 1
      this.init()
      let obj = {
        keyword: this.formInlineScan.keyword,
        ip: this.formInlineScan.ip,
        port: this.formInlineScan.port,
        protocol: this.formInlineScan.protocol,
        domain: this.formInlineScan.domain,
        subdomain: this.formInlineScan.subdomain,
        title: this.formInlineScan.title,
        cert: this.formInlineScan.cert,
        icp: this.formInlineScan.icp
      }
      for (let key in obj) {
        if (
          (Array.isArray(obj[key]) && obj[key].length != 0) ||
          (!Array.isArray(obj[key]) && (obj[key] || obj[key] === 0))
        ) {
          return (this.hightFilterShow = 1)
        } else {
          this.hightFilterShow = 2
        }
      }
    },
    highCheckCancelScan(data) {
      this.formInlineScan = {
        keyword: this.formInlineScan.keyword,
        ...data
      }
      this.currentPage = 1
      this.init()
      let obj = {
        keyword: this.formInlineScan.keyword,
        ip: this.formInlineScan.ip,
        port: this.formInlineScan.port,
        protocol: this.formInlineScan.protocol,
        domain: this.formInlineScan.domain,
        subdomain: this.formInlineScan.subdomain,
        title: this.formInlineScan.title,
        cert: this.formInlineScan.cert,
        icp: this.formInlineScan.icp
      }

      for (let key in obj) {
        if (
          (Array.isArray(obj[key]) && obj[key].length != 0) ||
          (!Array.isArray(obj[key]) && (obj[key] || obj[key] === 0))
        ) {
          return (this.hightFilterShow = 1)
        } else {
          this.hightFilterShow = 2
        }
      }
    },
    // 执行扫描弹层
    async handleScanFun() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      this.isPortScan = false
      this.dialogFormVisibleScan = true
      // 获取端口分组
      this.portLoading = true
      let groupres = await portGroupsNoPage({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.portLoading = false
      })
      if (groupres.data) {
        groupres.data.forEach((item) => {
          if (item.name == '0-65535') {
            this.isScanId = item.id
          }
        })
      }
      this.portLoading = false
      this.selectData.portGroupsNoPageArr = groupres.data
      this.selectData.portGroupsNoPageArr.unshift(
        { id: -1, name: '列表端口' },
        { id: '', name: '自定义' }
      )
      this.ruleFormScan.port_group_ids = -1
      this.ruleFormScan.operate_company_id = this.currentCompany
      this.ruleFormScan.name = `立即推荐-扫描任务(${this.transferTime(new Date())})` // 自动生成任务名称，可以修改
    },
    // 执行扫描
    async handleScanFunSave() {
      if (
        this.ruleFormScan.bandwidth &&
        /^(?:[1-9]\d*)$/.test(this.importRuleForm.bandwidth) == false
      ) {
        this.$message.error('扫描带宽请输入整数')
        return
      }
      let obj = Object.assign({}, this.ruleFormScan)
      if (obj.port_group_ids == '') {
        obj.is_define_port = 1
        obj.define_ports = obj.define_ports
          ? obj.define_ports
              .split(/[；|;|\r\n]/)
              .filter((item) => {
                return item.trim()
              })
              .map((item) => {
                return item.trim()
              })
          : []
        if (obj.define_ports.length == 0) {
          this.$message.error('请输入端口信息')
          return
        }
        if (obj.define_port_protocols.length == 0) {
          this.$message.error('请选择域名')
          return
        }
        if (obj.define_ports.length > 20) {
          this.$message.error('输入的端口信息过多，最多输入20个端口信息')
          return
        }
      } else {
        obj.is_define_port = 0
        obj.define_ports = []
      }
      if (this.user.level == 0 && this.user.role == 3 && obj.port_group_ids == this.isScanId) {
        //扫描端口是全部端口和用户是测试用户时
        this.$message.error('如需下发全部端口扫描，请升级为正式用户！')
        return
      }
      // 是否开启ping识别
      obj.ping_switch = obj.ping_switch ? 1 : 0
      // 是否开启首页截图
      obj.web_logo_switch = obj.web_logo_switch ? 1 : 0
      obj.id = this.checkedArr.map((item) => {
        return item.id
      })
      obj.flag = this.$route.query.flag
      obj.whole = this.checkedAll ? 1 : ''
      if (obj.port_group_ids == -1) {
        obj.port_range = 1
        obj.port_group_ids = ''
      }
      let newObj = {
        ...obj,
        whole: this.checkedAll ? 1 : '',
        ...this.formInlineScan
      }
      this.ruleFormScan.define_ports = ''
      this.ruleFormScan.define_port_protocols = []
      this.btnLoadingScan = true
      let res = await handleScan(newObj).catch(() => {
        this.btnLoading = false
      })
      this.btnLoadingScan = false
      if (res.code == 0) {
        const h = this.$createElement
        let notify = this.$notify({
          title: '操作成功',
          type: 'success',
          duration: 180000,
          offset: 64,
          message: h('p', {}, [
            h('i', {}, `已成功下发扫描任务`),
            h(
              'i',
              { style: 'color: #F8C136' },
              `${res.data.warn_message ? '(' + res.data.warn_message + ')' : ''}`
            ),
            h(
              'u',
              {
                on: {
                  click: () => {
                    this.goTask() // 不能传参，否则会自动执行
                    notify.close()
                  }
                }
              },
              '查看结果'
            )
          ])
        })
        sessionStorage.setItem('tuijianScan', 2)
        this.dialogFormVisibleScan = false
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
    },
    async changePort(val) {
      if (val == '') {
        this.protocoldiaLoadings = true
        let prores = await getPortProList({
          operate_company_id: this.currentCompany
        })
        this.protocoldiaLoadings = false
        this.protocolTransferDatas = prores.data
        this.isPortScan = true
      } else {
        this.isPortScan = false
      }
    },
    goTask() {
      sessionStorage.setItem('menuId', '2-1')
      this.$router.push('/assetsScan')
      this.changeMenuId('2-1')
    }
  },
  beforeDestroy() {}
}
</script>

<style lang="less" scoped>
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.blockBox {
  display: flex;
  flex-direction: column;
}
.redBlock {
  // width: 40px;
  // display: inline-block;
  cursor: pointer;
  width: auto;
  padding: 0 8px;
  height: 24px;
  opacity: 1;
  border-radius: 4px;
  background: rgba(255, 70, 70, 0.12);
  color: rgba(255, 70, 70, 1);
  text-align: center;
  line-height: 24px;
  margin-bottom: 5px;
  &.first {
    width: 70px !important;
  }
  &.second {
    width: 80px !important;
  }
}
.container {
  position: relative;
  width: 100%;
  height: 98% !important;
  // overflow: hidden;
  .downloadClass1 {
    position: absolute;
    top: -41.5px;
    right: 0px;
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
    color: #62666c;

    i {
      font-size: 14px;
      color: #2677ff;
      margin: 0 8px 0 16px;
    }
    .el-icon-close {
      position: absolute;
      right: 10px;
      top: 12px;
      font-size: 16px;
      color: #9fa6af;
      // font-weight: bold;
    }
    span {
      color: #2677ff;
    }
    .task {
      display: inline-block;
      max-width: 186px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #37393c;
    }
  }
  .importDialog {
    display: flex;
    height: 497px;
    .left {
      flex-grow: 1;
    }
    .right {
      width: 55%;
      margin-bottom: 24px;
      margin-left: 33px;
      border-left: 1px solid #e9ebef;
      .uploadIps {
        height: 124px;
        overflow: auto;
        border: 1px solid #e9ebef;
        border-radius: 5px;
        padding: 10px;
        white-space: pre-line;
      }
    }
  }
  .dialog-body {
    /deep/.el-tree {
      width: 100%;
      height: 382px;
      overflow: auto;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d1d5dd;
      margin-bottom: 27px;
      .el-tree-node {
        width: 100%;
        height: 40px;
        background: #ffffff;
        border-bottom: 1px solid #e9ebef;
        .el-tree-node__content {
          height: 40px;
          box-sizing: border-box;
          line-height: 40px;
        }
      }
    }
  }
  .dialog-footer {
    position: relative;
    .resetClass {
      position: absolute;
      left: 16px;
      top: 5px;
      color: #2677ff;
      cursor: pointer;
    }
  }
  #expandClass {
    background: rgba(245, 247, 250, 0);
    border-radius: 2px;
    border: 1px solid #acb4c0;
    color: #62666c;
    z-index: 3;
    cursor: pointer;
    font-size: 8px;
  }
  .deleteClass {
    cursor: pointer;
    border-radius: 2px;
    &:hover {
      color: #2677ff;
    }
  }
  #expandClassBot {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 1px 8px;
    background: rgba(38, 119, 255, 0.18);
    color: #fff;
  }
  .home_header {
    width: 100%;
    height: 100%;
    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        width: 32.5%;
        height: 140px;
        padding: 36px 26px;
        background: #fff;
        p {
          b {
            display: block;
            font-size: 28px;
            font-weight: 500;
            color: #37393c;
            line-height: 40px;
            height: 40px;
            box-sizing: border-box;
          }
          span {
            color: #62666c;
            font-size: 16px;
          }
        }
        svg {
          font-size: 64px;
        }
      }
    }
  }

  // .bot {
  //   width: 100%;
  //   height: calc(100% - 160px);
  //   background: #fff;
  .filterTab {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    & > div {
      .el-input {
        width: 240px;
      }
      .el-select {
        width: 240px;
      }
      & > span {
        font-weight: 400;
        color: #2677ff;
        line-height: 20px;
        // margin-left: 16px;
        cursor: pointer;
      }
    }
  }
  .tableIndex {
    height: 100% !important;
  }
  .tableWrap {
    padding: 0px 20px;
    height: calc(100% - 131px);
    &-with-filter {
      height: calc(100% - 175px);
    }
    .emptyClass {
      height: 100%;
      text-align: center;
      vertical-align: middle;
      svg {
        display: inline-block;
        font-size: 120px;
      }
      p {
        line-height: 25px;
        color: #d1d5dd;
        span {
          margin-left: 4px;
          color: #2677ff;
          cursor: pointer;
        }
      }
    }
  }
  .el-table {
    width: 99%;
    border: 0;
    box-sizing: border-box;
    .tagBox {
      padding: 0 10px;
    }
    .tagBox > .assetsTag:first-child {
      margin-top: 0 !important;
    }
    .assetsTag {
      display: block;
      max-width: 88px;
      margin-top: 5px;
      margin-left: 0;
      img {
        width: 12px;
        margin-right: 2px;
        vertical-align: middle;
      }
    }
    .detail {
      padding: 0 0;
      // height: 55px !important;
      line-height: 40px;
      border-bottom: 1px solid #ebeef5;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      box-sizing: border-box;
      // padding-right: 8px !important;
      // padding-left: 12px !important;
      p {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .hostDetail {
        display: inline-block;
        vertical-align: middle;
        line-height: 32px !important;
        max-width: 160px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .hostDetailHas {
        max-width: 90px;
      }
      .item {
        padding: 0;
      }
    }
    /deep/.el-table__body td.el-table__cell {
      padding: 0 !important;
      .cell {
        padding: 0 !important;
      }
    }
    .detail:last-child {
      border-bottom: 0;
    }
  }
  // }
  .mydialog {
    // padding: 28px 28px 0 28px;
    min-height: 300px;
    max-height: 500px;
    overflow: auto;
  }
}
.assetsTag {
  display: block;
  max-width: 88px;
  margin-top: 5px;
  margin-left: 0;
  img {
    width: 12px;
    margin-right: 2px;
    vertical-align: middle;
  }
}
.el-table {
  width: 99%;
  border: 0;
  .detail {
    padding: 0 0;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ebeef5;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    img {
      vertical-align: middle;
    }
    p {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    div {
      div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  /deep/.el-table__body td.el-table__cell {
    padding: 0 !important;
  }
  /deep/.el-table__body .cell {
    padding: 0 !important;
  }
  .detail:last-child {
    border-bottom: 0;
  }
  .cell-other {
    padding: 0 0;
  }
}
/deep/.el-dialog__header {
  background: #f5f7fa;
  border-bottom: 1px solid #e9ebef;
}
/deep/.el-dialog__footer {
  border-top: 1px solid #e9ebef;
}
.mySelect {
  width: 100%;
  margin-bottom: 16px;
  /deep/.el-select__caret {
    line-height: 32px;
  }
}
/deep/.cell-other > div {
  width: 100%;
  padding: 0 10px;
  overflow: hidden;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}
/deep/.cell-other > span {
  display: block;
  width: 100%;
  padding: 0 10px;
  overflow: hidden;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}
.portBox {
  display: flex;
  justify-content: space-between;
}
/deep/.el-textarea {
  width: 100%;
  height: 415px;
  margin-top: 10px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #d1d5dd;
  .el-textarea__inner {
    height: 100%;
    border: 0 !important;
  }
  .el-textarea__inner:hover {
    border: 0;
  }
}
// /deep/.el-tooltip {
//   display: inline !important;
// }
/deep/.el-transfer {
  margin-top: 10px;
  height: 434px !important;
}
/deep/.el-transfer-panel {
  height: 100% !important;
}

/deep/.el-tabs__nav {
  height: 32px !important;
  border-radius: 4px 4px 0px 0px;
  border: 1px solid #e4e7ed;
  margin-bottom: 0;
  padding-left: 0;
  margin-left: 33px;
  padding-left: 0px !important;
}
/deep/.el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
  height: 32px !important;
  line-height: 32px !important;
  text-align: center;
  background: rgba(38, 119, 255, 0.1);
  border-radius: 0;
  border: 1px solid #2677ff;
  font-weight: bold;
  color: #2677ff;
  background: rgba(38, 119, 255, 0.08);
}
/deep/.el-tabs__active-bar {
  left: 4px;
  height: 0;
  padding: 0 16px;
  background: #2677ff;
}
/deep/.el-tabs__header {
  margin: 0;
}
/deep/.el-tabs__nav-wrap::after {
  height: 0px;
  // background-color: #E9EBEF;
}
/deep/.el-tabs__item {
  padding: 0 16px;
  height: 32px !important;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  font-weight: 400;
  color: #62666c;
  transition: none;
}
/deep/.el-tabs__nav-wrap::after {
  display: none !important;
}
.importDialogs {
  display: flex;
  .left {
    width: 100%;
  }
  /deep/.el-transfer-panel {
    height: 100% !important;
  }
}
.confirmBox {
  margin-right: 16px;
}
/deep/.taskBlue {
  color: #2677ff !important;
  cursor: pointer;
}
/deep/.confirmBox .el-radio-button__inner {
  display: flex;
  align-items: center;
}
// /deep/.el-table .el-table__cell {
//   padding: 12px 12px !important;
// }
/deep/.el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: 1px solid #e1e5ed !important;
}
/deep/.el-table th.el-table__cell::after {
  display: none;
}
.placeholderIdBox {
  position: absolute;
  top: 0px;
  background-color: transparent !important;
  /deep/.el-textarea__inner {
    background-color: transparent !important;
  }
}
.placeholderId {
  margin-top: 10px;
  div {
    min-height: 22px;
    line-height: 22px;
    color: #c0c4cc;
    padding-left: 15px;
  }
}
.productImg {
  max-height: 16px;
  max-width: 16px;
  margin-right: 4px;
  vertical-align: middle;
}
.logoBg img {
  width: 20px;
  height: 20px;
}
.titleImg {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  .el-image {
    width: 30px;
    height: 30px;
  }
}
.el-image {
  display: flex;
  align-items: center;
}
/deep/.el-image__inner {
  max-width: 16px;
  max-height: 16px;
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.detailThree {
  box-sizing: border-box;
  padding: 0 !important;
  .detailThreeItem {
    box-sizing: border-box !important;
    display: flex;
    width: 100%;
    padding: 0 12px 0 8px;
    border-bottom: 1px solid #e1e5ed;
    line-height: 40px;
    height: 40px;
    box-sizing: border-box;
    &:last-child {
      border-bottom: 0;
    }
  }
}
.productImg {
  max-height: 16px;
  max-width: 16px;
  margin-right: 4px;
  vertical-align: middle;
}
.logoBg img {
  width: 20px;
  height: 20px;
}
.titleImg {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  .el-image {
    width: 30px;
    height: 30px;
  }
}
.el-image {
  display: flex;
  align-items: center;
}
/deep/.el-image__inner {
  max-width: 16px;
  max-height: 16px;
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.detailThree {
  box-sizing: border-box;
  padding: 0 !important;
  .detailThreeItem {
    box-sizing: border-box !important;
    display: flex;
    width: 100%;
    padding: 0 12px 0 8px;
    border-bottom: 1px solid #e1e5ed;
    line-height: 40px;
    height: 40px;
    box-sizing: border-box;
    &:last-child {
      border-bottom: 0;
    }
  }
}
.portBox {
  height: 100% !important;
  display: flex;
  align-items: center;
  &:hover,
  &:focus,
  &:active {
    .tagName {
      display: block;
    }
    .delBtn {
      display: block;
    }
  }
}
.detailThreeItemSpecial {
  display: flex;
  width: 100%;
}
// .account_import{
//   margin: 0 20px;
// }
.import {
  margin: 0 12px;
}
.indexImg {
  width: 12px;
  height: 14px;
}
.indexTag {
  // padding: 0 !important;
  width: 100%;
  font-size: 10px;
}
.index {
  margin-top: 30px;
}
#account_filter {
  margin-left: 16px;
}
.tagName {
  display: none;
  &:hover {
    color: #2677ff;
  }
}
.delBtn {
  display: none;
  &:hover {
    color: #2677ff;
  }
}
.tagDialogs {
  .diaTip {
    height: 32px;
    line-height: 32px;
    margin-bottom: 16px;
    color: red;
  }
  .dialog-item {
    display: flex;
    justify-content: start;
    align-items: start;
    margin-bottom: 20px;
    .label {
      line-height: 30px;
    }
    .value {
      flex: 1;
    }
  }
}
.ipContent {
  display: flex;
  align-items: center;
}
.defaultTags {
  min-width: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
