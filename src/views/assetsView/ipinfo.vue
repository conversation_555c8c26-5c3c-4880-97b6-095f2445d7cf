<template>
  <div class="containerInfo">
    <div class="headerTitle">
      <!-- <span class="goback" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i>返回上一层</span>
      <span class="spline">/</span> -->
      <span>{{ headerTitle }}</span>
    </div>
    <div class="home_header" v-loading="loading">
      <div class="tableWrap">
        <div class="ipHead">
          <img src="@/assets/images/ip.png" alt="" class="icon" />
          <span class="title">{{ ipInfoData.ip }}</span>
          <span class="greenLine" v-if="ipInfoData.online_state == 1">在线</span>
          <span class="grayLine" v-else>离线</span>
          <span class="grayLine" v-if="ipInfoData.is_shadow == 1">影子资产</span>

          <el-button
            v-if="
              (!$route.query.userId && fromAccountInfo) ||
              ($route.query.userId && $route.query.userId == currentCompanyId)
            "
            style="margin-left: 20px"
            @click="edit"
            type="text"
            size="small"
            >编辑</el-button
          >
        </div>
        <div class="module hardInfo">
          <div class="moduleTitle">物理信息</div>
          <div class="moduleContent">
            <el-descriptions
              class="cardLeft"
              :column="2"
              :colon="false"
              label-class-name="desc-label"
              content-class-name="desc-content"
            >
              <el-descriptions-item v-for="item in infoHeader" :key="item.name" :label="item.label">
                <el-tooltip
                  v-if="getTableItem(ipInfoData[item.name])"
                  class="item"
                  effect="dark"
                  :content="getTableItem(ipInfoData[item.name])"
                  placement="top"
                  :open-delay="500"
                >
                  <span class="ellipsis">{{ getTableItem(ipInfoData[item.name]) }}</span>
                </el-tooltip>
              </el-descriptions-item>
            </el-descriptions>
            <div class="cardRight">
              <div class="mapMask"></div>
              <div v-if="showGeolocation" id="mapWrap" style="width: 100%; height: 100%"></div>
              <div v-else class="emptyClass">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-kong"></use>
                </svg>
                <p>暂无地理位置信息</p>
              </div>
            </div>
          </div>
        </div>
        <div class="module networkInfo">
          <div class="moduleTitle">网络信息</div>
          <div class="moduleContent">
            <el-row :gutter="12" type="flex">
              <el-col :span="40">
                <iptable
                  :vulnsList="vulnsList"
                  :threatsData="threatsData"
                  :tableData="tableData"
                  :ruleTags="ruleTags"
                  :ruleTagsNum="ruleTagsNum"
                  :fromPath="fromAccountInfo"
                />
              </el-col>
              <el-col :span="60">
                <ipdraw :treeData="treeData" />
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="module">
          <div class="moduleTitle">关联分析</div>
          <div class="moduleContentBot">
            <!-- <el-row :gutter="12" type="flex">
              <el-col :span="60">
                <ipchain :chain_list="chain_list" />
                <ipinfo-poc-hole v-if="fromAccountInfo" :threatsData="threatsData"></ipinfo-poc-hole>
              </el-col>
              <el-col :span="40">
                <iphistory v-if="fromAccountInfo" :timeLineData="timeLineData"/>
              </el-col>
            </el-row> -->
            <el-row v-if="fromAccountInfo" :gutter="14" type="flex">
              <el-col :span="60">
                <ipchain :chain_list="chain_list" />
                <ipinfo-poc-hole :threatsData="threatsData"></ipinfo-poc-hole>
              </el-col>
              <el-col :span="40">
                <iphistory :timeLineData="timeLineData" />
                <DomainHistory
                  :timeLineData="historyTimeLineData"
                  :domainHistoryLoading="domainHistoryLoading"
                />
              </el-col>
            </el-row>
            <el-row v-else :gutter="12" type="flex">
              <el-col :span="24">
                <ipchain :chain_list="chain_list" />
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <!-- 编辑弹框 -->
      <el-dialog
        class="elDialogAdd"
        :close-on-click-modal="false"
        title="编辑"
        :visible.sync="dialogVisible"
        width="560px"
      >
        <div class="mydialog">
          <el-form
            :model="ruleForm"
            :rules="editRules"
            ref="ruleForm"
            label-width="100px"
            class="demo-ruleForm"
          >
            <el-form-item label="企业名称" prop="clue_company_name">
              <el-input
                v-model="ruleForm.clue_company_name"
                placeholder="请输入企业名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="地理位置" prop="province">
              <el-input v-model="ruleForm.province" placeholder="请输入地理位置"></el-input>
            </el-form-item>
            <el-form-item label="经度" prop="lon">
              <el-input v-model="ruleForm.lon" placeholder="请输入经度"></el-input>
            </el-form-item>
            <el-form-item label="纬度" prop="lat">
              <el-input v-model="ruleForm.lat" placeholder="请输入纬度"></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="online_state">
              <el-radio-group v-model="ruleForm.online_state">
                <el-radio :label="1">在线</el-radio>
                <el-radio :label="0">离线</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="是否影子资产"
              prop="online_state"
              v-if="$route.query.preList == '/assetsLedger'"
            >
              <el-radio-group v-model="ruleForm.is_shadow">
                <el-radio :label="2">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="内置标签" prop="tagname">
              <el-select multiple clearable v-model="tagName" placeholder="请选择">
                <el-option
                  v-for="item in tagOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="tagName.indexOf(1) != -1" label="云厂商名称" prop="tagname">
              <el-input v-model="ruleForm.cloud_name" placeholder="请输入云厂商名称"></el-input>
            </el-form-item>
            <el-form-item label="自定义标签" prop="customer_tags">
              <tagsInput
                :parentTags="ruleForm.customer_tags"
                @onChange="changeDefaultTag"
              ></tagsInput>
            </el-form-item>
            <el-form-item label="威胁类型" prop="" v-if="$route.query.preList == '/threatAssets'">
              <el-select
                filterable
                v-model="ruleForm.threaten_type"
                placeholder="请选择"
                value-key="id"
              >
                <el-option
                  v-for="item in threaten_type_arr"
                  :key="item.id"
                  :label="item.type_name"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="highBtnRe" @click="dialogVisible = false">关闭</el-button>
          <el-button class="highBtn" type="primary" :loading="editBtnLoading" @click="editSave"
            >确定</el-button
          >
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import iptable from './ipinfo_table.vue'
import iphistory from './ipinfo_history.vue'
import ipdraw from './ipinfo_draw.vue'
import ipchain from './ipinfo_chain.vue'
import ipchainEchart from './ipinfo_chain_echart.vue'
import IpinfoPocHole from './ipinfo_poc_hole.vue'
import DomainHistory from './domain_history.vue'
import L from 'leaflet'
import { mapGetters, mapState } from 'vuex'
import tagsInput from './tagsInput.vue'
import { pgdTypeList } from '@/api/apiConfig/api.js'
import { ipDetailHistory, ipDetail } from '@/api/apiConfig/discovery.js'
import { ipinfoVulnsList, editStandingDataV1 } from '@/api/apiConfig/asset.js'

export default {
  components: {
    tagsInput,
    iphistory,
    iptable,
    ipchain,
    ipdraw,
    ipchainEchart,
    IpinfoPocHole,
    DomainHistory
  },
  data() {
    return {
      defaultTags: [],
      parentTags: [],
      threaten_type_arr: [],
      timer: null,
      labeledValueList: [
        // 钓鱼仿冒、ICP盗用、黄赌毒网站、其他类型
        {
          name: '其他类型',
          value: '0'
        },
        {
          name: '钓鱼仿冒',
          value: '1'
        },
        {
          name: '黄赌毒网站',
          value: '2'
        },
        {
          name: 'ICP盗用',
          value: '3'
        },
        {
          name: '域名混淆',
          value: '4'
        }
      ],
      domainHistoryLoading: false,
      historyTimeLineData: [],
      showGeolocation: true,
      tagOptions: [
        {
          label: '云厂商',
          value: 1
        },
        {
          label: 'CDN',
          value: 2
        }
      ],
      tagName: [],
      loading: false,
      fromAccountInfo: false,
      dialogVisible: false,
      timeLineData: [],
      vulnsList: [],
      treeData: [],
      ruleForm: {
        clue_company_name: '',
        province: '',
        lat: '',
        lon: '',
        online_state: 1
      },
      editRules: {
        lon: [
          {
            message: '请输入正确的经度',
            pattern: /^[-+]?((180(\.0+)?)|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/,
            trigger: 'blur'
          }
        ],
        lat: [
          {
            message: '请输入正确的纬度',
            pattern: /^[-+]?((90(\.0+)?)|(([1-8]?\d)(\.\d+)?))$/,
            trigger: 'blur'
          }
        ]
      },
      chain_list: [],
      threatsData: [],
      tableData: [],
      ruleTags: [],
      ruleTagsNum: 0,
      infoHeader: [
        {
          label: '企业名称：',
          name: 'clue_company_name',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '运营商：',
          name: 'isp',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '域名：',
          name: 'hosts',
          icon: 'input',
          detailIs: 1
        },
        {
          label: 'ASN：',
          name: 'asn',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '经度：',
          name: 'lon',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '纬度：',
          name: 'lat',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '地理位置：',
          name: 'province',
          icon: 'select',
          detailIs: 1
        }
      ],
      portGroupsNoPageArr: [],
      formInline: {
        rank: '',
        port: ''
      },
      ipDetailData: {},
      ipInfoData: {
        online_state: 1
      },
      zSmap: null,
      addIsTrue: true,
      user: null,
      editBtnLoading: false
    }
  },
  watch: {
    getterCompanyChange(val) {
      if (this.$route.query && this.$route.query.fromIcon == 1) {
        // 查看列表-安服账号切换企业需要回到任务扫描列表
        this.$router.replace('/assetsScan')
      } else {
        this.$router.go(-1) // 其他页面ip详情，安服账号切换企业需要回到一级页面
      }
    },
    getterCurrentCompany() {
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      if (this.user.role == 2) {
        this.getTaskResultData()
      } else {
        this.getTaskResultData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange', 'currentCompanyId']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
    // showGeolocation() {
    //   return this.ipInfoData.lat && this.ipInfoData.lon
    // }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.headerTitle = this.$route.name
    if (this.$route.query.fromIcon == 1) {
      // 资产扫描任务ip详情
      this.fromAccountInfo = false
    } else {
      // 台账ip详情
      this.fromAccountInfo = true
    }
  },
  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getTaskResultData()
    } else {
      this.getTaskResultData()
    }
  },
  methods: {
    changeDefaultTag(val) {
      this.defaultTags = val
    },
    getTableItem(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length > 0) {
            return item.join('，')
          } else {
            return '-'
          }
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    async editSave() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          if (this.tagName.indexOf(1) == -1) {
            this.ruleForm.cloud_name = ''
          } else if (!this.ruleForm.cloud_name) {
            this.$message.error('请填写云厂商名称')
            return
          }
          this.ruleForm.is_cdn = this.tagName.indexOf(2) != -1 ? true : false // this.tagName包含2，代表选择了CDN
          let obj = JSON.parse(JSON.stringify(this.ruleForm))
          obj.cloud_name = [this.ruleForm.cloud_name]
          obj.threaten_type = this.ruleForm.threaten_type.id
          obj.threaten_type_name = this.ruleForm.threaten_type.type_name
          obj.customer_tags = this.defaultTags
          this.editBtnLoading = true
          this.loading = true

          // obj.cloud_name = ''+this.ruleForm.threaten_type
          let res = await editStandingDataV1({
            ...obj,
            is_risk: this.$route.query.preList == '/threatAssets' ? 1 : 0
          }).catch(() => {
            this.loading = false
            this.editBtnLoading = false
          })
          if (res.code == 0) {
            this.$message.success('操作成功')
            this.dialogVisible = false
            this.timer = setTimeout(() => {
              this.getTaskResultData()
            }, 1500)
          }
          this.editBtnLoading = false
        }
      })
    },
    // 编辑
    async edit() {
      this.dialogVisible = true
      console.log(this.ipDetailData, '--ipDetailData')
      let copyForm = JSON.parse(JSON.stringify(this.ipDetailData))
      this.ruleForm = {
        clue_company_name: '',
        province: '',
        lat: '',
        lon: '',
        online_state: '',
        is_cdn: '',
        cloud_name: '',
        threaten_type: '',
        is_shadow: 2
      }
      for (let k in this.ruleForm) {
        this.ruleForm[k] = copyForm[k]
      }
      this.ruleForm.clue_company_name = this.ruleForm.clue_company_name.join(',')
      this.ruleForm.cloud_name =
        this.ipDetailData.cloud_name && this.ipDetailData.cloud_name.length > 0
          ? this.ipDetailData.cloud_name[0]
          : ''
      this.ruleForm.lat = this.ipDetailData.geo && this.ipDetailData.geo.lat
      this.ruleForm.lon = this.ipDetailData.geo && this.ipDetailData.geo.lon
      this.ruleForm.customer_tags = this.ipDetailData.customer_tags
      this.ruleForm.province = this.ipDetailData.geo && this.ipDetailData.geo.province
      this.ruleForm.id = this.$route.query.id
      this.ruleForm.operate_company_id = this.currentCompany
      this.ruleForm.threaten_type = this.ipDetailData.threaten_type + ''
      if (this.$route.query.preList == '/assetsLedger') {
        this.ruleForm.is_shadow = this.ipDetailData.is_shadow == 1 ? 1 : 2
      }
      this.tagName = []
      if (this.ruleForm.is_cdn) {
        // 选中CDN
        this.tagName.push(2)
      }
      if (this.ruleForm.cloud_name) {
        // 选中云厂商
        this.tagName.push(1)
      }
      let res1 = {}
      res1 = await pgdTypeList({ operate_company_id: this.currentCompany })
      if (res1.code == 0) {
        this.threaten_type_arr = res1.data.items
      }
      this.ruleForm.threaten_type = { id: Number(this.ipDetailData.threaten_type) }

      // this.ruleForm.threaten_type = this.ipDetailData.threaten_type+''
    },
    async getVuleList(ip) {
      let res = ipinfoVulnsList({
        ip: ip,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.vulnsList = res.data && res.data.items ? res.data.items : []
      }
    },
    getLevelName(level) {
      let name = ''
      if (level == 1) {
        name = '硬件层'
      } else if (level == 2) {
        name = '系统层'
      } else if (level == 3) {
        name = '服务层'
      } else if (level == 4) {
        name = '支撑层'
      } else if (level == 5) {
        name = '业务层'
      }
      return name
    },
    async getTaskResultData() {
      const { id, fromIcon, userId, preList } = this.$route.query
      let obj = {
        id: id,
        operate_company_id: this.currentCompany,
        from: fromIcon,
        user_id: userId,
        is_from_search: preList == '/spatialRetrieval' ? '1' : ''
      }
      this.loading = true
      let res = await ipDetail(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.ipDetailData = res.data
      if (!res.data.geo || !res.data.geo.lat || !res.data.geo.lon) {
        this.showGeolocation = false
      } else {
        this.setMap(res.data.geo.lat, res.data.geo.lon)
      }
      // 树图数据结构处理
      this.treeData = [
        {
          name: res.data.ip,
          collapsed: false,
          level: 1,
          children: []
        }
      ]
      let that = this
      let portList = []
      if (res.data.port_list) {
        res.data.port_list.forEach((item, index) => {
          portList.push({
            name: item.port + (item.protocol ? '/' + item.protocol : ''),
            collapsed: false,
            level: 2,
            children: []
          })
          if (item.rules) {
            let levelArr = [
              ...new Set(
                item.rules.map((ru) => {
                  return ru.level
                })
              )
            ] // 获取组件类型
            let ruleArr = [] // 处理好的组件数组
            levelArr.forEach((level, levelIndex) => {
              // 循环组件类型
              ruleArr.push({
                name: that.getLevelName(level),
                is_level: 1,
                collapsed: false,
                level: 3,
                children: []
              })
              item.rules.forEach((ru) => {
                if (level == ru.level) {
                  ruleArr[levelIndex].children.push({
                    name: ru.cn_product,
                    level: 4,
                    is_rule: 1
                  })
                }
              })
            })
            portList[index].children = ruleArr
          }
        })
      }
      this.treeData[0].children = portList
      // 线索连数据处理
      let ipInfoData = Object.assign({}, res.data.geo) // IP信息
      if (res.data.chain_list) {
        this.chain_list = []
        res.data.chain_list.forEach((item) => {
          if (item) {
            let arr = []
            item.forEach((ch) => {
              if (ch && ch.content) {
                arr.push({
                  type: ch.type,
                  content: ch.content
                })
              }
            })
            this.chain_list.push(arr)
          }
        })
      }
      ipInfoData.ip = res.data.ip
      ipInfoData.online_state = res.data.online_state
      ipInfoData.clue_company_name = res.data.clue_company_name.join(',')
      ipInfoData.province = res.data.geo ? res.data.geo.province : ''
      ipInfoData.hosts = res.data.hosts
      ipInfoData.is_shadow = res.data.is_shadow == 1 ? 1 : 2

      this.ipInfoData = ipInfoData
      this.tableData = res.data.port_list ? res.data.port_list : [] // 端口信息
      if (this.tableData) {
        this.tableData.forEach((val, index) => {
          val.id = index
        })
      }
      this.threatsData = res.data.threats ? res.data.threats : []
      let rule_infos = {
        rule1: [],
        rule2: [],
        rule3: [],
        rule4: [],
        rule5: [],
        rule6: []
      }
      if (res.data.rule_tags) {
        res.data.rule_tags.forEach((ru) => {
          if (ru.level == '1') {
            rule_infos.rule1.push(ru.cn_product)
          } else if (ru.level == '2') {
            rule_infos.rule2.push(ru.cn_product)
          } else if (ru.level == '3') {
            rule_infos.rule3.push(ru.cn_product)
          } else if (ru.level == '4') {
            rule_infos.rule4.push(ru.cn_product)
          } else if (ru.level == '5') {
            rule_infos.rule5.push(ru.cn_product)
          } else if (!ru.level) {
            // 资产导入
            rule_infos.rule6.push(ru.cn_product)
          }
        })
        this.ruleTagsNum = res.data.rule_tags.length
      }
      this.ruleTags = rule_infos
      this.timeLineData = res.data.histyory
      this.total = res.data.total
      this.getIpDetailHistory()
    },
    async getIpDetailHistory(form) {
      this.domainHistoryLoading = true
      const { id, fromIcon, userId, preList } = this.$route.query
      let obj = {
        id: id,
        operate_company_id: this.currentCompany,
        from: fromIcon,
        user_id: userId,
        is_from_search: preList == '/spatialRetrieval' ? '1' : ''
      }
      let res = await ipDetailHistory(obj).catch(() => {
        this.domainHistoryLoading = false
      })
      if (res.code == 0) {
        this.historyTimeLineData = res.data && res.data[0] && res.data[0].items
        this.domainHistoryLoading = false
      } else {
        this.domainHistoryLoading = false
      }
    },
    //地图
    setMap(l, r) {
      if (this.zSmap) {
        // 清空实例
        this.zSmap.off()
        this.zSmap.remove()
      }
      this.zSmap = L.map('mapWrap', {
        maxZoom: 12,
        center: [l, r],
        zoom: 6,
        zoomControl: false,
        attributionControl: false,
        crs: L.CRS.EPSG3857
      })
      const basicBeachIcon = L.icon({
        iconUrl: require('../../assets/images/mapLine.png'),
        iconSize: [40, 40]
      })
      L.tileLayer(
        'http://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}'
      ).addTo(this.zSmap)
      L.marker([l, r], { icon: basicBeachIcon }).addTo(this.zSmap)
    },
    handleNodeClick(node) {},
    highCheck() {},
    resetForm() {
      this.formInline = {
        rank: '',
        port: ''
      }
    },
    exportWord() {}
  },

  beforeDestroy() {
    clearTimeout(this.timer)
  }
}
</script>

<style lang="less" scoped>
@module-card-header-hight: 45px;
@module-card-content-vertical-padding: 16px;

.containerInfo /deep/ {
  position: relative;
  width: 100%;
  height: 100%;
  // background: #fff;
  .headerTitle {
    padding: 0 16px;
  }
  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left {
      height: 100%;
      display: flex;
      /deep/.el-textarea {
        width: 300px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }
  }
  /deep/.home_header {
    // position: relative;
    height: 100%;
    overflow: auto;

    .el-tabs__nav {
      position: relative;
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      // width: 0 !important;
      padding: 0 16px;
      // padding: 0;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
      background: #fff;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      // padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0 16px 15px;
      margin-bottom: 20px;
      & > div {
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrap {
      height: 100%;
      overflow: auto;
      padding: 0 16px;

      .childwrap {
        border-radius: 4px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      }
      .infoTitle {
        display: flex;
        align-items: center;
        height: 44px;
        line-height: 44px;
        padding: 0px 16px;
        background-color: transparent;
      }
      .ipHead {
        display: flex;
        align-items: center;
        line-height: 22px;
        margin-bottom: 20px;
        .icon {
          width: 24px;
          height: 22px;
        }
        .title {
          margin-left: 8px;
        }
        .el-button {
          padding-top: 0;
          padding-bottom: 0;
        }
      }
    }
    .el-table {
      border: 0;
    }
  }
}
.ipInfo > li > p:last-child {
  margin-bottom: 0px !important;
}
.tiemBox {
  color: #62666c;
  .tiemContent {
    color: #37393c;
    margin-left: 16px;
  }
}

::v-deep .module {
  & + .module {
    margin-top: 28px;
  }
  &.hardInfo {
    .moduleContent {
      display: flex;
      height: 228px;
      border-radius: 4px;
      background: linear-gradient(270deg, #ffffff 50.53%, #f0f6ff 100%), #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      & > div {
        width: 50%;
        height: 100%;
      }
      .cardLeft {
        width: 50%;
        padding: 24px 16px;
        box-sizing: border-box;
        &.el-descriptions {
          :not(.is-bordered) .el-descriptions-item__cell {
            padding-bottom: 20px;
            line-height: 24px;
          }
          .el-descriptions__body {
            background-color: transparent;
            .desc-label {
              width: 70px;
            }
            .desc-content {
              color: #37393c;
              overflow: hidden;
            }
          }
        }
        .ellipsis {
          max-width: 88%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          display: initial;
        }
      }
      .cardRight {
        width: 50%;
        position: relative;
        .mapMask {
          position: absolute;
          top: 0;
          left: 0;
          width: 150px;
          height: 100%;
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 1) 0%,
            rgba(255, 255, 255, 0) 100%
          );
          z-index: 3;
        }
        #mapWrap {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
        .emptyClass {
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, #ffffff 0%, #f0f6ff 100%);
          backdrop-filter: blur(8px);
          svg {
            display: inline-block;
            font-size: 90px;
          }
          p {
            margin-top: 12px;
            color: #62666c;
          }
        }
      }
    }
  }
}
.moduleTitle {
  position: relative;
  padding: 0 10px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: block;
    height: 20px;
    width: 4px;
    background-color: #2677ff;
  }
}
.moduleContent {
  height: 601px;
  overflow: hidden;
  .el-row {
    height: 100%;
    .el-col {
      height: 100%;
    }
  }
}
.moduleContentBot {
  overflow: hidden;
  .el-row {
    height: 100%;
  }
}
.moduleCard {
  height: 100%;
  border-radius: 4px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
::v-deep .cardContent {
  height: calc(100% - @module-card-header-hight - @module-card-content-vertical-padding * 2);
  padding: 16px;
  overflow: auto;
}

.el-row {
  .el-col-40 {
    width: 40%;
  }
  .el-col-60 {
    width: 60%;
  }
}
</style>
