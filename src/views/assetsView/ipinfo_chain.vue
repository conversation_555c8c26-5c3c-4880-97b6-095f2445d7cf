<template>
  <div class="moduleCard chainWrap">
    <div class="infoTitle"><i></i>线索链</div>
    <div class="cardContent">
      <ul class="chainClass_info">
        <li v-for="(chain, chainIndex) in chain_list" :key="chainIndex">
          <span class="xuhao">{{ chainIndex + 1 }}</span>
          <div>
            <span v-for="(con, index) in chain" :key="index">
              <span v-if="con.type && con.type == 3">
                <span :class="getClueTypeClass(con.type)">{{ getClueType(con.type) }}</span>
                <el-image
                  class="imgwrap"
                  :src="con.content.includes('http') ? con.content : showSrcIp + con.content"
                  alt=""
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </span>
              <span v-else :class="!con.type && con.type != 0 ? 'chain chainNoType' : 'chain'"
                ><span :class="getClueTypeClass(con.type)">{{ getClueType(con.type) }}</span
                >{{ con.content || con }}</span
              >
              <img
                class="arrowClass"
                v-if="index < chain.length - 1"
                src="../../assets/images/clueArrow.png"
                alt=""
              />
            </span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
export default {
  props: ['chain_list'],
  data() {
    return {
      loadingFl: false
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getInit()
      }
    },
    chain_list() {
      this.getInit()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.getInit()
  },
  methods: {
    getClueTypeClass(type) {
      let className = ''
      if (type == 0) {
        className = 'clueType domainClass'
      } else if (type == 1) {
        className = 'clueType certClass'
      } else if (type == 2) {
        className = 'clueType icpClass'
      } else if (type == 3) {
        className = 'clueType iconClass'
      } else if (type == 4) {
        className = 'clueType keyClass'
      } else if (type == 6) {
        className = 'clueType ipClass'
      }
      return className
    },
    getClueType(type) {
      let name = ''
      if (type == 0) {
        name = '域名'
      } else if (type == 1) {
        name = '证书'
      } else if (type == 2) {
        name = 'ICP'
      } else if (type == 3) {
        name = 'ICON'
      } else if (type == 4) {
        name = '关键词'
      } else if (type == 6) {
        name = 'IP'
      }
      return name
    },
    async getInit() {}
  }
}
</script>
<style lang="less" scoped>
.moduleCard.chainWrap {
  height: 202px;
  margin-bottom: 12px;
  overflow-y: auto;
}
.chainWrap {
  .chainClass_info {
    img {
      margin-bottom: 0 !important;
    }
    .el-image {
      vertical-align: middle !important;
    }
    li {
      padding: 5px 0;
      display: flex;
      .xuhao {
        width: 18px;
        height: 18px;
        border-radius: 2px;
        background: rgba(38, 119, 255, 0.12);
        border: 1px solid rgba(38, 118.99999999999994, 255, 1);
        line-height: 18px;
        text-align: center;
        margin-right: 10px;
        margin-top: 5px;
        color: rgba(38, 119, 255, 1);
      }
      .clueType {
        display: inline-block;
        padding: 5px 5px;
        margin: 0 8px 0 1px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 2px 0px 4px 0px rgba(0, 0, 0, 0.08);
      }
      .domainClass {
        color: #2677ff;
      }
      .certClass {
        color: #13b7ff;
      }
      .icpClass {
        color: #05d4a7;
      }
      .iconClass {
        color: #ec8f3c;
      }
      .keyClass {
        color: #ec5f5c;
      }
      .ipClass {
        color: #5346ff;
      }
      .chain {
        display: inline-block;
        margin-bottom: 5px;
        padding-right: 10px;
        background: #ffffff;
        border-radius: 34px;
        color: #62666c;
        border: 1px solid #dfe4ed;
      }
      .chainNoType {
        padding: 3px 10px;
      }
      .arrowClass {
        vertical-align: middle;
        margin: 0 8px;
      }
    }
  }
}
</style>
