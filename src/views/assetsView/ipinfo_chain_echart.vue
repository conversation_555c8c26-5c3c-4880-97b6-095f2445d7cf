<template>
  <div class="childwrap chainWrap">
    <div class="infoTitle"><i></i>线索类型分布图</div>
    <div id="chart_chain"></div>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
export default {
  props: ['chain_list'],
  data() {
    return {
      loadingFl: false
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getInit()
      }
    },
    chain_list() {
      this.getInit()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.chart_chain = this.$echarts.init(document.getElementById('chart_chain'))
    this.optionsArr = {
      color: [
        'rgba(38, 119, 255, 1)',
        'rgba(38, 119, 255, 0.1)',
        'rgba(38, 119, 255, 1)',
        'rgba(38, 119, 255, 1)',
        'rgba(38, 119, 255, 1)'
      ],
      series: {
        type: 'sunburst',
        data: [
          {
            name: '',
            children: [
              {
                name: '',
                children: [
                  {
                    name: '域名',
                    value: 11
                  }
                ]
              }
            ]
          },
          {
            name: '',
            children: [
              {
                name: '',
                children: [
                  {
                    name: '证书',
                    value: 11
                  }
                ]
              }
            ]
          },
          {
            name: '',
            children: [
              {
                name: '',
                children: [
                  {
                    name: 'ICP',
                    value: 11
                  }
                ]
              }
            ]
          },
          {
            name: '',
            children: [
              {
                name: '',
                children: [
                  {
                    name: 'ICON',
                    value: 11
                  }
                ]
              }
            ]
          },
          {
            name: '',
            children: [
              {
                name: '',
                children: [
                  {
                    name: '关键词',
                    value: 11
                  }
                ]
              }
            ]
          },
          {
            name: '',
            children: [
              {
                name: '',
                children: [
                  {
                    name: 'IP',
                    value: 11
                  }
                ]
              }
            ]
          }
        ],
        radius: ['10%', '90%'],
        label: {
          rotate: 0
        },
        nodeClick: false, // 禁止点击
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 6,
          borderRadius: 6
        },
        emphasis: {
          focus: 'none'
        },
        levels: [
          {
            // 留给数据下钻点的空白配置
            itemStyle: {
              color: '#fff'
            },

            radius: ['10%', '23%']
          },
          {
            radius: ['27%', '45%']
          },
          {
            radius: ['48%', '66%'],
            itemStyle: {
              opacity: 0.95
            }
          },
          {
            radius: ['70%', '90%'],

            label: {
              position: 'outside',

              distance: 12
            },
            itemStyle: {
              opacity: 0.9
            },
            downplay: {
              label: {
                opacity: 0.5
              }
            }
          }
        ]
      }
    }

    this.getInit()
  },
  methods: {
    async getInit() {
      let chain_list = []
      this.chain_list.forEach((item) => {
        if (item.length >= 2) {
          chain_list.push(item[item.length - 2].type)
        } else {
          chain_list.push(item.type)
        }
      })
      let colorArr = [
        'rgba(38, 119, 255, 0.1)',
        'rgba(38, 119, 255, 0.1)',
        'rgba(38, 119, 255, 0.1)',
        'rgba(38, 119, 255, 0.1)',
        'rgba(38, 119, 255, 0.1)',
        'rgba(38, 119, 255, 0.1)'
      ]
      let levelArr = [0, 1, 2, 3, 4, 6]
      let newArr = [...new Set(chain_list)]
      newArr.forEach((item) => {
        levelArr.forEach((le, leindex) => {
          if (item == le) {
            colorArr[leindex] = 'rgba(38, 119, 255, 1)'
          }
        })
      })
      this.optionsArr.color = colorArr
      this.pie('chart_chain', 'two')
    },
    getClueType(type) {
      let name = ''
      if (type == 0) {
        name = '域名'
      } else if (type == 1) {
        name = '证书'
      } else if (type == 2) {
        name = 'ICP'
      } else if (type == 3) {
        name = 'ICON'
      } else if (type == 4) {
        name = '关键词'
      } else if (type == 6) {
        name = 'IP'
      }
      return name
    },
    pie(id, options) {
      this.chart_chain.clear()
      this.chart_chain.setOption(this.optionsArr, true)
      window.addEventListener('resize', () => {
        this.chart_chain.resize()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.chainWrap {
  width: 100%;
  height: 382px;
  #chart_chain {
    height: calc(100% - 75px);
  }
}
</style>
