<template>
  <div class="moduleCard drawWrap">
    <div class="infoTitle"><i></i>IP画像</div>
    <div class="cardContent">
      <div v-loading="loadingFl" id="chart_tree"></div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
export default {
  props: ['treeData'],
  data() {
    return {
      loadingFl: false,
      ruleArr2: [],
      ruleArr3: [],
      ruleArr4: []
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getInit()
      }
    },
    treeData() {
      this.getInit()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    let that = this
    this.chart_tree = this.$echarts.init(document.getElementById('chart_tree'))
    this.optionsArr = {
      series: [
        {
          type: 'tree',
          data: [],
          top: '1%',
          left: '22%',
          bottom: '1%',
          right: '22%',
          symbolSize: [15, 15],
          symbolOffset: [5, 0],
          edgeShape: 'polyline', // polyline，curve
          edgeForkPosition: '10%', // 线与节点距离
          animationDuration: 0,
          symbol: function (params, params1, params2) {
            //加号
            let svgAdd = 'image://' + require('../../assets/images/ipdraw_plus.png')
            //减号
            let svgD = 'image://' + require('../../assets/images/ipdraw_sub.png')
            // 节点标识
            if (!params1.data.children || params1.data.children.length == 0) {
              if (params1.data.is_rule && params1.data.name) {
                // is_rule组件要展示图片
                if (that.showimg(params1.data.name).result) {
                  return 'image://' + that.showimg(params1.data.name).url
                } else {
                  // 组件图片不存在
                  return 'none'
                }
              } else {
                return 'none'
              }
            } else {
              //节点没有展开，显示+号图标
              if (params1.collapsed) {
                return svgAdd
              } else {
                return svgD
              }
            }
          },
          initialTreeDepth: 4,
          lineStyle: {
            color: '#92BBFF'
          },
          roam: 'move',
          leaves: {
            // 收缩后末级节点与文字位置
          },
          label: {
            position: 'left',
            verticalAlign: 'middle',
            align: 'right',
            distance: 0,
            formatter: function (params) {
              if (params.data.level === 1) {
                return '{a|' + params.name + '}'
              } else if (params.data.level === 2) {
                return '{b|' + params.name + '}'
              } else if (params.data.level === 3) {
                return '{c|' + params.name + '}'
              } else {
                return '{d|' + params.name + '}'
              }
            },
            rich: {
              a: {
                backgroundColor: '#2677ff',
                borderColor: '#2677ff',
                borderWidth: 0.5,
                borderRadius: 12,
                padding: [5, 8],
                color: '#fff'
              },
              b: {
                backgroundColor: '#E6EEFF',
                borderColor: '#2677FF',
                color: '#2677FF',
                borderWidth: 1,
                borderRadius: 12,
                padding: [5, 8]
              },
              c: {
                color: 'rgba(98, 102, 108, 1)',
                fontSize: '12',
                borderWidth: 0.5,
                borderRadius: 12,
                padding: [5, 8],
                backgroundColor: '#EBEFF6',
                borderColor: 'rgba(213, 222, 232, 1)'
              },
              d: {
                color: '#37393C',
                borderWidth: 0.5,
                borderRadius: 12,
                lineHeight: 40,
                padding: [5, 8],
                backgroundColor: '#fff',
                borderColor: '#D1D5DD'
              }
            }
          },
          emphasis: {
            focus: 'none'
          },
          expandAndCollapse: true,
          animationDuration: 550,
          animationDurationUpdate: 750
        }
      ]
    }
  },
  methods: {
    showimg(url) {
      let imgUrl = ''
      url = url.replace('/', '')
      url = url.replace('.', '')
      url = url.replace(' ', '')
      let result = false
      url = './' + url + '.png'
      let urlTmp = url
      urlTmp = urlTmp.replace('./', '').replace('.png', '')
      // 目录下所有文件名
      const files = require.context('../../assets/images/componentIcon/', false, /\.png$/)
      const filedata = files.keys()
      filedata.forEach((item) => {
        if (item === url) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp + '.png')
        } else if (item === url.toLowerCase()) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp.toLowerCase() + '.png')
        }
      })
      // 返回结果
      return {
        result,
        url: imgUrl
      }
    },
    async getInit() {
      this.treeData.forEach((item) => {
        item.label = {
          // 第一级节点位置
          position: 'left',
          align: 'right'
        }
        if (!item.children) return
        item.children.forEach((port) => {
          if (!port.children) return
          port.children.forEach((levelRule) => {
            if (!levelRule.children) return
            levelRule.children.forEach((rule) => {
              // 设置组件节点图片在左边
              rule.label = {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left'
              }
            })
          })
        })
      })
      this.optionsArr.series[0].data = this.treeData
      this.pie('chart_trend', 'two')
    },
    pie(id, options) {
      this.chart_tree.clear()
      this.chart_tree.setOption(this.optionsArr, true)
      //主要就是这里
      let container = document.getElementById('chart_tree')
      container.style.height =
        this.findMaxChild(this.treeData) > 0 ? this.findMaxChild(this.treeData) * 80 + 'px' : '88%'
      this.chart_tree.resize()
      window.addEventListener('resize', () => {
        this.chart_tree.resize()
      })
    },
    findMaxChild(arr) {
      this.ruleArr2 = []
      this.ruleArr3 = []
      this.ruleArr4 = []
      this.findChild(arr)
      return Math.max.apply(null, [
        this.ruleArr2.length,
        this.ruleArr3.length,
        this.ruleArr4.length
      ])
    },
    findChild(arr) {
      arr.forEach((parentItem) => {
        if (parentItem.level == 2) {
          this.ruleArr2.push(parentItem)
          this.findChild(parentItem.children)
        } else if (parentItem.level == 3) {
          // 末级组件节点数量
          this.ruleArr3.push(parentItem)
          this.findChild(parentItem.children)
        } else if (parentItem.level == 4) {
          // 末级组件节点数量
          this.ruleArr4.push(parentItem)
        } else {
          this.findChild(parentItem.children)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.moduleCard.drawWrap {
  height: 601px;
}

.drawWrap {
  .emptyClass {
    height: 80%;
    text-align: center;
    vertical-align: middle;
    svg {
      display: inline-block;
      font-size: 120px;
      margin-top: 80px;
    }
    p {
      line-height: 25px;
      color: #d1d5dd;
      span {
        margin-left: 4px;
        color: #2677ff;
        cursor: pointer;
      }
    }
  }
}
</style>
