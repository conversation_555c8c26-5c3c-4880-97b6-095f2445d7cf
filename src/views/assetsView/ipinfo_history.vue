<template>
  <div class="moduleCard rightWrap">
    <div class="infoTitle"><i></i>IP变化历史</div>
    <div class="cardContent">
      <el-timeline>
        <el-timeline-item v-for="(item, index) in timeLineData" :key="item.id">
          <span class="dot" slot="dot"></span>
          <div class="tiemBox"
            >{{ item.time }}<span class="tiemContent">{{ getName(index) }}</span>
          </div>
          <div v-if="timeLineData.length - 1 != index">
            <el-card v-for="child in item.children" :key="child.id">
              <el-form ref="form" label-width="80px">
                <el-form-item :label="child.title">
                  <ul>
                    <li style="margin-bottom: 5px" v-for="ch in child.children" :key="ch.id">
                      <el-tooltip
                        v-if="ch.title && (child.title == '端口变化' || child.title == '组件变化')"
                        class="item"
                        effect="dark"
                        :content="ch.detail"
                        placement="top"
                        :open-delay="500"
                      >
                        <span>{{ ch.detail }}</span>
                      </el-tooltip>
                      <el-tooltip
                        v-else-if="ch.title"
                        class="item"
                        effect="dark"
                        :content="ch.title"
                        placement="top"
                        :open-delay="500"
                      >
                        <span>{{ ch.title }}</span>
                      </el-tooltip>
                    </li>
                  </ul>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
export default {
  props: ['timeLineData'],
  data() {
    return {
      loadingFl: false
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getInit()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.getInit()
  },
  methods: {
    async getInit() {},
    getName(index) {
      if (this.timeLineData.length - 1 == index) {
        return ' 发现新资产'
      } else {
        return ' 发生变化'
      }
    }
  }
}
</script>
<style lang="less" scoped>
.rightWrap {
  height: 300px !important;
  overflow: auto;
  margin-bottom: 12px;
  .el-timeline-item__node {
    background: #ffffff;
    border: 2px solid #4285f4;
  }
  .el-timeline-item__node--normal {
    left: 0px;
  }
  .el-timeline-item__tail {
    left: 7px;
    border-left: 2px solid #edf0f5;
  }
  .el-timeline-item__wrapper {
    padding-right: 20px;
  }
  .el-card.is-always-shadow {
    box-shadow: none;
  }
  .el-card {
    margin-top: 10px;
    background: #eff2f7;
  }
  .el-card__body {
    padding: 16px 24px;
    .el-form {
      padding: 0 !important;
    }
    .el-form-item__label {
      text-align: left;
      line-height: 28px;
      height: 28px;
    }
    .el-form-item__content {
      & > ul {
        display: flex;
        flex-wrap: wrap;
        li {
          max-width: 80%;
          height: 28px;
          line-height: 28px;
          padding: 0 10px;
          margin-right: 12px;
          background: #ffffff;
          border-left: 2px solid #1bec9e;
          border-radius: 2px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

::v-deep .el-timeline {
  .el-timeline-item__tail {
    left: 7px;
  }
  .el-timeline-item__dot {
    top: -1px;
    .dot {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(38, 119, 255, 1);
      border-radius: 50%;
      box-sizing: border-box;
      background-color: #fff;
    }
  }
}
</style>
