<template>
  <div class="moduleCard pocHole">
    <div class="infoTitle">PoC漏洞</div>
    <div class="cardContent">
      <div class="tableBox">
        <el-table
          :data="threatsData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltable1"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            v-for="item in threatsTableHeaderPoc"
            :key="item.name"
            :prop="item.name"
            align="left"
            :show-overflow-tooltip="true"
            :min-width="item.minWidth"
            :label="item.label"
          >
            <template slot-scope="scope">
              <div class="rules" v-if="item.name == 'level'" style="padding: 0">
                <span
                  v-if="scope.row[item.name] == 3"
                  class="deepRedRadiusBorder"
                  >严重</span
                >
                <span v-if="scope.row[item.name] == 2" class="redRadiusBorder"
                  >高危</span
                >
                <span
                  v-if="scope.row[item.name] == 1"
                  class="originRadiusBorder"
                  >中危</span
                >
                <span
                  v-if="scope.row[item.name] == 0"
                  class="yellowRadiusBorder"
                  >低危</span
                >
              </div>
              <span v-else-if="item.name == 'state'">
                <span
                  v-if="scope.row[item.name] == 2 || scope.row[item.name] == 3"
                  >已修复</span
                >
                <span v-else>未修复</span>
              </span>
              <span v-else>{{
                scope.row[item.name] ? scope.row[item.name] : "-"
              }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ipinfoPocHole",
  props: ["threatsData"],
  data() {
    return {
      threatsTableHeaderPoc: [
        {
          label: "漏洞名称",
          name: "common_title",
          minWidth: 100,
        },
        {
          label: "漏洞地址",
          name: "url",
          minWidth: 100,
        },
        {
          label: "风险等级",
          name: "level",
          minWidth: 50,
        },
        {
          label: "漏洞状态",
          name: "state",
          minWidth: 50,
        },
        {
          label: "发现时间",
          name: "last_update_time",
          minWidth: 100,
        },
      ],
    };
  },
};
</script>

<style lang="less" scoped>
.moduleCard.pocHole {
  height: 387px;
}
.tableBox {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
