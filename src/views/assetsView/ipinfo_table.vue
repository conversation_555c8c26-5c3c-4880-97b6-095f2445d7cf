<template>
  <div class="moduleCard portInfo">
    <el-tabs class="tabs" v-model="activeName">
      <el-tab-pane :label="`组件信息(${ruleTagsNum})`" name="a"> </el-tab-pane>
      <el-tab-pane :label="`端口信息(${tableData.length})`" name="b"> </el-tab-pane>
      <!-- <el-tab-pane v-if="fromPath" :label="`POC漏洞(${threatsData.length})`" name="c">
      </el-tab-pane> -->
      <!-- <el-tab-pane :label="`情报漏洞(${vulnsList.length})`" name="d">
      </el-tab-pane> -->
    </el-tabs>
    <div class="cardContent">
      <ul class="ruleClass" v-if="activeName == 'a'">
        <li v-for="item in ruleTitle" :key="item.id">
          <i :class="'line' + item.name"></i>
          <p
            ><span>{{ item.label }}</span></p
          >
          <div class="rules">
            <span class="ruleItem" v-for="ch in ruleTags[item.name]" :key="ch"
              ><img :src="showimg(ch).result ? showimg(ch).url : ''" alt="" class="productImg" />{{
                ch
              }}</span
            >
          </div>
        </li>
      </ul>
      <div v-show="activeName == 'b'" class="tableBox">
        <el-table
          v-show="activeName == 'b'"
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column v-if="activeName == 'b'" type="expand">
            <template slot-scope="props">
              <div
                class="banner"
                v-text="props.row.banner ? props.row.banner.replace(/\n/g, '<br>') : '暂无信息'"
              ></div>
            </template>
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :show-overflow-tooltip="true"
            :min-width="item.minWidth"
            :label="item.label"
          >
            <template slot-scope="scope">
              <p class="rules" v-if="item.name == 'rules'" style="padding: 0">
                <span v-if="scope.row[item.name]" class="ruleItemBox">
                  <span class="ruleItem" v-for="(item, index) in scope.row[item.name]" :key="index"
                    ><img
                      :src="showimg(item.cn_product).result ? showimg(item.cn_product).url : ''"
                      alt=""
                      class="productImg1"
                    />{{ item.cn_product }}</span
                  >
                </span>
                <i v-else>-</i>
              </p>
              <span v-else>{{ getTableItem(scope.row[item.name]) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <div v-show="activeName == 'c'" class="tableBox">
        <el-table
          border
          :data="threatsData"
          row-key="id"
          :header-cell-style="{background: '#F2F3F5',color: '#62666C'}"
          ref="eltable1"
          height="100%"
          style="width: 100%">
          <el-table-column
            v-for="item in threatsTableHeaderPoc"
            :key="item.id"
            :prop="item.name"
            align="left"
            :show-overflow-tooltip="true"
            :min-width="item.minWidth"
            :label="item.label">
            <template slot-scope="scope">
              <div class="rules" v-if="item.name == 'level'" style="padding:0">
                <span v-if="scope.row[item.name] == 3" class="deepRedRadiusBorder">严重</span>
                <span v-if="scope.row[item.name] == 2" class="redRadiusBorder">高危</span>
                <span v-if="scope.row[item.name] == 1" class="originRadiusBorder">中危</span>
                <span v-if="scope.row[item.name] == 0" class="yellowRadiusBorder">低危</span>
              </div>
              <span v-else-if="item.name == 'state'">
                <span v-if="scope.row[item.name] == 2 || scope.row[item.name] == 3">已修复</span>
                <span v-else>未修复</span>
              </span>
              <span v-else>{{scope.row[item.name] ? scope.row[item.name] : '-'}}</span>
            </template>
          </el-table-column>
        </el-table>
      </div> -->
      <div v-show="activeName == 'd'" class="tableBox">
        <el-table
          border
          :data="vulnsList"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltable1"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            v-for="item in threatsTableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :show-overflow-tooltip="true"
            :min-width="item.minWidth"
            :label="item.label"
          >
            <template slot-scope="scope">
              <div class="rules" v-if="item.name == 'level'" style="padding: 0">
                <span v-if="scope.row[item.name] == 3" class="deepRedRadiusBorder">严重</span>
                <span v-if="scope.row[item.name] == 2" class="redRadiusBorder">高危</span>
                <span v-if="scope.row[item.name] == 1" class="originRadiusBorder">中危</span>
                <span v-if="scope.row[item.name] == 0" class="yellowRadiusBorder">低危</span>
              </div>
              <span v-else-if="item.name == 'ip'">
                <span v-if="scope.row['ip']">{{ scope.row['ip'] }}</span>
                <span v-if="scope.row['port']">:{{ scope.row['port'] }}</span>
                <span v-if="!scope.row['ip'] && !scope.row['port']">-</span>
              </span>
              <span v-else-if="item.name == 'state'">
                <span v-if="scope.row[item.name] == 2 || scope.row[item.name] == 3">已修复</span>
                <span v-else>未修复</span>
              </span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
export default {
  props: ['vulnsList', 'threatsData', 'tableData', 'ruleTags', 'ruleTagsNum', 'fromPath'],
  data() {
    return {
      loadingFl: false,
      activeName: 'a',
      ipInfoItemData: [
        {
          label: 'IP地址',
          name: 'ip'
        },
        {
          label: '域名',
          name: 'ip'
        },
        {
          label: '地理位置',
          name: 'province'
        }
      ],
      ruleTitle: [
        {
          id: 1,
          label: '业务层',
          name: 'rule5'
        },
        {
          id: 2,
          label: '支撑层',
          name: 'rule4'
        },
        {
          id: 3,
          label: '服务层',
          name: 'rule3'
        },
        {
          id: 4,
          label: '系统层',
          name: 'rule2'
        },
        {
          id: 5,
          label: '硬件层',
          name: 'rule1'
        },
        {
          id: 6,
          label: '资产导入',
          name: 'rule6'
        }
      ],
      threatsTableHeaderPoc: [
        {
          label: '漏洞名称',
          name: 'common_title',
          minWidth: 100
        },
        {
          label: '漏洞地址',
          name: 'url',
          minWidth: 100
        },
        {
          label: '风险等级',
          name: 'level',
          minWidth: 50
        },
        {
          label: '漏洞状态',
          name: 'state',
          minWidth: 50
        },
        {
          label: '发现时间',
          name: 'last_update_time',
          minWidth: 100
        }
      ],
      threatsTableHeader: [
        {
          label: '漏洞名称',
          name: 'name',
          minWidth: 100
        },
        {
          label: 'CVE编号',
          name: 'cve_id',
          minWidth: 100
        },
        {
          label: '影响资产',
          name: 'ip',
          minWidth: 100
        },
        {
          label: '漏洞等级',
          name: 'level',
          minWidth: 50
        },
        {
          label: '发现日期',
          name: 'find_date',
          minWidth: 100
        }
      ],
      tableHeader: [
        {
          label: '端口',
          name: 'port',
          minWidth: 80
        },
        {
          label: '协议',
          name: 'protocol',
          minWidth: 80
        },
        {
          label: '组件信息',
          name: 'rules',
          minWidth: 400
        }
      ]
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getInit()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.getInit()
  },
  methods: {
    getTableItem(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length > 0) {
            return item.join('，')
          } else {
            return '-'
          }
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    showimg(url) {
      let imgUrl = ''
      url = url.replace('/', '')
      url = url.replace('.', '')
      url = url.replace(' ', '')
      let result = false
      url = './' + url + '.png'
      let urlTmp = url
      urlTmp = urlTmp.replace('./', '').replace('.png', '')
      // 目录下所有文件名
      const files = require.context('../../assets/images/componentIcon/', false, /\.png$/)
      const filedata = files.keys()
      filedata.forEach((item) => {
        if (item === url) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp + '.png')
        } else if (item === url.toLowerCase()) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp.toLowerCase() + '.png')
        }
      })
      // 返回结果
      return {
        result,
        url: imgUrl
      }
    },
    async getInit() {}
  }
}
</script>
<style lang="less" scoped>
.portInfo {
  .productImg {
    max-height: 16px;
    max-width: 16px;
    margin-right: 2px;
    vertical-align: middle;
  }
  .productImg1 {
    max-height: 16px;
    max-width: 16px;
    margin-right: 2px;
    vertical-align: middle;
  }
  .rules {
    display: flex;
    flex-wrap: wrap;
    padding: 0 12px;
    .ruleItemBox {
      display: flex;
      flex-wrap: wrap;
    }
    .ruleItem {
      display: inline-block;
      padding: 2px 10px;
      // margin-right: 8px;
      margin: 5px 8px 5px 0px;
      background: #ffffff;
      border-radius: 12px;
      border: 1px solid #d1d5dd;
      font-size: 12px;
      color: #37393c;
    }
  }
  .ruleClass {
    li {
      position: relative;
      width: 100%;
      min-height: 60px;
      background: #eff2f7;
      border-radius: 4px;
      padding-bottom: 12px;
      & + li {
        margin-top: 12px;
      }
      .rules {
        color: #2677ff;
      }
      i {
        position: absolute;
        top: 0;
        left: 0px;
        display: block;
        width: 4px;
        height: 100%;
        border-radius: 100px 0px 0px 100px;
      }
      .linerule1 {
        background: #2677ff;
      }
      .linerule2 {
        background: #5192ff;
      }
      .linerule3 {
        background: #7dadff;
      }
      .linerule4 {
        background: #a8c9ff;
      }
      .linerule5 {
        background: #d4e4ff;
      }
      .linerule6 {
        background: rgba(16, 213, 149, 1);
      }
      p {
        margin-bottom: 12px;
        padding-left: 12px;
        span {
          display: block;
          width: 68px;
          border-radius: 0px 0px 4px 4px;
          text-align: center;
          font-size: 14px;
          color: #fff;
          background: #2677ff;
          padding: 2px;
          box-sizing: border-box;
        }
      }
    }
  }

  td.el-table__expanded-cell {
    padding: 0;
    .banner {
      background: #f5f7fa;
      padding: 20px;
      line-height: 32px;
      overflow: auto;
    }
  }
}
.tableBox {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
