<template>
  <div class="arrbox">
    <el-tag
      :key="tag"
      type="info"
      v-for="tag in dynamicTags"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)"
    >
      {{ tag }}
    </el-tag>
    <el-input
      class="input-new-tag"
      v-model="inputValue"
      ref="saveTagInput"
      size="small"
      @keyup.enter.native="handleInputConfirm"
      @blur="handleInputConfirm"
      placeholder="请输入内容,回车可生成标签"
    >
    </el-input>
  </div>
</template>
<script>
export default {
  props:{
    parentTags: {
      type: Array,
      default () {
          return []
      }
    },
  },
  data() {
    return {
      dynamicTags: [],
      inputVisible: false,
      inputValue: ""
    };
  },
  watch:{
    parentTags:{
      handler(){
        this.dynamicTags = this.parentTags.length ? JSON.parse(JSON.stringify(this.parentTags)) : []
      },
      deep: true,
      immediate: true
    },
  },
  methods: {
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      this.$emit('onChange', this.dynamicTags)
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (this.dynamicTags.indexOf(inputValue) > -1) {
        this.$message.error('输入重复')
        return;
      }
      if (inputValue) {
        this.dynamicTags.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
      this.$emit('onChange', this.dynamicTags)
    }
  }
};
</script>


<style lang="less" scoped>
.arrbox {
  width: 100%;
  box-sizing:border-box;
  background-color: white;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  font-size: 12px;
  text-align: left;
  // padding-left: 5px;
  word-wrap: break-word;
  overflow: hidden;
}
/deep/.el-tag {
  height: 30px;
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
    box-sizing: border-box;
    border-color: transparent;
    margin: 2px 0 2px 6px;
    background-color: #f0f2f5;
    font-size: 14px;
    .el-select__tags-text {
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .el-icon-close {
      border-radius: 50%;
      text-align: center;
      position: relative;
      cursor: pointer;
      font-size: 12px;
      height: 16px;
      width: 16px;
      line-height: 16px;
      vertical-align: middle;
      top: -1px;
      right: -5px;
      background-color: #c0c4cc;
    }
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 190px;
  height: 32px;
  // margin-left: 10px;
  border: none;
  vertical-align: bottom;
  /deep/.el-input__inner {
    border: none;
  }
}
</style>
