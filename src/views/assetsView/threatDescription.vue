<template>
  <el-dialog
    class="elDialogAdd"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="800px"
  >
    <template slot="title"> 威胁类型说明 </template>
    <div class="dialog-body">
      <div class="content">
        <div class="section">
          <div class="title"> <strong> 1.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;钓鱼仿冒</strong></div>
          <p
            >钓鱼仿冒是一种网络攻击手段，指攻击者通过伪装成合法机构或个人的方式，欺骗用户输入敏感信息或下载恶意软件。</p
          >
        </div>
        <div class="section">
          <div class="title"> <strong> 2.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;黄赌毒网站</strong></div>
          <p
            >黄赌毒网站是指企业的某个特征，如：ICP、ICON、title等被不法份子用于黄赌毒网站，这可能会给该公司带来严重的负面影响。首先，根据相关法律法规，网络服务提供者应当对其所提供的服务内容负主要责任，因此，公司若出现其ICP或icon在黄赌毒网站中使用的情况，将面临相应的法律责任。</p
          >
        </div>
        <div class="section">
          <div class="title"> <strong> 3.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ICP盗用</strong></div>
          <p
            >ICP盗用指的是黑客或不法分子通过各种手段，冒用他人的ICP（InternetContentProvider）备案信息，以达到欺骗用户，传播虚假信息或进行其他违法犯罪活动的目的。</p
          >
        </div>
        <div class="section">
          <div class="title"> <strong> 4.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;域名混淆</strong></div>
          <p
            >域名混淆是指黑客或不法分子通过修改域名的拼写、字符和后缀等信息，以制造类似真实网站的假冒网站，从而欺骗用户输入敏感信息或下载恶意软件的行为。域名混淆可以通过以下方式进行：</p
          >
          <ul>
            <li
              >修改域名拼写：利用误差率高、用户注意力不集中的特点，将正常域名的拼写稍加改动，例如将“fafo.info”替换成“fofa.info”。
            </li>
            <li>使用相似字符：使用看起来相似但实际上不同的字符，例如“rn”代替“m”，“1”代替“l”等。</li>
            <li
              >变更域名后缀：在正常域名后面添加类似“.com”、“.cn”之类的后缀，以模仿真实网站的形式。</li
            >
          </ul>
          <p
            >通过域名混淆，黑客或不法分子可以让用户误认为其假冒网站是真实的网站，并借此骗取用户的账号密码、银行卡信息等敏感信息，或者在用户下载文件时植入恶意软件，从而造成损失。</p
          >
        </div>
        <div class="section">
          <div class="title"> <strong> 5.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;其他类型</strong></div>
          <p
            >其他类型是指根据企业的某些特征推荐出来的资产，资产内容为无效文章类以及与企业无关类的信息。</p
          >
        </div>
        <div class="section">
          <div class="title"> <strong> 6.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;违法信息交易</strong></div>
          <p
            >违法信息交易被视为一种严重的威胁类型，其内容通常包括毒品交易、军火交易、恐怖主义犯罪活动、个人隐私信息交易等，这些信息可能会被用于进行欺诈、敲诈勒索、诈骗等违法活动，对个人和企业都具有很大的潜在威胁。</p
          >
        </div>
        <div class="section">
          <div class="title"> <strong> 7.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;伪造文件</strong></div>
          <p
            >伪造文件是指以欺骗、篡改等手段制作虚假的文件，使其看起来与真实的文件相同或类似。伪造文件在现代社会中被广泛使用，可能用于欺诈、非法牟利、逃税等违法活动。</p
          >
        </div>
        <div class="section">
          <div class="title">
            <strong> 8.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;社会安全和秩序问题</strong></div
          >
          <p
            >社会安全和秩序问题包括各种危害公共安全和破坏社会秩序的问题，例如犯罪活动、社会治安问题、火灾、自然灾害等突发事件。此外，社会安全和秩序问题也涉及危险物品和装备，如枪支弹药、军刀手铐、电击棒等可能被用于违法犯罪活动的危险物品。</p
          >
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="dialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false
    }
  }
}
</script>

<style lang="less" scoped>
.content {
  // width: 500px;
  p {
    text-indent: 2em;
    line-height: 32px;
  }
  ul {
    margin: 14px 0;
  }
  li {
    position: relative;
    // text-indent: 2em;
    line-height: 32px;
    margin-left: 27px;
    &::before {
      content: '';
      position: absolute;
      top: 12px;
      left: -27px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: black;
    }
  }
}
.title {
  height: 32px;
  margin: 14px 0 0;
  line-height: 32px;
}
</style>
