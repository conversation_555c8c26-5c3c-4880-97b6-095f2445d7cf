<template>
  <div class="assets-box">
    <div class="assets">
      <div class="top-box">
        <div class="number-top header-label" @click="handleDoing">
          <div class="blueBlock"></div>
          <span class="title">资产空间</span>
          <img src="../../assets/images/arrowRight.png" alt="" />
        </div>
      </div>
      <div class="bottom-box">
        <img src="../../assets/images/assetsImg.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
export default {
  methods: {
    ...mapMutations(['changeMenuId']),
    handleDoing() {
      sessionStorage.setItem('menuId', '1-3-1')
      this.changeMenuId('1-3-1')
      this.$router.push('/assetsLedger')
    }
  }
}
</script>

<style lang="less" scoped>
.assets-box {
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  width: 70%;
  .assets {
    box-sizing: border-box;
    height: 530px;
    width: 100%;
    margin-top: 20px;
    // background-color: #e8f1fc;
    background: url(../../assets/images/assetsBgc.png) no-repeat;
    background-size: 100% 100%;
    border: 2px solid #fff;
    border-radius: 4px;
    // box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.8);
    .top-box {
      position: relative;
    }
    .bottom-box {
      box-sizing: border-box;
      width: calc(100% - 30px);
      margin: 0 auto;
      img {
        // width:100%;
        display: block;
        margin: 0 auto;
        // text-align: center;
        height: 500px;
      }
    }
    // .number-top::before {
    //   content: "";
    //   position: absolute;
    //   left: 0px;
    //   top: 15px;
    //   width: 4px;
    //   height: 16px;
    //   background-color: #2677ff;
    // }
    .number-top {
      cursor: pointer;
      height: 46px;
      // padding-top: 12px;
      // padding-left: 16px;
      border-bottom: 1px solid #e9ebef;
      .title {
        // // font-family: PingFangSC-Semibold;
        color: #37393c;
        font-size: 16px;
        font-weight: 400;
      }
      img {
        margin: 0 !important;
        vertical-align: middle;
      }
    }
  }
}
</style>
