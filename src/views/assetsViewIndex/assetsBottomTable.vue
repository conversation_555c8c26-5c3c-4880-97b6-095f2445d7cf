<template>
  <div class="assets-table">
    <div class="assets-table-top">
      <div class="assets-table-title">
        <div
          class="title-list"
          v-for="(titleItem, n) in title"
          :key="n"
          @click="num = n"
          :class="{ active: num == n }"
        >
          {{ titleItem }}
        </div>
      </div>
    </div>
    <div class="myTable" :class="{ iptable: num == 1 }">
      <ipAssetsVue class="ipAssetsVue" v-if="num == 0" />
      <ipTable v-if="num == 1" style="height: 550px !important" />
      <loginEntryVue v-if="num == 2" />
      <domainListVue v-if="num == 3" />
      <certAssetListVue v-if="num == 4" />
      <businessSystemVue v-if="num == 5" />
    </div>
    <div class="moreBtn" @click="jumpOther">查看更多</div>
  </div>
</template>

<script>
import sha1 from '@/utils/sha1Encrypt'
import iptable from './iptable.vue'
import tableList from '../home_set/indexTable.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import ipAssetsVue from '../assetsView/ipAssets.vue'
import loginEntryVue from '../loginEntry/loginEntry.vue'
import domainListVue from '../domainAsset/domainList.vue'
import certAssetListVue from '../certAsset/certAssetList.vue'
import businessSystemVue from '../assetsView/businessSystem.vue'
import ipTable from './ip+table.vue'
import { ansysDataIndex } from '@/api/apiConfig/asset.js'
export default {
  components: {
    iptable,
    tableList,
    ipAssetsVue,
    loginEntryVue,
    domainListVue,
    certAssetListVue,
    businessSystemVue,
    ipTable
  },
  data() {
    return {
      jumpPath: [
        {
          path: '/assetsLedger',
          menu: '1-3-1'
        },
        {
          path: '/assetsLedger',
          menu: '1-3-1'
        },
        {
          path: '/loginEntry',
          menu: '1-3-2'
        },
        {
          path: '/domainAsset',
          menu: '1-3-3'
        },
        {
          path: '/certAsset',
          menu: '1-3-4'
        },
        {
          path: '/businessSystem',
          menu: '1-3-5'
        }
      ],
      title: ['IP资产', 'IP+端口', '登录入口', '域名资产', '证书资产', '业务系统'],
      num: 0,
      tableData: [],
      formInline: {
        ip: '',
        clue_company_name: '',
        province: '', // 省份名称（传汉字）,
        keyword: '', // 123123,
        asn: '', // 123123,
        icp: '', // icp,
        url: '',
        title: '', // title,
        protocol: '', // protocol,
        logo: '', // logo,
        port: '', // port,
        cname: '', // cname,
        domain: [], // domain
        subdomain: [],
        online_state: [],
        lat: '',
        lon: '',
        state: '',
        reason: '',
        updated_at: []
      },
      identifier: '',
      loading: false,
      total: 0,
      radioParams: false,
      user: {
        role: ''
      },
      // currentCompany: 1,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      currentPage: 1
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    jumpOther() {
      this.changeMenuId(this.jumpPath[this.num].menu)
      sessionStorage.setItem('menuId', this.jumpPath[this.num].menu)
      this.$router.push({ path: this.jumpPath[this.num].path })
    },
    // 已知资产列表
    async getknownAssetsList() {
      this.formInline.page = this.currentPage
      this.formInline.per_page = 10
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      this.tableData = []
      this.identifier = sha1(this.user.id + 'sha1' + new Date().getTime())
      let obj = {
        identifier: this.identifier,
        operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
        type: '', // 统计接口直接传''
        status: [1, 4],
        ...this.formInline
      }
      this.loading = true
      let res = await ansysDataIndex(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val

      this.getknownAssetsList(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getknownAssetsList(true)
    }
  },
  watch: {
    getterCompanyChange(val) {
      if (this.user.role == 2) {
        this.getknownAssetsList()
      }
    }
  },
  mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getknownAssetsList()
    } else {
      this.getknownAssetsList()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCompanyChange'])
  }
}
</script>

<style lang="less" scoped>
.moreBtn {
  text-align: center;
  height: 38px;
  color: #62666c;
  &:hover {
    cursor: pointer;
  }
  // line-height: 38px;
}
.assets-table {
  width: 100%;
  margin: 0 auto;
  background-color: #fff;
  margin-top: 20px;
  border-radius: 4px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  .assets-table-top {
    height: 45px;
    position: relative;
    width: 100%;
    border-bottom: 1px solid #e9ebef;

    .assets-table-title::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      width: 4px;
      height: 16px;
      border-radius: 0 2px 2px 0;
      background-color: #2677ff;
      border-radius: 0 2px 2px 0;
    }
    .assets-table-title {
      // margin-left: 3%;
      margin-left: 14px;
      display: flex;
      font-size: 16px;
      // font-family: PingFangSC-Regular;
      .title-list {
        // width: 7%;
        height: 45px;
        line-height: 45px;
        margin-right: 20px;
        color: #62666c;
        text-align: center;
        cursor: pointer;
        // font-family: PingFangSC-Regular;
        font-size: 16px;
      }
      .active {
        border-bottom: 2px solid #2677ff;
        color: #2677ff;
        font-weight: 600;
      }
    }
  }
  .myTable {
    position: relative;
    height: 558px;
    // margin-top: 20px;
    padding: 20px 16px;
    .cardBox {
      height: 100%;
    }
    .ipAssetsVue {
      /deep/.el-table__body td.el-table__cell {
        padding: 0 !important;
        .cell {
          padding: 0 !important;
        }
      }
    }
    .moreBtn {
      position: absolute;
      bottom: 20px;
      left: 0;
      right: 0;
      margin: auto;
    }
  }
  .iptable {
    height: 540px;
  }
  .con {
    ::deep(.el-table) {
      width: 500px !important;
    }
  }
}
.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  & > div {
    .el-input {
      width: 240px;
    }
    .el-select {
      width: 240px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      // margin-left: 16px;
      cursor: pointer;
    }
  }
}
</style>
