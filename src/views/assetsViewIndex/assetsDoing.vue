<template>
  <div class="conWrap">
    <div class="conWrap-title">
      <div class="header-label">
        <div class="blueBlock"></div>
        <span>资产动态</span>
      </div>
      <div class="date-select">
        <el-select
          :disabled="loading"
          v-model="createdTimeType"
          @change="getTableList"
          placeholder="请选择"
        >
          <el-option label="近30天" :value="30">近30天</el-option>
          <el-option label="近60天" :value="60">近60天</el-option>
          <el-option label="全部" :value="0">全部</el-option>
        </el-select>
      </div>
    </div>
    <div class="com-top" v-loading="loading">
      <div v-if="componentsArr.length > 0" class="componentClass">
        <div class="componentClass-box">
          <div
            class="list-compo"
            v-for="(item, index) in componentsArr"
            :key="index"
            @click="handleTypeChange(item)"
            :class="{ active: activeItem == item.valueName }"
          >
            <div class="top">
              <div>
                <span :class="'indexClass' + index">{{ index + 1 }}</span>
                <i class="numClass">{{ item.name ? item.name : '-' }}</i>
              </div>
              <el-tooltip class="item" effect="dark" :content="item.tip" placement="top">
                <span class="numClass">{{
                  componentsCount[item.valueName] ? componentsCount[item.valueName] : 0
                }}</span>
              </el-tooltip>
            </div>
            <el-progress
              v-if="!loading"
              :percentage="(componentsCount[item.valueName] / totalNum) * 100 || 0"
              :show-text="false"
            ></el-progress>
          </div>
        </div>
        <div class="componentClass-content" v-loading="listLoading">
          <div
            class="componentClass-ips"
            :class="{ 'list-top': activeItem == 'assets', 'list-last': activeItem == 'event' }"
            v-if="componentsList[activeItem] && componentsList[activeItem].length !== 0"
            :key="activeItem"
          >
            <template v-if="activeItem == 'event'">
              <div
                class="ip-list event-list"
                v-for="(ipItem, n) in componentsList[activeItem]"
                :key="n"
              >
                <span class="ip">{{ ipItem.name ? ipItem.name : '-----' }}</span>
                <span class="time">{{ ipItem.created_at ? ipItem.created_at : '-----' }}</span>
              </div>
            </template>
            <template v-else>
              <div class="ip-list" v-for="(ipItem, n) in componentsList[activeItem]" :key="n">
                <span class="status-box">
                  <span v-if="ipItem.status == 1" class="status1 status">
                    {{ getTitle(ipItem.data_type) }}{{ ipItem.data_type == 3 ? '修复' : '增加' }}
                  </span>
                  <span v-else class="status2 status">
                    {{ getTitle(ipItem.data_type)
                    }}{{ ipItem.data_type == 3 || ipItem.data_type == 4 ? '发现' : '减少' }}
                  </span>
                </span>
                <span class="ip" style="cursor: pointer" @click="infoCheck(ipItem.id)">
                  <span class="content" style="color: #2677ff">
                    {{ ipItem.ip ? ipItem.ip : '-----' }}
                  </span>
                  <span class="content update" style="color: #62666c; font-size: 12px">
                    更新内容：
                    <span class="text">
                      {{ ipItem.data ? ipItem.data.join('，') : '-' }}
                    </span>
                  </span>
                </span>
                <span class="time">{{ ipItem.time ? ipItem.time : '-----' }}</span>
              </div>
            </template>
          </div>
          <div v-else class="emptyClass">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-kong"></use>
            </svg>
            <p>暂无数据</p>
          </div>
          <div class="moreModule" @click="jumpPageByFilter(componentsArrObj[activeItem])">
            查看更多
            <svg class="icon svg-icon" aria-hidden="true">
              <use xlink:href="#icon-more-right"></use>
            </svg>
            <svg class="icon svg-icon" aria-hidden="true">
              <use xlink:href="#icon-more-right"></use>
            </svg>
          </div>
        </div>
      </div>
      <div v-else class="emptyClass">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-kong"></use>
        </svg>
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import baseUse from '@/utils/baseUse'
import { eventWaringList, assetsDynamicCount, assetsDynamicList } from '@/api/apiConfig/api.js'

export default {
  props: ['taskIcon'],
  data() {
    return {
      listLoading: false,
      createdTimeRange: '',
      createdTimeType: 30,
      componentsList: {
        assets: 0,
        poc: 0,
        event: 0
      },
      componentsCount: {
        assets: 0,
        poc: 0,
        event: 0
      },
      componentsArrObj: {
        assets: {
          name: '资产变更',
          valueName: 'assets',
          pathName: '/assetsLedger',
          menuId: '1-3-1',
          tip: '资产台账ip的数量'
        },
        poc: {
          name: '漏洞变更',
          valueName: 'poc',
          pathName: '/repairLeakScan',
          menuId: '3-1-2',
          tip: '未修复漏洞的数量'
        },
        event: {
          name: '风险事件',
          valueName: 'event',
          pathName: '/eventWarning',
          menuId: '3-3',
          tip: '事件告警的数量'
        }
      },
      totalNum: 0,
      activeItem: 'assets',
      loading: false,
      clickTimer: null
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getTableList()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany']),
    componentsArr() {
      return Object.values(this.componentsArrObj)
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2 && !this.currentCompany) return
    this.getTableList()
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    handleTypeChange(item) {
      this.activeItem = item.valueName
      if (!this.componentsList.poc && item.valueName == 'poc') {
        this.getAssetsList(true)
      }
    },
    getTitle(type) {
      let name = ''
      switch (type) {
        case 1:
          name = '组件'
          break
        case 2:
          name = '端口'
          break
        case 3:
          name = '漏洞'
          break
        case 4:
          name = '资产'
          break
        default:
          name = ''
      }
      return name
    },
    createdTimeRangeChange(val) {
      if (!val) {
        return ''
      }
      let toData = new Date(new Date().toLocaleDateString()).getTime()
      let start = toData - Number(val) * 3600 * 24 * 1000
      let end = new Date(toData.valueOf())
      let pastDaysStart = baseUse.getModificationDate(new Date(start.valueOf())).split(' ')[0]
      let pastDaysEnd = baseUse.getModificationDate(end).split(' ')[0]
      return [pastDaysStart, pastDaysEnd]
    },
    infoCheck(id) {
      const _that = this
      clearTimeout(this.clickTimer) // 清除第一个单击事件
      this.clickTimer = setTimeout(function () {
        // 单击事件的代码执行区域
        window.open(
          `/alreadyTask_viewlist_ipinfo?id=${id}&fromIcon=0&preList=${_that.$route.path}`,
          '_blank'
        ) // fromIcon: 默认'0' 已完成任务列查看ip详情，传1
      }, 500)
    },
    jumpPageByFilter(row) {
      this.$emit('scrollToBottom')
      this.$router.push({ path: row.pathName, query: { createdTimeRange: this.createdTimeRange } })
      sessionStorage.setItem('menuId', row.menuId)
      this.changeMenuId(row.menuId)
    },
    async getTableList() {
      this.loading = true
      this.componentsList = {
        assets: 0,
        poc: 0,
        event: 0
      }
      this.activeItem = 'assets'
      this.createdTimeRange = this.createdTimeRangeChange(this.createdTimeType)
      // 资产、漏洞 数量汇总
      let assetCountRes = await assetsDynamicCount({
        operate_company_id: this.currentCompany,
        created_at_range: this.createdTimeRange
      }).catch(() => {
        this.loading = false
      })
      this.componentsCount.assets = assetCountRes.data.items.asset_changes || 0
      this.componentsCount.poc = assetCountRes.data.items.vulnerability_changes || 0
      // 事件告警列表
      let eventRes = await eventWaringList({
        operate_company_id: this.currentCompany,
        created_at_range: this.createdTimeRange
      }).catch(() => {
        this.loading = false
      })
      this.componentsCount.event = eventRes.data.total || 0
      this.componentsList.event = eventRes.data.items || []
      // 资产、漏洞 列表汇总
      let assetListRes = await assetsDynamicList({
        operate_company_id: this.currentCompany,
        created_at_range: this.createdTimeRange,
        has_threat_data: false
      }).catch(() => {
        this.loading = false
      })
      let itemList =
        assetListRes.data.items && assetListRes.data.items.filter((item) => item.data_type != 3)
      this.componentsList.assets = itemList || []
      this.totalNum = Math.max.apply(null, Object.values(this.componentsCount)) + 5 // 获取数量最大值
      this.loading = false
    },
    async getAssetsList(has_threat_data = false) {
      this.listLoading = true
      let assetListRes = await assetsDynamicList({
        operate_company_id: this.currentCompany,
        created_at_range: this.createdTimeRange,
        has_threat_data
      }).catch(() => {
        this.listLoading = false
      })
      if (has_threat_data) {
        let itemList =
          assetListRes.data.items && assetListRes.data.items.filter((item) => item.data_type == 3)
        this.$set(this.componentsList, 'poc', itemList || [])
        this.listLoading = false
      }
    },
    async getEventList() {
      // 事件告警列表
      let eventRes = await eventWaringList({
        operate_company_id: this.currentCompany,
        created_at_range: this.createdTimeRange
      }).catch(() => {
        this.loading = false
      })
      this.componentsList.event = eventRes.data.items || []
    },
    // 判断图片是否存在
    showimg(url) {
      let imgUrl = ''
      url = url.replace('/', '')
      url = url.replace('.', '')
      url = url.replace(' ', '')
      let result = false
      url = './' + url + '.png'
      let urlTmp = url
      urlTmp = urlTmp.replace('./', '').replace('.png', '')
      // 目录下所有文件名
      const files = require.context('../../assets/images/componentIcon/', false, /\.png$/)
      const filedata = files.keys()
      filedata.forEach((item) => {
        if (item === url) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp + '.png')
        } else if (item === url.toLowerCase()) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp.toLowerCase() + '.png')
        }
      })
      // 返回结果
      return {
        result,
        url: imgUrl
      }
    }
  },
  beforeDestroy() {
    this.clickTimer = null
    clearTimeout(this.clickTimer)
  }
}
</script>
<style lang="less" scoped>
.conWrap {
  height: 475px;
  margin-top: 20px;
  width: 70%;

  .conWrap-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    height: 46px;
    padding-right: 20px;
    border-bottom: 1px solid #e9ebef;
    span {
      font-weight: 400 !important;
    }
    .date-select {
      /deep/.el-select {
        height: 28px;
        .el-input {
          height: 100%;
          .el-input__inner {
            height: 100%;
          }
        }
      }
    }
  }

  .com-top {
    box-sizing: border-box;
    // width: 95%;
    padding: 0 24px;
    height: calc(100% - 66px);
    margin: 0 auto;
    margin-top: 20px;
    // height: 450px;
    .com-title {
      font-size: 14px;
      font-weight: 500;
      color: #37393c;
    }
  }

  .emptyClass {
    height: 100%;
    text-align: center;
    vertical-align: middle;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: rgba(237, 240, 245, 1);
    svg {
      display: inline-block;
      font-size: 120px;
    }
    p {
      line-height: 25px;
      color: #d1d5dd;
      span {
        margin-left: 4px;
        color: #2677ff;
        cursor: pointer;
      }
    }
  }
  .componentClass {
    flex: 1;
    display: flex;
    .componentClass-box {
      width: 40%;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      height: 364px;
      margin-bottom: 24px;

      & > div {
        width: 100%;
        // height: 64px;
        padding: 20px 16px;
      }
      & > div:nth-child(1) {
        margin-top: 0 !important;
      }
      /deep/.el-progress-bar__outer {
        background: rgba(30, 31, 33, 0.08) !important;
      }
      .active {
        background-color: #ecf0f6;
        border-radius: 4px 0 0 4px;
      }
      .top {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        // margin-top: 7px;
        & > div > span {
          display: inline-block;
          width: 18px;
          height: 18px;
          text-align: center;
          line-height: 18px;
          color: #fff;
          opacity: 1;
          border-radius: 2px;
          margin-right: 8px;
        }
        img {
          // width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        .numClass {
          color: rgba(55, 57, 60, 1);
          font-size: 14px;
        }
        .indexClass0 {
          background: rgba(255, 70, 70, 1);
        }
        .indexClass1 {
          background: rgba(255, 121, 0, 1);
        }
        .indexClass2 {
          background: rgba(38, 119, 255, 1);
        }
      }
    }
    .list-top {
      border-radius: 0 4px 4px 4px !important;
    }
    .list-last {
      border-radius: 4px 4px 4px 0 !important;
    }
    .componentClass-content {
      position: relative;
      // width: 60%;
      flex: 1;
      padding-top: 16px;
      background-color: #ecf0f6;
      .moreModule {
        box-sizing: border-box;
        z-index: 99;
        color: #2677ff;
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 62px;
        // margin-bottom: -20px;
        padding-top: 30px;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 64%);
        text-align: center;

        &:hover {
          cursor: pointer;
        }

        .icon {
          height: 12px;
          width: 6px;
          margin-right: 0;
        }
      }
    }
    .componentClass-ips {
      box-sizing: border-box;
      height: 364px;
      background-color: #ecf0f6;
      border-radius: 4px;
      padding-left: 20px;
      padding-right: 16px;
      padding-bottom: 50px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 6px;
      }

      .ip-list {
        display: flex;
        align-items: center;
        // height: 40px;
        padding: 8px 0 8px;
        margin-top: 16px;
        background-color: #fff;
        font-size: 14px;
        box-shadow: 0px 2px 4px 0px rgba(0, 43, 115, 0.1);
        border-radius: 4px;
        .status-box {
          width: 100px;
          text-align: center;
        }
        .status {
          display: inline-block;
          width: 72px;
          height: 24px;
          border-radius: 14px;
          line-height: 24px;
        }
        .time {
          width: 94px;
          padding-left: 12px;
          color: #62666c;
          font-size: 12px;
          line-height: 22px;
        }
        .ip {
          flex: 1;
          width: 0;
          position: relative;
          color: #37393c;
          .content {
            display: block;
            line-height: 22px;
            .text {
              color: #37393c;
            }
          }
          .update {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        &:first-child {
          margin-top: 0;
        }
      }
      .event-list {
        display: flex;
        padding: 8px 12px;
      }
    }
  }
}
.list-compo {
  /deep/ .el-progress-bar__inner {
    &:nth-child(1) {
      background: linear-gradient(90deg, #ff7a7a 0%, #ff4646 100%);
    }
    &:nth-child(2) {
      background: linear-gradient(90deg, #ffae66 0%, #ff7900 100%);
    }
    &:nth-child(3) {
      background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
    }
  }
}
.status1 {
  background: rgba(16, 213, 149, 0.12);
  border: 1px solid rgba(16, 213, 149, 1);
  color: rgba(16, 213, 149, 1) !important;
}
.status2 {
  background: rgba(255, 70, 70, 0.12);
  border: 1px solid rgba(255, 70, 70, 1);
  color: rgba(255, 70, 70, 1) !important;
}
</style>
