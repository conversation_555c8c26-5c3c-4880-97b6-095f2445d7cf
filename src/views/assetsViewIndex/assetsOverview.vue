<template>
  <div class="conBanner">
    <div style="display: flex; justify-content: space-between">
      <div class="conIpAssets">
        <div class="conIpAssets-title">
          <div class="title-box header-label" @click="handleIP">
            <div class="blueBlock"></div>
            <span>IP资产</span>
            <img src="../../assets/images/arrowRight.png" alt="" />
          </div>
        </div>
        <div class="assets-container" v-loading="loading">
          <div class="conIpAssets-box">
            <div class="conIpAssets-total">
              <div class="total-top" v-for="(ipItem, index) in ipAssets" :key="index">
                <div class="total-top-li">
                  <i :style="{ background: ipItem.color }"></i>
                  {{ ipItem.title ? ipItem.title : 0 }}
                </div>
                <div class="total-top-num">
                  {{ myOverview[ipItem.word] ? myOverview[ipItem.word] : 0 }}
                </div>
              </div>
            </div>
            <div class="conIpAssets-line">
              <div
                v-for="(lineItem, index) in ipLength"
                :key="index"
                :style="{
                  backgroundColor: ipAssets[index].color,
                  width: lineItem
                }"
                :class="index > 0 ? 'line-li' : 'line-blue'"
              ></div>
            </div>
          </div>
          <div class="conIpAssets-label">
            <div class="conIpAssets-inline">
              <p class="inline-total">资产台账在/离线统计</p>
              <div class="con-total-box">
                <div class="conIpAssets-total">
                  <div class="total-top" v-for="(lineItem, index) in lineTotal" :key="index">
                    <div class="total-top-li">
                      <i :style="{ background: lineItem.color }"></i>
                      {{ lineItem.title ? lineItem.title : 0 }}
                    </div>
                    <div class="total-top-num">
                      {{ myOverview[lineItem.word] ? myOverview[lineItem.word] : 0 }}
                    </div>
                    <!-- <div class="total-top-num">12</div> -->
                  </div>
                </div>
                <div class="conIpAssets-line">
                  <div
                    v-for="(onItem, index) in lineWidth"
                    :key="index"
                    :style="{
                      backgroundColor: lineTotal[index].color,
                      width: onItem
                    }"
                    :class="index > 0 ? 'line-li' : 'line-blue'"
                  ></div>
                </div>
              </div>
            </div>
            <div class="conIpAssets-inline">
              <p class="inline-total">资产标签统计</p>
              <div class="con-total-box">
                <div class="conIpAssets-total">
                  <div class="total-top" v-for="(labelItem, index) in labelTotal" :key="index">
                    <div class="total-top-li">
                      <i :style="{ background: labelItem.color }"></i>
                      {{ labelItem.title ? labelItem.title : 0 }}
                    </div>
                    <div class="total-top-num">
                      {{ myOverview[labelItem.word] ? myOverview[labelItem.word] : 0 }}
                    </div>
                    <!-- <div class="total-top-num">12</div> -->
                  </div>
                </div>
                <div class="conIpAssets-line">
                  <div
                    v-for="(laItem, index) in labelWidth"
                    :key="index"
                    :style="{
                      backgroundColor: labelTotal[index].color,
                      width: laItem
                    }"
                    :class="index > 0 ? 'line-li' : 'line-blue'"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getOverviewStatistics, assetsViewRateCount } from '@/api/apiConfig/api.js'
import { mapGetters, mapState, mapMutations } from 'vuex'
export default {
  data() {
    return {
      number: {
        ip_num: 0,
        login_num: 0,
        domain_num: 0,
        cert_num: 0,
        rule_num: 0,
        number_num: 0
      },
      totalNumber: [
        {
          img: require('../../assets/images/assetsView/assetsTotal.png'),
          title: '资产总数',
          word: 'sure_ip_num'
        },

        {
          img: require('../../assets/images/assetsView/logEntry.png'),
          title: '登录入口总数',
          word: 'login_total'
        },
        {
          img: require('../../assets/images/assetsView/domainNames.png'),
          title: '域名总数',
          word: 'domain_total'
        },
        {
          img: require('../../assets/images/assetsView/certTotal.png'),
          title: '证书总数',
          word: 'cert_total'
        },
        {
          img: require('../../assets/images/assetsView/conpomentNumber.png'),
          title: '组件总数',
          word: 'sure_rule_num'
        },
        {
          img: require('../../assets/images/assetsView/numberAssets.png'),
          title: '数字资产总数',
          word: 'digital_num'
        }
      ],
      myOverview: {
        app_num: 0,
        sure_ip_num: 0,
        sure_port_num: 0,
        sure_rule_num: 0,
        threaten_ip_num: 0,
        threaten_port_num: 0,
        threaten_rule_num: 0,
        unsure_ip_num: 0,
        unsure_port_num: 0,
        unsure_rule_num: 0,
        digital_num: 0,
        wechat_num: 0,
        xiaochengxu_num: 0,
        login_total: 0,
        login_to_confirmed: 0,
        login_confirmed: 0,
        login_ignore: 0,
        domain_total: 0,
        domain_father: 0,
        domain_child: 0,
        cert_total: 0,
        cert_valid: 0,
        cert_notValid: 0,
        ignore_ip_num: 0,
        // 新添加的数据
        // 资产台账在/离线统计
        online_ip_num: 0,
        offline_ip_num: 0,
        // 资产标签统计
        recommend_label_num: 0,
        scan_label_num: 0
      }, //资产概览-数量统计
      ipAssets: [
        {
          title: '资产台账',
          color: '#2677FF',
          word: 'sure_ip_num'
        },
        {
          title: '疑似资产',
          color: '#FFC226',
          word: 'unsure_ip_num'
        },
        {
          title: '威胁资产',
          color: '#FF4646',
          word: 'threaten_ip_num'
        },
        {
          title: '忽略资产',
          color: '#D1D5DD',
          word: 'ignore_ip_num'
        }
      ], // IP资产
      // IP资产总览进度条长度
      ipLength: [],
      // 资产台账在/离线统计
      lineTotal: [
        {
          title: '在线',
          color: '#10D595',
          word: 'online_ip_num'
        },
        {
          title: '离线',
          color: '#D1D5DD',
          word: 'offline_ip_num'
        }
      ],
      // 资产台账在/离线统计进度条长度
      lineWidth: [],
      // 资产标签统计
      labelTotal: [
        {
          title: '推荐',
          color: '#10D595',
          word: 'recommend_label_num'
        },
        {
          title: '扫描',
          color: '#2677FF',
          word: 'scan_label_num'
        }
      ],
      // 资产标签统计长度
      labelWidth: [],
      loading: false,
      user: {
        role: ''
      },
      userInfo: {}
    }
  },

  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      await this.getData()
    } else {
      await this.getData()
    }
    this.ipWid()
    this.degreeWidth(this.myOverview.online_ip_num, this.myOverview.offline_ip_num, this.lineWidth)
    this.degreeWidth(
      this.myOverview.recommend_label_num,
      this.myOverview.scan_label_num,
      this.labelWidth
    )
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    async getData() {
      //资产概览-数量统计
      this.loading = true
      let res = await getOverviewStatistics({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        for (let i in this.myOverview) {
          this.myOverview[i] = res.data[i] ? res.data[i] : 0
        }
        this.myOverview.digital_num =
          this.myOverview.app_num + this.myOverview.wechat_num + this.myOverview.xiaochengxu_num
        this.getRate()
      }
    },
    async getRate() {
      let res = await assetsViewRateCount({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.myOverview.login_total = res.data.login.total ? res.data.login.total : 0
        this.myOverview.login_to_confirmed = res.data.login.to_confirmed
          ? res.data.login.to_confirmed
          : 0
        this.myOverview.login_confirmed = res.data.login.confirmed ? res.data.login.confirmed : 0
        this.myOverview.login_ignore = res.data.login.ignore ? res.data.login.ignore : 0
        this.myOverview.domain_total = res.data.domain.total ? res.data.domain.total : 0
        this.myOverview.domain_father = res.data.domain.father ? res.data.domain.father : 0
        this.myOverview.domain_child = res.data.domain.child ? res.data.domain.child : 0
        this.myOverview.cert_total = res.data.cert.total ? res.data.cert.total : 0
        this.myOverview.cert_valid = res.data.cert.valid ? res.data.cert.valid : 0
        this.myOverview.cert_notValid = res.data.cert.notValid ? res.data.cert.notValid : 0

        // this.myRateData = {
        //   account_rate: res.data.ledger ? res.data.ledger : 0,
        //   unsure_rate: res.data.doubt ? res.data.doubt : 0,
        //   threaten_rate: res.data.threaten ? res.data.threaten : 0,
        //   digital_rate: res.data.digital ? res.data.digital : 0,
        //   domain_rate: res.data.domain.rate ? res.data.domain.rate : 0,
        //   login_rate: res.data.login.rate ? res.data.login.rate : 0,
        //   cert_rate: res.data.cert.rate ? res.data.cert.rate : 0,
        // }
      }
    },
    // 计算ip资产进度条长度
    ipWid() {
      const sum =
        this.myOverview.sure_ip_num +
        this.myOverview.unsure_ip_num +
        this.myOverview.threaten_ip_num +
        this.myOverview.ignore_ip_num
      for (let i = 0; i < this.ipAssets.length; i++) {
        // this.ipLength[i] =
        //   (this.myOverview[this.ipAssets[i].word] / sum) * 100 + "%";
        this.$set(this.ipLength, i, (this.myOverview[this.ipAssets[i].word] / sum) * 100 + '%')
      }
    },
    degreeWidth(num1, num2, arr) {
      const sum = num1 + num2
      arr[0] = (num1 / sum) * 100 + '%'
      // this.$set(arr[0])
      arr[1] = (num2 / sum) * 100 + '%'
      return arr
    },
    handleIP() {
      sessionStorage.setItem('menuId', '1-3-1')
      this.changeMenuId('1-3-1')
      this.$router.push('/assetsLedger')
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  }
}
</script>

<style scoped lang="less">
.conBanner {
  width: 70%;
  .conIpAssets {
    // width: 790px;
    width: 100%;
    height: 370px;
    background-color: #fff;
    position: relative;
    margin-top: 20px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    .conIpAssets-title {
      height: 46px;
      border-bottom: 1px solid #e9ebef;
      font-size: 16px;

      color: #37393c;
      .title-box {
        // width: 10%;
        // margin-left: 12px;
        height: 46px;
        line-height: 46px;
        cursor: pointer;
        span {
          // margin-right: 8px;
          vertical-align: middle;
          font-weight: 400;
          // // font-family: PingFangSC-Semibold;
        }
        img {
          margin: 0 !important;
          vertical-align: middle;
        }
      }
    }
    // .conIpAssets-title::before {
    //   content: "";
    //   position: absolute;
    //   left: 0px;
    //   top: 15px;
    //   width: 4px;
    //   height: 16px;
    //   background-color: #2677ff;
    // }
    .assets-container {
      width: calc(100% - 48px);
      margin: 0 auto;
      .conIpAssets-box {
        padding-top: 24px;
        height: 100px;

        background-color: #f5f8fc;

        margin-top: 18px;
      }
      .conIpAssets-total {
        display: flex;
        justify-content: space-evenly;
        .total-top {
          .total-top-li {
            font-size: 14px;
            vertical-align: middle;
            color: #62666c;
            // font-family: PingFangSC-Regular;
            i {
              display: inline-block;
              width: 6px;
              height: 6px;
              margin-right: 5px;
              border-radius: 1px;
              -webkit-transform: rotate(135deg);
              -ms-transform: rotate(135deg);
              transform: rotate(135deg);
              vertical-align: middle;
            }
          }
          .total-top-num {
            // font-family: PingFangSC-Semibold;
            font-size: 20px;
            color: #37393c;
            text-align: center;
          }
        }
      }
      .conIpAssets-line {
        width: calc(100% - 84px);
        margin: 0 auto;
        display: flex;
        .line-li {
          // width: 25%;
          height: 8px;
          margin-top: 18px;
        }
        .line-blue {
          height: 12px;
          margin-top: 16px;
          // background-color: #2677FF;
        }
      }
      .conIpAssets-label {
        display: flex;
        justify-content: space-between;
        .conIpAssets-inline {
          margin-top: 15px;
          width: 49%;
          .inline-total {
            font-size: 14px;
            font-weight: 500;
            // font-family: PingFangSC-Medium;
            color: #37393c;
            margin-bottom: 12px;
          }
          .con-total-box {
            height: 115px;
            background-color: #f5f8fc;
          }
          .conIpAssets-total {
            .total-top {
              margin-top: 18px;
            }
          }
        }
      }
    }
  }
}
</style>
