<template>
  <div class="conBanner" v-loading="loading">
    <div class="conBanner-box">
      <div class="content" v-for="(item, index) in totalNumber" :key="index">
        <div class="content-box">
          <div class="content-left">
            <img :src="item.img" alt="" />
          </div>
          <div class="content-right">
            <p class="content-number">
              {{ myOverview[item.word] ? myOverview[item.word] : 0 }}
            </p>
            <p class="content-name">{{ item.title ? item.title : 0 }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import { getOverviewStatistics, assetsViewRateCount } from '@/api/apiConfig/api.js'
export default {
  data() {
    return {
      number: {
        ip_num: 0,
        login_num: 0,
        domain_num: 0,
        cert_num: 0,
        rule_num: 0,
        number_num: 0
      },
      totalNumber: [
        {
          img: require('../../assets/images/assetsView/assetsTotal.png'),
          title: '资产总数',
          word: 'sure_ip_num'
        },

        {
          img: require('../../assets/images/assetsView/logEntry.png'),
          title: '登录入口总数',
          word: 'login_total'
        },
        {
          img: require('../../assets/images/assetsView/domainNames.png'),
          title: '域名总数',
          word: 'domain_total'
        },
        {
          img: require('../../assets/images/assetsView/certTotal.png'),
          title: '证书总数',
          word: 'cert_total'
        },
        {
          img: require('../../assets/images/assetsView/conpomentNumber.png'),
          title: '组件总数',
          word: 'sure_rule_num'
        },
        {
          img: require('../../assets/images/assetsView/numberAssets.png'),
          title: '数字资产总数',
          word: 'digital_num'
        }
      ],
      myOverview: {
        app_num: 0,
        sure_ip_num: 0,
        sure_port_num: 0,
        sure_rule_num: 0,
        threaten_ip_num: 0,
        threaten_port_num: 0,
        threaten_rule_num: 0,
        unsure_ip_num: 0,
        unsure_port_num: 0,
        unsure_rule_num: 0,
        digital_num: 0,
        wechat_num: 0,
        xiaochengxu_num: 0,
        login_total: 0,
        login_to_confirmed: 0,
        login_confirmed: 0,
        login_ignore: 0,
        domain_total: 0,
        domain_father: 0,
        domain_child: 0,
        cert_total: 0,
        cert_valid: 0,
        cert_notValid: 0,
        ignore_ip_num: 0,
        // 新添加的数据
        // 资产台账在/离线统计
        online_ip_num: 0,
        offline_ip_num: 0,
        // 资产标签统计
        recommend_label_num: 0,
        scan_label_num: 0
      }, //资产概览-数量统计
      ipAssets: [
        {
          title: '资产台账',
          color: '#2677FF',
          word: 'sure_ip_num'
        },
        {
          title: '疑似资产',
          color: '#FFC226',
          word: 'unsure_ip_num'
        },
        {
          title: '威胁资产',
          color: '#FF4646',
          word: 'threaten_ip_num'
        },
        {
          title: '忽略资产',
          color: '#D1D5DD',
          word: 'ignore_ip_num'
        }
      ], // IP资产
      // IP资产总览进度条长度
      ipLength: [],
      // 资产台账在/离线统计
      lineTotal: [
        {
          title: '在线',
          color: '#10D595',
          word: 'online_ip_num'
        },
        {
          title: '离线',
          color: '#D1D5DD',
          word: 'offline_ip_num'
        }
      ],
      // 资产台账在/离线统计进度条长度
      lineWidth: [],
      // 资产标签统计
      labelTotal: [
        {
          title: '推荐',
          color: '#10D595',
          word: 'recommend_label_num'
        },
        {
          title: '扫描',
          color: '#2677FF',
          word: 'scan_label_num'
        }
      ],
      // 资产标签统计长度
      labelWidth: [],
      loading: false,
      user: {},
      userInfo: {}
    }
  },

  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      await this.getData()
    } else {
      await this.getData()
    }
    this.ipWid()
    this.degreeWidth(this.myOverview.online_ip_num, this.myOverview.offline_ip_num, this.lineWidth)
    this.degreeWidth(
      this.myOverview.recommend_label_num,
      this.myOverview.scan_label_num,
      this.labelWidth
    )
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    async getData() {
      //资产概览-数量统计
      this.loading = true
      let res = await getOverviewStatistics({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        for (let i in this.myOverview) {
          this.myOverview[i] = res.data[i] ? res.data[i] : 0
        }
        this.myOverview.digital_num =
          this.myOverview.app_num + this.myOverview.wechat_num + this.myOverview.xiaochengxu_num
        this.getRate()
      }
    },
    async getRate() {
      let res = await assetsViewRateCount({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.myOverview.login_total = res.data.login.total ? res.data.login.total : 0
        this.myOverview.login_to_confirmed = res.data.login.to_confirmed
          ? res.data.login.to_confirmed
          : 0
        this.myOverview.login_confirmed = res.data.login.confirmed ? res.data.login.confirmed : 0
        this.myOverview.login_ignore = res.data.login.ignore ? res.data.login.ignore : 0
        this.myOverview.domain_total = res.data.domain.total ? res.data.domain.total : 0
        this.myOverview.domain_father = res.data.domain.father ? res.data.domain.father : 0
        this.myOverview.domain_child = res.data.domain.child ? res.data.domain.child : 0
        this.myOverview.cert_total = res.data.cert.total ? res.data.cert.total : 0
        this.myOverview.cert_valid = res.data.cert.valid ? res.data.cert.valid : 0
        this.myOverview.cert_notValid = res.data.cert.notValid ? res.data.cert.notValid : 0

        // this.myRateData = {
        //   account_rate: res.data.ledger ? res.data.ledger : 0,
        //   unsure_rate: res.data.doubt ? res.data.doubt : 0,
        //   threaten_rate: res.data.threaten ? res.data.threaten : 0,
        //   digital_rate: res.data.digital ? res.data.digital : 0,
        //   domain_rate: res.data.domain.rate ? res.data.domain.rate : 0,
        //   login_rate: res.data.login.rate ? res.data.login.rate : 0,
        //   cert_rate: res.data.cert.rate ? res.data.cert.rate : 0,
        // }
      }
    },
    // 计算ip资产进度条长度
    ipWid() {
      const sum =
        this.myOverview.sure_ip_num +
        this.myOverview.unsure_ip_num +
        this.myOverview.threaten_ip_num +
        this.myOverview.ignore_ip_num
      for (let i = 0; i < this.ipAssets.length; i++) {
        // this.ipLength[i] =
        //   (this.myOverview[this.ipAssets[i].word] / sum) * 100 + "%";
        this.$set(this.ipLength, i, (this.myOverview[this.ipAssets[i].word] / sum) * 100 + '%')
      }
    },
    degreeWidth(num1, num2, arr) {
      const sum = num1 + num2
      arr[0] = (num1 / sum) * 100 + '%'
      // this.$set(arr[0])
      arr[1] = (num2 / sum) * 100 + '%'
      return arr
    },
    handleIP() {
      sessionStorage.setItem('menuId', '1-3-1')
      this.changeMenuId('1-3-1')
      this.$router.push('/assetsLedger')
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  }
}
</script>

<style scoped lang="less">
.conBanner {
  .conBanner-box {
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    background-color: #fff;
    // width: calc(100% - 240px);
    margin: 0 auto;
    // width: 1200px;
    margin: 0 auto;
    height: 120px;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    .content:nth-child(1) {
      margin-left: 33px;
    }
    .content:nth-last-child(1) {
      margin-right: 33px;
    }
    .content {
      width: 14%;
      .content-box {
        display: flex;
        margin-top: 30px;
        // margin-left: 30px;
      }
      .content-left {
        width: 56px;
        height: 56px;
        background-color: rgba(38, 119, 255, 0.08);
        margin-right: 12px;
        img {
          margin: 14px 0 0 16px;
        }
      }
      .content-right {
        .content-number {
          font-size: 24px;
          color: #37393c;
          // font-family: PingFangSC-Semibold;
          font-weight: 600;
        }
        .content-name {
          // font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #62666c;
        }
      }
    }
  }
}
</style>
