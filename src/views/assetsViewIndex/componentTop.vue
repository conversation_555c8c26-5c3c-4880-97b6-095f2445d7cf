<template>
  <div class="conWrap">
    <div class="conWrap-title">
      <div class="header-label">
        <div class="blueBlock"></div>
        <span>组件排行TOP5</span>
      </div>
    </div>
    <div class="com-top">
      <template v-if="componentsArr.length > 0">
        <div class="componentClass-box">
          <div class="list-compo" v-for="(item, index) in componentsArr" :key="index">
            <div class="top">
              <div>
                <span :class="'indexClass' + index">{{ index + 1 }}</span>
                <img
                  :src="showimg(item.name).result ? showimg(item.name).url : ''"
                  alt=""
                  class="productImg"
                />
                <i class="numClass">{{ item.name ? item.name : '-' }}</i>
              </div>
              <span
                class="numClass"
                style="color: #2677ff; cursor: pointer"
                @click="jumpPageByFilter(item.name)"
                >{{ item.count ? item.count : 0 }}</span
              >
            </div>

            <el-progress
              :percentage="(item.count / totalNum) * 100"
              :show-text="false"
              :color="colorArr[index]"
            ></el-progress>
          </div>
        </div>
      </template>
      <div v-else class="emptyClass">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-kong"></use>
        </svg>
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>
<script>
import { assetsViewRuleCount } from '@/api/apiConfig/api.js'

import { mapGetters, mapState, mapMutations } from 'vuex'
export default {
  props: ['taskIcon'],
  data() {
    return {
      componentsArr: [],
      totalNum: 0,
      num: 0,
      colorArr: [
        'linear-gradient(90deg, #FF7A7A 0%, #FF4646 100%)',
        'linear-gradient(90deg, #FFAE66 0%, #FF7900 100%)',
        'linear-gradient(90deg, #FFAE66 0%, #FF7900 100%)',
        'linear-gradient(90deg, #4EAFFF 0%, #2677FF 100%)',
        'linear-gradient(90deg, #4EAFFF 0%, #2677FF 100%)'
      ],
      loading: false,
      clickTimer: null
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getTableList()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getTableList()
    } else {
      this.getTableList()
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    infoCheck(id) {
      const _that = this
      clearTimeout(this.clickTimer) // 清除第一个单击事件
      this.clickTimer = setTimeout(function () {
        // 单击事件的代码执行区域
        window.open(
          `/alreadyTask_viewlist_ipinfo?id=${id}&fromIcon=0&preList=${_that.$route.path}`,
          '_blank'
        ) // fromIcon: 默认'0' 已完成任务列查看ip详情，传1
      }, 500)
    },
    jumpPageByFilter(rule_tags) {
      this.$emit('scrollToBottom')
      this.$router.push({ path: '/assetsLedger', query: { rule_tags } })
      sessionStorage.setItem('menuId', '1-3-1')
      this.changeMenuId('1-3-1')
    },
    async getTableList() {
      this.loading = true
      let res = await assetsViewRuleCount({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.componentsArr = res.data ? res.data : []
        let arr = []
        this.componentsArr.forEach((item) => {
          let count = item.count ? item.count / 1 : 0
          arr.push(count)
        })
        this.totalNum = Math.max.apply(null, arr) + 5 // 获取数量最大值
      }
    },
    // 判断图片是否存在
    showimg(url) {
      let imgUrl = ''
      url = url.replace('/', '')
      url = url.replace('.', '')
      url = url.replace(' ', '')
      let result = false
      url = './' + url + '.png'
      let urlTmp = url
      urlTmp = urlTmp.replace('./', '').replace('.png', '')
      // 目录下所有文件名
      const files = require.context('../../assets/images/componentIcon/', false, /\.png$/)
      const filedata = files.keys()
      filedata.forEach((item) => {
        if (item === url) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp + '.png')
        } else if (item === url.toLowerCase()) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp.toLowerCase() + '.png')
        }
      })
      // 返回结果
      return {
        result,
        url: imgUrl
      }
    }
  },
  beforeDestroy() {
    this.clickTimer = null
    clearTimeout(this.clickTimer)
  }
}
</script>
<style lang="less" scoped>
.conWrap {
  width: 30%;
  height: 370px;
  margin-top: 20px;
  margin-left: 20px;
  .conWrap-title {
    border-bottom: 1px solid #e9ebef;
    height: 46px;
    position: relative;
    .headerEcharts {
      // height: 46px;
      // margin-left: 15px;
      img {
        vertical-align: middle;
        margin-left: 8px;
      }
    }
    span {
      font-weight: 400 !important;
    }
  }

  .com-top {
    height: 287px;
    margin-top: 20px;
    .com-title {
      font-size: 14px;
      font-weight: 500;
      color: #37393c;
    }
  }

  .emptyClass {
    height: 80%;
    text-align: center;
    vertical-align: middle;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    svg {
      display: inline-block;
      font-size: 120px;
      // margin-top: 25%;
    }
    p {
      line-height: 25px;
      color: #d1d5dd;
      span {
        margin-left: 4px;
        color: #2677ff;
        cursor: pointer;
      }
    }
  }
  .componentClass-box {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    & > div {
      width: 100%;
      height: 48px;
      padding: 0 20px;
      margin-top: 12px;
      &:first-child {
        margin-top: 0;
      }
    }
    /deep/.el-progress-bar__outer {
      background: rgba(30, 31, 33, 0.08) !important;
    }
    .active {
      background-color: #ecf0f6;
      border-radius: 4px 0 0 4px;
    }
    .top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      margin-top: 7px;
      & > div > span {
        display: inline-block;
        width: 18px;
        height: 18px;
        text-align: center;
        line-height: 18px;
        color: #fff;
        opacity: 1;
        border-radius: 2px;
        margin-right: 8px;
      }
      img {
        // width: 16px;
        height: 16px;
        margin-right: 4px;
      }
      .numClass {
        color: rgba(55, 57, 60, 1);
        font-size: 14px;
      }
      .indexClass0 {
        background: rgba(16, 213, 149, 1);
      }
      .indexClass1 {
        background: rgba(248, 193, 54, 1);
      }
      .indexClass2 {
        background: rgba(255, 121, 0, 1);
      }
      .indexClass3 {
        background: rgba(38, 119, 255, 1);
      }
      .indexClass4 {
        background: rgba(38, 119, 255, 1);
      }
    }
  }
  .list-top {
    border-radius: 0 4px 4px 4px !important;
  }
  .list-last {
    border-radius: 4px 4px 4px 0 !important;
  }
  .componentClass-content {
    position: relative;
    width: 49%;
    .moreModule {
      box-sizing: border-box;
      z-index: 99;
      color: #2677ff;
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 62px;
      // margin-bottom: -20px;
      padding-top: 30px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 64%);
      text-align: center;

      &:hover {
        cursor: pointer;
      }

      .icon {
        height: 12px;
        width: 6px;
        margin-right: 0;
      }
    }
  }
  .componentClass-ips {
    box-sizing: border-box;
    background-color: #ecf0f6;
    border-radius: 4px;
    height: 335px;
    padding-bottom: 50px;
    overflow-y: auto;

    .ip-list {
      width: 90%;
      height: 30px;
      background-color: #fff;
      padding: 10px 2% 0 2%;
      font-size: 14px;
      box-shadow: 0px 2px 4px 0px rgba(0, 43, 115, 0.1);
      margin-top: 16px;
      margin-left: 3%;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      .ip {
        display: inline-block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: calc(100% - 145px);
        color: #2677ff;
      }
      .time {
        color: #62666c;
        display: inline-block;
      }
    }
  }
}
.list-compo {
  width: calc(100% - 32px);
  margin: 0 auto;
}
.list-compo:nth-child(1) /deep/ .el-progress-bar__inner {
  background: linear-gradient(90deg, #ff7a7a 0%, #ff4646 100%);
}
.list-compo:nth-child(2) /deep/ .el-progress-bar__inner {
  background: linear-gradient(90deg, #ffae66 0%, #ff7900 100%);
}
.list-compo:nth-child(3) /deep/ .el-progress-bar__inner {
  background: linear-gradient(90deg, #ffae66 0%, #ff7900 100%);
}
.list-compo:nth-child(4) /deep/ .el-progress-bar__inner {
  background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
}
.list-compo:nth-child(5) /deep/ .el-progress-bar__inner {
  background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
}
</style>
