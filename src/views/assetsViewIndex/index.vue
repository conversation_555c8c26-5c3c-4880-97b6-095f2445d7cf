<template>
  <div class="container">
    <div class="container-box" id="brief">
      <assetsTopVue />
      <div style="display:flex;justify-content:space-between">
        <assets />
        <numberAssets />
      </div>

      <div style="display:flex;justify-content:space-between">
        <assetsOverview />
        <componentTop @scrollToBottom="scrollToBottom" />
        <!-- <assetsDoing /> -->
      </div>
      <div style="display:flex;justify-content:space-between">
        <assetsDoing @scrollToBottom="scrollToBottom" />
        <threetypesAssets @scrollToBottom="scrollToBottom" />
      </div>
      <assetsTable />
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import assetsOverview from "./assetsOverview.vue";
import assets from "./assets.vue";
import numberAssets from './numberAssets.vue';
import componentTop from './componentTop.vue';
import threetypesAssets from './threetypes_assets.vue';
import assetsTable from './assetsBottomTable.vue';
import assetsTopVue from './assetsTop.vue';
import assetsDoing from './assetsDoing.vue';

export default {
  name: "index",
  components: { assetsTopVue, assetsOverview, assetsDoing, assets, numberAssets, componentTop, threetypesAssets, assetsTable },
  computed: {
    ...mapState(["scrollHeight"])
  },
  methods:{
    ...mapMutations(["setJumpScrollHeight"]),
    scrollToBottom(){
      let scrollHeight = document.getElementsByClassName('main_body')[0].scrollTop
      this.setJumpScrollHeight(scrollHeight)
    },
  },
  mounted(){
    document.getElementsByClassName('main_body')[0].scrollTop = this.scrollHeight
  },
};
</script>

<style lang="less" scoped>
.container {
  // height: 100%;/SS
  padding: 20px 20px 16px 16px;
  margin-bottom: 20px;
  .container-box {
    margin: 0 auto;
  }
}

/deep/.header-label {
  display: flex;
  align-items: center;
  height: 46px;
  font-size: 16px;
  font-weight: 600;
  line-height: 16px;
  img {
    vertical-align: middle;
    // margin-left: 8px;
  }
  span{
    margin-right: 8px;
  }
  .blueBlock{
    display: inline-block;
    width: 4px;
    height: 16px;
    border-radius:0 2px 2px 0;
    margin-right: 12px;
    background-color: #2677FF;
  }
}
</style>
