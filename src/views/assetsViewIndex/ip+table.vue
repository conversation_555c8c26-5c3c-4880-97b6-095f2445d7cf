<template>
  <div style="height: 450px" v-loading="loading">
    <tableList
      ref="tableList"
      :isChecked="false"
      :tableData="tableData"
      :radioParams="radioParams"
    />
    <!-- <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage" :page-sizes="pageSizeArr" :page-size="pageSize" layout="total, prev, pager, next, sizes, jumper" :total="total">
    </el-pagination> -->
  </div>
</template>

<script>
import sha1 from '@/utils/sha1Encrypt'
import tableList from '../home_set/indexTable.vue'
import { mapGetters, mapState } from 'vuex'
import { ansysDataIndex } from '@/api/apiConfig/asset.js'

export default {
  components: { tableList },
  data() {
    return {
      title: ['IP资产', 'IP+端口', '登录入口', '域名资产', '证书资产', '业务系统'],
      num: 0,
      tableData: [],
      formInline: {
        ip: '',
        clue_company_name: '',
        province: '', // 省份名称（传汉字）,
        keyword: '', // 123123,
        asn: '', // 123123,
        icp: '', // icp,
        url: '',
        title: '', // title,
        protocol: '', // protocol,
        logo: '', // logo,
        port: '', // port,
        cname: '', // cname,
        domain: [], // domain
        subdomain: [],
        online_state: [],
        lat: '',
        lon: '',
        state: '',
        reason: '',
        updated_at: []
      },
      identifier: '',
      loading: false,
      total: 0,
      radioParams: false,
      user: {
        role: ''
      },
      // currentCompany: 1,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      currentPage: 1
    }
  },
  methods: {
    // 已知资产列表
    async getknownAssetsList() {
      this.formInline.page = this.currentPage
      this.formInline.per_page = 10
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      this.tableData = []
      this.identifier = sha1(this.user.id + 'sha1' + new Date().getTime())
      let obj = {
        identifier: this.identifier,
        operate_company_id: this.currentCompany, // 安服角色操控的企业id集合，如果是企业租户，不需要这个字段,
        type: '', // 统计接口直接传''
        status: [1, 4],
        ...this.formInline
      }
      this.loading = true
      let res = await ansysDataIndex(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      if (res.code == 0) {
        this.loading = false
        this.tableData = res.data.items
        this.total = res.data.total
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val

      this.getknownAssetsList(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getknownAssetsList(true)
    }
  },
  watch: {
    getterCompanyChange(val) {
      if (this.user.role == 2) {
        this.getknownAssetsList()
      }
    }
  },
  mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getknownAssetsList()
    } else {
      this.getknownAssetsList()
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCompanyChange'])
  }
}
</script>

<style lang="less" scoped>
.assets-table {
  width: 100%;
  margin: 0 auto;
  background-color: #fff;
  margin-top: 13px;
  border-radius: 4px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  .assets-table-top {
    height: 45px;
    position: relative;
    width: 100%;
    border-bottom: 1px solid #e9ebef;

    .assets-table-title::before {
      content: '';
      position: absolute;
      left: 0px;
      top: 15px;
      width: 4px;
      height: 16px;
      background-color: #2677ff;
    }
    .assets-table-title {
      margin-left: 3%;
      display: flex;
      font-size: 16px;
      // font-family: PingFangSC-Regular;
      .title-list {
        width: 7%;
        height: 45px;
        line-height: 45px;
        margin: 0 1%;
        color: #62666c;
        text-align: center;
        cursor: pointer;
        // font-family: PingFangSC-Regular;
        font-size: 16px;
      }
      .active {
        border-bottom: 2px solid #2677ff;
        color: #2677ff;
      }
    }
  }
  .myTable {
    height: 500px;
    margin-top: 20px;
    .cardBox {
      height: 100%;
    }
  }
  .con {
    ::deep(.el-table) {
      width: 500px !important;
    }
  }
}
.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  & > div {
    .el-input {
      width: 240px;
    }
    .el-select {
      width: 240px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      // margin-left: 16px;
      cursor: pointer;
    }
  }
}
</style>
