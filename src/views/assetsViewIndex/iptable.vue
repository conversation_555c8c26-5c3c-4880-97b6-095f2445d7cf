<template>
  <div class="container">
    <div class="boxTwo1">
      <div class="tableLabel">
        <div class="tab">
          <div
            class="item"
            v-for="item in tabList"
            :key="item.val"
            :class="{ active: activeItemVal == item.val }"
            @click="changeTab(item)"
          >
            {{ item.label }}
          </div>
        </div>
        <div>
          <el-checkbox class="checkboxAll" id="keyword_all" v-model="checkedAll"
            >选择全部</el-checkbox
          >
          <el-button class="normalBtnRe" type="primary" @click="exportList">导出</el-button>
        </div>
      </div>
      <div class="myTable">
        <tableList
          :checkedAll="checkedAll"
          ref="tableList"
          :tableData="tableData"
          :isChecked="false"
          :tabIcon="tabIcon"
          :radioParams="false"
          @handleSelectionChange="handleSelectionChange"
        />
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
        :class="{ index: $route.path == '/index' }"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {
  orgAssetsList,
  orgDomainsList,
  orgDomainsExport,
  orgAssetsExport
} from '@/api/apiConfig/api.js'

import { mapGetters, mapState } from 'vuex'
import tableList from '../groupAssets/organization/assetsList.vue'
export default {
  components: {
    tableList
  },
  data() {
    return {
      checkedArr: [],
      checkedAll: false,
      activeItemVal: '1',
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 50, 100],
      total: 0,
      tabList: [
        { label: '台账-IP', val: '1' },
        { label: '疑似-IP', val: '2' },
        { label: '域名', val: '3' }
      ],
      tabIcon: 'IP'
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  mounted() {
    let isGetCompany = sessionStorage.getItem('isGetCompany')
    if (Boolean(isGetCompany)) {
      this.getData()
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.getData()
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    async exportList() {
      if (!this.checkedAll && this.checkedArr.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let { orgId, comId } = this.$route.query

      let is_all = this.checkedAll ? '1' : '0'
      let id = this.checkedArr.map((item) => item.id)
      let funcName = this.activeItemVal == 3 ? orgDomainsExport : orgAssetsExport
      let res = await funcName({
        id,
        is_all,
        assets_level: this.activeItemVal,
        operate_company_id: this.currentCompany,
        organization_group_id: orgId,
        company_id: comId
      })
      if (res.code == 0) {
        this.checkedAll = false
        this.download(this.showSrcIp + res.data.url)
        this.$refs.tableList.$refs.eltable.clearSelection()
        this.$message.success('导出成功')
      }
    },
    goBack() {
      // sessionStorage.setItem("menuId", "2-1");
      // this.changeMenuId("2-1");
      this.$router.go(-1)
      // this.$router.push({path:'',})
    },
    changeTab(item) {
      this.checkedAll = false
      this.checkedArr = []
      this.$refs.tableList.$refs.eltable.clearSelection()
      this.activeItemVal = item.val
      if (item.val == 3) {
        this.tabIcon = 'domain'
      } else {
        this.tabIcon = 'IP'
      }
      this.currentPage = 1
      this.getData()
    },
    async getData() {
      let { orgId, comId } = this.$route.query
      let funcName =
        this.activeItemVal == 1 || this.activeItemVal == 2 ? orgAssetsList : orgDomainsList
      let obj = {
        organization_group_id: orgId,
        company_id: comId,
        operate_company_id: this.currentCompany,
        page: this.currentPage,
        per_page: this.pageSize,
        assets_level: this.activeItemVal
      }
      let res = await funcName(obj)
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData('yesLoading')
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData('yesLoading')
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 100%;
}
.boxTwo1 {
  box-sizing: border-box;
  background-color: #fff;
  // height: 100%;
  height: calc(100% - 64px);
  padding: 16px 20px 0;
}
.myTable {
  padding: 0px 20px;
  height: calc(100% - 178px);
}
.tableLabel {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #37393c;
  justify-content: space-between;
  // margin-bottom: 16px;
  .tab {
    border-radius: 4px;
    overflow: hidden;
    .item {
      padding: 6px 12px;
      background: #e1e7f1;
      cursor: pointer;
      &.active {
        color: #fff;
        background: #2677ff;
      }
    }
  }
}

.tableLabel > div {
  display: flex;
  align-items: center;
  & > p {
    margin-right: 16px;
  }
  /deep/.el-input {
    width: 240px;
  }
  & > span {
    font-weight: 400;
    color: #2677ff;
    line-height: 20px;
    margin-left: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}
.index {
  margin-top: 20px;
}
</style>
