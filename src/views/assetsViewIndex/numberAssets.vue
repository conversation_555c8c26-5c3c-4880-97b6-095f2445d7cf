<template>
  <div class="number-box">
    <div class="container-box">
      <div class="number-top header-label" @click="handleNum">
        <div class="blueBlock"></div>
        <span class="title">数字资产</span>
        <img src="../../assets/images/arrowRight.png" alt="" />
      </div>
      <div class="number-content" v-loading="loading">
        <div class="content-top">
          <div
            class="top-list"
            v-for="(item, index) in titleList"
            :key="index"
            @click="num = item.num"
            :class="{ 'list-active': num == item.num }"
          >
            <div class="title-top">
              <img :src="item.img" alt="" />
              {{ item.title ? item.title : '-' }}
            </div>
            <div class="title-bottom">
              {{ total[index] ? total[index] : '-' }}
            </div>
          </div>
        </div>
        <div
          class="content-bottom"
          v-if="num == 0 ? appNum != 0 : num == 1 ? gongzhonghao != 0 : xiaochengxu != 0"
        >
          <div
            class="bottom-all"
            v-for="(bottomItem, n) in num == 0 ? app : num == 1 ? gongzhonghao : xiaochengxu"
            :key="n"
          >
            <div class="all-box">
              <div class="box-img">
                <el-image
                  :class="{ 'wechat-img': num == 1 }"
                  :src="num == 1 ? bottomItem.erweima : bottomItem.logo"
                  lazy
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
              <div class="name-title">
                <span class="box-title">{{ bottomItem.name ? bottomItem.name : '-' }}</span>
                <div class="status">
                  <span class="status-yel" v-if="!bottomItem.status || bottomItem.status == 0"
                    >待处理</span
                  >
                  <span class="status-blue" v-if="bottomItem.status == 1">已确认</span>
                  <span class="status-gray" v-if="bottomItem.status == 2">已忽略</span>
                  <span class="status-red" v-if="bottomItem.status == 3">仿冒</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="emptyClass">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-kong"></use>
          </svg>
          <p>暂无数据</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { dataAssetsTable } from '@/api/apiConfig/api.js'

import { mapGetters, mapState, mapMutations } from 'vuex'
export default {
  props: ['taskIcon'],
  data() {
    return {
      dataList: [],
      num: 1,
      limitMoveNumMax: 0,
      titleList: [
        {
          title: '公众号',
          img: require('../../assets/images/assetsView/numberAssets/gongzhonghao.png'),
          word: 'gongzhonghao',
          num: 1
        },
        {
          title: 'APP',
          img: require('../../assets/images/assetsView/numberAssets/app.png'),
          word: 'APP',
          num: 0
        },
        {
          title: '小程序',
          img: require('../../assets/images/assetsView/numberAssets/xiaochengxu.png'),
          word: 'xiaochengxu',
          num: 2
        }
      ],
      total: [],
      app: [],
      gongzhonghao: [],
      xiaochengxu: [],
      appNum: 0,
      gzhNum: 0,
      xcxNum: 0,
      loading: false
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getTableList()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getTableList()
    } else {
      this.getTableList()
    }
  },
  methods: {
    async getTableList() {
      this.gzhNum = 0
      this.xcxNum = 0
      this.appNum = 0
      this.gongzhonghao = []
      this.xiaochengxu = []
      this.app = []
      this.loading = true
      let res = await dataAssetsTable({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.dataList = res.data ? res.data : []
        this.limitMoveNumMax = this.dataList.length
      }
      this.arrFormat()
    },
    arrFormat() {
      for (let i = 0; i < this.dataList.length; i++) {
        if (this.dataList[i].type == 4) {
          this.gongzhonghao.push(this.dataList[i])
          this.gzhNum++
        } else if (this.dataList[i].type == 5) {
          this.xcxNum++
          this.xiaochengxu.push(this.dataList[i])
        } else if (this.dataList[i].type == 6) {
          this.appNum++
          this.app.push(this.dataList[i])
        }
      }
      this.$set(this.total, 0, this.gzhNum)
      this.$set(this.total, 1, this.appNum)
      this.$set(this.total, 2, this.xcxNum)
      // this.total.push();
      // this.total.push(this.gzhNum);
      // this.total.push(this.xcxNum);
    },
    ...mapMutations(['changeMenuId']),
    handleNum() {
      sessionStorage.setItem('menuId', '1-7')
      this.changeMenuId('1-7')
      this.$router.push('/newAssets')
    },
    image(img) {
      return 'https://open.weixin.qq.com/qr/code?username=' + img
    }
  }
}
</script>

<style lang="less" scoped>
.number-box {
  width: 30%;
  margin-left: 20px;
  // margin-top: 20px;
  position: relative;
  .container-box {
    height: 530px;
    width: 100%;
    margin-top: 20px;
    background-color: #fff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
  }
  // .number-top::before {
  //   content: "";
  //   position: absolute;
  //   left: 0px;
  //   top: 15px;
  //   width: 4px;
  //   height: 16px;
  //   background-color: #2677ff;
  // }
  .number-top {
    cursor: pointer;
    height: 46px;
    // padding-top: 12px;
    // padding-left: 16px;
    border-bottom: 1px solid #e9ebef;
    .title {
      // font-family: PingFangSC-Semibold;
      color: #37393c;
      font-size: 16px;
      font-weight: 400;
    }
    img {
      margin: 0 !important;
      vertical-align: middle;
    }
  }
  .number-content {
    height: 468px;
    overflow: hidden;
    width: 90%;
    margin: 0 auto;
    .content-top {
      background-color: #f5f8fc;
      margin-top: 25px;
      height: 85px;
      margin-bottom: 24px;
      display: flex;
      border-radius: 4px;
      align-content: center;
      justify-content: center;
      align-items: center;
      .list-active {
        border: 1px solid #2677ff;
        background: linear-gradient(180deg, #f4f8ff 0%, #e1ecff 100%);
        box-shadow: 2px 0px 4px 0px rgba(0, 0, 0, 0.12);
        border-radius: 4px;
      }
      .top-list {
        width: 33%;
        height: 86px;
        // padding-top: 20px;
        cursor: pointer;
        display: flex;
        box-sizing: border-box;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .title-top {
          text-align: center;
          margin: 0 auto;
          width: 90%;
          // font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #37393c;
          margin-top: 16px;

          img {
            vertical-align: middle;
            margin-left: 5px;
          }
        }
        .title-bottom {
          width: 50%;
          margin: 0 auto;
          text-align: center;
          // // font-family: PingFangSC-Semibold;
          color: #37393c;
          font-size: 20px;
          margin-top: 7px;
        }
      }
    }
    .content-bottom {
      height: 340px;
      overflow: auto;
      margin: 0 auto;
      .bottom-all:nth-child(1) {
        margin: 0;
      }
      .bottom-all {
        // width: 340px;
        width: 98%;
        height: 60px;
        border: 1px solid #d8e1f3;
        margin-top: 12px;
        // padding: 12px 0 0 16px ;
        border-radius: 4px;
        .all-box {
          margin-top: 12px;
          margin-left: 16px;
          display: flex;
          box-sizing: border-box;
          flex-wrap: nowrap;
          .box-img {
            position: relative;
            display: inline-block;
            width: 36px;
            height: 36px;
            overflow: hidden;
          }
          .wechat-img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-52%);
            width: 195px;
            height: 195px;
            // background-size: 195px 195px;
            // background-position: center center;
          }
          .wechatImg {
            width: 142px;
            height: 142px;
            margin: -56px -56px;
          }
          img {
            vertical-align: middle;
            margin-right: 10px;
            width: 36px;
            height: 36px;
          }
          .name-title {
            width: 80%;
            margin-left: 2%;
            display: flex;
            overflow: hidden;
            text-overflow: ellipsis;
            justify-content: flex-end;
          }
          .box-title {
            // font-family: PingFangSC-Semibold;
            font-size: 14px;
            color: #37393c;
            display: inline-block;
            // width: 220px;
            width: 65%;
            height: 36px;
            line-height: 36px;
            justify-content: space-around;
          }
          .status-red {
            padding: 2px 8px;
            // text-align: right;
            display: inline-block;
            // width: 50px;
            // border: 1px solid #ff4646;
            background: rgba(255, 70, 70, 0.1216);
            color: #ff4646;
            text-align: center;
            // font-family: PingFangSC-Regular;
            border-radius: 4px;
          }
          .status-blue {
            padding: 2px 8px;
            // text-align: right;
            display: inline-block;
            // width: 50px;
            // border: 1px solid #2677ff;
            background: rgba(38, 119, 255, 0.12);
            color: #2677ff;
            text-align: center;
            // font-family: PingFangSC-Regular;
            border-radius: 4px;
          }
          .status {
            margin-top: 8px;
          }
          .status-yel {
            padding: 2px 8px;
            // text-align: right;
            display: inline-block;
            // width: 50px;
            // border: 1px solid #ff7900;
            background: rgba(255, 121, 0, 0.12);
            color: #ff7900;
            text-align: center;
            // font-family: PingFangSC-Regular;
            border-radius: 4px;
          }
          .status-gray {
            padding: 2px 8px;
            // text-align: right;
            display: inline-block;
            // width: 50px;
            // border: 1px solid #a9abac;
            background: rgba(211, 228, 231, 0.12);
            color: gray;
            text-align: center;
            // font-family: PingFangSC-Regular;
            border-radius: 4px;
          }
        }
      }
    }
    .emptyClass {
      height: 75%;
      text-align: center;
      vertical-align: middle;
      svg {
        display: inline-block;
        font-size: 120px;
        margin-top: 25%;
      }
      p {
        line-height: 25px;
        color: #d1d5dd;
      }
    }
  }
}
</style>
