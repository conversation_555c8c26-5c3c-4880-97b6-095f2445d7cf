<template>
  <div class="conWrap">
    <div v-if="!titleIsShow" class="headerEcharts"><i></i>任务概览</div>
    <!-- 任务概览页面不显示标题，动图 -->
    <el-carousel
      v-if="carouselArr.length > 0"
      :class="titleIsShow == 1 ? 'taskClass' : 'taskClass taskClass1'"
      :autoplay="false"
      :interval="5000"
      indicator-position="outside"
    >
      <el-carousel-item v-for="(item, index) in carouselArr" :key="index">
        <div class="itemClass">
          <div v-if="!titleIsShow" class="tu tuThird" lazy></div>
          <!-- proClassTask 任务概览需要单独配置高度 -->
          <div :class="!titleIsShow ? 'proClass' : 'proClass proClassTask'">
            <p class="taskName"
              ><span>{{ item.taskName }}</span
              ><span>{{ item.progress }}%</span></p
            >
            <el-progress :percentage="item.progress" :show-text="false"></el-progress>
            <p class="timeClass">
              <span class="taskIcon">{{ item.icon }}</span>
              {{ item.time }}</p
            >
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
    <div class="taskEmpty" v-else>
      <p>暂无任务正在进行</p>
      <el-dropdown class="dropdownClass" trigger="click">
        <el-button class="normalBtn" type="primary"
          >发起任务<i class="el-icon-arrow-down el-icon--right"></i
        ></el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="goTask('2-3', '/unitIndex')" id="account_deal_place"
            >单位资产测绘</el-dropdown-item
          >
          <el-dropdown-item @click.native="goTask('2-2', '/assetsCloud')" id="account_deal_ignore"
            >云端推荐资产</el-dropdown-item
          >
          <el-dropdown-item @click.native="goTask('2-1', '/assetsScan')" id="account_deal_threat"
            >资产扫描任务</el-dropdown-item
          >
          <el-dropdown-item @click.native="goTask('2-4', '/checkTask')" id="account_deal_threat"
            >资产核查任务</el-dropdown-item
          >
          <el-dropdown-item @click.native="goTask('2-5', '/statusTask')" id="account_deal_threat"
            >资产状态检测</el-dropdown-item
          >
          <el-dropdown-item @click.native="goTask('2-6', '/domainTask')" id="account_deal_threat"
            >域名发现任务</el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>
<script>
import { auditTaskInfo, statusDetectTaskInfo } from '@/api/apiConfig/api.js'
import { getAllTaskList } from '@/api/apiConfig/discovery.js'
import { detectTaskInfo } from '@/api/apiConfig/surveying.js'
import { domainAssetTaskList } from '@/api/apiConfig/domain.js'

import { mapGetters, mapState, mapMutations } from 'vuex'
export default {
  props: ['taskIcon', 'titleIsShow'],
  data() {
    return {
      carouselArr: [],
      totalNum: 0,
      taskTimer: null,
      isClearTimeout: true
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getTaskInfo()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.carouselArr = []
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getTaskInfo()
    } else {
      this.getTaskInfo()
    }
  },
  beforeDestroy() {
    this.isClearTimeout = false
    clearTimeout(this.taskTimer)
    this.taskTimer = null
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goTask(menuId, path) {
      sessionStorage.setItem('menuId', menuId)
      this.changeMenuId(menuId)
      this.$router.push(path)
    },
    async getTaskInfo(taskId) {
      // 获取任务详情数据，任务记录id存在，查询对应的详情数据；不存在后端直接查询未完成的任务数据，有返回值则展示，没有返回值新建流程
      this.carouselArr = []
      // 云端推荐
      let obj = {
        taskId: '',
        data: {
          operate_company_id: this.currentCompany,
          expand_source: 1 // 1云端推荐，0单位资产测绘
        }
      }
      this.loading = true
      let res = await detectTaskInfo(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        if (res.data) {
          let progress = 0
          if (res.data.step == 2) {
            progress = 40
          } else if (res.data.step == 3) {
            progress = 60
          } else if (res.data.step == 4) {
            progress = 80
          }
          this.carouselArr.push({
            icon: '云端推荐',
            progress: progress,
            taskName: res.data.name,
            time: res.data.updated_at
          })
        }
      }
      // 单位资产测绘
      let objDw = {
        taskId: '',
        data: {
          operate_company_id: this.currentCompany,
          expand_source: 0 // 1云端推荐，0单位资产测绘
        }
      }
      let resDw = await detectTaskInfo(objDw)
      if (resDw.code == 0) {
        if (resDw.data) {
          let progress = 0
          if (resDw.data.step == 2) {
            progress = 40
          } else if (resDw.data.step == 3) {
            progress = 60
          } else if (resDw.data.step == 4) {
            progress = 80
          }
          this.carouselArr.push({
            icon: '单位资产测绘',
            progress: progress,
            taskName: resDw.data.name,
            time: resDw.data.updated_at
          })
        }
      }
      // 扫描
      let objScan = {
        page: 1,
        per_page: 10,
        type: 0, // 0立即执行1周期扫描
        task_type: 1, // 任务分类 1 资产扫描 2 漏洞扫描,
        name: '',
        status: 1, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
        sort_field: 'status',
        sort_order: 'asc',
        is_schedule: 0,
        operate_company_id: this.currentCompany
      }
      let resScan = await getAllTaskList(objScan)
      if (resScan.code == 0) {
        let data =
          resScan.data.items && resScan.data.items.length > 0 ? resScan.data.items[0] : null
        if (data) {
          this.carouselArr.push({
            icon: '资产扫描',
            progress: data.progress / 1,
            taskName: data.name,
            time: data.updated_at
          })
        }
      }
      // 核查
      let objhc = {
        taskId: '',
        operate_company_id: this.currentCompany
      }
      let reshc = await auditTaskInfo(objhc)
      if (reshc.code == 0) {
        if (reshc.data) {
          this.carouselArr.push({
            icon: '资产核查',
            progress: reshc.data.progress,
            taskName: reshc.data.name,
            time: reshc.data.updated_at
          })
        }
      }
      // 状态
      let objzt = {
        taskId: '',
        operate_company_id: this.currentCompany
      }
      let reszt = await statusDetectTaskInfo(objzt)
      if (reszt.code == 0) {
        if (reszt.data) {
          this.carouselArr.push({
            icon: '资产状态检测',
            progress: reszt.data.progress,
            taskName: reszt.data.name,
            time: reszt.data.updated_at
          })
        }
      }
      // 域名任务
      let objdomain = {
        page: 1,
        per_page: 10,
        operate_company_id: this.currentCompany,
        status: 1 // 扫描中
      }
      this.loading = true
      let resDomain = await domainAssetTaskList(objdomain)
      if (resDomain.code == 0) {
        let data =
          resDomain.data.items && resDomain.data.items.length > 0 ? resDomain.data.items[0] : ''
        if (data) {
          this.carouselArr.push({
            icon: '域名发现',
            progress: data.progress / 1,
            taskName: data.name,
            time: data.updated_at
          })
        }
      }
      // 如果有正在执行任务，定时请求更新进度
      if (this.carouselArr.length > 0 && this.isClearTimeout) {
        // if (this.carouselArr.length > 0 && this.$route.path == '/assetsTanzhi') {
        clearTimeout(this.taskTimer)
        this.taskTimer = null
        this.taskTimer = setTimeout(() => {
          this.getTaskInfo()
        }, 6000)
      } else {
        clearTimeout(this.taskTimer)
        this.taskTimer = null
      }
    }
  }
}
</script>
<style lang="less" scoped>
.conWrap {
  height: 100%;
  border-radius: 4px;
  margin-bottom: 12px;
  // background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(240, 246, 255, 1) 100%);
  background: url('../../assets/images/taskViewTop.png') no-repeat;
  background-size: 100% 100%;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  .taskEmpty {
    width: 100%;
    height: 70%;
    text-align: center;
    overflow: hidden;
    p {
      width: 100%;
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      color: rgba(172, 180, 192, 1);
      margin: 20px 0 12px 0;
    }
  }
  .headerEcharts {
    padding: 20px 0 10px 0;
  }
  /deep/ul {
    justify-content: center;
  }
  /deep/.el-progress-bar__outer {
    background: rgba(30, 31, 33, 0.08) !important;
  }
  /deep/ .taskClass1 {
    height: 66% !important;
    padding-top: 10px !important;
  }
  /deep/.taskClass {
    height: 100%;
    .el-carousel__container {
      height: 98% !important;
    }
    .el-carousel__button {
      background: rgba(209, 213, 221, 0.5);
    }
    .el-carousel__indicator.is-active button {
      background: rgba(38, 119, 255, 1);
    }
    .el-carousel__indicators--horizontal {
      position: absolute;
      bottom: 1px !important;
      li {
        padding: 5px 4px;
      }
    }
    .itemClass {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      flex-wrap: nowrap;
      align-items: center;
      .proClass {
        width: calc(100% - 150px);
        margin: 0 16px;
        .taskName {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          font-weight: 500;
          color: rgba(55, 57, 60, 1);
          span:nth-child(2) {
            color: rgba(38, 119, 255, 1);
          }
        }
        .el-progress {
          margin: 16px 0;
        }
        .timeClass {
          color: rgba(98, 102, 108, 1);
          .taskIcon {
            color: #fff;
            padding: 2px 2px;
            margin-right: 10px;
            background: rgba(38, 119, 255, 0.4);
            border: 1px solid rgba(233, 241, 255, 1);
          }
        }
      }
      .proClassTask {
        width: 95% !important;
      }
    }
    .tu {
      width: 120px !important;
      height: 120px !important;
      margin-right: 12px;
      background-repeat: no-repeat;
      background-size: 100%;
      background-position-y: 0px;
      animation: running 6s steps(149) infinite;
      -webkit-animation: running 6s steps(149) infinite;
    }
    @keyframes running {
      0% {
        background-position-y: 0px;
      }
      100% {
        background-position-y: -17880px;
      }
    }
    .tuThird {
      background-image: url('../../assets/images/dongtu1.png');
    }
  }
}
</style>
