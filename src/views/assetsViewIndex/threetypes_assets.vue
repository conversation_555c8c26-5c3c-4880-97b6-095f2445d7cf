<template>
  <div class="threeTypes">
    <div class="threeTypes-title">
      <div
        class="title-list"
        v-for="(item, index) in titleMap"
        :key="index"
        :class="{ active: num == index }"
        @click="handleClick(item, index)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="threeTypes-bottom" v-loading="changeTabLoading">
      <div class="list" :class="activeTitleItem.className">
        <ul class="list-title">
          <li
            v-for="(item, index) in contentTableList[activeTitleItem.value]"
            :key="index"
            :class="item.className ? item.className : 'item'"
          >
            {{ item.label }}
          </li>
        </ul>
        <div class="three-content-box" v-if="num == 0 && cdnAssets.length != 0">
          <ul class="content" v-for="(item, n) in cdnAssets" :key="n">
            <el-tooltip effect="dark" :content="item.ip ? item.ip : '-'" placement="top">
              <li class="item" style="color: rgba(38, 119, 255, 1)">
                <span class="ip" @click="infoCheck(item._id)">
                  {{ item.ip ? item.ip : '-' }}
                </span>
                <span class="cdn-tag">CDN</span>
              </li>
            </el-tooltip>
            <li class="time-li">
              {{ item.updated_at ? item.updated_at : '-' }}
            </li>
          </ul>
        </div>
        <div class="three-content-box" v-else-if="num == 1 && domainArr.length != 0">
          <ul class="content" v-for="(item, n) in domainArr" :key="n">
            <el-tooltip effect="dark" :content="item.domain ? item.domain : '-'" placement="top">
              <li
                class="item"
                style="color: rgba(38, 119, 255, 1); cursor: pointer"
                @click="goDetail(item.id)"
              >
                {{ item.domain ? item.domain : '-' }}
              </li>
            </el-tooltip>
            <li class="time-li">
              {{ item.updated_at ? item.updated_at : '-' }}
            </li>
          </ul>
          <infinite-loading
            :distance="20"
            v-if="domainArr.length >= 10"
            @infinite="infiniteHandler"
            key="domain"
          ></infinite-loading>
        </div>
        <div class="three-content-box" v-else-if="num == 2 && outArr.length != 0">
          <ul class="content" v-for="(item, n) in outArr" :key="n">
            <el-tooltip
              effect="dark"
              :content="item.subject_cn ? item.subject_cn : '-'"
              placement="top"
            >
              <li
                class="item"
                style="color: rgba(38, 119, 255, 1); cursor: pointer"
                @click="checkInfo(item.id)"
              >
                {{ item.subject_cn ? item.subject_cn : '-' }}
              </li>
            </el-tooltip>
            <el-tooltip
              effect="dark"
              :content="item.issuer_cn ? item.issuer_cn : '-'"
              placement="top"
            >
              <li class="item">
                {{ item.issuer_cn ? item.issuer_cn : '-' }}
              </li>
            </el-tooltip>
            <li class="time-li">
              {{ item.not_after ? item.not_after : '-' }}
            </li>
          </ul>
          <infinite-loading
            :distance="20"
            v-if="outArr.length >= 10"
            @infinite="infiniteHandler"
            key="overdue"
          ></infinite-loading>
        </div>
        <template v-else>
          <div class="emptyClass">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-kong"></use>
            </svg>
            <p>暂无数据</p>
          </div>
        </template>
      </div>
      <div class="moreModule" @click="jumpPageByFilter()">
        查看更多
        <svg class="icon svg-icon" aria-hidden="true">
          <use xlink:href="#icon-more-right"></use>
        </svg>
        <svg class="icon svg-icon" aria-hidden="true">
          <use xlink:href="#icon-more-right"></use>
        </svg>
      </div>
    </div>
    <CertAssetDetail ref="certAssetDetail" />
  </div>
</template>

<script>
import { certAssetsList, cdnAssetList } from '@/api/apiConfig/api.js'
import { domainAssetList } from '@/api/apiConfig/domain.js'
import CertAssetDetail from '../certAsset/certAssetDetail.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import InfiniteLoading from 'vue-infinite-loading'
export default {
  components: { InfiniteLoading, CertAssetDetail },
  props: ['taskIcon'],
  data() {
    return {
      changeTabLoading: false,
      infiniteTimer: null,
      cdnAssets: [],
      domainArr: [],
      titleMap: [
        { label: 'CDN资产', value: 'cdn', className: 'cdn' },
        { label: '泛域名解析', value: 'parse', className: 'parse' },
        { label: '过期证书', value: 'overdue', className: 'overdue' }
      ],
      activeTitleItem: { label: 'CDN资产', value: 'cdn', className: 'cdn' },
      contentTableList: {
        cdn: [{ label: 'IP' }, { label: '更新时间', className: 'time-li' }],
        parse: [{ label: '域名' }, { label: '更新时间', className: 'time-li' }],
        overdue: [
          { label: '使用者' },
          { label: '颁发者' },
          { label: '有效期', className: 'time-li' }
        ]
      },
      num: 0,
      domainInline: {
        company_name: [],
        page: 1,
        per_page: 20,
        source: [],
        open_parse: 1,
        keyword: '',
        domain: [],
        type: '',
        status: '',
        created_at_range: [],
        updated_at_range: [],
        operate_company_id: '',
        title: [],
        status_code: [],
        page: 1,
        busy: false
        // domain_arr:[]
      },
      formInline: {
        page: 1,
        per_page: 20,
        keyword: '',
        issuer_cn: '',
        subject_cn: '',
        subject_org: '',
        ip: '',
        domain: '',
        version: '',
        is_valid: '',
        not_after: 0,
        created_at: [],
        updated_at: [],
        operate_company_id: ''
      },
      arr: [],
      outArr: [],
      page: 1,
      certPage: 1,
      t: true,
      busy: false,
      loading: false,
      clickTimer: null
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.num = 0
        this.domainArr = []
        this.outArr = []
        this.getTableList()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2 && !this.currentCompany) return
    this.getTableList()
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    checkInfo(id) {
      this.$refs.certAssetDetail.checkInfo(id)
    },
    // 跳转到详情
    goDetail(id) {
      window.open(`/domainDetails?id=${id}`, '_blank')
    },
    infoCheck(id) {
      const _that = this
      clearTimeout(this.clickTimer) // 清除第一个单击事件
      this.clickTimer = setTimeout(function () {
        // 单击事件的代码执行区域
        window.open(
          `/alreadyTask_viewlist_ipinfo?id=${id}&fromIcon=0&preList=${_that.$route.path}`,
          '_blank'
        ) // fromIcon: 默认'0' 已完成任务列查看ip详情，传1
      }, 500)
    },
    handleClick(item, index) {
      if (document.getElementsByClassName('three-content-box')[0]) {
        document.getElementsByClassName('three-content-box')[0].scrollTop = 0
      }
      this.domainArr = []
      this.outArr = []
      this.cdnAssets = []
      this.num = index
      this.activeTitleItem = item
      this.page = 1
      this.certPage = 1
      this.infiniteTimer = null
      clearTimeout(this.infiniteTimer)
      if (this.num == 0) {
        this.getTableList()
      } else if (this.num == 1) {
        this.getDomain(1)
      } else if (this.num == 2) {
        this.getAssets(1)
      }
    },
    jumpPageByFilter() {
      this.$emit('scrollToBottom')
      if (this.num == 0) {
        this.$router.push({ path: '/assetsLedger', query: { is_cdn: true } })
        sessionStorage.setItem('menuId', '1-3-1')
        this.changeMenuId('1-3-1')
      } else if (this.num == 1) {
        this.$router.push({
          path: '/domainAsset',
          query: { open_parse: true }
        })
        sessionStorage.setItem('menuId', '1-3-3')
        this.changeMenuId('1-3-3')
      } else if (this.num == 2) {
        this.$router.push({ path: '/certAsset', query: { overdue: true } })
        sessionStorage.setItem('menuId', '1-3-4')
        this.changeMenuId('1-3-4')
      }
    },
    async getTableList() {
      this.loading = true
      this.changeTabLoading = true
      let res = await cdnAssetList({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
        this.changeTabLoading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.changeTabLoading = false
        this.cdnAssets = res.data ? res.data : []
      }
      // this.getDomain();
    },
    getDomain(page) {
      let obj = {
        // page: this.formInline.page,
        page: page,
        per_page: this.domainInline.per_page,
        keyword: this.domainInline.keyword,
        company_name: this.domainInline.company_name,
        source: this.domainInline.source,
        domain: this.domainInline.domain,
        type: this.domainInline.type,
        open_parse: this.domainInline.open_parse,
        created_at_range: this.domainInline.created_at_range,
        updated_at_range: this.domainInline.updated_at_range,
        operate_company_id: this.currentCompany,
        title: this.domainInline.title,
        status_code: this.domainInline.status_code,
        status: this.domainInline.status
      }
      this.changeTabLoading = true
      domainAssetList(obj)
        .then((res) => {
          const len = this.domainArr.length
          const resLen = res.data.items.length
          if (res.data.items != []) {
            for (let i = len; i < resLen + len; i++) {
              this.$set(this.domainArr, i, res.data.items[i - len] ? res.data.items[i - len] : [])
            }
          } else {
            this.domainArr = []
          }
          this.changeTabLoading = false
          // this.getOut();
        })
        .catch((error) => {
          this.changeTabLoading = false
          this.domainArr = []
        })
    },
    getAssets(page) {
      this.formInline.page = page
      this.formInline.operate_company_id = this.currentCompany

      this.changeTabLoading = true
      certAssetsList(this.formInline)
        .then((res) => {
          const len = this.outArr.length
          const resLen = res.data.items.length
          if (this.outArr != []) {
            for (let i = len; i < resLen + len; i++) {
              this.$set(this.outArr, i, res.data.items[i - len] ? res.data.items[i - len] : [])
            }
          } else {
            this.outArr == []
          }
          this.changeTabLoading = false
        })
        .catch((error) => {
          this.changeTabLoading = false
          this.outArr = []
        })
    },
    infiniteHandler($state) {
      this.infiniteTimer = null
      // 这里模仿加载延迟1秒钟
      if (this.domainArr.length >= 20 && this.num == 1) {
        clearTimeout(this.infiniteTimer)
        console.log('infiniteTimer--domainArr')
        this.infiniteTimer = setTimeout(() => {
          this.page++
          this.getDomain(this.page)
          $state.loaded()
        }, 5000)
      }
      if (this.outArr.length >= 20 && this.num == 2) {
        clearTimeout(this.infiniteTimer)
        console.log('infiniteTimer--outArr')
        this.infiniteTimer = setTimeout(() => {
          this.certPage++
          this.getAssets(this.certPage)
          $state.loaded()
        }, 5000)
      }
    }
  },
  beforeDestroy() {
    this.clickTimer = null
    clearTimeout(this.clickTimer)
  }
}
</script>

<style lang="less" scoped>
.threeTypes {
  width: 30%;
  height: 475px;
  margin-top: 20px;
  margin-left: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  color: #62666c;

  .threeTypes-title {
    height: 45px;
    position: relative;
    width: 100%;
    border-bottom: 1px solid #e9ebef;
    padding-left: 14px;
    display: flex;
    font-size: 16px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      width: 4px;
      height: 16px;
      background-color: #2677ff;
      border-radius: 0 2px 2px 0;
    }

    .title-list {
      height: 45px;
      line-height: 45px;
      margin-right: 20px;
      text-align: center;
      font-size: 16px;
      cursor: pointer;
    }

    .active {
      border-bottom: 2px solid #2677ff;
      color: #2677ff;
      font-weight: 600;
    }
  }

  .threeTypes-bottom {
    box-sizing: border-box;
    position: relative;
    padding: 20px 20px;
    height: calc(100% - 46px);

    .list {
      height: 100%;

      ul {
        display: flex;
        padding-left: 16px;

        .item {
          flex: 1;
          width: 0;
          margin-right: 8px;
          &:last-child {
            margin-right: 0;
          }
        }

        .time-li {
          width: 148px;
        }
      }

      .list-title {
        height: 40px;
        line-height: 40px;
        font-size: 12px;
        font-weight: 500;
        color: #62666c;
        border-radius: 4px;
        background-color: #f2f4f7;
      }

      .three-content-box {
        box-sizing: border-box;
        padding-bottom: 50px;
        height: calc(100% - 58px);
        overflow: auto;
        .content {
          border-bottom: 1px solid #e9ebef;
        }
        li {
          height: 44px;
          line-height: 44px;
        }
      }
    }
    .cdn .content .item {
      display: flex;
      align-items: center;

      .cdn-tag {
        display: inline-block;
        margin: 0 5px;
        padding: 2px 8px;
        height: 24px;
        background: rgba(38, 119, 255, 0.12);
        border-radius: 4px;
        text-align: center;
        line-height: 24px;
        color: #2677ff;
      }

      .ip {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &:hover {
          cursor: pointer;
        }
      }
    }

    .parse .content .item,
    .overdue .content .item {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .moreModule {
      box-sizing: border-box;
      z-index: 99;
      color: #2677ff;
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 82px;
      padding-top: 30px;
      margin: 0 -20px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 64%);
      text-align: center;
      &:hover {
        cursor: pointer;
      }
      .icon {
        height: 12px;
        width: 6px;
        margin-right: 0;
      }
    }
  }
}

.emptyClass {
  height: 80%;
  text-align: center;
  vertical-align: middle;

  svg {
    display: inline-block;
    font-size: 120px;
    margin-top: 25%;
  }

  p {
    line-height: 25px;
    color: #d1d5dd;

    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
