<template>
  <div class="container" v-loading="loading">
    <div class="headerTitle">
      {{ routePath == 'newAssets' ? '数字资产' : '数据泄露' }}
      <span v-if="notifyFilterId">
        / {{ notifyFilterMsg }}
        <span style="color: #2677ff; cursor: pointer" @click="clearNotifyFilter">恢复默认</span>
      </span>
    </div>
    <div class="home_header">
      <el-tabs
        v-if="routePath == 'newAssets'"
        v-model="activeName"
        @tab-click="handleClick($event)"
      >
        <el-tab-pane name="4" v-if="!notifyFilterId || (notifyFilterId && activeName == '4')">
          <span slot="label">公众号({{ tabArrNum[4] }})</span>
        </el-tab-pane>
        <el-tab-pane name="5" v-if="!notifyFilterId || (notifyFilterId && activeName == '5')">
          <span slot="label">小程序({{ tabArrNum[5] }})</span>
        </el-tab-pane>
        <el-tab-pane name="6" v-if="!notifyFilterId || (notifyFilterId && activeName == '6')">
          <span slot="label">APP({{ tabArrNum[6] }})</span>
        </el-tab-pane>
      </el-tabs>
      <div class="statusFilter">
        <el-tooltip class="item" effect="dark" content="可点击筛选" placement="top">
          <span @click="checkByStatus(0)">待处理：{{ status_num.default }}个，</span>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="可点击筛选" placement="top">
          <span @click="checkByStatus(1)">已确认：{{ status_num.sure }}个，</span>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="可点击筛选" placement="top">
          <span @click="checkByStatus(2)">已忽略：{{ status_num.ingore }}个，</span>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="可点击筛选" placement="top">
          <span @click="checkByStatus(3)">仿冒：{{ status_num.fake }}个</span>
        </el-tooltip>
      </div>
      <span class="nowNum" @click="goKeyword"
        >正在监控关键词：<span>{{ monitor_keyword_num }}</span></span
      >
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入关键字检索"
            id="number_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            <el-tooltip slot="prepend" class="item" effect="dark" placement="top" :open-delay="500">
              <span slot="content">
                <span v-if="activeName == 4">支持检索字段：公众号名称、账号主体</span>
                <span v-if="activeName == 5">支持检索字段：小程序名称、账号主体</span>
                <span v-if="activeName == 6">支持检索字段：应用名称、企业名称</span>
              </span>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-input>
          <span
            v-if="userIsOpen"
            @click="highCheckdialog = true"
            id="number_filter"
            style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll['checkedAll' + activeName]"
            @change="checkAllChange"
            id="number_all"
            >选择全部</el-checkbox
          >
          <div style="display: flex; align-items: center">
            <el-button
              :disabled="!userIsOpen"
              class="normalBtnRe"
              type="primary"
              @click="updateStatusSave('more', '')"
              id="number_more_del"
              >删除</el-button
            >
            <el-button
              :disabled="!userIsOpen"
              class="normalBtnRe"
              type="primary"
              @click="updateStatusSave('more', 3)"
              v-if="routePath == 'newAssets'"
              id="number_more_ignore"
              >仿冒</el-button
            >
            <el-button
              :disabled="!userIsOpen"
              class="normalBtnRe"
              type="primary"
              @click="updateStatusSave('more', 2)"
              id="number_more_ignore"
              >忽略</el-button
            >
            <el-button
              :disabled="!userIsOpen"
              class="normalBtn"
              type="primary"
              @click="updateStatusSave('more', 1)"
              id="number_more_confirm"
              >确认</el-button
            >
            <!-- 只有超管和安服账号才有推荐设置(安服切换到企业与企业本身保持一致)，超管、安服有批量上传 -->
            <!-- <el-button class="normalBtn" type="primary" v-if="user.role == 1 || user.role == 2" @click="tuijianIsShow" id="number_recommend">推荐设置</el-button> -->
            <!-- <el-button :disabled="!userIsOpen" class="normalBtn" type="primary" @click="uploadMore" id="number_upload">批量上传</el-button> -->

            <el-dropdown>
              <el-button :disabled="!userIsOpen && user.role == 2" class="normalBtn" type="primary">
                新增<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="uploadMore">批量上传</el-dropdown-item>
                <el-dropdown-item @click.native="editList()">单个新增</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              class="normalBtnRe"
              style="margin-left: 10px"
              type="primary"
              :loading="exportLoadingBtn"
              :disabled="exportLoadingBtn"
              @click="exportExcel"
              id="user_export"
              >导出</el-button
            >
          </div>
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="
          highTabShow.filter((item) => {
            return (item.tab && item.tab.includes(activeName / 1)) || !item.tab
          })
        "
        :highlist="highlist"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()" style="padding: 0px 20px">
        <el-table
          id="listTable"
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          :ref="'eltable' + activeName"
          height="100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="checkedAll['checkedAll' + activeName]"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="(item, index) in getTableData"
            :key="index"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'name'">
                <el-tooltip class="item" effect="dark" content="重点关注" placement="top">
                  <el-image
                    class="logosmall"
                    v-if="scope.row['is_danger'] == 1"
                    :src="require('../../assets/images/dangerImg.png')"
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </el-tooltip>
                {{ scope.row[item.name] }}</span
              >
              <span
                v-else-if="item.name == 'status'"
                :class="getStatusClass(scope.row[item.name])"
                >{{ getStatus(scope.row[item.name]) }}</span
              >
              <span v-else-if="item.name == 'is_online'">{{
                scope.row[item.name] == 1 ? '在线' : '离线'
              }}</span>
              <span v-else-if="item.name == 'index'">{{ scope.$index + 1 }}</span>
              <span v-else-if="item.name == 'url' || item.name == 'download_url'">
                <a
                  v-if="scope.row[item.name] && String(scope.row[item.name]).includes('http')"
                  style="color: #409eff"
                  :href="scope.row[item.name]"
                  target="_blank"
                  >{{ scope.row[item.name] }}</a
                >
                <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
              </span>
              <span v-else-if="item.name == 'img_url' || item.name == 'erweima'">
                <el-image
                  scroll-container="#listTable .el-table__body-wrapper"
                  v-if="scope.row[item.name]"
                  :preview-src-list="[
                    scope.row[item.name].includes('http')
                      ? scope.row[item.name]
                      : showSrcIp + scope.row[item.name]
                  ]"
                  :src="
                    scope.row[item.name].includes('http')
                      ? scope.row[item.name]
                      : showSrcIp + scope.row[item.name]
                  "
                  lazy
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <span v-else>-</span>
              </span>
              <span v-else-if="item.name == 'logo'">
                <el-image
                  scroll-container="#listTable .el-table__body-wrapper"
                  class="logoBg"
                  v-if="scope.row['erweima']"
                  :src="
                    scope.row['erweima'].includes('http')
                      ? scope.row['erweima']
                      : showSrcIp + scope.row['erweima']
                  "
                  lazy
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <span v-else>-</span>
              </span>
              <span v-else-if="item.name == 'xiaochengxu_logo'">
                <el-image
                  scroll-container="#listTable .el-table__body-wrapper"
                  v-if="scope.row['logo']"
                  :src="
                    scope.row['logo'].includes('http')
                      ? scope.row['logo']
                      : showSrcIp + scope.row['logo']
                  "
                  lazy
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <span v-else>-</span>
              </span>
              <!-- :preview-src-list="[scope.row['logo'].includes('http') ? scope.row['logo'] : showSrcIp + scope.row['logo']]" -->
              <span v-else-if="item.name == 'logoApp'">
                <el-image
                  scroll-container="#listTable .el-table__body-wrapper"
                  v-if="scope.row['logo']"
                  :src="
                    scope.row['logo'].includes('http')
                      ? scope.row['logo']
                      : showSrcIp + scope.row['logo']
                  "
                  lazy
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <span v-else>-</span>
              </span>
              <span v-else-if="item.name == 'app_type'">{{
                getAppType(scope.row[item.name])
              }}</span>
              <span v-else v-html="scope.row[item.name] ? scope.row[item.name] : '-'"></span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="200">
            <template slot-scope="scope">
              <!-- 安服有编辑权限 -->
              <el-button
                type="text"
                v-if="user.role == 1 || user.role == 2"
                size="small"
                @click="editList(scope.row)"
                >编辑</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="updateStatusSave('one', 1, scope.row.id)"
                v-if="
                  scope.row['status'] == 0 || scope.row['status'] == 2 || scope.row['status'] == 3
                "
                id="number_confirm"
                >确认</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="updateStatusSave('one', 2, scope.row.id)"
                v-if="
                  scope.row['status'] == 0 || scope.row['status'] == 1 || scope.row['status'] == 3
                "
                id="number_ignore"
                >忽略</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="updateStatusSave('one', 3, scope.row.id)"
                v-if="
                  (scope.row['status'] == 0 ||
                    scope.row['status'] == 1 ||
                    scope.row['status'] == 2) &&
                  routePath == 'newAssets'
                "
                id="number_ignore"
                >仿冒</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="updateStatusSave('one', '', scope.row.id)"
                id="number_del"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip> -->
      </div>
      <el-pagination
        @size-change="handleSizeChange($event, 'assets')"
        @current-change="handleCurrentChange($event, 'assets')"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd upload-dialog"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleImport"
      width="400px"
    >
      <template slot="title"> 批量上传 </template>
      <div class="dialog-body">
        <p class="downloadClass" @click="downloadAssetsExcel">
          <i class="el-icon-warning"></i>请点击下载
          <span>{{ getHeaderTitle() }}模板</span>
        </p>
        <el-upload
          class="upload-demo"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          :accept="routePath == 'newAssets' ? '.xlsx' : '.zip'"
          :before-upload="beforeIpUpload"
          :on-success="uploadSuccess"
          :on-remove="uploadRemove"
          :on-error="uploadError"
          :limit="uploadMaxCount"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">{{
            `支持上传${routePath == 'newAssets' ? 'xlsx' : 'zip'}文件，且大小不超过${routePath == 'newAssets' ? '20M' : '45M'}`
          }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleImport = false"
          id="number_upload_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="uploadSave" id="number_upload_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 编辑 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="500px"
      v-if="dialogFormVisibleInsert"
    >
      <template slot="title">
        {{ this.editForm.id ? '编辑' : '新增' }}
      </template>
      <div class="dialog-body">
        <el-form
          :model="editForm"
          :rules="editrules"
          style="padding: 0 !important"
          ref="editForm"
          label-width="110px"
          class="demo-ruleForm"
        >
          <el-form-item
            v-for="(item, index) in editItem.filter((item) => {
              return item.tab.indexOf(Number(activeName)) != -1
            })"
            :key="index"
            :label="item.label"
            :prop="item.name"
          >
            <el-select
              v-if="item.name == 'app_type'"
              v-model="editForm[item.name]"
              placeholder="请选择应用分类"
            >
              <el-option label="IOS" :value="1"></el-option>
              <el-option label="Android" :value="2"></el-option>
            </el-select>
            <!-- 小程序的新增：logo、二维码上传图片 -->
            <el-upload
              v-else-if="item.name == 'xiaochengxu_logo'"
              class="avatar-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              accept=".png,.ico,.bmp,.jpg,.jpeg"
              :on-success="logoUploadSuccess"
              :limit="uploadMaxCount"
              :on-exceed="handleExceed"
              :on-remove="logoUploadRemove"
              :file-list="imageLogoUrl"
              list-type="picture"
              :before-upload="beforeIconUpload"
            >
              <i
                class="el-icon-plus avatar-uploader-icon"
                style="padding: 10px; border: 1px dashed #ddd"
              ></i>
            </el-upload>
            <el-upload
              v-else-if="item.name == 'xiaochengxu_erweima'"
              class="avatar-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              accept=".png,.ico,.bmp,.jpg,.jpeg"
              :on-success="ewmUploadSuccess"
              :limit="uploadMaxCount"
              :on-exceed="handleExceed"
              :on-remove="ewmUploadRemove"
              list-type="picture"
              :file-list="imageEwmUrl"
              :before-upload="beforeIconUpload"
            >
              <i
                class="el-icon-plus avatar-uploader-icon"
                style="padding: 10px; border: 1px dashed #ddd"
              ></i>
            </el-upload>
            <el-radio-group v-else-if="item.name == 'is_online'" v-model="editForm[item.name]">
              <el-radio :label="1">在线</el-radio>
              <el-radio :label="2">离线</el-radio>
            </el-radio-group>
            <el-input
              v-else
              v-model="editForm[item.name]"
              :placeholder="`请输入${item.label}`"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="editSave('editForm')"
          :loading="editLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 推荐设置 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleTuijian"
      width="500px"
    >
      <template slot="title"> 推荐设置 </template>
      <div class="dialog-body">
        <p v-if="!tuijianLogIsShow" class="tuijian"
          ><u @click="tuijianLog">剩余推荐次数{{ tuijianNum }}</u></p
        >
        <p v-if="tuijianLogIsShow" class="tuijian"
          ><span class="el-icon-back" @click="tuijianLogIsShow = false">推荐记录</span></p
        >
        <el-form
          v-if="!tuijianLogIsShow"
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="ruleForm"
          label-width="95px"
          class="tuijianForm"
        >
          <el-form-item label="推荐频率">{{ getRate() }}</el-form-item>
          <el-form-item class="time" label="" prop="recommand_data_day">
            <template slot="label">
              <span>推荐时间</span>
              <el-tooltip
                class="item"
                effect="dark"
                content="将于下个推荐频率周期提供推荐数据"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
            <el-col :span="12">
              <el-select
                v-model="ruleForm.recommand_data_day"
                placeholder="请选择日期"
                class="type"
              >
                <el-option
                  v-for="item in monthArr"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="1" style="text-align: center">-</el-col>
            <el-col :span="11">
              <el-time-picker
                :clearable="false"
                v-model="ruleForm.recommand_data_time"
                size="small"
                placeholder="请选择开始时间"
                format="HH:mm"
                value-format="HH:mm"
                class="time"
              ></el-time-picker>
            </el-col>
          </el-form-item>
        </el-form>
        <el-table
          v-if="tuijianLogIsShow"
          border
          :data="tuijianLogData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltableTuijian"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            prop="created_at"
            align="center"
            :show-overflow-tooltip="true"
            min-width="120"
            label="操作时间"
          >
          </el-table-column>
          <el-table-column
            prop="recommand_time"
            align="center"
            :show-overflow-tooltip="true"
            min-width="120"
            label="推荐时间"
          >
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="tuijianLogIsShow"
          @size-change="handleSizeChange($event, 'tuijian')"
          @current-change="handleCurrentChange($event, 'tuijian')"
          :current-page="tuijianLogCurrentPage"
          :page-sizes="pageSizeArr"
          :page-size="tuijianLogCurrentPageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="tuijianLogTotal"
        >
        </el-pagination>
      </div>
      <div v-if="!tuijianLogIsShow" slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleTuijian = false"
          id="number_recommend_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          :loading="btnLoading"
          @click="tuijianSave"
          id="number_recommend_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="128px">
          <el-form-item v-if="activeName == '4'" label="公众号名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item v-if="activeName == '4'" label="微信号：" prop="account">
            <el-input v-model="formInline.account" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item v-if="activeName == '5'" label="小程序名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item v-if="activeName == '5'" label="账号原始ID：" prop="origin_id">
            <el-input v-model="formInline.origin_id" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item v-if="activeName == '5'" label="APPID：" prop="account">
            <el-input v-model="formInline.account" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item v-if="activeName != '6'" label="账号主体：" prop="owner">
            <el-select
              filterable
              v-model="formInline.owner"
              multiple
              collapse-tags
              placeholder="请选择"
              @change="selectChange($event, 'owner', owner_search, false, true)"
              clearable
            >
              <el-option
                v-for="item in owner_search"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="activeName == '6'" label="应用名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item v-if="activeName == '6'" label="应用分类：" prop="app_type">
            <el-select
              v-model="formInline.app_type"
              placeholder="请选择应用分类"
              @change="selectChange($event, 'app_type', app_type_search, true, false)"
            >
              <el-option label="IOS" :value="1"></el-option>
              <el-option label="Android" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="activeName == '6'" label="企业名称：" prop="owner">
            <el-select
              filterable
              v-model="formInline.owner"
              multiple
              collapse-tags
              placeholder="请选择"
              @change="selectChange($event, 'owner', owner_search, false, true)"
              clearable
            >
              <el-option
                v-for="item in owner_search"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：" prop="status">
            <el-select
              filterable
              v-model="formInline.status"
              placeholder="请选择"
              @change="selectChange($event, 'status', statusArr, true, false)"
              clearable
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="首次发现时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="最新发现时间：" prop="updated_at_range">
            <el-date-picker
              v-model="formInline.updated_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            v-if="[1, 2].includes(user.role)"
            label="数据源更新时间："
            prop="third_plat_update_time_range"
          >
            <el-date-picker
              v-model="formInline.third_plat_update_time_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="number_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="number_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import {
  sensitiveIndex,
  updateStatus,
  uploadSensitiveData,
  sensitiveRecommandSettings,
  storeSensitiveRecommandSetting,
  sensitiveRecommandRecordIndex,
  sensitiveIndexNewAssets,
  updateStatusNewAssets,
  uploadSensitiveDataNewAssets,
  removeSensitiveDataNewAssets,
  removeSensitiveData,
  editLeakageList,
  addLeakageList,
  exportLeakageList
} from '@/api/apiConfig/api.js'
import { delExpendList } from '@/api/apiConfig/recommend.js'

export default {
  components: { tableTooltip, hightFilter },
  data() {
    return {
      exportLoadingBtn: false,
      notifyFilterMsg: '',
      notifyFilterId: '',
      highTabShow: [
        {
          label: '公众号名称',
          name: 'name',
          type: 'input',
          tab: [4]
        },
        {
          label: '微信号',
          name: 'account',
          type: 'input',
          tab: [4]
        },
        {
          label: '小程序名称',
          name: 'name',
          type: 'input',
          tab: [5]
        },
        {
          label: '应用名称',
          name: 'name',
          type: 'input',
          tab: [6]
        },
        {
          label: '账号原始ID',
          name: 'origin_id',
          type: 'input',
          tab: [5]
        },
        {
          label: 'APPID',
          name: 'account',
          type: 'input',
          tab: [5]
        },
        {
          label: '账号主体',
          name: 'owner',
          type: 'select',
          tab: [4, 5]
        },
        {
          label: '应用分类',
          name: 'app_type',
          type: 'select',
          tab: [6]
        },
        {
          label: '企业名称',
          name: 'owner',
          type: 'select',
          tab: [6]
        },
        {
          label: '状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '首次发现时间',
          name: 'created_at_range',
          type: 'date'
        },
        {
          label: '最新发现时间',
          name: 'updated_at_range',
          type: 'date'
        }
      ],
      owner_search: [],
      app_type_search: [
        {
          id: 1,
          name: 'IOS'
        },
        {
          id: 2,
          name: 'Android'
        }
      ],
      imageLogoUrl: [],
      imageEwmUrl: [],
      fileList: [],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      monitor_keyword_num: 0,
      uploadPath: '', // 数据泄露的上传后路径
      uploadPathLogo: '', // 数据泄露的上传后路径
      uploadPathEwm: '', // 数据泄露的上传后路径
      routePath: '', // 页面路由名称
      // 平台名称 1/网盘 2/文库 3/代码仓库 4/公众号 5/小程序 6/APP
      tabArr: {
        newAssets: [
          {
            label: '公众号',
            name: '4',
            num: ''
          },
          {
            label: '小程序',
            name: '5',
            num: ''
          },
          {
            label: 'APP',
            name: '6',
            num: ''
          }
        ]
      },
      tabArrNum: {}, //监控数量
      formInline: {
        owner: [],
        account: '',
        origin_id: '',
        set_status: '',
        created_at_range: [],
        updated_at_range: [],
        third_plat_update_time_range: [],
        keyword: '',
        name: '',
        url: '',
        status: '',
        type: '',
        id: [],
        app_type: '',
        page: 1,
        per_page: 10
      },
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      checkedAll: {
        checkedAll1: false,
        checkedAll2: false,
        checkedAll3: false,
        checkedAll4: false,
        checkedAll5: false,
        checkedAll6: false
      },
      dialogFormVisibleInsert: false,
      dialogFormVisibleTuijian: false,
      dialogFormVisibleImport: false,
      btnLoading: false,
      editLoading: false,
      uploadAction: '',
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      logoImg: [],
      ewmImg: [],
      loading: false,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      currentPage: 1,
      tuijianLogCurrentPage: 0,
      tuijianLogCurrentPageSize: 10,
      total: 0,
      tuijianLogTotal: 0,
      tuijianLogData: [],
      tuijianLogIsShow: false,
      statusArr: [
        {
          name: '待处理',
          id: 0
        },
        {
          name: '已确认',
          id: 1
        },
        {
          name: '已忽略',
          id: 2
        },
        {
          name: '仿冒',
          id: 3
        }
      ],
      statusBrr: [
        {
          name: '待处理',
          id: 0
        },
        {
          name: '已确认',
          id: 1
        },
        {
          name: '已忽略',
          id: 2
        }
      ],
      tuijianNum: '',
      ruleForm: {
        type: '', // '推荐设置对应的平台种类 1：网盘，文库，代码仓库 2：app，公众号，小程序
        recommand_data_day: '',
        recommand_data_time: '',
        operate_company_id: this.currentCompany
      },
      monthArr: [
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25,
        26, 27, 28, 29, 30, 31
      ],
      rules: {
        recommand_data_day: [{ required: true, message: '请选择推荐时间', trigger: 'change' }]
      },
      highCheckdialog: false,
      editForm: {
        name: '',
        account: '',
        owner: '',
        description: '',
        origin_id: '',
        download_url: '',
        app_type: '',
        is_online: 1,
        type: '',
        logo: ''
      },
      editrules: {
        name: [{ required: true, message: '请填写', trigger: 'blur' }],
        account: [{ required: true, message: '请填写', trigger: 'blur' }],
        owner: [{ required: true, message: '请填写', trigger: 'blur' }],
        origin_id: [{ required: true, message: '请填写', trigger: 'blur' }],
        download_url: [{ required: true, message: '请填写', trigger: 'blur' }],
        app_type: [{ required: true, message: '请填写', trigger: 'change' }],
        is_online: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      editItem: [
        {
          label: '公众号名称',
          name: 'name',
          tab: [4]
        },
        {
          label: '小程序名称',
          name: 'name',
          tab: [5]
        },
        {
          label: '应用名称',
          name: 'name',
          tab: [6]
        },
        {
          label: '微信号',
          name: 'account',
          tab: [4]
        },
        {
          label: '账号主体',
          name: 'owner',
          tab: [4, 5]
        },
        {
          label: '账号原始ID',
          name: 'origin_id',
          tab: [5]
        },
        {
          label: 'AppID',
          name: 'account',
          tab: [5]
        },
        {
          label: '下载链接',
          name: 'download_url',
          tab: [6]
        },
        {
          label: '应用分类',
          name: 'app_type',
          tab: [6]
        },
        {
          label: '企业名称',
          name: 'owner',
          tab: [6]
        },
        {
          label: 'LOGO',
          name: 'xiaochengxu_logo',
          tab: [5]
        },
        {
          label: 'APP在线离线',
          name: 'is_online',
          tab: [6]
        },
        {
          label: 'LOGO地址',
          name: 'logo',
          tab: [6]
        },
        {
          label: '二维码',
          name: 'xiaochengxu_erweima',
          tab: [5]
        },
        {
          label: '简介',
          name: 'description',
          tab: [4, 5]
        }
      ],
      activeName: '',
      //       公众号：
      // 序号、logo、公众号名称、微信号、账号主体、二维码、简介、首次发现时间、最新发现时间、状态、操作（确认/忽略、删除）

      // 小程序：
      // 序号、logo、小程序名称、账号原始ID、AppID、账号主体、二维码、简介、首次发现时间、最新发现时间、状态、操作（确认/忽略、删除）

      // APP：
      // 序号、logo、应用名称、下载链接、应用分类（IOS、Android）、企业名称、首次发现时间、最新发现时间、状态、操作（确认/忽略、删除）
      tableData: [],
      tableHeader: {
        // 平台名称 1/网盘 2/文库 3/代码仓库 4/公众号 5/小程序 6/APP
        newAssets: [
          {
            label: '序号',
            name: 'index',
            tab: [4, 5, 6],
            minWidth: '55'
          },
          {
            label: 'LOGO',
            name: 'logo',
            tab: [4],
            minWidth: '90'
          },
          {
            label: 'LOGO',
            name: 'xiaochengxu_logo',
            tab: [5],
            minWidth: '90'
          },
          {
            label: 'LOGO',
            name: 'logoApp',
            tab: [6],
            minWidth: '90'
          },
          {
            label: '公众号名称',
            name: 'name',
            tab: [4],
            minWidth: '90'
          },
          {
            label: '小程序名称',
            name: 'name',
            tab: [5],
            minWidth: '90'
          },
          {
            label: '应用名称',
            name: 'name',
            tab: [6],
            minWidth: '90'
          },
          {
            label: '微信号',
            name: 'account',
            tab: [4]
          },
          {
            label: '账号主体',
            name: 'owner',
            tab: [4, 5]
          },
          {
            label: '二维码',
            name: 'erweima',
            tab: [4]
          },
          {
            label: '二维码',
            name: 'erweima',
            tab: [5]
          },
          {
            label: '简介',
            name: 'description',
            tab: [4, 5]
          },
          {
            label: '账号原始ID',
            name: 'origin_id',
            tab: [5]
          },
          {
            label: 'AppID',
            name: 'account',
            tab: [5]
          },
          {
            label: '下载链接',
            name: 'download_url',
            tab: [6],
            minWidth: '200'
          },
          {
            label: 'APP在线离线',
            name: 'is_online',
            tab: [6],
            minWidth: '100'
          },
          {
            label: '应用分类',
            name: 'app_type',
            tab: [6],
            minWidth: '90'
          },
          {
            label: '企业名称',
            name: 'owner',
            tab: [6]
          },
          {
            label: '首次发现时间',
            name: 'first_found_time',
            tab: [4, 5, 6],
            minWidth: '120'
          },
          {
            label: '最新发现时间',
            name: 'lastest_found_time',
            tab: [4, 5, 6],
            minWidth: '120'
          },
          {
            label: '数据源更新时间',
            name: 'third_plat_update_time',
            tab: [4, 5, 6],
            minWidth: '120'
          },
          {
            label: '状态',
            name: 'status',
            tab: [4, 5, 6]
          }
        ]
      },
      companyInfo: null,
      userIsOpen: true,
      status_num: {
        default: 0,
        sure: 0,
        ingore: 0,
        fake: 0
      }
    }
  },
  watch: {
    getterCompanyChange(val) {
      if (this.notifyFilterId) {
        this.clearNotifyFilter()
      }
    },
    getterCurrentCompany(msg) {
      setTimeout(() => {
        // 权限控制
        if (sessionStorage.getItem('companyInfo')) {
          let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
          if (this.routePath == 'newAssets') {
            // 数字资产
            this.userIsOpen = companyInfo.limit_new_asset == 0 ? false : true
          } else {
            // 数据泄露
            this.userIsOpen = companyInfo.limit_data_leak == 0 ? false : true
          }
        } else {
          this.userIsOpen = true
        }
      }, 1000)
      // 切换账号去除全选
      this.checkedAll['checkedAll' + this.activeName] = false
      this.tableData.forEach((row) => {
        this.$refs['eltable' + this.activeName].toggleRowSelection(row, false)
      })
      this.currentPage = 1
      if (this.user.role == 2) {
        this.getlogsIndexData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable' + this.activeName)
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange']),
    getTableData() {
      let arr = []
      this.tableHeader[this.routePath][0].fixed = 'left'
      arr = this.tableHeader[this.routePath].filter((item) => {
        return item.tab.indexOf(Number(this.activeName)) != -1
      })
      return arr
    }
  },
  async created() {
    this.routePath = this.$route.name // 路由的name
    this.activeName = this.routePath == 'newAssets' ? '4' : '1'
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      if (![1, 2].includes(this.user.role)) {
        let dataHeaderForTable = this.tableHeader.newAssets
        this.tableHeader.newAssets = dataHeaderForTable.filter(
          (item) => item.name != 'third_plat_update_time'
        )
      }
    }
    if (sessionStorage.getItem('companyInfo')) {
      this.companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      if (this.routePath == 'newAssets') {
        // 数字资产
        this.userIsOpen = this.companyInfo.limit_new_asset == 0 ? false : true
      } else {
        // 数据泄露
        this.userIsOpen = this.companyInfo.limit_data_leak == 0 ? false : true
      }
    } else {
      this.companyInfo = null
      this.userIsOpen = true
    }
  },
  async mounted() {
    this.notifyFilterId = this.$route.query.notifyFilterId
    this.notifyFilterMsg = this.$route.query.notifyFilterMsg
    this.formInline.website_message_id = this.notifyFilterId || ''
    this.activeName = this.$route.query.notifyFilterTab || '4'
    if (sessionStorage.getItem('activeTabName')) {
      this.activeName = sessionStorage.getItem('activeTabName')
    }
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getlogsIndexData()
    } else {
      this.getlogsIndexData()
    }
  },
  beforeDestroy() {
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    clearNotifyFilter() {
      this.$router.replace({ path: '/newAssets', query: {} })
    },
    async exportExcel() {
      if (this['checkedArr' + this.activeName].length == 0) {
        this.$message.error('请选择数据')
        return
      }
      let params = {
        ...this.formInline,
        id: this.checkedAll['checkedAll' + this.activeName]
          ? []
          : this['checkedArr' + this.activeName].map((item) => {
              return item.id
            }),
        is_all: this.checkedAll['checkedAll' + this.activeName] ? '1' : '',
        group_id: this.$route.query.group_id,
        operate_company_id: this.currentCompany,
        type: this.activeName // 导出类型
      }
      try {
        this.exportLoadingBtn = true
        let res = await exportLeakageList(params)
        if (res.code == 0) {
          this.$message.success('操作成功！请稍后…')
          this.download(this.showSrcIp + res.data.url)
          this.$refs['eltable' + this.activeName].clearSelection()
          this.exportLoadingBtn = false
          this.checkedAll['checkedAll' + this.activeName] = false
        }
      } catch (error) {
        this.exportLoadingBtn = false
      }
    },
    getAppType(val) {
      //转换app类型
      if (val == 1) {
        return 'IOS'
      } else if (val == 2) {
        return 'Android'
      } else {
        return '-'
      }
    },
    goKeyword() {
      //跳转关键词页面
      sessionStorage.setItem('menuId', '6-3')
      this.changeMenuId('6-3')
      this.$router.push('/keywordManage')
    },
    // 获取企业设置的推荐频率
    getRate() {
      if (sessionStorage.getItem('companyInfo')) {
        let rate = ''
        let rateIcon = ''
        let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
        if (this.routePath == 'newAssets') {
          rateIcon = companyInfo.new_asset_rate
        } else if (this.routePath == 'dataLeak') {
          rateIcon = companyInfo.data_leak_rate
        }
        switch (rateIcon) {
          case 1:
            rate = '每月一次'
            break
          case 2:
            rate = '每两个月一次'
            break
          case 3:
            rate = '每三个月一次'
            break
          default:
            rate = '关闭'
        }
        return rate
      } else {
        return '关闭'
      }
    },
    getHeaderTitle() {
      // 1/网盘 2/文库 3/代码仓库 4/公众号 5/小程序 6/APP
      let str = ''
      switch (this.activeName) {
        case '1':
          str = '网盘'
          break
        case '2':
          str = '文库'
          break
        case '3':
          str = '代码仓库'
          break
        case '4':
          str = '公众号'
          break
        case '5':
          str = '小程序'
          break
        case '6':
          str = 'APP'
          break
        default:
      }
      return str
    },
    getStatusClass(status) {
      let classN = ''
      if (status == 0) {
        classN = 'grayLine'
      } else if (status == 1) {
        classN = 'greenLine'
      } else if (status == 2) {
        classN = 'originLine'
      } else if (status == 3) {
        classN = 'redLine'
      }
      return classN
    },
    getStatus(status) {
      let str = ''
      switch (status) {
        case 0:
          str = '待处理'
          break
        case 1:
          str = '已确认'
          break
        case 2:
          str = '已忽略'
          break
        case 3:
          str = '仿冒'
          break
        default:
      }
      return str
    },
    checkAllChange() {
      if (this.checkedAll['checkedAll' + this.activeName]) {
        this.tableData.forEach((row) => {
          this.$refs['eltable' + this.activeName].toggleRowSelection(row, true)
        })
      } else {
        this.$refs['eltable' + this.activeName].clearSelection()
      }
    },
    checkByStatus(val) {
      this.formInline.status = val
      this.selectChange(val, 'status', this.statusArr, true, false)
      this.checkFuncList()
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getlogsIndexData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    async removeMore(icon, id) {
      let obj = {}
      if (icon == 'more') {
        if (this['checkedArr' + this.activeName].length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
        obj = {
          ...this.formInline,
          id: this.checkedAll['checkedAll' + this.activeName]
            ? []
            : this['checkedArr' + this.activeName].map((item) => {
                return item.id
              }),
          is_all: this.checkedAll['checkedAll' + this.activeName] ? '1' : '',
          group_id: this.$route.query.group_id,
          operate_company_id: this.currentCompany
        }
      } else {
        obj = {
          id: [id],
          group_id: this.$route.query.group_id,
          operate_company_id: this.currentCompany,
          is_all: ''
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_info_del_cancel',
        confirmButtonClass: 'cloud_info_del_sure',
        customClass: 'cloud_info_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await delExpendList(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.getRecommendRecordsList()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.cloud_info_del>.el-message-box__btns')
        del.children[0].id = 'cloud_info_del_cancel'
        del.children[1].id = 'cloud_info_del_sure'
      }, 50)
    },
    // 新增，编辑
    async editList(row) {
      this.uploadPath = ''
      this.uploadPathLogo = ''
      this.uploadPathEwm = ''
      this.imageLogoUrl = []
      this.imageEwmUrl = []
      this.uploadAction = `${this.uploadSrcIp}/assets/account/files`
      this.dialogFormVisibleInsert = true
      if (row) {
        for (let i in this.editForm) {
          this.editForm[i] = row[i]
        }
        this.editForm.id = row.id
        this.editForm.operate_company_id = this.currentCompany
        this.uploadPathLogo = row.logo
        this.uploadPathEwm = row.erweima
        this.imageLogoUrl = row.logo ? [{ name: '', url: this.showSrcIp + row.logo }] : []
        this.imageEwmUrl = row.erweima ? [{ name: '', url: this.showSrcIp + row.erweima }] : []
      } else {
        this.editForm = {
          name: '',
          account: '',
          owner: '',
          description: '',
          origin_id: '',
          download_url: '',
          app_type: '',
          is_online: 1,
          type: '',
          logo: ''
        }
      }
    },
    async editSave(row) {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          let actionFun = null
          if (this.activeName == 5) {
            this.editForm.xiaochengxu_logo = this.uploadPathLogo
            this.editForm.xiaochengxu_erweima = this.uploadPathEwm
          }
          if (!this.editForm.id) {
            // 新增
            actionFun = addLeakageList
            this.editForm.type = this.activeName
          } else {
            // 编辑
            actionFun = editLeakageList
          }
          this.editForm.operate_company_id = this.currentCompany
          this.editLoading = true
          let res = await actionFun(this.editForm).catch(() => {
            this.editLoading = false
          })
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.editLoading = false
            this.dialogFormVisibleInsert = false
            this.getlogsIndexData()
          }
        }
      })
    },
    async updateStatusSave(icon, status, id) {
      if (icon == 'more') {
        if (this['checkedArr' + this.activeName].length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.set_status = status ? status : ''
      this.formInline.type = this.activeName
      this.formInline.operate_company_id = this.currentCompany
      if (this.checkedAll['checkedAll' + this.activeName]) {
        this.formInline.id = icon == 'one' ? [id] : []
      } else {
        this.formInline.id =
          icon == 'one'
            ? [id]
            : this['checkedArr' + this.activeName].map((item) => {
                return item.id
              })
      }
      if (status) {
        // 存在代表忽略、确认
        let actionFun = null
        if (this.routePath == 'newAssets') {
          // 新型资产
          actionFun = updateStatusNewAssets
        } else if (this.routePath == 'dataLeak') {
          // 数据泄露
          actionFun = updateStatus
        }
        let res = await actionFun(this.formInline)
        if (res.code == 0) {
          this.$message.success('操作成功！')
          this.$nextTick(() => {
            this.$refs['eltable' + this.activeName].clearSelection()
          })
          this.getlogsIndexData()
        }
      } else {
        let _this = this
        this.$confirm('确定删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'cloud_info_del_cancel',
          confirmButtonClass: 'cloud_info_del_sure',
          customClass: 'cloud_info_del',
          type: 'warning'
        })
          .then(async () => {
            let actionFun = null
            if (this.routePath == 'newAssets') {
              // 新型资产
              actionFun = removeSensitiveDataNewAssets
            } else if (this.routePath == 'dataLeak') {
              // 数据泄露
              actionFun = removeSensitiveData
            }
            let res = await actionFun(_this.formInline)
            if (res.code == 0) {
              this.$message.success('操作成功！')
              this.$nextTick(() => {
                this.$refs['eltable' + this.activeName].clearSelection()
              })
              this.getlogsIndexData()
            }
          })
          .catch(() => {})
        setTimeout(() => {
          var del = document.querySelector('.cloud_info_del>.el-message-box__btns')
          del.children[0].id = 'cloud_info_del_cancel'
          del.children[1].id = 'cloud_info_del_sure'
        }, 50)
      }
    },
    async getlogsIndexData(tmp) {
      this.highCheckdialog = false
      this.loading = true
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll['checkedAll' + this.activeName] = false
        if (this.$refs['eltable' + this.activeName] != undefined) {
          this.$refs['eltable' + this.activeName].clearSelection()
        }
      }
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        type: this.activeName, // 1/网盘 2/文库 3/代码仓库 4/公众号 5/小程序 6/APP
        created_at_range: this.formInline.created_at_range ? this.formInline.created_at_range : [],
        updated_at_range: this.formInline.updated_at_range ? this.formInline.updated_at_range : [],
        third_plat_update_time_range: this.formInline.third_plat_update_time_range
          ? this.formInline.third_plat_update_time_range
          : [],
        url: '',
        name: this.formInline.name,
        account: this.formInline.account,
        origin_id: this.formInline.origin_id,
        owner: this.formInline.owner,
        status: this.formInline.status,
        app_type: this.formInline.app_type,
        keyword: this.formInline.keyword, // "关键词",
        operate_company_id: this.currentCompany,
        website_message_id: this.notifyFilterId || ''
      }
      let actionFun = null
      if (this.routePath == 'newAssets') {
        // 新型资产
        actionFun = sensitiveIndexNewAssets
      } else if (this.routePath == 'dataLeak') {
        // 数据泄露
        actionFun = sensitiveIndex
      }
      actionFun(obj)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.list.data
          this.status_num = res.data.status_num
          this.total = res.data.list.total
          this.owner_search = res.data.owner_search
          // 全选操作
          if (this.checkedAll['checkedAll' + this.activeName]) {
            this.tableData.forEach((row) => {
              this.$refs['eltable' + this.activeName].toggleRowSelection(row, true)
            })
          }
          // 当前正在监测数量
          this.monitor_keyword_num = res.data.monitor_keyword_num
          // 标签数量显示
          this.tabArrNum = res.data.num
          if ((!this.total || this.total == 0) && this.notifyFilterId) {
            this.$message.error('该批新增数字资产已被删除')
          }
        })
        .catch(() => {
          this.tableData = []
          this.total = 0
          this.monitor_keyword_num = 0
          this.tabArrNum = {}
          this.loading = false
        })
    },
    handleClick(val) {
      this.tableData = []
      this.resetForm()
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.$nextTick(() => {
        this.$refs['eltable' + this.activeName].clearSelection()
      })
      this.checkedAll['checkedAll' + this.activeName] = false
      sessionStorage.setItem('activeTabName', this.activeName)
      this.currentPage = 1
      this.getlogsIndexData()
    },
    handleSelectable(row, index) {
      return !this.checkedAll['checkedAll' + this.activeName]
    },
    handleSelectionChange(val) {
      this['checkedArr' + this.activeName] = val
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSizeChange(val, icon) {
      if (icon == 'assets') {
        this.pageSize = val
        this.getlogsIndexData(true)
      } else {
        this.tuijianLogCurrentPageSize = val
        this.getTuijianLog()
      }
    },
    handleCurrentChange(val, icon) {
      if (icon == 'assets') {
        this.currentPage = val
        this.getlogsIndexData(true)
      } else {
        this.tuijianLogCurrentPage = val
        this.getTuijianLog()
      }
    },
    uploadMore() {
      this.uploadPath = ''
      this.uploadPathLogo = ''
      this.uploadPathEwm = ''
      this.fileList = []
      this.dialogFormVisibleImport = true
      this.uploadAction = `${this.uploadSrcIp}/assets/account/files?encode=0`
    },
    downloadAssetsExcel() {
      if (this.routePath == 'newAssets') {
        window.location.href = `/downloadTemplate/${this.getHeaderTitle()}数据模板.xlsx`
      } else {
        window.location.href = `/downloadTemplate/${this.getHeaderTitle()}导入数据模板.zip`
      }
    },
    // 推荐设置详情
    async tuijianIsShow() {
      this.dialogFormVisibleTuijian = true
      this.tuijianLogIsShow = false
      this.tuijianLogCurrentPage = 1
      this.tuijianLogCurrentPageSize = 10
      let res = await sensitiveRecommandSettings({
        type: this.routePath == 'newAssets' ? 2 : 1,
        operate_company_id: this.currentCompany
      })
      this.tuijianNum = res.data.num
      this.ruleForm.recommand_data_day = res.data.recommand_data_day
      this.ruleForm.recommand_data_time = res.data.recommand_data_time
    },
    // 推荐设置保存
    async tuijianSave() {
      this.ruleForm.operate_company_id = this.currentCompany
      this.ruleForm.type = this.routePath == 'newAssets' ? 2 : 1 // '推荐设置对应的平台种类 1：网盘，文库，代码仓库 2：app，公众号，小程序
      if (!this.ruleForm.recommand_data_day || !this.ruleForm.recommand_data_time) {
        this.$message.error('请选择推荐时间')
        return
      }
      this.btnLoading = true
      let res = await storeSensitiveRecommandSetting(this.ruleForm).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.btnLoading = false
        this.$message.success('操作成功！将于下个推荐频率周期提供推荐数据')
        this.dialogFormVisibleTuijian = false
      }
    },
    // 点击剩余次数-查看推荐记录
    async tuijianLog() {
      this.tuijianLogIsShow = true
      this.getTuijianLog()
    },
    async getTuijianLog() {
      let obj = {
        page: this.tuijianLogCurrentPage,
        per_page: this.tuijianLogCurrentPageSize,
        type: this.routePath == 'newAssets' ? 2 : 1,
        operate_company_id: this.currentCompany
      }
      let res = await sensitiveRecommandRecordIndex(obj)
      this.tuijianLogData = res.data.items
      this.tuijianLogTotal = res.data.total
    },
    // 批量上传保存
    async uploadSave() {
      if (!this.uploadPath) {
        this.$message.error('请上传文件')
        return
      }
      this.btnLoading = true
      let actionFun = null
      if (this.routePath == 'newAssets') {
        // 新型资产
        actionFun = uploadSensitiveDataNewAssets
      } else if (this.routePath == 'dataLeak') {
        // 数据泄露
        actionFun = uploadSensitiveData
      }
      let res = await actionFun({
        path: this.uploadPath,
        type: this.activeName,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.btnLoading = false
        this.dialogFormVisibleImport = false
        this.getlogsIndexData()
      }
    },
    beforeIpUpload(file) {
      let isLt1M = ''
      if (this.routePath == 'newAssets') {
        isLt1M = file.size / 1024 / 1024 < 20
      } else {
        isLt1M = file.size / 1024 / 1024 < 45
      }
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过${this.routePath == 'newAssets' ? '20M' : '45MB'}!`)
      }
      return isLt1M
    },
    beforeIconUpload(file) {
      let isLt1M = ''
      if (this.routePath == 'newAssets') {
        isLt1M = file.size / 1024 / 1024 < 20
      } else {
        isLt1M = file.size / 1024 / 1024 < 45
      }
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过${this.routePath == 'newAssets' ? '20M' : '45MB'}!`)
      }
      return isLt1M
    },
    logoUploadSuccess(response, file, fileList) {
      if (file.response && file.response.data && file.response.data.url) {
        // this.imageLogoUrl = URL.createObjectURL(file.raw);
        this.uploadPathLogo = file.response.data.url
      }
      if (file.response.code == 0) {
        this.$message.success('上传成功！')
      } else {
        this.$message.error(file.response.message)
      }
    },
    ewmUploadSuccess(response, file, fileList) {
      if (file.response && file.response.data && file.response.data.url) {
        // this.imageEwmUrl = URL.createObjectURL(file.raw);
        this.uploadPathEwm = file.response.data.url
      }
      if (file.response.code == 0) {
        this.$message.success('上传成功！')
      } else {
        this.$message.error(file.response.message)
      }
    },
    // 批量上传
    uploadSuccess(response, file, fileList) {
      if (file.response && file.response.data && file.response.data.url) {
        this.uploadPath = file.response.data.url
      }
      if (file.response.code == 0) {
        this.$message.success('上传成功！')
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadError(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      if (myError.status == 401) {
        this.$router.replace('/login')
        sessionStorage.clear()
        localStorage.clear()
      } else {
        this.$message.error(myError.message)
      }
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.uploadPath = ''
      }
    },
    logoUploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.uploadPath = ''
        this.uploadPathLogo = ''
      }
    },
    ewmUploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.uploadPath = ''
        this.uploadPathEwm = ''
      }
    },
    resetForm() {
      this.formInline = {
        owner: [],
        account: '',
        origin_id: '',
        set_status: '',
        created_at_range: [],
        updated_at_range: [],
        third_plat_update_time_range: [],
        keyword: '',
        name: '',
        url: '',
        status: '',
        type: '',
        id: [],
        app_type: '',
        page: 1,
        per_page: 10
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .el-upload-list {
        max-height: 50px;
        min-height: 45px;
        overflow-y: auto;
      }
    }
    img {
      display: block;
      margin: 0 auto;
      // margin-bottom: 30px;
      width: 50px;
      height: 50px;
    }
    .tuijian {
      color: #2677ff;
      text-align: right;
      margin-bottom: 10px;
      u,
      span {
        cursor: pointer;
      }
    }
    /deep/.tuijianForm {
      .el-form-item .el-form-item__label {
        text-align: center !important;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .statusFilter {
      position: absolute;
      right: 20px;
      top: 13px;
      color: rgb(38, 119, 255);
      span {
        &:hover {
          cursor: pointer;
          color: #66b1ff;
        }
      }
    }
    .logoBg img {
      width: 142px;
      height: 142px;
      margin: -56px -56px;
    }
    .logosmall {
      width: 15px;
      height: 15px;
    }
    .nowNum {
      position: absolute;
      right: 0;
      top: -42px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      border-radius: 2px;
      padding: 4px 10px;
      border-left: 2px solid #2677ff;
      cursor: pointer;
      .num {
        font-weight: 500;
        color: #2677ff;
        margin-right: 0px;
      }
    }
    .el-tabs__nav.is-top {
      margin-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      padding: 0;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: none;
      & > span {
        margin-left: 0 !important;
        background: rgba(38, 119, 255, 0.08);
      }
    }
    .el-tabs__active-bar {
      width: 100%;
      padding: 0;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      padding: 0;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
      & > span {
        display: inline-block;
        height: 100%;
        padding: 0 16px;
      }
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 224px) !important;
    }
    .tableWrap {
      height: calc(100% - 169px);
    }
    .el-table {
      border: 0;
    }
  }
  .upload-dialog {
    /deep/.el-dialog__body {
      padding-top: 28px !important;
    }
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
