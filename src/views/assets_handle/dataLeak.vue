<template>
  <div class="container" v-loading="loading">
    <div class="headerTitle">数据泄露</div>
    <div class="home_header">
      <span class="nowNum" @click="goKeyword"
        >正在监控关键词：<span>{{ monitor_keyword_num }}</span></span
      >
      <div class="contentWrap">
        <div class="leftNav">
          <el-card class="box-card box-card_first">
            <p :class="activeType == 1 ? 'activeLeak' : ''" @click="leakTypeFun(1)"
              ><span><img src="../../assets/images/leak_wp.png" />网盘</span
              ><i>{{ tabArrNum[1] }}</i></p
            >
            <p :class="activeType == 2 ? 'activeLeak' : ''" @click="leakTypeFun(2)"
              ><span><img src="../../assets/images/leak_wk.png" />文库</span
              ><i>{{ tabArrNum[2] }}</i></p
            >
            <p :class="activeType == 3 ? 'activeLeak' : ''" @click="leakTypeFun(3)"
              ><span><img src="../../assets/images/leak_dm.png" />代码仓库</span
              ><i>{{ tabArrNum[3] }}</i></p
            >
          </el-card>
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span><i></i>平台</span>
            </div>
            <p
              class="cursor"
              :class="activePlat == v.label ? 'plateClass' : ''"
              v-for="(v, index) in polyArrNum.plat_name"
              :key="index"
              @click="sideConditionClick([v.label], 'plat_name', polyArrNum.plat_name, false, true)"
            >
              <span>{{ v.label ? v.label : '未知' }}</span
              ><i>{{ v.value }}</i>
            </p>
            <!-- <p
              class="cursor"
              :class="activePlat == k ? 'plateClass' : ''"
              v-for="(v, k, index) in polyArrNum.plat_name"
              :key="index"
              @click="sideConditionClick([k], 'plat_name', polyArrNum.plat_name, false, true)"
            >
              <span>{{ k ? k : '未知' }}</span
              ><i>{{ v }}</i>
            </p> -->
          </el-card>
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span><i></i>状态</span>
            </div>
            <template v-if="tableData.length > 0">
              <p
                class="cursor"
                v-for="item in Object.keys(polyArrNum.status)"
                :key="item"
                @click="sideConditionClick(statusBrrObj[item].id, 'status', statusBrr, true, false)"
                ><span>{{ statusBrrObj[item].name }}</span
                ><i>{{ polyArrNum.status[item] ? polyArrNum.status[item] : 0 }}</i></p
              >
              <!-- <p
                v-for="item in statusBrr"
                :key="item.id"
                :class="activeStatus === 0 ? 'plateClass' : ''"
                ><span>{{ item.name }}</span
                ><i>{{ polyArrNum['status'].default ? polyArrNum['status'].default : 0 }}</i></p
              > -->
              <!-- <p :class="activeStatus == 1 ? 'plateClass' : ''"
                ><span>已确认</span
                ><i>{{ polyArrNum['status'].sure ? polyArrNum['status'].sure : 0 }}</i></p
              >
              <p :class="activeStatus == 2 ? 'plateClass' : ''"
                ><span>已忽略</span
                ><i>{{ polyArrNum['status'].ingore ? polyArrNum['status'].ingore : 0 }}</i></p
              > -->
            </template>
          </el-card>
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span><i></i>关键词</span>
            </div>
            <p
              class="cursor"
              :class="activeKeyword == k ? 'plateClass' : ''"
              v-for="(v, k, index) in polyArrNum['keyword']"
              :key="index"
              @click="
                sideConditionClick(
                  k,
                  'match_keyword',
                  Object.keys(polyArrNum.keyword),
                  false,
                  false
                )
              "
            >
              <span>{{ k }}</span
              ><i>{{ v }}</i>
            </p>
          </el-card>
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span><i></i>语言</span>
            </div>
            <p
              class="cursor"
              :class="activeLaugue == k ? 'plateClass' : ''"
              v-for="(v, k, index) in polyArrNum.language"
              :key="index"
              @click="
                sideConditionClick([k], 'language', Object.keys(polyArrNum.language), false, true)
              "
            >
              <span>{{ k }}</span
              ><i>{{ v }}</i>
            </p>
          </el-card>
        </div>
        <div class="rightTable">
          <div class="filterTab">
            <div>
              <el-input
                v-model="formInline.keyword"
                @keyup.enter.native="checkFuncList"
                placeholder="请输入标题或关键词检索"
                id="number_keycheck"
              >
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
              </el-input>
              <span
                v-if="userIsOpen"
                @click="highCheckdialog = true"
                id="number_filter"
                style="width: 80px"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <div>
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAll"
                @change="checkAllChange"
                id="number_all"
                >选择全部</el-checkbox
              >
              <div style="display: flex; align-items: center">
                <el-button
                  :disabled="!userIsOpen"
                  class="normalBtnRe"
                  type="primary"
                  @click="updateStatusSave('more', 3)"
                  id="number_more_del"
                  >删除</el-button
                >
                <el-button
                  :disabled="!userIsOpen || exportLoadingBtn"
                  class="normalBtnRe"
                  type="primary"
                  @click="updateStatusSave('more', 4)"
                  id="number_more_confirm"
                  :loading="exportLoadingBtn"
                  >导出</el-button
                >
                <el-button
                  :disabled="!userIsOpen"
                  class="normalBtnRe"
                  type="primary"
                  @click="updateStatusSave('more', 2)"
                  id="number_more_ignore"
                  >忽略</el-button
                >
                <el-button
                  :disabled="!userIsOpen"
                  class="normalBtn"
                  type="primary"
                  @click="updateStatusSave('more', 1)"
                  id="number_more_confirm"
                  >确认</el-button
                >
                <!-- 只有超管和安服账号才有推荐设置(安服切换到企业与企业本身保持一致)，超管、安服有导入 -->
                <!-- <el-button class="normalBtn" type="primary" v-if="user.role == 1 || user.role == 2" @click="tuijianIsShow" id="number_recommend">推荐设置</el-button> -->
                <!-- <el-button :disabled="!userIsOpen" class="normalBtn" type="primary" @click="uploadMore" id="number_upload">导入</el-button> -->
                <el-dropdown>
                  <el-button
                    :disabled="!userIsOpen && user.role == 2"
                    class="normalBtn"
                    type="primary"
                  >
                    新增<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="uploadMore">批量上传</el-dropdown-item>
                    <el-dropdown-item @click.native="() => editList()">单个新增</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>
          <!-- 高级筛选条件 -->
          <hightFilter
            id="hightFilter"
            :highTabShow="highTabShow"
            :highlist="highlist"
            @highcheck="highCheck"
          ></hightFilter>
          <div :class="hightFilterIsShow()" style="padding: 0px 20px" ref="boxCard">
            <div v-if="tableData.length == 0" class="emptyClass">
              <div>
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-kong"></use>
                </svg>
                <p>暂无数据</p>
              </div>
            </div>
            <el-card v-else v-for="(item, index) in tableData" :key="item.id" class="box-card">
              <div class="infoClass">
                <div class="titleClass">
                  <div class="checkboxWrap">
                    <el-checkbox :disabled="checkedAll" v-model="item.checked">
                      <el-tooltip class="item" effect="dark" content="重点关注" placement="top">
                        <el-image
                          class="logosmall"
                          v-if="item['is_danger'] == 1"
                          :src="require('../../assets/images/dangerImg.png')"
                        >
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                          </div>
                        </el-image>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                        <span>{{ item.name }}</span>
                      </el-tooltip>
                    </el-checkbox>
                    <!-- <img class="starImg" src="../../assets/images/star.png" alt=""> -->

                    <a
                      v-if="item.url && String(item.url).includes('http')"
                      class="activeHref"
                      :href="item.url"
                      target="_blank"
                    >
                      <el-tooltip
                        class="item"
                        effect="dark"
                        popper-class="chainClass"
                        placement="top"
                      >
                        <p slot="content" style="line-height: 24px">{{ item.url }}</p>
                        <!-- <i class="iconfont icon-lianjie"></i> -->
                        <svg class="icon svg-icon" aria-hidden="true">
                          <use xlink:href="#icon-lianjie"></use>
                        </svg>
                      </el-tooltip>
                    </a>
                    <span v-else class="disHref">
                      <el-tooltip class="item" effect="dark" placement="top">
                        <p slot="content" style="line-height: 24px">{{ item.url }}</p>
                        <!-- <i class="iconfont icon-lianjie"></i> -->
                        <svg class="icon svg-icon" aria-hidden="true">
                          <use xlink:href="#icon-lianjie"></use>
                        </svg>
                      </el-tooltip>
                    </span>
                    <!-- 代码仓库显示代码片段 -->
                    <span
                      v-if="item.type == 3 && noCodeSnippet.indexOf(item.plat_name) == -1"
                      class="gitClass"
                    >
                      <el-tooltip
                        class="item"
                        effect="dark"
                        popper-class="chainClass"
                        placement="top"
                      >
                        <p
                          slot="content"
                          style="line-height: 24px"
                          v-html="
                            `代码片段：${changeColor(item.code_snippet ? encodeForHTML(item.code_snippet) : '暂无', item.keyword)}`
                          "
                        ></p>
                        <img src="../../assets/images/gitIcon.svg" alt="" />
                      </el-tooltip>
                    </span>
                  </div>
                  <span>
                    <!-- 代码仓库显示语言标签 -->
                    <span v-if="item.type == 3 && item.language" class="greenLine">{{
                      item.language
                    }}</span>
                    <span class="blueLine">{{ item.plat_name ? item.plat_name : '未知' }}</span>
                  </span>
                </div>
                <p>
                  <span class="keywordWrap">
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-guanjianci"></use>
                    </svg>
                    关键词：
                    <span
                      v-show="item.keyword"
                      class="keyClass"
                      v-for="ch in get_keyword(item.keyword.split(','), 'filter')"
                      :key="ch"
                      >{{ ch }}</span
                    >
                    <el-popover
                      placement="top"
                      width="315"
                      style="padding-right: 0px !important; padding-left: 0px !important"
                      popper-class="rulePopover"
                      trigger="click"
                    >
                      <div class="myruleItemBox">
                        <span
                          class="myruleItem"
                          v-show="item.keyword"
                          v-for="(v, i) in get_keyword(item.keyword.split(','))"
                          :key="i"
                          >{{ v }}</span
                        >
                      </div>
                      <div
                        slot="reference"
                        v-if="get_keyword(item.keyword.split(',')).length > 3"
                        class="ruleItemNum"
                        >共{{ get_keyword(item.keyword.split(',')).length }}条</div
                      >
                    </el-popover>
                  </span>
                  <span>
                    <el-button
                      style="margin-right: 10px"
                      plain
                      class="normalBtn"
                      type="primary"
                      @click="editList(item)"
                      id="number_more_confirm"
                      >编辑</el-button
                    >
                    <el-dropdown v-if="!item.status">
                      <el-button class="normalBtnRe" type="primary">
                        待处理<i class="el-icon-arrow-down el-icon--right"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item @click.native="updateStatusSave('one', 1, item.id)"
                          >确认</el-dropdown-item
                        >
                        <el-dropdown-item @click.native="updateStatusSave('one', 2, item.id)"
                          >忽略</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </el-dropdown>
                    <el-select
                      style="width: 95px"
                      v-if="item.status"
                      v-model="item.status"
                      placeholder=""
                    >
                      <el-option
                        @click.native="updateStatusSave('one', 1, item.id)"
                        label="确认"
                        :value="1"
                      ></el-option>
                      <el-option
                        @click.native="updateStatusSave('one', 2, item.id)"
                        label="忽略"
                        :value="2"
                      ></el-option>
                    </el-select>
                  </span>
                </p>
                <!-- <p><span><i class="el-icon-monitor"></i>平台：<span class="plateClass">{{item.plat_name}}</span></span></p> -->
                <p
                  ><span>
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-shijian"></use>
                    </svg>
                    首次发现时间：{{ item.first_found_time }}
                  </span>
                </p>
                <p
                  ><span>
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-shijian"></use>
                    </svg>
                    最新发现时间：{{ item.lastest_found_time }}
                  </span>
                </p>
              </div>
              <span class="imgWrap">
                <el-image
                  :preview-src-list="[
                    item.img_url && item.img_url.includes('http')
                      ? item.img_url
                      : showSrcIp + item.img_url
                  ]"
                  :src="
                    item.img_url && item.img_url.includes('http')
                      ? item.img_url
                      : showSrcIp + item.img_url
                  "
                  lazy
                >
                  <div
                    v-loading="currentIndex == index && picLoading"
                    slot="error"
                    class="image-slot"
                  >
                    <!-- 给当前操作的截图加loading,其余的不可操作 -->
                    <div>
                      <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-kong"></use>
                      </svg>
                      <p
                        >暂无图片，<el-button
                          :disabled="picLoading"
                          @click="reGetPic(item.id, index)"
                          type="text"
                          >请重试</el-button
                        ></p
                      >
                    </div>
                  </div>
                </el-image>
              </span>
            </el-card>
          </div>
          <el-pagination
            @size-change="handleSizeChange($event, 'assets')"
            @current-change="handleCurrentChange($event, 'assets')"
            :current-page="currentPage"
            :page-sizes="pageSizeArr"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- 编辑 -->
    <el-dialog
      class="elDialogAdd"
      @close="closeDialog"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="500px"
    >
      <template slot="title">
        {{ editForm.id ? '编辑' : '新增' }}
      </template>
      <div class="dialog-body">
        <el-form
          :model="editForm"
          :rules="editrules"
          style="padding: 0 !important"
          ref="editForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="类型" prop="type">
            <el-select :disabled="Boolean(editForm.id)" v-model="addType" placeholder="请选择类型">
              <el-option
                v-for="item in typeArr"
                :key="item.type"
                :label="item.name"
                :value="item.type"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-for="(item, index) in editItem.filter((item) => {
              return item.tab.indexOf(Number(addType)) != -1
            })"
            :key="index"
            :label="item.label"
            :prop="item.name"
          >
            <el-select
              v-if="item.name == 'app_type'"
              v-model="editForm[item.name]"
              placeholder="请选择应用分类"
            >
              <el-option label="IOS" :value="1"></el-option>
              <el-option label="Android" :value="2"></el-option>
            </el-select>
            <el-select
              v-if="item.name == 'language'"
              filterable
              v-model="editForm[item.name]"
              placeholder="请选择语言"
            >
              <el-option
                v-for="(item, index) in languageArr"
                :key="index"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
            <!-- 截图 -->
            <el-upload
              v-else-if="item.name == 'img_url'"
              class="upload-demo"
              drag
              :action="uploadSrcIp + '/assets/account/files'"
              :headers="uploadHeaders"
              :before-upload="($event) => beforeIpUpload($event, 3)"
              :on-success="logoUploadSuccess"
              :on-remove="uploadRemove"
              :on-error="uploadError"
              :on-exceed="handleExceed"
              list-type="picture"
              accept=".png,.ico,.bmp,.jpg,.jpeg"
              :limit="1"
              :file-list="fileListIcon"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">{{
                `支持上传.png,.ico,.bmp,.jpg,.jpeg文件，且大小不超过3M`
              }}</div>
            </el-upload>
            <el-switch
              v-else-if="item.name == 'is_danger'"
              v-model="editForm[item.name]"
              :active-value="1"
              :inactive-value="0"
              active-color="#2677FF"
            ></el-switch>
            <el-input
              v-else
              v-model="editForm[item.name]"
              :placeholder="`请输入${item.label}`"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="editSave('editForm')"
          :loading="editLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd upload-dialog"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleImport"
      width="400px"
    >
      <template slot="title"> 批量上传 </template>
      <div class="dialog-body">
        <el-select style="margin-bottom: 16px" v-model="uploadType" placeholder="请选择类型">
          <el-option
            v-for="item in typeArr"
            :key="item.type"
            :label="item.name"
            :value="item.type"
          ></el-option>
        </el-select>
        <p class="downloadClass" @click="downloadAssetsExcel">
          <i class="el-icon-warning"></i>请点击下载
          <span>{{ getHeaderTitle() }}模板</span>
        </p>
        <el-upload
          class="upload-demo"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          accept=".zip"
          :before-upload="($event) => beforeIpUpload($event, 45)"
          :on-success="uploadSuccess"
          :on-remove="uploadRemove"
          :on-error="uploadError"
          :limit="uploadMaxCount"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">支持上传zip文件，且大小不超过45M</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleImport = false"
          id="number_upload_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="uploadSave" id="number_upload_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 推荐设置 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleTuijian"
      width="500px"
    >
      <template slot="title"> 推荐设置 </template>
      <div class="dialog-body">
        <p v-if="!tuijianLogIsShow" class="tuijian"
          ><u @click="tuijianLog">剩余推荐次数{{ tuijianNum }}</u></p
        >
        <p v-if="tuijianLogIsShow" class="tuijian"
          ><span class="el-icon-back" @click="tuijianLogIsShow = false">推荐记录</span></p
        >
        <el-form
          v-if="!tuijianLogIsShow"
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="ruleForm"
          label-width="95px"
          class="tuijianForm"
        >
          <el-form-item label="推荐频率">{{ getRate() }}</el-form-item>
          <el-form-item class="time" label="" prop="recommand_data_day">
            <template slot="label">
              <span>推荐时间</span>
              <el-tooltip
                class="item"
                effect="dark"
                content="将于下个推荐频率周期提供推荐数据"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
            <el-col :span="12">
              <el-select
                v-model="ruleForm.recommand_data_day"
                placeholder="请选择日期"
                class="type"
              >
                <el-option
                  v-for="item in monthArr"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="1" style="text-align: center">-</el-col>
            <el-col :span="11">
              <el-time-picker
                :clearable="false"
                v-model="ruleForm.recommand_data_time"
                size="small"
                placeholder="请选择开始时间"
                format="HH:mm"
                value-format="HH:mm"
                class="time"
              ></el-time-picker>
            </el-col>
          </el-form-item>
        </el-form>
        <el-table
          v-if="tuijianLogIsShow"
          border
          :data="tuijianLogData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltableTuijian"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            prop="created_at"
            align="center"
            :show-overflow-tooltip="true"
            min-width="120"
            label="操作时间"
          >
          </el-table-column>
          <el-table-column
            prop="recommand_time"
            align="center"
            :show-overflow-tooltip="true"
            min-width="120"
            label="推荐时间"
          >
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="tuijianLogIsShow"
          @size-change="handleSizeChange($event, 'tuijian')"
          @current-change="handleCurrentChange($event, 'tuijian')"
          :current-page="tuijianLogCurrentPage"
          :page-sizes="pageSizeArr"
          :page-size="tuijianLogCurrentPageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="tuijianLogTotal"
        >
        </el-pagination>
      </div>
      <div v-if="!tuijianLogIsShow" slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleTuijian = false"
          id="number_recommend_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          :loading="btnLoading"
          @click="tuijianSave"
          id="number_recommend_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="110px">
          <el-form-item label="标题：" prop="title">
            <el-input v-model="formInline.title" placeholder="请输入标题"></el-input>
          </el-form-item>
          <el-form-item label="关键词：" prop="match_keyword">
            <el-select
              filterable
              v-model="formInline.match_keyword"
              placeholder="请选择"
              @change="
                selectChange($event, 'match_keyword', Object.keys(polyArrNum.keyword), false, false)
              "
              clearable
            >
              <el-option
                v-for="(v, k, index) in polyArrNum.keyword"
                :key="index"
                :label="k ? k : '未知'"
                :value="k ? k : '未知'"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="平台：" prop="plat_name">
            <el-select
              filterable
              v-model="formInline.plat_name"
              placeholder="请选择"
              @change="selectChange($event, 'plat_name', polyArrNum.plat_name, false, true)"
              clearable
              multiple
            >
              <el-option
                v-for="(v, index) in polyArrNum.plat_name"
                :key="index"
                :label="v.label ? v.label : '未知'"
                :value="v.label ? v.label : '未知'"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="activeType == 3" label="文件后缀：" prop="ext">
            <el-input v-model="formInline.ext" placeholder="请输入文件后缀"></el-input>
          </el-form-item>
          <el-form-item v-if="activeType == 3" label="语言：" prop="language">
            <el-select
              filterable
              v-model="formInline.language"
              placeholder="请选择"
              @change="
                selectChange($event, 'language', Object.keys(polyArrNum.language), false, true)
              "
              clearable
              multiple
            >
              <el-option
                v-for="(v, k, index) in polyArrNum.language"
                :key="index"
                :label="k"
                :value="k"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：" prop="status">
            <el-select
              filterable
              v-model="formInline.status"
              placeholder="请选择"
              @change="selectChange($event, 'status', statusBrr, true, false)"
              clearable
            >
              <el-option
                v-for="item in statusBrr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="代码片段：" prop="code_snippet">
            <el-input
              v-model="formInline.code_snippet"
              type="textarea"
              placeholder="请输入代码片段"
            ></el-input>
          </el-form-item>
          <el-form-item label="首次发现时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="最新发现时间：" prop="updated_at_range">
            <el-date-picker
              v-model="formInline.updated_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="number_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="number_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import {
  sensitiveIndex,
  updateStatus,
  exportLeakageList,
  uploadSensitiveData,
  sensitiveRecommandSettings,
  storeSensitiveRecommandSetting,
  sensitiveRecommandRecordIndex,
  regetScreenshot,
  uploadSensitiveDataNewAssets,
  removeSensitiveData,
  addSensitiveList,
  editSensitiveList
} from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip, hightFilter },
  data() {
    return {
      noCodeSnippet: ['Gitcode', 'Postman', 'Gitee'],
      exportLoadingBtn: false,
      fileListIcon: [],
      currentIndex: '', // 当前截图的数据index
      picLoading: false,
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      languageArr: [],
      addType: 1,
      editForm: {
        name: '',
        url: '',
        plat_name: '',
        keyword: '',
        language: '',
        is_danger: '',
        img_url: ''
      },
      ruleForm: {
        name: [{ required: true, message: '请输入', trigger: 'blur' }],
        url: [{ required: true, message: '请输入', trigger: 'blur' }],
        plat_name: [{ required: true, message: '请输入', trigger: 'blur' }],
        keyword: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      imageUrl: [],
      uploadPathImgurl: '',
      dialogFormVisibleInsert: false,
      highTabShow: [
        {
          label: '标题',
          name: 'title',
          type: 'input',
          tab: [1, 2, 3]
        },
        {
          label: '关键词',
          name: 'match_keyword',
          type: 'input',
          tab: [1, 2, 3]
        },
        {
          label: '链接',
          name: 'url',
          type: 'input',
          tab: [1, 2, 3]
        },
        {
          label: '关键词',
          name: 'name',
          type: 'input',
          tab: [1, 2, 3]
        },
        {
          label: '平台',
          name: 'plat_name',
          type: 'select',
          tab: [1, 2, 3]
        },
        {
          label: '代码片段',
          name: 'code_snippet',
          type: 'input',
          tab: [1, 2, 3]
        },
        {
          label: '文件后缀',
          name: 'ext',
          type: 'input',
          tab: [3]
        },
        {
          label: '语言',
          name: 'language',
          type: 'select',
          tab: [3]
        },
        {
          label: '状态',
          name: 'status',
          type: 'select',
          tab: [1, 2, 3]
        },
        {
          label: '首次发现时间',
          name: 'created_at_range',
          type: 'date',
          tab: [1, 2, 3]
        },
        {
          label: '最新发现时间',
          name: 'updated_at_range',
          type: 'date',
          tab: [1, 2, 3]
        }
      ],
      editLoading: false,
      // 1/2/3: 网盘、文库、代码仓库
      editItem: [
        {
          label: '文件名称',
          name: 'name',
          tab: [1, 2],
          minWidtth: '90'
        },
        {
          label: '代码名称',
          name: 'name',
          tab: [3],
          minWidtth: '90'
        },
        {
          label: '链接',
          name: 'url',
          tab: [1, 2, 3],
          minWidtth: '90'
        },
        {
          label: '平台',
          name: 'plat_name',
          tab: [1, 2, 3],
          minWidtth: '90'
        },
        {
          label: '关键词',
          name: 'keyword',
          tab: [1, 2, 3]
        },
        {
          label: '语言',
          name: 'language',
          tab: [3],
          minWidtth: '90'
        },
        {
          label: '是否重点关注',
          name: 'is_danger',
          tab: [1, 2, 3],
          minWidtth: '90'
        },
        {
          label: '截图',
          name: 'img_url',
          tab: [1, 2, 3],
          minWidtth: '90'
        }
      ],
      plateArr: [
        {
          id: 1,
          name: '百度网盘'
        },
        {
          id: 2,
          name: '百度文库'
        },
        {
          id: 3,
          name: 'gitLab'
        },
        {
          id: 4,
          name: '百度文库1'
        },
        {
          id: 5,
          name: 'gitLab1'
        }
      ],
      keywordArr: [
        {
          id: 1,
          name: '华顺信安'
        },
        {
          id: 2,
          name: '白帽汇'
        },
        {
          id: 3,
          name: 'foradar'
        }
      ],
      monitor_keyword_num: 0,
      uploadPath: '', // 数据泄露的上传后路径
      routePath: '', // 页面路由名称
      // 平台名称 1/网盘 2/文库 3/代码仓库 4/公众号 5/小程序 6/APP 7/全部
      typeArr: [
        {
          name: '网盘',
          type: 1
        },
        {
          name: '文库',
          type: 2
        },
        {
          name: '代码仓库',
          type: 3
        }
      ],
      tabArrNum: {}, //监控数量
      polyArrNum: {
        keyword: {},
        plat_name: {},
        status: {},
        language: []
      },
      formInline: {
        owner: '',
        set_status: '',
        created_at_range: [],
        updated_at_range: [],
        keyword: '',
        plat_name: [],
        title: '',
        name: '',
        match_keyword: '',
        url: '',
        status: '',
        type: '',
        language: '',
        ext: '',
        id: [],
        page: 1,
        per_page: 10
      },
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      checkedAll: false,
      dialogFormVisibleTuijian: false,
      dialogFormVisibleImport: false,
      btnLoading: false,
      uploadAction: '',
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      loading: false,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      currentPage: 1,
      tuijianLogCurrentPage: 0,
      tuijianLogCurrentPageSize: 10,
      total: 0,
      tuijianLogTotal: 0,
      tuijianLogData: [],
      tuijianLogIsShow: false,
      statusBrrObj: {
        default: {
          name: '待处理',
          id: 0,
          valLabel: 'default'
        },
        sure: {
          name: '已确认',
          id: 1,
          valLabel: 'sure'
        },
        ingore: {
          name: '已忽略',
          id: 2,
          valLabel: 'ingore'
        }
      },
      statusBrr: [
        {
          name: '待处理',
          id: 0,
          valLabel: 'default'
        },
        {
          name: '已确认',
          id: 1,
          valLabel: 'sure'
        },
        {
          name: '已忽略',
          id: 2,
          valLabel: 'ingore'
        }
      ],
      tuijianNum: '',
      ruleForm: {
        type: '', // '推荐设置对应的平台种类 1：网盘，文库，代码仓库 2：app，公众号，小程序
        recommand_data_day: '',
        recommand_data_time: '',
        operate_company_id: this.currentCompany
      },
      editrules: {
        name: [{ required: true, message: '请填写', trigger: 'blur' }],
        url: [{ required: true, message: '请填写', trigger: 'blur' }],
        plat_name: [{ required: true, message: '请填写', trigger: 'blur' }],
        keyword: [{ required: true, message: '请填写', trigger: 'blur' }],
        language: [{ required: true, message: '请选择', trigger: 'blur' }]
      },
      monthArr: [
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25,
        26, 27, 28, 29, 30, 31
      ],
      rules: {
        recommand_data_day: [{ required: true, message: '请选择推荐时间', trigger: 'change' }]
      },
      checkedArr: [],
      highCheckdialog: false,
      uploadType: 1,
      activeName: '1',
      activeType: '7', // 平台名称 1/网盘 2/文库 3/代码仓库 4/公众号 5/小程序 6/APP 7/全部
      tableData: [],
      activePlat: '',
      activeStatus: '',
      activeKeyword: '',
      activeLaugue: '',
      companyInfo: null,
      userIsOpen: true,
      languageObj: {
        '.c': 'C',
        '.h': 'C',
        '.cpp': 'C++',
        '.c++': 'C++',
        '.cp': 'C++',
        '.cmake': 'CMake',
        '.lisp': 'Lisp',
        '.java': 'Java',
        '.jsp': 'Java Server Pages',
        '.php': 'PHP',
        '.go': 'Go',
        '.lua': 'Lua',
        '.md': 'Markdown',
        '.py': 'Python',
        '.pyp': 'Python',
        '.pyt': 'Python',
        '.sh': 'Shell',
        '.bash': 'Shell',
        '.zsh': 'Shell',
        '.ksh': 'Shell',
        '.js': 'JavaScript',
        '.ts': 'TypeScript',
        '.tsx': 'TypeScript',
        '.vue': 'Vue',
        '.csv': 'CSV',
        '.html': 'HTML',
        '.txt': 'Text',
        '.xml': 'XML',
        '.css': 'CSS',
        '.json': 'JSON',
        '.yaml': 'YAML',
        '.yml': 'YAML',
        '.sql': 'SQL',
        '.plsql': 'PLSQL',
        '.dockerfile': 'Dockerfile',
        '.mk': 'Makefile',
        '.mkfile': 'Makefile',
        '.swift': 'Swift',
        '.txt': 'Txt',
        '.other': 'Other'
      }
    }
  },
  watch: {
    getterCurrentCompany(msg) {
      setTimeout(() => {
        // 权限控制
        if (sessionStorage.getItem('companyInfo')) {
          let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
          this.userIsOpen = companyInfo.limit_data_leak == 0 ? false : true
        } else {
          this.userIsOpen = true
        }
      }, 1000)
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((item) => {
        this.$set(item, 'checked', false)
      })
      this.currentPage = 1
      if (this.user.role == 2) {
        this.getlogsIndexData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  async created() {
    this.routePath = this.$route.name
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    if (sessionStorage.getItem('companyInfo')) {
      this.companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      this.userIsOpen = this.companyInfo.limit_data_leak == 0 ? false : true
    } else {
      this.companyInfo = null
      this.userIsOpen = true
    }
  },
  async mounted() {
    if (sessionStorage.getItem('activeTabName')) {
      this.activeName = sessionStorage.getItem('activeTabName')
    }
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getlogsIndexData()
    } else {
      this.getlogsIndexData()
    }
  },
  beforeDestroy() {
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    sideConditionClick(k, conditionName, conditionList, isCh, isMul) {
      this.formInline[conditionName] = k
      this.selectChange(k, conditionName, conditionList, isCh, isMul)
      this.checkFuncList()
    },
    encodeForHTML(str) {
      let str1 = ('' + str)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;')
      return str1
    },
    ...mapMutations(['changeMenuId']),
    encodeForHTML(str) {
      let str1 = ('' + str)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;')
      return str1
    },
    // 重新获取截图
    async reGetPic(id, index) {
      this.currentIndex = index
      this.picLoading = true
      let res = await regetScreenshot({
        id: id,
        type: 2, // 1登录入口，2数据泄露
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.picLoading = false
      })
      if (res.code == 0) {
        this.picLoading = false
        this.tableData[index].img_url = res.data
      }
    },
    get_keyword(val, filter) {
      let arr = []
      if (val) {
        if (filter) {
          arr = val
            .filter((item) => {
              return item
            })
            .slice(0, 3)
        } else {
          arr = val.filter((item) => {
            return item
          })
        }
      } else {
        arr = []
      }
      return arr
    },
    getAppType(val) {
      //转换app类型
      if (val == 1) {
        return 'IOS'
      } else if (val == 2) {
        return 'Android'
      } else {
        return '-'
      }
    },
    goKeyword() {
      //跳转关键词页面
      sessionStorage.setItem('menuId', '6-3')
      this.changeMenuId('6-3')
      this.$router.push('/keywordManage')
    },
    // 获取企业设置的推荐频率
    getRate() {
      if (sessionStorage.getItem('companyInfo')) {
        let rate = ''
        let rateIcon = ''
        let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
        if (this.routePath == 'newAssets') {
          rateIcon = companyInfo.new_asset_rate
        } else if (this.routePath == 'dataLeak') {
          rateIcon = companyInfo.data_leak_rate
        }
        switch (rateIcon) {
          case 1:
            rate = '每月一次'
            break
          case 2:
            rate = '每两个月一次'
            break
          case 3:
            rate = '每三个月一次'
            break
          default:
            rate = '关闭'
        }
        return rate
      } else {
        return '关闭'
      }
    },
    getHeaderTitle() {
      // 1/网盘 2/文库 3/代码仓库 4/公众号 5/小程序 6/APP
      let str = ''
      switch (this.uploadType) {
        case 1:
          str = '网盘'
          break
        case 2:
          str = '文库'
          break
        case 3:
          str = '代码仓库'
          break
        default:
      }
      return str
    },
    getStatus(status) {
      let str = ''
      switch (status) {
        case 0:
          str = '待处理'
          break
        case 1:
          str = '已确认'
          break
        case 2:
          str = '已忽略'
          break
        case 3:
          str = '仿冒'

          break
        default:
      }
      return str
    },
    // 全选
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((item) => {
          this.$set(item, 'checked', true)
        })
      } else {
        this.checkedArr = []
        this.tableData.forEach((item) => {
          this.$set(item, 'checked', false)
        })
      }
    },
    highCheck(val) {
      this.currentPage = 1
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.checkedArr = []
      this.getlogsIndexData(false)
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              } else if (item == ar.label) {
                this.formInline['ch_' + name].push(ar.label)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    async updateStatusSave(icon, status, id) {
      if (icon == 'more') {
        this.checkedArr = []
        this.tableData.forEach((item) => {
          if (item.checked) {
            this.checkedArr.push(item.id)
          }
        })
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.set_status = status ? status : ''
      this.formInline.type = this.activeType
      this.formInline.new_type = 1 // new_type: 1, // 数据泄露重构，要兼容旧版
      this.formInline.operate_company_id = this.currentCompany
      if (this.checkedAll) {
        this.formInline.id = icon == 'one' ? [id] : []
      } else {
        this.formInline.id = icon == 'one' ? [id] : Array.from(new Set(this.checkedArr))
      }
      if (status == 1 || status == 2) {
        // 代表忽略、确认
        let res = await updateStatus(this.formInline)
        if (res.code == 0) {
          this.$message.success('操作成功！')
          this.checkedArr = []
          this.getlogsIndexData(true) // 分页不回到第一页
        }
      } else if (status == 3) {
        // 删除
        let _this = this
        this.$confirm('确定删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'cloud_info_del_cancel',
          confirmButtonClass: 'cloud_info_del_sure',
          customClass: 'cloud_info_del',
          type: 'warning'
        })
          .then(async () => {
            let res = await removeSensitiveData(_this.formInline)
            if (res.code == 0) {
              this.$message.success('操作成功！')
              this.checkedArr = []
              this.getlogsIndexData()
            }
          })
          .catch(() => {})
        setTimeout(() => {
          var del = document.querySelector('.cloud_info_del>.el-message-box__btns')
          del.children[0].id = 'cloud_info_del_cancel'
          del.children[1].id = 'cloud_info_del_sure'
        }, 50)
      } else if (status == 4) {
        // 导出
        try {
          this.exportLoadingBtn = true
          let res = await exportLeakageList(this.formInline)
          if (res.code == 0) {
            this.download(this.showSrcIp + res.data.url)
            this.tableData.map((v) => {
              if (this.checkedArr.indexOf(v.id) != -1) {
                this.$set(v, 'checked', false)
              }
            })
            this.exportLoadingBtn = false
          }
        } catch (error) {
          this.exportLoadingBtn = false
        }
      }
    },
    // 点击网盘，文库
    leakTypeFun(type) {
      this.formInline = {
        owner: '',
        set_status: '',
        created_at_range: [],
        updated_at_range: [],
        keyword: '',
        plat_name: [],
        title: '',
        name: '',
        url: '',
        status: '',
        type: '',
        language: '',
        ext: '',
        id: [],
        page: 1,
        per_page: 10
      }
      this.checkedAll = false
      this.checkedArr = []
      this.highlist = null // 清空筛选条件tab
      if (type == this.activeType) {
        // 选中、取消圈中切换
        this.activeType = '7' // 代表查询全部
      } else {
        this.activeType = type
      }
      this.getlogsIndexData(true)
    },
    async getlogsIndexData(tmp) {
      this.highCheckdialog = false
      this.loading = true
      if (!tmp) {
        // 调用之前清空选择项
        this.currentPage = 1
        this.checkedAll = false
      }
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        type: this.activeType, // 1/网盘 2/文库 3/代码仓库 4/公众号 5/小程序 6/APP 7/全部
        new_type: 1, // 数据泄露重构，要兼容旧版
        created_at_range: this.formInline.created_at_range ? this.formInline.created_at_range : [],
        updated_at_range: this.formInline.updated_at_range ? this.formInline.updated_at_range : [],
        url: '',
        plat_name: this.formInline.plat_name,
        name: this.formInline.name, // "关键词",
        title: this.formInline.title, // "标题",
        owner: this.formInline.owner,
        ext: this.formInline.ext.replace(/\./g, ''), // 去除输入后缀的所有.
        language: this.formInline.language,
        status: this.formInline.status,
        keyword: this.formInline.keyword, // "关键字",
        operate_company_id: this.currentCompany,
        code_snippet: this.formInline.code_snippet,
        match_keyword: this.formInline.match_keyword
      }
      let actionFun = null
      actionFun = sensitiveIndex
      actionFun(obj)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.list.data
          this.total = res.data.list.total
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((item) => {
              this.$set(item, 'checked', true)
            })
          } else {
            // 切换分页回显已勾选
            this.tableData.forEach((item) => {
              if (this.checkedArr.indexOf(item.id) != -1) {
                this.$set(item, 'checked', true)
              } else {
                this.$set(item, 'checked', false)
              }
            })
          }
          //切换分页，自动滚到顶部
          // this.$refs.boxCard.scrollTop = 0;
          // 当前正在监测数量
          this.monitor_keyword_num = res.data.monitor_keyword_num
          // 标签数量显示
          this.tabArrNum = res.data.num
          // 平台、状态，关键词统计
          if (res.data.return.plat_name) {
            let newObj = []
            let plat_name = res.data.return.plat_name
            Object.keys(plat_name).forEach((item) => {
              newObj.push({ label: item, value: plat_name[item] })
            })
            let plat_namearr = newObj.sort((a, b) => {
              return b.value - a.value
            })
            res.data.return.plat_name = plat_namearr
            console.log(res.data.return.plat_name, 'plat_namearr')
          }
          this.polyArrNum = res.data.return
        })
        .catch(() => {
          this.tableData = []
          this.total = 0
          this.monitor_keyword_num = 0
          this.tabArrNum = {}
          // 平台、状态，关键词统计
          this.polyArrNum = {
            keyword: {},
            plat_name: {},
            status: {},
            language: []
          }
          this.loading = false
        })
    },
    handleClick(val) {
      this.checkedAll = false
      this.currentPage = 1
      this.getlogsIndexData()
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSizeChange(val, icon) {
      if (icon == 'assets') {
        this.pageSize = val
        this.getlogsIndexData(true)
      } else {
        this.tuijianLogCurrentPageSize = val
        this.getTuijianLog()
      }
    },
    handleCurrentChange(val, icon) {
      this.$refs.boxCard.scrollTop = 0
      if (icon == 'assets') {
        this.currentPage = val
        // 切换分页时拿到选中数据
        this.tableData.forEach((item) => {
          if (item.checked) {
            this.checkedArr.push(item.id)
          }
        })
        this.getlogsIndexData(true)
      } else {
        this.tuijianLogCurrentPage = val
        this.getTuijianLog()
      }
    },
    uploadMore() {
      this.fileList = []
      this.dialogFormVisibleImport = true
      this.uploadAction = `${this.uploadSrcIp}/assets/account/files?encode=0`
    },
    downloadAssetsExcel() {
      if (this.routePath == 'newAssets') {
        window.location.href = `/downloadTemplate/${this.getHeaderTitle()}数据模板.xlsx`
      } else {
        window.location.href = `/downloadTemplate/${this.getHeaderTitle()}导入数据模板.zip`
      }
    },
    // 推荐设置详情
    async tuijianIsShow() {
      this.dialogFormVisibleTuijian = true
      this.tuijianLogIsShow = false
      this.tuijianLogCurrentPage = 1
      this.tuijianLogCurrentPageSize = 10
      let res = await sensitiveRecommandSettings({
        type: this.routePath == 'newAssets' ? 2 : 1,
        operate_company_id: this.currentCompany
      })
      this.tuijianNum = res.data.num
      this.ruleForm.recommand_data_day = res.data.recommand_data_day
      this.ruleForm.recommand_data_time = res.data.recommand_data_time
    },
    // 推荐设置保存
    async tuijianSave() {
      this.ruleForm.operate_company_id = this.currentCompany
      this.ruleForm.type = this.routePath == 'newAssets' ? 2 : 1 // '推荐设置对应的平台种类 1：网盘，文库，代码仓库 2：app，公众号，小程序
      if (!this.ruleForm.recommand_data_day || !this.ruleForm.recommand_data_time) {
        this.$message.error('请选择推荐时间')
        return
      }
      this.btnLoading = true
      let res = await storeSensitiveRecommandSetting(this.ruleForm).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.btnLoading = false
        this.$message.success('操作成功！将于下个推荐频率周期提供推荐数据')
        this.dialogFormVisibleTuijian = false
      }
    },
    // 点击剩余次数-查看推荐记录
    async tuijianLog() {
      this.tuijianLogIsShow = true
      this.getTuijianLog()
    },
    async getTuijianLog() {
      let obj = {
        page: this.tuijianLogCurrentPage,
        per_page: this.tuijianLogCurrentPageSize,
        type: this.routePath == 'newAssets' ? 2 : 1,
        operate_company_id: this.currentCompany
      }
      let res = await sensitiveRecommandRecordIndex(obj)
      this.tuijianLogData = res.data.items
      this.tuijianLogTotal = res.data.total
    },
    // 新增，编辑
    async editList(row) {
      this.uploadMaxCount = 1
      this.getTableData = this.editItem.filter((item) => {
        return item.tab.indexOf(Number(this.activeName)) != -1
      }) // 展示对应类型字段
      let languageArr = [...new Set(Object.values(this.languageObj))] // 语言字段

      this.languageArr = languageArr.sort(function (s1, s2) {
        let x1 = s1.toUpperCase()
        let x2 = s2.toUpperCase()
        if (x1 < x2) {
          return -1
        }
        if (x1 > x2) {
          return 1
        }
        return 0
      })
      this.uploadPath = ''
      this.uploadPathLogo = ''
      this.uploadPathEwm = ''
      this.fileList = []
      this.uploadAction = `${this.uploadSrcIp}/assets/account/files`
      this.dialogFormVisibleInsert = true
      if (row) {
        for (let i in this.editForm) {
          this.editForm[i] = row[i]
        }
        let url = ''
        if (row.img_url) {
          url = row.img_url.includes('http') ? row.img_url : this.showSrcIp + row.img_url
          this.fileListIcon = [
            {
              name: '截图',
              url
            }
          ]
        } else {
          this.fileListIcon = []
        }
        this.addType = row.type
        this.editForm.id = row.id
        this.editForm.operate_company_id = this.currentCompany
      } else {
        this.fileListIcon = []
        this.editForm = {
          name: '',
          url: '',
          plat_name: '',
          keyword: '',
          language: ''
        }
        this.editForm.operate_company_id = this.currentCompany
      }
    },
    closeDialog() {
      this.$refs.editForm.resetFields()
      this.dialogFormVisibleInsert = false
    },
    async editSave(row) {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          let actionFun = null
          if (!this.editForm.id) {
            // 新增
            actionFun = addSensitiveList
          } else {
            // 编辑
            actionFun = editSensitiveList
          }
          let obj = { ...this.editForm }
          obj.type = String(this.addType)
          this.editLoading = true
          let res = await actionFun(obj).catch(() => {
            this.editLoading = false
          })
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.editLoading = false
            this.dialogFormVisibleInsert = false
            this.getlogsIndexData()
          }
        }
      })
    },
    // 批量上传保存
    async uploadSave() {
      if (!this.uploadPath) {
        this.$message.error('请上传文件')
        return
      }
      this.btnLoading = true
      let actionFun = null
      if (this.routePath == 'newAssets') {
        // 新型资产
        actionFun = uploadSensitiveDataNewAssets
      } else if (this.routePath == 'dataLeak') {
        // 数据泄露
        actionFun = uploadSensitiveData
      }
      let res = await actionFun({
        path: this.uploadPath,
        type: this.uploadType,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.btnLoading = false
        this.dialogFormVisibleImport = false
        this.getlogsIndexData()
      }
    },
    // beforeIpUpload(file){
    //   let isLt1M = ''
    //   if (this.routePath == 'newAssets') {
    //     isLt1M = file.size / 1024 / 1024 < 20;
    //   } else {
    //     isLt1M = file.size / 1024 / 1024 < 45;
    //   }
    //   if (!isLt1M) {
    //     this.$message.error(`上传文件不能超过${this.routePath == 'newAssets' ? '20M' : '45MB'}!`);
    //   }
    //   return isLt1M;
    // },
    beforeIpUpload(file, limit) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    },
    uploadSuccess(response, file, fileList) {
      if (file.response && file.response.data && file.response.data.url) {
        this.uploadPath = file.response.data.url
      }
      if (file.response.code == 0) {
        this.$message.success('上传成功！')
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadError(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      if (myError.status == 401) {
        this.$router.replace('/login')
        sessionStorage.clear()
        localStorage.clear()
      } else {
        this.$message.error(myError.message)
      }
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.uploadPath = ''
        // this.uploadPathImgurl = ''
        this.editForm.img_url = ''
      }
    },
    beforeIconUpload(file) {
      let isLt1M = ''
      if (this.routePath == 'newAssets') {
        isLt1M = file.size / 1024 / 1024 < 20
      } else {
        isLt1M = file.size / 1024 / 1024 < 45
      }
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过${this.routePath == 'newAssets' ? '20M' : '45MB'}!`)
      }
      return isLt1M
    },
    logoUploadSuccess(response, file, fileList) {
      this.fileListIcon = fileList
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        let obj = response.data
        this.editForm.img_url = obj.url
      } else {
        this.$message.error(file.response.message)
      }
    },
    resetForm() {
      this.formInline = {
        owner: '',
        set_status: '',
        created_at_range: [],
        updated_at_range: [],
        keyword: '',
        plat_name: [],
        title: '',
        name: '',
        url: '',
        status: '',
        type: '',
        language: '',
        ext: '',
        id: [],
        page: 1,
        per_page: 10
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  /deep/.el-dialog__body {
    padding-top: 10px !important;
  }
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .el-upload-list {
        max-height: 50px;
        min-height: 45px;
        overflow-y: auto;
      }
    }
    .el-select {
      width: 100%;
    }
    img {
      display: block;
      margin: 0 auto;
      margin-bottom: 30px;
    }
    .tuijian {
      color: #2677ff;
      text-align: right;
      margin-bottom: 10px;
      u,
      span {
        cursor: pointer;
      }
    }

    /deep/.tuijianForm {
      .el-form-item .el-form-item__label {
        text-align: center !important;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .logoBg img {
      width: 142px;
      height: 142px;
      margin: -56px -56px;
    }
    .logosmall {
      width: 15px;
      height: 15px;
    }
    .contentWrap {
      // height: calc(100% - 57px);
      height: 100%;
      position: relative;
      display: flex;
      justify-content: space-between;
      flex-wrap: nowrap;
      .leftNav {
        width: 20%;
        margin-right: 16px;
        // overflow-y: auto;
        // overflow-x: hidden;
        .box-card {
          height: 18%;
          margin-bottom: 3.6%;
          p {
            height: 22px;
            line-height: 22px;
            padding: 2px 3px;
            margin-bottom: 8px;
            border-radius: 4px;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            & > span {
              display: inline-block;
              width: 80%;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          p:last-child {
            margin-bottom: 0;
          }
          .activePlate {
            border: 1px solid #2766ff;
          }
          .activePlate {
            border: 1px solid #2766ff;
          }
          .activeStatus {
            border: 1px solid #2766ff;
          }
          .activeKey {
            border: 1px solid #2766ff;
          }
          .el-card__header {
            display: flex;
            align-items: center;
            height: 36px;
            line-height: 36px;
            padding: 0 16px;
            border: 0;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
          }
          .clearfix {
            i {
              display: inline-block;
              width: 6px;
              height: 6px;
              vertical-align: middle;
              border-radius: 1px;
              margin-right: 5px;
              transform: rotate(135deg);
              background: rgba(38, 119, 255, 1);
            }
          }
          .el-card__body {
            height: calc(100% - 55px);
            overflow: auto;
            padding: 10px 0;
          }
        }
        .box-card:last-child {
          margin-bottom: 0;
        }
        .box-card_first {
          width: 100%;
          height: 177px;
          .el-card__body {
            height: 177px;
            overflow: hidden;
            padding: 20px;
            box-sizing: border-box;
          }
          p {
            padding: 0 12px;
            height: 40px;
            line-height: 40px;
            cursor: pointer;
            background: linear-gradient(
              90deg,
              rgba(38, 119, 255, 0.12) 0%,
              rgba(38, 119, 255, 0.06) 100%
            );
            img {
              vertical-align: middle;
              margin-right: 5px;
            }
          }
        }
      }
      .activeLeak {
        border: 1px solid #2766ff;
      }
      // .plateClass{
      // font-size: 14px;
      // color: rgba(38, 119, 255, 1);
      // padding: 2px 20px !important;
      // border-radius: 0 !important;
      // background: rgba(38, 119, 255, 0.12);
      // }
      .rightTable {
        width: 80%;
        background:
          linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%),
          linear-gradient(90deg, rgba(240, 246, 255, 1) 0%, rgba(255, 255, 255, 1) 100%);
        .conwrap {
          background: transparent !important;
        }
        .box-card {
          height: 215px;
          margin-bottom: 12px;
          p {
            line-height: 22px;
            margin-bottom: 8px;
            border-radius: 2px;
            display: flex;
            justify-content: space-between;
            svg {
              font-size: 14px;
              font-weight: bold;
              color: #2677ff;
              margin-right: 8px;
            }
            .keywordWrap {
              .ruleItemBox {
                display: flex;
                flex-wrap: wrap !important;
              }
              .ruleItem,
              .ruleItemNum {
                line-height: 16px;
                padding: 2px 10px;
                margin: 5px 8px 5px 0px;
                background: #ffffff;
                border-radius: 14px;
                border: 1px solid #d1d5dd;
                white-space: pre-wrap;
              }
              .ruleItemNum {
                display: inline-block;
                background: #f0f3f8;
                border: 1px solid #dce5f3;
                cursor: pointer;
              }
            }
            .keyClass {
              padding: 2px 12px;
              margin-right: 5px;
              border-radius: 12px;
              background: rgba(255, 255, 255, 1);
              border: 1px solid rgba(209, 213, 221, 1);
            }
          }
          .titleClass {
            display: flex;
            justify-content: space-between;
            flex-wrap: nowrap;
            align-items: center;
            padding: 0 0 17px 0;
            margin-bottom: 16px;
            border-bottom: 1px solid rgba(233, 235, 239, 1);
            .el-checkbox__label {
              width: calc(100% - 14px);
              vertical-align: middle;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .el-checkbox__input {
              vertical-align: middle;
            }
            .checkboxWrap {
              width: 75%;
              .starImg {
                width: 20px;
              }
              .el-checkbox {
                max-width: 90%;
                margin-right: 1%;
              }
              .logosmall {
                width: 15px;
                height: 15px;
                margin-right: 5px;
                vertical-align: middle;
              }
              .el-checkbox__input.is-disabled + span.el-checkbox__label {
                color: #606266;
              }
              i {
                font-size: 16px;
                font-weight: bold;
              }
              .icon {
                font-size: 16px;
                font-weight: bold;
              }
              .disHref {
                i {
                  color: rgb(172, 180, 192);
                }
              }
              .activeHref {
                i {
                  color: #409eff;
                }
              }
              .gitClass {
                display: inline-block;
                vertical-align: middle;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                  vertical-align: top;
                }
              }
            }
          }
          .editClass {
            font-size: 14px;
            color: rgba(38, 119, 255, 1);
            cursor: pointer;
          }
          .plateClass {
            font-size: 12px;
            color: rgba(38, 119, 255, 1);
            padding: 4px 8px;
            border-radius: 2px;
            background: rgba(38, 119, 255, 0.12);
          }
          p:last-child {
            margin-bottom: 0;
          }
          .el-card__header {
            height: 44px;
            line-height: 44px;
            padding: 0 16px;
            border: 0;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
          }
          .clearfix {
            i {
              display: inline-block;
              width: 6px;
              height: 6px;
              vertical-align: middle;
              border-radius: 1px;
              margin-right: 5px;
              transform: rotate(135deg);
              background: rgba(38, 119, 255, 1);
            }
          }
          .el-card__body {
            height: calc(100% - 45px);
            display: flex;
            justify-content: space-between;
            flex-wrap: nowrap;
            .infoClass {
              width: 74%;
              .el-select {
                .el-input__icon {
                  line-height: 32px;
                }
              }
            }
            .imgWrap {
              display: inline-block;
              width: 25%;
              height: 170px;
              display: flex;
              align-items: center;
              overflow: hidden;
              margin-left: 16px;
            }
            .el-image {
              width: 100%;
              height: auto !important;
              .image-slot {
                height: 170px;
                color: #606266;
                background: #e9ebef;
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                align-items: center;
                text-align: center;
                svg {
                  display: inline-block;
                  font-size: 60px;
                }
                p {
                  line-height: 25px;
                  color: #d1d5dd;
                  span {
                    margin-left: 4px;
                    color: #2677ff;
                  }
                  .el-button {
                    padding: 0;
                  }
                }
              }
            }
          }
        }
      }
    }
    .nowNum {
      position: absolute;
      right: 0;
      top: -42px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      border-radius: 2px;
      padding: 4px 10px;
      border-left: 2px solid #2677ff;
      cursor: pointer;
      .num {
        font-weight: 500;
        color: #2677ff;
        margin-right: 0px;
      }
    }
    .el-tabs {
      margin-bottom: 12px;
      background: #fff;
    }
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      padding: 0 16px;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .el-pagination {
      background: #fff;
      margin-top: 2px;
      box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      margin-bottom: 10px;
      background: #fff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 184px) !important;
    }
    .tableWrap {
      height: calc(100% - 140px);
      overflow: auto;
      .emptyClass {
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        text-align: center;
        svg {
          display: inline-block;
          font-size: 120px;
        }
        p {
          line-height: 25px;
          color: #d1d5dd;
          span {
            margin-left: 4px;
            color: #2677ff;
            cursor: pointer;
          }
        }
      }
    }
    .el-table {
      border: 0;
    }
  }
  .upload-dialog {
    /deep/.el-dialog__body {
      padding-top: 28px !important;
    }
  }
}
/deep/.el-button--primary.is-plain {
  border: 1px solid rgba(38, 118.99999999999994, 255, 1);
  background-color: #fff;
  color: rgba(38, 119, 255, 1);
  &:hover {
    background-color: #fff;
  }
}
/deep/.el-button--primary.is-plain:focus,
.el-button--primary.is-plain:hover {
  &:hover {
    background-color: rgba(38, 118.99999999999994, 255, 1);
    color: #fff;
  }
}
.avatar {
  width: 200px;
}

.cursor {
  cursor: pointer;
}
</style>
