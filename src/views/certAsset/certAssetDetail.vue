<template>
  <el-dialog
    class="elDialogAdd"
    :close-on-click-modal="false"
    :visible="certInfoDialog"
    @close="certInfoDialog = false"
    width="680px"
  >
    <template slot="title">
      <span>证书详情</span>
    </template>
    <div class="dialog-body" v-loading="dialogLoading">
      <p class="certTitle">关联资产信息</p>
      <el-table
        class="certInfoTable"
        border
        :data="certInfoData.ip_domain"
        row-key="id"
        :span-method="objectSpanMethod"
        :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
        ref="eltable"
        max-height="200px"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in infoHeader"
          :key="item.id"
          :prop="item.name"
          align="left"
          min-width="120"
          :label="item.label"
        >
          <template slot-scope="scope">
            <span v-if="item.name == 'is_valid'"
              ><i :class="scope.row.is_valid == 1 ? 'greenStatus' : 'redStatus'"></i
              >{{ getValid(scope.row[item.name]) }}</span
            >
            <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <p class="certTitle">证书基本信息</p>
      <el-form class="certInfoList" ref="form" label-width="100px">
        <el-form-item label="版本">
          <p>{{ certInfoData.version }}</p>
        </el-form-item>
        <el-form-item label="有效期">
          <p :class="assetsDeal(certInfoData.not_after) == '已过期' ? 'validClassNot' : ''">
            {{ assetsDeal(certInfoData.not_after) }}
          </p>
          <p>颁发日期：{{ certInfoData.not_before }}</p>
          <p>截止日期：{{ certInfoData.not_after }}</p>
        </el-form-item>
        <el-form-item label="使用者信息">
          <p>使用者（CN）：{{ certInfoData.subject_cn }}</p>
          <p>组织（O）：{{ certInfoData.subject_org }}</p>
          <p>组织单位（OU）：{{ certInfoData.subject_ou }}</p>
        </el-form-item>
        <el-form-item label="颁发者信息">
          <p>颁发者（CN）：{{ certInfoData.issuer_cn }}</p>
          <p>组织（O）：{{ certInfoData.issuer_org }}</p>
          <p>组织单位（OU）：{{ certInfoData.issuer_ou }}</p>
        </el-form-item>
        <!-- <el-form-item label="指纹信息">
              <p>SHA-256指纹：{{certInfoData.sha256}}</p>
              <p>SHA-1指纹：{{certInfoData.sha1}}</p>
            </el-form-item> -->
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="certInfoDialog = false" id="report_add_cancel"
        >关闭</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
import { mapState } from 'vuex'
import { certAssetsInfo } from '@/api/apiConfig/api.js'
export default {
  data() {
    return {
      certInfoDialog: false,
      dialogLoading: false,
      certInfoData: {
        ip_domain: [],
        is_valid: 0,
        version: '',
        not_before: '',
        not_after: '',
        subject_cn: '',
        subject_org: '',
        subject_ou: '',
        issuer_cn: '',
        issuer_org: '',
        issuer_ou: '',
        sha256: '',
        sha1: ''
      },
      infoHeader: [
        {
          label: '关联IP',
          name: 'ip'
        },
        {
          label: '关联端口',
          name: 'port'
        },
        {
          label: '关联域名',
          name: 'domain'
        },
        {
          label: '可信度',
          name: 'is_valid'
        }
      ]
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  methods: {
    async checkInfo(id) {
      this.dialogLoading = true
      this.certInfoDialog = true
      let res = await certAssetsInfo({
        id,
        data: {
          operate_company_id: this.currentCompany
        }
      }).catch(() => {
        this.dialogLoading = false
      })
      if (res.code == 0) {
        this.dialogLoading = false
        this.certInfoData = res.data
        this.certInfoData.ip_domain = res.data.ip_domain.sort((a, b) => {
          const ip1 = a.ip
            .split('.')
            .map((e) => e.padStart(3, '0'))
            .join('')
          const ip2 = b.ip
            .split('.')
            .map((e) => e.padStart(3, '0'))
            .join('')
          return ip1 - ip2
        })
        this.setRowSpan(this.certInfoData.ip_domain)
      }
    },
    setRowSpan(data) {
      const tableData = data
      tableData.forEach((item) => {
        item.rowspan = 1
        item.Content_span = 1
      })
      // 双层循环
      for (let i = 0; i < tableData.length; i++) {
        for (let j = i + 1; j < tableData.length; j++) {
          // 此处可根据相同字段进行合并，此处是根据的itemName
          if (tableData[i].ip === tableData[j].ip) {
            tableData[i].rowspan++
            tableData[j].rowspan--
          }
        }
        // 这里跳过已经重复的数据
        i = i + tableData[i].rowspan - 1
      }
    },
    getValid(type) {
      let str = ''
      switch (type) {
        case 0:
          str = '不可信'
          break
        case 1:
          str = '可信'
          break
        default:
          str = '-'
      }
      return str
    },
    // 有效期处理
    assetsDeal(time) {
      let timer = this.secondsFormatValid(
        (new Date(time.replace(/-/g, '/')).getTime() - new Date().getTime()) / 1000
      )
      if (new Date(time.replace(/-/g, '/')).getTime() - new Date().getTime() < 0) {
        return '已过期'
      } else {
        return '剩余(' + timer + ')'
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // this.setRowSpan(this.certInfoData.ip_domain);
      // 当前列号columnIndex
      // 当前行号rowIndex
      // 当前行row
      // 当前列column

      //我们需要把内容和名称如果相等就进行合并
      if (columnIndex === 0) {
        // ip在第1列
        return {
          rowspan: row.rowspan, // 列跨越x格
          colspan: 1 // 行跨越1格
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.dialog-body {
  .certTitle {
    font-weight: bold;
    padding: 3px 0 3px 8px;
    margin-bottom: 10px;
    border-left: 4px solid #2677ff;
  }
  /deep/.certInfoList {
    .el-form-item .el-form-item__label {
      font-weight: bold !important;
    }
  }
  /deep/.certInfoTable {
    margin-bottom: 10px;
    th.el-table__cell::after {
      height: 0;
    }
    td {
      border-right: 1px solid #ebeef5;
    }
    tbody tr:hover > td {
      background-color: unset !important;
    }
  }
}
</style>
