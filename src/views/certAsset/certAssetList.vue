<template>
  <div class="container">
    <div class="headerTitle" v-if="!($route.path == '/index')">
      <span class="goback" v-if="$route.query.overdue" @click="goBackFromBrief()"
        ><i class="el-icon-arrow-left"></i>返回上一层 / &nbsp;&nbsp;</span
      >
      证书资产
      <span v-if="notifyFilterId">
        / {{ notifyFilterMsg }}
        <span style="color: #2677ff; cursor: pointer" @click="clearNotifyFilter">恢复默认</span>
        <span style="color: #2677ff; cursor: pointer" @click="$router.go(-1)">返回站内信</span>
      </span>
    </div>
    <div class="home_header">
      <div class="filterTab" v-if="!($route.path == '/index')">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
            id="keyword_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            <el-tooltip
              slot="prepend"
              class="item"
              effect="dark"
              content="支持检索字段：使用者、使用组织、颁发者、关联IP、关联域名"
              placement="top"
              :open-delay="100"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-input>
          <span @click="highCheckdialog = true" id="keyword_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="keyword_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="remove('more')"
            id="keyword_more_del"
            >删除</el-button
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            :loading="exportLoadingBtn"
            :disabled="exportLoadingBtn"
            @click="exportExcel"
            id="keyword_more_open"
            >导出</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        v-if="!($route.path == '/index')"
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="certAssets"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :fit="true"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            v-if="!($route.path == '/index')"
            type="selection"
            align="center"
            :reserve-selection="true"
            default-expand-all
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :label="item.label"
          >
            <template slot-scope="scope">
              <div v-if="item.name == 'ip'" class="ruleItemBox">
                <span
                  class="ruleItem"
                  v-for="ch in get_ipdomain(Object.keys(scope.row['ip_domain_list']), 'filter')"
                  :key="ch"
                >
                  <el-tooltip effect="light" class="item" placement="top" :content="ch">
                    <span>{{ ch }}</span>
                  </el-tooltip>
                </span>
                <el-popover
                  placement="top"
                  width="360"
                  style="padding-right: 0px !important; padding-left: 0px !important"
                  popper-class="rulePopover"
                  trigger="click"
                >
                  <div class="myruleItemBox">
                    <span
                      class="myruleItem"
                      v-for="(v, i) in get_ipdomain(Object.keys(scope.row['ip_domain_list']))"
                      :key="i"
                      >{{ v }}</span
                    >
                  </div>
                  <div
                    slot="reference"
                    v-if="get_ipdomain(Object.keys(scope.row['ip_domain_list'])).length > 2"
                    class="ruleItemNum"
                  >
                    共{{ get_ipdomain(Object.keys(scope.row['ip_domain_list'])).length }}条
                  </div>
                </el-popover>
              </div>
              <div v-else-if="item.name == 'domain'" class="ruleItemBox">
                <span
                  v-if="
                    Object.values(scope.row['ip_domain_list']) &&
                    Object.values(scope.row['ip_domain_list']).length !== 0 &&
                    get_ipdomain(...Object.values(scope.row['ip_domain_list']), 'filter').length ==
                      0
                  "
                  >-</span
                >
                <template
                  v-else-if="
                    Object.values(scope.row['ip_domain_list']) &&
                    Object.values(scope.row['ip_domain_list']).length !== 0
                  "
                >
                  <span
                    class="ruleItem"
                    v-for="ch in get_ipdomain(
                      ...Object.values(scope.row['ip_domain_list']),
                      'filter'
                    )"
                    :key="ch"
                  >
                    <el-tooltip effect="light" class="item" placement="top" :content="ch">
                      <span>{{ ch }}</span>
                    </el-tooltip>
                  </span>
                </template>
                <template v-else> - </template>
                <el-popover
                  placement="top"
                  width="350"
                  style="padding-right: 0px !important; padding-left: 0px !important"
                  popper-class="rulePopover"
                  trigger="click"
                >
                  <div class="myruleItemBox">
                    <span
                      class="myruleItem"
                      v-for="(v, i) in get_ipdomain(...Object.values(scope.row['ip_domain_list']))"
                      :key="i"
                      >{{ v }}</span
                    >
                  </div>
                  <div
                    slot="reference"
                    v-if="
                      Object.values(scope.row['ip_domain_list']) &&
                      Object.values(scope.row['ip_domain_list']).length !== 0 &&
                      get_ipdomain(...Object.values(scope.row['ip_domain_list'])).length > 2
                    "
                    class="ruleItemNum"
                  >
                    共{{
                      Object.values(scope.row['ip_domain_list'])
                        ? get_ipdomain(...Object.values(scope.row['ip_domain_list'])).length
                        : 0
                    }}条
                  </div>
                </el-popover>
              </div>
              <span v-else-if="item.name == 'not_after'">
                <span>{{ scope.row['not_after'] }}</span
                ><br />
                <span
                  :class="assetsDeal(scope.row['not_after']) == '已过期' ? 'validClassNot' : ''"
                  >{{ assetsDeal(scope.row['not_after']) }}</span
                >
              </span>
              <span v-else-if="item.name == 'is_self_sign'">
                <span class="tagBlock greenTagBlock" v-if="scope.row['is_self_sign'] == 0"
                  >权威证书</span
                >
                <span class="tagBlock orangeTagBlock" v-if="scope.row['is_self_sign'] == 1"
                  >自签证书</span
                >
              </span>
              <span v-else>
                <el-tooltip
                  v-if="scope.row[item.name]"
                  class="item"
                  effect="dark"
                  :content="scope.row[item.name]"
                  placement="top"
                >
                  <span>{{ scope.row[item.name] }}</span>
                </el-tooltip>
                <span v-else>-</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="left"
            width="80"
            v-if="$route.path != '/index'"
          >
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="checkInfo(scope.row.id)" id="user_edit"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-if="$route.path != '/index'"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="100px">
          <el-form-item label="企业名称：" prop="not_after">
            <el-select
              v-model="formInline.company_name"
              clearable
              multiple
              collapse-tags
              placeholder="请选择"
              @change="selectChange($event, 'company_name', selectArr.company_name, false, true)"
            >
              <el-option
                v-for="item in selectArr.company_name"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="颁发者：" prop="issuer_cn">
            <el-input v-model="formInline.issuer_cn" placeholder="请输入颁发者"></el-input>
          </el-form-item>
          <el-form-item label="使用者：" prop="subject_cn">
            <el-input v-model="formInline.subject_cn" placeholder="请输入使用者"></el-input>
          </el-form-item>
          <el-form-item label="使用组织：" prop="subject_org">
            <el-input v-model="formInline.subject_org" placeholder="请输入使用组织"></el-input>
          </el-form-item>
          <el-form-item label="关联IP：" prop="ip">
            <el-input v-model="formInline.ip" placeholder="请输入关联IP"></el-input>
          </el-form-item>
          <el-form-item label="关联域名：" prop="domain">
            <el-input v-model="formInline.domain" placeholder="请输入关联域名"></el-input>
          </el-form-item>
          <el-form-item label="版本：" prop="version">
            <el-select
              v-model="formInline.version"
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'version', versionArr, false, false)"
            >
              <el-option
                v-for="item in versionArr"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="有效期：" prop="not_after">
            <el-select
              v-model="formInline.not_after"
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'not_after', validArr, true, false)"
            >
              <el-option
                v-for="item in validArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现时间：" prop="created_at">
            <el-date-picker
              v-model="formInline.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="更新时间：" prop="updated_at">
            <el-date-picker
              v-model="formInline.updated_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
    <CertAssetDetail ref="certAssetDetail" />
  </div>
</template>

<script>
import CertAssetDetail from './certAssetDetail'
import { mapGetters, mapState, mapMutations } from 'vuex'
import hightFilter from '../../components/assets/highTab.vue'
import { certAssetsList, exportCertAssets, delCertAssets } from '@/api/apiConfig/api.js'

export default {
  components: {
    hightFilter,
    CertAssetDetail
  },
  data() {
    return {
      exportLoadingBtn: false,
      selectArr: {},
      highTabShow: [
        {
          label: '颁发者',
          name: 'issuer_cn',
          type: 'input'
        },
        {
          label: '使用者',
          name: 'subject_cn',
          type: 'input'
        },
        {
          label: '企业名称',
          name: 'company_name',
          type: 'select'
        },
        {
          label: '使用组织',
          name: 'subject_org',
          type: 'input'
        },
        {
          label: '关联IP',
          name: 'ip',
          type: 'input'
        },
        {
          label: '关联域名',
          name: 'domain',
          type: 'input'
        },
        {
          label: '版本',
          name: 'version',
          type: 'select'
        },
        {
          label: '有效期',
          name: 'not_after',
          type: 'select'
        },
        {
          label: '发现时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      updateType: 0,
      updateTypeDialog: false,
      dialogLoading: false,
      highlist: null,
      formInline: {
        page: 1,
        per_page: 10,
        keyword: '',
        issuer_cn: '',
        subject_cn: '',
        subject_org: '',
        ip: '',
        domain: '',
        version: '',
        is_valid: '',
        not_after: '',
        created_at: [],
        updated_at: [],
        operate_company_id: ''
      },
      checkedAll: false,
      highCheckdialog: false,
      certStatusArr: [
        {
          name: '可信',
          id: 1
        },
        {
          name: '不可信',
          id: 0
        }
      ],
      versionArr: ['v1', 'v2', 'v3'],
      validArr: [
        {
          name: '已过期',
          id: 0
        },
        {
          name: '未过期',
          id: 1
        }
      ],

      tableHeader: [
        {
          label: '使用者（CN）',
          name: 'subject_cn',
          minWidth: '120'
        },

        {
          label: '使用组织（O）',
          name: 'subject_org',
          minWidth: '120'
        },
        {
          label: '企业名称',
          name: 'company_name',
          minWidth: '120'
        },
        {
          label: '颁发者',
          name: 'issuer_cn',
          minWidth: '120'
        },
        {
          label: '关联IP',
          name: 'ip',
          minWidth: '150'
        },
        {
          label: '关联域名',
          name: 'domain',
          minWidth: '200'
        },
        {
          label: '版本',
          name: 'version',
          minWidth: '60'
        },
        {
          label: '证书类型',
          name: 'is_self_sign',
          minWidth: '90'
        },
        {
          label: '有效期',
          name: 'not_after',
          minWidth: '150'
        },
        {
          label: '发现时间',
          name: 'created_at',
          minWidth: '150'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: '150'
        }
      ],
      typeArr: [
        {
          name: '全部',
          id: 0
        },
        {
          name: '数字资产',
          id: 1
        },
        {
          name: '数据泄露',
          id: 2
        }
      ],
      statusArr: [
        // 状态 0/1 禁用/启用
        {
          name: '禁用',
          id: 0
        },
        {
          name: '启用',
          id: 1
        }
      ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      uploadAction: '',
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      btnLoading: false,
      certInfoDialog: false,
      ruleForm: {
        way: 0,
        type: 0,
        name: ''
      },
      rules: {
        name: [{ required: true, message: ' ', trigger: 'blur' }],
        type: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      user: {
        role: ''
      },
      notifyFilterId: '',
      notifyFilterMsg: ''
    }
  },
  mounted() {
    this.notifyFilterId = this.$route.query.notifyFilterId
    this.notifyFilterMsg = this.$route.query.notifyFilterMsg
    this.formInline.website_message_id = this.notifyFilterId || ''
    if (sessionStorage.getItem('userMessage')) {
      this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (this.userInfo) {
        this.user = this.userInfo.user
      }
    }
    if (this.$route.query.overdue) {
      this.formInline.not_after = 0
      this.selectChange(0, 'not_after', this.validArr, true, false)
      this.highlist = Object.assign({}, this.formInline)
      this.hightFilterIsShow()
    }
    if (this.user.role == 2 && !this.currentCompany) return
    this.getData()
  },
  watch: {
    getterCompanyChange(val) {
      if (this.notifyFilterId) {
        this.clearNotifyFilter()
      }
    },
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goBackFromBrief() {
      sessionStorage.setItem('menuId', '1-1')
      this.changeMenuId('1-1')
      this.$router.go(-1)
    },
    // 有效期处理
    assetsDeal(time) {
      let timer = this.secondsFormatValid(
        (new Date(time.replace(/-/g, '/')).getTime() - new Date().getTime()) / 1000
      )
      if (new Date(time.replace(/-/g, '/')).getTime() - new Date().getTime() < 0) {
        return '已过期'
      } else {
        return '剩余(' + timer + ')'
      }
    },
    clearNotifyFilter() {
      this.$router.replace({ path: '/certAsset', query: {} })
    },
    get_ipdomain(val, filter) {
      let arr = []
      if (val && val.length) {
        if (filter) {
          arr = val
            .filter((item) => {
              return item
            })
            .slice(0, 2)
        } else {
          arr = val.filter((item) => {
            return item
          })
        }
      } else {
        arr = []
      }

      return arr
    },
    objectSpanMethodList({ row, column, rowIndex, columnIndex }) {
      this.setRowSpan(this.tableData)
      // 当前列号columnIndex
      // 当前行号rowIndex
      // 当前行row
      // 当前列column

      //我们需要把内容和名称如果相等就进行合并
      if (columnIndex === 4) {
        // 列表的ip在第3列
        return {
          rowspan: row.rowspan, // 列跨越x格
          colspan: 1 // 行跨越1格
        }
      }
    },

    setRowSpan(data) {
      const tableData = data
      tableData.forEach((item) => {
        item.rowspan = 1
        item.Content_span = 1
      })
      // 双层循环
      for (let i = 0; i < tableData.length; i++) {
        for (let j = i + 1; j < tableData.length; j++) {
          // 此处可根据相同字段进行合并，此处是根据的itemName
          if (tableData[i].ip === tableData[j].ip) {
            tableData[i].rowspan++
            tableData[j].rowspan--
          }
        }
        // 这里跳过已经重复的数据
        i = i + tableData[i].rowspan - 1
      }
    },

    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else if (this.$route.path == '/index') {
        bol = 'tableIndex tableWrap'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    uploadSucc(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.ruleForm.name = response.data
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadErr(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    uploadRemove(file, fileList) {},
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过20MB!')
      }
      return isLt1M
    },

    getData() {
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at = this.formInline.created_at ? this.formInline.created_at : []
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      certAssetsList(this.formInline)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
          this.selectArr = res.data.condition
          this.doLayout(this, 'eltable')
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
          if ((!this.total || this.total == 0) && this.notifyFilterId) {
            this.$message.error('该批新增证书资产已被删除')
          }
          this.doLayout(this, 'eltable')
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async exportExcel() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      let params = {
        ...this.formInline,
        ids: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            })
      }
      try {
        this.exportLoadingBtn = true
        let res = await exportCertAssets(params)

        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.url)
          this.$refs.eltable.clearSelection()
          this.checkedAll = false
          this.exportLoadingBtn = false
        }
      } catch (error) {
        this.exportLoadingBtn = false
      }
    },
    checkInfo(id) {
      this.$refs.certAssetDetail.checkInfo(id)
    },
    async remove(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'keyword_del_cancel',
        confirmButtonClass: 'keyword_del_sure',
        customClass: 'keyword_del',
        type: 'warning'
      })
        .then(async () => {
          this.formInline.page = this.currentPage
          this.formInline.per_page = this.pageSize
          if (this.checkedAll) {
            this.formInline.ids = icon == 'one' ? [id] : []
          } else {
            this.formInline.ids =
              icon == 'one'
                ? [id]
                : this.checkedArr.map((item) => {
                    return item.id
                  })
          }
          let obj = Object.assign({}, this.formInline)
          let res = await delCertAssets(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.checkedAll = false
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.$refs.eltable.clearSelection()
            this.getData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.keyword_del>.el-message-box__btns')
        del.children[0].id = 'keyword_del_cancel'
        del.children[1].id = 'keyword_del_sure'
      }, 50)
    },
    wayChange(val) {
      this.ruleForm.name = ''
    },
    downloadcluesExcel() {
      window.location.href = '/downloadTemplate/关键词管理模板.xlsx'
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        page: 1,
        per_page: 10,
        keyword: '',
        issuer_cn: '',
        subject_cn: '',
        subject_org: '',
        ip: '',
        domain: '',
        version: '',
        is_valid: '',
        not_after: '',
        created_at: [],
        updated_at: [],
        operate_company_id: ''
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .validClassNormal {
    color: #2677ff;
  }
  .validClassNot {
    color: #ff7900;
  }

  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableIndex {
      height: 100% !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
      .detailDiv {
        border-bottom: 1px solid #ebeef5;
      }
      .detailDiv:last-child {
        border-bottom: 0;
      }
      .detail {
        padding: 0;
        height: 40px;
        line-height: 40px;
        border-bottom: 1px solid #ebeef5;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .detail:last-child {
        border-bottom: 0;
      }
      /deep/.el-table__body td.el-table__cell div {
        padding: 0 !important;
        .cell {
          padding: 0 !important;
        }
      }
      // /deep/.el-table__body .cell {
      //   padding: 0 !important;
      // }
    }
  }
}
.index {
  margin-top: 30px;
}
.tagBlock {
  // height: 24px;
  text-align: center;
  // line-height: 24px;
  border-radius: 4px;
  padding: 4px 6px;
}
.greenTagBlock {
  color: rgb(130, 189, 91);
  border: 1px solid #e8efdf;
  background-color: rgb(241, 249, 236);
}
.orangeTagBlock {
  color: #c8a870;
  border: 1px solid #f0e8db;
  background-color: #fbf5ec;
}
.myruleItem {
  line-height: 22px;
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
