<template>
  <div class="myBox">
    <div class="box">
      <div class="grayBox">
        <div v-if="$route.path == '/checkTask'">
          <span>导入核查 > <span style="color: #2677ff; font-weight: 600">导入核查资产</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >导入需要进行核查的资产，帮助判断是否存在于资产台账或者推荐资产库中</span
          >
        </div>
        <div v-if="$route.path == '/statusTask'">
          <span>导入检测 > <span style="color: #2677ff; font-weight: 600">导入检测资产</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >导入需要进行检测的资产，帮助快速判定资产的存活状态并输出结果</span
          >
        </div>
      </div>
    </div>
    <div class="boxTwo">
      <div class="uploadWrap">
        <div class="statusRadio" v-if="$route.path == '/statusTask'">
          <el-radio v-model="checkoutType" label="assets_status_detect">IP在线离线检测</el-radio>
          <el-radio v-model="checkoutType" label="url_status_detect">URL访问状态检测</el-radio>
        </div>
        <p class="downloadClass" @click="downloadAssetsExcel">
          <i class="el-icon-warning"></i>请点击下载
          <span
            >{{
              $route.path == '/checkTask'
                ? '核查'
                : checkoutType == 'assets_status_detect'
                  ? '资产IP在线离线检测'
                  : '资产URL访问状态检测'
            }}模板</span
          >
        </p>
        <el-upload
          class="upload-demo"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          accept=".xlsx"
          :before-upload="beforeIpUpload"
          :on-success="uploadSuccess"
          :on-remove="uploadRemove"
          :on-error="uploadError"
          :limit="uploadMaxCount"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">{{
            `支持上传xlsx文件，${$route.path == '/checkTask' ? '单次任务最多核查5000条资产，' : '单次任务最多检测5000条资产，'}且大小不超过20M`
          }}</div>
        </el-upload>
      </div>
      <div class="footer">
        <el-button
          v-if="$route.path == '/checkTask'"
          class="normalBtn"
          :loading="checkLoading"
          type="primary"
          @click="goDownTask(1)"
          id="unit_scan"
          >选择核查模式</el-button
        >
        <el-button
          v-if="$route.path == '/statusTask'"
          class="normalBtn"
          :loading="outputLoading"
          type="primary"
          @click="goDownTask(2)"
          id="unit_scan"
          >输出检测结果</el-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import Lottie from 'vue-lottie/src/lottie.vue'
import { mapGetters, mapState } from 'vuex'
import { urlStartDetect, startDetect, fileCheck } from '@/api/apiConfig/api.js'

export default {
  components: { Lottie },
  props: ['taskInfoData'],
  data() {
    return {
      outputLoading: false,
      checkLoading: false,
      uploadAction: `${this.golangUploadSrcIp}/public/upload`,
      uploadHeaders: {
        is_golang: 1,
        Authorization: localStorage.getItem('token')
      },
      user: { role: '' },
      uploadMaxCount: 1,
      fileList: [],
      uploadPath: '',
      uploadName: '', // 数据泄露的上传后路径
      checkoutType: 'assets_status_detect'
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.fileList = []
        this.uploadPath = ''
        this.uploadName = ''
      }
    },
    checkoutType() {}
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  beforeDestroy() {
    clearInterval(this.setIntervalLevel)
    this.setIntervalLevel = null
  },
  methods: {
    // 确认
    async goDownTask(icon) {
      if (icon == 1) {
        // 资产核查
        if (!this.uploadPath) {
          this.$message.error('请上传文件')
          return
        }
        this.checkLoading = true
        let res = await fileCheck({
          file_path: this.uploadPath,
          operate_company_id: this.currentCompany
        }).catch(() => {
          this.checkLoading = false
        })
        if (res.code == 0) {
          this.checkLoading = false
          this.$emit('son', '2', '', { file_path: this.uploadPath })
        } else {
          this.checkLoading = false
        }
      } else {
        // 检测输出结果
        if (!this.checkoutType) {
          this.$message.error('请选择检测类型')
          return false
        }
        if (!this.uploadName) {
          this.$message.error('请上传文件')
          return
        }
        this.outputLoading = true
        let obj = {
          file: this.uploadName,
          operate_company_id: this.currentCompany
        }
        let funcName = this.checkoutType === 'assets_status_detect' ? startDetect : urlStartDetect
        let res = await funcName(obj).catch(() => {
          this.outputLoading = false
        })
        if (res.code == 0) {
          this.outputLoading = false
          this.$emit('son', '2', res.data.task_id, '', this.checkoutType)
        }else{
          this.outputLoading = false
        }
      }
    },
    downloadAssetsExcel() {
      if (this.$route.path == '/checkTask') {
        // 资产核查任务
        window.location.href = `/downloadTemplate/资产核查模板.xlsx`
      } else if (this.$route.path == '/statusTask') {
        // 资产状态检测
        if (!this.checkoutType) {
          this.$message.error('请选择检测类型')
          return
        }
        window.location.href =
          this.checkoutType == 'assets_status_detect'
            ? `/downloadTemplate/资产IP在线离线检测模板.xlsx`
            : `/downloadTemplate/资产URL访问状态检测模板.xlsx`
      }
    },
    beforeIpUpload(file) {
      // if (!this.checkoutType) {
      //   this.$message.error('请选择检测类型')
      //   return false
      // }
      let isLt1M = ''
      isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过20M!`)
      }
      return isLt1M
    },
    uploadSuccess(response, file, fileList) {
      if (file.response && file.response.data && file.response.data.name) {
        this.uploadName = file.response.data.name
        this.uploadPath = file.response.data.path
      }
      if (file.response.code == 0) {
        this.$message.success('上传成功！')
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadError(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      if (myError.code == 401) {
        this.$router.push('/login')
        sessionStorage.clear()
        localStorage.clear()
      } else {
        this.$message.error(myError.message)
      }
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.uploadName = ''
        this.uploadPath = ''
      }
    }
  }
}
</script>
<style lang="less" scoped>
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.progressBarBox {
  padding-bottom: 4px !important;
}
.fourthBox {
  height: 100%;
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .fourthTitle {
    position: absolute;
    top: 20px;
    left: 0px;
  }
}
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.boxTwo {
  height: calc(100% - 92px) !important;

  .uploadWrap {
    width: 45%;
    height: 91%;
    margin: 0 auto;
    padding-top: 8%;
    box-sizing: border-box;
    overflow: auto;
    .statusRadio {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      line-height: 40px;
      /deep/.el-radio {
        display: flex;
        align-items: center;
      }
      /deep/.el-radio__inner {
        width: 20px;
        height: 20px;
      }
      /deep/.el-radio__label {
        font-size: 18px;
        font-weight: 700;
      }
    }
    .el-upload__tip {
      margin-top: 16px;
      line-height: 30px;
    }
    .downloadClass {
      height: 40px;
      line-height: 40px;
      margin-bottom: 16px;
      background: rgba(38, 119, 255, 0.18);
      border-radius: 2px;
      border: 1px solid rgba(38, 119, 255, 0.44);
      cursor: pointer;
      i {
        font-size: 14px;
        color: #2677ff;
        margin: 0 8px 0 16px;
      }
      span {
        color: #2677ff;
      }
    }
  }
}
</style>
