<template>
  <div class="container">
    <div class="headerTitle">
      <span class="goback" @click="goBack"
        ><i class="el-icon-arrow-left"></i>返回<span class="spline">/</span></span
      >
      {{ $route.path == '/checkTask' ? '资产核查任务' : '资产状态检测' }}
      <span v-if="$route.path == '/checkTask'" class="headerShow"
        ><i class="el-icon-warning"></i>快速盘点客户资产是否已纳入系统进行管理</span
      >
      <span v-else class="headerShow"
        ><i class="el-icon-warning"></i>快速判断导入资产的存活状态并输出结果</span
      >
    </div>
    <div class="home_header">
      <div class="tabsWrap">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane :label="$route.path == '/checkTask' ? '核查任务' : '检测任务'" name="first">
          </el-tab-pane>
          <el-tab-pane v-if="$route.path == '/checkTask'" label="任务记录" name="second">
          </el-tab-pane>
          <el-tab-pane v-if="$route.path == '/statusTask'" label="任务记录" name="second">
          </el-tab-pane>
          <!-- <el-tab-pane v-if="$route.path == '/statusTask'" label="URL访问状态检测任务记录" name="three">
                  </el-tab-pane> -->
        </el-tabs>
      </div>
      <div
        class="tab_content"
        :style="activeName == 'first' ? 'height: calc(100% - 64px);' : 'height: calc(100% - 44px);'"
      >
        <unitTask v-if="activeName == 'first'" :taskId="taskId" />
        <unitRecord
          v-if="activeName == 'second' || activeName == 'three'"
          :activeName="activeName"
          @son="sonData"
        />
      </div>
    </div>
  </div>
</template>

<script>
import unitTask from './checkTask.vue'
import unitRecord from './checkTaskrecord.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
export default {
  components: { unitRecord, unitTask },
  data() {
    return {
      activeName: 'first',
      taskId: ''
    }
  },
  watch: {
    getterWebsocketMessage(msg) {}
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage'])
  },
  mounted() {
    if (this.$route.query.activeName) {
      this.activeName = this.$route.query.activeName
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goBack() {
      sessionStorage.setItem('menuId', '2-1')
      this.changeMenuId('2-1')
      this.$router.push('/assetsTanzhi')
    },
    // 用于任务记录查看详情
    sonData(val) {
      this.activeName = 'first'
      this.taskId = val
    },
    handleClick(tab) {
      this.taskId = ''
      this.$router.push({
        query: {
          activeName: tab.name || 'first'
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  // background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    & > .tabsWrap {
      .el-tabs__nav {
        padding-left: 20px;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        // min-width: 60px;
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        // padding: 0;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
      }
    }
  }
}
</style>
