<template>
  <div class="myBox">
    <div class="box">
      <div class="grayBox">
        <div>
          <span>输出核查结果 > <span style="color: #2677ff; font-weight: 600">核查结果</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >模板中如果只填写了IP进行核查，系统默认只要这个IP存在即为存在，并且返回所有的存在端口</span
          >
        </div>
      </div>
    </div>
    <div v-if="progressBar" class="fourthBox">
      <div class="yuTu tuThird" lazy></div>
      <div style="margin: 12px 8px 10px 0px"
        ><span style="margin: 0px 8px 0px 0px">正在核查数据</span
        ><span style="color: #2677ff">{{ currentPercent }}%</span></div
      >
      <div class="fourthProgressBox"
        ><el-progress
          :stroke-width="14"
          :percentage="currentPercent"
          style="margin-bottom: 20px"
        ></el-progress
      ></div>
    </div>
    <div v-if="!progressBar" class="boxTwo1">
      <div class="tableLabel">
        <div>
          <div>输出结果</div>
          <div class="confirmBox">
            <el-radio-group v-model="tab" @change="changeTab(tab, 'isTab')">
              <el-radio-button :label="item.level" v-for="(item, index) in tabList" :key="index">{{
                item.name
              }}</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div class="progressBoxFinish">
        <span><i class="el-icon-success"></i>核查完成</span>
        <div class="progressContent">
          <img src="../../assets/images/taskSed.png" alt="" />
          <span v-if="tab == 1"
            >导入{{ ipNum }}个ip,{{ portNum }}个端口，系统核查后发现，有{{
              included_num
            }}个端口已纳入系统管理，{{ not_included_num }}个未纳入系统管理
            {{ new_port ? `，同时系统发现新增${new_port}个端口资产` : '' }}</span
          >
          <span v-if="tab == 2">系统核查发现新增{{ ipNum }}个IP，{{ portNum }}个端口资产</span>
          <span v-if="tab == 3"
            >包含用户导入和系统发现，总计{{ ipNum }}个IP，{{ portNum }}个端口资产</span
          >
        </div>
      </div>
      <div class="myTable">
        <el-table
          border
          v-loading="loading"
          :data="tableData"
          row-key="id"
          ref="eltable"
          height="100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            v-for="(item, itemIndex) in tableHeaderIsShow"
            :key="itemIndex"
            align="left"
            :prop="item.name"
            :label="item.label"
            :fixed="item.fixed"
            :min-width="item.minWidth"
          >
            <template slot-scope="scope">
              <div class="detail" style="padding-left: 12px" v-if="item.name == 'ip'">
                <span>
                  <!-- 折叠按钮：端口，域名，组件信息折叠 -->
                  <i
                    v-if="scope.row.ports && scope.row.ports.length > 3 && !scope.row.isExpand"
                    class="el-icon-plus"
                    id="expandClass"
                    @click="getExpand(scope.$index)"
                  ></i>
                  <i
                    v-if="scope.row.ports && scope.row.ports.length > 3 && scope.row.isExpand"
                    class="el-icon-minus"
                    id="expandClass"
                    @click="getExpands(scope.$index)"
                  ></i>
                </span>
                <span>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
              </div>
              <div v-else>
                <div
                  class="detail"
                  v-for="(detailItem, detailindex) in getExpandData(scope.row.ports, scope.$index)"
                  :key="detailindex"
                  style="padding-right: 12px !important; padding-left: 12px !important"
                >
                  <el-tooltip
                    :open-delay="500"
                    class="item"
                    v-if="item.name == 'url' && detailItem[item.name]"
                    effect="dark"
                    :content="String(detailItem[item.name])"
                    placement="top"
                  >
                    <a
                      v-if="String(detailItem[item.name]).includes('http')"
                      style="color: #409eff"
                      :href="detailItem[item.name]"
                      target="_blank"
                      >{{ detailItem[item.name] }}</a
                    >
                    <span v-else>{{ detailItem[item.name] }}</span>
                  </el-tooltip>
                  <span v-else-if="item.name == 'url' && !detailItem[item.name]">-</span>
                  <!-- 1-用户导入+系统发现 2-用户导入 3-系统发现 -->
                  <div v-else-if="item.name == 'is_included'">
                    <el-tag
                      v-if="detailItem['is_included'] == 1 || detailItem['is_included'] == 2"
                      style="margin-right: 10px; margin-top: 5px"
                      type="success"
                    >
                      用户导入
                    </el-tag>
                    <el-tag v-if="detailItem['is_included'] == 1 || detailItem['is_included'] == 3">
                      系统发现
                    </el-tag>
                  </div>
                  <div v-else>
                    <el-tooltip
                      v-if="detailItem[item.name]"
                      :open-delay="500"
                      class="item"
                      effect="dark"
                      :content="String(detailItem[item.name])"
                      placement="top"
                    >
                      <p>{{ detailItem[item.name] ? detailItem[item.name] : '-' }}</p>
                    </el-tooltip>
                    <p v-else>-</p>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
      <div class="footer">
        <el-button class="normalBtn" type="primary" @click="goDownTask(1)" id="unit_scan"
          >下载核查结果</el-button
        >
        <el-button
          class="normalBtn"
          type="primary"
          :loading="asyncLoading"
          @click="asyncAssets"
          id="unit_scan"
          >同步台账</el-button
        >
        <el-button class="normalBtnRe" type="primary" @click="goDownTask(3)" id="unit_scan"
          >完成</el-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import Lottie from 'vue-lottie/src/lottie.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import {
  auditResult,
  auditDownload,
  detectDownload,
  auditTaskInfo,
  auditSyncAssets,
  finishAudit
} from '@/api/apiConfig/api.js'

export default {
  components: { Lottie },
  props: ['taskInfoData', 'taskId'],
  data() {
    return {
      ipNum: 0,
      portNum: 0,
      not_included_num: 0,
      included_num: 0,
      new_port: 0,
      tableData: [],
      tableHeader: [
        {
          label: 'IP地址',
          name: 'ip',
          minWidth: '150',
          fixed: 'left',
          path: [1, 2, 3]
        },
        {
          label: '系统名称',
          name: 'system_name',
          minWidth: '120',
          path: [1, 2, 3]
        },
        {
          label: '端口',
          name: 'port',
          minWidth: '80',
          path: [1, 2, 3]
        },
        {
          label: '域名',
          name: 'domain',
          minWidth: '100',
          path: [1, 2, 3]
        },
        {
          label: '协议',
          name: 'protocol',
          minWidth: '100',
          path: [1, 2, 3]
        },
        {
          label: 'URL',
          name: 'url',
          minWidth: '150',
          path: [1, 2, 3]
        },
        {
          label: '组件信息',
          name: 'component',
          minWidth: '100',
          path: [1, 2, 3]
        },
        {
          label: '状态码',
          name: 'status_code',
          minWidth: '100',
          path: [1, 2, 3]
        },
        {
          label: '资产标签',
          name: 'is_included',
          minWidth: '180',
          path: [1, 3]
        }
      ],
      loading: false,
      asyncLoading: false,
      currentPercent: 0,
      noIncluded: 0,
      progressBar: false,
      total: 10,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      intervalTimer: null,
      tab: 1,
      tabList: [
        {
          level: 1,
          name: '核查结果',
          icon: 'a'
        },
        {
          level: 2,
          name: '系统发现数据',
          icon: 'b'
        },
        {
          level: 3,
          name: '数据总表',
          icon: 'c'
        }
      ]
    }
  },
  watch: {
    taskId(val) {
      this.getRunningTask()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage', 'getterCurrentCompany']),
    tableHeaderIsShow(path) {
      let arr = this.tableHeader.filter((item) => {
        return item.path.indexOf(this.tab) != -1
      })
      if (arr && arr.length > 0) {
        arr[0].fixed = 'left'
      }
      return arr
    }
  },
  beforeDestroy() {
    clearTimeout(this.intervalTimer)
    this.intervalTimer = null
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getRunningTask()
    } else {
      this.getRunningTask()
    }
  },
  methods: {
    async asyncAssets() {
      this.asyncLoading = true
      let obj = {
        task_id: this.taskId,
        operate_company_id: this.currentCompany
      }
      let res = await auditSyncAssets(obj).catch(() => {
        this.asyncLoading = false
      })
      if (res.code == 0) {
        this.asyncLoading = false
        if (res.data && res.data.assets) {
          this.$message.success(
            `操作成功！本次同步到台账${res.data.assets}条数据${res.data.limit ? '；有' + res.data.limit + '条超出台账总量限制，已自动忽略' : ''}`
          )
        } else {
          this.$message.success('操作成功！')
        }
      }
    },
    getExpandData(data, index) {
      let arr = []
      if (!this.tableData[index].isExpand) {
        arr = data && data.length > 0 ? data.slice(0, 3) : []
      } else {
        arr = data
      }
      this.doLayout(this, 'eltable')
      return arr
    },
    // 展开
    getExpand(index) {
      this.tableData[index].isExpand = true
    },
    // 收缩
    getExpands(index) {
      this.tableData[index].isExpand = false
    },
    // 切换查询等级数据
    async changeTab(val, isTab) {
      if (isTab) {
        // tab切换要把分页重置
        this.currentPage = 1
      }
      this.getListDataLeft(val)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getListDataLeft(this.tab)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getListDataLeft(this.tab)
    },
    async getRunningTask() {
      let obj = {
        task_id: this.taskId,
        operate_company_id: this.currentCompany
      }
      let res = await auditTaskInfo(obj)
      if (res.code == 0) {
        if (res.data) {
          if (res.data.status == 1) {
            // 正在进行
            this.progressBar = true
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
            clearTimeout(this.intervalTimer)
            this.intervalTimer = null
            this.intervalTimer = setTimeout(() => {
              // 任务未完成，定时请求接口
              this.getRunningTask()
            }, 3000)
          } else if (res.data.status == 2) {
            // 已完成
            clearTimeout(this.intervalTimer)
            this.intervalTimer = null
            this.currentPercent = 100
            setTimeout(() => {
              this.progressBar = false
            }, 500)
            this.getListDataLeft(this.tab)
          }
        }
      } else {
        clearTimeout(this.intervalTimer)
        this.intervalTimer = null
      }
    },
    async getListDataLeft(tab) {
      let obj = {
        type: 1,
        result_type: tab, // 1-核查结果，2-域名发现，3-数据总表）
        task_id: this.taskId,
        current_page: this.currentPage,
        per_page: this.pageSize,
        operate_company_id: this.currentCompany
      }
      this.loading = true
      let res = await auditResult(obj).catch(() => {
        this.loading = false
      })
      this.loading = false
      let arr = res.data.items ? res.data.items : []
      arr.forEach((item) => {
        item['isExpand'] = false
      })
      this.tableData = arr
      this.total = res.data && res.data.total ? res.data.total : 0
      this.ipNum = res.data.ip ? res.data.ip : 0
      this.portNum = res.data.port ? res.data.port : 0
      this.not_included_num = res.data.not_included ? res.data.not_included : 0
      this.included_num = res.data.included ? res.data.included : 0
      this.new_port = res.data.new_port ? res.data.new_port : 0
    },
    // 下载结果
    async goDownTask(icon) {
      let obj = null
      if (icon == 1 || icon == 2) {
        // 核查、auditDownload
        obj = {
          task_id: this.taskId,
          operate_company_id: this.currentCompany,
          get_file_path: true
        }
        let funcName = icon == 1 ? auditDownload : detectDownload
        let res = await funcName(obj)
        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.file_path)
        }
      } else {
        // 完成
        let res = await finishAudit({
          task_id: this.taskId,
          operate_company_id: this.currentCompany
        })
        if (res.code == 0) {
          this.$emit('son', '1')
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.progressBarBox {
  padding-bottom: 4px !important;
}
.fourthBox {
  height: calc(100% - 77px);
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .fourthTitle {
    position: absolute;
    top: 20px;
    left: 0px;
  }
}
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  background-repeat: no-repeat;
  background-size: 100%;
  background-position-y: 0px;
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.tuThird {
  width: 180px !important;
  height: 180px !important;
  background-image: url('../../assets/images/dongtu2.png');
}
.boxTwo1 {
  height: calc(100% - 196px) !important;
}
.tableLabel {
  .el-icon-question {
    color: #2677ff;
    font-size: 16px;
    margin-left: 10px;
  }
}
/deep/.myTable1 {
  height: calc(100% - 102px) !important;
}
/deep/.myTable {
  padding: 0px 20px;
  height: calc(100% - 92px) !important;
  display: flex;
  justify-content: space-between;
  #expandClass {
    background: rgba(245, 247, 250, 0);
    border-radius: 2px;
    border: 1px solid #acb4c0;
    color: #62666c;
    z-index: 3;
    cursor: pointer;
    font-size: 8px;
  }
  .detail {
    padding: 0 0;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ebeef5;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    p {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .el-tag {
      height: 30px;
      line-height: 29px;
    }
  }
  .el-table__body td.el-table__cell {
    padding: 0 !important;
    .cell {
      padding: 0 !important;
    }
  }
  .el-table .el-table__cell.is-hidden > * {
    visibility: visible;
  }
  .el-table__body .cell {
    padding: 0 !important;
  }
  .detail:last-child {
    border-bottom: 0;
  }
  .el-table .el-table__cell {
    border-right: 1px solid #ebeef5 !important;
  }
  .el-table--border .el-table__cell,
  .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
    border-right: 1px solid #e1e5ed !important;
  }
  .el-table th.el-table__cell::after {
    display: none;
  }
  & > div {
    height: 100%;
    width: 48%;
  }
  .tableHeader {
    height: 41px;
    background: rgba(250, 251, 252, 1);
    border: 1px solid rgba(233, 235, 239, 1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #62666c;
    span {
      display: inline-block;
      width: 50%;
      text-align: center;
    }
  }
  .title {
    margin-bottom: 8px;
    color: rgba(98, 102, 108, 1);
    span {
      color: #2677ff;
      margin-left: 10px;
    }
  }
  .infinite-list-wrapper {
    height: 81%;
    .list {
      border-bottom: 1px solid rgba(233, 235, 239, 1);
      li {
        background: transparent;
        border-bottom: 0;
        color: rgba(98, 102, 108, 1);
      }
      .emptyTable {
        text-align: center;
        color: rgba(98, 102, 108, 1);
        padding: 10px 0;
      }
    }
    .tableInfo {
      text-align: center;
      color: rgba(98, 102, 108, 1);
    }
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
