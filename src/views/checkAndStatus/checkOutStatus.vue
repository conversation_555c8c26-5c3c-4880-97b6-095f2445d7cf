<template>
  <div class="myBox">
    <div class="box">
      <div class="grayBox">
        <div v-if="$route.path == '/statusTask'">
          <span>输出检测结果 > <span style="color: #2677ff; font-weight: 600">检测结果</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            v-if="checkoutType === 'assets_status_detect'"
            >模板中如果只填写了IP，系统默认探测全部常用端口，并且返回探测结果</span
          >
        </div>
      </div>
    </div>
    <div v-if="progressBar" class="fourthBox">
      <div class="yuTu tuThird" lazy></div>
      <div style="margin: 12px 8px 10px 0px"
        ><span style="margin: 0px 8px 0px 0px">正在检测数据</span
        ><span style="color: #2677ff">{{ currentPercent }}%</span></div
      >
      <div class="fourthProgressBox"
        ><el-progress
          :stroke-width="14"
          :percentage="currentPercent"
          style="margin-bottom: 20px"
        ></el-progress
      ></div>
    </div>
    <div v-if="!progressBar">
      <div class="progressBoxFinish">
        <span><i class="el-icon-success"></i>检测完成</span>
        <div class="progressContent">
          <img src="../../assets/images/taskSed.png" alt="" />
          <span>导入资产共计：</span>
          <span v-if="checkoutType === 'assets_status_detect'"
            ><span style="color: #2677ff">{{ totalLeft / 1 + totalRight / 1 }}</span
            >个，经过检测，有{{ totalLeft }}个在线，{{ totalRight }}个离线</span
          >
          <span v-if="checkoutType === 'url_status_detect'"
            ><span style="color: #2677ff">{{ totalAll }}</span
            >个，经过检测，有{{ onlineNum }}个可访问，{{ outlineNum }}个不可访问</span
          >
        </div>
      </div>
    </div>
    <div v-if="!progressBar" class="boxTwo1">
      <div class="tableLabel">
        <div>
          <div>输出结果</div>
        </div>
        <div> </div>
      </div>
      <div class="myTable" v-if="checkoutType === 'assets_status_detect'">
        <div class="tableItem">
          <p class="title"
            >在线资产<span>({{ totalLeft }})</span></p
          >
          <p class="tableHeader">
            <span>IP</span>
            <span>端口</span>
          </p>
          <div class="infinite-list-wrapper" style="overflow: auto">
            <ul class="list" v-infinite-scroll="loadLeft" infinite-scroll-disabled="disabledLeft">
              <li v-for="item in tableDataLeft" :key="item.ip" class="tableHeader">
                <span>{{ item.ip }}</span>
                <span>{{ item.port }}</span>
              </li>
              <li></li>
              <p v-if="tableDataLeft.length == 0" class="emptyTable">暂无数据</p>
            </ul>
            <p class="tableInfo" v-if="loadingLeft">加载中...</p>
            <p class="tableInfo" v-if="tableDataLeft.length > 10 && noMoreLeft">没有更多了</p>
          </div>
        </div>
        <div class="tableItem">
          <p class="title"
            >离线资产<span>({{ totalRight }})</span></p
          >
          <p class="tableHeader">
            <span>IP</span>
            <span>端口</span>
          </p>
          <div class="infinite-list-wrapper" style="overflow: auto">
            <ul class="list" v-infinite-scroll="loadRight" infinite-scroll-disabled="disabledRight">
              <li v-for="item in tableDataRight" :key="item.ip" class="tableHeader">
                <span>{{ item.ip }}</span>
                <span>{{ item.port }}</span>
              </li>
              <p v-if="tableDataRight.length == 0" class="emptyTable">暂无数据</p>
            </ul>
            <p class="tableInfo" v-if="loadingRight">加载中...</p>
            <p class="tableInfo" v-if="tableDataRight.length > 10 && noMoreRight">没有更多了</p>
          </div>
        </div>
      </div>
      <div class="myTableOne" style="display: block" v-if="checkoutType === 'url_status_detect'">
        <div
          v-infinite-scroll="loadAll"
          infinite-scroll-instance="10"
          infinite-scroll-disabled="disabledAll"
          class="tableWapper"
        >
          <CheckoutResult
            :tableDataAll="tableDataAll"
            :loadingAll="loadingAll"
            :noMoreAll="noMoreAll"
            @loadAll="loadAll"
            style="display: block; width: 100%; height: 100%"
          />
        </div>
      </div>
      <div class="footer">
        <el-button
          v-if="$route.path == '/statusTask'"
          class="normalBtn"
          type="primary"
          @click="goDownTask(2)"
          id="unit_scan"
          >下载检测结果</el-button
        >
        <el-button class="normalBtnRe" type="primary" @click="goDownTask(3)" id="unit_scan"
          >完成</el-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import Lottie from 'vue-lottie/src/lottie.vue'
import CheckoutResult from './checkoutResult.vue'
import { mapGetters, mapState } from 'vuex'
import {
  urlDetectDownload,
  urlStatusDetectTaskInfo,
  urlDetectResult,
  detectResult,
  auditResult,
  detectDownload,
  statusDetectTaskInfo
} from '@/api/apiConfig/api.js'

export default {
  components: { Lottie, CheckoutResult },
  props: ['taskInfoData', 'taskId', 'checkoutType'],
  data() {
    return {
      currentPercent: 0,
      noIncluded: 0,
      outlineNum: 0,
      onlineNum: 0,
      numNo: 0,
      current_page_all: 1,
      current_page_left: 1,
      current_page_right: 1,
      progressBar: true,
      total: 10,
      totalAllPage: 10,
      totalLeftPage: 10,
      totalRightPage: 10,
      totalAll: 0,
      totalLeft: 0,
      totalRight: 0,
      tableDataAll: [],
      tableDataLeft: [],
      tableDataRight: [],
      loadingAll: false,
      loadingLeft: false,
      loadingRight: false,
      intervalTimer: null
    }
  },
  watch: {
    taskInfoData(val) {}
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage']),
    noMoreAll() {
      return this.tableDataAll.length >= this.totalAll
    },
    noMoreLeft() {
      return this.tableDataLeft.length >= this.totalLeft
    },
    noMoreRight() {
      return this.tableDataRight.length >= this.totalRight
    },
    disabledAll() {
      return this.loadingAll || this.noMoreAll
    },
    disabledLeft() {
      return this.loadingLeft || this.noMoreLeft
    },
    disabledRight() {
      return this.loadingRight || this.noMoreRight
    }
  },
  watch: {
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    }
  },
  beforeDestroy() {
    clearInterval(this.intervalTimer)
    this.intervalTimer = null
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.getRunningTask()
    if (this.checkoutType === 'assets_status_detect') {
      this.getListDataLeft()
      this.getListDataRight()
    } else {
      this.getListDataAll()
    }
  },
  methods: {
    async getRunningTask() {
      let obj = {
        task_id: this.taskId,
        operate_company_id: this.currentCompany
      }
      let funcName = ''
      funcName =
        this.checkoutType === 'assets_status_detect'
          ? statusDetectTaskInfo
          : urlStatusDetectTaskInfo
      let res = await funcName(obj)
      if (res.code == 0) {
        if (res.data) {
          if (res.data.status == 1) {
            // 正在进行
            this.progressBar = true
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
            clearInterval(this.intervalTimer)
            this.intervalTimer = null
            this.intervalTimer = setInterval(() => {
              this.getRunningTask()
            }, 3000)
          } else if (res.data.status == 2) {
            // 已完成
            clearInterval(this.intervalTimer)
            this.intervalTimer = null
            this.currentPercent = 100
            setTimeout(() => {
              this.progressBar = false
            }, 1000)
            if (this.checkoutType === 'assets_status_detect') {
              this.getListDataLeft()
              this.getListDataRight()
            } else {
              this.getListDataAll()
            }
          }
        }
      } else {
        clearInterval(this.intervalTimer)
        this.intervalTimer = null
      }
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (
        res.cmd == 'assets_status_detect_progress' &&
        res.data &&
        this.taskId == res.data.task_id
      ) {
        // 数据导入进度
        if (res.data.status == 2) {
          this.progressBar = false // 进度条不显示
          setTimeout(() => {
            if (this.checkoutType === 'assets_status_detect') {
              this.getListDataLeft()
              this.getListDataRight()
            } else {
              this.getListDataAll()
            }
          }, 1500)
        } else if (res.data.status == 1) {
          // 正在导入
          this.progressBar = false // 进度条不显示
          // 推送进度;因后端拆分任务扫描，会有多个进度而导致进度回退，所以小于当前进度不显示
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
    },
    loadAll() {
      this.loadingAll = true
      let timer = setTimeout(() => {
        //请求列表接口
        this.current_page_all++
        if (this.current_page_all > this.totalAllPage) {
          this.loadingAll = false
          clearTimeout(timer)
          return
        }
        this.getLoadAll()
        this.loadingAll = false
      }, 1500)
    },
    loadLeft() {
      this.loadingLeft = true
      setTimeout(() => {
        //请求列表接口
        this.current_page_left++
        if (this.current_page_left > this.totalLeftPage) {
          this.loadingLeft = false
          return
        }
        this.getLoadLeft()
        this.loadingLeft = false
      }, 1500)
    },
    loadRight() {
      this.loadingRight = true
      setTimeout(() => {
        //请求列表接口
        this.current_page_right++
        if (this.current_page_right > this.totalRightPage) {
          this.loadingRight = false
          return
        }
        this.getLoadRight()
        this.loadingRight = false
      }, 1500)
    },
    async getLoadAll() {
      let obj = {
        online_status: 1, // 1 在线资产表
        task_id: this.taskId,
        current_page: this.current_page_all,
        per_page: 20,
        operate_company_id: this.currentCompany
      }
      let res = await urlDetectResult(obj)
      if (res.data && res.data.items) {
        // this.tableDataAll.push(...res.data.items)
        this.tableDataAll = [...this.tableDataAll, ...res.data.items]
      }
    },
    async getLoadLeft() {
      if (this.$route.path == '/statusTask') {
        let obj = {
          online_status: 1, // 1 在线资产表
          task_id: this.taskId,
          current_page: this.current_page_left,
          per_page: 20,
          operate_company_id: this.currentCompany
        }
        let res = await detectResult(obj)
        if (res.data && res.data.items) {
          this.tableDataLeft.push(...res.data.items)
        }
      } else {
        let obj = {
          type: 2, // （1-已纳入管理，2-未纳入管理）
          task_id: this.taskId,
          current_page: this.current_page_left,
          per_page: 20,
          operate_company_id: this.currentCompany
        }
        let res = await auditResult(obj)
        if (res.data && res.data.items) {
          this.tableDataLeft.push(...res.data.items)
        }
      }
    },
    async getLoadRight() {
      if (this.$route.path == '/statusTask') {
        let obj = {
          online_status: 2, // 2离线资产表
          task_id: this.taskId,
          current_page: this.current_page_right,
          per_page: 20,
          operate_company_id: this.currentCompany
        }
        let res = await detectResult(obj)
        if (res.data && res.data.items) {
          this.tableDataRight.push(...res.data.items)
        }
      } else {
        let obj = {
          type: 1, // （1-已纳入管理，2-未纳入管理）
          task_id: this.taskId,
          current_page: this.current_page_right,
          per_page: 20,
          operate_company_id: this.currentCompany
        }
        let res = await auditResult(obj)
        if (res.data && res.data.items) {
          this.tableDataRight.push(...res.data.items)
        }
      }
    },
    async getListDataAll() {
      let obj = {
        online_status: 1, // 1 在线资产表
        task_id: this.taskId,
        current_page: this.current_page_all,
        per_page: 20,
        operate_company_id: this.currentCompany
      }
      let res = await urlDetectResult(obj)

      this.tableDataAll = res.data.items ? res.data.items : []

      this.totalAll = res.data && res.data.total ? res.data.total : 0
      this.onlineNum = res.data && res.data.online_num ? res.data.online_num : 0
      this.outlineNum = res.data && res.data.offline_num ? res.data.offline_num : 0
      if (parseInt(this.totalAll / 20) == 0) {
        this.totalAllPage = 1
      } else {
        this.totalAllPage =
          parseInt(this.totalAll % 20) >= 0
            ? parseInt(this.totalAll / 20) + 1
            : parseInt(this.totalAll / 20)
      }
    },
    async getListDataLeft() {
      if (this.$route.path == '/statusTask') {
        let obj = {
          online_status: 1, // 1 在线资产表
          task_id: this.taskId,
          current_page: this.current_page_left,
          per_page: 20,
          operate_company_id: this.currentCompany
        }
        let res = await detectResult(obj)
        this.tableDataLeft = res.data.items ? res.data.items : []
        this.totalLeft = res.data && res.data.total ? res.data.total : 0
      } else {
        let obj = {
          type: 2, // （1-已纳入管理，2-未纳入管理）
          task_id: this.taskId,
          current_page: this.current_page_left,
          per_page: 20,
          operate_company_id: this.currentCompany
        }
        let res = await auditResult(obj)
        this.tableDataLeft = res.data.items ? res.data.items : []
        this.totalLeft = res.data && res.data.total ? res.data.total : 0
      }
      if (parseInt(this.totalLeft / 20) == 0) {
        this.totalLeftPage = 1
      } else {
        this.totalLeftPage =
          parseInt(this.totalLeft % 20) > 0
            ? parseInt(this.totalLeft / 20) + 1
            : parseInt(this.totalLeft / 20)
      }
    },
    async getListDataRight() {
      if (this.$route.path == '/statusTask') {
        let obj = {
          online_status: 2, // 2离线资产表
          task_id: this.taskId,
          current_page: this.current_page_right,
          per_page: 20,
          operate_company_id: this.currentCompany
        }
        let res = await detectResult(obj)
        this.tableDataRight = res.data.items ? res.data.items : []
        this.totalRight = res.data && res.data.total ? res.data.total : 0
      } else {
        let obj = {
          type: 1, // （1-纳入管理，2-未纳入管理）
          task_id: this.taskId,
          current_page: this.current_page_right,
          per_page: 20,
          operate_company_id: this.currentCompany
        }
        let res = await auditResult(obj)
        this.tableDataRight = res.data.items ? res.data.items : []
        this.totalRight = res.data && res.data.total ? res.data.total : 0
      }
      if (parseInt(this.totalRight / 20) == 0) {
        //
        this.totalRightPage = 1
      } else {
        this.totalRightPage =
          parseInt(this.totalRight % 20) > 0
            ? parseInt(this.totalRight / 20) + 1
            : parseInt(this.totalRight / 20)
      }
    },
    // 下载结果
    async goDownTask(icon) {
      let obj = null
      if (icon == 2) {
        // 核查
        obj = {
          task_id: this.taskId,
          operate_company_id: this.currentCompany,
          get_file_path: true
        }
        let funcName =
          this.checkoutType === 'assets_status_detect' ? detectDownload : urlDetectDownload
        let res = await funcName(obj)
        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.file_path)
        }
      } else {
        this.$emit('son', '1')
      }
    }
  }
}
</script>
<style lang="less" scoped>
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.progressBarBox {
  padding-bottom: 4px !important;
}
.fourthBox {
  height: calc(100% - 77px);
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .fourthTitle {
    position: absolute;
    top: 20px;
    left: 0px;
  }
}
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  background-repeat: no-repeat;
  background-size: 100%;
  background-position-y: 0px;
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.tuThird {
  width: 180px !important;
  height: 180px !important;
  background-image: url('../../assets/images/dongtu2.png');
}
.boxTwo {
  height: calc(100% - 85px) !important;
}
.tableLabel {
  .el-icon-question {
    color: #2677ff;
    font-size: 16px;
    margin-left: 10px;
  }
}
/deep/.myTable1 {
  height: calc(100% - 102px) !important;
}
/deep/.myTable {
  padding: 0px 20px;
  height: calc(100% - 92px) !important;
  display: flex;
  justify-content: space-between;
  & > div {
    height: 100%;
    width: 48%;
  }
  .tableHeader {
    height: 41px;
    background: rgba(250, 251, 252, 1);
    border: 1px solid rgba(233, 235, 239, 1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #62666c;
    span {
      display: inline-block;
      width: 50%;
      text-align: center;
    }
  }
  .title {
    margin-bottom: 8px;
    color: rgba(98, 102, 108, 1);
    span {
      color: #2677ff;
      margin-left: 10px;
    }
  }
  .infinite-list-wrapper {
    height: 81%;
    .list {
      border-bottom: 1px solid rgba(233, 235, 239, 1);
      li {
        background: transparent;
        border-bottom: 0;
        color: rgba(98, 102, 108, 1);
        span {
          display: inline-block;
          padding: 5px;
          margin-right: 8px;
          margin-bottom: 16px;
          font-weight: 400;
          white-space: nowrap;
          color: #37393c;
          background: #f8f9fc;
          border-radius: 4px;
          // border: 1px solid #DFE4ED;
        }
      }
      .emptyTable {
        text-align: center;
        color: rgba(98, 102, 108, 1);
        padding: 10px 0;
      }
    }
    .tableInfo {
      text-align: center;
      color: rgba(98, 102, 108, 1);
    }
  }
}
.myTableOne {
  padding: 15px 20px;
  height: calc(100% - 120px) !important;
  .tableWapper {
    width: 100%;
    height: calc(100% - 30px);
    overflow-y: auto;
    overflow-anchor: none;
    /deep/.el-table {
      .el-table__body {
        width: 100% !important;
        table-layout: fixed !important;
      }
    }
  }
  .tableInfo {
    line-height: 30px;
    text-align: center;
    color: rgba(98, 102, 108, 1);
  }
}
</style>
