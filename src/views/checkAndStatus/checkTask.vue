<template>
  <div class="Box">
    <stepBar
      style="margin: 20px 0 16px 0"
      :active.sync="taskStep"
      :taskStep="taskStep"
      :stepArr="stepArr[$route.path]"
      :width="$route.path == '/checkTask' ? '33%' : '50%'"
    />
    <div v-if="$route.path == '/checkTask'" class="myContent" v-loading="loading">
      <taskFirst v-if="taskStep == '1'" @son="sonData" />
      <taskSecond v-if="taskStep == '2'" :secondParams="secondParams" @son="sonData" />
      <checkTaskThird v-if="taskStep == '3'" :taskId="curt_task_id" @son="sonData" />
    </div>
    <div v-else class="myContent" v-loading="loading">
      <taskFirst v-if="taskStep == '1'" @son="sonData" />
      <!-- <statusTaskThird :taskId="75" @son="sonData" :checkoutType="'url_status_detect'"/> -->
      <statusTaskThird
        v-if="taskStep == '2'"
        :checkoutType="checkoutType"
        :taskId="curt_task_id"
        @son="sonData"
      />
    </div>
  </div>
</template>
<script>
import stepBar from '@/components/stepBar.vue'
import taskFirst from './checkIn.vue'
import taskSecond from '../unit_surveying/taskFive.vue'
import statusTaskThird from './checkOutStatus.vue'
import checkTaskThird from './checkOutCheck.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { statusDetectTaskInfo, auditTaskInfo } from '@/api/apiConfig/api.js'

export default {
  components: { stepBar, taskFirst, taskSecond, statusTaskThird, checkTaskThird },
  props: ['taskId'],
  data() {
    return {
      loading: false,
      taskInfoData: {},
      taskStep: '1',
      curt_task_id: '',
      secondParams: '',
      stepWidth: '26%',
      user: {
        role: ''
      },
      stepArr: {
        '/checkTask': [
          {
            id: 1,
            title: '导入核查资产'
          },
          {
            id: 2,
            title: '选择核查模式'
          },
          {
            id: 3,
            title: '输出核查结果'
          }
        ],
        '/statusTask': [
          {
            id: 1,
            title: '导入检测资产'
          },
          {
            id: 2,
            title: '输出检测结果'
          }
        ]
      },
      userInfo: '',
      checkoutType: ''
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getTaskInfo()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2) {
      // props接受任务记录查看详情的id
      if (!this.currentCompany) return
      this.getTaskInfo(this.taskId ? this.taskId : '')
    } else {
      this.getTaskInfo(this.taskId ? this.taskId : '')
    }
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    change(val) {
      this.taskStep = val
    },
    sonDataFirst(val, taskId) {
      if (taskId) {
        // 第一步点击确定后拿任务记录id去获取任务详情
        this.getTaskInfo(taskId)
      } else {
        this.taskStep = val
      }
    },
    sonData(val, taskId, secondParams, checkoutType) {
      //父子传参改变tab
      if (taskId) {
        // 第一步点击确定后拿任务记录id去获取任务详情
        this.taskStep = val
        this.curt_task_id = taskId
        this.checkoutType = checkoutType
      } else if (secondParams) {
        // 核查传递参数
        this.taskStep = val
        this.secondParams = secondParams
      } else {
        this.taskStep = val
      }
    },
    async getTaskInfo(taskId) {
      // 获取任务详情数据，任务记录id存在，查询对应的详情数据；不存在后端直接查询未完成的任务数据，有返回值则展示，没有返回值新建流程
      let id = taskId ? taskId : ''
      let obj = {
        task_id: id,
        operate_company_id: this.currentCompany
      }
      this.loading = true
      let funcName = ''
      if (this.$route.path == '/checkTask') {
        funcName = auditTaskInfo
      } else {
        funcName = statusDetectTaskInfo
      }
      let res = await funcName(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false

        if (res.data) {
          this.curt_task_id = res.data.task_id
          if (res.data.status) {
            if (res.data.task_type == 2) {
              this.checkoutType = 'url_status_detect'
            } else {
              this.checkoutType = 'assets_status_detect'
            }
            this.taskStep = this.$route.path == '/checkTask' ? '3' : '2'
          }
        } else {
          // 返回数据为空，直接跳转到第一步即可
          this.curt_task_id = ''
          this.taskStep = '1'
        }
      } else {
        // 返回数据为空，直接跳转到第一步即可
        this.loading = false
        this.curt_task_id = ''
        this.taskStep = '1'
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.tu {
  width: 120px !important;
  height: 120px !important;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position-y: 0px;
}
/deep/.tuSecond {
  background-image: url('../../assets/images/dongtu1.png');
}
.Box {
  height: 100%;
}
.tabBox {
  display: flex;
  align-items: center;
  margin: 20px 20px 16px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.tabBox > div {
  margin-right: 20px;
  height: 40px;
  width: 25%;
  display: flex;
  align-items: center;
}
.tabBox > div > span {
  display: flex;
  align-items: center;
}
.tabBox > div:last-child {
  margin-right: 0px;
}
.tabBaryuan {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  color: #2677ff;
  margin: 0px 8px 0px 16px;
}
.tabBargray {
  color: #ffffff;
  background: #b2bdd4;
}
.tabBaryuan1 {
  margin: 0px 8px 0px 16px;
  font-size: 24px;
  color: #2677ff;
}
.myContent {
  height: calc(100% - 56px);
}
/deep/.el-progress {
  position: relative;
  .el-progress-bar {
    padding-right: 0px !important;
  }
  .el-progress__text {
    font-size: 14px !important;
    color: #2677ff;
    position: absolute;
    bottom: 23px;
    right: 0px;
  }
}
.myBox {
  height: 100%;
}
/deep/.box {
  background-image: url('../../assets/images/bg.png');
  // background: linear-gradient(90deg, #F0F6FF 0%, #FFFFFF 100%);
  background-repeat: no-repeat;
  // width: 100%;
  background-size: 100% 100%;
  padding: 16px 0 0 16px;
}
/deep/.progressBarBox {
  display: flex;
  align-items: center;
  margin: 0px 20px;
  color: #37393c;
  padding: 16px 0;
  .tu {
    width: 100px;
    height: 100px;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .progressBox {
    flex-grow: 1;
    margin-left: 22px;
  }
  .progressBox > div {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }
}
/deep/.tableLabel {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #37393c;
  justify-content: space-between;
  // margin-bottom: 16px;
}
/deep/.tableLabel > div {
  display: flex;
  align-items: center;
}
/deep/.progressBoxFinish {
  display: flex;
  align-items: center;
  color: #37393c;
  padding: 0px 20px 16px 20px;
}
/deep/.progressBoxFinish > span {
  display: flex;
  align-items: center;
  margin-right: 16px;
  i {
    color: #10d595;
    margin-right: 5px;
    font-size: 20px;
  }
}
/deep/.progressContent {
  color: #62666c;
  display: flex;
  align-items: center;
  img {
    width: 24px;
    height: 20px;
    margin-right: 12px;
  }
}
/deep/.el-pagination {
  background: transparent !important;
  box-shadow: none !important;
}
/deep/.boxTwo {
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  padding-top: 22px;
  height: calc(100% - 213px);
}
/deep/.boxTwo1 {
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  // padding-top: 22px;
  height: calc(100% - 113px);
}
/deep/.footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  background: #ffffff;
  box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
}
</style>
