<template>
  <div class="container">
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.user_name"
            placeholder="请输入发起人检索"
            @keyup.enter.native="checkFuncList"
            id="keyword_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="keyword_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="keyword_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="remove('more')"
            id="keyword_more_del"
            >删除</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="checkDetect"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <!-- 只有单位资产测绘才有企业名称 -->
          <el-table-column
            v-for="item in tableHeader.filter((item) => {
              return (
                !item.path ||
                (!item.activeName && item.path == $route.path) ||
                (item.activeName && item.activeName == activeName && item.path == $route.path)
              )
            })"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :label="item.label"
          >
            <el-table-column
              :min-width="120"
              v-if="item.name == 'checkResult'"
              prop="included_assets"
              label="已纳入管理资产"
            >
              <template slot-scope="scope">
                <span>{{ scope.row['included_assets'] ? scope.row['included_assets'] : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :min-width="120"
              v-if="item.name == 'checkResult'"
              prop="not_included_assets"
              label="未纳入管理资产"
            >
              <template slot-scope="scope">
                <span>{{
                  scope.row['not_included_assets'] ? scope.row['not_included_assets'] : '-'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :min-width="120"
              v-if="item.name == 'checkResult'"
              prop="port_assets"
              label="新增端口资产"
            >
              <template slot-scope="scope">
                <span>{{ scope.row['port_assets'] ? scope.row['port_assets'] : '-' }}</span>
              </template>
            </el-table-column>

            <template slot-scope="scope">
              <span v-if="item.name == 'status'">{{ getState(scope.row[item.name]) }}</span>
              <span v-else-if="item.name == 'task_type'">{{
                scope.row[item.name] == 2 ? 'URL访问状态检测' : 'IP在线离线检测'
              }}</span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="200">
            <template slot-scope="scope">
              <!-- 只有已完成的才有下载结果 -->
              <el-button
                v-if="scope.row.status == 2"
                type="text"
                size="small"
                @click="downloadFunc(scope.row)"
                id="keyword_del"
                >下载结果</el-button
              >
              <!-- 已完成的没有查看详情： -->
              <el-button
                type="text"
                v-if="scope.row.status == 1"
                size="small"
                @click="reviewInfo(scope.row)"
                id="keyword_del"
                >查看详情</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="remove('one', scope.row.id)"
                id="keyword_del"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="发起人：" prop="user_name">
            <el-input
              v-model="formInline.user_name"
              placeholder="请输入发起人"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="发起时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  getUrlDetectTask,
  getDetectTask,
  getAuditTask,
  delCheckDetectTask,
  delAuditTask,
  statusDetectTaskInfo,
  auditDownload,
  detectDownload
} from '@/api/apiConfig/api.js'
import { detectTaskInfo } from '@/api/apiConfig/surveying.js'

export default {
  components: {
    tableTooltip,
    hightFilter
  },
  props: {
    activeName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      highTabShow: [
        {
          label: '发起人',
          name: 'user_name',
          type: 'input'
        },
        {
          label: '发起时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      formInline: {
        user_name: '',
        current_page: 1,
        per_page: 10,
        operate_company_id: '',
        created_at_range: []
      },
      checkedAll: false,
      highCheckdialog: false,
      tableHeader: [
        {
          label: '导入资产',
          name: 'import_assets',
          minWidth: 120
        },
        {
          label: '进度',
          name: 'status',
          minWidth: 120
        },
        {
          label: '核查结果',
          name: 'checkResult',
          minWidth: 200,
          path: '/checkTask'
        },
        {
          label: '检测类型',
          name: 'task_type',
          minWidth: 200,
          path: '/statusTask'
        },
        {
          label: '系统发现数据',
          name: 'system_find',
          minWidth: 120,
          path: '/checkTask'
        },
        {
          label: '数据总表',
          name: 'data_table',
          minWidth: 120,
          path: '/checkTask'
        },
        {
          label: '在线/可访问资产',
          name: 'online_assets',
          minWidth: 120,
          path: '/statusTask',
          activeName: 'second'
        },
        {
          label: '离线/不可访问资产',
          name: 'unonline_assets',
          minWidth: 120,
          path: '/statusTask',
          activeName: 'second'
        },
        // {
        //   label: '可访问资产',
        //   name: 'online_assets',
        //   minWidth: 120,
        //   path: '/statusTask',
        //   activeName:'three'
        // },
        // {
        //   label: '不可访问资产',
        //   name: 'unonline_assets',
        //   minWidth: 120,
        //   path: '/statusTask',
        //   activeName:'three'
        // },
        {
          label: '发起人',
          name: 'operator',
          minWidth: 120
        },
        {
          label: '发起时间',
          name: 'date',
          minWidth: 150
        }
      ],
      statusArr: [
        // 状态 0/1 禁用/启用
        {
          name: '禁用',
          id: 0
        },
        {
          name: '启用',
          id: 1
        }
      ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      ruleForm: {
        way: '0',
        name: ''
      },
      rules: {
        name: [{ required: true, message: ' ', trigger: 'blur' }]
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      user: {
        role: ''
      },
      taskInfoData: {}
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
    // this.getcehui();
  },
  watch: {
    activeName() {
      this.getData()
    },
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
        // this.getcehui();
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    },
    getterWebsocketMessage(msg) {}
  },
  computed: {
    ...mapState(['currentCompany', 'recommentFlag']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  methods: {
    async getcehui() {
      let obj = {
        taskId: '',
        data: {
          expand_source: this.$route.meta.expand_source, // 1云端推荐，0单位资产测绘
          operate_company_id: this.currentCompany
        }
      }
      let res = await detectTaskInfo(obj)
      if (res.code == 0) {
        this.taskInfoData = res.data
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    getState(val) {
      let str = ''
      if (val == 1) {
        str = '进行中'
      } else if (val == 2) {
        str = '已完成'
      } else {
        str = ''
      }
      return str
    },
    async downloadFunc(row) {
      if (row.file_path && this.$route.path != '/statusTask') {
        this.download(this.showSrcIp + row.file_path)
      } else {
        let obj = null
        obj = {
          task_id: row.id,
          operate_company_id: this.currentCompany,
          get_file_path: true
        }
        let funcName = this.$route.path == '/checkTask' ? auditDownload : detectDownload
        let res = await funcName(obj)
        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.file_path)
        }
      }
    },
    // 查看详情
    async reviewInfo(item, icon) {
      this.$router.push({
        query: {
          activeName: 'first'
        }
      })
      this.$emit('son', item.id)
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    getData() {
      this.loading = true
      this.formInline.per_page = this.pageSize
      this.formInline.current_page = this.currentPage
      this.formInline.operate_company_id = this.currentCompany
      let funcName = ''
      if (this.$route.path == '/checkTask') {
        funcName = getAuditTask
      } else {
        funcName = this.activeName == 'second' ? getDetectTask : getUrlDetectTask
      }
      funcName(this.formInline)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items ? res.data.items : []
          this.total = res.data.total
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async remove(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'keyword_del_cancel',
        confirmButtonClass: 'keyword_del_sure',
        customClass: 'keyword_del',
        type: 'warning'
      })
        .then(async () => {
          let task_id = ''
          if (this.checkedAll) {
            task_id = icon == 'one' ? [id] : []
          } else {
            task_id =
              icon == 'one'
                ? [id]
                : this.checkedArr.map((item) => {
                    return item.id
                  })
          }
          this.formInline.operate_company_id = this.currentCompany
          let obj = {
            task_ids: task_id,
            ...this.formInline
          }
          let funcName = ''
          if (this.$route.path == '/checkTask') {
            funcName = delAuditTask
          } else {
            funcName = delCheckDetectTask
          }
          let res = await funcName(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.checkedAll = false
            this.$refs.eltable.clearSelection()
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.keyword_del>.el-message-box__btns')
        del.children[0].id = 'keyword_del_cancel'
        del.children[1].id = 'keyword_del_sure'
      }, 50)
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        user_name: '',
        current_page: 1,
        per_page: 10,
        operate_company_id: '',
        created_at_range: []
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
          display: flex;
          align-items: center;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0 16px;
    }
    /deep/.el-table {
      border: 0;
      th .el-table__cell {
        border-right: 1px solid #e1e5ed !important;
      }
      th.el-table__cell::after {
        background-color: transparent !important;
      }
    }
  }
  /deep/.el-table--border .el-table__cell,
  .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
    border-right: 1px solid #e1e5ed !important;
  }
  /deep/.el-table th.el-table__cell::after {
    background-color: transparent !important;
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
