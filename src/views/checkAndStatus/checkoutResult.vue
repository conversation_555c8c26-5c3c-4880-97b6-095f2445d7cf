<template>
  <!-- 表格 -->
  <el-table
    border
    :data="tableDataAll"
    row-key="id"
    :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
    ref="eltable"
    height="100%"
    style="width: 100%"
  >
    <template slot="empty">
      <div class="emptyClass">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-kong"></use>
        </svg>
        <p>暂无数据</p>
      </div>
    </template>
    <template slot="append">
      <div>
        <p class="tableInfo" v-if="loadingAll">加载中...</p>
        <p class="tableInfo" v-if="!loadingAll && tableDataAll.length > 10 && noMoreAll"
          >没有更多了</p
        >
      </div>
    </template>
    <el-table-column
      prop="url"
      align="left"
      :show-overflow-tooltip="true"
      min-width="120"
      label="URL"
    >
    </el-table-column>
    <el-table-column
      prop="ip"
      align="left"
      :show-overflow-tooltip="true"
      min-width="120"
      label="IP"
    >
    </el-table-column>
    <el-table-column
      prop="port"
      align="left"
      :show-overflow-tooltip="true"
      min-width="120"
      label="端口"
    >
    </el-table-column>
    <el-table-column
      prop="protocol"
      align="left"
      :show-overflow-tooltip="true"
      min-width="120"
      label="协议"
    >
    </el-table-column>
    <el-table-column align="left" :show-overflow-tooltip="true" min-width="120" label="访问状态">
      <template slot-scope="{ row }">
        {{ onlineStatusObj[row.online_state] }}
      </template>
    </el-table-column>
    <el-table-column
      prop="title"
      align="left"
      :show-overflow-tooltip="true"
      min-width="120"
      label="网站标题"
    >
    </el-table-column>
    <el-table-column align="left" :show-overflow-tooltip="true" min-width="120" label="状态码">
      <template slot-scope="{ row }">
        {{ row.status_code && row.status_code != 0 ? row.status_code : '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="created_at"
      align="left"
      :show-overflow-tooltip="true"
      min-width="120"
      label="检测时间"
    >
    </el-table-column>
  </el-table>
</template>

<script>
/**
 * @author: cunhang_wei
 * @description: 监听列表是否滚动到底部
 */

const debounce = function (func, delay) {
  let timer = null
  return function () {
    if (timer) clearTimeout(timer)
    timer = null
    let self = this
    let args = arguments
    timer = setTimeout(() => {
      func.apply(self, args)
    }, delay)
  }
}

export default {
  data() {
    return {
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      onlineStatusObj: {
        1: '可访问',
        2: '不可访问'
      }
      // tableDataAll:[]
    }
  },
  props: {
    tableDataAll: {
      type: Array,
      default: () => []
    },
    loadingAll: {
      type: Boolean,
      default: false
    },
    noMoreAll: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    loadMore() {
      this.$emit('loadAll')
      // this.currentPage ++;
      // this.forbidIpListData();
    }
    // getData() {
    //   if(this.forbidIpsData.total == this.forbidIpsData.data.length && this.forbidIpsData.total > 0){
    //     return ;
    //   }
    //   this.forbidIpListParams.operate_company_id = this.currentCompany
    //   let result = await getForbidIps(this.forbidIpListParams);
    //   this.forbidIpsData.data = [...this.forbidIpsData.data ,...result.data.items];
    //   this.forbidIpsData.total = result.data.total;
    // }
  },
  directives: {
    'load-more': {
      bind(el, binding, vnode) {
        const { expand } = binding.modifiers
        // 使用更丰富的功能，支持父组件的指令作用在指定的子组件上
        if (expand) {
          /**
           * target 目标DOM节点的类名
           * distance 减少触发加载的距离阈值，单位为px
           * func 触发的方法
           * delay 防抖时延，单位为ms
           * load-more-disabled 是否禁用无限加载
           */
          let { target, distance = 0, func, delay = 200 } = binding.value
          if (typeof target !== 'string') return
          let targetEl = el.querySelector(target)
          if (!targetEl) {
            return
          }
          binding.handler = debounce(function () {
            const { scrollTop, scrollHeight, clientHeight } = targetEl
            let disabled = el.getAttribute('load-more-disabled')
            disabled = vnode[disabled] || disabled
            if (scrollHeight <= scrollTop + clientHeight + distance) {
              if (disabled) return
              func && func()
            }
          }, delay)
          targetEl.addEventListener('scroll', binding.handler)
        } else {
          binding.handler = helper.debounce(function () {
            const { scrollTop, scrollHeight, clientHeight } = el
            if (scrollHeight === scrollTop + clientHeight) {
              binding.value && binding.value()
            }
          }, 200)
          el.addEventListener('scroll', binding.handler)
        }
      },

      unbind(el, binding) {
        let { arg } = binding
        // 使用更丰富的功能，支持父组件的指令作用在指定的子组件上
        if (arg === 'expand') {
          /**
           * target 目标DOM节点的类名
           * offset 触发加载的距离阈值，单位为px
           * method 触发的方法
           * delay 防抖时延，单位为ms
           */
          const { target } = binding.value
          if (typeof target !== 'string') return
          let targetEl = el.querySelector(target)
          targetEl && targetEl.removeEventListener('scroll', binding.handler)
          targetEl = null
        } else {
          el.removeEventListener('scroll', binding.handler)
          el = null
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.tableInfo {
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: rgba(98, 102, 108, 1);
}
</style>
