<template>
  <div class="container">
    <div class="filterTab">
      <div>
        <el-input
          v-model="formInline.keyword"
          @keyup.enter.native="checkFuncList"
          clearable
          placeholder="请输入查询条件"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <span v-if="user.role != 3" @click="highCheckdialog = true" style="width: 80px"
          ><img
            src="../../assets/images/filter.png"
            alt=""
            style="width: 16px; vertical-align: middle; margin-right: 3px"
          />高级筛选</span
        >
      </div>
      <div v-if="user.role != 3">
        <el-checkbox class="checkboxAll" v-model="checkedAll" @change="checkAllChange"
          >选择全部</el-checkbox
        >
        <el-button class="normalBtnRe" type="primary" @click="removeMore">删除</el-button>
        <el-button class="normalBtnRe" type="primary" @click="exportList">导出</el-button>
      </div>
    </div>
    <div class="tableWrap">
      <el-table
        border
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
        @selection-change="handleSelectionChange"
        @cell-mouse-enter="showTooltip"
        @cell-mouse-leave="hiddenTooltip"
        ref="eltable"
        height="100%"
        style="width: 100%"
      >
        <el-table-column
          type="selection"
          align="center"
          :reserve-selection="true"
          :selectable="handleSelectable"
          width="55"
        >
        </el-table-column>
        <el-table-column align="center" label="推荐理由">
          <el-table-column
            v-for="item in tableHeaderClue"
            :key="item.id"
            :prop="item.name"
            align="left"
            min-width="120"
            :label="item.label"
          >
            <template slot-scope="scope">
              <p v-if="item.name == 'clue_company_name'">
                {{ getCompany(scope.row[item.name]) }}
              </p>
              <div v-else-if="item.name == 'logo'">
                <!-- @click="imgClick(pic.includes('http') ? pic : showSrcIp + pic)" width="30" height="30"  -->
                <img
                  :key="picindex"
                  v-for="(pic, picindex) in getLogo(scope.row[item.name])"
                  :src="pic.includes('http') ? pic : showSrcIp + pic"
                  alt=""
                />
              </div>
              <span
                class="clickPointer"
                v-if="item.name == 'title'"
                @click="noteBlackList(scope.row.title)"
              >
                {{ scope.row.title }}
              </span>
              <span v-else>{{ getTableItem(scope.row[item.name]) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          v-for="item in tableHeader"
          :key="item.id"
          :prop="item.name"
          align="left"
          min-width="120"
          :label="item.label"
        >
          <template slot-scope="scope">
            <span v-if="item.name == 'status'">{{ getStatus(scope.row[item.name]) }}</span>
            <span v-if="item.name != 'status'">{{
              scope.row[item.name] ? scope.row[item.name] : '-'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="user.role != 3" fixed="right" label="操作" align="left" width="80">
          <template slot-scope="scope">
            <el-button
              :disabled="scope.row['status'] == '2' ? false : true"
              type="text"
              size="small"
              @click="viewResult(scope.row)"
              >查看结果</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="pageSizeArr"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
    >
    </el-pagination>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogVisibleImg"
      width="80%"
    >
      <div class="dialog-body">
        <img :src="dialogImageUrl" alt="" />
      </div>
    </el-dialog>
    <highCheckDrawer
      :highCheckdialog="highCheckdialog"
      :selectArr="cluesList"
      :formInline="formInline"
      @highCheck="highCheck"
      @highIsClose="highIsClose"
    />
    <noteBlack
      :selectTitle="currentTitle"
      :visible="noteDialogVisible"
      ref=""
      @close="noteDialogVisible = false"
    />
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import highCheckDrawer from '../home_set/highCheck.vue'
import { mapGetters, mapState } from 'vuex'
import noteBlack from '@/components/assets/noteBlack.vue'
import {
  delRecommendRecords,
  exportRecommendRecords,
  recommendRecords,
  recommendClues
} from '@/api/apiConfig/recommend.js'

export default {
  components: { tableTooltip, highCheckDrawer, noteBlack },
  props: ['group_id'],
  data() {
    return {
      currentTitle: '',
      noteDialogVisible: false,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      loading: false,
      taskNameArr: [],
      clueCheckedArr: [],
      dialogImageUrl: '', // 图片放大的地址
      dialogVisibleImg: false,
      showMore: true,
      highCheckdialog: false,
      viewResultIsShow: false,
      checkedCities: ['上海', '北京'],
      isIndeterminate: true,
      cluesList: [], // 线索数据
      formInline: {
        keyword: '',
        task_name: '',
        clue_company_name: '',
        domain: '',
        cert: '',
        icp: '',
        title: '',
        subdomain: '',
        count: '',
        status: '',
        created_at: ''
      },
      ruleForm: {
        type: []
      },
      checkedAll: false,
      clueCheckedAll: false,
      tableData: [],
      checkedArr: [],
      currentPage: 1,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      tableHeaderClue: [
        {
          label: '任务名称',
          name: 'task_name'
        },
        {
          label: '企业名称',
          name: 'clue_company_name'
        },
        {
          label: '根域',
          name: 'domain'
        },
        {
          label: '证书',
          name: 'cert'
        },
        {
          label: 'ICP',
          name: 'icp'
        },
        {
          label: 'ICON',
          name: 'logo'
        },
        {
          label: '网站标题',
          name: 'title'
        },
        {
          label: '子域名',
          name: 'subdomain'
        }
      ],
      tableHeader: [
        {
          label: '推荐资产数量',
          name: 'count'
        },
        {
          label: '状态',
          name: 'status'
        },
        {
          label: '操作时间',
          name: 'created_at'
        }
      ],
      user: {
        role: ''
      }
    }
  },
  watch: {
    getterCompanyChange(val) {
      this.$router.go(-1) // 安服账号切换企业需要回到线索分组一级页面
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    },
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      if (val) {
        // 切换账号去除全选
        this.checkedAll = false
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, false)
        })
        this.getRecommendCluesData()
        this.getRecommendRecordsList()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },

  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getRecommendCluesData()
      this.getRecommendRecordsList()
    } else {
      this.getRecommendCluesData()
      this.getRecommendRecordsList()
    }
  },

  methods: {
    noteBlackList(title) {
      this.noteDialogVisible = true
      this.currentTitle = title
    },
    checkFuncList() {
      this.currentPage = 1
      this.getRecommendRecordsList()
    },
    getTableItem(item) {
      if (Array.isArray(item)) {
        if (item.length == '0') {
          return '-'
        }
      }
      if (item) {
        if (Array.isArray(item)) {
          return item.join(',')
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    getReason(item) {
      // 推荐理由默认展示两个，鼠标移入显示更多
      if (item) {
        let str = item.split(/;|；|\s+/).splice(0, 2)
        if (str.length > 2) {
          return str.join(';') + '…'
        } else {
          return str.join(';')
        }
      } else {
        return '-'
      }
    },
    getCompany(item) {
      if (Array.isArray(item)) {
        if (item.length == '0') {
          return '-'
        }
      }
      if (item) {
        if (Array.isArray(item)) {
          return item.join(',')
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    imgClick(url) {
      this.dialogImageUrl = url
      this.dialogVisibleImg = true
    },
    getStatus(item) {
      let str = ''
      switch (item) {
        case 0:
          str = '待处理'
          break
        case 1:
          str = '处理中'
          break
        case 2:
          str = '处理完成'
          break
        case 3:
          str = '执行失败'
          break
        default:
          str = '-'
      }
      return str
    },
    getLogo(row) {
      if (row && row.length) {
        return row.map((item) => {
          return item.content
        })
      } else {
        return ''
      }
    },
    async getRecommendCluesData() {
      // 线索
      let obj = {
        group_id: this.group_id,
        data: {
          confirm: '', // 下发状态:0/1 待下发/已下发
          operate_company_id: this.currentCompany
        }
      }
      let res = await recommendClues(obj).catch(() => {
        this.cluesList = []
      })
      if (res.data && res.data.logo) {
        res.data.logo.forEach((item, index) => {
          item.id = index + 1
        })
      }
      this.cluesList = res.data
    },
    async getRecommendRecordsList() {
      let obj = {
        group_id: this.group_id,
        data: {
          page: this.currentPage,
          per_page: this.pageSize,
          audit: [], // 审核状态:0/1/2 待审核/已通过/已忽略
          keyword: this.formInline.keyword, // 模糊搜索,
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      }
      this.loading = true
      let res = await recommendRecords(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.tableData = res.data.items
      this.total = res.data.total
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
    },
    highIsClose() {
      this.highCheckdialog = false
    },
    highCheck() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.getRecommendRecordsList()
    },
    viewResult(row) {
      this.$router.push({
        path: '/scanReg',
        query: {
          flag: row.flag
        }
      })
    },
    showMoreFun() {
      this.showMore = !this.showMore
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    clueHandleSelectionChange(val) {
      this.clueCheckedArr = val
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getRecommendRecordsList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getRecommendRecordsList()
    },
    async exportList() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      let obj = {
        group_id: this.group_id,
        operate_company_id: this.currentCompany,
        id: this.checkedArr.map((item) => {
          return item._id
        }),
        whole: this.checkedAll ? 1 : ''
      }
      let res = await exportRecommendRecords(obj)
      if (res.code == 0) {
        this.download(this.showSrcIp + res.data.url)
      }
    },
    async removeMore() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            group_id: this.group_id,
            data: {
              operate_company_id: this.currentCompany,
              id: this.checkedArr.map((item) => {
                return item._id
              }),
              whole: this.checkedAll ? 1 : ''
            }
          }
          let res = await delRecommendRecords(obj)
          if (res.code == 0) {
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getRecommendRecordsList()
            this.$message.success('删除成功！')
            this.$refs.eltable.clearSelection()
          }
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  .dialog-body {
    & > .el-form {
      width: 100%;
    }
    img {
      display: block;
      margin: 0 auto;
      margin-bottom: 30px;
    }
    .el-form-item {
      margin-bottom: 20px !important;
    }
  }
  .filterTab {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    & > div {
      .el-input {
        width: 240px;
        margin-right: 0;
      }
      & > span {
        font-weight: 400;
        color: #2677ff;
        line-height: 20px;
        margin-left: 16px;
        cursor: pointer;
      }
    }
  }
  & > .el-form {
    position: relative;
    // height: 244px;
    overflow: hidden;
    background: #eff2f7;
    border-radius: 4px;
    margin: 0 20px 28px;
    padding: 20px;
    .divider {
      position: absolute;
      bottom: 12px;
      width: 100%;
      text-align: center;
      color: #2677ff;
      font-size: 14px;
      i {
        margin-left: 4px;
      }
    }
    .el-form-item {
      margin-bottom: 0px !important;
    }
    .el-form-item__content {
      & > div {
        display: flex;
      }
    }
    .el-checkbox-group {
      margin-left: 20px;
    }
  }
  .tableWrap {
    height: calc(100% - 128px);
  }
  .el-table {
    width: 99%;
    border: 0;
    .companyClass {
      padding: 0 2px;
      height: 40px;
      line-height: 40px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .detail {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
