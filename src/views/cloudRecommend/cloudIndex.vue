<template>
  <div class="container">
    <div class="headerTitle">云端推荐任务</div>
    <div class="home_header">
      <div class="tabsWrap">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="线索策略" name="first"> </el-tab-pane>
          <el-tab-pane label="推荐任务" name="second"> </el-tab-pane>
          <el-tab-pane label="结果列表" name="third"> </el-tab-pane>
        </el-tabs>
      </div>
      <div class="tab_content">
        <clueManage v-if="activeName == 'first'" />
        <recommendIndex v-if="activeName == 'second'" />
        <ipAssets v-if="activeName == 'third'" />
      </div>
    </div>
  </div>
</template>

<script>
import recommendIndex from './recommendIndex.vue'
import clueManage from './clueManage.vue'
import ipAssets from '../assetsView/ipAssets.vue'

export default {
  components: { recommendIndex, clueManage, ipAssets },

  data() {
    return {
      activeName: 'first'
    }
  },
  computed: {},
  watch: {
    $route(val) {}
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 从详情等页面回来不清空搜索条件，其他的都要清掉
      if (from.query.flag) {
        // 从推荐记录详情返回的，定位在推荐记录页面
        vm.activeName = 'second'
      } else {
        vm.activeName = 'first'
      }
    })
  },
  created() {
    if (this.activeName == 'first') {
      this.headerTitle = '线索策略'
    } else if (this.activeName == 'second') {
      this.headerTitle = '推荐任务'
    } else {
      this.headerTitle = '结果列表'
    }
  },
  async mounted() {
    if (sessionStorage.getItem('activeTabName')) {
      this.activeName = sessionStorage.getItem('activeTabName')
    }
  },
  beforeDestroy() {
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    handleClick() {
      if (this.activeName == 'first') {
        this.headerTitle = '线索策略'
      } else if (this.activeName == 'second') {
        this.headerTitle = '推荐任务'
      } else {
        this.headerTitle = '结果列表'
      }
      sessionStorage.setItem('activeTabName', this.activeName)
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  // background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    & > .tabsWrap {
      .el-tabs__nav {
        padding-left: 20px;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        // min-width: 60px;
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        // padding: 0;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
      }
    }
    .tab_content {
      height: calc(100% - 44px);
    }
  }
}
</style>
