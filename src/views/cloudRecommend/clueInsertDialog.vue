<template>
  <!-- 新建 -->
  <el-dialog
    class="elDialogAdd"
    :close-on-click-modal="false"
    :visible.sync="dialogFormVisibleInsert"
    :before-close="beforeClose"
    width="45%"
  >
    <template slot="title"> 新建 </template>
    <div class="dialog-body">
      <el-form
        :model="contentForm"
        :rules="rules"
        style="padding: 0 !important"
        ref="ruleForms"
        label-width="80px"
        class="demo-ruleForm"
      >
        <!-- 企业线索库，单位测绘，钓鱼仿冒发现 -->
        <el-form-item v-if="is_detect || is_fake" label="添加方式" prop="way">
          <el-select v-model="ruleForm.way" @change="wayChange" placeholder="请选择方式">
            <el-option label="手动输入" :value="0"></el-option>
            <el-option label="从文件导入" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <div v-if="ruleForm.way == 0">
          <!-- v-if="!is_fake" 钓鱼仿冒只有域名，icon,关键词 -->
          <el-form-item v-if="!is_fake" class="clueItem" label="IP段" prop="content6">
            <el-input
              type="textarea"
              :rows="2"
              v-model="contentForm.content6"
              placeholder="请输入IP段，多个值请用分号或换行分隔，例如：**********或者**********/24，每条线索不能超过200字符"
            ></el-input>
          </el-form-item>
          <el-form-item class="clueItem" label="根域" prop="content0">
            <el-input
              type="textarea"
              :rows="2"
              v-model="contentForm.content0"
              placeholder="请输入根域，多个值请用分号或换行分隔，例如：fofa.info，每条线索不能超过200字符"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="!is_fake" class="clueItem" label="证书" prop="content1">
            <el-input
              type="textarea"
              :rows="2"
              v-model="contentForm.content1"
              placeholder='请输入证书，多个值请用分号或换行分隔，例如：O="北京华顺信安科技有限公司"或者CN="fofa.info"，每条线索不能超过200字符'
            ></el-input>
          </el-form-item>
          <el-form-item v-if="!is_fake" class="clueItem" label="ICP" prop="content2">
            <el-input
              type="textarea"
              :rows="2"
              v-model="contentForm.content2"
              placeholder="请输入ICP，多个值请用分号或换行分隔，例如：京ICP备18024709号 或者 京ICP备18024709号-3，每条线索不能超过200字符"
            ></el-input>
          </el-form-item>
          <!-- 子域名添加有权限控制 -->
          <el-form-item
            v-if="has_subdomain_tab && !is_fake"
            class="clueItem"
            label="子域名"
            prop="content5"
          >
            <el-input
              type="textarea"
              :rows="2"
              v-model="contentForm.content5"
              placeholder="请输入子域名，多个值请用分号或换行分隔，例如：foradar.baimaohui.net，每条线索不能超过200字符"
            ></el-input>
          </el-form-item>
          <el-form-item
            style="margin-top: -15px !important"
            v-if="has_subdomain_tab && !is_fake"
            class="clueItem"
            label=""
            prop="content5_companyname"
          >
            <el-input
              style="margin-top: 10px"
              v-model="content5_companyname"
              placeholder="请输入子域名企业名称"
            ></el-input>
          </el-form-item>
          <!-- is_detect有值代表单位资产测绘第二步新增，不需要关键词 -->
          <el-form-item v-if="!is_detect" class="clueItem" label="关键词" prop="way">
            <el-input
              type="textarea"
              :rows="2"
              v-model="contentForm.content4"
              placeholder="请输入关键词，多个值请用分号或换行分隔，例如：北京华顺信安科技有限公司，每条线索不能超过200字符"
            ></el-input>
          </el-form-item>
          <el-form-item class="clueItem" v-if="!isNotGroup" label="ICON" prop="way">
            <el-upload
              class="upload-demo"
              drag
              :action="uploadSrcIp + '/assets/account/files'"
              :headers="uploadHeaders"
              :before-upload="beforeUpload"
              :on-success="iconUploadSucc"
              :on-remove="iconUploadRemove"
              :on-error="uploadErr"
              list-type="picture"
              accept=".png,.ico,.bmp,.jpg,.jpeg"
              :file-list="fileListIcon"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将ICON拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip"
                >支持上传.png,.ico,.bmp,.jpg,.jpeg文件，且大小不超过3M</div
              >
            </el-upload>
          </el-form-item>
        </div>
        <el-form-item v-if="ruleForm.way == 1" label="线索内容" prop="contentForm">
          <p class="downloadClass" @click="downloadcluesExcel">
            <i class="el-icon-warning"></i>请点击下载
            <span>线索模板</span>
          </p>
          <el-upload
            class="upload-demo"
            drag
            :action="uploadSrcIp + '/assets/account/files'"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="uploadSucc"
            :on-remove="uploadRemove"
            :on-error="uploadErr"
            :limit="uploadMaxCount"
            :on-exceed="handleExceed"
            list-type="text"
            accept=".xlsx"
            :file-list="fileList"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">支持上传.xlsx文件，且大小不超过3M</div>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="$emit('insertClueSave', 'isClose')" id="cloud_cancel"
        >关闭</el-button
      >
      <el-button
        class="highBtn"
        :loading="btnLoading"
        @click="
          () => {
            taskType == 'organization' ? insertOrgSave() : insertSave()
          }
        "
        id="cloud_sure"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
import tuijianLog from './assetsScan.vue'
import clueManage from './clueManage.vue'
import { mapState } from 'vuex'
import { orgTaskAddClue } from '@/api/apiConfig/api.js'
import { insertCluesV1 } from '@/api/apiConfig/clue.js'

export default {
  components: { tuijianLog, clueManage },
  props: [
    'taskType',
    'taskId',
    'is_detect',
    'isNotGroup',
    'is_fake',
    'company_name',
    'dialogFormVisibleInsert',
    'ruleForm',
    'tabActiveName',
    'currentGroupId',
    'has_subdomain_tab'
  ],
  data() {
    return {
      contentForm: {
        content0: '',
        content1: '',
        content2: '',
        content3: [],
        content4: '',
        content5: '',
        content6: ''
      },
      content5_companyname: '',
      rules: {
        content0: [],
        content5: []
      },
      btnLoading: false,
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      fileListIcon: []
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange'])
  },
  watch: {
    ruleForm(val) {
      this.fileList = []
      this.fileListIcon = []
      this.content5_companyname = ''
      this.contentForm = {
        content0: '',
        content1: '',
        content2: '',
        content3: [],
        content4: '',
        content5: '',
        content6: ''
      }
    }
  },
  methods: {
    beforeClose() {
      this.$emit('insertClueSave', 'isClose')
    },
    downloadcluesExcel() {
      if (this.is_fake) {
        window.location.href = '/downloadTemplate/钓鱼仿冒线索模板.xlsx'
      } else {
        window.location.href = '/downloadTemplate/批量导入线索模板.xlsx'
      }
    },
    wayChange(val) {
      this.ruleForm.content = val == 1 ? [] : ''
    },
    iconUploadSucc(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        let obj = response.data
        this.contentForm.content3.push(obj.url)
      } else {
        this.$message.error(file.response.message)
      }
    },
    iconUploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data.url
      })
      if (res.length == 0) {
        this.contentForm.content3 = ''
      } else {
        this.contentForm.content3 = res
      }
    },
    // 线索
    uploadSucc(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        let obj = response.data
        this.ruleForm.file = obj.url
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadErr(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.ruleForm.content = []
      } else {
        this.ruleForm.content = res[0]
      }
    },
    beforeUpload(file, limit = 3) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    },
    async insertOrgSave() {
      let obj = null
      obj = {
        group_id: this.currentGroupId,
        company_name: this.company_name,
        operate_company_id: this.currentCompany,
        organization_task_id: this.taskId
      }
      if (this.ruleForm.way == 0) {
        // 手动输入
        let val = Object.values(this.contentForm).join('')
        if (!val) {
          this.$message.error('请至少填写一种线索')
          return
        }
        let clueData = []
        for (let i = 0; i <= 6; i++) {
          if (
            (i == 3 && this.contentForm['content' + i].length > 0) ||
            (i != 3 && this.contentForm['content' + i])
          ) {
            clueData.push({
              type: i,
              content:
                i != 3
                  ? this.contentForm['content' + i]
                      .split(/[；|;|\r\n]/)
                      .filter((item) => {
                        return item.trim()
                      })
                      .map((item) => {
                        return item.trim()
                      })
                  : this.contentForm['content' + i],
              clue_company_name: i == 5 ? this.content5_companyname : '' // 子域名支持添加企业名称
            })
          }
        }
        obj.data = clueData
        obj.file = ''
      } else {
        if (!this.ruleForm.file) {
          this.$message.error('请上传文件')
          return
        }
        obj.data = []
        obj.file = this.ruleForm.file
      }
      this.btnLoading = true
      let res = await orgTaskAddClue(obj).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.btnLoading = false
        this.$message.success('操作成功！')
        this.$emit('insertClueSave')
      }
    },

    async insertSave() {
      let obj = null
      let set_status = ''
      // 单位资产测绘/钓鱼仿冒的新增需要此字段：初始线索成功后(this.is_detect.status == 0)、生成线索总表后(this.is_detect.status == 5)传值1已确认，其余情况0待确认
      let is_task = ''
      if (this.is_detect) {
        // 单位资产测绘
        set_status = this.is_detect.status == 0 || this.is_detect.status == 5 ? 1 : 0
        is_task = 1
      } else if (this.is_fake) {
        // 钓鱼仿冒
        set_status = this.is_fake.status == 0 || this.is_fake.status == 4 ? 1 : 0
        is_task = 2
      } else {
        // 线索库新建，默认1
        set_status = 1
        is_task = 0
      }
      if (this.ruleForm.way == 0) {
        // 手动输入
        let val = Object.values(this.contentForm).join('')
        if (!val) {
          this.$message.error('请至少填写一种线索')
          return
        }
        let clueData = []
        for (let i = 0; i <= 6; i++) {
          if (
            (i == 3 && this.contentForm['content' + i].length > 0) ||
            (i != 3 && this.contentForm['content' + i])
          ) {
            clueData.push({
              type: i,
              content:
                i != 3
                  ? this.contentForm['content' + i]
                      .split(/[；|;|\r\n]/)
                      .filter((item) => {
                        return item.trim()
                      })
                      .map((item) => {
                        return item.trim()
                      })
                  : this.contentForm['content' + i],
              clue_company_name: i == 5 ? this.content5_companyname : '' // 子域名支持添加企业名称
            })
          }
        }
        obj = {
          group_id: this.currentGroupId,
          data: clueData,
          is_auto_expend: this.ruleForm.is_auto_expend ? 1 : 0,
          file: '',
          set_status: set_status,
          is_task: is_task,
          company_name: this.company_name,
          operate_company_id: this.currentCompany
        }
      } else {
        // 模板上传
        obj = {
          group_id: this.currentGroupId,
          data: [],
          is_auto_expend: this.ruleForm.is_auto_expend ? 1 : 0, // 是否自动扩展
          file: this.ruleForm.file,
          set_status: set_status,
          is_task: is_task,
          company_name: this.company_name,
          is_fake_assets_task: this.is_fake ? 1 : '', // 钓鱼仿冒模板不同
          operate_company_id: this.currentCompany
        }
      }
      this.$refs.ruleForms.validate(async (valid) => {
        if (valid) {
          this.btnLoading = true
          let res = await insertCluesV1(obj).catch(() => {
            this.btnLoading = false
          })
          if (res.code == 0) {
            this.btnLoading = false
            if (this.is_detect || this.is_fake) {
              // 单位测绘、钓鱼仿冒
              this.$message.success('操作成功！')
            } else {
              // 线索库
              this.$message.success('线索成功添加至已确认列表！')
            }
            this.$emit('insertClueSave')
          }
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.el-upload-list {
  max-height: 250px;
  min-height: 45px;
  overflow-y: auto;
}
.elDialogAdd .el-dialog .el-form .el-form-item {
  margin-bottom: 24px !important;
}
</style>
