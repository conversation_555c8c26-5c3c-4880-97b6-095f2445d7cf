<template>
  <div class="container">
    <div class="groupClass">
      <p style="display: flex; justify-content: space-between">
        <span style="color: #37393c; font-size: 14px">场景</span>
        <i class="iconClass el-icon-plus" @click="insertShow" id="cloud_clue_add">新增</i>
      </p>
      <el-collapse
        v-model="currentGroupIds"
        accordion
        style="padding: 0px 8px; border: 0; height: calc(100% - 51px); overflow: auto"
        v-loading="groupLoading"
      >
        <el-collapse-item :name="String(item.id)" v-for="(item, index) in groupArr" :key="item.id">
          <div
            slot="title"
            class="nameClass"
            @mouseenter="enterTitle(index)"
            @mouseleave="leaveTitle(index)"
          >
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              :content="`${item.name}(${item.num})`"
              :disabled="item.name.length <= 5"
              :open-delay="500"
            >
              <span>{{ `${item.name}(${item.num})` }}</span>
            </el-tooltip>
            <el-dropdown
              trigger="click"
              class="dropdownMore"
              @click.native="dropdownClick"
              :placement="'bottom'"
            >
              <span class="el-dropdown-link">
                <i class="el-icon-more"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  icon="el-icon-edit-outline"
                  @click.native="exportMore(item)"
                  id="cloud_clue_edit"
                  >重命名</el-dropdown-item
                >
                <el-dropdown-item
                  icon="el-icon-delete"
                  @click.native="groupRemove(item.id)"
                  id="cloud_clue_del"
                  v-if="index > 0"
                  >删除</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div
            v-for="(val, ind) in item.tab_num"
            :key="ind"
            class="collapseType"
            @click="groupClick(item.id, item.is_detect_assets_tasks, val.value, index)"
          >
            <div
              :style="item.isHighlighted && val.value == tabActiveNameStatus ? 'color:#2677FF' : ''"
            >
              <span
                :class="
                  item.isHighlighted && val.value == tabActiveNameStatus
                    ? 'fangkuai fangkuaiblue'
                    : 'fangkuai'
                "
              ></span>
              {{ val.label }}
            </div>
            <div
              class="collapseCount"
              :style="item.isHighlighted && val.value == tabActiveNameStatus ? 'color:#2677FF' : ''"
              >({{ val.count }})</div
            >
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="clueWrap">
      <div class="filterTab top">
        <div style="flex-grow: 1; margin-right: 10px">
          <el-select v-model="ruleForm.clueType" placeholder="请选择" id="cloud_select">
            <el-option
              v-for="item in tabData.filter((to) => {
                return to.name != '3'
              })"
              :key="item.name"
              :label="item.label"
              :value="item.name"
            ></el-option>
          </el-select>
          <el-input
            placeholder="请输入线索内容进行扩展"
            v-model="ruleForm.clueContent"
            class="input-with-select"
            id="cloud_enter"
          >
          </el-input>
        </div>
        <div>
          <el-button
            icon="el-icon-caret-right"
            @click="batchAddClueFunOne"
            :loading="batchOneLoading"
            id="cloud_expand"
            >扩展</el-button
          >
          <i class="iconClass el-icon-zoom-in" @click="companyShow" id="cloud_company_inquire"
            >企业关系查询</i
          >
          <i
            style="margin-left: 16px"
            class="iconClass el-icon-plus"
            @click="insertClueShow"
            id="cloud_add"
            >新增</i
          >
          <i
            style="margin-left: 16px"
            class="iconClass el-icon-document"
            @click="expandLogClick"
            id="cloud_info"
            >扩展记录</i
          >
        </div>
      </div>
      <div v-if="toDoList" class="handle" style="margin: 10px 18px">
        <div class="bot">
          <span
            >基础线索: {{ lineClueNum }}<i style="margin: 0 10px"></i>开始时间：{{ lineStart_at
            }}<i style="margin: 0 10px"></i>任务耗时：{{ secondsFormat(lineUseSeconds) }}</span
          >
          <span class="progress">{{ lineProgress }}%</span>
        </div>
        <div class="progress">
          <el-progress
            :text-inside="true"
            :stroke-width="20"
            :percentage="parseFloat(lineProgress)"
          >
          </el-progress>
        </div>
        <div class="bot">
          <span>正在扩展线索…</span>
          <span class="delTask" @click="removeTask" id="cloud_expand_del"
            ><i class="el-icon-delete"></i>删除</span
          >
        </div>
      </div>
      <div style="display: inline-block; margin-top: 20px">
        <div class="confirmBox">
          <el-radio-group v-model="tabActiveName" @change="handleClick(tabActiveName)">
            <el-radio-button :label="item.name" v-for="(item, index) in tabData" :key="index"
              >{{ item.label }}({{ item.count }})</el-radio-button
            >
          </el-radio-group>
        </div>
      </div>
      <div class="filterTab">
        <div>
          <el-input
            v-if="tabActiveName != 3"
            v-model="keywordObj[tabKeywordArr[tabActiveName]]"
            :placeholder="`请输入${getLabel()}检索`"
            id="cloud_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="getClueData('tmp')"></el-button>
          </el-input>
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="cloud_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="exportList"
            :loading="exportLoading"
            id="cloud_export"
            >导出</el-button
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="removeMore"
            :loading="removeLoading"
            id="cloud_del"
            >删除</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            v-if="tabActiveNameStatus != 2"
            @click.native="clueConfirmSave(2)"
            id="cloud_ignore"
            >忽略</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            v-if="tabActiveNameStatus != 1"
            @click.native="clueConfirmSave(1)"
            id="cloud_confirm"
            >确认</el-button
          >
          <el-button
            v-if="tabActiveNameStatus == 1"
            class="normalBtn"
            type="primary"
            :loading="batchLoading"
            @click="batchAddClueFun"
            id="cloud_expands"
            >扩展线索</el-button
          >
          <el-button
            v-if="tabActiveNameStatus == 1"
            class="normalBtn"
            type="primary"
            :loading="recommentLoading"
            @click="goRecomment"
            id="cloud_recommend"
            >推荐资产</el-button
          >
          <el-button
            v-if="tabActiveNameStatus == 1 && tabActiveName == 4"
            class="normalBtn"
            type="primary"
            @click="goChain('more')"
            id="cloud_chain"
            >关联</el-button
          >
        </div>
      </div>
      <div
        :class="toDoList ? 'tableWrap' : 'tableWrapTwo'"
        v-loading="loading"
        style="padding: 0px 20px"
      >
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F5F7FA', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          :ref="'eltable' + tabActiveName"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            min-width="120"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot="header">
              <span v-if="item.name == 'content'">{{ getLabel() }}</span>
              <span v-else>{{ item.label }}</span>
            </template>
            <template slot-scope="scope">
              <span
                v-if="tabActiveName == '3' && item.name == 'content' && scope.row[item.name]"
                style="display: flex; align-items: center"
              >
                <el-tooltip
                  class="item"
                  effect="light"
                  placement="top"
                  :content="scope.row.is_expand == 1 ? '已扩展' : '未扩展'"
                >
                  <i
                    v-if="scope.row.status == 1"
                    :class="scope.row.is_expand == 1 ? 'greenStatus' : 'grayStatus'"
                  ></i>
                </el-tooltip>
                <el-tooltip class="item" effect="light" placement="top" :content="'新增'">
                  <i v-if="scope.row.status == 0 && isNew" :class="'redStatus'"></i>
                </el-tooltip>
                <el-image
                  class="imgwrap"
                  :src="
                    scope.row[item.name].includes('http')
                      ? scope.row[item.name]
                      : showSrcIp + scope.row[item.name]
                  "
                  alt=""
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </span>
              <span
                v-else-if="tabActiveName == '3' && item.name == 'content' && !scope.row[item.name]"
                >-</span
              >
              <span v-else>
                <span v-if="item.name == 'status'">{{ getStatus(scope.row[item.name]) }}</span>
                <!-- <span v-else-if="item.name == 'clue_company_name'"></span> -->
                <span v-else-if="item.name == 'content'">
                  <span v-if="scope.row[item.name] && scope.row[item.name].length > 0">
                    <el-tooltip
                      class="item"
                      effect="light"
                      placement="top"
                      :content="scope.row.is_expand == 1 ? '已扩展' : '未扩展'"
                    >
                      <i
                        v-if="scope.row.status == 1"
                        :class="scope.row.is_expand == 1 ? 'greenStatus' : 'grayStatus'"
                      ></i>
                    </el-tooltip>
                    <el-tooltip class="item" effect="light" placement="top" :content="'新增'">
                      <i v-if="scope.row.status == 0 && isNew" :class="'redStatus'"></i>
                    </el-tooltip>
                    <el-tooltip class="item" effect="light" placement="top">
                      <div slot="content">
                        {{ $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '')
                        }}{{
                          scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                        }}
                      </div>
                      <span>{{
                        $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '-')
                      }}</span>
                    </el-tooltip>
                  </span>
                </span>
                <span v-else-if="item.name == 'chain_list'">
                  <span v-if="scope.row[item.name] && scope.row[item.name].length > 0">
                    <el-tooltip
                      class="item"
                      effect="light"
                      placement="top"
                      popper-class="chainClass"
                    >
                      <div slot="content" style="position: relative">
                        <el-tooltip
                          effect="light"
                          class="item"
                          placement="top"
                          content="一键复制"
                          v-if="scope.row[item.name] && scope.row[item.name].length != 0"
                          :open-delay="500"
                        >
                          <i
                            class="el-icon-document-copy"
                            @click="copyClick(scope.row[item.name])"
                            style="
                              color: #2677ff;
                              cursor: pointer;
                              position: absolute;
                              right: -6px;
                              top: 0;
                            "
                          ></i>
                        </el-tooltip>
                        <span v-for="(con, index) in getChains(scope.row[item.name])" :key="index">
                          <span v-if="con.type && con.type == 3">
                            <el-image
                              :src="
                                con.content.includes('http') ? con.content : showSrcIp + con.content
                              "
                            >
                              <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i>
                              </div>
                            </el-image>
                          </span>
                          <span v-else
                            >{{ $punyCode.toUnicode(con.content || con)
                            }}{{ con.punycode_domain ? '(' + con.punycode_domain + ')' : '' }}</span
                          >
                          <i
                            v-if="index < getChains(scope.row[item.name]).length - 1"
                            class="el-icon-right iconRight"
                          ></i>
                        </span>
                      </div>
                      <span style="display: flex !important; align-items: center">
                        {{ scope.row[item.name].length - 1 }}
                        <img
                          src="../../assets/images/chain.svg"
                          alt=""
                          style="width: 12px; margin-left: 5px; vertical-align: middle"
                        />
                      </span>
                    </el-tooltip>
                  </span>
                  <span v-else>-</span>
                </span>
                <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="tabActiveNameStatus == 1 && tabActiveName == 4"
            fixed="right"
            label="操作"
            align="left"
            width="50"
          >
            <template slot-scope="scope">
              <el-button
                v-if="!scope.row.is_relate_keyword"
                type="text"
                size="small"
                @click="goChain('one', scope.row.content)"
                id="cloud_chain"
                >关联</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 线索分组名称 -->
    <el-dialog
      class="elDialogAddLiji"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleGroup"
      width="500px"
    >
      <template slot="title">
        <span v-if="addIsTrue">新建</span>
        <span v-if="!addIsTrue">编辑</span>
      </template>
      <div class="dialog-body" style="height: 100px">
        <el-form
          style="padding: 0 !important"
          :model="groupForm"
          :rules="groupFormrules"
          ref="groupForm"
          label-width="120px"
          class="demo-ruleForm"
        >
          <el-form-item label="场景名称" prop="name">
            <el-input v-model="groupForm.name" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleGroup = false" id="cloud_clue_cancel"
          >取消</el-button
        >
        <el-button
          class="highBtn"
          :loading="btnLoading"
          @click="exportMoreSave"
          id="cloud_clue_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 新建线索 -->
    <cluedialogs
      :dialogFormVisibleInsert="dialogFormVisibleInsert"
      :currentGroupId="currentGroupId"
      :tabActiveName="tabActiveName"
      :ruleForm="clueRuleForm"
      @insertClueSave="insertClueSave"
    />
    <!-- 企业信息 -->
    <conpanyRelation :dialogFormVisibleCompany="dialogFormVisibleCompany" @closeFun="closeFun" />
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="chainDialog"
      width="500px"
      title="提示"
    >
      <div class="dialog-body">
        <p class="infoKeyword">是否添加到数据泄漏关键词列表中?</p>
        <el-form
          :model="chainRuleForm"
          :rules="chainRules"
          ref="chainRuleForm"
          style="padding: 0 !important"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="关键词类型" prop="type" placeholder="请选择">
            <el-select v-model="chainRuleForm.type">
              <el-option
                v-for="item in typeArr"
                :label="item.name"
                :key="item.id"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="chainDialog = false">关闭</el-button>
        <el-button class="highBtn" :loading="tiquLoading" @click="chainSave('chainRuleForm')"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import $ from 'jquery'
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import conpanyRelation from './companyRelation.vue'
import tree from '../../components/tree/src/tree'
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import { addSensitiveKeyword } from '@/api/apiConfig/api.js'
import {
  passClueV1,
  insertCluesV1,
  editCluesGroupList,
  delCluesGroupList,
  addCluesGroupList,
  cluesGroupList,
  batchAddClue,
  exportCluesListV1,
  tabNumClues,
  delCluesListV1,
  cluesList
} from '@/api/apiConfig/clue.js'
import { recommend, delRunningClueJob, getRunningClueJob } from '@/api/apiConfig/recommend.js'

export default {
  components: {
    cluedialogs: () => import('./clueInsertDialog.vue'),
    tableTooltip,
    conpanyRelation,
    tree
  },
  data() {
    return {
      typeArr: [
        {
          name: '全部',
          id: 0
        },
        {
          name: '数字资产',
          id: 1
        },
        {
          name: '数据泄露',
          id: 2
        }
      ],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      tiquLoading: false,
      groupLoading: false,
      toDoList: null,
      lineProgress: 0,
      lineStart_at: '',
      lineClueNum: '',
      lineUseSeconds: '',
      btnLoading: false,
      groupDisabled: false,
      groupForm: {
        name: '',
        keyword: '',
        file: ''
      },
      groupFormrules: {
        name: [{ required: true, message: '请输入名称', trigger: 'change' }]
      },
      chainRules: {
        type: [{ required: true, message: '请选择关键词类型', trigger: 'change' }]
      },
      chainRuleForm: {
        type: ''
      },
      tabActiveNameStatus: '0',
      tabDataStatus: [
        {
          label: '待确认',
          value: '0',
          count: 0
        },
        {
          label: '已确认',
          value: '1',
          count: 0
        },
        {
          label: '已忽略',
          value: '2',
          count: 0
        }
      ],
      currentGroupId: '',
      currentGroupIds: '',
      editCurrentId: '',
      dialogFormVisibleCompany: false,
      dialogFormVisibleGroup: false,
      dialogFormVisibleInsert: false,
      addIsTrue: false,
      ruleForm: {
        clueType: '0',
        clueContent: ''
      },
      ruleFormCompany: {
        name: '',
        file: '',
        operate_company_id: ''
      },
      clueRuleForm: {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      },
      groupArr: [],
      checkedAll: false,
      loading: false,
      batchOneLoading: false,
      removeLoading: false,
      exportLoading: false,
      batchLoading: false,
      recommentLoading: false,
      formInline: {
        no_page: '', // 1 没有分页，不传值 有分页
        keyword: '',
        page: 1,
        per_page: 10,
        status: '', // 已确认线索
        group_id: '' // 安服角色分组id
      },
      tableData: [],
      currentPage: 1,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      tabActiveName: '0',
      tabNumStatus: 0, // 某状态(待确认)下标签数量总数
      keywordObj: {
        domain_keyword: '',
        cert_keyword: '',
        icp_keyword: '',
        key_keyword: '',
        ip_keyword: ''
      },
      tabData: [
        {
          name: '0',
          label: '域名',
          count: ''
        },
        {
          name: '1',
          label: '证书',
          count: ''
        },
        {
          name: '2',
          label: 'ICP',
          count: ''
        },
        {
          name: '3',
          label: 'ICON',
          count: ''
        },
        {
          name: '4',
          label: '关键词',
          count: ''
        },
        {
          name: '6',
          label: 'IP段',
          count: ''
        }
      ],
      tableHeader: [
        {
          label: '关键词',
          name: 'content'
        },
        {
          label: '企业名称',
          name: 'clue_company_name'
        },
        {
          label: '来源',
          name: 'source_label'
        },
        {
          label: '添加时间',
          name: 'created_at'
        },
        {
          label: '线索链',
          name: 'chain_list'
        }
      ],
      groupCurrentPage: 1,
      groupPageSize: 10,
      checkedArr0: [],
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      user: {
        role: ''
      },
      chainDialog: false,
      relname: '',
      isNew: false, //是否为新增线索
      activeName: '' //tab页（线索、推荐）
    }
  },
  watch: {
    getterCompanyChange(val) {
      if (this.user.role == 2) {
        this.$router.push('/assetsCloud') //重新跳转一下去除url传参
      }
    },
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    getterCurrentCompany(msg) {
      this.currentPage = 1
      this.currentGroupId = '' //切换企业时，清空一下
      this.isNew = false
      this.getTaskResultData()
    },
    activeName(val) {
      if (val) {
        this.currentGroupId = ''
        this.isNew = false
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange', 'getterWebsocketMessage'])
  },
  created() {
    this.activeName = sessionStorage.getItem('activeTabName')
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (this.user.role == 2) {
      // 安服
      if (this.currentCompany) {
        if (this.$route.query.group_id) {
          this.currentGroupId = String(this.$route.query.group_id)
          this.currentGroupIds = String(this.$route.query.group_id)
          this.isNew = true
          this.getTaskResultData()
        } else {
          this.isNew = false
          this.getTaskResultData()
        }
      }
    } else {
      if (this.$route.query.group_id) {
        this.currentGroupId = String(this.$route.query.group_id)
        this.currentGroupIds = String(this.$route.query.group_id)
        this.isNew = true
        this.getTaskResultData()
      } else {
        this.isNew = false
        this.getTaskResultData()
      }
    }
  },

  methods: {
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    // 企业关系开始
    companyShow() {
      this.companyName = ''
      this.dialogFormVisibleCompany = true
      this.companyInfo = false
      this.companyListVisible = false
    },
    closeFun() {
      this.dialogFormVisibleCompany = false
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (res.cmd == 'cloud_recommend_task_progress') {
        if (res.data.status == 2) {
          this.toDoList = null
          resetMessage.success('扩展完成！')
          this.currentId = '' // 控制推送结束后仅执行一次
          this.getTaskResultData()
        } else if (res.data.status == 1) {
          // 正在扫描
          this.toDoList = res.data
          if (!res.data) return
          this.currentId = res.data.task_id
          // 推送数据渲染到列表
          this.lineProgress = Number(res.data.progress)
          this.lineStart_at = res.data.start_time
          this.lineUseSeconds = res.data.use_seconds
          this.lineClueNum = res.data.total
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.toDoList = null
          this.getTaskResultData()
        }
      } else if (res.cmd == 'company_icp_info') {
        // websocket获取备案数量
        if (this.$refs.tree) {
          let nodes = this.$refs.tree.root.childNodes
          this.setTreeItemValue(nodes, res)
        }
      } else if (res.cmd == 'company_icp_info_end') {
        // this.loadingFun(1)
      }
    },
    // 获取当前正在执行任务
    async getRunningJob() {
      let res = await getRunningClueJob({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.toDoList = res.data
        if (!res.data) return
        this.currentId = res.data.task_id
        // 推送数据渲染到列表
        this.lineProgress = Number(res.data.progress)
        this.lineStart_at = res.data.start_time
        this.lineUseSeconds = res.data.use_seconds
        this.lineClueNum = res.data.total
      }
    },
    // 当前正在执行任务删除
    async removeTask() {
      this.$confirm('确定删除当前扩展任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await delRunningClueJob({
            operate_company_id: this.currentCompany,
            id: this.currentId
          })
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.getRunningJob()
          }
        })
        .catch(() => {})
    },
    // 关键词关联保存
    async chainSave(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (!valid) {
          return false
        }
        let obj = {
          is_clue_whole: this.checkedAll ? 1 : '', // 传1时下边的参数生效
          tab_status: this.tabActiveNameStatus,
          group_id: this.currentGroupId,
          type: this.chainRuleForm.type,
          name: this.relname,
          keyword: this.formInline.keyword,
          operate_company_id: this.currentCompany
        }
        this.tiquLoading = true
        let res = await addSensitiveKeyword(obj).catch(() => {
          this.tiquLoading = false
        })
        if (res.code == 0) {
          this.tiquLoading = false
          this.chainDialog = false
          this.$message.success('操作成功！')
          this.$refs['eltable' + this.tabActiveName].clearSelection()
          this.getClueData()
          this.getTabNum()
        }
      })
    },
    // 扩展记录
    async expandLogClick() {
      this.$router.push({
        path: '/expandLog',
        query: {
          group_id: this.currentGroupId
        }
      })
    },
    // 处置
    async clueConfirmSave(icon) {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      if (!this.checkedAll) {
        if (
          this.checkedArr0.length == 0 &&
          this.checkedArr1.length == 0 &&
          this.checkedArr2.length == 0 &&
          this.checkedArr3.length == 0 &&
          this.checkedArr4.length == 0 &&
          this.checkedArr5.length == 0 &&
          this.checkedArr6.length == 0
        ) {
          this.$message.error('请选择要操作的数据！')
          return
        }
      }
      let clueData = [
        {
          id: this.checkedAll
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        }
      ]
      let obj = {
        tab_status: this.tabActiveNameStatus,
        status: icon,
        group_id: this.currentGroupId,
        data: clueData,
        // keyword: this.formInline.keyword,
        keyword: {
          domain_keyword: '',
          cert_keyword: '',
          icp_keyword: '',
          key_keyword: '',
          ip_keyword: ''
        },
        operate_company_id: this.currentCompany
      }
      this.currentGroupIds = this.currentGroupId
      this.btnLoading = true
      let res = await passClueV1(obj).catch(() => {
        this.btnLoading = false
      })
      this.btnLoading = false
      if (res.code == 0) {
        if (icon == 1) {
          // 确认操作后提示是否下发推荐
          this.$confirm('操作成功！是否推荐资产?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(async () => {
              let objData = {
                tab_status: this.tabActiveNameStatus,
                group_id: this.currentGroupId,
                data: clueData,
                keyword: this.formInline.keyword,
                operate_company_id: this.currentCompany
              }
              let res = await recommend(objData)
              if (res.code == 0) {
                this.$message.success('操作成功！')
                this.$refs['eltable' + this.tabActiveName].clearSelection()
                this.getTaskResultData()
              }
            })
            .catch(() => {
              // 取消
              this.$refs['eltable' + this.tabActiveName].clearSelection()
              this.getTaskResultData()
            })
        } else {
          // 忽略
          this.$message.success('操作成功！')
          this.$refs['eltable' + this.tabActiveName].clearSelection()
          this.getTaskResultData()
        }
      }
    },
    async handleClickStatus(val) {
      this.currentPage = 1
      this.checkedAll = false
      this.$nextTick(() => {
        this.tableData.forEach((row) => {
          this.$refs['eltable' + this.tabActiveName].clearSelection()
        })
      })
      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []
      this.$forceUpdate()
      this.getClueData()
      this.getTabNum()
      // 切换的时候跟新待确认tab的数据
      let obj = {
        page: this.groupCurrentPage,
        per_page: 10,
        no_page: '1',
        operate_company_id: this.currentCompany
      }
      let res = await cluesGroupList(obj).catch(() => {
        this.groupLoading = false
      })
      let arr = res.data
      arr.forEach((item) => {
        item['num'] = item.collect ? item.collect : 0 // 所有线索数量
      })
      this.groupArr = arr
      if (!this.currentGroupId) {
        // 当前分组id不存在
        this.currentGroupId = this.groupArr.length > 0 ? this.groupArr[0].id : ''
      }
      if (this.currentGroupId) {
        this.total = res.data.total
      }
    },
    handleClick(data) {
      //icp和证书时要有企业名称
      if (data == 0 || data == 1 || data == 2 || data == 6) {
        this.tableHeader = [
          {
            label: '关键词',
            name: 'content'
          },
          {
            label: '企业名称',
            name: 'clue_company_name'
          },
          {
            label: '来源',
            name: 'source_label'
          },
          {
            label: '添加时间',
            name: 'created_at'
          },
          {
            label: '线索链',
            name: 'chain_list'
          }
        ]
      } else {
        this.tableHeader = [
          {
            label: '关键词',
            name: 'content'
          },
          {
            label: '来源',
            name: 'source_label'
          },
          {
            label: '添加时间',
            name: 'created_at'
          },
          {
            label: '线索链',
            name: 'chain_list'
          }
        ]
      }
      this.currentPage = 1
      this.getClueData()
    },
    getStatus(status) {
      // 忽略:2,确认:1,待确认：0
      let str = ''
      switch (status) {
        case 0:
          str = '待确认'
          break
        case 1:
          str = '已确认'
          break
        case 2:
          str = '忽略'
          break
      }
      return str
    },
    getChains(row) {
      let arr = row.filter((item) => {
        return item.content
      })
      return arr
    },
    groupClick(id, is_detect_assets_tasks, value, index) {
      this.currentGroupId = String(id)
      this.currentGroupIds = String(id)
      this.tabActiveNameStatus = value
      this.tableData = []
      this.groupArr.forEach((item) => {
        item.isHighlighted = false
      })
      this.groupArr[index].isHighlighted = true
      if (is_detect_assets_tasks) {
        this.isNew = true
      } else {
        this.isNew = false
      }
      this.checkedAll = false
      this.$nextTick(() => {
        this.$refs['eltable' + this.tabActiveName].clearSelection()
      })

      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []
      this.getRunningJob()
      this.getClueData()
      this.getTabNum()
    },
    insertShow() {
      this.groupForm.name = ''
      this.addIsTrue = true
      this.dialogFormVisibleGroup = true
      this.groupForm.name = ''
    },
    insertClueShow() {
      this.clueRuleForm = {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: ''
      }
      this.dialogFormVisibleInsert = true
      if (this.tabActiveName == 3) {
        this.clueRuleForm.content = []
      } else {
        this.clueRuleForm.content = ''
      }
      if (this.tabActiveName == '3') {
        this.$set(this.clueRuleForm, 'way', 1)
      } else {
        this.$set(this.clueRuleForm, 'way', 0)
      }
      this.fileList = []
    },
    async getClueData(tmp, flag) {
      // 忽略:2,确认:1,待确认：0
      if (!flag) {
        //调用之前清空选择项
        this.checkedAll = false
        if (this.$refs['eltable' + this.tabActiveName] != undefined) {
          this.$refs['eltable' + this.tabActiveName].clearSelection()
        }
      }
      // 搜索page为1
      if (tmp == 'tmp') {
        this.currentPage = 1
        this.formInline.page = 1
      } else {
        this.formInline.page = this.currentPage
      }
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.group_id = this.currentGroupId // 分组id
      this.formInline.no_page = ''
      this.formInline.status = this.tabActiveNameStatus
      let obj = {
        type: this.tabActiveName,
        query: this.formInline,
        operate_company_id: this.currentCompany
      }
      this.loading = true
      let res = await cluesList(obj).catch(() => {
        this.loading = false
      })
      this.loading = false
      let arr = res.data && res.data.items ? res.data.items : []
      let arrayNew = []
      arr.map((item) => {
        arrayNew.push(Object.assign({}, item, { type: this.tabActiveName }))
      })
      this.tableData = arrayNew
      this.total = res.data && res.data.total ? res.data.total : 0
      // 全选操作
      this.$nextTick(() => {
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs['eltable' + this.tabActiveName].toggleRowSelection(row, true)
          })
        } else {
          if (this.$refs['eltable' + this.tabActiveName]) {
            if (this.$refs['eltable' + this.tabActiveName].selection.length > 0) {
              this.$refs['eltable' + this.tabActiveName].selection.forEach((pre) => {
                this.tableData.forEach((row) => {
                  this.$refs['eltable' + this.tabActiveName].toggleRowSelection(pre, true)
                })
              })
            } else {
              this.$refs['eltable' + this.tabActiveName].clearSelection()
            }
          }
        }
      })
    },
    copyClick(tmp) {
      let data = []
      data.push(tmp)
      let text = ''
      data.map((item) => {
        this.getChains(item).map((v, i) => {
          if (v.type && v.type == 3) {
            if (i == item.length - 1 && v) {
              text += 'icon' + '\n'
            } else {
              text += 'icon' + '-->'
            }
          } else {
            if (i < item.length - 1 && v) {
              text += v.content + '-->'
            } else if (i == item.length - 1 && v) {
              text += v.content + '\n'
            } else {
              text += v.content
            }
          }
        })
      })
      this.$copyText(text).then(
        (res) => {
          console.log('复制成功：', res)
          this.$message.success('复制成功')
        },
        (err) => {
          console.log('', err)
          this.$message.error('复制失败')
        }
      )
    },
    async getTabNum() {
      let obj = {
        group_id: this.currentGroupId,
        data: {
          status: this.tabActiveNameStatus,
          operate_company_id: this.currentCompany
        }
      }
      let res = await tabNumClues(obj)
      this.tabNumStatus = 0 // 获取总数，用于删除、导出等操作无数据提示
      if (res.data.clues_count) {
        this.tabData.forEach((item) => {
          res.data.clues_count.forEach((ch) => {
            if (item['name'] == ch['type']) {
              this.tabNumStatus += ch.count
              item.count = ch.count
            }
          })
        })
      }
    },
    // 线索保存
    insertClueSave() {
      this.dialogFormVisibleInsert = false
      this.currentGroupIds = this.currentGroupId
      this.clueRuleForm = {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      }
      this.getTaskResultData()
    },
    async groupRemove(id) {
      this.$confirm('确定删除数据(默认的场景不可删除)?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_clue_del_cancel',
        confirmButtonClass: 'cloud_clue_del_sure',
        customClass: 'cloud_clue_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            id: [id],
            operate_company_id: this.currentCompany
          }
          let res = await delCluesGroupList(obj)
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.currentGroupId = '' // 删除后清空当前分组id,用来重新渲染选中分组样式
            this.getTaskResultData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.cloud_clue_del>.el-message-box__btns')
        del.children[0].id = 'cloud_clue_del_cancel'
        del.children[1].id = 'cloud_clue_del_sure'
      }, 50)
    },
    async exportMore(row) {
      this.editCurrentId = row.id
      this.groupForm.name = row.name
      this.addIsTrue = false
      this.dialogFormVisibleGroup = true
    },
    async exportMoreSave() {
      // 分组新建、编辑
      this.$refs['groupForm'].validate(async (valid) => {
        if (valid) {
          this.groupForm.operate_company_id = this.currentCompany
          let res = null
          this.currentGroupIds = this.currentGroupId
          this.btnLoading = true
          if (this.addIsTrue) {
            res = await addCluesGroupList(this.groupForm).catch(() => {
              this.btnLoading = false
            })
          } else {
            let obj = {
              id: this.editCurrentId,
              data: this.groupForm,
              operate_company_id: this.currentCompany
            }
            res = await editCluesGroupList(obj).catch(() => {
              this.btnLoading = false
            })
          }
          if (res.code == 0) {
            this.btnLoading = false
            this.$message.success('操作成功！')
            this.dialogFormVisibleGroup = false
            this.getTaskResultData(true)
          }
        } else {
          return false
        }
      })
    },
    async goChain(icon, name) {
      if (icon == 'more' && this.checkedArr4.length == 0) {
        this.$message.error('请选择要关联的数据！')
        return
      }
      this.relname = '' // 全选时name传空；
      if (name) {
        this.relname = name
      } else {
        if (this.checkedAll) {
          this.relname = ''
        } else {
          this.relname = this.checkedArr4
            .map((item) => {
              return item.content
            })
            .join(',')
        }
      }
      this.chainDialog = true
    },
    // 获取分组列表数据
    async getTaskResultData(val) {
      // // 调用之前清空选择项
      // this.checkedAll=false
      // this.$refs['eltable' + this.tabActiveName].clearSelection();
      let obj = {
        page: this.groupCurrentPage,
        per_page: 10,
        no_page: '1',
        operate_company_id: this.currentCompany
      }
      this.groupLoading = true
      let res = await cluesGroupList(obj)
        .catch(() => {
          this.groupLoading = false
        })
        .catch(() => {
          this.groupArr = []
        })
      this.groupLoading = false
      let arr = res.data
      arr.forEach((item) => {
        item['num'] = item.collect ? item.collect : 0 // 所有线索数量
      })
      this.groupArr = arr
      if (!this.currentGroupId) {
        // 当前分组id不存在
        this.currentGroupId = this.groupArr.length > 0 ? String(this.groupArr[0].id) : ''
        this.currentGroupIds = this.groupArr.length > 0 ? String(this.groupArr[0].id) : ''
      }
      if (this.currentGroupId) {
        this.total = res.data.total
        // 分组下面的待确认，已确认，忽略菜单数量
        if (!val) {
          this.getRunningJob()
          this.getClueData()
          this.getTabNum()
        }
        if (this.groupArr.length != 0) {
          this.groupArr.forEach((item, index) => {
            item.isHighlighted = false
            if (item.id == this.currentGroupIds) {
              this.groupArr[index].isHighlighted = true
            }
          })
        }
      }
    },
    async goRecomment() {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要下发的数据！')
        return
      }
      if (!this.checkedAll) {
        if (
          this.checkedArr0.length == 0 &&
          this.checkedArr1.length == 0 &&
          this.checkedArr2.length == 0 &&
          this.checkedArr3.length == 0 &&
          this.checkedArr4.length == 0 &&
          this.checkedArr5.length == 0 &&
          this.checkedArr6.length == 0
        ) {
          this.$message.error('请选择要下发的数据！')
          return
        }
      }
      let clueData = [
        {
          id: this.checkedAll
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAll ? '1' : ''
        }
      ]
      let obj = {
        tab_status: this.tabActiveNameStatus,
        group_id: this.currentGroupId,
        data: clueData,
        keyword: this.formInline.keyword,
        operate_company_id: this.currentCompany
      }
      this.currentGroupIds = this.currentGroupId
      this.recommentLoading = true
      let res = await recommend(obj).catch(() => {
        this.recommentLoading = false
      })
      if (res.code == 0) {
        this.recommentLoading = false
        this.$message.success('下发成功')
        this.$refs['eltable' + this.tabActiveName].clearSelection()
        this.getClueData()
        this.getTabNum()
      }
    },
    async removeMore() {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      if (!this.checkedAll) {
        if (
          this.checkedArr0.length == 0 &&
          this.checkedArr1.length == 0 &&
          this.checkedArr2.length == 0 &&
          this.checkedArr3.length == 0 &&
          this.checkedArr4.length == 0 &&
          this.checkedArr5.length == 0 &&
          this.checkedArr6.length == 0
        ) {
          this.$message.error('请选择要操作的数据！')
          return
        }
      }
      let clueData = [
        {
          id: this.checkedAll
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAll ? '1' : ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAll ? '1' : ''
        }
      ]
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_del_cancel',
        confirmButtonClass: 'cloud_del_sure',
        customClass: 'cloud_del',
        type: 'warning'
      })
        .then(async () => {
          this.currentGroupIds = this.currentGroupId
          let obj = {
            tab_status: this.tabActiveNameStatus,
            group_id: this.currentGroupId,
            data: clueData,
            operate_company_id: this.currentCompany
          }
          this.removeLoading = true
          let res = await delCluesListV1(obj).catch(() => {
            this.removeLoading = false
          })
          if (res.code == 0) {
            this.removeLoading = false
            this.$message.success('删除成功！')
            this.$refs['eltable' + this.tabActiveName].clearSelection()
            this.getTaskResultData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.cloud_del>.el-message-box__btns')
        del.children[0].id = 'cloud_del_cancel'
        del.children[1].id = 'cloud_del_sure'
      }, 50)
    },
    // 单独扩展
    async batchAddClueFunOne() {
      if (!this.ruleForm.clueContent) {
        this.$message.error('请输入线索内容')
        return
      }
      let clueData = [
        {
          type: this.ruleForm.clueType,
          content: [this.ruleForm.clueContent]
        }
      ]
      let obj = {
        group_id: this.currentGroupId,
        data: clueData,
        is_auto_expend: '1', // 单独扩展
        file: '',
        operate_company_id: this.currentCompany
      }
      this.batchOneLoading = true
      let res = await insertCluesV1(obj).catch(() => {
        this.batchOneLoading = false
      })
      if (res.code == 0) {
        this.batchOneLoading = false
        this.$message.success('操作成功！')

        this.getRunningJob()
      }
    },
    // 批量扩展batchAddClue
    async batchAddClueFun() {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要扩展的数据！')
        return
      }
      if (!this.checkedAll) {
        if (
          this.checkedArr0.length == 0 &&
          this.checkedArr1.length == 0 &&
          this.checkedArr2.length == 0 &&
          this.checkedArr3.length == 0 &&
          this.checkedArr4.length == 0 &&
          this.checkedArr5.length == 0 &&
          this.checkedArr6.length == 0
        ) {
          this.$message.error('请选择要扩展的数据！')
          return
        }
      }
      let clueData = [
        {
          id: this.checkedAll
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        }
      ]
      let obj = {
        tab_status: this.tabActiveNameStatus,
        group_id: this.currentGroupId,
        data: clueData,
        keyword: this.formInline.keyword,
        operate_company_id: this.currentCompany
      }
      this.currentGroupIds = this.currentGroupId
      this.batchLoading = true
      let res = await batchAddClue(obj).catch(() => {
        this.batchLoading = false
      })
      if (res.code == 0) {
        this.batchLoading = false
        this.$message.success('操作成功')
        this.$refs['eltable' + this.tabActiveName].clearSelection()
        this.getRunningJob()
        this.getClueData()
        this.getTabNum()
      }
    },
    async exportList() {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要导出的数据！')
        return
      }
      if (!this.checkedAll) {
        if (
          this.checkedArr0.length == 0 &&
          this.checkedArr1.length == 0 &&
          this.checkedArr2.length == 0 &&
          this.checkedArr3.length == 0 &&
          this.checkedArr4.length == 0 &&
          this.checkedArr5.length == 0 &&
          this.checkedArr6.length == 0
        ) {
          this.$message.error('请选择要导出的数据！')
          return
        }
      }
      let clueData = [
        {
          id: this.checkedAll
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        },
        {
          id: this.checkedAll
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAll ? '1' : '',
          keyword: ''
        }
      ]
      let obj = {
        tab_status: this.tabActiveNameStatus,
        group_id: this.currentGroupId,
        data: clueData,
        keyword: this.formInline.keyword,
        operate_company_id: this.currentCompany
      }
      this.exportLoading = true
      let res = await exportCluesListV1(obj).catch(() => {
        this.exportLoading = false
      })
      if (res.code == 0) {
        this.exportLoading = false
        // this.$refs['eltable' + this.tabActiveName].clearSelection();
        this.download(this.showSrcIp + res.data.url)
      }
    },
    getLabel() {
      let label = ''
      if (this.tabActiveName == '0') {
        label = '根域'
      } else if (this.tabActiveName == '1') {
        label = '证书'
      } else if (this.tabActiveName == '2') {
        label = 'ICP'
      } else if (this.tabActiveName == '3') {
        label = 'ICON'
      } else if (this.tabActiveName == '4') {
        label = '关键词'
      } else if (this.tabActiveName == '5') {
        label = '子域名'
      } else if (this.tabActiveName == '6') {
        label = 'IP段'
      }
      return label
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs['eltable' + this.tabActiveName].toggleRowSelection(row, true)
        })
      } else {
        this.$refs['eltable' + this.tabActiveName].clearSelection()
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectionChange(val) {
      let arr = this.$refs['eltable' + this.tabActiveName].selection.filter((item) => {
        return item.type == this.tabActiveName
      })
      this['checkedArr' + this.tabActiveName] = arr // .selection获取当前选中的数据
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getClueData('', true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getClueData('', true)
    },
    dropdownClick(event) {
      event.stopPropagation() //阻止冒泡事件
    },
    // 移入移出显示修改、删除
    enterTitle(index) {
      $('.dropdownMore').eq(index).css({ display: 'block' })
    },
    leaveTitle(index) {
      $('.dropdownMore').eq(index).css({ display: 'none' })
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  .iconClass {
    color: #2677ff;
    font-size: 14px;
    cursor: pointer;
  }
  .iconClass::before {
    margin-right: 3px;
  }
  .groupClass {
    width: 17%;
    height: 100%;
    background: #fff;
    border-right: 1px solid #e9ebef;
    p {
      padding: 16px;
      font-weight: 600;
    }
    .activeClass {
      color: #2677ff;
      border-left: 4px solid #2677ff;
      background: #eff2f7;
    }
    ul {
      height: calc(100% - 53px);
      // height: 100px;
      overflow: auto;
      li {
        padding: 10px 16px;
        line-height: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        color: #62666c;

        i {
          color: #62666c;
          margin-left: 6px;
          cursor: pointer;
        }
      }
    }
  }
  .handle {
    border-bottom: 1px solid #e9ebef;
    .row-bg {
      padding: 10px 0;
      background-color: #f9fafc;
    }
    .bot {
      color: #62666c;
      display: flex;
      justify-content: space-between;
      margin: 12px 0;
      .progress {
        color: #2677ff;
      }
      .delTask {
        cursor: pointer;
      }
      span {
        i {
          margin-right: 3px;
        }
      }
    }
  }
  .infoKeyword {
    padding: 0 0 20px 12px;
  }
  // 企业关系开始
  /deep/ .elDialogAdd2 {
    .el-dialog .el-dialog__header {
      background: #f5f7fa;
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 16px !important;
      font-size: 14px !important;
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid #e9ebef;
      .el-dialog__title {
        font-size: 14px;
        color: #37393c !important;
      }
      .el-dialog__headerbtn {
        top: auto !important;
        right: 16px !important;
      }
    }
    .el-dialog .el-dialog__body {
      height: 500px;
      position: relative;
    }
    .back {
      margin-bottom: 10px;
      margin-right: 5px;
      padding: 0;
      width: 24px;
      height: 24px;
      border-radius: 2px;
      background-color: #e1e5ec;
      border-color: #e1e5ec;
      i {
        color: #62666c;
      }
    }
  }
  /deep/ .elDialogAdd2.add_img {
    .el-dialog .el-dialog__body {
      background-image: url('../../assets/images/companyQuery.png');
      background-size: 100% 100%;
    }
  }
  .company-input {
    width: 400px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    margin-top: -14px;
    .search-btn {
      width: 100%;
      height: 40px;
      background: #2677ff;
      border-radius: 0px 4px 4px 0px;
      font-size: 16px;
      color: #fff;
      margin-left: 0;
    }
    /deep/ .el-input__inner {
      height: 40px;
      line-height: 40px;
      border-color: #2677ff;
    }
    /deep/ .el-input-group__append {
      padding: 0;
      width: 68px;
    }
  }
  .company-list {
    padding-right: 8px;
    .title {
      display: flex;
      height: 48px;
      line-height: 48px;
      background: #f5f7fa;
    }
    .name {
      flex: 1;
      white-space: normal;
      padding-right: 8px;
    }
    .text {
      display: inline-block;
      width: 100px;
      text-align: center;
    }
    .action {
      display: flex;
      justify-content: space-between;
      padding-bottom: 20px;
    }
    /deep/ .el-tree {
      max-height: 360px;
      overflow-y: auto;
    }
  }
  .icp-host-list {
    /deep/ .el-table__body-wrapper {
      height: 350px;
      overflow-y: auto;
    }
  }
  /deep/ .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
  }
  .count {
    color: #2677ff;
    text-align: center;
  }
  // 企业关系结束
  /deep/.clueWrap {
    width: 83%;
    height: 100%;
    background: #fff;
    .top {
      box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.08);
      background: #fafbff;
      .el-input__icon {
        line-height: 32px;
      }
      .el-input-group__append {
        color: #fff;
        background: #2677ff;
      }
    }
    .statusTabs {
      margin-top: 10px;
      margin-left: 20px;
      .el-tabs__nav {
        padding-left: 0;
      }
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      height: 20px;
      .dropdownClass {
        margin-right: 10px;
      }
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input--suffix {
          i {
            line-height: 26px;
          }
        }
      }
      .el-tabs__header {
        height: 33px;
        border-bottom: 0;
      }
      .el-tabs__nav {
        padding-left: 0;
        height: 33px;
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #e4e7ed;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        text-align: center;
        background: rgba(38, 119, 255, 0.1);
        border-radius: 0;
        border: 1px solid #2677ff;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
        transition: none;
      }
    }
    .tableWrap {
      height: calc(100% - 354px);
    }
    .tableWrapTwo {
      height: calc(100% - 234px);
    }
    .el-table {
      border: 0;
    }
  }
}
#cloud_expand {
  min-width: 66px;
  height: 32px;
  background: #2677ff;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  // line-height: 32px;
  text-align: center;
  padding: 0 12px;
  border: 0;
  margin: 0;
  margin-right: 33px;
  &:hover {
    background-color: #3d85ff;
  }
  &:focus {
    background-color: #0055e5;
  }
}
/deep/#cloud_select {
  border: 0 !important;
  border-right: 1px solid #dadde9 !important;
  border-radius: 0px;
  width: 80px;
  background-color: #fafbff;
}
/deep/#cloud_enter {
  border: 0 !important;
  background-color: #fafbff;
}
.nameClass {
  width: 100%;
  height: 100%;
  position: relative;
  font-size: 14px;
}
.nameClass > span {
  display: inline-block;
  max-width: 80%;
  height: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/deep/.el-collapse-item__header {
  position: relative;
}
/deep/.el-collapse-item__header > i {
  position: absolute;
  right: 0px;
  bottom: 18px;
}
/deep/.el-collapse-item__header,
/deep/.el-collapse-item__wrap {
  border: 0;
  color: #62666c;
}
/deep/.el-collapse-item {
  padding: 0px 8px;
}
/deep/.el-collapse {
  .is-active {
    background: #eff2f6;
    font-weight: 500;
    color: #37393c;
  }
}
/deep/.el-collapse-item__content {
  background: #eff2f6;
  padding-bottom: 0;
}
.collapseType {
  color: #62666c;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0px;
  font-size: 14px;
  cursor: pointer;
}
.fangkuai {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: rgba(38, 119, 255, 0.28);
  border-radius: 1px;
  transform: rotate(45deg);
  margin-left: 4px;
  margin-right: 5px;
  vertical-align: middle;
}
.fangkuaiblue {
  background-color: #2677ff;
}
.collapseCount {
  color: #9ba4b2;
}
.dropdownMore {
  position: absolute;
  right: 20px;
  bottom: 0;
  display: none;
}
.dropdownMore > span {
  color: #62666c;
  background: #dce2e9;
  border-radius: 4px;
  padding: 0px 3px;
}
.confirmBox {
  margin-left: 20px !important;
}
</style>
