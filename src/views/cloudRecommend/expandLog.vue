<template>
  <div class="container">
    <div class="headerTitle">
      <span class="goback" @click="$router.go(-1)"
        ><i class="el-icon-arrow-left"></i>返回上一层</span
      >
      <span class="spline">/</span>
      <span>扩展记录</span>
    </div>
    <div class="filterTab">
      <div>
        <!-- <el-input v-model="formInline.keyword" @keyup.enter.native="checkFuncList" clearable placeholder="请输入查询条件">
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input> -->
        <span @click="highIsShow = true" id="cloud_info_filter" style="width: 80px"
          ><img
            src="../../assets/images/filter.png"
            alt=""
            style="width: 16px; vertical-align: middle; margin-right: 3px"
          />高级筛选</span
        >
      </div>
      <div>
        <el-checkbox
          class="checkboxAll"
          v-model="checkedAll"
          @change="checkAllChange"
          id="cloud_info_all"
          >选择全部</el-checkbox
        >
        <el-button
          class="normalBtnRe"
          type="primary"
          @click="removeMore('more')"
          id="cloud_info_more_del"
          >删除</el-button
        >
      </div>
    </div>
    <!-- 高级筛选条件 -->
    <hightFilter
      id="hightFilter"
      :highTabShow="highTabShow"
      :highlist="highlist"
      :total="total"
      @highcheck="highCheck"
    ></hightFilter>
    <div :class="hightFilterIsShow()">
      <el-table
        border
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
        @selection-change="handleSelectionChange"
        @cell-mouse-enter="showTooltip"
        @cell-mouse-leave="hiddenTooltip"
        ref="eltable"
        height="100%"
        style="width: 100%"
      >
        <el-table-column
          type="selection"
          align="center"
          :reserve-selection="true"
          :selectable="handleSelectable"
          width="55"
        >
        </el-table-column>
        <el-table-column
          v-for="item in tableHeader"
          :key="item.id"
          :prop="item.name"
          align="left"
          :min-width="item.minWidth"
          :label="item.label"
        >
          <template slot-scope="scope">
            <!-- 基础线索 -->
            <div class="clueClass" v-if="item.name == 'clues'">
              <span class="clueNumClass" v-if="scope.row[item.name].length > 0">{{
                scope.row[item.name].length
              }}</span>
              <el-tooltip
                v-if="scope.row[item.name].length > 0"
                class="item"
                effect="light"
                popper-class="chainClass"
                placement="top"
              >
                <div slot="content">
                  <ul class="recordClass">
                    <li v-for="(con, index) in recordsTableHeader" :key="index">
                      <div
                        v-if="
                          getClues(scope.row[item.name])['arr' + con.type] &&
                          getClues(scope.row[item.name])['arr' + con.type].length > 0
                        "
                      >
                        <p
                          >{{ con.label }}：{{
                            getClues(scope.row[item.name])['arr' + con.type].length
                          }}</p
                        >
                        <span
                          class="recordItem"
                          v-for="ch in getClues(scope.row[item.name])['arr' + con.type]"
                          :key="ch.id"
                        >
                          <span v-if="ch.type && ch.type == 3">
                            <el-image
                              :src="
                                ch.content && ch.content.includes('http')
                                  ? ch.content
                                  : showSrcIp + ch.content
                              "
                              alt=""
                            >
                              <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i>
                              </div>
                            </el-image>
                            {{ ch.hash }}
                          </span>
                          <span v-else
                            >{{ $punyCode.toUnicode(ch.content || ch)
                            }}{{ ch.punycode_domain ? '(' + ch.punycode_domain + ')' : '' }}</span
                          >
                        </span>
                      </div>
                    </li>
                  </ul>
                </div>
                <div>
                  <span class="detail" v-for="to in scope.row[item.name].slice(0, 3)" :key="to.id">
                    <span v-if="to.clues_info && to.clues_info.type != 3">
                      {{ $punyCode.toUnicode(to.clues_info.content ? to.clues_info.content : '-') }}
                    </span>
                    <span v-else>
                      <el-image
                        class="imgwrap"
                        v-if="to.clues_info && to.clues_info.content"
                        :src="
                          to.clues_info.content.includes('http')
                            ? to.clues_info.content
                            : showSrcIp + to.clues_info.content
                        "
                        alt=""
                      >
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </span>
                  </span>
                </div>
              </el-tooltip>
              <span v-else>-</span>
            </div>
            <!-- 扩展结果 -->
            <div class="clueClass" v-else-if="item.name == 'clues_records'">
              <span class="clueNumClass" v-if="scope.row[item.name].length > 0">{{
                scope.row[item.name].length
              }}</span>
              <el-tooltip
                v-if="scope.row[item.name].length > 0"
                class="item"
                effect="light"
                popper-class="chainClass"
                placement="top"
              >
                <div slot="content">
                  <ul class="recordClass">
                    <li v-for="(con, index) in recordsTableHeader" :key="index">
                      <div
                        v-if="
                          getRecords(scope.row[item.name])['arr' + con.type] &&
                          getRecords(scope.row[item.name])['arr' + con.type].length > 0
                        "
                      >
                        <p
                          >{{ con.label }}：{{
                            getRecords(scope.row[item.name])['arr' + con.type].length
                          }}</p
                        >
                        <span
                          class="recordItem"
                          v-for="ch in getRecords(scope.row[item.name])['arr' + con.type]"
                          :key="ch.id"
                        >
                          <span v-if="ch.type && ch.type == 3">
                            <el-image
                              :src="
                                ch.content.includes('http') ? ch.content : showSrcIp + ch.content
                              "
                              alt=""
                            >
                              <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i>
                              </div>
                            </el-image>
                            {{ ch.hash }}
                          </span>
                          <!-- <span v-else>{{ch.content || ch}}</span> -->
                          <span v-else
                            >{{ $punyCode.toUnicode(ch.content || ch)
                            }}{{ ch.punycode_domain ? '(' + ch.punycode_domain + ')' : '' }}</span
                          >
                        </span>
                      </div>
                    </li>
                  </ul>
                </div>
                <div>
                  <span class="detail" v-for="to in scope.row[item.name].slice(0, 3)" :key="to.id">
                    <span v-if="to.type != 3">
                      {{ $punyCode.toUnicode(to.content) }}
                    </span>
                    <span v-else>
                      <el-image
                        class="imgwrap"
                        v-if="to.content"
                        :src="to.content.includes('http') ? to.content : showSrcIp + to.content"
                        alt=""
                      >
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </span>
                  </span>
                </div>
              </el-tooltip>
              <span v-else>-</span>
            </div>
            <span v-else-if="item.name == 'status'"
              >{{ getStatus(scope.row[item.name]) }}
              <el-progress
                v-if="scope.row['status'] == 1"
                :text-inside="true"
                :stroke-width="14"
                :percentage="parseFloat(scope.row['progress'])"
                :status="setProgressColor(scope.row['status'])"
              ></el-progress>
            </span>
            <!-- 扩展结束才有结束时间 -->
            <span v-else-if="item.name == 'updated_at'">{{
              scope.row['status'] == 1 ? '-' : scope.row[item.name]
            }}</span>
            <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="left" width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="removeMore('one', scope.row.id)"
              id="cloud_info_del"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip> -->
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="pageSizeArr"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
    >
    </el-pagination>
    <el-drawer title="高级筛选" :visible.sync="highIsShow" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item
            v-for="item in tableHeaderIsShow"
            :key="item.id"
            :label="item.label"
            :prop="item.name"
          >
            <el-select
              v-if="item.icon == 'select'"
              clearable
              filterable
              v-model="formInline[item.name]"
              @change="selectChange($event, 'status', statusData, true, false)"
            >
              <el-option
                v-for="item in statusData"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-date-picker
              v-if="item.icon == 'date'"
              v-model="formInline[item.name]"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="cloud_info_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="cloud_info_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { mapGetters, mapState } from 'vuex'
import { delExpendList, expendLogList } from '@/api/apiConfig/recommend.js'

export default {
  components: { tableTooltip, hightFilter },
  props: ['group_id'],
  data() {
    return {
      highTabShow: [
        {
          label: '扩展状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '开始时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '结束时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      highlist: null,
      highIsShow: false,
      recordsTableData: [],
      recordsTableHeader: [
        {
          name: '6',
          label: 'IP段',
          type: '6'
        },
        {
          name: 'content',
          label: '根域',
          type: '0'
        },
        {
          name: 'content',
          label: '证书',
          type: '1'
        },
        {
          name: 'content',
          label: 'ICP',
          type: '2'
        },
        {
          name: '3',
          label: 'ICON',
          type: '3'
        },
        {
          name: '4',
          label: '关键词',
          type: '4'
        },
        {
          name: '5',
          label: '子域名',
          type: '5'
        }
      ],
      statusData: [
        {
          value: '0',
          name: '待处理'
        },
        {
          value: '1',
          name: '扩展中'
        },
        {
          value: '2',
          name: '扩展完成'
        },
        {
          value: '3',
          name: '扩展失败'
        }
      ],
      tableHeaderIsShow: [
        {
          id: '1',
          name: 'status',
          icon: 'select',
          label: '扩展状态'
        },
        {
          id: '2',
          name: 'created_at',
          icon: 'date',
          label: '开始时间'
        },
        {
          id: '3',
          name: 'updated_at',
          icon: 'date',
          label: '结束时间'
        }
      ],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      loading: false,
      taskNameArr: [],
      clueCheckedArr: [],
      dialogImageUrl: '', // 图片放大的地址
      dialogVisibleImg: false,
      showMore: true,
      viewResultIsShow: false,
      checkedCities: ['上海', '北京'],
      isIndeterminate: true,
      cluesList: [], // 线索数据
      formInline: {
        keyword: '',
        status: '',
        created_at: [],
        updated_at: []
      },
      ruleForm: {
        type: []
      },
      checkedAll: false,
      clueCheckedAll: false,
      tableData: [],
      checkedArr: [],
      currentPage: 1,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      tableHeader: [
        {
          label: '基础线索',
          name: 'clues',
          minWidth: '100'
        },
        {
          label: '扩展状态',
          name: 'status',
          minWidth: '50'
        },
        {
          label: '开始时间',
          name: 'created_at',
          minWidth: '80'
        },
        {
          label: '结束时间',
          name: 'updated_at',
          minWidth: '80'
        },
        {
          label: '扩展结果',
          name: 'clues_records'
        }
      ],
      user: {
        role: ''
      }
    }
  },
  watch: {
    getterCompanyChange(val) {
      this.$router.go(-1) // 安服账号切换企业需要回到线索分组一级页面
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    },
    // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      if (val) {
        // 切换账号去除全选
        this.checkedAll = false
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, false)
        })
        this.getRecommendRecordsList()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },

  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getRecommendRecordsList()
    } else {
      this.getRecommendRecordsList()
    }
  },

  methods: {
    handleMessage(res, o) {
      //处理接收到的信息
      if (res.cmd == 'cloud_recommend_task_progress') {
        // 漏洞核查与资产、漏洞扫描区分，scan_poc_num存在是漏洞核查任务
        if (res.data.status == 2) {
          this.$message.success('扩展完成！')
          this.currentId = '' // 控制推送结束后仅执行一次
          this.getRecommendRecordsList()
        } else if (res.data.status == 1) {
          // 正在扫描
          this.currentId = res.data.id
          // 推送数据渲染到列表
          this.tableData.forEach((item, index) => {
            if (item.id == res.data.task_id) {
              this.$set(this.tableData[index], 'status', res.data.status)
              this.$set(this.tableData[index], 'progress', res.data.progress)
            }
          })
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.getRecommendRecordsList()
        }
      }
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getRecommendRecordsList()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    resetForm(drawerForm) {
      this.$nextTick(() => {
        this.$refs.drawerForm.resetFields()
      })
      this.formInline = {
        keyword: '',
        status: '',
        created_at: [],
        updated_at: []
      }
    },
    setProgressColor(status) {
      if (status == 4) {
        return 'warning'
      }
    },
    async getRecommendRecordsList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }

      this.loading = true
      this.highIsShow = false
      this.formInline.created_at = this.formInline.created_at ? this.formInline.created_at : []
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        group_id: this.$route.query.group_id,
        operate_company_id: this.currentCompany,
        ...this.formInline
      }
      let res = await expendLogList(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.tableData = res.data.items
      this.total = res.data.total
      this.$nextTick(() => {
        this.$refs.eltable.clearSelection()
      })
    },
    getRecords(clue) {
      let obj = {
        arr0: [],
        arr1: [],
        arr2: [],
        arr3: [],
        arr4: [],
        arr5: [],
        arr6: []
      }
      clue.forEach((item) => {
        if (item.type || item.type == 0) {
          if (item && item.content) {
            obj['arr' + item.type].push({
              id: item.id,
              type: item.type,
              content: item.content,
              hash: item.hash,
              punycode_domain: item.punycode_domain
            })
          }
        }
      })
      return obj
    },
    getClues(clue) {
      let obj = {
        arr0: [],
        arr1: [],
        arr2: [],
        arr3: [],
        arr4: [],
        arr5: [],
        arr6: []
      }
      clue.forEach((item) => {
        if (item && item.clues_info && (item.clues_info.type || item.clues_info.type == 0)) {
          if (item.clues_info.content) {
            obj['arr' + item.clues_info.type].push({
              id: item.clues_info.id,
              type: item.clues_info.type,
              content: item.clues_info.content,
              hash: item.clues_info.hash,
              punycode_domain: item.punycode_domain
            })
          }
        }
      })
      return obj
    },
    getStatus(item) {
      // 状态 0/1/2/3 默认/执行中/成功/失败
      let str = ''
      switch (item) {
        case 0:
          str = '待处理'
          break
        case 1:
          str = '扩展中'
          break
        case 2:
          str = '扩展完成'
          break
        case 3:
          str = '扩展失败'
          break
        default:
          str = '-'
      }
      return str
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getRecommendRecordsList(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getRecommendRecordsList(true)
    },
    async removeMore(icon, id) {
      let obj = {}
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
        obj = {
          id: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          is_all: this.checkedAll ? '1' : '',
          group_id: this.$route.query.group_id,
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else {
        obj = {
          id: [id],
          group_id: this.$route.query.group_id,
          operate_company_id: this.currentCompany,
          is_all: ''
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_info_del_cancel',
        confirmButtonClass: 'cloud_info_del_sure',
        customClass: 'cloud_info_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await delExpendList(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.getRecommendRecordsList()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.cloud_info_del>.el-message-box__btns')
        del.children[0].id = 'cloud_info_del_cancel'
        del.children[1].id = 'cloud_info_del_sure'
      }, 50)
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    & > .el-form {
      width: 100%;
    }
    img {
      display: block;
      margin: 0 auto;
      margin-bottom: 30px;
    }
    .el-form-item {
      margin-bottom: 20px !important;
    }
  }
  .filterTab {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #fff;
    & > div {
      .el-input {
        width: 240px;
        margin-right: 0;
      }
      & > span {
        font-weight: 400;
        color: #2677ff;
        line-height: 20px;
        margin-left: 16px;
        cursor: pointer;
      }
    }
  }
  & > .el-form {
    position: relative;
    // height: 244px;
    overflow: hidden;
    background: #eff2f7;
    border-radius: 4px;
    margin: 0 20px 28px;
    padding: 20px;
    .divider {
      position: absolute;
      bottom: 12px;
      width: 100%;
      text-align: center;
      color: #2677ff;
      font-size: 14px;
      i {
        margin-left: 4px;
      }
    }
    .el-form-item {
      margin-bottom: 0px !important;
    }
    .el-form-item__content {
      & > div {
        display: flex;
      }
    }
    .el-checkbox-group {
      margin-left: 20px;
    }
  }
  .tableWrapFilter {
    height: calc(100% - 175px) !important;
  }
  .tableWrap {
    height: calc(100% - 129px);
  }
  .el-table {
    width: 99%;
    border: 0;
    .companyClass {
      padding: 0 2px;
      height: 40px;
      line-height: 40px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .clueClass {
      display: flex;
      align-items: center;
    }
    .clueNumClass {
      display: block;
      padding: 1px 5px;
      margin-right: 10px;
      color: #2677ff;
      border: 1px solid #ebeef5;
    }
    .detail {
      width: 100%;
      display: block;
      margin-bottom: 5px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
