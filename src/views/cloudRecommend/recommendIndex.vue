<template>
  <div class="recommendBox">
    <div class="recommendHeader">
      <div style="display: flex; align-items: center">
        <el-input
          v-model="formInline.keyword"
          @keyup.enter.native="checkFuncList"
          placeholder="请输入关键字进行搜索"
          id="cloud_recommend_keycheck"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <span
          @click="highCheckIsShow"
          id="cloud_recommend_filter"
          style="width: 80px; display: inline-block"
          ><img
            src="../../assets/images/filter.png"
            alt=""
            style="width: 16px; vertical-align: middle; margin-right: 3px"
          />高级筛选</span
        >
      </div>
      <div>
        <el-checkbox
          class="checkboxAll"
          v-model="checkedAll"
          @change="checkAllChange"
          id="cloud_recommend_all"
          >选择全部</el-checkbox
        >
        <el-button
          class="normalBtnRe"
          type="primary"
          @click="removeMore"
          id="cloud_recommend_more_del"
          >删除</el-button
        >
        <el-button class="normalBtn" type="primary" @click="add" id="cloud_recommend_add"
          >新建任务</el-button
        >
        <!-- <el-button class="normalBtn" type="primary" >展开选择</el-button> -->
      </div>
    </div>
    <!-- 高级筛选条件 -->
    <hightFilter
      id="hightFilter"
      :highlist="highlist"
      :total="total"
      pageIcon="recommend"
      @highcheck="highCheck"
    ></hightFilter>
    <div :class="hightFilterIsShow()">
      <el-table
        border
        :data="tableData"
        v-loading="loading"
        row-key="_id"
        :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
        @selection-change="handleSelectionChange"
        @cell-mouse-enter="showTooltip"
        @cell-mouse-leave="hiddenTooltip"
        ref="eltable"
        height="100%"
        style="width: 100%"
      >
        <el-table-column
          type="selection"
          align="center"
          :reserve-selection="true"
          :show-overflow-tooltip="true"
          :selectable="handleSelectable"
          width="55"
        >
        </el-table-column>
        <el-table-column
          v-for="item in recommedData"
          :key="item.id"
          :prop="item.name"
          align="left"
          :label="item.label"
          :min-width="item.minWidtth"
          :fixed="item.fixed"
        >
          <template slot-scope="scope">
            <span v-if="item.name == 'status'">
              <i class="grayStatus" v-if="scope.row['status'] == 0"></i>
              <i class="greenStatus" v-if="scope.row['status'] == 2"></i>
              <i class="redStatus" v-if="scope.row['status'] == 3"></i>
              {{ getTableStatus(scope.row[item.name]) }}
              <el-progress
                v-if="scope.row['status'] == 1"
                :text-inside="true"
                :stroke-width="14"
                :percentage="parseFloat(scope.row['progress'])"
                :status="setProgressColor(scope.row['status'])"
              ></el-progress>
            </span>
            <span v-else-if="item.name == 'use_seconds'">{{
              secondsFormat(scope.row[item.name])
            }}</span>
            <span v-else-if="item.name == 'op'">{{
              scope.row[item.name] ? scope.row[item.name]['name'] : '-'
            }}</span>
            <span v-else>
              <span>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="left" width="150">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click.native="goDetails(scope.row)"
              id="cloud_recommend_info"
              >查看详情</el-button
            >
            <el-button
              type="text"
              size="small"
              @click.native="mydelete(scope.row)"
              id="cloud_recommend_del"
              v-if="scope.row['status'] != 1"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="pageSizeArr"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
    >
    </el-pagination>
    <!-- 新建弹框 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      title="新建任务"
      :visible.sync="dialogVisible"
      width="810px"
    >
      <div style="height: 100%">
        <div class="cluebox">
          <div class="clueboxtitle">场景:</div>
          <el-select v-model="value" placeholder="请选择">
            <el-option
              v-for="(item, index) in clueList"
              :key="index"
              :label="item.name"
              :value="item.id"
              @click.native="getGroupId(item.id)"
            ></el-option>
          </el-select>
        </div>
        <div style="display: inline-block; margin-top: 20px">
          <div class="confirmBox">
            <el-radio-group v-model="tabActiveName" @change="handleClick(tabActiveName)">
              <el-radio-button :label="item.name" v-for="(item, index) in tabData" :key="index"
                >{{ item.label }}({{ item.count }})</el-radio-button
              >
            </el-radio-group>
          </div>
        </div>
        <!-- <el-tabs v-model="tabActiveName" type="card" @tab-click="handleClick(tabActiveName)" class="tab">
                    <el-tab-pane v-for="(item, index) in tabData" :key="index" :label="`${item.label}(${item.count})`" :name="item.name"></el-tab-pane>
                </el-tabs> -->
        <div class="table" v-loading="dialogloading">
          <el-table
            border
            :data="loadingData"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            @selection-change="handleSelectionChange1"
            :ref="'eltable' + tabActiveName"
            height="100%"
            style="width: 100%"
          >
            <el-table-column
              type="selection"
              align="center"
              :reserve-selection="true"
              :selectable="handleSelectable1"
              width="55"
            >
            </el-table-column>
            <el-table-column
              v-for="item in loadingHeader"
              :key="item.id"
              :prop="item.name"
              align="left"
              min-width="120"
              :fixed="item.fixed"
              :label="item.label"
            >
              <template slot="header">
                <span v-if="item.name == 'content'">{{ getLabel() }}</span>
                <span v-else>{{ item.label }}</span>
              </template>
              <template slot-scope="scope">
                <el-image
                  v-if="tabActiveName == '3' && item.name == 'content' && scope.row[item.name]"
                  :src="
                    scope.row[item.name].includes('http')
                      ? scope.row[item.name]
                      : showSrcIp + scope.row[item.name]
                  "
                  alt=""
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <span
                  v-else-if="
                    tabActiveName == '3' && item.name == 'content' && !scope.row[item.name]
                  "
                  >-</span
                >
                <span v-else>
                  <span v-if="item.name == 'status'">{{ getStatus(scope.row[item.name]) }}</span>
                  <span v-else-if="item.name == 'chain_list'">
                    <span v-if="scope.row[item.name] && scope.row[item.name].length > 0">
                      <el-tooltip
                        class="item"
                        effect="light"
                        placement="top"
                        popper-class="chainClass"
                        :open-delay="500"
                      >
                        <div slot="content" style="position: relative">
                          <el-tooltip
                            effect="light"
                            class="item"
                            placement="top"
                            content="一键复制"
                            v-if="scope.row[item.name] && scope.row[item.name].length != 0"
                            :open-delay="500"
                          >
                            <i
                              class="el-icon-document-copy"
                              @click="copyClick(scope.row[item.name])"
                              style="
                                color: #2677ff;
                                cursor: pointer;
                                position: absolute;
                                right: -6px;
                                top: 0;
                              "
                            ></i>
                          </el-tooltip>
                          <span
                            v-for="(con, index) in getChains(scope.row[item.name])"
                            :key="index"
                          >
                            <span>{{ index + 1 }}、</span>
                            <span v-if="con.type && con.type == 3">
                              <el-image
                                :src="
                                  con.content.includes('http')
                                    ? con.content
                                    : showSrcIp + con.content
                                "
                                alt=""
                              >
                                <div slot="error" class="image-slot">
                                  <i class="el-icon-picture-outline"></i>
                                </div>
                              </el-image>
                            </span>
                            <span v-else
                              >{{ $punyCode.toUnicode(con.content || con)
                              }}{{
                                con.punycode_domain ? '(' + con.punycode_domain + ')' : ''
                              }}</span
                            >
                            <i
                              v-if="index < getChains(scope.row[item.name]).length - 1"
                              class="el-icon-right iconRight"
                            ></i>
                          </span>
                        </div>
                        <span style="display: flex !important; align-items: center">
                          {{ scope.row[item.name].length - 1 }}
                          <img
                            src="../../assets/images/chain.svg"
                            alt=""
                            style="width: 12px; margin-left: 5px; vertical-align: middle"
                          />
                        </span>
                      </el-tooltip>
                    </span>
                    <span v-else>-</span>
                  </span>
                  <span v-else>
                    <el-tooltip class="item" effect="light" placement="top">
                      <div slot="content">
                        {{ $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '')
                        }}{{
                          scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                        }}
                      </div>
                      <span>{{
                        $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '-')
                      }}</span>
                    </el-tooltip>
                  </span>
                </span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="handleSizeChange1"
            @current-change="handleCurrentChange1"
            :current-page="currentPage1"
            :page-sizes="pageSizeArr1"
            :page-size="pageSize1"
            layout="total, prev, pager, next, sizes, jumper"
            class="myPagination"
            :total="total1"
          >
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogVisible = false" id="cloud_recommend_add_cancel"
          >关 闭</el-button
        >
        <el-button
          class="highBtn"
          type="primary"
          :loading="btnLoading"
          @click="goRecomment"
          id="cloud_recommend_add_sure"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!-- 高级筛选 -->
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="85px">
          <el-form-item label="任务状态：" prop="status">
            <el-select
              filterable
              clearable
              v-model="formInline.status"
              @change="selectChange($event, 'status', statusArr, true, false)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in statusArr"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间：" prop="created_at">
            <el-date-picker
              v-model="formInline.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="发起人：" prop="op_id">
            <el-select
              filterable
              clearable
              v-model="formInline.op_id"
              @change="selectChange($event, 'op_id', userArr, true, false)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in userArr"
                :key="item.id"
                :label="item.name"
                :value="[item.id]"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="cloud_recommend_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="cloud_recommend_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { getAllUser } from '@/api/apiConfig/discovery.js'
import { cluesGroupList, tabNumClues, cluesList } from '@/api/apiConfig/clue.js'
import { delRecommendRecords, recommendRecords, recommend } from '@/api/apiConfig/recommend.js'

export default {
  components: { tableTooltip, hightFilter },
  data() {
    return {
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      btnLoading: false,
      value: '',
      clueList: [], //线索分组列表
      checkedAll: false, //全选
      checkedArr: [],
      checkedArr1: [],
      checkedAll1: false,
      highCheckdialog: false,
      total: 0, //总数
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      currentPage1: 1,
      pageSizeArr1: [10, 30, 50, 100],
      pageSize1: 10,
      total1: 0,
      loading: false, //加载
      dialogloading: false,
      tableData: [],
      recommedData: [
        {
          id: 1,
          label: '任务名称',
          name: 'task_name',
          fixed: 'left',
          minWidtth: '70'
        },
        {
          id: 2,
          label: '任务状态',
          name: 'status',
          minWidtth: '60'
        },
        {
          id: 3,
          label: '开始时间',
          name: 'created_at'
        },
        {
          id: 4,
          label: '结束时间',
          name: 'end_at'
        },
        {
          id: 5,
          label: '任务耗时',
          name: 'use_seconds',
          minWidtth: '60'
        },
        {
          id: 6,
          label: '推荐资产数量',
          name: 'count'
        },
        {
          id: 7,
          label: '发起人',
          name: 'op',
          minWidtth: '60'
        }
        //   {
        //     id: 8,
        //     label: 'ip段',
        //     name: 'content',
        //     minWidtth: '60'
        // }
      ],
      dialogVisible: false,
      tabActiveName: 0,
      tabData: [
        {
          name: '0',
          label: '域名',
          count: ''
        },
        {
          name: '1',
          label: '证书',
          count: ''
        },
        {
          name: '2',
          label: 'ICP',
          count: ''
        },
        {
          name: '3',
          label: 'ICON',
          count: ''
        },
        {
          name: '4',
          label: '关键词',
          count: ''
        },

        {
          name: '6',
          label: 'IP段',
          count: ''
        }
        // {
        //   name: '5',
        //   label: "子域名",
        //   count: ''
        // }
      ],
      loadingData: [],
      loadingHeader: [
        {
          label: '关键词',
          name: 'content'
        },
        {
          label: '企业名称',
          name: 'clue_company_name'
        },
        {
          label: '来源',
          name: 'source_label'
        },
        {
          label: '添加时间',
          name: 'created_at'
        },
        {
          label: '线索链',
          name: 'chain_list'
        }
      ],
      // 高级筛选
      formInline: {
        keyword: '',
        status: [],
        created_at: [],
        op_id: []
      },
      formInlineList: {
        no_page: '', // 1 没有分页，不传值 有分页
        keyword: '',
        page: 1,
        per_page: 10,
        status: '1', // 已确认线索
        operate_company_id: '',
        group_id: ''
      },
      user: {
        role: ''
      },
      statusArr: [
        {
          id: [0],
          name: '待处理'
        },
        {
          id: [1],
          name: '处理中'
        },
        {
          id: [2],
          name: '完成推荐'
        },
        {
          id: [3],
          name: '处理失败'
        }
      ],
      userArr: [],
      checkedArr0: [],
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: []
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.user.role == 2) {
        // 切换账号去除全选
        this.checkedAll = false
        this.checkedAll1 = false
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, false)
        })
        // this.getRecommendCluesData()
        this.getRecommendRecordsList()
        this.getcluesGroupList()
      }
    },
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },

  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getRecommendRecordsList()
      this.getcluesGroupList()
    } else {
      this.getRecommendRecordsList()
      this.getcluesGroupList()
    }
  },
  methods: {
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    copyClick(tmp) {
      let data = []
      data.push(tmp)
      let text = ''
      data.map((item) => {
        this.getChains(item).map((v, i) => {
          if (v.type && v.type == 3) {
            if (i == item.length - 1 && v) {
              text += 'icon' + '\n'
            } else {
              text += 'icon' + '-->'
            }
          } else {
            if (i < item.length - 1 && v) {
              text += v.content + '-->'
            } else if (i == item.length - 1 && v) {
              text += v.content + '\n'
            } else {
              text += v.content
            }
          }
        })
      })
      this.$copyText(text).then(
        (res) => {
          console.log('复制成功：', res)
          this.$message.success('复制成功')
        },
        (err) => {
          console.log('', err)
          this.$message.error('复制失败')
        }
      )
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    // 选中项中文名称-用于高级筛选标签
    selectItem(name, value) {
      // this.formInline[name] = value
      this.$set(this.formInline, name, value)
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (res.cmd == 'recommend_progress') {
        if (res.data.status == 2) {
          this.$message.success('推荐成功！')
          this.currentId = '' // 控制推送结束后仅执行一次
          // this.tableData.forEach((item, index) => {
          //   if (item.flag == res.data.flag) {
          //     this.$set(this.tableData[index], 'status', res.data.status)
          //   }
          // })
          this.getRecommendRecordsList()
        } else if (res.data.status == 1) {
          // 正在扫描
          this.currentId = res.data.user_id
          // 推送数据渲染到列表
          this.tableData.forEach((item, index) => {
            if (item.flag == res.data.flag) {
              this.$set(this.tableData[index], 'status', res.data.status)
              this.$set(this.tableData[index], 'progress', res.data.progress)
              this.$set(this.tableData[index], 'use_seconds', res.data.use_seconds)
              this.$set(this.tableData[index], 'start_at', res.data.start_at)
            }
          })
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.getRecommendRecordsList()
        }
      }
    },
    setProgressColor(status) {
      if (status == 4) {
        return 'warning'
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getRecommendRecordsList(true)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getRecommendRecordsList(true)
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange1(val) {
      this.pageSize1 = val
      this.getTaskResultData()
    },
    handleCurrentChange1(val) {
      this.currentPage1 = val
      this.getTaskResultData()
    },
    handleSelectionChange1(val) {
      this['checkedArr' + this.tabActiveName] = val
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectable1(row, index) {
      return !this.checkedAll1
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    getChains(row) {
      let arr = row.filter((item) => {
        return item.content
      })
      return arr
    },
    // 任务状态显示
    getTableStatus(val) {
      if (val == 0) {
        return '待处理'
      } else if (val == 1) {
        return '处理中'
      } else if (val == 2) {
        return '完成推荐'
      } else {
        return '处理失败'
      }
    },
    // getTableStatus(status) {
    //   let statusLabel = ''
    //   if (status == 0) {
    //     statusLabel = '等待扫描'
    //   } else if (status == 1) {
    //     statusLabel = '扫描中'
    //   } else if (status == 2) {
    //     statusLabel = '扫描完成'
    //   } else if (status == 3) {
    //     statusLabel = '扫描失败'
    //   } else if (status == 4) {
    //     statusLabel = '暂停扫描'
    //   }
    //   return statusLabel
    // },
    // 获取任务
    async getRecommendRecordsList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }

      let obj = {
        data: {
          page: this.currentPage,
          per_page: this.pageSize,
          audit: [], // 审核状态:0/1/2 待审核/已通过/已忽略
          keyword: this.formInline.keyword, // 模糊搜索,
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      }
      this.loading = true
      let res = await recommendRecords(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.tableData = res.data.items
      this.total = res.data.total
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
    },
    async mydelete(data) {
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_recommend_del_cancel',
        confirmButtonClass: 'cloud_recommend_del_sure',
        customClass: 'cloud_recommend_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            data: {
              operate_company_id: this.currentCompany,
              id: [data.flag],
              whole: this.checkedAll ? 1 : ''
            }
          }
          this.loading = true
          let res = await delRecommendRecords(obj)
          this.loading = false
          if (res.code == 0) {
            // this.currentPage = this.updateCurrenPage(this.total, this.checkedArr, this.currentPage, this.pageSize) // 更新页码
            this.getRecommendRecordsList()
            this.$message.success('删除成功！')
            this.$refs.eltable.clearSelection()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.cloud_recommend_del>.el-message-box__btns')
        del.children[0].id = 'cloud_recommend_del_cancel'
        del.children[1].id = 'cloud_recommend_del_sure'
      }, 50)
    },
    async removeMore() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      } else {
        this.checkedArr.forEach((val) => {
          // if(val.status == 1){
          //     this.$message.warning('执行中的数据不能删除！')
          //     throw new Error('执行中的数据不能删除！');
          // }
        })
        this.$confirm('确定删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'cloud_recommend_del_cancel',
          confirmButtonClass: 'cloud_recommend_del_sure',
          customClass: 'cloud_recommend_del',
          type: 'warning'
        })
          .then(async () => {
            let obj = {
              data: {
                operate_company_id: this.currentCompany,
                id: this.checkedArr.map((item) => {
                  return item.flag
                }),
                whole: this.checkedAll ? 1 : ''
              }
            }
            this.loading = true
            let res = await delRecommendRecords(obj)
            this.loading = false
            if (res.code == 0) {
              this.currentPage = this.updateCurrenPage(
                this.total,
                this.checkedArr,
                this.currentPage,
                this.pageSize
              ) // 更新页码
              this.getRecommendRecordsList()
              this.$message.success('删除成功！')
              this.$refs.eltable.clearSelection()
            }
          })
          .catch(() => {})
        setTimeout(() => {
          var del = document.querySelector('.cloud_recommend_del>.el-message-box__btns')
          del.children[0].id = 'cloud_recommend_del_cancel'
          del.children[1].id = 'cloud_recommend_del_sure'
        }, 50)
      }
    },
    async highCheckIsShow() {
      this.highCheckdialog = true
      let res = await getAllUser({ operate_company_id: this.currentCompany })
      this.userArr = res.data
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选查询
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getRecommendRecordsList()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    // 选中项中文名称-用于高级筛选标签
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    //重置
    resetForm() {
      this.formInline = {
        keyword: '',
        status: [],
        created_at: [],
        op_id: []
      }
    },
    // 跳转详情页面
    goDetails(data) {
      this.$router.push({ path: '/scanReg', query: { flag: data.flag } })
    },
    getGroupId(id) {
      this.formInlineList.group_id = id
      this.getTabNum()
      this.getTaskResultData()
    },
    async getTabNum() {
      let obj = {
        group_id: this.formInlineList.group_id,
        data: {
          status: 1,
          operate_company_id: this.currentCompany
        }
      }
      let res = await tabNumClues(obj)
      if (res.data.clues_count.length > 0) {
        res.data.clues_count.forEach((item, index) => {
          if (this.tabData[index]) {
            this.tabData[index].count = item.count
          } else if (index == 6) {
            this.tabData[5].count = item.count
          }
        })
      }
    },
    // 添加任务
    add() {
      this.dialogVisible = true
      this.getTaskResultData()
    },
    // 获取线索分组
    async getcluesGroupList() {
      let obj = {
        no_page: '1',
        operate_company_id: this.currentCompany
      }
      let res = await cluesGroupList(obj).catch(() => {
        this.clueList = []
      })
      if (res.code == 0) {
        if (res.data.length > 0) {
          this.formInlineList.group_id = res.data[0].id
          this.value = res.data[0].name
          this.getTabNum()
        }
        this.clueList = res.data
      }
    },
    // 获取线索列表
    async getTaskResultData() {
      this.checkedArr1 = []
      this.formInlineList.page = this.currentPage1
      this.formInlineList.per_page = this.pageSize1
      this.formInlineList.operate_company_id = this.currentCompany
      this.formInlineList.no_page = ''
      // this.formInlineList.group_id =
      let obj = {
        type: this.tabActiveName,
        query: this.formInlineList
      }
      this.dialogloading = true
      let res = await cluesList(obj).catch(() => {
        this.loading = false
      })
      this.dialogloading = false
      this.loadingData = res.data.items
      this.total1 = res.data.total
    },
    async goRecomment() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0
      ) {
        this.$message.error('请选择要下发的数据！')
        return
      }
      let clueData = [
        {
          id: this.checkedAll1
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAll1 ? '1' : ''
        },
        {
          id: this.checkedAll1
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAll1 ? '1' : ''
        },
        {
          id: this.checkedAll1
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAll1 ? '1' : ''
        },
        {
          id: this.checkedAll1
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAll1 ? '1' : ''
        },
        {
          id: this.checkedAll1
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAll1 ? '1' : ''
        },
        {
          id: this.checkedAll1
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAll1 ? '1' : ''
        }
      ]
      let obj = {
        group_id: this.formInlineList.group_id,
        data: clueData,
        keyword: '',
        operate_company_id: this.currentCompany
      }
      this.btnLoading = true
      let res = await recommend(obj).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        setTimeout(() => {
          this.btnLoading = false
          this.$message.success('下发成功')
          this.dialogVisible = false
          this.getRecommendRecordsList()
        }, 1000)
      }
    },
    // 高级筛选
    handleClick(data) {
      //icp和证书时要有企业名称
      if (data == 0 || data == 1 || data == 2) {
        this.loadingHeader = [
          {
            label: '关键词',
            name: 'content'
          },
          {
            label: '企业名称',
            name: 'clue_company_name'
          },
          {
            label: '来源',
            name: 'source_label'
          },
          {
            label: '添加时间',
            name: 'created_at'
          },
          {
            label: '线索链',
            name: 'chain_list'
          }
        ]
      } else {
        this.loadingHeader = [
          {
            label: '关键词',
            name: 'content'
          },
          {
            label: '来源',
            name: 'source_label'
          },
          {
            label: '添加时间',
            name: 'created_at'
          },
          {
            label: '线索链',
            name: 'chain_list'
          }
        ]
      }
      this.currentPage1 = 1
      this.getTaskResultData()
    },
    getLabel() {
      let label = ''
      if (this.tabActiveName == '0') {
        label = '根域'
      } else if (this.tabActiveName == '1') {
        label = '证书'
      } else if (this.tabActiveName == '2') {
        label = 'ICP'
      } else if (this.tabActiveName == '3') {
        label = 'ICON'
      } else if (this.tabActiveName == '4') {
        label = '关键词'
      } else if (this.tabActiveName == '5') {
        label = '子域名'
      } else if (this.tabActiveName == '6') {
        label = 'IP段'
      }
      return label
    }
  }
}
</script>

<style lang="less" scoped>
.recommendBox {
  width: 100%;
  height: 100%;
  background: #fff;
  .recommendHeader {
    // width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    // box-sizing: border-box;
    & > div {
      .el-input {
        width: 240px;
      }
      .el-select {
        width: 240px;
      }
      & > span {
        font-weight: 400;
        color: #2677ff;
        line-height: 20px;
        margin-left: 16px;
        cursor: pointer;
      }
    }
  }
  .tableWrapFilter {
    height: calc(100% - 180px) !important;
  }
  .tableWrap {
    height: calc(100% - 129px);
    padding: 0px 20px;
    .el-table {
      border: 0;
    }
  }
  /deep/.el-tabs__header {
    border-bottom: none !important;
    margin-top: 25px;
  }
  /deep/.el-tabs__nav {
    border-bottom: 1px solid #e4e7ed;
  }
  .table {
    height: calc(100% - 187px);
    margin-top: 20px;
  }
  .cluebox {
    display: flex;
    align-items: center;
    .clueboxtitle {
      margin-right: 10px;
    }
  }
}
/deep/.el-select__caret {
  line-height: 32px;
}
/deep/.el-dialog__body {
  height: 500px;
}
/deep/.myPagination {
  .el-pagination__jump {
    margin-right: 0px !important;
  }
  .el-pagination__total {
    left: 0px !important;
  }
}
.confirmBox {
  margin-left: 0px;
}
</style>
