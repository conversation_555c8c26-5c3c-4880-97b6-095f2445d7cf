<template>
  <div class="containerInfo">
    <div class="home_header">
      <div class="tableWrap">
        <div class="leftTab">
          <el-tooltip
            class="item"
            effect="dark"
            placement="top"
            :content="groupName"
            :open-delay="500"
          >
            <div class="leftTitle">
              {{ groupName }}
            </div>
          </el-tooltip>
          <div class="leftNum">
            <p>线索总数</p>
            <p>{{ leftNum }}</p>
          </div>
          <div v-for="(item, key) in left_data" :key="key">
            <div
              :class="'leftMain leftMain' + key"
              v-if="
                (!item.has_subdomain_tab || has_subdomain_tab == item.has_subdomain_tab) &&
                !(item.name == '10' && item.count == 0)
              "
            >
              <p>{{ item.label == 'IP段' ? 'IP' : item.label }}</p>
              <p>{{ item.count }}</p>
            </div>
          </div>
        </div>
        <div class="chartBox" v-loading="chartLoaing">
          <div class="rightWrap" id="container"> </div>
          <div class="rightWrap" id="container" v-if="mapResDataLength != 0"> </div>
          <div v-else-if="!chartLoaing"> </div>
        </div>
      </div>
      <div class="toopie">
        <div
          ><p
            style="
              background: linear-gradient(
                180deg,
                rgba(38, 118, 255, 1) 0%,
                rgba(38, 157, 255, 1) 100%
              );
            "
          ></p
          ><div>域名</div></div
        >
        <div
          ><p
            style="
              background: linear-gradient(
                180deg,
                rgba(19, 183, 255, 1) 0%,
                rgba(18, 227, 255, 1) 100%
              );
            "
          ></p
          ><div>证书</div></div
        >
        <div
          ><p
            style="
              background: linear-gradient(
                180deg,
                rgba(16, 213, 149, 1) 0%,
                rgba(16, 232, 210, 1) 100%
              );
            "
          ></p
          ><div>ICP</div></div
        >
        <div
          ><p
            style="
              background: linear-gradient(
                180deg,
                rgba(255, 121, 0, 1) 0%,
                rgba(255, 191, 0, 1) 100%
              );
            "
          ></p
          ><div>ICON</div></div
        >
        <div
          ><p
            style="
              background: linear-gradient(
                180deg,
                rgba(255, 70, 70, 1) 0%,
                rgba(255, 168, 168, 1) 100%
              );
            "
          ></p
          ><div>关键词</div></div
        >
        <div v-if="has_subdomain_tab == 1"
          ><p
            style="
              background: linear-gradient(
                180deg,
                rgba(224, 227, 34, 1) 0%,
                rgba(224, 168, 168, 1) 100%
              );
            "
          ></p
          ><div>子域名</div></div
        >
        <div
          ><p
            style="
              background: linear-gradient(
                180deg,
                rgba(39, 85, 153, 1) 0%,
                rgba(34, 111, 227, 1) 100%
              );
            "
          ></p
          ><div>IP段</div></div
        >
        <div v-if="fidItem.count != 0"
          ><p style="background: linear-gradient(180deg, #8a3cff 0%, #ae78ff 100%)"></p
          ><div>FID</div></div
        >
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { getCuleMapData, getCuleMapDataChildren } from '@/api/apiConfig/clue.js'

export default {
  props: ['groupId', 'leftData', 'groupName', 'has_subdomain_tab'],
  data() {
    return {
      mapResDataLength: 0,
      mapData: {},
      graph: null,
      leftNum: 0,
      chartLoaing: false,
      left_data: [],
      tmpFlag: false,
      fidItem: {}
    }
  },
  watch: {
    groupId(val) {
      if (val !== '') {
        this.getMapData()
      }
    },
    leftData: {
      handler(val) {
        this.left_data = val
        this.left_data.forEach((item) => {
          if (item.name == 10) {
            this.fidItem = item
          }
        })
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange', 'getterWebsocketMessage'])
  },
  methods: {
    init() {
      let _this = this
      const container = document.getElementById('container')
      this.graph = this.$echarts.init(container)
      let option = {
        label: {
          normal: {
            show: true
          }
        },
        tooltip: {
          show: true,
          formatter: function (params) {
            if (params.data.category == '-1') {
              return params.data.id
            } else if (params.data.category == '0') {
              return '域名：' + params.data.content
            } else if (params.data.category == '1') {
              return '证书：' + params.data.content
            } else if (params.data.category == '2') {
              return 'ICP：' + params.data.content
            } else if (params.data.category == '3') {
              return '<img src=' + _this.showSrcIp + params.data.content + '></img/>'
            } else if (params.data.category == '4') {
              return '关键词：' + params.data.content
            } else if (params.data.category == '5') {
              return '子域名：' + params.data.content
            } else if (params.data.category == '6') {
              return 'IP段：' + params.data.content
            } else if (params.data.category == '-2') {
              return 'IP'
            }
          }
        },
        animation: false,
        series: [
          {
            type: 'graph', //关系图
            layout: 'force', //力导向图的布局
            // symbolSize: 10,//节点大小
            roam: true,
            force: {
              layoutAnimation: false,
              repulsion: 500
            },
            draggable: true, //节点是否可以拖拽
            label: {
              normal: {
                show: true,
                color: 'rgba(38, 208, 255, 1)',
                formatter: '{b}',
                fontSize: 10,
                fontStyle: 400,
                position: 'inside'
              }
            },
            // focusNodeAdjacency: true,//鼠标悬停在节点上时，会隐藏和当前节点非直接连接的节点
            categories: [
              {
                //节点类别
                itemStyle: {
                  normal: {
                    color: '#009800',
                    border: 'red'
                  }
                },
                label: {
                  show: true
                }
              },
              {
                name: '-1',
                itemStyle: {
                  normal: {
                    color: '#ffff',
                    borderWidth: 2,
                    borderColor: {
                      // linear-gradient(135deg, rgba(38, 119, 255, 1) 0%, rgba(0, 0, 0, 1) 0%, rgba(38, 208, 255, 1) 100%)
                      type: 'radial',
                      x: 0.5,
                      y: 0.5,
                      r: 0.5,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(38, 119, 255, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(38, 208, 255, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                },
                label: {
                  show: false
                }
              },
              {
                name: '-2',
                itemStyle: {
                  normal: {
                    color: '#ffff',
                    borderWidth: 2,
                    borderColor: {
                      // linear-gradient(135deg, rgba(38, 119, 255, 1) 0%, rgba(0, 0, 0, 1) 0%, rgba(38, 208, 255, 1) 100%)
                      type: 'radial',
                      x: 0.5,
                      y: 0.5,
                      r: 0.5,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(38, 119, 255, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(38, 208, 255, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                },
                label: {
                  show: false
                }
              },
              {
                name: '0',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(38, 118, 255, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(38, 157, 255, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              },
              {
                name: '1',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(19, 183, 255, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(18, 227, 255, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              },
              {
                name: '2',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: ' rgba(16, 213, 149, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(16, 232, 210, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              },
              {
                name: '3',
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: 'rgba(255, 121, 0, 1)' // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: 'rgba(255, 191, 0, 1)' // 100% 处的颜色
                      }
                    ],
                    global: false // 缺省为 false
                  }
                }
              },
              {
                name: '4',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(255, 70, 70, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(255, 168, 168, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              },
              {
                name: '5',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(224, 227, 34, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(224, 168, 168, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              },
              {
                name: '6',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(39, 85, 153, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(34, 111, 227, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              }
            ],
            data: _this.mapData.nodes,
            links: _this.mapData.edges,
            lineStyle: {
              //关系连接线的样式设置
              normal: {
                opacity: 0.9, //关系连接线的不透明度为0.9
                width: 1, //关系连接线的宽度
                curveness: 0 //关系连接线的弯曲程度
              }
            }
          }
        ]
      }
      this.graph.setOption(option)
      this.chartLoaing = false
      window.addEventListener('resize', () => {
        this.graph.resize()
      })
      this.graph.on('click', (param) => {
        if (param.borderColor !== undefined) {
          return ''
        } else {
          if (param.data.category == -1) {
            _this.$message.error('单位数据已默认全部展开')
          } else if (param.data.is_show <= 0 || param.data.id == undefined) {
            _this.$message.error('此节点无子集数据')
          } else {
            // param.data.id.substr(0, param.data.id.length - 1) id随机拼接了一个数字，查询时要去掉
            _this.getChildrenData(param.data.id.substr(0, param.data.id.length - 1), param.data)
          }
        }
      })
    },
    async getMapData() {
      this.leftNum = 0
      this.chartLoaing = true
      let res = await getCuleMapData({
        group_id: this.groupId,
        data: { operate_company_id: this.currentCompany }
      })
      if (res.data.length > 0) {
        this.mapData = this.setMapData(res.data)
        // this.$nextTick(() => {
        this.init()
        // })
      } else {
        this.mapResDataLength = 0
        this.chartLoaing = false
      }
      this.left_data.map((v) => {
        this.leftNum = this.leftNum + v.count
      })
    },
    refreshDragedNodePosition(e) {
      const model = e.item.get('model')
      model.fx = e.x
      model.fy = e.y
    },
    // 处理接口数据
    setMapData(data) {
      let datanodesTmp = []
      let dataedgesTmp = []
      data.map((v, index) => {
        if (v.name != null && v.children.length > 0) {
          datanodesTmp.push({ id: v.name, category: '-1', symbolSize: 25, name: '单位' })
          v.children.map((item) => {
            // 是否是ip
            let obj = {}
            if (item.type == 5) {
              item.symbolSize = 15
            } else {
              item.symbolSize = 15
            }
            item.id = String(item.id) + index // id有重复造成报错，添加一个随机值index
            //  item.symbolSize=15
            item.category = String(item.type)
            datanodesTmp.push(item)
            if (item.is_ip == 1) {
              obj = { ...item }
              obj.id = obj.id + 'is_ip'
              obj.category = '-2'
              obj.name = 'IP'
              datanodesTmp.push(obj)
              // 先连接父级 然后链接子集
              dataedgesTmp.push({ source: v.name, target: String(obj.id) })
              dataedgesTmp.push({ source: obj.id, target: String(item.id) })
            } else {
              dataedgesTmp.push({ source: v.name, target: String(item.id) })
            }
          })
        }
      })
      return { nodes: datanodesTmp, edges: dataedgesTmp }
    },
    // 处理子集数据
    setMapDataChildren(data, parent) {
      data.map((v) => {
        // 是否是ip
        let obj = {}
        if (v.type == 5) {
          v.symbolSize = 20
        } else {
          v.symbolSize = 15
        }
        v.category = String(v.type)
        v.id = String(v.id)
        this.mapData.nodes.push(v)
        if (v.is_ip == 1) {
          obj = { ...v }
          obj.id = obj.id + 'is_ip'
          obj.category = '-2'
          obj.name = 'IP'
          this.mapData.nodes.push(obj)
          // 先连接父级 然后链接子集
          this.mapData.edges.push({ source: parent.id, target: String(obj.id) })
          this.mapData.edges.push({ source: obj.id, target: String(v.id) })
        } else {
          this.mapData.edges.push({ source: parent.id, target: String(v.id) })
        }
      })
      this.graph.setOption({
        series: [
          {
            data: this.mapData.nodes,
            links: this.mapData.edges
          }
        ]
      })
      this.chartLoaing = false
    },
    // 获取子集数据
    async getChildrenData(id, parent) {
      this.chartLoaing = true
      let res = await getCuleMapDataChildren({
        id,
        data: { group_id: this.groupId, operate_company_id: this.currentCompany }
      })
      if (res.data.length != 0) {
        let obj = res.data[0]
        this.mapData.nodes.map((v) => {
          if (v.id == String(obj.id)) {
            this.$message.error('当前节点已展开')
            this.tmpFlag = true
            this.chartLoaing = false
          }
        })
        if (!this.tmpFlag) {
          this.setMapDataChildren(res.data, parent)
        } else {
          this.chartLoaing = false
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getMapData()
      // this.setMapData()
      this.left_data = this.leftData
    })
  }
}
</script>
<style lang="less" scoped>
.containerInfo /deep/ {
  position: relative;
  width: 100%;
}
/deep/.home_header {
  position: relative;
  height: 100%;
}
.tableWrap {
  height: 100%;
  display: flex;
  justify-content: space-between;
  .leftTab {
    width: 17%;
    height: 100%;
    background: #ffff;
    padding: 16px;
    box-sizing: border-box;
  }
  .chartBox {
    width: 82%;
    height: 100%;
    background: #ffff;
    position: relative;
  }
}
.rightWrap {
  width: 100%;
  height: 100%;
}
/deep/ .g6-component-toolbar {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 25px;
  border: none;
}
/deep/ .g6-component-toolbar > li:nth-child(1) {
  display: none;
}
/deep/ .g6-component-toolbar > li:nth-child(2) {
  display: none;
}
/deep/ .g6-component-toolbar > li:nth-child(5) {
  display: none;
}
/deep/ .g6-component-toolbar > li:nth-child(6) {
  display: none;
}
.noneBox {
  display: none;
}
.toopie {
  width: 118px;
  // height: 224px;
  position: absolute;
  bottom: 16px;
  right: 16px;
  z-index: 999;
  border-radius: 4px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(233, 235, 239, 1);
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
  div {
    width: 100%;
    height: 37px;
    display: flex;
    box-sizing: border-box;
    // padding: 8px;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;

    p {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 5px;
      position: relative;
      left: 16px;
    }
    div {
      position: relative;
      left: 22px;
    }
  }
}
.leftTitle {
  width: 100%;
  font-size: 14px;
  font-weight: 500;
  color: rgba(55, 57, 60, 1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.leftNum {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid rgba(227, 238, 255, 1);
  border-radius: 2px;
  background: linear-gradient(90deg, rgba(38, 119, 255, 0.12) 0%, rgba(38, 119, 255, 0.06) 100%);
  box-sizing: border-box;
  padding: 8px;
  margin-top: 16px;
}
.leftNum > p:nth-child(1) {
  font-size: 14px;
  font-weight: 400;
  color: rgba(55, 57, 60, 1);
}
.leftNum > p:nth-child(2) {
  font-size: 14px;
  font-weight: 500;
  color: rgba(38, 119, 255, 1);
}
.leftMain {
  display: flex;
  height: 14px;
  justify-content: space-between;
  margin-top: 19px;
  display: flex;
  align-items: center;
}
.leftMain > p:nth-child(1) {
  margin-left: 8px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(98, 102, 108, 1);
}
.leftMain > p:nth-child(2) {
  margin-left: 8px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(55, 57, 60, 1);
}
.leftMain0 {
  border-left: solid rgba(38, 157, 255, 1) 4px;
}
.leftMain1 {
  border-left: solid rgba(18, 227, 255, 1) 4px;
}
.leftMain2 {
  border-left: solid rgba(16, 232, 210, 1) 4px;
}
.leftMain3 {
  border-left: solid rgba(255, 191, 0, 1) 4px;
}
.leftMain4 {
  border-left: solid rgba(255, 168, 168, 1) 4px;
}
.leftMain6 {
  border-left: solid rgba(34, 111, 227, 1) 4px;
}
.leftMain5 {
  border-left: solid rgba(224, 227, 34, 1) 4px;
}
.leftMain7 {
  border-left: solid #8a3cff 4px;
}
</style>
