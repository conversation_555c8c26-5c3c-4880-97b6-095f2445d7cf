<template>
  <div class="container">
    <div class="headerTitleTmp">
      <span>企业线索库</span>
      <span style="cursor: pointer" @click="collapsClick" class="collapsBox"
        >{{ collapseFlag ? '收起统计信息' : '展开统计信息'
        }}<i :class="collapseFlag ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i
      ></span>
    </div>
    <div class="statisticsBox" v-if="collapseFlag" v-loading="chartLoaing">
      <div class="companyBox">
        <div class="companyDetailBox">
          <div class="companyNameImage">
            <span v-if="companyDataCharts && companyDataCharts.company_name" class="companyName"
              >{{ companyDataCharts.company_name
              }}<i
                class="el-icon-search"
                @click="searchCompany(companyDataCharts && companyDataCharts.company_name)"
              ></i
            ></span>
          </div>
          <div class="companyIntroduction">
            <p>{{
              !companyDataCharts || (companyDataCharts && companyDataCharts.company_introduce == '')
                ? '暂无简介'
                : '简介：' + companyDataCharts && companyDataCharts.company_introduce
            }}</p>
          </div>
          <div class="companyImgBox"></div>
        </div>
        <div class="companyChartsBox">
          <div class="chartsBoxTitle chartTitleOne">
            <div style="display: flex; align-items: center">
              <img src="../../assets/images/companyClue/holdingCompany.png" alt="" />
              <div class="companyText">
                <p>控股企业</p>
                <p>{{ companyProgressAllValue.length }}</p>
              </div>
            </div>
            <div class="viewMoreBox" @click="viewMore(1)">查看更多</div>
          </div>
          <div class="companyChartRanking">
            <div class="rankingProgress" v-for="(item, key) in companyProgressValue" :key="key">
              <div class="progressNum">
                <div style="display: flex" class="progressNumTmp"
                  ><p :class="'p' + (key + 1)">{{ key + 1 }}</p>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    placement="top"
                    :content="item.name"
                    :open-delay="500"
                  >
                    <div class="numNameBox companyTextBoxTmp">{{ item.name }}</div>
                  </el-tooltip>
                  <div>{{ transferRate(item.percent) }}</div>
                  <businessTagVue
                    v-if="item.reg_status"
                    :reg_status="item.reg_status"
                  ></businessTagVue>
                </div>
              </div>
              <div class="numExprogress">
                <el-progress
                  :percentage="Number(item.percent ? item.percent.replace('%', '') : 0)"
                  :stroke-width="6"
                  :show-text="false"
                  :color="item.color"
                ></el-progress>
              </div>
            </div>
          </div>
        </div>
        <div class="companyChartsBox">
          <div class="chartsBoxTitle chartTitleTwo">
            <div style="display: flex; align-items: center">
              <img src="../../assets/images/companyClue/branchOffice.png" alt="" />
              <div class="companyText">
                <p>分支机构</p>
                <p>{{ companySurviveAllValue.length }}</p>
              </div>
            </div>
            <div class="viewMoreBox" @click="viewMore(2)">查看更多</div>
          </div>
          <div class="companyChartRanking">
            <div class="mechanismBox" v-for="(item, key) in companySurviveValue" :key="key">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                :content="item.name"
                :open-delay="500"
              >
                <div>{{ item.name }}</div>
              </el-tooltip>
              <p :class="setClass(item.status)">{{ item.status }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="pieChartsBox">
        <div class="cueState pieChartClueBox">
          <div class="pieTitle"> 线索状态 </div>
          <div class="pieMainBox">
            <div class="mainPieData" v-for="(item, key) in statusProssValue" :key="key">
              <div class="dataLeft">
                <div class="leftBoxTit"
                  ><div :class="'boxStatus boxStatus' + (key + 1)"></div
                  ><span>{{ item.name }}</span></div
                >
                <p class="pOne">占比</p>
                <p class="pTwo">{{ item.label + '%' }}</p>
              </div>
              <div class="dataRight">
                <el-progress
                  type="circle"
                  :show-text="false"
                  :percentage="Number(item.label ? item.label.replace('%', '') : 0)"
                  :width="65"
                  :color="item.color"
                >
                </el-progress>
                <p :class="setValueClass(item.value)">{{ item.value }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="cueSource pieChartClueBox">
          <div class="pieTitle"> 线索来源 </div>
          <div class="pieMainBox">
            <div class="mainPieData" v-for="(item, key) in sourceProssValue" :key="key">
              <div class="dataLeft">
                <div class="leftBoxTit"
                  ><div :class="'boxStatus boxStatus' + (key + 4)"></div
                  ><span>{{ item.name }}</span></div
                >
                <p class="pOne">占比</p>
                <p class="pTwo">{{ item.label + '%' }}</p>
              </div>
              <div class="dataRight">
                <el-progress
                  type="circle"
                  :show-text="false"
                  :percentage="Number(item.label ? item.label.replace('%', '') : 0)"
                  :width="65"
                  :color="item.color"
                ></el-progress>
                <p :class="setValueClass(item.value)">{{ item.value }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home_header">
      <div
        :class="
          clueMapFlag
            ? 'confirmBox confirmBoxListPic confirmBoxListPicTmp1'
            : 'confirmBox confirmBoxListPic confirmBoxListPicTmp'
        "
      >
        <el-radio-group v-model="listOrPic" @change="handleListOrPicClick">
          <el-radio-button :label="0"><i></i>列表</el-radio-button>
          <el-radio-button :label="1">图谱</el-radio-button>
        </el-radio-group>
      </div>
      <div v-if="!clueMapFlag" class="home_headerTmp">
        <div class="groupClass">
          <p class="groupHeader1">
            <el-tooltip content="场景管理">
              <span class="title">场景管理</span>
            </el-tooltip>
            <span class="checkoutAll">
              <el-tooltip content="全选">
                <el-checkbox v-model="isCheckedAllGroup"> 全选 </el-checkbox>
              </el-tooltip>
            </span>
            <i class="iconClass el-icon-delete" @click="groupRemove('more')" id="cloud_clue_delete"
              ><span class="icon-text">删除</span></i
            >
            <i class="iconClass el-icon-plus" @click="insertShow" id="cloud_clue_add"
              ><span class="icon-text">新增</span></i
            >
          </p>
          <div v-if="groupArr.length == 0" class="emptyClass">
            <div>
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无场景</p>
            </div>
          </div>
          <!-- @change="collapseChange" -->
          <el-collapse
            v-else
            v-model="currentGroupIds"
            accordion
            style="padding: 0px 8px; border: 0; height: calc(100% - 51px); overflow: auto"
            v-loading="groupLoading"
            @change="collapseChange"
          >
            <el-collapse-item
              :name="String(item.id)"
              v-for="(item, index) in groupArr"
              :key="item.id"
            >
              <div
                slot="title"
                class="nameClass"
                @mouseenter="enterTitle(index)"
                @mouseleave="leaveTitle(index)"
              >
                <el-checkbox
                  v-model="item.isChecked"
                  class="checkoutOne"
                  :key="index"
                  @click.stop.prevent.native="changeChecked(item, index, item.isChecked)"
                ></el-checkbox>
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="top"
                  :content="`${item.name}(${item.num})`"
                  :disabled="item.name.length <= 5"
                  :open-delay="500"
                >
                  <span>{{ `${item.name}(${item.num})` }}</span>
                </el-tooltip>
                <el-dropdown
                  trigger="click"
                  class="dropdownMore"
                  @click.native="dropdownClick"
                  :placement="'bottom'"
                >
                  <span class="el-dropdown-link">
                    <i class="el-icon-more"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      icon="el-icon-edit-outline"
                      @click.native="exportMore(item)"
                      id="cloud_clue_edit"
                      >重命名</el-dropdown-item
                    >
                    <el-dropdown-item
                      icon="el-icon-delete"
                      @click.native="groupRemove('one', item.id)"
                      id="cloud_clue_del"
                      v-if="index > 0"
                      >删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div
                v-for="(val, ind) in item.tab_num"
                :key="ind"
                class="collapseType"
                @click="groupClick(item.id, item.is_detect_assets_tasks, val.value, index)"
              >
                <div
                  :style="
                    item.isHighlighted && val.value == tabActiveNameStatus ? 'color:#2677FF' : ''
                  "
                >
                  <span
                    :class="
                      item.isHighlighted && val.value == tabActiveNameStatus
                        ? 'fangkuai fangkuaiblue'
                        : 'fangkuai'
                    "
                  ></span>
                  {{ val.label }}
                </div>
                <div
                  class="collapseCount"
                  :style="
                    item.isHighlighted && val.value == tabActiveNameStatus ? 'color:#2677FF' : ''
                  "
                  >({{ val.count }})</div
                >
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div class="clueWrap">
          <div style="height: 64px"></div>
          <el-row :gutter="20" style="padding: 0 16px; margin: 0">
            <el-col :span="6">
              <div class="grid-content">
                <p class="title">扩展记录</p>
                <div class="expandOver"
                  ><span style="cursor: pointer" @click="expandFinish"
                    ><i class="el-icon-success"></i>扩展完成</span
                  ><span>{{ extendNum }}</span></div
                >
                <div v-if="toDoList" class="loadingList">
                  <p class="title"
                    >扩展中
                    <span>{{ parseFloat(lineProgress) }}%</span>
                  </p>
                  <p>
                    <el-progress
                      :stroke-width="8"
                      :percentage="parseFloat(handleData(lineProgress))"
                      color="#2677FF"
                      :show-text="false"
                    ></el-progress>
                  </p>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="grid-content">
                <p class="title">单个扩展</p>
                <div class="singleExpand">
                  <el-input
                    :placeholder="clueTypePlaceholder"
                    v-model="ruleForm.clueContent"
                    class="input-with-select"
                  >
                    <el-select
                      v-model="ruleForm.clueType"
                      slot="prepend"
                      placeholder="请选择"
                      id="cloud_select"
                    >
                      <!-- // 此处不支持ICON和子域名扩展 -->
                      <el-option
                        v-for="item in tabData.filter((to) => {
                          return to.name != '3' && to.name != '5' && to.name != '10'
                        })"
                        :key="item.name"
                        :label="item.label"
                        :value="item.name"
                      ></el-option>
                    </el-select>
                  </el-input>
                </div>
                <div class="bot">
                  <span></span>
                  <el-button
                    class="normalBtnSmall"
                    @click.native="batchAddClueFunOne()"
                    id="cloud_ignore"
                    >扩展</el-button
                  >
                </div>
                <img
                  class="imgBot"
                  src="../../assets/images/clue1.png"
                  width="90"
                  height="90"
                  alt=""
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="grid-content">
                <p class="title">批量新增</p>
                <el-select style="width: 100%" v-model="clueRuleForm.way" placeholder="请选择方式">
                  <el-option label="手动输入" :value="0"></el-option>
                  <el-option label="从文件导入" :value="1"></el-option>
                </el-select>
                <div class="bot">
                  <el-checkbox v-model="clueRuleForm.is_auto_expend">开启自动扩展</el-checkbox>
                  <el-button class="normalBtnSmall" @click="insertClueShow" id="cloud_ignore"
                    >新增</el-button
                  >
                </div>
                <img
                  class="imgBot"
                  src="../../assets/images/clue2.png"
                  width="90"
                  height="90"
                  alt=""
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="grid-content">
                <p class="title">企业关系查询</p>
                <el-input
                  v-model="companyName"
                  placeholder="请输入企业名称全称进行搜索"
                  @keyup.enter.native="companyInfoShow"
                  id="cloud_company_enter"
                ></el-input>
                <div class="bot">
                  <span></span>
                  <el-button class="normalBtnSmall" @click="companyInfoShow" id="cloud_ignore"
                    >查询</el-button
                  >
                </div>
                <img
                  class="imgBot"
                  src="../../assets/images/clue3.png"
                  width="90"
                  height="90"
                  alt=""
                />
              </div>
            </el-col>
          </el-row>
          <div class="filterTab">
            <div> </div>
            <div>
              <el-button
                class="normalBtnRe"
                type="primary"
                v-if="user.role == 2"
                @click="noteBlackList"
                :loading="noteLoading"
                id="cloud_note"
                >标记到黑名单</el-button
              >
              <el-button
                class="normalBtnRe"
                type="primary"
                v-if="user.role == 2"
                @click="editCompanyName('more')"
                id="cloud_export"
                >批量编辑</el-button
              >
              <el-button
                class="normalBtnRe"
                type="primary"
                @click="exportList"
                :loading="exportLoading"
                id="cloud_export"
                >导出</el-button
              >
              <el-button
                class="normalBtnRe"
                type="primary"
                @click="removeMore"
                :loading="removeLoading"
                id="cloud_del"
                >删除</el-button
              >
              <el-button
                class="normalBtn"
                type="primary"
                v-if="tabActiveNameStatus != 2"
                @click.native="clueConfirmSave(2)"
                id="cloud_ignore"
                >忽略</el-button
              >
              <el-button
                class="normalBtn"
                type="primary"
                v-if="tabActiveNameStatus != 1"
                @click.native="clueConfirmSave(1)"
                id="cloud_confirm"
                >确认</el-button
              >
              <!-- // 已确认线索,非子域名线索有扩展线索功能 -->
              <el-button
                v-if="tabActiveNameStatus == 1 && tabActiveName != 5"
                class="normalBtn"
                type="primary"
                :loading="batchLoading"
                @click="batchAddClueFun"
                id="cloud_expands"
                >扩展线索</el-button
              >
              <el-button
                v-if="tabActiveNameStatus == 1"
                class="normalBtn"
                type="primary"
                :loading="recommentLoading"
                @click="goRecomment"
                id="cloud_recommend"
                >推荐资产</el-button
              >
              <el-button
                v-if="tabActiveNameStatus == 1 && tabActiveName == 4"
                class="normalBtn"
                type="primary"
                @click="goChain('more')"
                id="cloud_chain"
                >关联</el-button
              >
              <el-button
                v-if="tabActiveNameStatus == 0"
                class="normalBtn"
                type="primary"
                :loading="recommentLoading"
                @click.native="clueConfirmSave(3)"
                id="cloud_recommend"
                >供应链标记</el-button
              >
              <!-- <el-button v-if="tabActiveNameStatus == 0" class="normalBtn" type="primary" :loading="recommentLoading" @click.native="clueConfirmSave(4)">ICP盗用标记</el-button> -->
            </div>
          </div>
          <div
            style="
              display: flex;
              justify-content: space-between;
              padding: 0 20px;
              margin-bottom: 20px;
            "
          >
            <div class="confirmBox">
              <el-radio-group v-model="tabActiveName" @change="handleClick(tabActiveName)">
                <!-- 子域名展示有权限控制 fid 数量为0 不展示该tab -->
                <el-radio-button
                  :label="item.name"
                  v-for="(item, index) in tabData.filter((item) => {
                    return (
                      (!item.has_subdomain_tab ||
                        this.has_subdomain_tab == item.has_subdomain_tab) &&
                      !(item.name == '10' && item.count == 0)
                    )
                  })"
                  :key="index"
                  >{{ item.label }}({{ item.count }})</el-radio-button
                >
              </el-radio-group>
            </div>
            <div
              style="width: 34%; display: flex; justify-content: space-between; align-items: center"
            >
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAllObj[checkedAllArr[tabActiveName]]"
                @change="checkAllChange"
                >选择全部</el-checkbox
              >
              <el-input
                style="width: 75%"
                v-if="tabActiveName != 3"
                @keyup.enter.native="getClueData('tmp')"
                v-model="keywordObj[tabKeywordArr[tabActiveName]]"
                :placeholder="`请输入${getLabel()}或企业名称检索`"
                clearable
                id="cloud_keycheck"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="getClueData('tmp')"
                ></el-button>
              </el-input>
            </div>
          </div>
          <div
            :class="collapseFlag == true ? 'tableWrap' : 'tableWrapTwo'"
            v-loading="loading"
            style="padding: 0px 20px"
          >
            <el-table
              border
              :data="tableData"
              row-key="id"
              :header-cell-style="{ background: '#F5F7FA', color: '#62666C' }"
              @selection-change="handleSelectionChange"
              :ref="'eltable' + tabActiveName"
              height="100%"
              style="width: 100%"
            >
              <template slot="empty">
                <div class="emptyClass">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-kong"></use>
                  </svg>
                  <p>暂无数据</p>
                </div>
              </template>
              <el-table-column
                type="selection"
                align="center"
                :reserve-selection="true"
                :selectable="handleSelectable"
                width="55"
              >
              </el-table-column>
              <el-table-column
                v-for="item in tableHeader"
                :key="item.id"
                :prop="item.name"
                align="left"
                :min-width="item.minWidth"
                :fixed="item.fixed"
                :label="item.label"
              >
                <template slot="header">
                  <span v-if="item.name == 'content'">{{ getLabel() }}</span>
                  <span v-else>{{ item.label }}</span>
                </template>
                <template slot-scope="scope">
                  <span>
                    <span v-if="item.name == 'status'">{{ getStatus(scope.row[item.name]) }}</span>
                    <span v-else-if="item.name == 'clue_company_name'">
                      <!-- <span v-if="scope.row['type'] != 3"> -->
                      <el-tooltip
                        class="item"
                        effect="light"
                        placement="top"
                        :open-delay="500"
                        v-if="scope.row[item.name]"
                      >
                        <div slot="content">
                          <span style="padding: 2px 0px; display: inline-block">{{
                            scope.row[item.name] ? scope.row[item.name] : '-'
                          }}</span>
                          <span
                            v-if="scope.row['equity_percent'] && scope.row['equity_percent'] != '-'"
                            class="kgBox"
                          >
                            <el-tag>
                              <span style="color: #37393c">
                                控股比例：{{ scope.row['equity_percent'] }}%
                              </span>
                            </el-tag>
                          </span>
                        </div>
                        <span>
                          <span style="padding: 2px 0px; display: inline-block">{{
                            scope.row[item.name] ? scope.row[item.name] : '-'
                          }}</span>
                          <span
                            v-if="scope.row['equity_percent'] && scope.row['equity_percent'] != '-'"
                            class="kgBox"
                          >
                            <el-tag>
                              <span style="color: #37393c">
                                控股比例：{{ scope.row['equity_percent'] }}%
                              </span>
                            </el-tag>
                          </span>
                        </span>
                      </el-tooltip>
                      <span v-else style="padding: 2px 0px; display: inline-block">-</span>
                      <!-- </span> -->
                      <!-- <span v-else style="padding:2px 0px;display: inline-block;">-</span> -->
                    </span>
                    <span v-else-if="item.name == 'content'">
                      <span v-if="scope.row[item.name]" style="display: flex; align-items: center">
                        <!-- 未确认线索展示新增线索 -->
                        <el-tooltip class="item" effect="light" placement="top" :content="'新增'">
                          <i v-if="scope.row.status == 0 && isNew" class="redStatus"></i>
                        </el-tooltip>
                        <!-- ICON展示 -->
                        <el-image
                          @click="clickevaluatePicture(scope.row[item.name])"
                          :preview-src-list="evaluatePictureList"
                          v-if="tabActiveName == '3'"
                          class="imgwrap"
                          :src="
                            scope.row[item.name].includes('http')
                              ? imgUrl + scope.row[item.name]
                              : showSrcIp + scope.row[item.name]
                          "
                          alt=""
                        >
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                          </div>
                        </el-image>
                        <span v-if="tabActiveName == '3'">{{ scope.row.hash }}</span>
                        <!-- 中文域名解码展示,非域名字段解码后保持不变 -->
                        <el-tooltip class="item" effect="light" placement="top">
                          <div slot="content">
                            {{
                              $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '')
                            }}{{
                              scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                            }}
                          </div>
                          <span v-if="tabActiveName != '3'" class="contentClass">{{
                            $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '-')
                          }}</span>
                        </el-tooltip>
                        <!-- ICON和证书(tabActiveName:1、需要展示对应线索在fofa上的资产数量3) -->
                        <el-tooltip class="item" effect="light" placement="top" :open-delay="500">
                          <span slot="content">
                            <span>此线索在FOFA资产数量</span>：{{
                              scope.row.fofa_assets_num && scope.row.fofa_assets_num.num
                            }}
                            <span
                              v-loading="scope.row.fofaLoading"
                              v-if="scope.row.fofaLoading"
                              style="color: #409eff"
                              >去FOFA查看</span
                            >
                            <a
                              v-if="
                                String(
                                  scope.row.fofa_assets_num && scope.row.fofa_assets_num.fofa_url
                                ).includes('http') && !scope.row.fofaLoading
                              "
                              style="color: #409eff"
                              :href="
                                String(
                                  scope.row.fofa_assets_num && scope.row.fofa_assets_num.fofa_url
                                )
                              "
                              target="_blank"
                              >去FOFA查看</a
                            >
                          </span>
                          <!-- 企业没有fofa查询权限 -->
                          <img
                            v-if="
                              tabActiveName == '3' ||
                              tabActiveName == '1' ||
                              tabActiveName == '4' ||
                              tabActiveName == '6'
                            "
                            class="fofaImg"
                            src="../../assets/images/fofasmall.svg"
                            @mouseenter="getFofaNumV1(scope.row, scope.$index)"
                            alt=""
                          />
                        </el-tooltip>
                        <!-- 域名展示whois信息 -->
                        <el-tooltip
                          class="item"
                          popper-class="chainClass"
                          effect="light"
                          placmeent="top"
                          :open-delay="500"
                        >
                          <span v-loading="whoisLoading" slot="content">
                            <span>whois信息</span>：
                            <p v-for="(v, index) in whiosHeader" :key="index">{{
                              v.label + (whoisInfo[v.name] || '-')
                            }}</p>
                          </span>
                          <img
                            v-if="tabActiveName == '0'"
                            src="../../assets/images/whois.svg"
                            class="whoisImg"
                            @mouseenter="getWhois(scope.row, scope.$index)"
                            alt=""
                          />
                        </el-tooltip>
                        <!-- 已确认线索展示扩展状态 -->
                        <i
                          v-if="scope.row.status == 1 && scope.row.is_expand == 1"
                          class="greenLine"
                          >已扩展</i
                        >
                        <i v-if="scope.row.status == 1 && scope.row.is_expand != 1" class="grayLine"
                          >未扩展</i
                        >
                        <!-- <span class="originLine" v-if="scope.row['is_fake_icp'] == 1">ICP盗用</span> -->
                      </span>
                      <span v-else>-</span>
                    </span>
                    <!-- 证据链展示;若存在中文域名，解码展示; chain_list只有一条数据，代表只有线索本身，相当于没有线索链，就直接按没数据处理-->
                    <span v-else-if="item.name == 'chain_list'">
                      <span v-if="scope.row[item.name] && scope.row[item.name].length > 1">
                        <el-tooltip
                          class="item"
                          effect="light"
                          placement="top"
                          popper-class="chainClass"
                        >
                          <div slot="content" style="position: relative">
                            <el-tooltip
                              effect="light"
                              class="item"
                              placement="top"
                              content="一键复制"
                              v-if="scope.row[item.name] && scope.row[item.name].length != 0"
                              :open-delay="500"
                            >
                              <i
                                class="el-icon-document-copy"
                                @click="copyClick(scope.row[item.name])"
                                style="
                                  color: #2677ff;
                                  cursor: pointer;
                                  position: absolute;
                                  right: -6px;
                                  top: 0;
                                "
                              ></i>
                            </el-tooltip>
                            <span
                              v-for="(con, index) in getChains(scope.row[item.name])"
                              :key="index"
                            >
                              <span v-if="con.type && con.type == 3">
                                <el-image
                                  :src="
                                    con.content.includes('http')
                                      ? con.content
                                      : showSrcIp + con.content
                                  "
                                >
                                  <div slot="error" class="image-slot">
                                    <i class="el-icon-picture-outline"></i>
                                  </div>
                                </el-image>
                              </span>
                              <span v-else
                                >{{ $punyCode.toUnicode(con.content || con)
                                }}{{
                                  con.punycode_domain ? '(' + con.punycode_domain + ')' : ''
                                }}</span
                              >
                              <i
                                v-if="index < getChains(scope.row[item.name]).length - 1"
                                class="el-icon-right iconRight"
                              ></i>
                            </span>
                          </div>
                          <span style="display: flex !important; align-items: center">
                            {{ scope.row[item.name].length - 1 }}
                            <img
                              src="../../assets/images/chain.svg"
                              alt=""
                              style="width: 12px; margin-left: 5px; vertical-align: middle"
                            />
                          </span>
                        </el-tooltip>
                      </span>
                      <span v-else>-</span>
                    </span>
                    <span v-else>
                      <el-tooltip
                        v-if="scope.row[item.name]"
                        class="item"
                        effect="light"
                        placement="top"
                        :open-delay="500"
                      >
                        <div slot="content">{{ scope.row[item.name] }}</div>
                        <span>{{ scope.row[item.name] }}</span>
                      </el-tooltip>
                      <span v-else>-</span>
                    </span>
                  </span>
                </template>
              </el-table-column>
              <!-- 已确认的关键词可以关联到关键词管理 -->
              <el-table-column
                v-if="tabActiveNameStatus == 1 && tabActiveName == 4"
                fixed="right"
                label="操作"
                align="left"
                width="100"
              >
                <template slot-scope="scope">
                  <!-- v-if="!scope.row.is_relate_keyword" -->
                  <el-button
                    v-if="user.role == 2"
                    type="text"
                    size="small"
                    @click="editCompanyName('one', scope.row.id)"
                    id="cloud_chain"
                    >编辑</el-button
                  >
                  <el-button
                    type="text"
                    size="small"
                    @click="goChain('one', scope.row.content)"
                    id="cloud_chain"
                    >关联</el-button
                  >
                </template>
              </el-table-column>
              <el-table-column
                v-else-if="user.role == 2"
                fixed="right"
                label="操作"
                align="left"
                width="50"
              >
                <template slot-scope="scope">
                  <!-- v-if="!scope.row.is_relate_keyword" -->
                  <el-button
                    type="text"
                    size="small"
                    @click="editCompanyName('one', scope.row.id)"
                    id="cloud_chain"
                    >编辑</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="pageSizeArr"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
      <div class="home_headerTmp" v-if="clueMapFlag"
        ><clueMap
          :groupId="currentGroupId"
          :has_subdomain_tab="has_subdomain_tab"
          :leftData="tabData"
          :groupName="currentGroupName"
        ></clueMap
      ></div>
      <!-- 线索分组名称 -->
      <el-dialog
        class="elDialogAddLiji"
        :close-on-click-modal="false"
        :visible.sync="dialogChartVisible"
        width="500px"
      >
        <template slot="title">
          <span>{{ chartFlag == 1 ? '控股企业' : '分支机构' }}</span>
        </template>
        <!-- 控股企业 -->
        <div class="dialog-body" v-if="chartFlag == 1">
          <div class="companyChartRanking companyChartRanking1">
            <div
              class="rankingProgress rankingProgress1"
              v-for="(item, key) in companyProgressAllValue"
              :key="key"
            >
              <div class="progressNum">
                <div style="display: flex"
                  ><p class="p1">{{ key + 1 }}</p
                  ><span class="numNameBox">{{ item.name }}</span>

                  <businessTagVue
                    v-if="item.reg_status"
                    :reg_status="item.reg_status"
                  ></businessTagVue
                ></div>
                <div>{{ transferRate(item.percent) }}</div>
              </div>
              <div class="numExprogress">
                <el-progress
                  :percentage="Number(item.percent ? item.percent.replace('%', '') : 0)"
                  :stroke-width="6"
                  :show-text="false"
                  color="rgba(255, 70, 70, 1)"
                ></el-progress>
              </div>
            </div>
          </div>
        </div>
        <!-- 分支机构 -->
        <div class="dialog-body" v-else>
          <div class="companyChartRanking companyChartRanking1">
            <div class="mechanismBox" v-for="(item, key) in companySurviveAllValue" :key="key">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                :content="item.name"
                :open-delay="500"
              >
                <div class="companyTextBox">{{ item.name }}</div>
              </el-tooltip>
              <p :class="setClass(item.status)">{{ item.status }}</p>
            </div>
          </div>
        </div>
      </el-dialog>
      <!-- 线索分组名称 -->
      <el-dialog
        class="elDialogAddLiji"
        :close-on-click-modal="false"
        :visible.sync="dialogFormVisibleGroup"
        width="500px"
      >
        <template slot="title">
          <span v-if="addIsTrue">新建</span>
          <span v-if="!addIsTrue">编辑</span>
        </template>
        <div class="dialog-body" style="height: 100px">
          <el-form
            style="padding: 0 !important"
            :model="groupForm"
            :rules="groupFormrules"
            ref="groupForm"
            label-width="120px"
            class="demo-ruleForm"
          >
            <el-form-item label="场景名称" prop="name">
              <el-input v-model="groupForm.name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button
            class="highBtnRe"
            @click="dialogFormVisibleGroup = false"
            id="cloud_clue_cancel"
            >取消</el-button
          >
          <el-button
            class="highBtn"
            :loading="btnLoading"
            @click="exportMoreSave"
            id="cloud_clue_sure"
            >确定</el-button
          >
        </div>
      </el-dialog>
      <!-- 新建线索 -->
      <cluedialogs
        :dialogFormVisibleInsert="dialogFormVisibleInsert"
        :has_subdomain_tab="has_subdomain_tab"
        :currentGroupId="currentGroupId"
        :tabActiveName="tabActiveName"
        :ruleForm="clueRuleForm"
        @insertClueSave="insertClueSave"
      />
      <!-- 企业信息 -->
      <conpanyRelation
        :dialogFormVisibleCompany="dialogFormVisibleCompany"
        :relationData="relationData"
        @closeFun="closeFun"
      />
      <el-dialog
        class="elDialogAdd"
        :close-on-click-modal="false"
        :visible.sync="chainDialog"
        width="500px"
        title="提示"
      >
        <div class="dialog-body">
          <p class="infoKeyword">是否添加到数据泄漏关键词列表中?</p>
          <el-form
            :model="chainRuleForm"
            :rules="chainRules"
            ref="chainRuleForm"
            style="padding: 0 !important"
            label-width="100px"
            class="demo-ruleForm"
          >
            <el-form-item label="关键词类型" prop="type" placeholder="请选择">
              <el-select v-model="chainRuleForm.type">
                <el-option
                  v-for="item in typeArr"
                  :label="item.name"
                  :key="item.id"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button class="highBtnRe" @click="chainDialog = false">关闭</el-button>
          <el-button class="highBtn" :loading="tiquLoading" @click="chainSave('chainRuleForm')"
            >确定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import { mapGetters, mapState, mapMutations } from 'vuex'
import conpanyRelation from '../cloudRecommend/companyRelation.vue'
import clueMap from './clueMap.vue'
import tree from '../../components/tree/src/tree'
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次

import { addSensitiveKeyword, getFofanum, getWhoisInfo } from '@/api/apiConfig/api.js'

import {
  fakeClue,
  exportCluesListV1,
  noteClueBlack,
  getCompanyClueBaseData,
  goPointClue,
  passClueV1,
  insertCluesV1,
  editCluesGroupList,
  delCluesGroupList,
  addCluesGroupList,
  cluesGroupList,
  batchAddClue,
  tabNumClues,
  delCluesListV1,
  cluesList
} from '@/api/apiConfig/clue.js'
import { companyEnquiry, delRunningClueJob, getRunningClueJob } from '@/api/apiConfig/recommend.js'
import { detectTaskInfo, cluesUpdateSync } from '@/api/apiConfig/surveying.js'
import businessTagVue from '../../components/businessTag.vue'

export default {
  components: {
    cluedialogs: () => import('../cloudRecommend/clueInsertDialog.vue'),
    conpanyRelation,
    clueMap,
    tree,
    businessTagVue
  },
  data() {
    return {
      imgUrl: '',
      evaluatePictureList: [],
      listOrPic: 0,
      fofa_assets_num: 0,
      fofa_assets_url: '',
      whoisLoading: false,
      fofaLoading: false,
      whoisInfo: {
        registration_date: '-',
        expiration_date: '-',
        sponsoring_registrar: '-',
        registrant_name: '-',
        registrant_mobile: '-',
        registrant_org: '-'
      },
      companyName: '',
      // 收起展开
      collapseFlag: true,
      typeArr: [
        {
          name: '全部',
          id: 0
        },
        {
          name: '数字资产',
          id: 1
        },
        {
          name: '数据泄露',
          id: 2
        }
      ],
      whiosHeader: [
        {
          label: '域名创建时间：',
          name: 'registration_date'
        },
        {
          label: '域名更新时间：',
          name: 'expiration_date'
        },
        {
          label: '域名注册商：',
          name: 'sponsoring_registrar'
        },
        {
          label: '联系人：',
          name: 'registrant_name'
        },
        {
          label: '注册人手机：',
          name: 'registrant_mobile'
        },
        {
          label: '注册人组织：',
          name: 'registrant_org'
        }
      ],
      dialogChartVisible: false,
      chartFlag: 0,
      companyDataCharts: {},
      companySurviveValue: [],
      companySurviveAllValue: [],
      extendNum: 0,
      companyProgressAllValue: [],
      companyProgressValue: [],
      statusProssValue: [
        { name: '待确认', value: 0, color: 'rgba(248, 193, 54, 1)', label: 0 },
        { name: '已确认', value: 0, color: 'rgba(16, 213, 149, 1)', label: 0 },
        { name: '已忽略', value: 0, color: 'rgba(255, 70, 70, 1)', label: 0 }
      ],
      sourceProssValue: [
        { name: '初始线索', value: 0, color: 'rgba(38, 119, 255, 1)', label: 0 },
        { name: '扩展线索', value: 0, color: 'rgba(16, 213, 149, 1)', label: 0 },
        { name: '循环线索', value: 0, color: 'rgba(19, 183, 255, 1)', label: 0 }
      ],
      tiquLoading: false,
      groupLoading: false,
      toDoList: null,
      lineProgress: 0,
      lineStart_at: '',
      lineClueNum: '',
      lineUseSeconds: '',
      btnLoading: false,
      groupDisabled: false,
      groupForm: {
        name: '',
        keyword: '',
        file: ''
      },
      groupFormrules: {
        name: [{ required: true, message: '请输入名称', trigger: 'change' }]
      },
      chainRules: {
        type: [{ required: true, message: '请选择关键词类型', trigger: 'change' }]
      },
      chainRuleForm: {
        type: ''
      },
      tabDataTmp: [
        {
          name: '0',
          label: '域名',
          count: ''
        },
        {
          name: '1',
          label: '证书',
          count: ''
        },
        {
          name: '2',
          label: 'ICP',
          count: ''
        },
        {
          name: '3',
          label: 'ICON',
          count: ''
        },
        {
          name: '4',
          label: '关键词',
          count: ''
        },
        {
          name: '6',
          label: 'IP段',
          count: ''
        },
        {
          name: '5',
          label: '子域名',
          count: '',
          has_subdomain_tab: 1
        },
        {
          name: '10',
          label: 'FID',
          count: ''
        }
      ],
      tabActiveNameStatus: '0',
      tabDataStatus: [
        {
          label: '待确认',
          value: '0',
          count: 0
        },
        {
          label: '已确认',
          value: '1',
          count: 0
        },
        {
          label: '已忽略',
          value: '2',
          count: 0
        }
      ],
      currentId: '', // 正在扩展任务id
      currentGroupId: '',
      currentGroupIds: '',
      editCurrentId: '',
      dialogFormVisibleCompany: false,
      dialogFormVisibleGroup: false,
      dialogFormVisibleInsert: false,
      addIsTrue: false,
      ruleForm: {
        clueType: '0',
        clueContent: ''
      },
      ruleFormCompany: {
        name: '',
        file: '',
        operate_company_id: ''
      },
      clueRuleForm: {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      },
      groupArr: [],
      loading: false,
      noteLoading: false,
      batchOneLoading: false,
      removeLoading: false,
      exportLoading: false,
      batchLoading: false,
      recommentLoading: false,
      formInline: {
        no_page: '', // 1 没有分页，不传值 有分页
        keyword: '',
        page: 1,
        per_page: 10,
        status: '', // 已确认线索
        group_id: '' // 安服角色分组id
      },
      tableData: [],
      currentPage: 1,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      tabActiveName: '0',
      tabNumStatus: 0, // 某状态(待确认)下标签数量总数
      keywordObj: {
        domain_keyword: '',
        cert_keyword: '',
        icp_keyword: '',
        key_keyword: '',
        ip_keyword: '',
        subdomain_keyword: '',
        fid_keyword: ''
      },
      checkedAllObj: {
        domain_checked: false,
        cert_checked: false,
        icp_checked: false,
        icon_checked: false,
        key_checked: false,
        ip_checked: false,
        subdomain_checked: false,
        fid_checked: false
      },
      tabData: [
        {
          name: '0',
          label: '域名',
          count: '',
          placeholder: '请输入域名,例：fofa.info'
        },
        {
          name: '1',
          label: '证书',
          count: '',
          placeholder: '请输入证书，例：O="北京华顺信安科技有限公司"或者CN="fofa.info"'
        },
        {
          name: '2',
          label: 'ICP',
          count: '',
          placeholder: '请输入ICP，例：京ICP备18024709号 或者 京ICP备18024709号-3'
        },
        {
          name: '3',
          label: 'ICON',
          count: '',
          placeholder: '请输入icon'
        },
        {
          name: '4',
          label: '关键词',
          count: '',
          placeholder: '请输入关键词，例：北京华顺信安科技有限公司'
        },
        {
          name: '5',
          label: '子域名',
          count: '',
          has_subdomain_tab: 1,
          placeholder: '请输入子域名'
        },
        {
          name: '6',
          label: 'IP段',
          count: '',
          placeholder: '请输入IP段'
        },
        {
          name: '10',
          label: 'FID',
          count: ''
        }
      ],
      tableHeader: [
        {
          label: '关键词',
          name: 'content',
          minWidth: '150px'
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          minWidth: '120px'
        },
        {
          label: '来源',
          name: 'source_label'
        },
        {
          label: '添加时间',
          name: 'created_at'
        },
        {
          label: '线索链',
          name: 'chain_list'
        }
      ],
      groupCurrentPage: 1,
      groupPageSize: 10,
      checkedArr0: [],
      nameFlag: false,
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      checkedArr10: [],
      currentGroupName: '',
      // 控制图谱显示隐藏
      clueMapFlag: false,
      chartLoaing: false,
      user: {
        role: ''
      },
      has_subdomain_tab: 0,
      relationData: null,
      chainDialog: false,
      relname: '',
      isNew: false, //是否为新增线索
      activeName: '', //tab页（线索、推荐）
      isCheckedAllGroup: false
    }
  },
  watch: {
    isCheckedAllGroup(val) {
      let groupArrLength = this.groupArr && this.groupArr.length
      if (groupArrLength && groupArrLength == 0) return
      for (let index = 0; index < groupArrLength; index++) {
        const element = this.groupArr[index]
        element.isChecked = val
      }
    },

    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    getterCurrentCompany(msg) {
      if (this.user.role != 2) return
      this.currentPage = 1
      this.currentGroupId = '' //切换企业时，清空一下
      this.isNew = false
      this.resetTable()
      this.getTaskResultData().then((res) => {
        this.getTabNum('tmp')
      })
      this.getCompanyClueBaseFun()
      // 延时解决获取sessionStorage.getItem('companyInfo')接口没有响应数据
      setTimeout(() => {
        if (this.currentCompany == -1) {
          // 安服账号本身,不用配置就有权限
          this.has_subdomain_tab = 1
        } else {
          // 安服操作的企业,要你根据用户管理的配置判断
          this.has_subdomain_tab = sessionStorage.getItem('companyInfo')
            ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.has_subdomain_tab
            : ''
        }
      }, 100)
    },

    currentGroupId(val) {
      this.groupArr.map((v) => {
        if (v.id == val) {
          this.currentGroupName = v.name
        }
      })
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange', 'getterWebsocketMessage']),
    clueTypePlaceholder() {
      return this.tabData[this.ruleForm.clueType].placeholder
    },
    handleData(){
      return (lineProgress) => {
        if(lineProgress && lineProgress.length > 0){
          return parseFloat(lineProgress.replace('%', ''))
        }else {
          return parseFloat(0)
        }
      }
    }
  },
  created() {
    // this.activeName = sessionStorage.getItem('activeTabName')
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.imgUrl = this.userInfo.is_local ? '//images.weserv.nl/?url=' : this.imgBaseUrl
  },
  async mounted() {
    // 子域名推荐权限控制
    if (this.user.role == 1) {
      // 超管账号有权限
      this.has_subdomain_tab = 1
    } else if (this.user.role == 2) {
      // 安服
      if (this.currentCompany) {
        if (this.currentCompany == -1) {
          // 安服账号本身,不用配置就有权限
          this.has_subdomain_tab = 1
        } else {
          // 安服操作的企业,要你根据用户管理的配置判断
          this.has_subdomain_tab = sessionStorage.getItem('companyInfo')
            ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.has_subdomain_tab
            : ''
        }
      }
    } else {
      // 企业账号
      this.has_subdomain_tab = this.user ? this.user.has_subdomain_tab : ''
    }
    if (this.user.role == 2 && !this.currentCompany) return

    if (this.$route.query.group_id) {
      // 获取场景分组
      this.currentGroupId = String(this.$route.query.group_id)
      this.currentGroupIds = String(this.$route.query.group_id)
      this.isNew = true
      this.getTaskResultData()
    } else {
      this.isNew = false
      this.getTaskResultData()
    }
    this.getCompanyClueBaseFun() // 获取线索状态/线索来源统计信息
  },

  methods: {
    ...mapMutations(['saveRecommentData', 'changeMenuId']),

    clickevaluatePicture(row) {
      let url = row.includes('http') ? this.imgUrl + row : this.showSrcIp + row
      var srclist = []
      srclist.push(url)
      this.evaluatePictureList = srclist // 赋值
    },
    transferRate(rate) {
      if (rate == '0.00%') {
        return '-'
      } else {
        return rate && rate.replace('.00', '')
      }
    },
    // 将所勾选的线索标记为黑线索
    async noteBlackList() {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要标记的数据！')
        return
      }
      if (this.checkedArr10.length != 0) {
        this.$message.error('FID线索不支持此操作！')
        return
      }
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要标记的数据！')
        return
      }
      this.noteLoading = true
      let obj = this.__transferChecked()
      this.$confirm('此操作将把所勾选的数据标记为黑名单，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await noteClueBlack(obj).catch(() => {
          this.noteLoading = false
        })
        this.noteLoading = false
        if (res.code == 0) {
          this.$message.success('标记成功')
          this.resetTable()
          this.getTaskResultData()
          this.getCompanyClueBaseFun()
        }
      })
    },
    changeChecked(val, index, isChecked) {
      val.isChecked = !isChecked
    },
    // 批量以及单独修改企业名称
    editCompanyName(type, id) {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要编辑的数据！')
        return
      }
      if (this.checkedArr10.length != 0) {
        this.$message.error('FID线索不支持此操作！')
        return
      }
      let clueData = null
      if (type == 'more') {
        let clueData1 = [
          ...this.checkedArr0,
          ...this.checkedArr1,
          ...this.checkedArr2,
          ...this.checkedArr3,
          ...this.checkedArr4,
          ...this.checkedArr5,
          ...this.checkedArr6,
          ...this.checkedArr10
        ]
        clueData = clueData1.map((item) => {
          return item.id
        })
        if (clueData.length == 0) {
          this.$message.error('请选择要编辑的数据！')
          return
        }
      } else {
        clueData = [id]
      }
      this.$prompt('请输入新的企业名称', `${type == 'more' ? '批量' : ''}编辑企业名称`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'cloud_info_del_sure',
        cancelButtonClass: 'cloud_info_del_cancel',
        inputValidator: (value) => {
          if (value) {
            return true
          } else {
            return false
          }
        },
        inputErrorMessage: '请输入',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let res = await cluesUpdateSync({
              operate_company_id: this.currentCompany,
              sync: true,
              ids: clueData,
              clue_company_name: instance.inputValue.trim()
            })
            if (res.code == 0) {
              done()
              this.resetTable()
              this.getClueData()
              this.getTabNum()
            }
            setTimeout(() => {
              instance.confirmButtonLoading = false
            }, 300)
          } else {
            done()
          }
        }
      }).then(({ value }) => {
        this.$message({
          type: 'success',
          message: '修改成功'
        })
      })
    },
    changeChecked(val, index, isChecked) {
      val.isChecked = !isChecked
      // this.checkedGroupId = !this.checkedGroupId
      // this.$set(this.groupArr[index],isChecked,!isChecked)
    },
    getWhois(clue, clueindex) {
      if (clue.whoisInfo) {
        // 存在直接展示
        this.whoisInfo = clue.whoisInfo
      } else {
        // 不存在需要调用接口
        this.whoisLoading = true
        getWhoisInfo({
          id: clue.id,
          operate_company_id: this.currentCompany
        })
          .then((res) => {
            this.whoisLoading = false
            if (res.data) {
              this.whoisInfo = res.data
              // 查到后给列表fofa_assets_num赋值，避免重复查询
              this.tableData[clueindex].whoisInfo = res.data
            }
          })
          .catch(() => {
            this.whoisLoading = false
          })
      }
    },
    getFofaNum(clue, clueindex) {
      if (clue.fofa_assets_num) {
        // 存在直接展示
        this.fofa_assets_num = clue.fofa_assets_num.num
        this.fofa_assets_url = clue.fofa_assets_num.fofa_url
      } else {
        // 不存在需要调用接口
        this.fofa_assets_num = 0
        this.fofa_assets_url = ''
        this.fofaLoading = true
        getFofanum({
          id: clue.id,
          operate_company_id: this.currentCompany
        })
          .then((res) => {
            this.fofaLoading = false
            if (res.data) {
              this.fofa_assets_num = res.data.num
              this.fofa_assets_url = res.data.fofa_url
              // 查到后给列表fofa_assets_num赋值，避免重复查询
              this.tableData[clueindex].fofa_assets_num = {
                num: res.data.num,
                fofa_url: res.data.fofa_url
              }
            }
          })
          .catch(() => {
            this.fofaLoading = false
          })
      }
    },
    getFofaNumV1(clue, clueindex) {
      if (clue.fofa_assets_num) return
      this.$set(this.tableData[clueindex], 'fofaLoading', true)
      getFofanum({
        id: clue.id,
        operate_company_id: this.currentCompany
      }).then((res) => {
        this.$set(this.tableData[clueindex], 'fofaLoading', false)
        if (res.data) {
          this.$set(this.tableData[clueindex], 'fofa_assets_num', {
            num: res.data.num,
            fofa_url: res.data.fofa_url
          })
        }
      })
    },
    // 企业名称查询企业关系
    async searchCompany(val) {
      let params = {
        name: val,
        operate_company_id: this.currentCompany
      }
      let res = await companyEnquiry(params)
      if (res.code == 0) {
        res.data.icp_num = -1
        this.relationData = res.data
        this.dialogFormVisibleCompany = true
      }
    },
    viewMore(type) {
      // 控股企业
      if (type == 1) {
        if (this.companyProgressAllValue <= 3) {
          this.$message.error('无更多数据')
          return
        } else {
          this.chartFlag = 1
          this.dialogChartVisible = true
        }
      }
      // 分支机构
      if (type == 2) {
        if (this.companySurviveAllValue <= 3) {
          this.$message.error('无更多数据')
          return
        } else {
          this.chartFlag = 2
          this.dialogChartVisible = true
        }
      }
    },
    compare(a, b) {
      // 使用 toUpperCase() 忽略字符大小写
      const bandA = a.band.toUpperCase()
      const bandB = b.band.toUpperCase()

      let comparison = 0
      if (bandA > bandB) {
        comparison = 1
      } else if (bandA < bandB) {
        comparison = -1
      }
      return comparison
    },
    setClass(value) {
      if (value == '注销') {
        return 'pTmp2'
      } else if (value == '续存') {
        return 'pTmp1'
      } else if (value == '吊销') {
        return 'pTmp3'
      } else {
        return 'pTmp1'
      }
    },
    handleListOrPicClick(val) {
      if (val !== 0) {
        this.getTabNum('tmp')
      }
      this.clueMapFlag = val == 0 ? false : true
    },
    // 获取线索状态/线索来源统计信息
    async getCompanyClueBaseFun() {
      this.chartLoaing = tree
      let res = await getCompanyClueBaseData({
        operate_company_id: this.currentCompany
      })
      this.companySurviveValue = []
      this.companySurviveAllValue = []
      this.companyProgressValue = []
      this.companyProgressAllValue = []
      this.chartLoaing = false
      this.companyDataCharts = res.data
      // 数量小于3直接取 大于三放入
      let countTmp = 0
      if (
        res.data &&
        res.data.control_company_list.length != 0 &&
        res.data.control_company_list != undefined
      ) {
        let allTmp = []
        res.data.control_company_list.map((v) => {
          if (v.percent && v.percent !== 0) {
            allTmp.push(v)
          }
        })
        this.companyProgressAllValue = allTmp
        if (res.data.control_company_list.length <= 3) {
          let dataTmp = []
          this.setColorFunc(res.data.control_company_list).map((v) => {
            if (v.percent && v.percent !== 0) {
              dataTmp.push(v)
            }
            this.companyProgressValue = dataTmp
          })
        } else {
          let drrTmp = []
          let brrTmp = []
          res.data.control_company_list.map((v, i) => {
            if (v.percent && v.percent !== 0) {
              drrTmp.push(v)
            }
          })
          drrTmp.map((v, i) => {
            if (i <= 2) {
              brrTmp.push(v)
            }
          })
          this.companyProgressValue = this.setColorFunc(brrTmp)
        }
      }
      if (res.data && res.data.branch_company_list.list != undefined) {
        if (res.data && res.data.branch_company_list.list.length != 0) {
          this.companySurviveAllValue = res.data.branch_company_list.list
          if (res.data && res.data.branch_company_list.list.length <= 3) {
            this.companySurviveValue = res.data.branch_company_list.list
          } else {
            let brrTmp = []
            res.data.branch_company_list.list.map((v, i) => {
              if (i <= 2) {
                brrTmp.push(v)
              }
            })
            this.companySurviveValue = brrTmp
          }
        }
      }
      this.setNumNumber(res)
    },
    setNumNumber(res) {
      if (
        res.data &&
        (res.data.default_num !== 0 || res.data.confirm_num !== 0 || res.data.ingore_num !== 0)
      ) {
        let numTmp = res.data.default_num + res.data.confirm_num + res.data.ingore_num
        this.statusProssValue[0].label = ((res.data.default_num / numTmp) * 100).toFixed(2)
        this.statusProssValue[1].label = ((res.data.confirm_num / numTmp) * 100).toFixed(2)
        this.statusProssValue[2].label = (
          100 -
          (Number(this.statusProssValue[0].label) + Number(this.statusProssValue[1].label))
        ).toFixed(2)
        this.statusProssValue[0].value = res.data.default_num
        this.statusProssValue[1].value = res.data.confirm_num
        this.statusProssValue[2].value = res.data.ingore_num
      } else {
        this.statusProssValue[0].label = 0.0
        this.statusProssValue[1].label = 0.0
        this.statusProssValue[2].label = 0.0
        this.statusProssValue[0].value = 0
        this.statusProssValue[1].value = 0
        this.statusProssValue[2].value = 0
      }
      if (
        res.data &&
        (res.data.yuanshi_num !== 0 || res.data.xunhuan_num !== 0 || res.data.kuozhan_num !== 0)
      ) {
        let crrTmp = res.data.yuanshi_num + res.data.xunhuan_num + res.data.kuozhan_num
        this.sourceProssValue[0].label = ((res.data.yuanshi_num / crrTmp) * 100).toFixed(2)
        this.sourceProssValue[1].label = ((res.data.kuozhan_num / crrTmp) * 100).toFixed(2)
        this.sourceProssValue[2].label = (
          100 -
          (Number(this.sourceProssValue[0].label) + Number(this.sourceProssValue[1].label))
        ).toFixed(2)
        this.sourceProssValue[0].value = res.data.yuanshi_num
        this.sourceProssValue[1].value = res.data.kuozhan_num
        this.sourceProssValue[2].value = res.data.xunhuan_num
      } else {
        this.sourceProssValue[0].label = 0.0
        this.sourceProssValue[1].label = 0.0
        this.sourceProssValue[2].label = 0.0
        this.sourceProssValue[0].value = 0
        this.sourceProssValue[1].value = 0
        this.sourceProssValue[2].value = 0
      }
    },
    setColorFunc(data) {
      data.map((v, i) => {
        if (i == 0) {
          v.color = 'rgba(255, 70, 70, 1)'
        } else if (i == 1) {
          v.color = 'rgba(255, 121, 0, 1)'
        } else {
          v.color = 'rgba(248, 193, 54, 1)'
        }
      })
      return data
    },
    // 企业关系开始
    companyShow() {
      this.companyName = ''
      this.dialogFormVisibleCompany = true
      this.companyInfo = false
    },
    // 企业关系开始
    async companyInfoShow() {
      // 企业关系
      if (this.companyName.length == 0) {
        this.$message.error('请输入企业名称全称')
        return
      }
      let params = {
        name: this.companyName,
        operate_company_id: this.currentCompany
      }
      let res = await companyEnquiry(params)
      if (res.code == 0) {
        res.data.icp_num = -1
        this.relationData = res.data
        this.dialogFormVisibleCompany = true
      }
    },
    closeFun() {
      this.companyName = ''
      this.dialogFormVisibleCompany = false
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (
        res.cmd == 'cloud_recommend_task_progress' &&
        (!this.currentId || this.currentId == res.data.task_id)
      ) {
        if (res.data.status == 2) {
          this.toDoList = null
          resetMessage.success('扩展完成！')
          this.currentId = '' // 控制推送结束后仅执行一次
          this.getTaskResultData()
          this.getCompanyClueBaseFun()
        } else if (res.data.status == 1) {
          // 正在扫描
          this.toDoList = res.data
          if (!res.data) return
          this.currentId = res.data.task_id
          // 推送数据渲染到列表
          this.lineProgress = Number(res.data.progress)
          this.lineStart_at = res.data.start_time
          this.lineUseSeconds = res.data.use_seconds
          this.lineClueNum = res.data.total
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.toDoList = null
          this.getTaskResultData()
        }
      } else if (res.cmd == 'company_icp_info') {
        // websocket获取备案数量
        if (this.$refs.tree) {
          let nodes = this.$refs.tree.root.childNodes
          this.setTreeItemValue(nodes, res)
        }
      } else if (res.cmd == 'company_icp_info_end') {
        this.loadingFun(1)
      }
    },
    // 获取当前正在执行任务
    async getRunningJob() {
      this.lineProgress = 0
      let res = await getRunningClueJob({
        group_id: this.currentGroupId,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.toDoList = res.data
        if (!res.data) return
        this.currentId = res.data.task_id
        // 推送数据渲染到列表
        this.lineProgress = Number(res.data.progress)
        this.lineStart_at = res.data.start_time
        this.lineUseSeconds = res.data.use_seconds
        this.lineClueNum = res.data.total
      }
    },
    // 当前正在执行任务删除
    async removeTask() {
      this.$confirm('确定删除当前扩展任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await delRunningClueJob({
            operate_company_id: this.currentCompany,
            id: this.currentId
          })
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.getRunningJob()
          }
        })
        .catch(() => {})
    },
    // 关键词关联保存
    async chainSave(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (!valid) {
          return false
        }
        let obj = {
          is_clue_whole: this.checkedAllObj[this.checkedAllArr[this.tabActiveName]] ? 1 : '', // 传1时下边的参数生效
          tab_status: this.tabActiveNameStatus,
          group_id: this.currentGroupId,
          type: this.chainRuleForm.type,
          name: this.relname,
          keyword: this.formInline.keyword,
          operate_company_id: this.currentCompany
        }
        this.tiquLoading = true
        let res = await addSensitiveKeyword(obj).catch(() => {
          this.tiquLoading = false
        })
        if (res.code == 0) {
          this.tiquLoading = false
          this.chainDialog = false
          this.$message.success('操作成功！')
          this.$refs['eltable' + this.tabActiveName].clearSelection()
          this.getClueData()
          this.getTabNum()
        }
      })
    },
    // 扩展记录
    async expandLogClick() {
      this.$router.push({
        path: '/expandLog',
        query: {
          group_id: this.currentGroupId
        }
      })
    },
    // 处置
    async clueConfirmSave(icon) {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      if (this.checkedArr10.length != 0 && icon != 2 && icon != 1) {
        this.$message.error('FID线索不支持此操作！')
        return
      }
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0 &&
        this.checkedArr10.length == 0
      ) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let clueData = [
        {
          id: this.checkedAllObj.domain_checked
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAllObj.domain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.cert_checked
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAllObj.cert_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icp_checked
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAllObj.icp_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icon_checked
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAllObj.icon_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.key_checked
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAllObj.key_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.subdomain_checked
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAllObj.subdomain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.ip_checked
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAllObj.ip_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.fid_checked
            ? []
            : this.checkedArr10.map((item) => {
                return item.id
              }),
          type: '10',
          is_all: this.checkedAllObj.fid_checked ? '1' : ''
        }
      ]
      let actionFun = null
      let obj = null
      if (icon == 3) {
        // 供应链标记
        obj = {
          tab_status: this.tabActiveNameStatus,
          is_supply_chain: 1, // 0取消供应链 1设置为供应链
          group_id: this.currentGroupId,
          data: clueData,
          keyword: {
            domain_keyword: this.keywordObj.domain_keyword,
            cert_keyword: this.keywordObj.cert_keyword,
            icp_keyword: this.keywordObj.icp_keyword,
            key_keyword: this.keywordObj.key_keyword,
            ip_keyword: this.keywordObj.ip_keyword,
            subdomain_keyword: this.keywordObj.subdomain_keyword
          },
          operate_company_id: this.currentCompany
        }
        actionFun = goPointClue
      } else if (icon == 4) {
        // ICP盗用标记
        obj = {
          tab_status: this.tabActiveNameStatus,
          group_id: this.currentGroupId,
          is_fake_icp: 1,
          data: clueData,
          keyword: {
            domain_keyword: this.keywordObj.domain_keyword,
            cert_keyword: this.keywordObj.cert_keyword,
            icp_keyword: this.keywordObj.icp_keyword,
            key_keyword: this.keywordObj.key_keyword,
            ip_keyword: this.keywordObj.ip_keyword,
            subdomain_keyword: this.keywordObj.subdomain_keyword
          },
          operate_company_id: this.currentCompany
        }
        actionFun = fakeClue
      } else {
        // 忽略，确认
        obj = {
          tab_status: this.tabActiveNameStatus,
          status: icon,
          group_id: this.currentGroupId,
          data: clueData,
          keyword: {
            domain_keyword: this.keywordObj.domain_keyword,
            cert_keyword: this.keywordObj.cert_keyword,
            icp_keyword: this.keywordObj.icp_keyword,
            key_keyword: this.keywordObj.key_keyword,
            ip_keyword: this.keywordObj.ip_keyword,
            subdomain_keyword: this.keywordObj.subdomain_keyword
          },
          operate_company_id: this.currentCompany
        }
        actionFun = passClueV1
      }

      this.currentGroupIds = this.currentGroupId
      this.btnLoading = true
      let res = await actionFun(obj).catch(() => {
        this.btnLoading = false
      })
      this.btnLoading = false
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.resetTable()
        this.getTaskResultData()
        this.getCompanyClueBaseFun()
      }
    },
    resetTable() {
      this.formInline.keyword = ''
      this.keywordObj = {
        domain_keyword: '',
        cert_keyword: '',
        icp_keyword: '',
        key_keyword: '',
        ip_keyword: '',
        subdomain_keyword: ''
      }
      this.checkedAllObj = {
        domain_checked: false,
        cert_checked: false,
        icp_checked: false,
        icon_checked: false,
        key_checked: false,
        ip_checked: false,
        subdomain_checked: false,
        fid_checked: false
      }
      this.$refs['eltable' + this.tabActiveName].clearSelection()
    },
    getStatus(status) {
      // 忽略:2,确认:1,待确认：0
      let str = ''
      switch (status) {
        case 0:
          str = '待确认'
          break
        case 1:
          str = '已确认'
          break
        case 2:
          str = '忽略'
          break
      }
      return str
    },
    getChains(row) {
      let arr = row.filter((item) => {
        return item.content
      })
      return arr
    },
    chage() {},
    // 分组名称展开
    collapseChange(val) {
      this.tabActiveNameStatus = '0'
      this.currentPage = 1
      if (!val) return
      this.currentGroupId = val
      this.currentGroupIds = val
      if (this.groupArr && this.groupArr.length != 0) {
        this.groupArr.forEach((item, index) => {
          item.isHighlighted = false
          // item.isChecked = false
          if (item.id == this.currentGroupIds) {
            this.groupArr[index].isHighlighted = true
          }
        })
      }
      if (this.currentGroupId) {
        // 分组下面的待确认，已确认，忽略菜单数量
        this.getRunningJob()
        this.getClueData()
        this.getTabNum()
      }
    },
    // 点击分组名称
    groupClick(id, is_detect_assets_tasks, value, index) {
      this.currentGroupId = String(id)
      this.currentGroupIds = String(id)
      this.tabActiveNameStatus = value // 当前选中的分组状态的标识
      this.tableData = []
      this.groupArr &&
        this.groupArr.forEach((item) => {
          item.isHighlighted = false
        })
      this.groupArr[index].isHighlighted = true
      if (is_detect_assets_tasks) {
        this.isNew = true
      } else {
        this.isNew = false
      }
      this.keywordObj = {
        domain_keyword: '',
        cert_keyword: '',
        icp_keyword: '',
        key_keyword: '',
        ip_keyword: '',
        subdomain_keyword: '',
        fid_keyword: ''
      }
      this.checkedAllObj = {
        domain_checked: false,
        cert_checked: false,
        icp_checked: false,
        icon_checked: false,
        key_checked: false,
        ip_checked: false,
        subdomain_checked: false,
        fid_checked: false
      }
      this.currentPage = 1
      this.$nextTick(() => {
        this.$refs['eltable' + this.tabActiveName].clearSelection()
      })
      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []
      this.getRunningJob()
      this.getClueData()
      this.getTabNum()
    },
    insertShow() {
      this.groupForm.name = ''
      this.addIsTrue = true
      this.dialogFormVisibleGroup = true
      this.groupForm.name = ''
    },
    collapsClick() {
      this.collapseFlag = !this.collapseFlag
    },
    insertClueShow() {
      this.clueRuleForm.content = ''
      this.clueRuleForm.comment = ''
      this.clueRuleForm.clue_company_name = ''
      this.dialogFormVisibleInsert = true
      this.clueRuleForm.content = this.clueRuleForm.way == 1 ? [] : ''
      this.fileList = []
    },
    async getClueData(tmp, flag) {
      // 忽略:2,确认:1,待确认：0

      // 搜索page为1
      if (tmp == 'tmp') {
        this.currentPage = 1
        this.formInline.page = 1
        if (this['checkedArr' + this.tabActiveName].length > 0) {
          this['checkedArr' + this.tabActiveName] = []
        }
        // 清空当前页面已选中全部数据 而不是取消表头全选
        this.tableData &&
          this.tableData.forEach((row) => {
            this.$refs['eltable' + this.tabActiveName].toggleRowSelection(row, false)
          })
        this.checkedAllObj[this.checkedAllArr[this.tabActiveName]] = false
      } else {
        this.formInline.page = this.currentPage
      }
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.group_id = this.currentGroupId // 分组id
      this.formInline.no_page = ''
      this.formInline.status = this.tabActiveNameStatus
      this.formInline.keyword = this.keywordObj[this.tabKeywordArr[this.tabActiveName]]
      let obj = {
        type: this.tabActiveName,
        query: this.formInline,
        operate_company_id: this.currentCompany
      }
      console.log("执行开始")
      this.loading = true
      let res = await cluesList(obj).catch(() => {
        this.loading = false
      }).finally(() => this.loading = false )
      this.loading = false
      console.log("执行结束")

      let arr = res.data && res.data.items ? res.data.items : []
      let arrayNew = []
      arr.map((item) => {
        arrayNew.push(Object.assign({}, item, { type: this.tabActiveName }))
      })
      this.tableData = arrayNew
      this.total = res.data && res.data.total ? res.data.total : 0
      // 全选操作
      this.$nextTick(() => {
        this.loading = false
        this.$forceUpdate()
        if (this.checkedAllObj[this.checkedAllArr[this.tabActiveName]]) {
          this.tableData &&
            this.tableData.forEach((row) => {
              this.$refs['eltable' + this.tabActiveName].toggleRowSelection(row, true)
            })
        }
      })
    },
    copyClick(tmp) {
      let data = []
      data.push(tmp)
      let text = ''
      data.map((item) => {
        this.getChains(item).map((v, i) => {
          if (v.type && v.type == 3) {
            if (i == item.length - 1 && v) {
              text += 'icon' + '\n'
            } else {
              text += 'icon' + '-->'
            }
          } else {
            if (i < item.length - 1 && v) {
              text += v.content + '-->'
            } else if (i == item.length - 1 && v) {
              text += v.content + '\n'
            } else {
              text += v.content
            }
          }
        })
      })
      this.$copyText(text).then(
        (res) => {
          console.log('复制成功：', res)
          this.$message.success('复制成功')
        },
        (err) => {
          console.log('', err)
          this.$message.error('复制失败')
        }
      )
    },
    async getTabNum(tmp) {
      let obj = {}
      if (tmp) {
        obj = {
          group_id: this.currentGroupId,
          data: {
            status: '',
            operate_company_id: this.currentCompany
          }
        }
      } else {
        obj = {
          group_id: this.currentGroupId,
          data: {
            status: this.tabActiveNameStatus,
            operate_company_id: this.currentCompany
          }
        }
      }

      let res = await tabNumClues(obj)
      this.tabNumStatus = 0 // 获取总数，用于删除、导出等操作无数据提示
      this.extendNum = res.data.expand_finish
      if (res.data.clues_count) {
        if (tmp) {
          this.tabDataTmp.forEach((item) => {
            res.data.clues_count.forEach((ch) => {
              if (item['name'] == ch['type']) {
                this.tabNumStatus += ch.count
                item.count = ch.count
              }
            })
          })
        } else {
          this.tabData.forEach((item) => {
            res.data.clues_count.forEach((ch) => {
              if (item['name'] == ch['type']) {
                this.tabNumStatus += ch.count
                item.count = ch.count
              }
            })
          })
        }
      }
    },
    // 线索保存
    insertClueSave() {
      this.dialogFormVisibleInsert = false
      this.currentGroupIds = this.currentGroupId
      this.clueRuleForm = {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      }
      this.getTaskResultData()
      this.getCompanyClueBaseFun()
    },
    async groupRemove(type, id) {
      let ids = []
      if (type == 'more') {
        this.groupArr.forEach((item) => {
          if (item.isChecked) {
            ids.push(item.id)
          }
        })
        if (ids.length == 0) return this.$message.error('请勾选需要删除的数据')
      } else {
        ids = [id]
      }

      this.$confirm(
        `确定删除${type == 'more' ? '所勾选的' : ''}数据(默认的场景不可删除)?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'cloud_clue_del_cancel',
          confirmButtonClass: 'cloud_clue_del_sure',
          customClass: 'cloud_clue_del',
          type: 'warning'
        }
      )
        .then(async () => {
          let obj = {
            id: ids,
            operate_company_id: this.currentCompany
          }
          let res = await delCluesGroupList(obj)
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.currentGroupId = '' // 删除后清空当前分组id,用来重新渲染选中分组样式
            this.getTaskResultData()
            this.getCompanyClueBaseFun()
            if (type == 'more') {
              this.isCheckedAllGroup = false
            }
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.cloud_clue_del>.el-message-box__btns')
        del.children[0].id = 'cloud_clue_del_cancel'
        del.children[1].id = 'cloud_clue_del_sure'
      }, 50)
    },
    async exportMore(row) {
      this.editCurrentId = row.id
      this.groupForm.name = row.name
      this.addIsTrue = false
      this.dialogFormVisibleGroup = true
    },
    async exportMoreSave() {
      // 分组新建、编辑
      this.$refs['groupForm'].validate(async (valid) => {
        if (valid) {
          this.groupForm.operate_company_id = this.currentCompany
          let res = null
          this.currentGroupIds = this.currentGroupId
          this.btnLoading = true
          if (this.addIsTrue) {
            res = await addCluesGroupList(this.groupForm).catch(() => {
              this.btnLoading = false
            })
          } else {
            let obj = {
              id: this.editCurrentId,
              data: this.groupForm,
              operate_company_id: this.currentCompany
            }
            res = await editCluesGroupList(obj).catch(() => {
              this.btnLoading = false
            })
          }
          if (res.code == 0) {
            this.btnLoading = false
            this.$message.success('操作成功！')
            this.dialogFormVisibleGroup = false
            this.getTaskResultData(true)
          }
        } else {
          return false
        }
      })
    },
    async goChain(icon, name) {
      if (icon == 'more' && this.checkedArr4.length == 0) {
        this.$message.error('请选择要关联的数据！')
        return
      }
      this.relname = [] // 全选时name传空；
      if (name) {
        this.relname = [name]
      } else {
        if (this.checkedAllObj[this.checkedAllArr[this.tabActiveName]]) {
          this.relname = []
        } else {
          this.relname = this.checkedArr4.map((item) => {
            return item.content
          })
        }
      }
      this.chainDialog = true
    },
    // 获取分组列表数据
    async getTaskResultData(val) {
      let obj = {
        page: this.groupCurrentPage,
        per_page: 10,
        no_page: '1',
        operate_company_id: this.currentCompany
      }
      this.groupLoading = true
      let res = await cluesGroupList(obj).catch(() => {
        this.groupLoading = false
        this.groupArr = []
      })
      this.groupLoading = false
      let arr = res.data || []
      arr.forEach((item) => {
        item.isChecked = false
        item['num'] = item.collect ? item.collect : 0 // 所有线索数量
      })
      this.groupArr = arr
      if (!this.currentGroupId) {
        // 当前分组id不存在
        this.currentGroupId = this.groupArr.length > 0 ? String(this.groupArr[0].id) : ''
        this.currentGroupIds = this.groupArr.length > 0 ? String(this.groupArr[0].id) : ''
      }
      if (this.currentGroupId) {
        this.total = res.data.total
        // 分组下面的待确认，已确认，忽略菜单数量
        if (!val) {
          this.getRunningJob()
          this.getClueData()
          this.getTabNum()
        }
        if (this.groupArr.length != 0) {
          this.groupArr.forEach((item, index) => {
            item.isHighlighted = false
            if (item.id == this.currentGroupIds) {
              this.groupArr[index].isHighlighted = true
            }
          })
        }
      }
    },
    expandFinish() {
      this.$router.push({
        path: '/expandLog',
        query: {
          group_id: this.currentGroupId
        }
      })
    },
    async goRecomment() {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      if (this.checkedArr10.length != 0) {
        this.$message.error('FID线索不支持此操作！')
        return
      }
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let obj = {
        taskId: '',
        data: {
          operate_company_id: this.currentCompany,
          expand_source: 1 // 1云端推荐，0单位资产测绘
        }
      }
      let res = await detectTaskInfo(obj).catch(() => {
        this.loading = false
      }).finally(()=> {
        this.loading = false
      } )
      if (res.code == 0) {
        if (res.data) {
          this.$message.error('当前有推荐任务正在执行，请稍后操作')
        } else {
          let checkedIndex = ''
          for (let i = 0; i <= 6; i++) {
            if (this['checkedArr' + i].length != 0) {
              checkedIndex = i
              break
            }
          }
          let clueData = {
            checkedAllObj: this.checkedAllObj,
            keywordObj: this.keywordObj,
            group_id: this.currentGroupId,
            tabActiveName: checkedIndex,
            checkedArr0: this.checkedArr0,
            checkedArr1: this.checkedArr1,
            checkedArr2: this.checkedArr2,
            checkedArr3: this.checkedArr3,
            checkedArr4: this.checkedArr4,
            checkedArr5: this.checkedArr5,
            checkedArr6: this.checkedArr6
          }
          sessionStorage.setItem('menuId', '2-2')
          this.changeMenuId('2-2')
          this.$router.push('/assetsCloud')
          this.saveRecommentData(clueData)
        }
      }
    },
    async removeMore() {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0 &&
        this.checkedArr10.length == 0
      ) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let clueData = [
        {
          id: this.checkedAllObj.domain_checked
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAllObj.domain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.cert_checked
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAllObj.cert_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icp_checked
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAllObj.icp_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icon_checked
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAllObj.icon_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.key_checked
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAllObj.key_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.subdomain_checked
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAllObj.subdomain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.ip_checked
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAllObj.ip_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.fid_checked
            ? []
            : this.checkedArr10.map((item) => {
                return item.id
              }),
          type: '10',
          is_all: this.checkedAllObj.fid_checked ? '1' : ''
        }
      ]
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_del_cancel',
        confirmButtonClass: 'cloud_del_sure',
        customClass: 'cloud_del',
        type: 'warning'
      })
        .then(async () => {
          this.currentGroupIds = this.currentGroupId
          let obj = {
            tab_status: this.tabActiveNameStatus,
            group_id: this.currentGroupId,
            data: clueData,
            keyword: {
              domain_keyword: this.keywordObj.domain_keyword,
              cert_keyword: this.keywordObj.cert_keyword,
              icp_keyword: this.keywordObj.icp_keyword,
              key_keyword: this.keywordObj.key_keyword,
              ip_keyword: this.keywordObj.ip_keyword,
              subdomain_keyword: this.keywordObj.subdomain_keyword
            },
            operate_company_id: this.currentCompany
          }
          this.removeLoading = true
          let res = await delCluesListV1(obj).catch(() => {
            this.removeLoading = false
          })
          if (res.code == 0) {
            this.removeLoading = false
            this.$message.success('删除成功！')
            this.resetTable()
            this.getTaskResultData()
            this.getCompanyClueBaseFun()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.cloud_del>.el-message-box__btns')
        del.children[0].id = 'cloud_del_cancel'
        del.children[1].id = 'cloud_del_sure'
      }, 50)
    },
    // 单独扩展
    async batchAddClueFunOne() {
      if (!this.ruleForm.clueContent) {
        this.$message.error('请输入线索内容')
        return
      }

      let clueData = [
        {
          type: this.ruleForm.clueType,
          content: [this.ruleForm.clueContent]
        }
      ]
      let obj = {
        group_id: this.currentGroupId,
        data: clueData,
        is_auto_expend: '1', // 单独扩展
        file: '',
        operate_company_id: this.currentCompany
      }
      this.batchOneLoading = true
      let res = await insertCluesV1(obj).catch(() => {
        this.batchOneLoading = false
      })
      if (res.code == 0) {
        this.batchOneLoading = false
        this.$message.success('操作成功！')
        this.ruleForm.clueContent = ''
        this.getRunningJob()
        this.getTaskResultData()
      }
    },
    // 批量扩展batchAddClue
    async batchAddClueFun() {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要扩展的数据！')
        return
      }
      if (this.checkedArr10.length != 0) {
        this.$message.error('FID线索不支持此操作！')
        return
      }
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要扩展的数据！')
        return
      }
      let clueData = [
        {
          id: this.checkedAllObj.domain_checked
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAllObj.domain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.cert_checked
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAllObj.cert_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icp_checked
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAllObj.icp_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icon_checked
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAllObj.icon_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.key_checked
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAllObj.key_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.subdomain_checked
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAllObj.subdomain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.ip_checked
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAllObj.ip_checked ? '1' : ''
        }
      ]
      let obj = {
        tab_status: this.tabActiveNameStatus,
        group_id: this.currentGroupId,
        data: clueData,
        keyword: {
          domain_keyword: this.keywordObj.domain_keyword,
          cert_keyword: this.keywordObj.cert_keyword,
          icp_keyword: this.keywordObj.icp_keyword,
          key_keyword: this.keywordObj.key_keyword,
          ip_keyword: this.keywordObj.ip_keyword,
          subdomain_keyword: this.keywordObj.subdomain_keyword
        },
        operate_company_id: this.currentCompany
      }
      this.currentGroupIds = this.currentGroupId
      this.batchLoading = true
      let res = await batchAddClue(obj).catch(() => {
        this.batchLoading = false
      })
      if (res.code == 0) {
        this.batchLoading = false
        this.$message.success('操作成功')
        this.resetTable()
        this.getRunningJob()
        this.getClueData()
        this.getTabNum()
      }
    },
    __transferChecked() {
      // 导出以及标记黑名单的数据结构转换
      let clueData = [
        {
          id: this.checkedAllObj.domain_checked
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAllObj.domain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.cert_checked
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAllObj.cert_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icp_checked
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAllObj.icp_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icon_checked
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAllObj.icon_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.key_checked
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAllObj.key_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.subdomain_checked
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAllObj.subdomain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.ip_checked
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAllObj.ip_checked ? '1' : ''
        }
      ]
      let obj = {
        tab_status: this.tabActiveNameStatus,
        group_id: this.currentGroupId,
        data: clueData,
        keyword: {
          domain_keyword: this.keywordObj.domain_keyword,
          cert_keyword: this.keywordObj.cert_keyword,
          icp_keyword: this.keywordObj.icp_keyword,
          key_keyword: this.keywordObj.key_keyword,
          ip_keyword: this.keywordObj.ip_keyword,
          subdomain_keyword: this.keywordObj.subdomain_keyword
        },
        operate_company_id: this.currentCompany
      }
      return obj
    },
    async exportList() {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要导出的数据！')
        return
      }
      if (this.checkedArr10.length != 0) {
        this.$message.error('FID线索不支持此操作！')
        return
      }
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要导出的数据！')
        return
      }
      let obj = this.__transferChecked()
      this.exportLoading = true
      let res = await exportCluesListV1(obj).catch(() => {
        this.exportLoading = false
      })
      if (res.code == 0) {
        this.exportLoading = false
        this.resetTable()
        this.getClueData()
        this.getTabNum()
        this.download(this.showSrcIp + res.data.url)
      }
    },
    getLabel() {
      let label = ''
      if (this.tabActiveName == '0') {
        label = '根域'
      } else if (this.tabActiveName == '1') {
        label = '证书'
      } else if (this.tabActiveName == '2') {
        label = 'ICP'
      } else if (this.tabActiveName == '3') {
        label = 'ICON'
      } else if (this.tabActiveName == '4') {
        label = '关键词'
      } else if (this.tabActiveName == '5') {
        label = '子域名'
      } else if (this.tabActiveName == '6') {
        label = 'IP段'
      } else if (this.tabActiveName == '10') {
        label = 'FID'
      }
      return label
    },
    handleClick(data) {
      this.$refs['eltable' + this.tabActiveName].clearSelection()
      this['checkedArr' + this.tabActiveName] = []
      //域名，证书，ICP，子域名，ip需要有企业名称
      if (data == 0 || data == 1 || data == 2 || data == 5 || data == 6) {
        this.tableHeader = [
          {
            label: '关键词',
            name: 'content',
            minWidth: '150px'
          },
          {
            label: '企业名称',
            name: 'clue_company_name',
            minWidth: '120px'
          },
          {
            label: '来源',
            name: 'source_label'
          },
          {
            label: '添加时间',
            name: 'created_at'
          },
          {
            label: '线索链',
            name: 'chain_list'
          }
        ]
      } else {
        // ICON、关键词不需要企业名称
        this.tableHeader = [
          {
            label: '关键词',
            name: 'content',
            minWidth: '150px'
          },
          {
            label: '企业名称',
            name: 'clue_company_name',
            minWidth: '120px'
          },
          {
            label: '来源',
            name: 'source_label'
          },
          {
            label: '添加时间',
            name: 'created_at'
          },
          {
            label: '线索链',
            name: 'chain_list'
          }
        ]
      }
      this.currentPage = 1
      this.getClueData()
    },
    checkAllChange() {
      if (this.checkedAllObj[this.checkedAllArr[this.tabActiveName]]) {
        // 全选
        this.tableData.forEach((row) => {
          this.$refs['eltable' + this.tabActiveName].toggleRowSelection(row, true)
        })
      } else {
        // 取消全选
        this.$refs['eltable' + this.tabActiveName].clearSelection()
      }
    },
    setValueClass(value) {
      if (value >= 10000) {
        return 'p5'
      } else if (value >= 1000) {
        return 'p4'
      } else if (value >= 100) {
        return 'p3'
      } else if (value >= 10) {
        return 'p2'
      } else {
        return 'p1'
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAllObj[this.checkedAllArr[this.tabActiveName]]
    },
    handleSelectionChange(val) {
      let arr = this.$refs['eltable' + this.tabActiveName].selection.filter((item) => {
        return item.type == this.tabActiveName
      })
      this['checkedArr' + this.tabActiveName] = arr // .selection获取当前选中的数据
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getClueData('', true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getClueData('', true)
    },
    dropdownClick(event) {
      event.stopPropagation() //阻止冒泡事件
    },
    // 移入移出显示修改、删除
    enterTitle(index) {
      $('.dropdownMore').eq(index).css({ display: 'block' })
    },
    leaveTitle(index) {
      $('.dropdownMore').eq(index).css({ display: 'none' })
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  // overflow: auto;
  .home_header {
    width: 100%;
    height: 100%;
    position: relative;
  }
  .home_headerTmp {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
  }
  .statisticsBox {
    width: 100%;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;
    .pieChartsBox {
      width: 100%;
      display: flex;
      height: 179px;
      margin-top: 12px;
      .cueState {
        width: 50%;
        height: 100%;
        border-radius: 4px;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
        // background: rgba(225, 225, 225, 1);
      }
      .cueSource {
        width: 50%;
        height: 100%;
        width: 49.2%;
        margin-left: 0.8%;
        border-radius: 4px;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
        // background: rgba(225, 225, 225, 1);
      }
    }
    .companyBox {
      width: 100%;
      display: flex;
      height: 250px;
      .companyDetailBox {
        position: relative;
        width: 50%;
        height: 100%;
        padding: 20px;
        box-sizing: border-box;
        overflow: hidden;
        border-radius: 4px;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      }
      .companyChartsBox {
        width: 24.2%;
        height: 100%;
        border-radius: 4px;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
        margin-left: 0.8%;
      }
    }
  }
  .chartsBoxTitle {
    width: 100%;
    height: 90px;
    box-sizing: border-box;
    padding: 20px 16px 20px 16px;
    display: flex;
    justify-content: space-between;
  }
  .companyChartRanking > .mechanismBox:nth-child(2) {
    margin-top: 8px;
  }
  .companyChartRanking > .mechanismBox:nth-child(3) {
    margin-top: 8px;
  }
  .companyChartRanking1 {
    padding-top: 0;
    height: 500px !important;
    overflow: auto;
    margin: 0;
    .mechanismBox {
      margin-top: 8px;
    }
  }
  .companyChartRanking1 > .mechanismBox:nth-child(1) {
    margin-top: 0;
  }
  .companyChartRanking {
    width: 100%;
    height: 160px;
    box-sizing: border-box;
    padding: 18px 16px 16px 16px;
    .mechanismBox {
      width: 100%;
      height: 40px;
      background: linear-gradient(
        90deg,
        rgba(38, 119, 255, 0.12) 0%,
        rgba(38, 119, 255, 0.06) 100%
      );
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 12px 10px 12px;
      box-sizing: border-box;
      div {
        font-size: 14px;
        font-weight: 400;
        color: rgba(55, 57, 60, 1);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      p {
        width: 40px;
        height: 24px;
        border-radius: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
      }
      .pTmp1 {
        background: rgba(38, 119, 255, 0.12);
        color: rgba(38, 119, 255, 1);
      }
      .pTmp2 {
        background: rgba(255, 121, 0, 0.12);
        color: rgba(255, 121, 0, 1);
      }
      .pTmp3 {
        background: rgba(255, 70, 70, 0.12);
        color: rgba(255, 70, 70, 1);
      }
    }
    .numExprogress {
      margin-top: 8px;
    }
    .rankingProgress {
      width: 100%;
      height: 33%;
      .progressNum {
        width: 100%;
        display: flex;
        justify-content: space-between;
        div {
          align-items: center;
          font-size: 14px;
          font-weight: 400;
          color: rgba(55, 57, 60, 1);
        }
        p {
          width: 16px;
          height: 16px;
          border-radius: 2px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
        }
        .p1 {
          background: rgba(255, 70, 70, 1);
        }
        .p2 {
          background: rgba(255, 121, 0, 1);
        }
        .p3 {
          background: rgba(248, 193, 54, 1);
        }
        .numNameBox {
          margin-left: 8px;
          font-size: 14px;
          color: rgba(98, 102, 108, 1);
        }
      }
    }
    .rankingProgress1 {
      height: 70px;
    }
  }
  .chartTitleOne {
    border-left: 4px solid rgba(38, 119, 255, 1);
    background: linear-gradient(
      180deg,
      rgba(212, 228, 255, 0.08) 0%,
      rgba(38, 119, 255, 0.06) 100%
    );
  }
  .chartTitleTwo {
    border-left: 4px solid rgba(19, 183, 255, 1);
    background: linear-gradient(
      180deg,
      rgba(204, 240, 255, 0.08) 0%,
      rgba(13, 154, 214, 0.06) 100%
    );
  }
  .iconClass {
    color: #2677ff;
    font-size: 14px;
    cursor: pointer;
  }
  .iconClass::before {
    margin-right: 3px;
  }
  .groupClass {
    width: 17%;
    height: 100%;
    background: #fff;
    border-right: 1px solid #e9ebef;
    p {
      padding: 16px;
      font-weight: 600;
    }

    .activeClass {
      color: #2677ff;
      border-left: 4px solid #2677ff;
      background: #eff2f7;
    }
    ul {
      height: calc(100% - 53px);
      // height: 100px;
      overflow: auto;
      li {
        padding: 10px 16px;
        line-height: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        color: #62666c;

        i {
          color: #62666c;
          margin-left: 6px;
          cursor: pointer;
        }
      }
    }
    /deep/.el-collapse-item__arrow {
      margin: 0;
    }
  }

  .handle {
    border-bottom: 1px solid #e9ebef;
    .row-bg {
      padding: 10px 0;
      background-color: #f9fafc;
    }
    .bot {
      color: #62666c;
      display: flex;
      justify-content: space-between;
      margin: 12px 0;
      .progress {
        color: #2677ff;
      }
      .delTask {
        cursor: pointer;
      }
      span {
        i {
          margin-right: 3px;
        }
      }
    }
  }
  .infoKeyword {
    padding: 0 0 20px 12px;
  }
  // 企业关系开始
  /deep/ .elDialogAdd2 {
    .el-dialog .el-dialog__header {
      background: #f5f7fa;
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 16px !important;
      font-size: 14px !important;
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid #e9ebef;
      .el-dialog__title {
        font-size: 14px;
        color: #37393c !important;
      }
      .el-dialog__headerbtn {
        top: auto !important;
        right: 16px !important;
      }
    }
    .el-dialog .el-dialog__body {
      height: 500px;
      position: relative;
    }
    .back {
      margin-bottom: 10px;
      margin-right: 5px;
      padding: 0;
      width: 24px;
      height: 24px;
      border-radius: 2px;
      background-color: #e1e5ec;
      border-color: #e1e5ec;
      i {
        color: #62666c;
      }
    }
  }
  /deep/ .elDialogAdd2.add_img {
    .el-dialog .el-dialog__body {
      background-image: url('../../assets/images/companyQuery.png');
      background-size: 100% 100%;
    }
  }
  .company-input {
    width: 400px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    margin-top: -14px;
    .search-btn {
      width: 100%;
      height: 40px;
      background: #2677ff;
      border-radius: 0px 4px 4px 0px;
      font-size: 16px;
      color: #fff;
      margin-left: 0;
    }
    /deep/ .el-input__inner {
      height: 40px;
      line-height: 40px;
      border-color: #2677ff;
    }
    /deep/ .el-input-group__append {
      padding: 0;
      width: 68px;
    }
  }
  .company-list {
    padding-right: 8px;
    .title {
      display: flex;
      height: 48px;
      line-height: 48px;
      background: #f5f7fa;
    }
    .name {
      flex: 1;
      white-space: normal;
      padding-right: 8px;
    }
    .text {
      display: inline-block;
      width: 100px;
      text-align: center;
    }
    .action {
      display: flex;
      justify-content: space-between;
      padding-bottom: 20px;
    }
    /deep/ .el-tree {
      max-height: 360px;
      overflow-y: auto;
    }
  }
  .icp-host-list {
    /deep/ .el-table__body-wrapper {
      height: 350px;
      overflow-y: auto;
    }
  }
  /deep/ .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
  }
  .count {
    color: #2677ff;
    text-align: center;
  }
  // 企业关系结束
  /deep/.clueWrap {
    width: 83%;
    height: 100%;
    background: #fff;
    .grid-content {
      position: relative;
      height: 140px;
      padding: 12px;
      z-index: 99;
      opacity: 1;
      border-radius: 4px;
      box-sizing: border-box;
      background: linear-gradient(
          180deg,
          rgba(212, 228, 255, 0.08) 0%,
          rgba(38, 119, 255, 0.06) 100%
        ),
        rgba(245, 248, 252, 1);
      overflow: hidden;
      .el-progress-bar {
        padding-right: 0;
        .el-progress-bar__outer {
          background: rgba(209, 213, 221, 0.5);
        }
      }
      .el-progress__text {
        color: #2677ff;
        font-size: 14px !important;
      }
      .imgBot {
        position: absolute;
        bottom: -19px;
        z-index: -1;
        left: 9px;
      }
      .loadingList {
        margin-top: 12px;
      }
      .title {
        display: flex;
        justify-content: space-between;
        color: rgba(55, 57, 60, 1);
        font-size: 14px;
        margin-bottom: 12px;
        span {
          color: rgba(55, 57, 60, 1);
          font-size: 14px !important;
        }
      }
      .el-input__inner {
        background: rgba(255, 255, 255, 0.5);
        border: 1px solid rgba(209, 213, 221, 1);
      }
      .expandOver {
        // width: 100%;
        display: flex;
        justify-content: space-between;
        height: 36px;
        line-height: 36px;
        opacity: 1;
        padding: 0 12px;
        color: #2677ff;
        border-radius: 4px;
        background: linear-gradient(
          90deg,
          rgba(38, 119, 255, 0.12) 0%,
          rgba(38, 119, 255, 0.06) 100%
        );
        span {
          i {
            margin-right: 4px;
          }
        }
      }
      // .singleExpand {
      //   display: flex;
      //   .el-input-group__prepend {
      //     vertical-align: baseline !important;
      //     border: 0;
      //     padding: 0 !important;
      //   }
      //   .el-select {
      //     margin: 0 !important;
      //   }
      // }
      .singleExpand {
        .el-input-group__prepend .el-select {
          margin: 0;
        }
        .el-input-group__prepend {
          padding: 0;
        }
      }
      .bot {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16px;
        .normalBtnSmall {
          width: 50px;
          height: 26px;
          line-height: 24px;
          padding: 0px 10px;
          color: #fff;
          font-size: 12px;
          background: #2677ff;
        }
      }
    }
    .top {
      box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.08);
      background: #fafbff;
      .el-input__icon {
        line-height: 32px;
      }
      .el-input-group__append {
        color: #fff;
        background: #2677ff;
      }
    }
    .statusTabs {
      margin-top: 10px;
      margin-left: 20px;
      .el-tabs__nav {
        padding-left: 0;
      }
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      height: 20px;
      .dropdownClass {
        margin-right: 10px;
      }
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input--suffix {
          i {
            line-height: 26px;
          }
        }
      }
      .el-tabs__header {
        height: 33px;
        border-bottom: 0;
      }
      .el-tabs__nav {
        padding-left: 0;
        height: 33px;
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #e4e7ed;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        text-align: center;
        background: rgba(38, 119, 255, 0.1);
        border-radius: 0;
        border: 1px solid #2677ff;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
        transition: none;
      }
    }
    .tableWrap {
      height: calc(100% - 400px);
      .fofaImg {
        vertical-align: middle;
        margin: 0 5px;
        padding: 4px 8px;
        border-radius: 4px;
        box-sizing: border-box;
        background: #dbe0e3;
      }
      .greenLine {
        margin-left: 0;
        margin-right: 5px;
      }
      .grayLine {
        margin-left: 0;
        margin-right: 5px;
      }
      .redStatus {
        width: 6px;
        height: 6px;
      }
      .contentClass {
        display: inline-block;
        max-width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .tableWrapTwo {
      height: calc(100% - 380px);
    }
    .whoisImg {
      // vertical-align: middle;
      margin: 0 5px;
      padding: 6px 8px;
      border-radius: 4px;
      box-sizing: border-box;
      background: #e5eeff;
    }
    .el-table {
      border: 0;
    }
  }
}
#cloud_expand {
  min-width: 66px;
  height: 32px;
  background: #2677ff;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  // line-height: 32px;
  text-align: center;
  padding: 0 12px;
  border: 0;
  margin: 0;
  margin-right: 33px;
  &:hover {
    background-color: #3d85ff;
  }
  &:focus {
    background-color: #0055e5;
  }
}
/deep/#cloud_select {
  border-right: 0;
  border-radius: 0px;
  width: 80px;
  background: rgba(255, 255, 255, 0.5);
}
/deep/#cloud_enter {
  border: 0 !important;
  background-color: #fafbff;
}
.nameClass {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  font-size: 14px;
}
.nameClass > span {
  display: inline-block;
  max-width: 85%;
  height: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/deep/.el-collapse-item__header {
  position: relative;
  // display: flex;
  // align-items: center;
}
/deep/.el-collapse-item__header > i {
  position: absolute;
  right: 0px;
  bottom: 18px;
}
/deep/.el-collapse-item__header,
/deep/.el-collapse-item__wrap {
  border: 0;
  color: #62666c;
}
/deep/.el-collapse-item {
  padding: 0px 8px;
}
/deep/.el-collapse {
  .is-active {
    background: #eff2f6;
    font-weight: 500;
    color: #37393c;
  }
}
/deep/.el-collapse-item__content {
  background: #eff2f6;
  padding-bottom: 0;
}
.collapseType {
  color: #62666c;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0px;
  font-size: 14px;
  cursor: pointer;
}
.fangkuai {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: rgba(38, 119, 255, 0.28);
  border-radius: 1px;
  transform: rotate(45deg);
  margin-left: 4px;
  margin-right: 5px;
  vertical-align: middle;
}
.fangkuaiblue {
  background-color: #2677ff;
}
.collapseCount {
  color: #9ba4b2;
}
.dropdownMore {
  position: absolute;
  right: 20px;
  bottom: 0;
  display: none;
}
.dropdownMore > span {
  color: #62666c;
  background: #dce2e9;
  border-radius: 4px;
  padding: 0px 3px;
}
.confirmBox {
  margin-left: 0;
}
.confirmBoxListPic {
  width: 120px;
  margin: 16px 0 16px 16px;
}
.confirmBoxListPicTmp {
  position: absolute;
  left: 20%;
}
.confirmBoxListPicTmp1 {
  position: absolute;
  left: 18%;
  top: 16px;
  z-index: 99;
}
.collapsBox {
  position: absolute;
  right: 0;
  font-size: 14px;
  font-weight: 400;
  color: rgba(38, 119, 255, 1);
}
.headerTitleTmp {
  width: 100%;
  position: absolute;
  top: -39px;
  left: 0;
  font-weight: 700;
  font-size: 16px;
  color: #37393c;
  .goback {
    font-weight: 700;
    color: #2677ff;
    font-size: 14px;
    cursor: pointer;
    .el-icon-arrow-left {
      font-weight: 700;
    }
  }
  .spline {
    margin: 0 8px;
    color: #62666c;
  }
  & > p {
    display: inline-block;
    margin-left: 16px;
    font-size: 12px;
    color: #62666c;
    span {
      margin-right: 12px;
    }
  }
}
.companyName {
  font-size: 22px;
  font-weight: 600;
  color: rgba(55, 57, 60, 1);
  // margin-left: 12px;
}
.companyImg {
  width: 80px;
  height: 40px;
  display: flex;
  justify-content: center;
}
.companyNameImage {
  display: flex;
  height: 52px;
  align-items: center;
  box-sizing: border-box;
  .el-icon-search {
    font-size: 16px;
    color: #2677ff;
    margin-left: 8px;
    font-weight: bold;
    cursor: pointer;
  }
}
.companyIntroduction {
  width: 93%;
  height: 146px;
  margin-top: 18px;
  border-radius: 4px;
  background: linear-gradient(270deg, rgba(38, 119, 255, 0.1) 0%, rgba(38, 119, 255, 0.04) 100%);
  border: 1px solid rgba(233, 241, 255, 1);
  backdrop-filter: blur(2px);
  z-index: 2;
  position: absolute;
  bottom: 20px;
  padding: 16px 16px 20px 16px;
  box-sizing: border-box;
  font-size: 14px;
  font-weight: 400;
  color: rgba(98, 102, 108, 1);
  p {
    height: 100%;
    text-indent: 28px;
    line-height: 22px;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
.companyIntroductionTmp {
  overflow: auto;
}
.companyImgBox {
  width: 151px;
  height: 151px;
  position: absolute;
  z-index: 1;
  top: -20px;
  right: 0;
  background-image: url('../../assets/images/companyClue/company.png');
  background-size: 100% 100%;
}
.companyText {
  margin-left: 8px;
}
.companyText > p:nth-child(1) {
  font-size: 14px;
  font-weight: 500;
  color: rgba(55, 57, 60, 1);
}
.companyText > p:nth-child(2) {
  font-size: 22px;
  font-weight: 500;
  color: rgba(55, 57, 60, 1);
}
.viewMoreBox {
  font-size: 14px;
  font-weight: 400;
  color: rgba(38, 119, 255, 1);
  cursor: pointer;
}
.pieTitle {
  width: 100%;
  height: 44px;
  border-bottom: 1px solid rgba(233, 235, 239, 1);
  display: flex;
  align-items: center;
  padding-left: 16px;
  box-sizing: border-box;
  font-size: 14px;
  font-weight: 500;
  color: rgba(57, 57, 60, 1);
  background: #ffff;
}
.pieMainBox {
  width: 100%;
  height: 134px;
  padding: 16px;
  box-sizing: border-box;
  display: flex;
  background: #ffff;
  .mainPieData {
    width: 33%;
    height: 102px;
    background: rgba(245, 248, 252, 1);
    padding: 16px;
    box-sizing: border-box;
    display: flex;
    .dataLeft {
      width: 65%;
      .leftBoxTit {
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        color: rgba(55, 57, 60, 1);
        span {
          margin-left: 5px;
        }
      }
      div {
        display: flex;
      }
      //  background: yellow;
      .boxStatus {
        width: 10px;
        height: 10px;
        clip-path: polygon(50% 0, 100% 50%, 50% 100%, 0 50%);
        transition: 1s clip-path;
        position: relative;
      }
      .boxStatus1 {
        background: rgba(248, 193, 54, 1);
      }
      .boxStatus2 {
        background: rgba(16, 213, 149, 1);
      }
      .boxStatus3 {
        background: rgba(255, 70, 70, 1);
      }
      .boxStatus4 {
        background: rgba(38, 119, 255, 1);
      }
      .boxStatus5 {
        background: rgba(16, 213, 149, 1);
      }
      .boxStatus6 {
        background: rgba(19, 183, 255, 1);
      }
    }
    .dataRight {
      width: 35%;
      display: flex;
      align-items: center;
      position: relative;
      .p1 {
        position: absolute;
        left: 28px;
      }
      .p2 {
        position: absolute;
        left: 25px;
      }
      .p3 {
        position: absolute;
        left: 21px;
      }
      .p4 {
        position: absolute;
        left: 17px;
      }
      .p5 {
        position: absolute;
        left: 13px;
      }
    }
  }
}
.pOne {
  font-size: 12px;
  font-weight: 400;
  color: rgba(172, 180, 192, 1);
  margin-top: 14px;
}
.pTwo {
  font-size: 20px;
  font-weight: 600;
  color: rgba(55, 57, 60, 1);
}
.pieMainBox > .mainPieData:nth-child(2) {
  margin-left: 12px;
}
.pieMainBox > .mainPieData:nth-child(3) {
  margin-left: 12px;
}
.companyTextBoxTmp {
  width: 58%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.progressNumTmp {
  width: 100%;
  position: relative;
}
.progressNumTmp > div:nth-child(3) {
  position: absolute;
  right: 0;
}
/deep/ .el-select__caret {
  width: 14px;
  display: flex;
  align-items: center;
}
/deep/ .el-input__inner {
  padding-right: 14px !important;
}
/deep/ .el-select {
  margin-left: 0 !important;
}
.checkoutOne {
  margin-right: 5px;
}
.groupHeader1 {
  display: flex;
  justify-content: space-between;
  .iconClass {
    line-height: 20px !important;
  }
  .title {
    color: #37393c;
    font-size: 14px;
    max-width: 40%;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  // .checkoutAll{

  // /deep/.el-checkbox__label{
  // max-width: 30%;
  // display: inline-block;
  // overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  // }
  // }
}
@media (max-width: 1623px) {
  .groupClass {
    .icon-text {
      display: none !important;
    }
  }
}

/deep/ .el-row {
  .el-col {
    &:first-child {
      padding-left: 0 !important;
    }
    &:last-child {
      padding-right: 0 !important;
    }
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
