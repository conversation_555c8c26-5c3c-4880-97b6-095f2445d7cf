<template>
  <div class="containerBox">
    <div class="myBox">
      <div class="inputBox">
        <span>公共线索库</span>
        <el-autocomplete
          class="inline-input"
          v-model="inputSearch"
          :fetch-suggestions="querySearch"
          @select="selectChange"
          placeholder="请输入企业名称进行搜索"
        ></el-autocomplete>
        <el-button @click.native="Inquire" slot="append" icon="el-icon-search"></el-button>
        <el-tooltip
          class="item"
          effect="dark"
          placement="top"
          :content="'只支持查询本企业以及控股企业的线索数据。'"
          :open-delay="500"
        >
          <i class="el-icon-question searchIcon"></i>
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          placement="top"
          :content="'只支持查询本企业以及控股企业的线索数据。'"
          :open-delay="500"
        >
          <p>查询规则</p>
        </el-tooltip>
      </div>
      <div v-if="!tableFlag" class="statisticsBox" v-loading="chartsLoaing">
        <div v-for="(item, key) in fofainfo" :key="key">
          <div :class="'countBox countBox' + key">
            <div class="dataNumBox">
              <span class="nameBox">{{ item.name }}</span>
              <span class="numBox">{{
                formatNum(item['data'].count == undefined ? 0 : item['data'].count)
              }}</span>
            </div>
            <div class="dataNumBox">
              <img :src="require('../../assets/images/' + item.key + '.png')" alt="" />
            </div>
          </div>
          <div :id="item.key" class="chartsBox"> </div>
        </div>
      </div>
      <div v-if="tableFlag" class="tableBox" v-loading="loading">
        <div class="eltableBox">
          <ul class="ulBox">
            <li class="ultitle">线索分类</li>
            <li
              v-for="(item, index) in tabList"
              :key="index"
              @click="changeTab(item.label)"
              class="clueList"
              :style="
                tabActive == item.label ? 'background: #EAEFF6;color:#2677FF;font-weight: 500;' : ''
              "
            >
              <span class="fangkuai" v-if="item.label == 0"></span>
              <span class="fangkuai fangkuai1" v-else-if="item.label == 1"></span>
              <span class="fangkuai fangkuai2" v-else-if="item.label == 2"></span>
              <span class="fangkuai fangkuai4" v-else-if="item.label == 6"></span>
              <span class="fangkuai fangkuaiKeyword" v-else-if="item.label == 4"></span>
              <span class="fangkuai fangkuai3" v-else></span>
              {{ getType(item.name) }}
            </li>
          </ul>
          <div class="myTable" v-loading="loading">
            <div class="tableLabel filterTab">
              <div> </div>
              <div>
                <el-button class="normalBtnRe" type="primary" @click="goBack">返回</el-button>
                <el-button
                  class="normalBtnRe"
                  type="primary"
                  id="ip_all"
                  @click="exportData"
                  :loading="exportLoading"
                  >导出</el-button
                >
              </div>
            </div>
            <div class="myTableContent" ref="myTableContent">
              <clueTable
                ref="clueTable"
                @scrollChangeTab="scrollChangeTab"
                :handleIsShow="false"
                @checkedArr="checkedArrFun"
                :tableData="tableData"
                :tableHeader="tableHeader"
              ></clueTable>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import clueTable from '../unit_surveying/taskSecondTable.vue'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import {
  getCluecompanyList,
  getPublicSearchList,
  getPublicExport,
  getFofaAssetsAll
} from '@/api/apiConfig/clue.js'

export default {
  components: { tableTooltip, clueTable },
  data() {
    return {
      formInline: {
        page: 1,
        per_page: 10,
        operate_company_id: '',
        company_name: ''
      },
      pageSizeArr: [10, 30, 50, 100],
      exportLoading: false,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      pageSize: 10,
      total: 0,
      chartsLoaing: false,
      tableData: [],
      checkedAll: false,
      checkedArr: [],
      currentPage: 1,
      inputSearch: '',
      restaurants: [],
      fofaAssets: {},
      loading: false,
      tableFlag: false,
      charts: {
        domain: null,
        cert: null,
        icp: null,
        icon: null
      },
      tabList: [
        {
          label: '6',
          name: 'IP段'
        },
        {
          label: '0',
          name: '域名'
        },
        {
          label: '2',
          name: 'ICP'
        },
        {
          label: '1',
          name: '证书'
        },
        {
          label: '3',
          name: 'ICON'
        },
        {
          label: '4',
          name: '关键词'
        }
      ],
      tabActive: '6',
      tableHeader: [
        {
          label: '线索名称',
          name: 'content',
          fixed: 'left'
        },
        {
          label: '创建时间',
          name: 'created_at'
        },
        {
          label: '更新时间',
          name: 'updated_at'
        }
      ],
      fofainfo: [
        {
          name: '域名库',
          key: 'domain',
          data: {},
          background: ['rgba(38, 119, 255, 0.3)', 'rgba(38, 119, 255, 0.04)'],
          color: 'rgba(38, 119, 255, 1)'
        },
        {
          name: '证书库',
          key: 'cert',
          data: {},
          background: ['rgba(19, 183, 255, 0.3)', 'rgba(19, 183, 255, 0.04)'],
          color: 'rgba(19, 183, 255, 1)'
        },
        {
          name: 'ICP库',
          key: 'icp',
          data: {},
          background: ['rgba(16, 213, 149, 0.3)', 'rgba(16, 213, 149, 0.04) '],
          color: 'rgba(16, 213, 149, 1)'
        },
        {
          name: 'ICON库',
          key: 'icon',
          data: {},
          background: ['rgba(255, 121, 0, 0.3)', 'rgba(255, 121, 0, 0.04) '],
          color: 'rgba(255, 121, 0, 1)'
        }
      ],
      checkedAlls: false,
      isIndeterminate: false,
      checkedArr0: [],
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      selectnum: 0, //选中的数量
      domain_num: 0,
      ip_num: 0,
      icp_num: 0,
      cert_num: 0,
      icon_num: 0,
      keyword_num: 0
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      this.inputSearch = ''
      this.getSearchData()
      this.getFofaData()
    },
    inputSearch(val) {
      if (val == '') {
        this.tableFlag = false
        this.getFofaData()
      }
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany']),
    ...mapState(['currentCompany'])
  },
  methods: {
    // 滚动改变tab线索分类选中
    scrollChangeTab(type) {
      this.tabActive = String(type)
    },
    goBack() {
      this.tableFlag = false
      this.tabActive = '6'
      this.inputSearch = ''
      this.checkedAlls = false
      this.tableData = []
      this.domain_num = 0
      this.ip_num = 0
      this.icp_num = 0
      this.cert_num = 0
      this.icon_num = 0
      this.keyword_num = 0
    },
    checkedArrFun(arr) {
      this.checkedArr0 = arr
        .filter((item) => {
          return item.type == 0
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr1 = arr
        .filter((item) => {
          return item.type == 1
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr2 = arr
        .filter((item) => {
          return item.type == 2
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr3 = arr
        .filter((item) => {
          return item.type == 3
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr4 = arr
        .filter((item) => {
          return item.type == 4
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr5 = arr
        .filter((item) => {
          return item.type == 5
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr6 = arr
        .filter((item) => {
          return item.type == 6
        })
        .map((item) => {
          return item.id
        })
    },
    //切换线索分类
    changeTab(val) {
      var arr = this.tableData.map((item) => {
        return item.type
      })
      var num = arr.indexOf(Number(val))
      if (num != -1) {
        // 获取行高
        let dataList = document.getElementsByClassName('borderWrap')[0].clientHeight
        // 滚动距离
        document.getElementsByClassName('clueContent')[0].scrollTop = dataList * num
        setTimeout(() => {
          this.tabActive = val
        }, 100)
      }
    },
    getType(val) {
      //tabActive
      if (val == '域名') {
        return `域名(${this.domain_num})`
      } else if (val == 'ICP') {
        return `ICP(${this.icp_num})`
      } else if (val == '证书') {
        return `证书(${this.cert_num})`
      } else if (val == 'ICON') {
        return `ICON(${this.icon_num})`
      } else if (val == 'IP段') {
        return `IP段(${this.ip_num})`
      } else if (val == '关键词') {
        return `关键词(${this.keyword_num})`
      }
    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    handleSelectable(row, index) {
      return !this.checkedAlls
    },
    formatNum(val) {
      // return String(num).replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, '$1,');
      let num = 10000
      var sizesValue = ''
      /**
       * 判断取哪个单位
       */
      if (val < 1000) {
        // 如果小于1000则直接返回
        sizesValue = ''
        return val
      } else if (val > 1000 && val < 9999) {
        sizesValue = '千+'
      } else if (val > 10000 && val < 99999999) {
        sizesValue = '万+'
      } else if (val > 100000000) {
        sizesValue = '亿+'
      }
      /**
       * 大于一万则运行下方计算
       */
      let i = Math.floor(Math.log(val) / Math.log(num))
      /**
       * toFixed(0)看你们后面想要取值多少，我是不取所以填了0，一般都是取2个值
       */
      var sizes = Math.floor(val / Math.pow(num, i))
      sizes = sizes + sizesValue
      // resolve(sizes);// 输出
      return sizes
    },
    async exportData() {
      if (!this.checkedAlls) {
        if (
          this.checkedArr0.length == 0 &&
          this.checkedArr1.length == 0 &&
          this.checkedArr2.length == 0 &&
          this.checkedArr3.length == 0 &&
          this.checkedArr4.length == 0 &&
          this.checkedArr5.length == 0 &&
          this.checkedArr6.length == 0
        ) {
          this.$message.error('请选择要导出的数据！')
          return
        }
      }
      let obj = {
        ids: this.checkedAlls
          ? []
          : [
              ...this.checkedArr0,
              ...this.checkedArr1,
              ...this.checkedArr2,
              ...this.checkedArr3,
              ...this.checkedArr4,
              ...this.checkedArr5,
              ...this.checkedArr6
            ],
        operate_company_id: this.currentCompany,
        company_name: this.checkedAlls ? this.inputSearch : ''
      }
      this.exportLoading = true
      let res = await getPublicExport(obj)
      if (res.code == 0) {
        this.exportLoading = false
        this.checkedAlls = false
        this.isIndeterminate = false
        this.selectnum = 0
        this.checkedArr0 = []
        this.checkedArr1 = []
        this.checkedArr2 = []
        this.checkedArr3 = []
        this.checkedArr4 = []
        this.checkedArr5 = []
        this.checkedArr6 = []
        this.download(this.showSrcIp + res.data.url)
      }
    },
    selectChange(value) {
      this.Inquire()
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    async Inquire() {
      if (this.inputSearch == '') {
        this.$message.error('请输入企业名称查询')
        return
      }
      this.tableFlag = true
      this.formInline.company_name = this.inputSearch
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.loading = true
      this.tableData = []
      let tableData_ip = []
      let tableData_domain = []
      let tableData_icp = []
      let tableData_cert = []
      let tableData_icon = []
      let tableData_keyword = []
      this.checkedAlls = false
      this.domain_num = 0
      this.ip_num = 0
      this.icp_num = 0
      this.cert_num = 0
      this.icon_num = 0
      this.keyword_num = 0
      let res = await getCluecompanyList(this.formInline).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        if (res.data) {
          // 0域名，1证书，2ICP，3ICON，6IP段，4关键词
          res.data.forEach((item) => {
            if (item.type == 6) {
              tableData_ip.push(item)
              this.ip_num = tableData_ip.length
            } else if (item.type == 0) {
              tableData_domain.push(item)
              this.domain_num = tableData_domain.length
            } else if (item.type == 2) {
              tableData_icp.push(item)
              this.icp_num = tableData_icp.length
            } else if (item.type == 1) {
              tableData_cert.push(item)
              this.cert_num = tableData_cert.length
            } else if (item.type == 3) {
              tableData_icon.push(item)
              this.icon_num = tableData_icon.length
            } else if (item.type == 4) {
              tableData_keyword.push(item)
              this.keyword_num = tableData_keyword.length
            }
          })
          // 默认选中有数据的tab
          if (this.ip_num > 0) {
            this.tabActive = '6'
          } else if (this.domain_num > 0) {
            this.tabActive = '0'
          } else if (this.icp_num > 0) {
            this.tabActive = '2'
          } else if (this.cert_num > 0) {
            this.tabActive = '1'
          } else if (this.icon_num > 0) {
            this.tabActive = '3'
          } else if (this.keyword_num > 0) {
            this.tabActive = '4'
          }
        }
        // 用于滚动监听，按左侧分类tab顺序排序
        this.tableData = [
          ...tableData_ip,
          ...tableData_domain,
          ...tableData_icp,
          ...tableData_cert,
          ...tableData_icon,
          ...tableData_keyword
        ]
      }
    },
    async getSearchData() {
      let res = await getPublicSearchList({ operate_company_id: this.currentCompany })
      let data = []
      if (res.data.length > 0) {
        res.data.map((v) => {
          data.push({ value: v })
        })
      }
      this.restaurants = data
    },
    checkAllChange() {
      this.checkedAlls = false
      this.isIndeterminate = true
    },
    async getFofaData() {
      this.chartsLoaing = true
      let res = await getFofaAssetsAll()
      this.fofaAssets = res.data
      this.fofainfo.map((item) => {
        this.chartsFunc(item.key, item.background, item.color)
        if (res.data[item.key]) {
          item.data = res.data[item.key][res.data[item.key].length - 1]
        }
      })
      this.chartsLoaing = false
    },
    chartsFunc(id, background, color) {
      let xAxis = []
      let yAxis = []
      if (this.fofaAssets[id]) {
        this.fofaAssets[id].map((item) => {
          xAxis.push(item.time)
          yAxis.push(item.count)
        })
      }
      // xAxis.unshift('2023-01')
      // yAxis.unshift(yAxis[0]-25555)
      const chartDom = document.getElementById(id)
      this.charts[id] = this.$echarts.init(chartDom)
      const option = {
        grid: {
          top: '5%',
          left: '10%',
          bottom: '20%',
          right: '10%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxis
        },
        yAxis: {
          min: function (value) {
            return Math.floor(value.min) - value.max * 0.1
          },
          max: function (value) {
            return Math.floor(value.max)
          },
          type: 'value',
          lineColor: '#ffffff', //设置y轴颜色
          axisLabel: {
            show: false,
            margin: 20,
            textStyle: {
              color: '#d1e6eb'
            }
          }
        },
        tooltip: {
          trigger: 'axis',
          textStyle: { color: '#424242' },
          renderMode: 'html',
          className: 'tooltip'
        },
        series: [
          {
            data: yAxis,
            type: 'line',
            showSymbol: false,
            smooth: true,
            itemStyle: {
              normal: {
                lineStyle: {
                  color: color, //改变折线颜色
                  type: 'solid'
                }
              }
            },
            areaStyle: {
              //折线图颜色半透明
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: background[0] // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: background[1] // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
        ]
      }

      this.charts[id].setOption(option)
      window.addEventListener('resize', () => {
        this.charts[id].resize()
      })
    }
  },
  created() {
    this.getSearchData()
    this.getFofaData()
  },
  mounted() {}
}
</script>
<style lang="less" scoped>
.containerBox {
  width: 100%;
  height: 100%;
}

.main_body {
  width: 100%;
  height: 100%;
  overflow: auto;
}
.myBox {
  display: flex;
  flex-direction: column;
  width: 100%;
  // height: 280px;
  height: 100%;
  margin-bottom: 12px;
}
.myBox > div {
  width: 100%;
}
.tableBox {
  width: 100%;
  height: 80%;
  padding: 16px;
  box-sizing: border-box;
  background: #ffff;
}
.statisticsBox {
  width: 100%;
  height: 70%;
  // display: flex;
  padding-top: 16px;
  box-sizing: border-box;
  justify-content: center;
  // justify-content: space-between;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  overflow: auto;
  > div {
    width: 39%;
    height: 48%;
    border-radius: 4px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  }
}
.statisticsBox > div:nth-child(2) {
  margin-left: 12px;
}
.statisticsBox > div:nth-child(4) {
  margin-left: 12px;
}
.chartsBox {
  width: 100%;
  height: 60%;
}
.countBox {
  width: 100%;
  height: 40%;
  padding: 0 24px 0 24px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  .dataNumBox {
    display: flex;
    flex-direction: column;
    .nameBox {
      font-size: 14px;
      font-weight: 500;
      color: rgba(98, 102, 108, 1);
    }
    .numBox {
      font-size: 28px;
      font-weight: 500;
      color: rgba(55, 57, 60, 1);
    }
  }
}
.el-button {
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.inputBox {
  width: 100%;
  height: 17%;
  display: flex;
  background-image: url('../../assets/images/publicClueBank.png');
  background-size: 100% 100%;
  align-items: center;
  position: relative;
  justify-content: center;
  span {
    position: absolute;
    font-size: 14px;
    font-weight: 500;
    top: 16px;
    left: 16px;
  }
  /deep/.el-button {
    width: 40px;
    height: 40px;
    border-radius:
      0px 4px,
      4px,
      0px;
    background: rgba(38, 119, 255, 1);
    color: #ffff;
    margin-right: 2px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
/deep/.el-autocomplete {
  width: 65%;
  height: 40px;
}
/deep/ .el-input__inner {
  height: 40px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.tableWarp {
  height: calc(100% - 129px);
}
.eltableBox {
  display: flex;
  padding: 0px 20px 0px 0px;
  height: 100% !important;
  justify-content: space-between;
}
.ulBox {
  width: 160px;
  height: 100%;
  background: rgba(255, 255, 255, 0.36);
  border-right: 1px solid #e9ebef;
  box-sizing: border-box;
  padding-top: 5px;
  li {
    color: #62666c;
    cursor: pointer;
    height: 44px;
    background: rgba(234, 239, 246, 0);
    display: flex;
    align-items: center;
  }
  .ultitle {
    font-weight: 500;
    color: #37393c;
    padding-left: 16px;
  }
}
.fangkuai {
  width: 6px;
  height: 6px;
  background: #2677ff;
  box-shadow: 0px 0px 4px 0px rgba(38, 119, 255, 0.74);
  border-radius: 1px;
  margin: 0px 8px 0px 16px;
}
.fangkuai4 {
  background: #5346ff;
}
.fangkuai2 {
  background: #05d4a7;
}
.fangkuai1 {
  background: #13b7ff;
}
.fangkuai3 {
  background: #ec8f3c;
}
.fangkuaiKeyword {
  background: #ec5f5c;
}
.myTable {
  width: calc(100% - 170px);
  padding: 0px !important;
  height: calc(100% - 12px) !important;
}
.myTableContent {
  height: calc(100% - 31px);
  overflow: auto;
  background-color: #fff;
}
.myTableHeader {
  display: flex;
  align-items: center;
  height: 52px;
  font-weight: 600;
  color: #62666c;
  font-size: 12px;
  margin-left: 3px;
  background: #f2f3f5;
  border: 1px solid #e4e8ef;
  .chooseBox {
    padding: 0px 14px 0px 10px;
    width: 55px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
  }
  .operationBox {
    padding: 0px 10px;
    width: 55px;
    box-sizing: border-box;
  }
  .shuBox {
    width: 10px;
  }
}
.tableHeaderBox {
  flex-grow: 1;
}
.tableHeaderBox > div {
  padding: 0px 10px;
  box-sizing: border-box;
}
.qiyeBox {
  padding: 0px 0px 0px 10px !important;
}
/deep/.el-table {
  border: none !important;
}
.eltable_domain {
  border-left: 4px solid #2677ff !important;
  border-radius: 4px 0px 0px 4px;
}
.eltable_ip {
  border-left: 4px solid #1059d5 !important;
}
/deep/.boxTwo1 {
  height: calc(100% - 156px) !important;
  padding-top: 2px !important;
}
/deep/.boxTwo {
  height: calc(100% - 270px) !important;
  padding-top: 2px !important;
}
.eltable_icp {
  border-left: 4px solid #05d4a7 !important;
}
.eltable_cert {
  border-left: 4px solid #13b7ff !important;
}
.eltable_icon {
  border-left: 4px solid #ec8f3c !important;
}
.eltable_keyword {
  border-left: 4px solid #ec5f5c !important;
}
.tableLabel {
  padding: 0px 0px 0px 4px !important;
  margin-bottom: 10px !important;
  margin-top: 10px !important;
}
.zhanwu {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
}
.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  & > div {
    display: flex;
    align-items: center;
    .normalBtnRe {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
    }
    .el-input {
      width: 240px;
      margin-right: 12px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      margin-left: 4px;
      cursor: pointer;
    }
  }
}
.searchIcon {
  font-size: 16px;
  color: rgba(38, 119, 255, 0.2);
}
p {
  font-size: 14px;
  font-weight: 400;
  color: rgba(98, 102, 108, 1);
  box-sizing: border-box;
  padding-left: 4px;
}
</style>
