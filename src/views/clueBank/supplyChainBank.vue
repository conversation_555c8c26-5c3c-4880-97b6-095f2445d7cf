<template>
  <div class="container">
    <div class="headerTitle">供应链线索库</div>
    <div class="home_header">
      <div class="filterTab">
        <div> </div>
        <div>
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="exportList"
            :loading="exportLoading"
            id="cloud_export"
            >导出</el-button
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="removeMore"
            :loading="removeLoading"
            id="cloud_del"
            >删除</el-button
          >
        </div>
      </div>
      <div
        style="display: flex; justify-content: space-between; padding: 0 20px; margin-bottom: 20px"
      >
        <div class="confirmBox">
          <el-radio-group v-model="tabActiveName" @change="handleClick(tabActiveName)">
            <el-radio-button :label="item.name" v-for="(item, index) in tabData" :key="index"
              >{{ item.label }}({{ item.count }})</el-radio-button
            >
          </el-radio-group>
        </div>
        <div style="width: 40%; display: flex; justify-content: space-between; align-items: center">
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAllObj[checkedAllArr[tabActiveName]]"
            @change="checkAllChange"
            >选择全部</el-checkbox
          >
          <el-input
            style="width: 75%"
            v-if="tabActiveName != 3"
            v-model="keywordObj[tabKeywordArr[tabActiveName]]"
            @keyup.enter.native="checkFuncList('tmp')"
            :placeholder="`请输入${getLabel()}检索`"
            clearable
            id="cloud_keycheck"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="checkFuncList('tmp')"
            ></el-button>
          </el-input>
        </div>
      </div>
      <div :class="hightFilterIsShow()" v-loading="loading">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F5F7FA', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          :ref="'eltable' + tabActiveName"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot="header">
              <span v-if="item.name == 'content'">{{ getLabel() }}</span>
              <span v-else>{{ item.label }}</span>
            </template>
            <template slot-scope="scope">
              <span v-if="item.name == 'content'">
                <span v-if="tabActiveName == '3'">
                  <el-image
                    class="logoBg"
                    :src="
                      scope.row[item.name].includes('http')
                        ? scope.row[item.name]
                        : showSrcIp + scope.row[item.name]
                    "
                    lazy
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                  <span>{{ scope.row.hash }}</span>
                </span>
                <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
                <span v-if="scope.row['user_sign_count'] / 1 > 0" class="blueRadiusBorder">{{
                  `${scope.row['user_sign_count']}个用户已标记`
                }}</span>
              </span>
              <span v-else-if="item.name == 'chain_list'">
                <span v-if="scope.row[item.name] && scope.row[item.name].length > 0">
                  <el-tooltip class="item" effect="light" placement="top" popper-class="chainClass">
                    <div slot="content" style="position: relative">
                      <el-tooltip
                        effect="light"
                        class="item"
                        placement="top"
                        content="一键复制"
                        v-if="scope.row[item.name] && scope.row[item.name].length != 0"
                        :open-delay="500"
                      >
                        <i
                          class="el-icon-document-copy"
                          @click="copyClick(scope.row[item.name])"
                          style="
                            color: #2677ff;
                            cursor: pointer;
                            position: absolute;
                            right: -6px;
                            top: 0;
                          "
                        ></i>
                      </el-tooltip>
                      <span v-for="(con, index) in getChains(scope.row[item.name])" :key="index">
                        <span>{{ index + 1 }}、</span>
                        <span v-if="con.type && con.type == 3">
                          <el-image
                            :src="
                              con.content.includes('http') ? con.content : showSrcIp + con.content
                            "
                          >
                            <div slot="error" class="image-slot">
                              <i class="el-icon-picture-outline"></i>
                            </div>
                          </el-image>
                        </span>
                        <span v-else
                          >{{ $punyCode.toUnicode(con.content || con)
                          }}{{ con.punycode_domain ? '(' + con.punycode_domain + ')' : '' }}</span
                        >
                        <i
                          v-if="index < getChains(scope.row[item.name]).length - 1"
                          class="el-icon-right iconRight"
                        ></i>
                      </span>
                    </div>
                    <span style="display: flex !important; align-items: center">
                      {{ scope.row[item.name].length - 1 }}
                      <img
                        src="../../assets/images/chain.svg"
                        alt=""
                        style="width: 12px; margin-left: 5px; vertical-align: middle"
                      />
                    </span>
                  </el-tooltip>
                </span>
                <span v-else>-</span>
              </span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { mapGetters, mapState } from 'vuex'
import {
  supplyChiaExport,
  getSupplyChiaList,
  goPointClue,
  supplyChiaCount
} from '@/api/apiConfig/clue.js'

export default {
  components: {
    tableTooltip
  },
  data() {
    return {
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      tabActiveName: '0',
      exportLoading: false,
      removeLoading: false,
      keywordObj: {
        domain_keyword: '',
        cert_keyword: '',
        icp_keyword: '',
        key_keyword: '',
        ip_keyword: ''
      },
      checkedAllObj: {
        domain_checked: false,
        cert_checked: false,
        icp_checked: false,
        icon_checked: false,
        key_checked: false,
        ip_checked: false
      },
      tabData: [
        {
          name: '0',
          label: '根域',
          count: ''
        },
        {
          name: '1',
          label: '证书',
          count: ''
        },
        {
          name: '2',
          label: 'ICP',
          count: ''
        },
        {
          name: '3',
          label: 'ICON',
          count: ''
        },
        {
          name: '4',
          label: '关键词',
          count: ''
        },
        {
          name: '6',
          label: 'IP段',
          count: ''
        }
      ],
      formInline: {
        page: 1,
        per_page: 10,
        keyword: '',
        type: '',
        operate_company_id: ''
      },
      tableHeader: [
        {
          label: '根域',
          name: 'content',
          minWidth: '200px'
        },
        // {
        //   label: '企业名称',
        //   name: 'clue_company_name',
        //   minWidth: '120px'
        // },
        {
          label: '来源',
          name: 'source_label',
          minWidth: '120px'
        },
        {
          label: '添加时间',
          name: 'created_at',
          minWidth: '120px'
        },
        {
          label: '线索链',
          name: 'chain_list'
        }
      ],
      checkedArr0: [],
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      loading: false,
      user: {
        role: ''
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
    this.getTabNum()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.resetTable()
      if (this.user.role == 2) {
        this.getData(false)
        this.getTabNum()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    getTypeName(status) {
      let str = ''
      switch (status) {
        case 0:
          str = '根域'
          break
        case 1:
          str = '证书'
          break
        case 2:
          str = 'ICP'
          break
        case 3:
          str = 'ICON'
          break
        case 4:
          str = '关键词'
          break
        case 6:
          str = 'IP段'
          break
        default:
      }
      return str
    },
    async getTabNum() {
      let res = await supplyChiaCount({ operate_company_id: this.currentCompany })
      if (res.data) {
        this.tabData.forEach((item) => {
          res.data.forEach((ch) => {
            if (item['name'] == ch['type']) {
              item.count = ch.count
            }
          })
        })
      }
    },
    getLabel() {
      let label = ''
      if (this.tabActiveName == '0') {
        label = '根域'
      } else if (this.tabActiveName == '1') {
        label = '证书'
      } else if (this.tabActiveName == '2') {
        label = 'ICP'
      } else if (this.tabActiveName == '3') {
        label = 'ICON'
      } else if (this.tabActiveName == '4') {
        label = '关键词'
      } else if (this.tabActiveName == '6') {
        label = 'IP段'
      }
      return label
    },
    resetTable() {
      this.formInline.keyword = ''
      this.keywordObj = {
        domain_keyword: '',
        cert_keyword: '',
        icp_keyword: '',
        key_keyword: '',
        ip_keyword: ''
      }
      this.checkedAllObj = {
        domain_checked: false,
        cert_checked: false,
        icp_checked: false,
        icon_checked: false,
        key_checked: false,
        ip_checked: false
      }
      this.$refs['eltable' + this.tabActiveName].clearSelection()
    },
    checkAllChange() {
      if (this.checkedAllObj[this.checkedAllArr[this.tabActiveName]]) {
        this.tableData.forEach((row) => {
          this.$refs['eltable' + this.tabActiveName].toggleRowSelection(row, true)
        })
      } else {
        this.$refs['eltable' + this.tabActiveName].clearSelection()
      }
    },
    async removeMore() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let clueData = [
        {
          id: this.checkedAllObj.domain_checked
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAllObj.domain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.cert_checked
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAllObj.cert_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icp_checked
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAllObj.icp_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icon_checked
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAllObj.icon_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.key_checked
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAllObj.key_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.ip_checked
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAllObj.ip_checked ? '1' : ''
        }
      ]
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_del_cancel',
        confirmButtonClass: 'cloud_del_sure',
        customClass: 'cloud_del',
        type: 'warning'
      })
        .then(async () => {
          this.currentGroupIds = this.currentGroupId
          let obj = {
            tab_status: this.tabActiveName,
            is_supply_chain: 0, // 0取消供应链 1设置为供应链
            group_id: '0',
            data: clueData,
            keyword: {
              domain_keyword: this.keywordObj.domain_keyword,
              cert_keyword: this.keywordObj.cert_keyword,
              icp_keyword: this.keywordObj.icp_keyword,
              key_keyword: this.keywordObj.key_keyword,
              ip_keyword: this.keywordObj.ip_keyword
            },
            operate_company_id: this.currentCompany
          }
          this.removeLoading = true
          let res = await goPointClue(obj).catch(() => {
            this.removeLoading = false
          })
          this.removeLoading = false
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.resetTable()
            this.getData(false)
            this.getTabNum()
          }
        })
        .catch(() => {})
    },
    async exportList() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要导出的数据！')
        return
      }
      let clueData = [
        {
          id: this.checkedAllObj.domain_checked
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAllObj.domain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.cert_checked
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAllObj.cert_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icp_checked
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAllObj.icp_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icon_checked
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAllObj.icon_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.key_checked
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAllObj.key_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.ip_checked
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAllObj.ip_checked ? '1' : ''
        }
      ]
      let obj = {
        data: clueData,
        keyword: {
          domain_keyword: this.keywordObj.domain_keyword,
          cert_keyword: this.keywordObj.cert_keyword,
          icp_keyword: this.keywordObj.icp_keyword,
          key_keyword: this.keywordObj.key_keyword,
          ip_keyword: this.keywordObj.ip_keyword
        },
        operate_company_id: this.currentCompany
      }
      this.exportLoading = true
      let res = await supplyChiaExport(obj).catch(() => {
        this.exportLoading = false
      })
      if (res.code == 0) {
        this.exportLoading = false
        this.resetTable()
        this.getData(false)
        this.getTabNum()
        this.download(this.showSrcIp + res.data.url)
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    getChains(row) {
      let arr = row.filter((item) => {
        return item.content
      })
      return arr
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectable(row, index) {
      return !this.checkedAllObj[this.checkedAllArr[this.tabActiveName]]
    },
    handleSelectionChange(val) {
      let arr = this.$refs['eltable' + this.tabActiveName].selection.filter((item) => {
        return item.type == this.tabActiveName
      })
      this['checkedArr' + this.tabActiveName] = arr // .selection获取当前选中的数据
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    copyClick(tmp) {
      let data = []
      data.push(tmp)
      let text = ''
      data.map((item) => {
        this.getChains(item).map((v, i) => {
          if (v.type && v.type == 3) {
            if (i == item.length - 1 && v) {
              text += 'icon' + '\n'
            } else {
              text += 'icon' + '-->'
            }
          } else {
            if (i < item.length - 1 && v) {
              text += v.content + '-->'
            } else if (i == item.length - 1 && v) {
              text += v.content + '\n'
            } else {
              text += v.content
            }
          }
        })
      })
      this.$copyText(text).then(
        (res) => {
          console.log('复制成功：', res)
          this.$message.success('复制成功')
        },
        (err) => {
          console.log('', err)
          this.$message.error('复制失败')
        }
      )
    },
    checkFuncList() {
      this.currentPage = 1
      this.hightFilterIsShow()
      this.getData(false)
    },
    async getData(tmp) {
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.keyword = this.keywordObj[this.tabKeywordArr[this.tabActiveName]]
      this.loading = true
      let res = await getSupplyChiaList({ data: this.formInline, type: this.tabActiveName })
      this.loading = false
      this.tableData = res.data ? res.data.items : []
      this.total = res.data ? res.data.total : 0
      // 全选操作
      this.$nextTick(() => {
        if (this.checkedAllObj[this.checkedAllArr[this.tabActiveName]]) {
          this.tableData.forEach((row) => {
            this.$refs['eltable' + this.tabActiveName].toggleRowSelection(row, true)
          })
        }
      })
    },
    handleClick(data) {
      this.currentPage = 1
      this.getData(true)
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .confirmBox {
      margin-left: 0;
    }
    .tableWrapFilter {
      height: calc(100% - 212px) !important;
    }
    .tableWrap {
      height: calc(100% - 181px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
      .logoBg {
        vertical-align: middle;
      }
    }
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
