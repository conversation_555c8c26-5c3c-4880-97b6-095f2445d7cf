<template>
  <div class="container">
    <div class="headerTitleTmp"> 线索概览</div>
    <div class="header-module">
      <div
        class="header-item"
        v-for="(item, index) in descriptionItem"
        :key="index"
        @click="item.callback"
      >
        <div class="logo">
          <img src="@/assets/images/clueOverview/header-logo.png" alt="" />
        </div>
        <div class="content">
          <div class="title">{{ item.title }}</div>
          <el-tooltip class="item" effect="dark" :content="item.description" placement="top">
            <p class="description" style="cursor: pointer">{{ item.description }}</p>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="first-module">
      <div class="left">
        <img src="@/assets/images/clueOverview/process.png" alt="" />
      </div>
      <div class="right" v-loading="firstLoading">
        <div class="header-top">
          <span class="title">公共线索库变更</span>
          <span class="date-picker">
            <span
              class="date"
              :class="{
                active: datePickerSelectedType && item.value == datePickerSelectedType.value
              }"
              v-for="item in datePickerTypeList"
              :key="item.value"
              @click="handleClickDatePicker(item)"
              >{{ item.label }}</span
            >
          </span>
        </div>
        <div class="content">
          <div class="total">
            <div class="statistics">
              <div class="item-first">公共线索库总数</div>
              <div class="item-second">{{
                (clueCount.all_count && clueCount.all_count.toLocaleString()) || 0
              }}</div>
              <div class="item-third"
                >{{ datePickerSelectedType.name || '-' }}
                <!-- <i class="el-icon-caret-bottom"></i> -->
                <i v-if="clueCount.all_mom > 0" class="el-icon-caret-top"></i>
                <i v-if="clueCount.all_mom < 0" class="el-icon-caret-bottom"></i>
                <span
                  class="percent"
                  :class="{
                    rise: clueCount.all_mom > 0,
                    decline: clueCount.all_mom < 0,
                    grey: clueCount.all_mom == 0
                  }"
                  >{{ Math.abs(clueCount.all_mom * 100).toFixed(1) }}%</span
                >
              </div>
            </div>
            <div class="statistics-logo">
              <img src="@/assets/images/clueOverview/statistics-logo.png" alt="" />
            </div>
            <img class="total-bg" src="@/assets/images/clueOverview/total-bg.png" alt="" />
          </div>
          <div class="total-bottom descriptions">
            <div class="item" v-for="item in clueType" :key="item.name">
              <div class="item-first">{{ item.label }}总量</div>
              <div class="item-second">{{
                (clueCount[item.name + '_count'] &&
                  clueCount[item.name + '_count'].toLocaleString()) ||
                0
              }}</div>
              <div class="item-third"
                >{{ datePickerSelectedType.name }}
                <i v-if="clueCount[item.name + '_mom'] > 0" class="el-icon-caret-top"></i>
                <i v-if="clueCount[item.name + '_mom'] < 0" class="el-icon-caret-bottom"></i>
                <span
                  class="percent"
                  :class="{
                    rise: clueCount[item.name + '_mom'] > 0,
                    decline: clueCount[item.name + '_mom'] < 0,
                    grey: clueCount[item.name + '_mom'] == 0
                  }"
                  >{{ Math.abs(clueCount[item.name + '_mom'] * 100).toFixed(1) }}%</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="second-module">
      <div class="search-box">
        <div class="search-input">
          <el-autocomplete
            id="companySearchInput"
            class="inline-input"
            v-model="inputSearch"
            :fetch-suggestions="querySearch"
            @select="Inquire"
            placeholder="请输入企业名称进行搜索"
          >
          </el-autocomplete>
          <div class="search-logo" @click="Inquire">
            <img src="@/assets/images/clueOverview/search-logo.png" alt="" />
          </div>
        </div>
        <el-tooltip
          class="item"
          effect="dark"
          placement="top"
          :content="'只支持查询本企业以及控股企业的线索数据。'"
          :open-delay="500"
        >
          <i class="el-icon-question searchIcon"></i>
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          placement="top"
          :content="'只支持查询本企业以及控股企业的线索数据。'"
          :open-delay="500"
        >
          <p>查询规则</p>
        </el-tooltip>
      </div>
      <div class="search-result">
        <div class="header-top">
          <span class="title">公共线索库趋势图</span>
        </div>
        <div v-show="!tableFlag" class="content">
          <div class="legend">
            <div
              class="item"
              :class="{ active: legendSelectItem && legendSelectItem.name == item.name }"
              v-for="item in clueType"
              :key="item.name"
              @click="handleSelectLegend(item)"
            >
              <div class="title">{{ item.label }}</div>
              <span class="num">{{
                (clueAllCountNum[item.name] && clueAllCountNum[item.name].toLocaleString()) || 0
              }}</span
              ><span class="type">月环比</span
              ><i v-if="clueAllCount[item.name + '_mom'] > 0" class="el-icon-caret-top"></i>
              <i v-if="clueAllCount[item.name + '_mom'] < 0" class="el-icon-caret-bottom"></i>
              <span
                class="percent"
                :class="{
                  rise: clueAllCount[item.name + '_mom'] > 0,
                  decline: clueAllCount[item.name + '_mom'] < 0,
                  grey: clueAllCount[item.name + '_mom'] == 0
                }"
                >{{ Math.abs(clueAllCount[item.name + '_mom'] * 100).toFixed(1) }}%</span
              >
            </div>
          </div>
          <div id="clue-echarts"></div>
        </div>
        <div v-if="tableFlag" class="tableBox" v-loading="loading">
          <div class="eltableBox">
            <ul class="ulBox">
              <li class="ultitle">线索分类</li>
              <li
                v-for="(item, index) in tabList"
                :key="index"
                @click="changeTab(item.label)"
                class="clueList"
                :style="
                  tabActive == item.label
                    ? 'background: #EAEFF6;color:#2677FF;font-weight: 500;'
                    : ''
                "
              >
                <span class="fangkuai" v-if="item.label == 0"></span>
                <span class="fangkuai fangkuai1" v-else-if="item.label == 1"></span>
                <span class="fangkuai fangkuai2" v-else-if="item.label == 2"></span>
                <span class="fangkuai fangkuai4" v-else-if="item.label == 6"></span>
                <span class="fangkuai fangkuaiKeyword" v-else-if="item.label == 4"></span>
                <span class="fangkuai fangkuai3" v-else></span>
                {{ getType(item.name) }}
              </li>
            </ul>
            <div class="myTable" v-loading="loading">
              <div class="tableLabel filterTab">
                <div> </div>
                <div>
                  <el-button class="normalBtnRe" type="primary" @click="goBack">返回</el-button>
                  <el-button
                    class="normalBtnRe"
                    type="primary"
                    id="ip_all"
                    @click="exportData"
                    :loading="exportLoading"
                    >导出</el-button
                  >
                </div>
              </div>
              <div class="myTableContent" ref="myTableContent">
                <clueTable
                  ref="clueTable"
                  @scrollChangeTab="scrollChangeTab"
                  :handleIsShow="false"
                  @checkedArr="checkedArrFun"
                  :tableData="tableData"
                  :tableHeader="tableHeader"
                ></clueTable>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="third-module">
      <div class="header-top">
        <span class="title">企业线索库</span>
      </div>
      <div class="content">
        <div class="left">
          <div class="scenario">
            <div class="title">场景</div>
            <scenario></scenario>
          </div>
        </div>
        <div class="right">
          <div class="process">
            <div class="right-header">
              <img src="src/assets/images/company.png" alt="" />
            </div>
            <div class="process-box">
              <div style="display: flex" v-for="(item, index) in cluesSourceType" :key="item.name">
                <img
                  class="process-arrow"
                  src="@/assets/images/clueOverview/process-arrow.png"
                  alt=""
                  v-if="index != 0"
                />
                <div :class="{ active: selectedCluesType && selectedCluesType.name == item.name }">
                  <div
                    class="tag-container top"
                    v-if="selectedCluesType && selectedCluesType.name == item.name"
                  >
                    <div class="tag-box flex-between more">
                      <div class="tag"
                        ><img
                          class="assets-logo"
                          src="@/assets/images/clueOverview/icp-assets.png"
                          alt=""
                        />
                        域名 ({{ cluesTypeCountData.unsure_ip_num }})</div
                      >
                      <div class="tag">
                        <img
                          class="assets-logo"
                          src="@/assets/images/clueOverview/icp-assets.png"
                          alt=""
                        />
                        ICP ({{ cluesTypeCountData.threten_ip_num }})</div
                      >
                    </div>
                  </div>
                  <div
                    class="radius-box"
                    :class="{ first: index == 0 }"
                    @click="handleSelectedType(item)"
                  >
                    <div class="radius">
                      {{ item.label }}
                      <span>({{ cluesTypeCountData.origin_clue_num }})</span>
                    </div>
                  </div>
                  <div
                    class="tag-container bottom"
                    v-if="selectedCluesType && selectedCluesType.name == item.name"
                  >
                    <div class="tag-box flex-between more">
                      <div class="tag">
                        <img
                          class="assets-logo"
                          src="@/assets/images/clueOverview/icon-assets.png"
                          alt=""
                        />
                        ICON ({{ cluesTypeCountData.unsure_ip_num }})</div
                      >
                      <div class="tag"
                        ><img
                          class="assets-logo"
                          src="@/assets/images/clueOverview/cert-assets.png"
                          alt=""
                        />
                        证书 ({{ cluesTypeCountData.threten_ip_num }})</div
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="count">
            <div class="count-top">
              <div><div class="fangkuaiblue"></div>线索总数</div>
              <span class="num">2603</span><span class="type"> 月环比 </span
              ><span class="percent">12%</span>
            </div>
            <div class="count-bottom descriptions">
              <div class="item" v-for="item in companyClueType" :key="item.name">
                <div class="label">
                  <img
                    :src="require(`@/assets/images/clueOverview/clue-type-${item.imgName}.png`)"
                    alt=""
                  />{{ item.label }}</div
                >
                <div>12</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import scenario from './scenario.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import clueTable from '../unit_surveying/taskSecondTable.vue'
import {
  publicClueList,
  clueCompanyList,
  clueCompanyCount,
  exportPublicClueList
} from '@/api/apiConfig/clue.js'

export default {
  name: 'clueOverview',
  components: { scenario, clueTable },
  data() {
    return {
      clueAllCountNum: {},
      firstLoading: false,
      currentPage: 1,
      pageSize: 10,
      tableHeader: [
        {
          label: '线索名称',
          name: 'content',
          fixed: 'left'
        },
        {
          label: '创建时间',
          name: 'created_at'
        },
        {
          label: '更新时间',
          name: 'updated_at'
        }
      ],
      checkedAlls: false,
      isIndeterminate: false,
      checkedArr0: [],
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      selectnum: 0, //选中的数量
      exportLoading: false,
      tableFlag: false,
      domain_num: 0,
      ip_num: 0,
      icp_num: 0,
      cert_num: 0,
      icon_num: 0,
      keyword_num: 0,
      loading: false,
      tabActive: '6',
      tableData: [],
      tabList: [
        {
          label: '6',
          name: 'IP段'
        },
        {
          label: '0',
          name: '域名'
        },
        {
          label: '2',
          name: 'ICP'
        },
        {
          label: '1',
          name: '证书'
        },
        {
          label: '3',
          name: 'ICON'
        },
        {
          label: '4',
          name: '关键词'
        }
      ],
      clueAllCount: {},
      datePickerSelectedType: {},
      datePickerTypeList: [
        { label: '近一周', name: '周变更', value: 1 },
        { label: '近一个月', name: '月变更', value: 2 },
        { label: '近一季度', name: '季变更', value: 3 }
      ],
      datePickerTypeText: '',
      clueCount: {},
      selectedCluesType: null,
      cluesSourceType: [
        { name: 'init', label: '初始种子资产' },
        { name: 'expend', label: '扩展种子资产' },
        { name: 'credible', label: '可信种子资产' }
      ],
      cluesTypeCountData: {},
      legendSelectItem: null,
      myCharts: null,
      restaurants: [],
      inputSearch: '',
      clueType: [
        { label: '域名库', name: 'domain' },
        { label: '证书库', name: 'cert' },
        { label: 'ICON库', name: 'icon' },
        { label: 'ICP库', name: 'icp' }
        // { label: 'ORG库', name: 'org' }
      ],
      companyClueType: [
        { label: 'IP段', name: 'ip', imgName: 'one' },
        { label: '域名', name: 'domain', imgName: 'two' },
        { label: '子域名', name: 'subdomain', imgName: 'three' },
        { label: 'ICP', name: 'icp', imgName: 'one' },
        { label: '证书', name: 'cert', imgName: 'two' },
        { label: 'ICON', name: 'icon', imgName: 'three' }
      ],
      descriptionItem: [
        {
          title: '公共线索库',
          description:
            '支持联动FOFA查询全网公共域名库、证书库、ICP库、ICON库的总数，并支持查看变化趋势。',
          // '支持联动FOFA查询全网公共域名库、证书库、ICP库、ICON库、org库的总数，并支持查看变化趋势。',
          callback: this.jumpPosition
        },
        {
          title: '企业线索库',
          description:
            '针对授权单位所有的线索信息的管理，支持对线索进行确认/忽略/导出/删除/供应链标记，支持利用已确认的线索进行扩展以及云端资产的推荐。',
          callback: () => {
            this.$router.push({
              path: '/companyBank'
            })
            sessionStorage.setItem('menuId', '1-6-2')
            this.changeMenuId('1-6-2')
          }
        },
        {
          title: '供应链线索库',
          description: '针对通用供应链及自有供应链线索进行管理。',
          callback: () => {
            this.$router.push({
              path: '/supplyChainBank'
            })
            sessionStorage.setItem('menuId', '1-6-3')
            this.changeMenuId('1-6-3')
          }
        }
      ],
      option: {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          show: false,
          x: 'left',
          y: 'center',
          orient: 'vertical'
        },
        grid: {
          left: '3%',
          right: '0',
          bottom: '0',
          containLabel: true
        },
        xAxis: {
          type: 'category'
          // boundaryGap: false,
        },
        yAxis: {
          type: 'value'
          // min: function (value) {
          //   return Math.floor(value.min) - value.max * 0.1
          // },
          // max: function (value) {
          //   return Math.floor(value.max)
          // }
        },
        series: []
      },
      user: {
        role: ''
      }
    }
  },
  watch: {
    getterCompanyChange(val) {
      console.log('---切换企业---')
      // this.myCharts.dispose()
    },
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.$nextTick(() => {
          this.goBack()
          this.getEchartData()
        })
        this.getSearchData()

        this.selectedCluesType = this.cluesSourceType[0]
        this.datePickerSelectedType = this.datePickerTypeList[0]
        this.getData()
      }
    },
    inputSearch(val) {
      if (val == '') {
        this.tableFlag = false
      }
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange']),
    ...mapState(['currentCompany'])
  },
  created() {
    if (sessionStorage.getItem('userMessage')) {
      let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (userInfo) {
        this.user = userInfo.user
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initEcharts()
    })
    if (this.user.role == 2 && !this.currentCompany) return
    this.getEchartData()
    this.getSearchData()

    this.selectedCluesType = this.cluesSourceType[0]
    this.datePickerSelectedType = this.datePickerTypeList[0]
    this.getData()
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    formatNum(numVal) {
      let type = Number(numVal) < 0 ? '-' : ''
      let val = Math.abs(numVal)
      let num = 10000
      var sizesValue = ''
      /**
       * 判断取哪个单位
       */
      if (val < 1000) {
        // 如果小于1000则直接返回
        sizesValue = ''
        return val
      } else if (val > 1000 && val < 9999) {
        sizesValue = '千+'
      } else if (val > 10000 && val < 99999999) {
        sizesValue = '万+'
      } else if (val > *********) {
        sizesValue = '亿+'
      }
      /**
       * 大于一万则运行下方计算
       */
      let i = Math.floor(Math.log(val) / Math.log(num))
      /**
       * toFixed(0)看你们后面想要取值多少，我是不取所以填了0，一般都是取2个值
       */
      var sizes = Math.floor(val / Math.pow(num, i))
      sizes = sizes + sizesValue
      // resolve(sizes);// 输出
      return type + sizes
    },
    // 跳转公共线索库页面位置 并聚焦搜索框
    jumpPosition() {
      this.tableFlag = false
      let dataList = document.getElementsByClassName('search-box')[0].clientHeight

      // 滚动距离
      document.getElementsByClassName('container')[0].scrollTop = dataList - 100
      setTimeout(() => {
        document.getElementById('companySearchInput').focus()
      }, 10)
    },
    goBack() {
      this.tableFlag = false
      this.tabActive = '6'
      this.inputSearch = ''
      this.checkedAlls = false
      this.tableData = []
      this.domain_num = 0
      this.ip_num = 0
      this.icp_num = 0
      this.cert_num = 0
      this.icon_num = 0
      this.keyword_num = 0
      // this.$nextTick(() => {
      //   this.initEcharts()
      // })
    },
    checkedArrFun(arr) {
      this.checkedArr0 = arr
        .filter((item) => {
          return item.type == 0
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr1 = arr
        .filter((item) => {
          return item.type == 1
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr2 = arr
        .filter((item) => {
          return item.type == 2
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr3 = arr
        .filter((item) => {
          return item.type == 3
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr4 = arr
        .filter((item) => {
          return item.type == 4
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr5 = arr
        .filter((item) => {
          return item.type == 5
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr6 = arr
        .filter((item) => {
          return item.type == 6
        })
        .map((item) => {
          return item.id
        })
    },
    async exportData() {
      if (!this.checkedAlls) {
        if (
          this.checkedArr0.length == 0 &&
          this.checkedArr1.length == 0 &&
          this.checkedArr2.length == 0 &&
          this.checkedArr3.length == 0 &&
          this.checkedArr4.length == 0 &&
          this.checkedArr5.length == 0 &&
          this.checkedArr6.length == 0
        ) {
          this.$message.error('请选择要导出的数据！')
          return
        }
      }
      let obj = {
        ids: this.checkedAlls
          ? []
          : [
              ...this.checkedArr0,
              ...this.checkedArr1,
              ...this.checkedArr2,
              ...this.checkedArr3,
              ...this.checkedArr4,
              ...this.checkedArr5,
              ...this.checkedArr6
            ],
        operate_company_id: this.currentCompany,
        company_name: this.checkedAlls ? this.inputSearch : ''
      }
      this.exportLoading = true
      let res = await exportPublicClueList(obj).catch(() => {
        this.exportLoading = false
      })
      if (res.code == 0) {
        this.checkedAlls = false
        this.isIndeterminate = false
        this.selectnum = 0
        this.checkedArr0 = []
        this.checkedArr1 = []
        this.checkedArr2 = []
        this.checkedArr3 = []
        this.checkedArr4 = []
        this.checkedArr5 = []
        this.checkedArr6 = []
        this.download(this.showSrcIp + res.data.url)
      }
      this.exportLoading = false
    },
    getType(val) {
      //tabActive
      if (val == '域名') {
        return `域名(${this.domain_num})`
      } else if (val == 'ICP') {
        return `ICP(${this.icp_num})`
      } else if (val == '证书') {
        return `证书(${this.cert_num})`
      } else if (val == 'ICON') {
        return `ICON(${this.icon_num})`
      } else if (val == 'IP段') {
        return `IP段(${this.ip_num})`
      } else if (val == '关键词') {
        return `关键词(${this.keyword_num})`
      }
    },
    // 滚动改变tab线索分类选中
    scrollChangeTab(type) {
      this.tabActive = String(type)
    },
    changeTab(val) {
      var arr = this.tableData.map((item) => {
        return item.type
      })
      var num = arr.indexOf(Number(val))
      if (num != -1) {
        // 获取行高
        let dataList = document.getElementsByClassName('borderWrap')[0].clientHeight
        // 滚动距离
        document.getElementsByClassName('main_body_unit')[0].scrollTop = dataList * num
        setTimeout(() => {
          this.tabActive = val
        }, 100)
      }
    },
    async Inquire() {
      if (this.inputSearch == '') {
        this.$message.error('请输入企业名称查询')
        return
      }
      this.tableFlag = true

      let formInline = {}
      formInline.company_name = this.inputSearch
      formInline.page = this.currentPage
      formInline.per_page = this.pageSize
      formInline.operate_company_id = this.currentCompany
      this.loading = true
      this.tableData = []
      let tableData_ip = []
      let tableData_domain = []
      let tableData_icp = []
      let tableData_cert = []
      let tableData_icon = []
      let tableData_keyword = []
      this.checkedAlls = false
      this.domain_num = 0
      this.ip_num = 0
      this.icp_num = 0
      this.cert_num = 0
      this.icon_num = 0
      this.keyword_num = 0

      let res = await publicClueList(formInline).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        if (res.data) {
          // 0域名，1证书，2ICP，3ICON，6IP段，4关键词
          res.data.forEach((item) => {
            if (item.type == 6) {
              tableData_ip.push(item)
              this.ip_num = tableData_ip.length
            } else if (item.type == 0) {
              tableData_domain.push(item)
              this.domain_num = tableData_domain.length
            } else if (item.type == 2) {
              tableData_icp.push(item)
              this.icp_num = tableData_icp.length
            } else if (item.type == 1) {
              tableData_cert.push(item)
              this.cert_num = tableData_cert.length
            } else if (item.type == 3) {
              tableData_icon.push(item)
              this.icon_num = tableData_icon.length
            } else if (item.type == 4) {
              tableData_keyword.push(item)
              this.keyword_num = tableData_keyword.length
            }
          })
          // 默认选中有数据的tab
          if (this.ip_num > 0) {
            this.tabActive = '6'
          } else if (this.domain_num > 0) {
            this.tabActive = '0'
          } else if (this.icp_num > 0) {
            this.tabActive = '2'
          } else if (this.cert_num > 0) {
            this.tabActive = '1'
          } else if (this.icon_num > 0) {
            this.tabActive = '3'
          } else if (this.keyword_num > 0) {
            this.tabActive = '4'
          }
        }
        // 用于滚动监听，按左侧分类tab顺序排序
        this.tableData = [
          ...tableData_ip,
          ...tableData_domain,
          ...tableData_icp,
          ...tableData_cert,
          ...tableData_icon,
          ...tableData_keyword
        ]
      }
    },
    getData() {
      this.firstLoading = true
      clueCompanyCount({
        query_mode: this.datePickerSelectedType.value,
        operate_company_id: this.currentCompany
      }).then((res) => {
        if (res.code == 0) {
          this.clueCount = res.data || {}
        }
        this.firstLoading = false
      }).catch(() => {
        this.firstLoading = false
      })
    },
    handleClickDatePicker(item) {
      this.datePickerSelectedType = item
      this.getData()
    },
    handleSelectedType(item) {
      this.selectedCluesType = item
    },
    handleSelectLegend(item) {
      this.myCharts.dispatchAction({
        type: 'legendToggleSelect',
        name: item.name
      })
      this.legendSelectItem = item
    },
    async getEchartData() {
      this.legendSelectItem = this.clueType[0]
      let res = await clueCompanyCount({
        query_mode: '',
        operate_company_id: this.currentCompany
      })

      if (res.code == 0) {
        this.clueAllCount = res.data || {}

        for (let index = 0; index < this.clueType.length; index++) {
          const element = this.clueType[index]
          let value = res.data[element.name]
          this.clueAllCountNum[element.name] = value[value.length - 1].count
        }

        this.option.xAxis.data = res.data.cert.map((item) => item.time)
        this.option.legend.data = []
        this.option.series = []
        this.clueType.forEach((item) => {
          this.option.legend.data.push(item.name)
          let obj = {
            name: item.name,
            type: 'line',
            stack: 'Total',
            smooth: true,
            data: res.data[item.name].map((item) => item.count)
          }
          this.option.series.push(obj)
        })
        this.myCharts.setOption(this.option)

        this.myCharts.dispatchAction({
          type: 'legendToggleSelect',
          name: this.legendSelectItem.name
        })
      }
    },
    async initEcharts() {
      this.legendSelectItem = this.clueType[0]
      this.myCharts =
        document.getElementById('clue-echarts') &&
        this.$echarts.init(document.getElementById('clue-echarts'))

      window.addEventListener('resize', () => {
        this.myCharts.resize()
      })
      let echarts = this.myCharts
      this.myCharts.on('legendselectchanged', function (params) {
        let selected = params.selected
        let name = params.name
        let lengthArr = []
        let trueNum = 0
        if (selected != undefined) {
          for (let x in selected) {
            //判断是否有选中

            if (selected[x]) {
              trueNum++
            }
            lengthArr.push({
              name: x
            })
            if (x == name) {
              echarts.dispatchAction({
                type: 'legendSelect',
                name: x
              })
            } else {
              echarts.dispatchAction({
                type: 'legendUnSelect',
                name: x
              })
            }
          }
          if (trueNum == 0) {
            // echarts.dispatchAction({
            //   type: 'legendSelect',
            //   // 图例名称
            //   batch: lengthArr
            // })
          }
        }
      })
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    async getSearchData() {
      let res = await clueCompanyList({ operate_company_id: this.currentCompany })
      let data = []
      if (res.data && res.data.length > 0) {
        res.data.map((v) => {
          data.push({ value: v })
        })
      }
      this.restaurants = data
    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    selectChange(value) {
      // this.Inquire()
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: calc(100% - 32px);
  margin: 0 16px 16px 16px;
  padding-bottom: 16px;
  .headerTitleTmp {
    width: 100%;
    position: absolute;
    top: -39px;
    left: 0;
    font-weight: 700;
    font-size: 16px;
    color: #37393c;
  }
  .header-module {
    display: flex;
    margin-bottom: 20px;
    .header-item {
      box-sizing: border-box;
      flex: 1;
      display: flex;

      height: 96px;
      padding: 16px 12px;
      margin-right: 20px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      background: linear-gradient(180deg, #e3eeff 0%, #ffffff 100%);
      &:last-child {
        margin-right: 0;
      }
      .logo {
        width: 44px;
        height: 44px;
        margin-right: 8px;
        line-height: 36px;
        text-align: center;
        background: rgba(38, 119, 255, 0.08);
        img {
          width: 26px;
        }
      }
      .content {
        flex: 1;
        line-height: 20px;
        font-size: 14px;

        .title {
          font-weight: 600;
          margin-bottom: 4px;
        }
        .description {
          color: #62666c;
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          overflow: hidden;
        }
      }
    }
  }
  .first-module {
    display: flex;
    flex-direction: row;
    height: 292px;
    margin-bottom: 20px;

    .left {
      flex: 1;
      margin-right: 20px;
      background: url('../../assets/images/clueOverview/process-bg.png') no-repeat;
      background-size: 100% 100%;
      text-align: center;
      padding-top: 38px;
      border: 2px solid #ffffff;
      box-shadow:
        0px 2px 6px 0px rgba(0, 0, 0, 0.08),
        inset 0px 0px 60px 0px rgba(0, 67, 179, 0.2);

      img {
        height: 255px;
      }
    }
    .right {
      flex: 1;
      background-color: #fff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      .date-picker {
        border-radius: 4px;
        padding: 4px 8px;
        background: #f5f8fc;
        .date {
          height: 20px;
          padding: 2px 12px;
          border-radius: 4px;
          line-height: 22px;
          font-size: 12px;
          cursor: pointer;
          &.active {
            border-radius: 4px;
            font-weight: 500;
            color: #2677ff;
            background: #ffffff;
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
          }
        }
      }
      .content {
        display: flex;
        flex-direction: row;
        box-sizing: border-box;
        height: calc(100% - 47px);
        padding: 20px;
        .item-first {
          font-size: 14px;
          color: #62666c;
        }
        .item-second {
          height: 30px;
          line-height: 30px;
          font-size: 22px;
          font-weight: 600;
          color: #37393c;
        }
        .item-third {
          font-size: 12px;
          color: #acb4c0;
        }
        .total {
          position: relative;
          box-sizing: border-box;
          // width: 172px;
          flex: 1;
          min-width: 136px;
          height: 100%;
          padding: 20px 16px;
          border-radius: 8px;
          background: linear-gradient(
            220deg,
            rgba(38, 119, 255, 0.12) 22%,
            rgba(255, 255, 255, 0) 100%
          );

          .statistics-logo {
            width: 44px;
            height: 44px;
            position: absolute;
            top: 20px;
            right: 16px;
            border-radius: 4px;
            text-align: center;
            line-height: 34px;
            background: rgba(38, 119, 255, 0.08);
            img {
              width: 24px;
            }
          }
          .total-bg {
            position: absolute;
            bottom: -8px;
            right: -8px;
          }
        }
        .total-bottom {
          flex: 2;
          margin-left: 32px;
          padding-top: 20px;
          .item {
            height: 78px;
            margin: 5px 5px 0 0;
          }
        }
      }
    }
  }
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: 46px;
  padding-left: 16px;
  padding-right: 16px;
  border-bottom: 1px solid #e9ebef;
  font-size: 16px;
  font-weight: 600;
  color: #37393c;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto 0;
    width: 4px;
    height: 16px;
    background-color: #2677ff;
    border-radius: 0 2px 2px 0;
  }
  .title {
    // min-width: 50px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.percent {
  &.rise {
    color: #10d595;
  }
  &.decline {
    color: red;
  }
  &.grey {
    color: #a8acb3;
  }
}
.el-icon-caret-top {
  color: #10d595;
}
.el-icon-caret-bottom {
  color: red;
}
.second-module {
  margin-bottom: 20px;
  box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
  .search-box {
    position: relative;
    display: flex;
    justify-content: center;
    height: 171px;
    padding-top: 40px;
    background: url('../../assets/images/clueOverview/search-bg.png') no-repeat;
    background-size: 100% 100%;
    i,
    p {
      height: 46px;
      line-height: 46px;
    }
    i {
      margin-left: 12px;
      font-size: 20px;
      color: #2677ff;
    }
    p {
      font-size: 14px;
      color: #62666c;
    }
    .search-input {
      display: flex;
      align-items: center;
      width: 65%;
      height: 46px;
      border: solid 2px transparent;
      border-radius: 8px;
      background-image: linear-gradient(#fff, #fff),
        linear-gradient(270deg, rgba(2, 114, 255, 1), rgba(25, 170, 254, 1));
      background-origin: border-box;
      background-clip: content-box, border-box;
    }
    .inline-input {
      width: 100%;
    }
    /deep/.el-input {
      --el-border-color-hover: transparent;
      .el-input__wrapper {
        box-shadow: none !important;
      }
    }
    /deep/.el-input__inner {
      border: 0;
    }

    .search-logo {
      width: 30px;
      height: 30px;
      margin-right: 10px;
      border-radius: 4px;
      line-height: 26px;
      text-align: center;
      background: linear-gradient(135deg, #26b3ff 0%, #2677ff 100%);
      img {
        height: 20px;
      }
    }
  }
  .search-result {
    position: relative;
    z-index: 2;
    height: 473px;
    margin-top: -46px;
    border-radius: 8px 8px 4px 4px;
    box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
    background-color: #fff;
    .content {
      display: flex;
      flex-direction: row;
      height: calc(100% - 46px);
      padding: 24px;
      padding-left: 0;
      box-sizing: border-box;
      .legend {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding-left: 24px;
        cursor: pointer;
        .item {
          flex: 1;
          padding: 10px 16px 0;
          margin-bottom: 12px;
          border-radius: 8px;
          background: linear-gradient(
            256deg,
            rgba(38, 119, 255, 0.2) 0%,
            rgba(38, 119, 255, 0.02) 100%
          );
          &:last-child {
            margin-bottom: 0;
          }

          .title {
            font-size: 14px;
            color: #62666c;
          }

          .num {
            font-size: 20px;
            font-weight: 600;
            color: #37393c;
          }
          .type {
            margin-left: 8px;
            margin-right: 8px;
            font-size: 12px;
            color: #acb4c0;
          }

          &.active {
            color: #2677ff !important;
            border: 1px solid #2677ff;
            .title {
              color: #2677ff;
            }
            .num {
              color: #2677ff;
            }
          }
        }
      }

      #clue-echarts {
        flex: 4;
      }
    }
  }
}
.third-module {
  height: 399px;
  background-color: #fff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  .content {
    display: flex;
    height: calc(100% - 46px);
    flex-direction: row;
    .left {
      flex: 1;
      width: 0;
      padding-left: 24px;
      .scenario {
        width: calc(100% + 24px);
        height: 100%;
        margin-left: -24px;
        background: #f5f8fc;
        border-right: 1px solid #e9ebef;
        .title {
          height: 44px;
          padding-left: 16px;
          line-height: 44px;
          background-color: #fff;
        }
      }
    }
    .right {
      flex: 4;
      padding: 20px 24px;
      display: flex;

      .process {
        flex: 2;
        min-width: 528px;
        padding: 16px 20px 12px;
        background: linear-gradient(90deg, #f4f7fc 0%, #e7f0ff 100%);

        .process-box {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 172px;
          margin: 52px 0;
          border-radius: 8px;

          .process-arrow {
            height: 28px;
            margin: auto 12px;
          }
          .active {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 211px;
            height: 172px;
            background-image: url('../../assets/images/clueOverview/step-bg.png');
            background-position: center;
            background-repeat: no-repeat;
            background-size: 115% 125%;
            margin-left: -47px;
            margin-right: -47px;
            .radius {
              top: 28px;
              left: 47px;
            }
            .tag-container {
              position: absolute;
              .text {
                font-size: 14px;
                color: #2677ff;
                text-align: center;
              }
              &.top .tag-box:last-child {
                margin-bottom: 0;
              }
              .tag-box {
                &:first-child {
                  margin-bottom: 12px;
                }
                &.more .tag:first-child {
                  margin-right: 12px;
                }
              }
              .tag {
                display: flex;
                align-items: center;
                height: 32px;
                padding: 0 10px;
                color: #62666c;
                font-size: 14px;
                line-height: 32px;
                border-radius: 16px;
                border: 1px solid #cfdaeb;
                background: linear-gradient(270deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);

                .assets-logo {
                  width: 16px;
                  margin-right: 4px;
                }
              }
              &.top {
                top: 16px;
                transform: translateY(-100%);
              }
              &.bottom {
                bottom: 16px;
                transform: translateY(100%);
              }
            }
          }
          .radius-box {
            min-width: 116px;
            height: 116px;
            border-radius: 50%;
            background-color: rgba(38, 119, 255, 0.16);

            &.active {
              width: 211px;
              height: 172px;
              background-image: url('../../assets/images/clueOverview/step-bg.png');
              background-position: center;
              background-repeat: no-repeat;
              background-size: 120%;
              margin-left: -47px;
              .radius {
                top: 28px;
                left: 47px;
              }
            }
            .radius {
              position: relative;
              top: 8px;
              left: 8px;

              width: 100px;
              height: 100px;
              border-radius: 50%;
              border: 1px solid #2677ff;
              background: linear-gradient(270deg, #dde9fe 0%, #ffffff 100%);
              color: #2677ff;
              font-size: 14px;
              text-align: center;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
            }
            &.first {
              .radius {
                font-weight: 600;
                color: #ffffff;
                border: 0;
                background: linear-gradient(90deg, #26b3ff 0%, #2677ff 100%);
              }
            }
          }
        }
      }
      .count {
        flex: 1;
        margin-left: 16px;
        background: linear-gradient(
          180deg,
          rgba(38, 119, 255, 0.12) 0%,
          rgba(255, 255, 255, 0) 100%
        );
        .count-top {
          box-sizing: border-box;
          height: 40%;
          padding: 20px;
          border-bottom: 1px solid #e9ebef;
          .num {
            font-size: 20px;
            font-weight: 600;
            color: #37393c;
          }
          .type {
            margin-left: 8px;
            margin-right: 8px;
            font-size: 12px;
            color: #acb4c0;
          }
        }
        .count-bottom {
          box-sizing: border-box;

          padding: 29px 24px;
          .item {
            box-sizing: border-box;
            height: 49px;
            // margin: 0 5px 29px 0;
            margin: 29px 5px 0 0;
            .label {
              height: 22px;
              line-height: 22px;
              margin-bottom: 8px;
              img {
                width: 22px;
                height: 22px;
                margin-right: 4px;
              }
            }
          }
        }
      }
    }
  }
}
.flex-between {
  display: flex;
  justify-content: space-between;
}
.descriptions {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .item {
    flex: 0 0 calc((100% - 10px) / 3);
    /* 间隙为5px */
    // margin: 5px 5px 0 0;
  }
  .item:nth-child(3n) {
    /* 去除第3n个的margin-right */
    margin-right: 0 !important;
  }
  .item:nth-child(-n + 3) {
    margin-top: 0 !important;
  }
}
.fangkuaiblue {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: rgba(38, 119, 255, 0.28);
  border-radius: 1px;
  transform: rotate(45deg);
  margin-left: 4px;
  margin-right: 5px;
  vertical-align: middle;
  background-color: #2677ff;
}

.tableBox {
  width: 100%;
  height: calc(100% - 32px);
  // height: 80%;
  padding: 16px;
  box-sizing: border-box;
  background: #ffff;
  .eltableBox {
    display: flex;
    padding: 0px 20px 0px 0px;
    height: 100% !important;
    justify-content: space-between;
    .ulBox {
      width: 160px;
      height: 100%;
      background: rgba(255, 255, 255, 0.36);
      border-right: 1px solid #e9ebef;
      box-sizing: border-box;
      padding-top: 5px;
      li {
        color: #62666c;
        cursor: pointer;
        height: 44px;
        background: rgba(234, 239, 246, 0);
        display: flex;
        align-items: center;
      }
      .ultitle {
        font-weight: 500;
        color: #37393c;
        padding-left: 16px;
      }
    }
    .myTable {
      width: calc(100% - 170px);
      padding: 0px !important;
      height: 100%;
      overflow: auto;
      .tableLabel {
        padding: 0px 0px 0px 4px !important;
        margin-bottom: 10px !important;
        margin-top: 10px !important;
      }
      .filterTab {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        & > div {
          display: flex;
          align-items: center;
          .normalBtnRe {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
          }
          .el-input {
            width: 240px;
            margin-right: 12px;
          }
          & > span {
            font-weight: 400;
            color: #2677ff;
            line-height: 20px;
            margin-left: 4px;
            cursor: pointer;
          }
        }
      }
      .myTableContent {
        height: calc(100% - 52px);
      }
    }
  }
  .fangkuai {
    width: 6px;
    height: 6px;
    background: #2677ff;
    box-shadow: 0px 0px 4px 0px rgba(38, 119, 255, 0.74);
    border-radius: 1px;
    margin: 0px 8px 0px 16px;
  }
  .fangkuai4 {
    background: #5346ff;
  }
  .fangkuai2 {
    background: #05d4a7;
  }
  .fangkuai1 {
    background: #13b7ff;
  }
  .fangkuai3 {
    background: #ec8f3c;
  }
  .fangkuaiKeyword {
    background: #ec5f5c;
  }
}
</style>
