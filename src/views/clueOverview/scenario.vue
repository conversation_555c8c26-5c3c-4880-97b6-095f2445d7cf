<template>
  <div class="scenario-component">
    <div v-if="groupArr.length == 0" class="emptyClass">
      <div>
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-kong"></use>
        </svg>
        <p>暂无场景</p>
      </div>
    </div>
    <el-collapse
      v-else
      v-model="currentGroupIds"
      accordion
      style="overflow: auto"
      v-loading="groupLoading"
      @change="collapseChange"
    >
      <el-collapse-item :name="String(item.id)" v-for="item in groupArr" :key="item.id">
        <div slot="title" class="nameClass">
          <span>{{ `${item.name}(${item.num})` }}</span>
        </div>
        <div v-for="(val, ind) in item.tab_num" :key="ind" class="collapseType">
          <div
            :style="item.isHighlighted && val.value == tabActiveNameStatus ? 'color:#2677FF' : ''"
          >
            <span
              :class="
                item.isHighlighted && val.value == tabActiveNameStatus
                  ? 'fangkuai fangkuaiblue'
                  : 'fangkuai'
              "
            ></span>
            {{ val.label }}
          </div>
          <div
            class="collapseCount"
            :style="item.isHighlighted && val.value == tabActiveNameStatus ? 'color:#2677FF' : ''"
            >({{ val.count }})</div
          >
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { cluesGroupList } from '@/api/apiConfig/clue.js'

export default {
  name: 'scenario',
  data() {
    return {
      tabActiveNameStatus: '0',
      groupCurrentPage: 1,
      groupArr: [],
      // currentCompany: '',
      currentGroupIds: '',
      groupLoading: false
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  mounted() {
    this.getGroupList()
  },
  methods: {
    collapseChange(val) {},
    async getGroupList() {
      let obj = {
        page: this.groupCurrentPage,
        per_page: 10,
        no_page: '1',
        operate_company_id: this.currentCompany
      }
      this.groupLoading = true
      let res = await cluesGroupList(obj).catch(() => {
        this.groupLoading = false
        this.groupArr = []
      })
      this.groupLoading = false
      res.data.forEach((item) => {
        item.isChecked = false
        item['num'] = item.collect ? item.collect : 0 // 所有线索数量
      })
      this.groupArr = res.data
    }
  }
}
</script>

<style lang="less" scoped>
.scenario-component {
  // width: 17%;
  height: calc(100% - 44px);
  // margin-left: 24px;
}
.nameClass {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  font-size: 14px;
}
.nameClass > span {
  display: inline-block;
  max-width: 85%;
  height: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/deep/.el-collapse {
  height: 100%;
  width: 100%;
  &.is-active {
    background: #eff2f6;
    font-weight: 500;
    color: #37393c;
  }
}
/deep/.el-collapse-item__header {
  position: relative;
  background-color: transparent;
  & > i {
    position: absolute;
    right: 0px;
    bottom: 18px;
  }
}
// /deep/.el-collapse-item__header > i {
//   position: absolute;
//   right: 0px;
//   bottom: 18px;
// }
/deep/.el-collapse-item__header,
/deep/.el-collapse-item__wrap {
  border: 0;
  color: #62666c;
  background: transparent;
}
/deep/.el-collapse-item {
  padding: 0px 8px;
}

/deep/.el-collapse-item__content {
  background: transparent;
  padding-bottom: 0;
}
.collapseType {
  color: #62666c;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0px;
  font-size: 14px;
  cursor: pointer;
}
.fangkuai {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: rgba(38, 119, 255, 0.28);
  border-radius: 1px;
  transform: rotate(45deg);
  margin-left: 4px;
  margin-right: 5px;
  vertical-align: middle;
}
.fangkuaiblue {
  background-color: #2677ff;
}
</style>
