<template>
  <div class="container">
    <div class="headerTitle">线索总库</div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="keyword_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <!-- <el-checkbox class="checkboxAll" v-model="checkedAll" @change="checkAllChange" id="user_all">选择全部</el-checkbox> -->
          <el-button type="primary" class="normalBtnRe" @click="batchEditFn('more')"
            >企业名称编辑</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="blackClue"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            :reserve-selection="true"
            :selectable="handleSelectable"
          ></el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            min-width="120"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'type'">
                {{ getClueType(scope.row[item.name]) }}
              </span>
              <span class="icon-image" v-else-if="item.name == 'content'">
                <el-image
                  v-if="scope.row.type == 3 && scope.row.content"
                  :src="
                    scope.row.content.includes('http')
                      ? scope.row.content
                      : showSrcIp + scope.row.content
                  "
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <span v-else>{{ scope.row.content ? scope.row.content : '-' }}</span>
                <span class="hash" v-if="scope.row.type == 3">{{ scope.row.hash }}</span>
              </span>
              <span v-else-if="item.name == 'confirmed' || item.name == 'cert_valid'">
                <span v-if="scope.row[item.name] == 1">是</span>
                <span v-else>否</span>
              </span>
              <span v-else-if="item.name == 'source'">
                <a
                  style="color: #2677ff"
                  v-if="scope.row[item.name] && scope.row[item.name].includes('http')"
                  :href="scope.row[item.name]"
                  target="_blank"
                  >{{ scope.row[item.name] }}</a
                >
                <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
              </span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="150">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="editOne(scope.row)" id="keyword_del"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="590px"
      v-if="dialogFormVisibleInsert"
    >
      <template slot="title"> 编辑 </template>
      <div class="dialog-body">
        <el-form
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="ruleForm"
          label-width="130px"
          class="demo-ruleForm"
        >
          <el-form-item label="线索内容" prop="content">
            <el-input
              v-if="ruleForm.type != 3"
              v-model="ruleForm.content"
              placeholder="请输入"
            ></el-input>
            <el-upload
              v-if="ruleForm.type == 3"
              class="upload-demo"
              drag
              :action="uploadSrcIp + '/assets/account/files'"
              :headers="uploadHeaders"
              accept=".png,.ico,.bmp,.jpg,.jpeg"
              :before-upload="beforeIpUpload"
              :on-success="uploadSuccess"
              :on-remove="uploadRemove"
              :on-error="uploadError"
              :limit="uploadMaxCount"
              :on-exceed="handleExceed"
              list-type="picture"
              :file-list="fileList"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将ICON拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip"
                >支持上传.png,.ico,.bmp,.jpg,.jpeg文件，且大小不超过3M</div
              >
            </el-upload>
          </el-form-item>
          <el-form-item label="平台" prop="platform">
            <el-input v-model="ruleForm.platform" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="来源" prop="source">
            <el-input v-model="ruleForm.source" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="企业名称" prop="company_name">
            <el-input v-model="ruleForm.company_name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="是否确认" prop="confirmed">
            <el-switch
              v-model="ruleForm.confirmed"
              active-text="是"
              inactive-text="否"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </el-form-item>
          <el-form-item label="来源证书是否有效" prop="cert_valid">
            <el-switch
              v-model="ruleForm.cert_valid"
              active-text="是"
              inactive-text="否"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="otherLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="130px">
          <el-form-item
            v-for="(item, index) in tableHeader.filter((item) => {
              return item.highfilter
            })"
            :key="index"
            :label="item.label"
          >
            <el-select
              v-if="item.name == 'type'"
              clearable
              v-model="formInline[item.name]"
              placeholder="请选择"
              @change="selectChange($event, item.name, typeArr, true, false)"
            >
              <el-option
                v-for="item in typeArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-select
              v-else-if="item.name == 'platform'"
              multiple
              collapse-tags
              clearable
              v-model="formInline[item.name]"
              placeholder="请选择"
              @change="selectChange($event, item.name, platformArr, false, true)"
            >
              <el-option
                v-for="item in platformArr"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
            <el-select
              v-else-if="item.name == 'confirmed' || item.name == 'cert_valid'"
              v-model="formInline[item.name]"
              placeholder="请选择"
              clearable
              @change="selectChange($event, item.name, comfirmArr, true, false)"
            >
              <el-option label="是" value="1"></el-option>
              <el-option label="否" value="0"></el-option>
            </el-select>
            <el-date-picker
              v-else-if="item.name == 'created_at' || item.name == 'updated_at'"
              v-model="formInline[item.name]"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
            <el-input v-else v-model="formInline[item.name]" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { editClueStoreList, clueStoreList, batchEditClueStore } from '@/api/apiConfig/clue.js'

export default {
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      highTabShow: [
        {
          label: '线索类型',
          name: 'type',
          type: 'select'
        },
        {
          label: '线索内容',
          name: 'content',
          type: 'input'
        },
        {
          label: '平台',
          name: 'platform',
          type: 'select'
        },
        {
          label: '来源',
          name: 'source',
          type: 'input'
        },
        {
          label: '企业名称',
          name: 'company_name',
          type: 'input'
        },
        {
          label: '是否确认',
          name: 'confirmed',
          type: 'select'
        },
        {
          label: '来源证书是否有效',
          name: 'cert_valid',
          type: 'select'
        },
        {
          label: '创建时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      platformArr: ['MIIT', 'FOFA', 'Crawler', 'INPUT'],
      typeArr: [
        {
          id: 1,
          name: '证书'
        },
        {
          id: 2,
          name: 'ICP'
        },
        {
          id: 3,
          name: 'ICON'
        },
        {
          id: 4,
          name: '关键词'
        },
        {
          id: 5,
          name: '域名'
        },
        {
          id: 6,
          name: 'IP'
        },
        {
          id: 7,
          name: '子域名'
        },
        {
          id: 8,
          name: '企业名称'
        }
      ],
      formInline: {
        type: '',
        content: '',
        platform: [],
        source: '',
        company_name: '',
        // confirmed: 0,
        // cert_valid: 0,
        page: 1,
        per_page: 10,
        keyword: '',
        operate_company_id: '',
        created_at_before: 0,
        created_at_after: 0,
        updated_at_before: 0,
        updated_at_after: 0
      },
      highCheckdialog: false,
      comfirmArr: [
        {
          name: '是',
          id: '1'
        },
        {
          name: '否',
          id: '0'
        }
      ],
      tableHeader: [
        {
          label: '线索类型',
          name: 'type',
          highfilter: 1
        },
        {
          label: '线索内容',
          name: 'content',
          highfilter: 1
        },
        {
          label: '平台',
          name: 'platform',
          highfilter: 1
        },
        {
          label: '来源',
          name: 'source',
          highfilter: 1
        },
        {
          label: '企业名称',
          name: 'company_name',
          highfilter: 1
        },
        {
          label: '是否确认',
          name: 'confirmed',
          highfilter: 1
        },
        {
          label: '来源证书是否有效',
          name: 'cert_valid',
          highfilter: 1
        },
        {
          label: '创建时间',
          name: 'created_at',
          highfilter: 1
        },
        {
          label: '更新时间',
          name: 'updated_at',
          highfilter: 1
        }
      ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      dialogFormVisibleInsert: false,
      rules: {
        content: [{ required: true, message: '线索内容不能为空', trigger: 'blur' }],
        platform: [{ required: true, message: '请输入', trigger: 'blur' }],
        // source: [
        //   { required: true, message: '请输入', trigger: 'blur' }
        // ],
        // company_name: [
        //   { required: true, message: '请输入', trigger: 'blur' }
        // ],
        expired_at: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      ruleForm: {
        type: '',
        content: '',
        platform: '',
        source: '',
        type: '',
        company_name: '',
        confirmed: 0,
        cert_valid: 0
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      user: {
        role: ''
      },
      checkedAll: false
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    batchEditFn(type, id) {
      // 批量以及单独修改企业名称
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要编辑的数据！')
        return
      }
      let clueData = null
      if (type == 'more' && !this.checkedAll) {
        clueData = this.checkedArr.map((item) => {
          return item.id
        })
        if (clueData.length == 0) {
          this.$message.error('请选择要编辑的数据！')
          return
        }
      }
      // let params = {
      //   ...this.formInline,
      //   ids: this.checkedAll ? [] : this.clueData
      // }
      // let ids = this.checkedAll ? [] : clueData
      // return
      this.$prompt('请输入新的企业名称', `${type == 'more' ? '批量' : ''}编辑企业名称`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'cloud_info_del_sure',
        cancelButtonClass: 'cloud_info_del_cancel',
        inputValidator: (value) => {
          if (value) {
            return true
          } else {
            return false
          }
        },
        inputErrorMessage: '请输入',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let res = await batchEditClueStore({
              ...this.formInline,
              ids: this.checkedAll ? [] : clueData,
              update_company_name: instance.inputValue
            })
            if (res.code == 0) {
              done()
              // this.resetTable()
              // this.getClueData()
              // this.getTabNum()
              this.getData()
            }
            setTimeout(() => {
              instance.confirmButtonLoading = false
            }, 300)
          } else {
            done()
          }
        }
      }).then(({ value }) => {
        this.$message({
          type: 'success',
          message: '修改成功'
        })
      })
    },
    // 线索类型: 1/2/3/4/5/6/7 证书/ICP/LOGO/关键词/域名/IP/子域名
    getClueType(type) {
      let label = ''
      if (type == '1') {
        label = '证书'
      } else if (type == '2') {
        label = 'ICP'
      } else if (type == '3') {
        label = 'ICON'
      } else if (type == '4') {
        label = '关键词'
      } else if (type == '5') {
        label = '域名'
      } else if (type == '6') {
        label = 'IP'
      } else if (type == '7') {
        label = '子域名'
      } else if (type == '8') {
        label = '企业名称'
      }
      return label
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.formInline.type = this.formInline.type / 1
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    getData(tmp) {
      if (!tmp) {
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.loading = true
      if (!this.formInline.type) {
        delete this.formInline.type
      }
      this.formInline.created_at_before = this.formInline.created_at
        ? this.formInline.created_at[1]
        : ''
      this.formInline.created_at_after = this.formInline.created_at
        ? this.formInline.created_at[0]
        : ''
      this.formInline.updated_at_before = this.formInline.updated_at
        ? this.formInline.updated_at[1]
        : ''
      this.formInline.updated_at_after = this.formInline.updated_at
        ? this.formInline.updated_at[0]
        : ''
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      let obj = { ...this.formInline }
      if (!String(obj.confirmed)) delete obj.confirmed
      if (!String(obj.cert_valid)) delete obj.cert_valid
      delete obj.created_at
      delete obj.updated_at
      delete obj.ch_confirmed
      delete obj.ch_cert_valid
      clueStoreList(obj)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    async insertSave(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.ruleForm.operate_company_id = this.currentCompany
          let obj = { ...this.ruleForm }
          this.otherLoading = true
          let res = await editClueStoreList(obj).catch(() => {
            this.otherLoading = false
          })
          this.otherLoading = false
          if (res.code == 0) {
            this.dialogFormVisibleInsert = false
            this.getData()
            this.$message.success('操作成功！')
          }
        }
      })
    },
    resetForm(formName) {
      this.formInline = {
        type: '',
        content: '',
        platform: '',
        source: '',
        company_name: '',
        page: 1,
        per_page: 10,
        keyword: '',
        operate_company_id: '',
        created_at_before: 0,
        created_at_after: 0,
        updated_at_before: 0,
        updated_at_after: 0
      }
    },
    editOne(row) {
      this.ruleForm = {
        content: '',
        platform: '',
        source: '',
        type: '',
        company_name: '',
        confirmed: 0,
        cert_valid: 0
      }
      for (let i in this.ruleForm) {
        this.ruleForm[i] = row[i]
      }
      // 线索类型是图片
      if (this.ruleForm.type == 3) {
        this.fileList = [
          {
            name: 'ICON',
            url: row.content.includes('http') ? row.content : this.showSrcIp + row.content
          }
        ]
      }
      this.ruleForm.id = row.id
      this.dialogFormVisibleInsert = true
    },
    beforeIpUpload(file) {
      let isLt1M = file.size / 1024 / 1024 < 3
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过3M!`)
      }
      return isLt1M
    },
    uploadSuccess(response, file, fileList) {
      if (file.response && file.response.data && file.response.data.url) {
        this.ruleForm.content = file.response.data.url
      }
      if (file.response.code == 0) {
        this.$message.success('上传成功！')
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadError(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      if (myError.status == 401) {
        this.$router.replace('/login')
        sessionStorage.clear()
        localStorage.clear()
      } else {
        this.$message.error(myError.message)
      }
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.ruleForm.content = ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}

.icon-image {
  display: flex;
  align-items: center;
  .hash {
    margin-left: 10px;
  }
}
</style>
