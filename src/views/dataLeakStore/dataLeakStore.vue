<template>
  <div class="container" v-loading="loading">
    <div class="headerTitle">数据泄露总库</div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="网盘" name="netWork"> </el-tab-pane>
        <el-tab-pane label="文库" name="library"> </el-tab-pane>
        <el-tab-pane label="代码仓库" name="code"> </el-tab-pane>
      </el-tabs>
      <div class="filterTab">
        <div style="display: flex; align-items: center">
          <div class="confirmBox" style="margin-right: 20px">
            <el-radio-group v-model="activeConfirm" @change="changeType">
              <el-radio-button
                :label="item"
                v-for="(item, i) in activeConfirmList[activeName]"
                :key="i"
                >{{ item.type }}</el-radio-button
              >
            </el-radio-group>
          </div>
          <el-input
            v-model="formInline.search"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入关键字进行搜索"
            id="account_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            <el-tooltip
              slot="prepend"
              class="item"
              effect="dark"
              content="支持检索字段：文件名称"
              placement="top"
              :open-delay="100"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-input>
          <span
            @click="highCheckClick"
            id="account_filter"
            style="width: 80px; display: inline-block"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F5F7FA', color: '#62666C' }"
          ref="eltable"
          height="100%"
          style="width: 100%"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
        >
          <el-table-column
            v-for="(item, itemIndex) in tableHeaderIsShow"
            :key="itemIndex"
            align="left"
            :prop="item.name"
            :label="item.label"
            :fixed="item.fixed"
            :min-width="item.minWidth"
          >
            <template slot-scope="{ row }">
              <span v-if="item.name == 'origin'">
                {{ activeConfirmName }}
              </span>
              <span v-else-if="item.name == 'keywords'">
                {{ row.keywords.join(',') }}
              </span>
              <span v-else>
                {{ row[item.name] }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="left"
            :min-width="activeConfirm.label == 1 ? '150' : '80'"
          >
            <template slot-scope="{ row }">
              <el-button type="text" size="small" @click="goEdit(row)">编辑</el-button>
              <el-button
                type="text"
                size="small"
                v-if="activeName == 'code' && row.code_spippet"
                @click="getCode(row)"
              >
                查看代码片段
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 高级筛选抽屉 -->
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="110px">
          <el-form-item label="文件名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="访问地址：" prop="url">
            <el-input v-model="formInline.url" placeholder="请输入"></el-input>
          </el-form-item>
          <!-- <el-form-item label="匹配关键词：" prop="keyword">
            <el-select v-model="formInline.keyword" placeholder="请选择">
              <el-option
                v-for="item in selectArr.keywords"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="语言类型" prop="language" v-if="activeName == 'code'">
            <el-input v-model="formInline.language" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="首次发现时间：" prop="created_at">
            <el-date-picker
              v-model="formInline.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="最新发现时间：" prop="updated_at">
            <el-date-picker
              v-model="formInline.updated_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetSelect('drawerForm')"
            id="number_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="number_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
    <!-- 编辑数据泄露总库 -->
    <el-dialog
      class="el-dialog__wrapper elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleEdit"
      width="500px"
      title="编辑"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleFormIp"
        style="padding-left: 0 !important"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="文件名称：" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="访问地址：" prop="url">
          <el-input v-model="ruleForm.url" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="语言类型：" prop="language" v-if="activeName == 'code'">
          <el-input v-model="ruleForm.language" placeholder="请输入"></el-input>
        </el-form-item>
        <!-- <el-form-item label="匹配关键词：" prop="keyword">
          <span>
            {{ currentRow.keywords && currentRow.keywords.join(",") }}
          </span>
        </el-form-item> -->
        <el-form-item label="数据来源：" prop="keyword">
          <span>
            {{ activeConfirmName }}
          </span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleEdit = false"
          id="number_recommend_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          :loading="btnLoading"
          @click="editSure"
          id="number_recommend_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 查看代码片段 -->

    <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { dataLeakTaskList, dataLeakTaskEdit, dataLeakTaskAgg } from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      btnLoading: false,
      currentRow: {}, // 行数据详情展示obj
      rules: {},
      ruleForm: {
        id: '',
        name: '',
        url: '',
        language: ''
      },
      dialogFormVisibleEdit: false,
      highCheckdialog: false,
      selectArr: {},
      highlist: null,
      formInline: {},
      tableData: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      loading: false,
      activeName: 'netWork',
      activeConfirm: { label: 8, type: '阿里云盘', parentType: '网盘' },
      activeConfirmList: {
        netWork: [
          { label: 8, type: '阿里云盘', parentType: '网盘' },
          { label: 9, type: '百度网盘', parentType: '网盘' },
          { label: 10, type: '迅雷网盘', parentType: '网盘' },
          { label: 11, type: '夸克网盘', parentType: '网盘' }
        ],
        library: [
          { label: 3, type: '百度文库', parentType: '文库' },
          { label: 4, type: '豆丁', parentType: '文库' },
          { label: 7, type: '道客巴巴', parentType: '文库' }
        ],
        code: [
          { label: 1, type: 'GitHub', parentType: '代码仓库' },
          { label: 2, type: 'Gitee', parentType: '代码仓库' },
          { label: 12, type: 'Postman', parentType: '代码仓库' },
          { label: 13, type: 'GitCode', parentType: '代码仓库' }
        ]
      },
      highTabShow: [
        {
          label: '关键字',
          name: 'search',
          type: 'input'
        },
        {
          label: '文件名称',
          name: 'name',
          type: 'input'
        },
        {
          label: '访问地址',
          name: 'url',
          type: 'input'
        },
        // {
        //   label: "匹配关键词",
        //   name: "keyword",
        //   type: "input",
        // },
        {
          label: '语言类型',
          name: 'language',
          type: 'input',
          minWidth: '100'
        },
        {
          label: '首次发现时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '最新发现时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      tableHeaderIsShow: [
        {
          label: '文件名称',
          name: 'name',
          icon: 'input',
          minWidth: '200'
        },
        {
          label: '访问地址',
          name: 'url',
          icon: 'input',
          minWidth: '200'
        },
        // {
        //   label: "匹配关键词",
        //   name: "keywords",
        //   icon: "input",
        //   minWidth: "200",
        // },
        {
          label: '数据来源',
          name: 'origin',
          icon: 'input',
          minWidth: '100'
        },
        {
          label: '语言类型',
          name: 'language',
          icon: 'input',
          minWidth: '100'
        },
        {
          label: '首次发现时间',
          name: 'created_at',
          icon: 'input',
          minWidth: '200'
        },
        {
          label: '最新发现时间',
          name: 'updated_at',
          icon: 'input',
          minWidth: '200'
        }
      ],
      activeConfirmName: '盘搜搜', // 用来显示数据来源
      search: '',
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      user: {
        role: ''
      }
    }
  },
  created() {
    this.getList()
    // this.getCondition()
  },
  mounted() {
    if (sessionStorage.getItem('userMessage')) {
      this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (this.userInfo) {
        this.user = this.userInfo.user
      }
    }
  },
  watch: {
    'activeConfirm.label'() {
      // this.getCondition()
    },
    tableData(val) {
      // this.doLayout(this, 'eltable')
    },
    activeName: {
      handler(newVal, oldVal) {
        if (newVal === 'code') {
          this.tableHeaderIsShow = [
            {
              label: '文件名称',
              name: 'name',
              icon: 'input',
              minWidth: '200'
            },
            {
              label: '访问地址',
              name: 'url',
              icon: 'input',
              minWidth: '200'
            },
            // {
            //   label: "匹配关键词",
            //   name: "keywords",
            //   icon: "input",
            //   minWidth: "200",
            // },
            {
              label: '数据来源',
              name: 'origin',
              icon: 'input',
              minWidth: '100'
            },
            {
              label: '语言类型',
              name: 'language',
              icon: 'input',
              minWidth: '100'
            },
            {
              label: '首次发现时间',
              name: 'created_at',
              icon: 'input',
              minWidth: '200'
            },
            {
              label: '最新发现时间',
              name: 'updated_at',
              icon: 'input',
              minWidth: '200'
            }
          ]
        } else {
          this.tableHeaderIsShow = [
            {
              label: '文件名称',
              name: 'name',
              icon: 'input',
              minWidth: '200'
            },
            {
              label: '访问地址',
              name: 'url',
              icon: 'input',
              minWidth: '200'
            },
            // {
            //   label: "匹配关键词",
            //   name: "keywords",
            //   icon: "input",
            //   minWidth: "200",
            // },
            {
              label: '数据来源',
              name: 'origin',
              icon: 'input',
              minWidth: '100'
            },
            {
              label: '首次发现时间',
              name: 'created_at',
              icon: 'input',
              minWidth: '200'
            },
            {
              label: '最新发现时间',
              name: 'updated_at',
              icon: 'input',
              minWidth: '200'
            }
          ]
        }
      },
      immediate: true
    }
  },
  methods: {
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    getCode(row) {
      let codeSippet = ''
      try {
        codeSippet = decodeURIComponent(row.code_spippet.replace(/\n/g, '<br/>'))
      } catch (error) {
        codeSippet = row.code_spippet
      }
      this.$alert(codeSippet, '代码片段', {
        dangerouslyUseHTMLString: true,
        customClass: 'codeClass',
        showCancelButton: true,
        showConfirmButton: false,
        cancelButtonText: '关闭'
      }).catch(() => {})
    },
    async editSure() {
      this.btnLoading = true
      // let res = await
      dataLeakTaskEdit({
        ...this.ruleForm,
        source_type: this.activeConfirm.label
      })
        .then((res) => {
          this.btnLoading = false
          if (res.code == 0) {
            this.dialogFormVisibleEdit = false
            // setTimeout(() => {
            this.$message.success('成功')
            this.getList()
            // }, 500)
          }
        })
        .catch(() => (this.btnLoading = false))
    },
    goEdit(row) {
      this.currentRow = JSON.parse(JSON.stringify(row))
      this.ruleForm.id = this.currentRow.id
      this.ruleForm.name = this.currentRow.name
      this.ruleForm.url = this.currentRow.url
      this.ruleForm.language = this.currentRow.language
      this.dialogFormVisibleEdit = true
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleClick() {
      this.currentPage = 1
      this.pageSize = 10
      let activeItem = this.activeConfirmList[this.activeName][0]
      this.activeConfirm = activeItem
      this.activeConfirmName = activeItem.type
      this.tableData = []
      this.resetSelect()
      this.getList()
    },
    changeType() {
      this.activeConfirmName = this.activeConfirm.type
      // this.$refs.eltable.doLayout()
      this.formInline = {}
      this.currentPage = 1
      this.pageSize = 10
      this.tableData = []
      this.resetSelect()
      this.getList()
    },
    async getCondition() {
      let res = await dataLeakTaskAgg({ source_type: this.activeConfirm.label })
      if (res.code == 0) {
        this.selectArr = res.data
      }
    },
    async getList() {
      const { currentPage, pageSize, activeConfirm, formInline } = this
      let obj = {
        page: currentPage,
        per_page: pageSize,
        source_type: activeConfirm.label,
        ...formInline
      }
      this.loading = true
      let res = await dataLeakTaskList(obj).catch(() => {
        this.loading = false
      })
      this.loading = false
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
      }
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList()
    },
    highCheck() {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    highCheckClick() {
      this.highCheckdialog = true
    },
    resetSelect() {
      this.formInline = {}
      this.highlist = {}
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.getList()
      this.hightFilterIsShow()
    },
    resetForm() {
      this.ruleForm = {
        name: '',
        url: ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: calc(100% - 30px);
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 120px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 120px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
  }
}
</style>

<style lang="less">
.codeClass {
  width: 80%;
  color: red;
}
</style>
