<template>
  <div class="container">
    <div class="headerTitle">
      <!-- <div>
        <span>数字资产总库</span>
        <span class="spline">/</span>
        <span>数据管理</span>
      </div> -->
      <div>
        <span>数字资产数据管理</span>
      </div>
    </div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="公众号" name="1"> </el-tab-pane>
        <el-tab-pane label="APP" name="2"> </el-tab-pane>
      </el-tabs>
      <div class="content" v-loading="allLoading">
        <DataList
          ref="dataList"
          @setPage="setPage"
          @getData="getData"
          :selectList="selectList"
          :tableData="tableData"
          :source="activeName"
          :currentPage="currentPage"
          :pageSize="pageSize"
        />
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import DataList from './dataList.vue'
import { digitalAssetList, digitalAssetDataFilter } from '@/api/apiConfig/api.js'

export default {
  components: {
    DataList
  },
  data() {
    return {
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      activeName: '1',
      selectList: [],
      allLoading: false,
      formInline: {}
    }
  },
  mounted() {
    this.getData()
    this.getFilterList()
  },
  methods: {
    setPage(currentPage, pageSize, total) {
      this.currentPage = currentPage
      if (pageSize) {
        this.pageSize = pageSize
      }
      if (total) {
        this.total = total
      }
    },
    // 获取下拉列表
    async getFilterList() {
      let res = await digitalAssetDataFilter({ source: Number(this.activeName) })
      if (res.code == 0) {
        this.selectList = res.data.company_name
      }
    },
    async getData(formInline) {
      this.formInline = formInline
      this.allLoading = true
      let res = await digitalAssetList({
        per_page: this.pageSize,
        page: this.currentPage,
        source: this.activeName,
        ...formInline
      })
      if (res.code == 0) {
        // if (this.checkedAll) {
        //       this.tableData.forEach(row => {
        //         this.$refs.eltable.toggleRowSelection(row, true);
        //       });
        //     }
        this.tableData = res.data.items || []
        this.total = res.data.total || 0
        this.allLoading = false
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(this.formInline)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(this.formInline)
    },
    handleClick() {
      this.currentPage = 1
      this.pageSize = 10
      this.tableData = []
      this.$refs.dataList.highlist = {}
      this.getData()
      this.getFilterList()
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  // height: calc(100% - 72px);
  background: #fff;
  /deep/.home_header {
    position: relative;
    // height: calc(100% - 72px);
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      padding: 0 16px;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .content {
      height: calc(100% - 43px);
      // height: 100%;
    }
  }
}
</style>
