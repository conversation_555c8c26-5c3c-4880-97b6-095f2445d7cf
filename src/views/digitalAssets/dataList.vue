<template>
  <div class="container1">
    <div class="home_header1">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.search"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
            id="user_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="user_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div v-if="!taskId">
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="digital_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            style="margin-left: 10px"
            type="primary"
            @click="digitalAssetsDelete('more')"
            id="digital_del"
            >删除</el-button
          >

          <el-dropdown>
            <el-button class="normalBtn" type="primary">
              新增<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="uploadMore">批量上传</el-dropdown-item>
              <el-dropdown-item @click.native="editList()">单个新增</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <hightFilter
        id="hightFilter"
        :highTabShow="
          highTabShow.filter((item) => {
            return (item.tab && item.tab.includes(source / 1)) || !item.tab
          })
        "
        :highlist="highlist"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="() => handleSelectable"
            v-if="!taskId"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="{ row }">
              <span v-if="item.name == 'is_online' && row.is_online">{{
                isOnlineMap[row.is_online]
              }}</span>
              <span v-else-if="item.name == 'logo' && row.logo">
                <el-image
                  :preview-src-list="[
                    row[item.name].includes('http') ? row[item.name] : showSrcIp + row[item.name]
                  ]"
                  :src="
                    row[item.name].includes('http') ? row[item.name] : showSrcIp + row[item.name]
                  "
                  lazy
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </span>
              <span v-else>{{ row[item.name] ? row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" v-if="!taskId" width="200">
            <template slot-scope="scope">
              <el-button type="text" @click="editList(scope.row)">编辑</el-button>
              <el-button type="text" @click="digitalAssetsDelete('one', scope.row.id)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="110px">
          <el-form-item v-if="source == '1'" label="公众号名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item v-if="source == '1'" label="微信号：" prop="account">
            <el-input v-model="formInline.account" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item v-if="source != '2'" label="账号主体：" prop="company_name">
            <el-select
              filterable
              v-model="formInline.company_name"
              multiple
              collapse-tags
              placeholder="请选择"
              @change="selectChange($event, 'company_name', selectList, false, true)"
              clearable
            >
              <el-option
                v-for="item in selectList"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="source == '2'" label="应用名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item v-if="source == '2'" label="应用分类：" prop="platform">
            <el-select
              v-model="formInline.platform"
              placeholder="请选择应用分类"
              @change="selectChange($event, 'platform', app_type_search, true, false)"
            >
              <el-option label="IOS" value="1"></el-option>
              <el-option label="Android" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="source == '2'" label="是否在线：" prop="status">
            <el-select
              filterable
              v-model="formInline.is_online"
              placeholder="请选择"
              @change="selectChange($event, 'is_online', statusArr, true, false)"
              clearable
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="source == '2'" label="企业名称：" prop="owner">
            <el-select
              filterable
              v-model="formInline.company_name"
              multiple
              collapse-tags
              placeholder="请选择"
              @change="selectChange($event, 'company_name', selectList, false, true)"
              clearable
            >
              <el-option
                v-for="item in selectList"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="首次发现时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="最新发现时间：" prop="updated_at_range">
            <el-date-picker
              v-model="formInline.updated_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="number_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="number_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
    <el-dialog
      class="elDialogAdd upload-dialog"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleImport"
      width="400px"
    >
      <template slot="title"> 批量上传 </template>
      <div class="dialog-body">
        <p class="downloadClass" @click="downloadAssetsExcel">
          <i class="el-icon-warning"></i>请点击下载
          <span>{{ titleMap[source] }}模板</span>
        </p>
        <el-upload
          class="upload-demo"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          accept=".xlsx"
          :before-upload="beforeIpUpload"
          :on-success="uploadSuccess"
          :on-remove="uploadRemove"
          :on-error="uploadError"
          :limit="uploadMaxCount"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">支持上传xlsx文件，且大小不超过20M</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleImport = false"
          id="number_upload_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="uploadSave" id="number_upload_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 编辑 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="500px"
      v-if="dialogFormVisibleInsert"
    >
      <template slot="title">
        {{ editForm.id ? '编辑' : '新增' }}
      </template>
      <div class="dialog-body">
        <el-form
          :model="editForm"
          :rules="editrules"
          style="padding: 0 !important"
          ref="editForm"
          label-width="110px"
          class="demo-ruleForm"
        >
          <el-form-item
            v-for="(item, index) in editItem.filter((item) => {
              return item.tab.indexOf(Number(source)) != -1
            })"
            :key="index"
            :label="item.label"
            :prop="item.name"
          >
            <el-select
              v-if="item.name == 'platform'"
              v-model="editForm[item.name]"
              placeholder="请选择应用分类"
            >
              <el-option label="IOS" value="1"></el-option>
              <el-option label="Android" value="2"></el-option>
            </el-select>
            <!-- 小程序的新增：logo、二维码上传图片 -->
            <el-radio-group v-else-if="item.name == 'is_online'" v-model="editForm.is_online">
              <el-radio label="1">在线</el-radio>
              <el-radio label="2">离线</el-radio>
            </el-radio-group>
            <el-input
              v-else-if="item.name == 'keyword'"
              :placeholder="`请输入${item.label}`"
              :disabled="Boolean(editForm.id)"
            ></el-input>
            <el-input
              v-else
              v-model="editForm[item.name]"
              :placeholder="`请输入${item.label}`"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="editSave('editForm')"
          :loading="editLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  digitalAssetDataImport,
  digitalAssetDataAdd,
  digitalAssetDataDelete,
  digitalAssetDataEdit
} from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip, hightFilter },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    source: {
      type: String,
      default: ''
    },
    currentPage: {
      type: [String, Number],
      default: 1
    },
    pageSize: {
      type: [String, Number],
      default: 10
    },
    total: {
      type: [String, Number],
      default: 0
    },
    selectList: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    source: {
      handler(val) {
        this.$emit('setPage', 1, 10, 0)
        // this.currentPage = 1
        // this.pageSize = 10
        // this.total = 0
        this.tableHeader = this['tableHeader' + val]
        this.formInline = {}
        this.checkedArr = []
        this.checkedAll = false
        this.$nextTick(() => {
          this.$refs.eltable.clearSelection()
        })
        this.highList = []
      },
      immediate: true
    },
    checkedAll(val) {
      if (val) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
      this.handleSelectable = !val
    },
    tableData: {
      handler() {
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
          })
        } else {
          this.$refs.eltable.clearSelection()
        }
      },
      deep: true
    }
  },
  data() {
    return {
      editLoading: false,
      editrules: {
        name: [{ required: true, message: '请填写', trigger: 'blur' }],
        account: [{ required: true, message: '请填写', trigger: 'blur' }],
        company_name: [{ required: true, message: '请填写', trigger: 'blur' }],
        url: [{ required: true, message: '请填写', trigger: 'blur' }],
        platform: [{ required: true, message: '请填写', trigger: 'change' }],
        is_online: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      highTabShow: [
        {
          label: '公众号名称',
          name: 'name',
          type: 'input',
          tab: [1]
        },
        {
          label: '微信号',
          name: 'account',
          type: 'input',
          tab: [1]
        },
        {
          label: '账号主体',
          name: 'company_name',
          type: 'select',
          tab: [1]
        },
        {
          label: '应用名称',
          name: 'name',
          type: 'input',
          tab: [2]
        },
        {
          label: '是否在线',
          name: 'is_online',
          type: 'select',
          tab: [1, 2]
        },
        {
          label: '应用分类',
          name: 'platform',
          type: 'select',
          tab: [2]
        },
        {
          label: '企业名称',
          name: 'company_name',
          type: 'select',
          tab: [2]
        },
        {
          label: '首次发现时间',
          name: 'created_at_range',
          type: 'date',
          tab: [1, 2]
        },
        {
          label: '最新发现时间',
          name: 'updated_at_range',
          type: 'date',
          tab: [1, 2]
        }
      ],
      editItem: [
        {
          label: '公众号名称',
          name: 'name',
          tab: [1]
        },
        {
          label: '应用名称',
          name: 'name',
          tab: [2]
        },
        {
          label: '微信号',
          name: 'account',
          tab: [1]
        },
        {
          label: '下载链接',
          name: 'url',
          tab: [2]
        },
        {
          label: '账号主体',
          name: 'company_name',
          type: 'select',
          tab: [1]
        },
        {
          label: '应用分类',
          name: 'platform',
          tab: [2]
        },
        {
          label: '企业名称',
          name: 'company_name',
          tab: [2]
        },
        {
          label: '是否在线',
          name: 'is_online',
          tab: [2]
        },
        {
          label: 'LOGO地址',
          name: 'logo',
          tab: [2]
        },
        {
          label: '简介',
          name: 'description',
          tab: [1]
        }
        // {
        //   label: '匹配关键词',
        //   name: 'keyword',
        //   tab: [1,2]
        // }
      ],
      dialogFormVisibleInsert: false,
      editForm: {
        name: '',
        account: '',
        company_name: '',
        description: '',
        url: '',
        platform: '',
        is_online: '1',
        logo: ''
      },
      btnLoading: false,
      uploadMaxCount: 1,
      uploadPath: '',
      uploadPathLogo: '',
      uploadPathEwm: '',
      fileList: [],
      dialogFormVisibleImport: false,
      uploadAction: '',
      highlist: [],
      statusArr: [
        {
          name: '在线',
          id: 1
        },
        {
          name: '离线',
          id: 2
        }
      ],
      app_type_search: [
        {
          id: '1',
          name: 'IOS'
        },
        {
          id: '2',
          name: 'Android'
        }
      ],
      owner_search: [],
      tableHeader: [],
      loading: false,
      // total: 0,
      // currentPage: 1,
      // pageSize: 10,
      tableHeader1: [
        {
          label: '公众号',
          name: 'name',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '微信号',
          name: 'account',
          minWidth: 100
        },
        {
          label: '二维码URL',
          name: 'logo',
          minWidth: 100
        },
        {
          label: '账号主体',
          name: 'company_name',
          minWidth: 100
        },
        {
          label: '数据来源',
          name: 'platform',
          minWidth: 100
        },
        // {
        //   label: '是否在线',
        //   name: 'is_online',
        //   minWidth: 100
        // },
        {
          label: '描述',
          name: 'description',
          minWidth: 100
        },
        // {
        //   label: '匹配关键词',
        //   name: 'keyword',
        //   minWidth: 100
        // },
        {
          label: '创建时间',
          name: 'created_at',
          minWidth: 100
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: 100
        }
      ],
      tableHeader2: [
        {
          label: '应用名称',
          name: 'name',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '下载链接',
          name: 'url',
          minWidth: 100
        },
        {
          label: 'Logo URL',
          name: 'logo',
          minWidth: 100
        },
        {
          label: '企业名称',
          name: 'company_name',
          minWidth: 100
        },
        {
          label: '应用分类',
          name: 'platform',
          minWidth: 100
        },
        {
          label: '是否在线',
          name: 'is_online',
          minWidth: 100
        },
        // {
        //   label: '匹配关键词',
        //   name: 'keyword',
        //   minWidth: 100
        // },
        {
          label: '创建时间',
          name: 'created_at',
          minWidth: 100
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: 100
        }
      ],
      checkedArr: [],
      checkedAll: false,
      formInline: {},
      highCheckdialog: false,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      titleMap: {
        1: '公众号',
        2: 'APP'
      },
      isOnlineMap: {
        1: '在线',
        2: '离线'
      },
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      taskId: this.$route.query.taskId
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  methods: {
    resetForm() {
      this.formInline = {}
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    digitalAssetsDelete(type, id) {
      let ids = []
      if (type == 'more') {
        if (!this.checkedAll && this.checkedArr.length == 0) {
          this.$message.warning('请选择要操作的数据')
          return
        }
        ids = !this.checkedAll ? this.checkedArr.map((item) => item.id) : []
      } else {
        ids = [id]
      }
      let obj = {}
      obj = {
        ids,
        operate_company_id: this.currentCompany,
        is_all: this.checkedAll ? 1 : 0,
        page: this.currentPage,
        per_page: this.pageSize,
        source: Number(this.source),
        ...this.formInline
      }
      // return
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_info_del_cancel',
        confirmButtonClass: 'cloud_info_del_sure',
        customClass: 'cloud_info_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await digitalAssetDataDelete(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.$emit('getData', this.formInline)
            this.checkedAll = false
            this.$refs.eltable.clearSelection()
            this.checkedArr = []
          }
        })
        .catch(() => {})
    },
    // 新增确定
    editSave(row) {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          let actionFun = null

          if (!this.editForm.id) {
            // 新增
            actionFun = digitalAssetDataAdd
          } else {
            // 编辑
            actionFun = digitalAssetDataEdit
          }
          this.editForm.source = Number(this.source)
          this.editForm.operate_company_id = this.currentCompany
          this.editLoading = true
          let res = await actionFun(this.editForm).catch(() => {
            this.editLoading = false
          })
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.dialogFormVisibleInsert = false
            this.$emit('getData', this.formInline)
            // this.getlogsIndexData()
          }
          this.editLoading = false
        }
      })
    },
    // 新增，编辑
    async editList(row) {
      this.uploadPath = ''
      this.uploadPathLogo = ''
      this.uploadPathEwm = ''
      this.uploadAction = `${this.golangUploadSrcIp}/public/upload`
      this.dialogFormVisibleInsert = true
      if (row) {
        for (let i in this.editForm) {
          this.editForm[i] = row[i]
        }
        this.editForm.id = row.id
        this.editForm.operate_company_id = this.currentCompany
        this.uploadPathLogo = row.logo
        this.uploadPathEwm = row.erweima
      } else {
        this.editForm = {
          name: '',
          account: '',
          company_name: '',
          description: '',
          url: '',
          platform: '',
          is_online: '1',
          logo: ''
        }
      }
    },
    // 批量上传保存
    async uploadSave() {
      if (!this.uploadPath) {
        this.$message.error('请上传文件')
        return
      }
      this.btnLoading = true

      let res = await digitalAssetDataImport({
        file_path: this.uploadPath,
        source: Number(this.source),
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.dialogFormVisibleImport = false
      }
      this.btnLoading = false
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadError(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      if (myError.status == 401) {
        this.$router.replace('/login')
        sessionStorage.clear()
        localStorage.clear()
      } else {
        this.$message.error(myError.message)
      }
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.uploadPath = ''
      }
    },
    // 批量上传
    uploadSuccess(response, file, fileList) {
      if (file.response && file.response.data && file.response.data.path) {
        this.uploadPath = file.response.data.path
      }
      if (file.response.code == 0) {
        this.$message.success('上传成功！')
      } else {
        this.$message.error(file.response.message)
      }
    },
    beforeIpUpload(file) {
      let isLt1M = ''
      isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过20M!`)
      }
      return isLt1M
    },
    downloadAssetsExcel() {
      window.location.href = `/downloadTemplate/${this.titleMap[this.source]}数据导入模板.xlsx`
    },
    uploadMore() {
      this.uploadPath = ''
      this.uploadPathLogo = ''
      this.uploadPathEwm = ''
      this.fileList = []
      this.dialogFormVisibleImport = true
      this.uploadAction = `${this.golangUploadSrcIp}/public/upload`
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    checkFuncList() {
      this.highCheckdialog = false
      // this.currentPage = 1;
      this.$emit('setPage', 1, 10)
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.$emit('getData', this.formInline)
      // this.getlogsIndexData();
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    }
  }
}
</script>

<style lang="less" scoped>
.container1 {
  position: relative;
  width: 100%;
  height: calc(100% - 60px);
  // height: 100%;
  // background: #fff;
  /deep/.home_header1 {
    position: relative;
    // height: calc(100% - 200px);
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }
    .tableWrap {
      height: calc(100% - 79px);
      padding: 0px 20px;
      &.tableWrapFilter {
        height: calc(100% - 129px) !important;
      }
    }
  }
}
</style>
