<template>
  <div class="container">
    <div class="headerTitle">
      <div>
        <span>数字资产总库</span>
        <span class="spline">/</span>
        <span>任务管理</span>
      </div>
    </div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="公众号" name="1"></el-tab-pane>
        <el-tab-pane label="APP" name="2"> </el-tab-pane>
      </el-tabs>
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.search"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
            id="user_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckDialog = true" id="user_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="digital_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            style="margin-left: 10px"
            type="primary"
            @click="digitalTaskDelete('more')"
            id="digital_del"
            >删除</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            id="digital_add"
            @click="addDialogVisible = true"
            >下发任务</el-button
          >
        </div>
      </div>
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highList"
        :total="total"
        pageIcon="user"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <taskList
          ref="taskList"
          :source="activeName"
          :tableData="tableData"
          :checkedAll="checkedAll"
          @hightFilterIsShow="hightFilterIsShow"
          @digitalTaskDelete="digitalTaskDelete"
          @handleSelectionChange="handleSelectionChange"
        />
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- </div> -->
    <el-drawer title="高级筛选" :visible.sync="highCheckDialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="关键词：" prop="keyword">
            <el-input v-model="formInline.keyword" placeholder="请输入关键词"></el-input>
          </el-form-item>
          <el-form-item label="任务进度：" prop="progress">
            <el-select
              v-model="formInline.progress"
              placeholder="请选择任务进度"
              collapse-tags
              @change="selectChange($event, 'progress', progressArr, true, false)"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in progressArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间：" prop="created_at">
            <el-date-picker
              v-model="formInline.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="更新时间：" prop="updated_at">
            <el-date-picker
              v-model="formInline.updated_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="user_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="user_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
    <prompt
      title="下发任务"
      placeholder="请输入关键词,支持输入多个,分号或换行分隔"
      :visible="addDialogVisible"
      @save="addTask"
      @close="addDialogVisible = false"
      :loading="addDialogBtnLoading"
      inputType="textarea"
      label="关键词"
      :rows="6"
    />
  </div>
</template>

<script>
import prompt from '@/components/assets/prompt'
import hightFilter from '../../components/assets/highTab.vue'
import taskList from './taskList.vue'
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { digitalAssetTaskList, digitalAssetTask, digitalAssetTaskDel } from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip, taskList, hightFilter, prompt },
  data() {
    return {
      addDialogVisible: false,
      addDialogBtnLoading: false,
      highList: null,
      highTabShow: [
        {
          label: '关键词',
          name: 'keyword',
          type: 'input'
        },
        {
          label: '任务进度',
          name: 'progress',
          type: 'select'
        },
        {
          label: '创建时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      progressArr: [
        { name: '进行中', value: '1' },
        { name: '已完成', value: '2' }
      ],
      tableData: [],
      loading: false,
      total: 0,
      currentPage: 1,
      pageSize: 10,
      checkedArr: [],
      checkedAll: false,
      formInline: {},
      highCheckDialog: false,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      activeName: this.$route.query.source || '1'
    }
  },
  mounted() {
    this.getData()
  },
  watch: {
    activeName() {
      this.currentPage = 1
      this.pageSize = 10
      this.total = 0
      this.loading = false
      this.formInline = {}
      this.checkedArr = []
      this.checkedAll = false
      this.$refs.taskList.$refs.eltable.clearSelection()
      this.highList = []
      this.resetForm()
      this.getData()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    async addTask(val) {
      if (!val) {
        this.$message.error('请输入关键词')
        return
      }
      let name_list = val
        ? val
            .split(/[；|;|\r\n]/)
            .filter((item) => {
              return item.trim()
            })
            .map((item) => {
              return item.trim()
            })
        : []
      let res = await digitalAssetTask({
        name: name_list,
        force: 0,
        source: Number(this.activeName)
      })
      if (res.code == 0) {
        this.$message.success('下发任务成功')
        this.addDialogVisible = false
        this.addDialogBtnLoading = false
        this.getData()
      }
    },
    digitalTaskDelete(type, id) {
      let ids = []
      if (type == 'more') {
        if (!this.checkedAll && this.checkedArr.length == 0) {
          this.$message.warning('请选择要操作的数据')
          return
        }
        ids = !this.checkedAll ? this.checkedArr.map((item) => item.id) : []
      } else {
        ids = [id]
      }
      let obj = {}
      obj = {
        ids,
        operate_company_id: this.currentCompany,
        is_all: this.checkedAll ? 1 : 0,
        page: this.currentPage,
        per_page: this.pageSize,
        source: Number(this.activeName),
        ...this.formInline
      }
      // return
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_info_del_cancel',
        confirmButtonClass: 'cloud_info_del_sure',
        customClass: 'cloud_info_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await digitalAssetTaskDel(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.getData()
            this.checkedAll = false
            this.$refs.taskList.$refs.eltable.clearSelection()
            this.checkedArr = []
          }
        })
        .catch(() => {})
    },
    handleClick(val) {
      this.$router.push({ source: val })
    },
    resetForm() {
      this.formInline = {
        keyword: '',
        progress: '',
        created_at: [],
        updated_at: []
      }
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highList)
      this.checkFuncList()
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckDialog = false
      this.highList = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.currentPage = 1
      this.getData()
    },
    async getData() {
      let res = await digitalAssetTaskList({
        page: this.currentPage,
        per_page: this.pageSize,
        source: this.activeName,
        ...this.formInline
      })
      if (res.code == 0) {
        this.tableData = res.data.items || []
        this.total = res.data.total || 0
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    checkAllChange() {},
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      padding: 0 16px;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }
    // .tableWrap {
    //   height: calc(100% - 129px);
    //   padding: 0px 20px;
    //   &.tableWrapFilter{
    //     height: calc(100% - 180px) !important;
    //   }
    // }
  }
}
.tableWrap {
  height: calc(100% - 169px);
  padding: 0px 20px;
  &.tableWrapFilter {
    height: calc(100% - 220px) !important;
  }
}
</style>
