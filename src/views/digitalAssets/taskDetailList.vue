<template>
  <div class="container">
    <div class="headerTitle">
      <!-- <div>
        <span>数字资产总库</span>
        <span class="spline">/</span>
        <span>数据管理</span>
      </div> -->
      <div>
        <span class="goback" @click="goback()"><i class="el-icon-arrow-left"></i>返回上一层</span>
        <span class="spline">/</span>
        <!-- <span>数字资产数据管理</span> -->
        <span>查看详情</span>
      </div>
    </div>
    <div class="home_header">
      <div class="content" v-loading="allLoading">
        <DataList
          @setPage="setPage"
          :tableData="tableData"
          :selectList="selectList"
          :source="source"
          @getData="getData"
          :currentPage="currentPage"
          :pageSize="pageSize"
        />
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import DataList from './dataList.vue'
import { digitalAssetTaskResList, digitalAssetDataFilter } from '@/api/apiConfig/api.js'

export default {
  components: {
    DataList
  },
  data() {
    return {
      allLoading: false,
      taskId: '',
      source: '',
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      formInline: {},
      selectList: []
    }
  },
  mounted() {
    const { source, taskId } = this.$route.query
    this.source = source
    this.taskId = taskId
    this.getData()
    this.getFilterList()
  },
  methods: {
    setPage(currentPage, pageSize, total) {
      this.currentPage = currentPage
      if (pageSize) {
        this.pageSize = pageSize
      }
      if (total) {
        this.total = total
      }
    },
    goback() {
      if (this.source == '2') {
        this.$router.push({ path: 'digitalAssets-task', query: { source: '2' } })
      } else {
        this.$router.go(-1)
      }
    },
    // 获取下拉列表
    async getFilterList() {
      let res = await digitalAssetDataFilter({ source: Number(this.source) })
      if (res.code == 0) {
        this.selectList = res.data.company_name
      }
    },
    async getData(formInline) {
      this.formInline = formInline
      let res = await digitalAssetTaskResList({
        page: this.currentPage,
        per_page: this.pageSize,
        source: this.source,
        task_id: this.taskId,
        ...formInline
      })
      if (res.code == 0) {
        this.tableData = res.data.items || []
        this.total = res.data.total || 0
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(this.formInline)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(this.formInline)
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
  }
  .content {
    height: calc(100% - 13px);
    // height: 100%;
  }
}
</style>
