<template>
  <div class="tableContent">
    <el-table
      border
      :data="tableData"
      v-loading="loading"
      row-key="id"
      :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
      @selection-change="handleSelectionChange"
      @cell-mouse-enter="showTooltip"
      @cell-mouse-leave="hiddenTooltip"
      ref="eltable"
      height="100%"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        align="center"
        :reserve-selection="true"
        :selectable="() => handleSelectable"
        width="55"
      >
      </el-table-column>
      <el-table-column
        v-for="item in tableHeader"
        :key="item.id"
        :prop="item.name"
        align="left"
        :min-width="item.minWidth"
        :fixed="item.fixed"
        :label="item.label"
      >
        <template slot-scope="{ row }">
          <span v-if="item.name == 'progress' && (row.progress == 0 || row.progress)">
            <span v-if="row.progress == 0">未开始</span>
            <span v-else-if="row.progress != 0 && row.progress != 100">进行中</span>
            <span v-else-if="row.progress == 100">已完成</span>
          </span>
          <span v-else>{{ row[item.name] ? row[item.name] : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="left" width="200">
        <template slot-scope="{ row, $index }">
          <el-button
            type="text"
            size="small"
            id="digital_update"
            @click="forceUpdate(row.id, $index)"
            >强制刷新</el-button
          >
          <el-button
            type="text"
            size="small"
            id="digital_detail"
            @click="
              $router.push({
                path: '/digitalAssets-task-detail',
                query: { source, taskId: row.id }
              })
            "
            >查看详情</el-button
          >
          <el-button
            type="text"
            size="small"
            id="digital_del"
            @click="$emit('digitalTaskDelete', 'one', row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { digitalAssetTask, digitalAssetTaskInfo } from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    checkedAll: {
      type: Boolean,
      default: false
    },
    source: {
      type: [String, Number],
      default: ''
    }
  },
  watch: {
    checkedAll(val) {
      if (val) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
      this.handleSelectable = !val
    },
    tableData: {
      handler() {
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
          })
        } else {
          this.$refs.eltable.clearSelection()
        }
      },
      deep: true
    }
  },
  data() {
    return {
      handleSelectable: true,
      loading: false,
      formInline: {},
      total: 0,
      currentPage: 1,
      pageSize: 10,
      tableHeader: [
        {
          label: '关键词',
          name: 'name',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '任务进度',
          name: 'progress',
          minWidth: 100
        },
        {
          label: '创建时间',
          name: 'created_at',
          minWidth: 100
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: 100
        }
      ],
      checkedArr: [],
      // checkedAll: false,
      formInline: {},
      highCheckdialog: false,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      }
    }
  },

  methods: {
    async forceUpdate(id, index) {
      let res = await digitalAssetTask({ force: '1', ids: [id], source: +this.source })
      if (res.code == 0) {
        let res1 = await digitalAssetTaskInfo({ id, source: +this.source }).catch(() => {
          this.$message.error('刷新失败')
        })
        if (res1.code == 0) {
          this.$set(this.tableData, index, res1.data)
          this.$message.success('刷新成功')
        }
      }
    },
    resetForm(ref) {
      // this.$refs[ref].resetFields()
      this.formInline = {
        keyword: '',
        name: '',
        mobile: '',
        email: '',
        role: [],
        status: '',
        created_at_range: [],
        company: ''
      }
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1

      // this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.getData()
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    // hightFilterIsShow() {
    //   return this.$emit('hightFilterIsShow')
    // },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    // handleSelectable (row, index) {
    //   // return !this.checkedAll;
    //   return true
    // },
    handleSelectionChange(val) {
      // this.checkedArr = val
      this.$emit('handleSelectionChange', val)
    },
    checkFuncList() {},
    checkAllChange() {},
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }
  }
}
.tableContent {
  height: 100%;
}

.content {
  height: calc(100% - 170px);
}
</style>
