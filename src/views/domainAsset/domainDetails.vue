<template>
  <div class="containerInfo">
    <div class="headerTitle">
      <!-- <span class="goback" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i>返回上一层</span>
      <span class="spline">/</span> -->
      <span>域名详情</span>
    </div>
    <div class="home_header" v-loading="loading">
      <div class="tableWrap">
        <div class="leftTab">
          <div class="leftTabChange">
            <ul class="ipInfo ipInfoSpecial">
              <div class="infoTitle">
                <div>
                  {{ domain_info.domain }}
                  <span class="greenLine" v-if="domain_info.status == 1">可解析</span>
                  <span class="redLine" v-else-if="domain_info.status == 0">不可解析</span>
                  <span class="grayLine" v-else-if="domain_info.status == 2">待解析</span>
                </div>
                <div>
                  <!-- <el-button style="margin-left: 20px;" @click="edit" type="text" size="small">编辑</el-button> -->
                </div>
              </div>
              <li>
                <p v-for="item in infoHeader" :key="item.name">
                  {{ item.label }}
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="domain_info[item.name]"
                    placement="top"
                    :open-delay="500"
                  >
                    <div>
                      {{ domain_info[item.name] == '' ? '-' : domain_info[item.name] }}
                    </div>
                  </el-tooltip>
                </p>
              </li>
            </ul>
            <ul class="ipInfo ipInfoSpecial1" style="margin-top: 12px">
              <div class="infoTitle">
                <div>Whois信息</div>
              </div>
              <li class="whiosLi">
                <div v-for="item in whiosHeader" :key="item.name" class="whiosLiLabel">
                  <span>{{ item.label }}</span>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="item.text"
                    placement="top"
                    :open-delay="500"
                  >
                    <div class="whiosText">{{ item.text || '-' }}</div>
                  </el-tooltip>
                </div>
              </li>
            </ul>
          </div>
          <div class="rightWrap">
            <p class="infoTitle">IP历史变化</p>
            <el-timeline>
              <el-timeline-item v-for="item in timeLineData" :key="item.id">
                <div class="tiemBox">
                  {{ item.created_at
                  }}<span class="tiemContent"
                    >解析IP：<span style="color: rgba(38, 119, 255, 1)">{{ item.ips }}</span></span
                  >
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
        <div class="portInfo">
          <div class="infoTitle">
            <div class="leftT">
              <div>关联IP信息</div>
              <div>
                <el-checkbox
                  :indeterminate="isIndeterminate"
                  v-model="checkAll"
                  @change="handleCheckAllChange"
                  >全选</el-checkbox
                >
              </div>
              <el-button @click="scanIp('more')" type="text">扫描入账IP资产</el-button>
            </div>
            <div class="leftR">
              <!-- 扫描查询实时进度接口需要name -->
              <IpScanProgress
                typeName="资产台账-域名详情"
                ref="ipScanProgress"
                @updateScanStatus="updateScanStatus"
                v-loading="scanLoading"
              />
            </div>
          </div>
          <div class="infoMain">
            <div class="infoLeft">
              <el-checkbox-group v-model="checkedArr" @change="handleCheckedIPChange">
                <div class="infoIpLeft">
                  <div
                    v-for="(item, key) in ipUpdateSpecail"
                    :key="key"
                    :class="item.ip == tabChangeVal ? 'infoIpLeftSpecial' : ''"
                  >
                    <el-checkbox :label="item" :key="key">
                      <el-tooltip
                        class="ellipsis"
                        effect="dark"
                        :content="item.ip"
                        placement="top"
                        :open-delay="500"
                      >
                        <div @click.stop.prevent="ipListClick(item)">{{ item.ip }}</div>
                      </el-tooltip>
                    </el-checkbox>
                  </div>
                </div>
              </el-checkbox-group>
              <!-- <el-tabs tab-position="left" style="height: 200px;" @tab-click="tabClick" v-model="tabChangeVal">
                <el-tab-pane v-for="(item,key) in ipUpdateSpecail" :key="key" :label="item.ip" :name="item.ip" @hover='tabHover(item.ip)'>
                </el-tab-pane>
              </el-tabs> -->
            </div>
            <div class="infoRight" v-if="ipUpdateFlag">
              <div class="rightTop">
                <span class="ipTextBox">
                  {{ ipUpdateObj['ip'] }}
                </span>
                <span class="greenLine" v-if="ipUpdateObj['online_state'] == 1">在线</span>
                <span class="grayLine" v-else>离线</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="点击可前往IP资产页面查看详情"
                  placement="top"
                  :open-delay="500"
                >
                  <span class="redLine cursor" @click="goToIPList">IP台账</span>
                </el-tooltip>
                <span class="text domainTime"> 关联时间:{{ domainTime }} </span>
              </div>
              <div class="rightMain">
                <div class="mainDomainLeft">
                  <div>
                    <div class="domainLeftText">
                      <div class="domainLeftTextMain">
                        <div v-for="(cn_item, key) in leftMainHeader" :key="key" class="pText">
                          <p class="pTextLabel">{{ cn_item.label }}</p>
                          <el-tooltip
                            class="item"
                            effect="dark"
                            :content="setGeo(ipUpdateObj['geo'])"
                            placement="top"
                            :open-delay="500"
                            v-if="cn_item.name == 'geo'"
                          >
                            <p class="pTextSpecial">
                              {{ setGeo(ipUpdateObj['geo']) }}
                            </p>
                          </el-tooltip>
                          <el-tooltip
                            v-else-if="cn_item.name == 'isp'"
                            class="item"
                            effect="dark"
                            :content="ipUpdateObj['geo'] ? ipUpdateObj['geo'].isp : ''"
                            placement="top"
                            :open-delay="500"
                          >
                            <p class="pTextSpecial">
                              {{ ipUpdateObj['geo'] ? ipUpdateObj['geo'].isp : '' }}
                            </p>
                          </el-tooltip>
                          <el-tooltip
                            v-else-if="cn_item.name == 'lon'"
                            class="item"
                            effect="dark"
                            :content="String(ipUpdateObj['geo'] ? ipUpdateObj['geo'].lon : '')"
                            placement="top"
                            :open-delay="500"
                          >
                            <p class="pTextSpecial">
                              {{ ipUpdateObj['geo'] ? ipUpdateObj['geo'].lon : '' }}
                            </p>
                          </el-tooltip>
                          <el-tooltip
                            v-else-if="cn_item.name == 'lat'"
                            class="item"
                            effect="dark"
                            :content="String(ipUpdateObj['geo'] ? ipUpdateObj['geo'].lat : '')"
                            placement="top"
                            :open-delay="500"
                          >
                            <p class="pTextSpecial">
                              {{ ipUpdateObj['geo'] ? ipUpdateObj['geo'].lat : '' }}
                            </p>
                          </el-tooltip>
                          <el-tooltip
                            v-else-if="cn_item.name == 'tags'"
                            class="item"
                            effect="dark"
                            :content="getTagsClassName(ipUpdateObj['tags'])"
                            placement="top"
                            :open-delay="500"
                          >
                            <p class="pTextSpecial">
                              <span
                                v-for="(tags, i) in ipUpdateObj['tags']"
                                :key="i"
                                class="spanSpecal"
                                >{{ getTagsName(tags) }}</span
                              >
                            </p>
                          </el-tooltip>
                          <el-tooltip
                            v-else
                            class="item"
                            effect="dark"
                            :content="ipUpdateObj[cn_item.name]"
                            placement="top"
                            :open-delay="500"
                          >
                            <p class="pTextSpecial">
                              {{
                                ipUpdateObj[cn_item.name] == null ? '-' : ipUpdateObj[cn_item.name]
                              }}
                            </p>
                          </el-tooltip>
                        </div>
                      </div>
                    </div>
                    <img src="../../assets/images/domainDetails.png" alt="" />
                  </div>
                </div>
                <div class="mainDomainRight">
                  <div class="portInfoMess">
                    <!-- <el-tabs class="tabs" v-model="ipUpdateObj['tabKey']">
                       <el-tab-pane :label="`组件信息`" name="a">
                     </el-tab-pane>
                       <el-tab-pane :label="`端口信息`" name="b">
                     </el-tab-pane>
                    <el-tab-pane  :label="`漏洞信息`" name="c">
                        </el-tab-pane>
                      <el-tab-pane :label="`线索链`" name="d">
                      </el-tab-pane>
                      </el-tabs> -->
                    <el-radio-group v-model="ipUpdateObj['tabKey']" style="height: 32px">
                      <el-radio-button label="a">组件信息</el-radio-button>
                      <el-radio-button label="b">端口信息</el-radio-button>
                      <el-radio-button label="c">漏洞信息</el-radio-button>
                      <el-radio-button label="d">线索链</el-radio-button>
                    </el-radio-group>
                    <ul class="ruleClass" v-if="ipUpdateObj['tabKey'] == 'a'">
                      <li v-for="item_t in ruleTitle" :key="item_t.id">
                        <i :class="'line' + item_t.name"></i>
                        <p>
                          <span>{{ item_t.label }}</span>
                        </p>
                        <div class="rules">
                          <span
                            class="ruleItem"
                            v-for="(ch, key) in setRuleTags(ipUpdateObj['rule_tags'])[item_t.name]"
                            :key="key"
                            >{{ ch }}</span
                          >
                        </div>
                      </li>
                    </ul>
                    <div class="bBoxText" v-show="ipUpdateObj['tabKey'] == 'b'">
                      <el-table
                        border
                        :data="ipUpdateObj['port_list']"
                        row-key="id"
                        :header-cell-style="{
                          background: '#F2F3F5',
                          color: '#62666C'
                        }"
                        ref="eltable"
                        height="100%"
                        style="width: 100%"
                      >
                        <el-table-column v-show="ipUpdateObj['tabKey'] == 'b'" type="expand">
                          <template slot-scope="props">
                            <p
                              class="banner"
                              v-html="
                                props.row.banner ? props.row.banner.replace(/\n/g, '<br>') : ''
                              "
                            ></p>
                          </template>
                        </el-table-column>
                        <el-table-column
                          v-for="item_s in tableHeader"
                          :key="item_s.id"
                          :prop="item_s.name"
                          align="left"
                          :show-overflow-tooltip="true"
                          :min-width="item_s.minWidth"
                          :label="item_s.label"
                        >
                          <template slot-scope="scope">
                            <p class="rules" v-if="item_s.name == 'rules'" style="padding: 0">
                              <span v-if="scope.row[item_s.name]" class="ruleItemBox">
                                <span
                                  class="ruleItem"
                                  v-for="(item_ta, index) in scope.row[item_s.name]"
                                  :key="index"
                                  >{{ item_ta }}</span
                                >
                              </span>
                              <i v-else>-</i>
                            </p>
                            <span v-else>{{ getTableItem(scope.row[item_s.name]) }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                    <div v-show="ipUpdateObj['tabKey'] == 'c'">
                      <el-table
                        border
                        :data="ipUpdateObj['threats']"
                        row-key="id"
                        :header-cell-style="{
                          background: '#F2F3F5',
                          color: '#62666C'
                        }"
                        ref="eltable1"
                        height="100%"
                        style="width: 100%"
                      >
                        <el-table-column
                          v-for="items in threatsTableHeader"
                          :key="items.id"
                          :prop="items.name"
                          align="left"
                          :show-overflow-tooltip="true"
                          :min-width="items.minWidth"
                          :label="items.label"
                        >
                          <template slot-scope="scope">
                            <div class="rules" v-if="items.name == 'level'" style="padding: 0">
                              <span v-if="scope.row[item.name] == 3" class="deepRedRadiusBorder"
                                >严重</span
                              >
                              <span v-if="scope.row[item.name] == 2" class="redRadiusBorder"
                                >高危</span
                              >
                              <span v-if="scope.row[item.name] == 1" class="originRadiusBorder"
                                >中危</span
                              >
                              <span v-if="scope.row[item.name] == 0" class="yellowRadiusBorder"
                                >低危</span
                              >
                            </div>
                            <span v-else-if="items.name == 'state'">
                              <span v-if="scope.row[items.name] == 2 || scope.row[items.name] == 3"
                                >已修复</span
                              >
                              <span v-else>未修复</span>
                            </span>
                            <span v-else>{{
                              scope.row[items.name] ? scope.row[items.name] : '-'
                            }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                    <ul class="chainClass_info" v-if="ipUpdateObj['tabKey'] == 'd'">
                      <li
                        v-for="(chain, chainIndex) in ipUpdateObj['chain_list']"
                        :key="chainIndex"
                      >
                        <span class="xuhao">{{ chainIndex + 1 }}</span>
                        <div>
                          <span v-for="(con, index) in chain" :key="index">
                            <span v-if="con.type && con.type == 3">
                              <el-image
                                class="imgwrap"
                                :src="
                                  con.content.includes('http')
                                    ? con.content
                                    : showSrcIp + con.content
                                "
                                alt=""
                              >
                                <div slot="error" class="image-slot">
                                  <i class="el-icon-picture-outline"></i>
                                </div>
                              </el-image>
                            </span>
                            <span class="chain">{{ con.content || con }}</span>
                            <i v-if="index < chain.length - 1" class="el-icon-right iconRight"></i>
                          </span>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <!-- 实时解析 -->
            <div class="infoRight" v-if="!ipUpdateFlag">
              <div class="rightTop" v-if="ipUpdateObj.ip">
                <span class="ipTextBox">
                  {{ ipUpdateObj['ip'] }}
                </span>
                <span class="redLine">实时解析</span>
                <span class="text">关联时间:{{ domainTime }}</span>
              </div>
              <!-- <el-empty description="暂无数据"></el-empty> -->
              <div class="emptyClass">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-kong"></use>
                </svg>
                <p>暂无数据</p>
                <el-button v-if="ipUpdateObj.ip" @click="scanIp('one')" type="text"
                  >扫描获取详细信息</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import baseUse from '@/utils/baseUse'
import { mapGetters, mapState, mapMutations } from 'vuex'
import IpScanProgress from '@/components/ipScanProgress'
import { domainDetails, domainWhoIsDetails } from '@/api/apiConfig/domain.js'
import { getAllTaskList } from '@/api/apiConfig/discovery.js'
import { importSureIpDataV1 } from '@/api/apiConfig/asset.js'

export default {
  components: { IpScanProgress },
  data() {
    return {
      scanLoading: false,
      domainTime: '',
      portGroupIds: '',
      checkedArr: [],
      checkAll: false,
      isIndeterminate: false,
      loading: false,
      domain_info: {},
      activeName: 'a',
      total: 0,
      ip_now: [],
      ipUpdateSpecail: [],
      ipUpdateObj: {},
      ipUpdateFlag: false,
      tableData: [],
      timeLineData: [],
      ruleTags: [],
      chain_list: [],
      ruleTitle: [
        {
          id: 1,
          label: '业务层',
          name: 'rule5'
        },
        {
          id: 2,
          label: '支撑层',
          name: 'rule4'
        },
        {
          id: 3,
          label: '服务层',
          name: 'rule3'
        },
        {
          id: 4,
          label: '系统层',
          name: 'rule2'
        },
        {
          id: 5,
          label: '硬件层',
          name: 'rule1'
        },
        {
          id: 6,
          label: '资产导入',
          name: 'rule6'
        }
      ],
      tableHeader: [
        {
          label: '端口',
          name: 'port',
          minWidth: 80
        },
        {
          label: '协议',
          name: 'protocol',
          minWidth: 80
        }
        // {
        //   label: '组件信息',
        //   name: 'rules',
        //   minWidth: 80
        // },
      ],
      threatsTableHeader: [
        {
          label: '漏洞名称',
          name: 'common_title',
          minWidth: 100
        },
        {
          label: '漏洞地址',
          name: 'url',
          minWidth: 100
        },
        {
          label: '风险等级',
          name: 'level',
          minWidth: 50
        },
        {
          label: '漏洞状态',
          name: 'state',
          minWidth: 50
        },
        {
          label: '发现时间',
          name: 'last_update_time',
          minWidth: 100
        }
      ],
      whiosHeader: [
        {
          label: '域名创建时间：',
          name: 'isp',
          icon: 'input',
          detailIs: 1,
          text: ''
        },
        {
          label: '域名更新时间：',
          name: 'isp1',
          icon: 'input',
          detailIs: 1,
          text: ''
        },
        {
          label: '域名注册商：',
          name: 'isp2',
          icon: 'input',
          detailIs: 1,
          text: ''
        },
        {
          label: '联系人：',
          name: 'nameList',
          icon: 'input',
          detailIs: 1,
          text: ''
        },
        {
          label: '注册人手机：',
          name: 'registrant_mobile：',
          icon: 'input',
          detailIs: 1,
          text: ''
        },
        {
          label: '注册人组织：',
          name: 'registrant_org：',
          icon: 'input',
          detailIs: 1,
          text: ''
        }
      ],
      leftMainHeader: [
        {
          label: '地理位置：',
          name: 'geo',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '运营商：',
          name: 'isp',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '经度：',
          name: 'lon',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '纬度：',
          name: 'lat',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '资产标签：',
          name: 'tags',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '更新时间：',
          name: 'updated_at',
          icon: 'input',
          detailIs: 1
        }
      ],
      tabChangeVal: '',
      infoHeader: [
        {
          label: '企业名称：',
          name: 'company_name',
          icon: 'input',
          detailIs: 1
        },
        {
          label: 'A纪录（IPV4）：',
          name: 'dns_a',
          icon: 'input',
          detailIs: 1
        },
        {
          label: 'AAA纪录（IPV6）：',
          name: 'dns_aaaa',
          icon: 'input',
          detailIs: 1
        },
        {
          label: 'CNAME：',
          name: 'cname',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '发现时间：',
          name: 'created_at',
          icon: 'input',
          detailIs: 1
        },
        {
          label: '更新时间：',
          name: 'updated_at',
          icon: 'input',
          detailIs: 1
        }
      ]
    }
  },
  watch: {
    getterCompanyChange(val) {
      this.$router.go(-1) // 其他页面ip详情，安服账号切换企业需要回到一级页面
    },
    getterCurrentCompany() {
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      if (this.user.role == 2) {
        if (!this.currentCompany) return
        this.getTaskResultData()
      } else {
        this.getTaskResultData()
      }
    },
    tabChangeVal(val) {
      if (this.ipUpdateSpecail.length > 0) {
        this.ipUpdateSpecail.map((v) => {
          if (v.ip == val) {
            if (v.ipNow) {
              //实时解析
              this.ipUpdateFlag = false
              this.ipUpdateObj = { ip: val }
            } else {
              this.ipUpdateFlag = true
            }
          }
          this.tableData.map((item) => {
            if (item.ip == val) {
              this.ipUpdateObj = item
              this.ipUpdateFlag = true
            }
          })
        })
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goToIPList() {
      sessionStorage.setItem('menuId', '1-3-1')
      this.changeMenuId('1-3-1')
      this.$router.push({ path: 'assetsLedger', query: { ip: this.ipUpdateObj['ip'] } })
    },
    transfer() {
      return this.getModificationDate()
    },
    async scanIp(scanType) {
      let IPV4Data = []
      let IPV6Data = []
      if (scanType == 'more') {
        //勾选解析
        if (this.checkedArr.length == 0) return this.$message.error('请选择需要扫描的域名')
        for (let i = 0; i < this.checkedArr.length; i++) {
          const element = this.checkedArr[i]
          if (element.ip.indexOf(':') >= 0) {
            IPV6Data.push({ ip: element.ip })
          } else {
            IPV4Data.push({ ip: element.ip })
          }
        }
      } else {
        //单个域名解析
        const { ip } = this.ipUpdateObj
        if (ip.indexOf(':') >= 0) {
          IPV6Data.push({ ip: ip })
        } else {
          IPV4Data.push({ ip: ip })
        }
      }
      const { portGroupIds } = this
      let time = '-' + baseUse.getModificationDate()
      let IPV4Obj = {
        port_group_ids: portGroupIds,
        source: 'input',
        data: IPV4Data,
        operate_company_id: '',
        task_param: {
          name: '资产台账-域名详情IPV4资产执行扫描-IPV4资产扫描任务' + time,
          bandwidth: 1000,
          task_type: 1,
          ip_type: 1,
          protocol_concurrency: 0,
          scan_type: 1,
          type: 6,
          ping_switch: 0,
          web_logo_switch: 0,
          port_group_ids: portGroupIds
        },
        is_define_port: 0,
        define_ports: [],
        define_port_protocols: []
      }
      let IPV6Obj = {
        port_group_ids: portGroupIds,
        source: 'input',
        data: IPV6Data,
        operate_company_id: '',
        task_param: {
          name: '资产台账-域名详情IPV6资产执行扫描-IPV6资产扫描任务' + time,
          bandwidth: 1000,
          task_type: 1,
          ip_type: 2,
          protocol_concurrency: 0,
          scan_type: 0,
          type: 6,
          ping_switch: 0,
          web_logo_switch: 0,
          port_group_ids: portGroupIds
        },
        is_define_port: 0,
        define_ports: [],
        define_port_protocols: []
      }
      let res1 = null
      let res2 = null
      if (IPV4Data.length != 0) {
        res1 = await importSureIpDataV1({
          ...IPV4Obj,
          operate_company_id: this.currentCompany
        }).catch(() => {})
      }
      if (IPV6Data.length != 0) {
        res2 = await importSureIpDataV1({
          ...IPV6Obj,
          operate_company_id: this.currentCompany
        }).catch(() => {})
      }
      if ((!res2 && res1.code == 0) || (res2 && res2.code == 0)) {
        this.scanLoading = true
        // 操作成功 已下发扫描任务 请稍后
        setTimeout(() => {
          this.$message.success('操作成功,已下发扫描任务,请稍后...')
          this.$refs.ipScanProgress.getMergeTakList()
        }, 2000)
      }
    },
    async getMergeTakList() {
      let obj = {
        page: 1,
        per_page: 10,
        type: '',
        task_type: 1, // 任务分类 1 资产扫描 2 漏洞扫描,
        name: '资产台账-域名详情',
        status: '', // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
        created_at_range: [],
        dispatched_at_range: [],
        sortField: 'status',
        sortOrder: 'asc',
        is_schedule: 0,
        op_id: '',
        operate_company_id: this.currentCompany
      }
      let res = await getAllTaskList(obj).catch(() => {
        this.scanLoading = false
      })
      if (res.data) {
        this.tipData = res.data.items && res.data.items.length > 0 ? res.data.items[0] : ''
      }
      this.scanLoading = false
    },
    updateScanStatus() {
      this.scanLoading = false
    },
    handleCheckedIPChange(value) {
      // this.checkedArr = value
      let checkedCount = value.length
      this.checkAll = checkedCount === this.ipUpdateSpecail.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.ipUpdateSpecail.length
    },
    handleCheckAllChange(val) {
      this.checkedArr = val ? this.ipUpdateSpecail : []
      // this.checkedArr = val ? this.ipUpdateSpecail.map((item) => item.ip) : [];
      this.isIndeterminate = false
    },
    async getTaskResultData() {
      let obj = {
        id: this.$route.query.id,
        operate_company_id: this.currentCompany
      }
      this.loading = true
      let res = await domainDetails(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.domain_info = res.data.domain_info
      this.ip_now = res.data.ip_now
      this.timeLineData = res.data.domain_history

      res.data.ip_arr.map((v) => {
        v.tabKey = 'a'
        if (v.port_list) {
          v.port_list.map((item, key) => {
            item.id = key
          })
        } else if (v.chain_list) {
          v.chain_list.forEach((item) => {
            if (item) {
              let arr = []
              item.forEach((ch) => {
                if (ch && ch.content) {
                  arr.push({
                    type: ch.type,
                    content: ch.content
                  })
                }
              })
              v.chain_list.push(arr)
            }
          })
        }
        if (v.ip) {
          if (this.ip_now.length > 0) {
            this.ip_now.map((value) => {
              if (v.ip == value) {
                v.isShow = true
              }
            })
          }
        }
      })
      if (this.ip_now.length > 0) {
        this.ip_now.map((v) => {
          this.ipUpdateSpecail.push({ ip: v, ipNow: true })
        })
      }
      if (res.data.ip_arr.length > 0) {
        res.data.ip_arr.map((v) => {
          if (v.ip) {
            this.ipUpdateSpecail.push({ ip: v.ip, ipNow: false })
          }
        })
      }
      if (this.ipUpdateSpecail.length > 0) {
        this.tabChangeVal = this.ipUpdateSpecail[0].ip
      }
      this.tableData = res.data.ip_arr
      this.portGroupIds = res.data.port_group_ids
      this.domainTime = res.data.domain_time
      let { data } = await domainWhoIsDetails(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.whiosHeader[0].text = data.registration_date
      this.whiosHeader[1].text = data.expiration_date
      this.whiosHeader[2].text = data.sponsoring_registrar
      this.whiosHeader[3].text = data.registrant_name
      this.whiosHeader[4].text = data.registrant_mobile
      this.whiosHeader[5].text = data.registrant_org
    },
    ipClick(id) {
      window.open(`/alreadyTask_viewlist_ipinfo?id=${id}&fromIcon=0`, '_blank') // fromIcon: 默认'0' 已完成任务列查看ip详情，传1
    },
    ipListClick(item) {
      this.tabChangeVal = item.ip
    },
    tabClick(value) {
      console.log(this.tabChangeVal)
    },
    getTableItem(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length > 0) {
            return item.join('，')
          } else {
            return '-'
          }
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    setGeo(data) {
      let str = ''
      str += !data || data.country == null ? '' : data.country
      str += !data || data.province == null ? '' : data.province
      // str+=data.city==null?'':data.city
      return str
    },
    setRuleTags(data) {
      let rule_infos = {
        rule1: [],
        rule2: [],
        rule3: [],
        rule4: [],
        rule5: [],
        rule6: []
      }
      if (data) {
        data.forEach((ru) => {
          if (ru.level == '1') {
            rule_infos.rule1.push(ru.cn_product)
          } else if (ru.level == '2') {
            rule_infos.rule2.push(ru.cn_product)
          } else if (ru.level == '3') {
            rule_infos.rule3.push(ru.cn_product)
          } else if (ru.level == '4') {
            rule_infos.rule4.push(ru.cn_product)
          } else if (ru.level == '5') {
            rule_infos.rule5.push(ru.cn_product)
          } else if (!ru.level) {
            // 资产导入
            rule_infos.rule6.push(ru.cn_product)
          }
        })
      }
      return rule_infos
    },
    tabHover(ip) {
      console.log(ip)
    },
    getTagsClassName(tagsList) {
      let str = ''
      if (tagsList && tagsList.length > 0) {
        tagsList.map((v, i) => {
          if (tagsList.length > 1 && i !== tagsList.length - 1) {
            str += this.getTagsName(v)
          } else {
            str += this.getTagsName(v)
          }
        })
      }
      return str
    },
    getTagsName(tags) {
      let tagname = ''
      if (this.ipUpdateObj['tags'] && this.ipUpdateObj['tags'].length <= 1) {
        switch (Number(tags)) {
          case 0:
            tagname = '用户-扫描'
            break
          case 1:
            tagname = '安服-扫描'
            break
          case 2:
            tagname = '用户-推荐'
            break
          case 3:
            tagname = '安服-推荐'
            break
          case 4:
            tagname = '安服-导入'
            break
        }
      } else {
        if (this.ipUpdateObj['tags']) {
          this.ipUpdateObj['tags'].map((v, i) => {
            if (v == tags) {
              if (i !== this.ipUpdateObj['tags'].length - 1) {
                switch (Number(tags)) {
                  case 0:
                    tagname = '用户-扫描，'
                    break
                  case 1:
                    tagname = '安服-扫描，'
                    break
                  case 2:
                    tagname = '用户-推荐，'
                    break
                  case 3:
                    tagname = '安服-推荐，'
                    break
                  case 4:
                    tagname = '安服-导入'
                    break
                }
              } else {
                switch (Number(tags)) {
                  case 0:
                    tagname = '用户-扫描'
                    break
                  case 1:
                    tagname = '安服-扫描'
                    break
                  case 2:
                    tagname = '用户-推荐'
                    break
                  case 3:
                    tagname = '安服-推荐'
                    break
                  case 4:
                    tagname = '安服-导入'
                    break
                }
              }
            }
          })
        }
      }
      return tagname
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getTaskResultData()
    } else {
      // this.getDetails(this.$route.query.id)
      this.getTaskResultData()
    }
  }
}
</script>
<style lang="less" scoped>
.containerInfo /deep/ {
  position: relative;
  width: 100%;
  height: 100%;
  // background: #fff;
  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left {
      height: 100%;
      display: flex;
      /deep/.el-textarea {
        width: 300px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }
  }
  /deep/.home_header {
    // position: relative;
    height: 100%;
    .el-tabs__nav {
      position: relative;
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      // width: 0 !important;
      padding: 0 16px;
      // padding: 0;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
      background: #fff;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      // padding: 0;
      font-size: 14px;
      font-weight: 400;
      // color: #62666C;
    }
    .mainLeft {
      width: 24%;
      height: 100%;
      padding: 0 12px;
      .leftMain {
        display: flex;
        width: 100%;
      }
      .el-tabs__item {
        padding: 0px 0px !important;
      }
      .ipList {
        width: 100%;
        margin-top: 30px;
        p {
          width: 70%;
          height: 30px;
          margin-bottom: 16px;
          color: #62666c;
          white-space: nowrap; //禁止换行
          overflow: hidden;
          text-overflow: ellipsis;
          span {
            color: #37393c;
          }
        }
      }
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0 16px 15px;
      margin-bottom: 20px;
      & > div {
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrap {
      height: 100%;
      // display: flex;
      // justify-content: space-between;
      .leftTabFromIndex {
        width: 100% !important;
      }
      .leftTab {
        // height: 100%;
        width: 100%;
        height: 44.4%;
        display: flex;
        // flex-direction: column;
        justify-content: space-between;
        .leftTabChange {
          width: 58%;
        }
        .ipInfo {
          width: 100%;
          background: #ffffff linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #f3f7ff 100%);
          box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
          .whiosLi {
            display: flex;
            flex-wrap: wrap;
            .whiosLiLabel {
              width: 33.3%;
              display: flex;
              flex-wrap: nowrap;
              box-sizing: border-box;
              padding-right: 16px;
              margin-bottom: 16px;
              color: #62666c;
              white-space: nowrap;
              span {
                color: #37393c;
              }
            }
            .whiosText {
              width: 60%;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          & > li {
            // height: calc(100% - 80px);
            display: flex;
            flex-wrap: wrap;
            padding: 20px 16px;
            // padding: 10px;
            overflow: auto;
            color: rgba(98, 102, 108, 1);
            font-weight: 400;
            p {
              width: 50%;
              margin-bottom: 16px;
              color: #62666c;
              display: flex;
              justify-content: space-between;
              div {
                color: #37393c;
                // margin-left: 43px;
                color: rgba(55, 57, 60, 1);
                font-weight: 400;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 60%;
              }
            }
          }
        }
        .ipInfoSpecial {
          height: 53.6%;
          border-radius: 4px;
          background:
            linear-gradient(180deg, rgba(255, 255, 255, 0.01) 0%, rgba(240, 246, 255, 1) 100%),
            rgba(255, 255, 255, 1);
          box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
        }
        .ipInfoSpecial1 {
          height: 43%;
          border-radius: 4px;
          background: rgba(255, 255, 255, 1);
          box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
        }
      }
      .infoMain {
        width: 100%;
        height: 88.4%;
        display: flex;
      }
      .infoRight {
        width: 94%;
        height: 100%;
        // background: red;
        padding: 20px 16px 20px 16px;
        box-sizing: border-box;
        .emptyClass {
          height: calc(100% - 30px);
          text-align: center;
          vertical-align: middle;
          svg {
            display: inline-block;
            font-size: 120px;
            margin-top: 70px;
          }
          p {
            line-height: 25px;
            color: #d1d5dd;
            span {
              margin-left: 4px;
              color: #2677ff;
              cursor: pointer;
            }
          }
        }
      }
      .rightTop {
        width: 100%;
        height: 30px;
        box-sizing: border-box;
        padding-left: 16px;
        // display: flex;
        // align-items: center;
        .ipTextBox {
          font-size: 14px;
          font-weight: 500;
          color: rgba(55, 57, 60, 1);
        }
        .text {
          margin-left: 10px;
        }
        .cursor:hover {
          cursor: pointer;
        }
      }
      .rightMain {
        width: 100%;
        height: 90%;
        // background: yellow;
        display: flex;
      }
      .mainDomainRight {
        width: 60%;
        height: 100%;
        // background: yellow;
        padding-left: 16px;
        box-sizing: border-box;
      }
      .mainDomainLeft {
        width: 40%;
        height: 100%;
        padding-right: 16px;
        border-right: 1px solid rgba(233, 235, 239, 1);
        box-sizing: border-box;
        padding: 0px 16px 0px 16px;
        div {
          width: 100%;
          height: 100%;
          border-radius: 4px;
          position: relative;
          background: linear-gradient(
            270deg,
            rgba(38, 119, 255, 0.1) 0%,
            rgba(38, 119, 255, 0.04) 100%
          );
          img {
            position: absolute;
            width: 140px;
            height: 140px;
            right: 0;
            bottom: 0;
          }
        }
        .domainLeftText {
          box-sizing: border-box;
          padding: 24px 16px 0 16px;
          .domainLeftTextMain {
            width: 100%;
            height: 120px;
            display: flex;
            flex-wrap: wrap;
            overflow: auto;
            background: none;
          }
          .pText {
            display: flex;
            justify-content: space-between;
            height: 40px;
            width: 50%;
            box-sizing: border-box;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            background: none !important;
            div {
              background: none !important;
            }
            span {
              color: rgba(98, 102, 108, 1);
              font-weight: 400;
            }
            .spanSpecal {
              color: rgba(55, 57, 60, 1);
            }
            .pTextLabel {
              width: 30%;
              color: rgba(98, 102, 108, 1);
              font-weight: 400;
            }
            .pTextSpecial {
              width: 60%;
              height: 100%;
              // background: yellow;
              color: rgba(55, 57, 60, 1);
              font-size: 14px;
              font-weight: 400;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }
      .infoIpLeft {
        width: 100%;
        // display: flex;
        // align-items: center;
        // flex-direction: column;
        font-size: 16px;
        padding-left: 16px;
        padding-top: 12px;
        box-sizing: border-box;
        div {
          height: 44px;
          width: 100%;
          padding-right: 16px;
          // display: flex;
          // align-items: center;
          line-height: 44px;
          font-weight: 500;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: rgba(98, 102, 108, 1);
          box-sizing: border-box;
          cursor: pointer;
        }
        .infoIpLeftSpecial {
          color: rgba(38, 119, 255, 1);
          font-weight: 500;
          border-right: 2px solid rgba(38, 119, 255, 1);
          box-sizing: border-box;
        }
      }
      .infoLeft {
        width: 180px;
        height: 100%;
        box-sizing: border-box;
        border-right: 1px solid rgba(233, 235, 239, 1);
        overflow-y: auto;
        .el-tabs__nav {
          padding-left: 0;
          padding-top: 14px;
          box-sizing: border-box;
        }
        /deep/.el-tabs__header .is-left {
          width: 115px;
        }
        .el-tabs__active-bar {
          padding: 0 0;
          top: 14px;
          left: 127px;
          background: rgba(38, 119, 255, 1);
        }
        .el-tabs__item .is-top .is-active {
          color: rgba(38, 119, 255, 1) !important;
        }
        .el-tabs__item {
          width: 115px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .rightWrap {
        width: 41.3%;
        height: 100%;
        border-radius: 4px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
        .el-timeline {
          height: calc(100% - 98px);
          margin-left: 20px;
          margin-top: 20px;
          overflow: auto;
        }
        .el-timeline-item__node {
          background: #ffffff;
          border: 2px solid #4285f4;
        }
        .el-timeline-item__node--normal {
          left: 0px;
        }
        .el-timeline-item__tail {
          left: 7px;
          border-left: 2px solid #edf0f5;
        }
        .el-timeline-item__wrapper {
          padding-right: 20px;
        }
        .el-card.is-always-shadow {
          box-shadow: none;
        }
        .el-card {
          margin-top: 10px;
          background: #eff2f7;
        }
        .el-card__body {
          padding: 16px 24px;
          .el-form {
            padding: 0 !important;
          }
          .el-form-item__label {
            text-align: left;
            line-height: 28px;
            height: 28px;
          }
          .el-form-item__content {
            & > ul {
              display: flex;
              flex-wrap: wrap;
              li {
                max-width: 80%;
                height: 28px;
                line-height: 28px;
                padding: 0 10px;
                margin-right: 12px;
                background: #ffffff;
                border-left: 2px solid #1bec9e;
                border-radius: 2px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
    }
    .el-table {
      border: 0;
      margin-top: 20px;
      width: 95% !important;
    }
  }
}
.ipInfo > li > p:last-child {
  margin-bottom: 0px !important;
}
.tiemBox {
  color: #62666c;
  .tiemContent {
    color: #37393c;
    margin-left: 16px;
  }
}
.infoTitle {
  height: 44px;
  border-bottom: 1px solid rgba(233, 235, 239, 1);
  padding-left: 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0px 16px;
  color: rgba(55, 57, 60, 1);
  font-size: 14px;
  font-weight: 500;

  .leftT {
    display: flex;
    align-items: center;
    div {
      margin-right: 10px;
    }
  }
}
.portInfoMess {
  width: 100%;
  // height: calc(100%);
}
.rightWrap {
  width: 49%;
  background: #fff;
  .el-timeline {
    height: calc(100% - 98px);
    margin-left: 20px;
    margin-top: 20px;
    overflow: auto;
  }
  .el-timeline-item__node {
    background: #ffffff;
    border: 2px solid #4285f4;
  }
  .el-timeline-item__node--normal {
    left: 0px;
  }
  .el-timeline-item__tail {
    left: 7px;
    border-left: 2px solid #edf0f5;
  }
  .el-timeline-item__wrapper {
    padding-right: 20px;
  }
  .el-card.is-always-shadow {
    box-shadow: none;
  }
  .el-card {
    margin-top: 10px;
    background: #eff2f7;
  }
  .el-card__body {
    padding: 16px 24px;
    .el-form {
      padding: 0 !important;
    }
    .el-form-item__label {
      text-align: left;
      line-height: 28px;
      height: 28px;
    }
    .el-form-item__content {
      & > ul {
        display: flex;
        flex-wrap: wrap;
        li {
          max-width: 80%;
          height: 28px;
          line-height: 28px;
          padding: 0 10px;
          margin-right: 12px;
          background: #ffffff;
          border-left: 2px solid #1bec9e;
          border-radius: 2px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
.infoNowIp {
  // width: 100%;
  height: 50px;
  box-sizing: border-box;
  margin-top: 10px;
  color: #37393c;
  font-weight: 600;
  line-height: 50px;
  border-bottom: 1px solid #e9ebef;
}
.boxStatus {
  width: 20px;
  height: 20px;
  clip-path: polygon(50% 0, 100% 50%, 50% 100%, 0 50%);
  transition: 1s clip-path;
  position: relative;
}
.boxStatuscolor {
  background: #10d595;
}
.boxStatuscolor1 {
  background: gray;
}
.boxStatus span {
  position: absolute;
  color: #fff;
  left: 40px;
  top: 40px;
}
.ipText {
  margin-left: 5px;
}

.portInfo {
  // margin-top: 12px;
  // flex-grow: 1;
  height: 53.4%;
  background: #fff;
  overflow: auto;
  margin-top: 12px;
  .portMain {
    // background: #FFFFFF linear-gradient(180deg, rgba(255,255,255,0) 0%, #F3F7FF 100%);
    width: 100%;
    // height: 100%;
    margin-top: 2%;
    display: flex;
    box-sizing: border-box;
    // flex-direction: column;
    .mainRight {
      width: 75%;
      //  height: 100%;
      margin-left: 2%;
      padding: 0 12px;
      padding-top: 0;
    }
  }

  .rules {
    display: flex;
    flex-wrap: wrap;
    padding: 0 12px;
    .ruleItemBox {
      display: flex;
      flex-wrap: wrap;
    }
    .ruleItem {
      display: inline-block;
      padding: 2px 10px;
      // margin-right: 8px;
      margin: 5px 8px 5px 0px;
      background: #ffffff;
      border-radius: 12px;
      border: 1px solid #d1d5dd;
      font-size: 12px;
      color: #37393c;
    }
  }
  .bBoxText {
    height: 110% !important;
    padding-left: 0;
    padding-right: 0;
    width: 100%;
  }
  .ruleClass {
    height: 89% !important;
    padding-top: 16px;
    // padding-left: 0;
    // padding-right: 0;
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    justify-content: space-between;
    li {
      position: relative;
      width: 49%;
      // min-height: 60px;
      background: #eff2f7;
      border-radius: 4px;
      margin-bottom: 12px;
      padding-bottom: 12px;
      min-height: 76px;
      box-sizing: border-box;
      //  display: flex;
      // justify-content: space-between;
      .rules {
        color: #2677ff;
      }
      i {
        position: absolute;
        top: 0;
        left: 0px;
        display: block;
        width: 4px;
        height: 100%;
        border-radius: 100px 0px 0px 100px;
      }
      .linerule1 {
        background: #2677ff;
      }
      .linerule2 {
        background: #5192ff;
      }
      .linerule3 {
        background: #7dadff;
      }
      .linerule4 {
        background: #a8c9ff;
      }
      .linerule5 {
        background: #d4e4ff;
      }
      .linerule6 {
        background: rgba(16, 213, 149, 1);
      }
      p {
        margin-bottom: 12px;
        padding-left: 12px;
        span {
          display: block;
          width: 60px;
          border-radius: 0px 0px 4px 4px;
          text-align: center;
          font-size: 14px;
          color: #fff;
          background: #2677ff;
          padding: 2px;
          box-sizing: border-box;
        }
      }
    }
  }
  .chainClass_info {
    height: 89% !important;
    padding: 16px;
    overflow: auto;
    img {
      margin-bottom: 0 !important;
    }
    li {
      padding: 5px 0;
      display: flex;
      .xuhao {
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        border-radius: 50%;
        margin-right: 10px;
        margin-top: 5px;
        color: #fff;
        background: #2677ff;
      }
      .chain {
        display: inline-block;
        padding: 5px 10px;
        margin-bottom: 5px;
        background: #ffffff;
        border-radius: 34px;
        color: #62666c;
        border: 1px solid #dfe4ed;
      }
      i {
        color: #2677ff;
        font-weight: bold;
      }
    }
  }
  td.el-table__expanded-cell {
    padding: 0;
    .banner {
      background: #f5f7fa;
      padding: 20px;
      line-height: 32px;
    }
  }
}
/deep/.el-radio-button__inner {
  height: 32px !important;
  display: flex;
  align-items: center;
  color: rgba(98, 102, 108, 1);
  justify-content: center;
}
/deep/.is-active > span {
  margin-left: 0px !important;
}
/deep/.el-tabs {
  height: 100% !important;
}

/deep/.el-checkbox-group {
  width: 100%;
  height: auto;
}
/deep/.el-checkbox {
  height: 44px;
  display: flex;
  align-items: center;
  .el-checkbox__label {
    // height: 44px !important;
    span {
      height: 100%;
    }
  }
}
.ellipsis {
  width: 130px !important;
  overflow: hidden;
  white-space: nowrap;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
</style>
