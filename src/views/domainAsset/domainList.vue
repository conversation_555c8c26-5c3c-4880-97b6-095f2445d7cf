<template>
  <div class="container">
    <div class="headerTitle" v-if="$route.path != '/index'">
      <span class="goback" v-if="$route.query.open_parse" @click="goBackFromBrief()"
        ><i class="el-icon-arrow-left"></i>返回上一层 / &nbsp;&nbsp;</span
      >
      域名资产
      <span v-if="notifyFilterId">
        / {{ notifyFilterMsg }}
        <span style="color: #2677ff; cursor: pointer" @click="clearNotifyFilter">恢复默认</span>
      </span>
    </div>
    <div class="home_header">
      <div class="progress" v-if="$route.path != '/index'">
        <IpScanProgress
          typeName="资产台账-域名资产"
          @updateList="getData"
          ref="ipScanProgress"
          @updateScanStatus="updateScanStatus"
          v-loading="scanLoading"
        />
      </div>
      <div class="filterTab" v-if="$route.path != '/index'">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
            id="keyword_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            <el-tooltip
              slot="prepend"
              class="item"
              effect="dark"
              content="支持检索字段：域名、企业名称"
              placement="top"
              :open-delay="100"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-input>
          <span @click="highCheckdialog = true" id="keyword_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="keyword_all"
            >选择全部</el-checkbox
          >
          <template v-if="user.role == 2">
            <el-tooltip
              class="item"
              effect="dark"
              content="针对所有的资产，去FOFA更新资产信息，更新后资产信息会更准确。 "
              placement="top"
            >
              <el-button class="normalBtn" type="primary" @click="updateAssetsInfoByFOFA"
                >去FOFA更新</el-button
              >
            </el-tooltip>
          </template>
          <el-tooltip
            class="item"
            effect="dark"
            content="扫描域名后解析对应IP并入账至IP台账"
            placement="top"
          >
            <el-button class="normalBtnRe" type="primary" @click="scanIp('more')"
              >扫描入账IP资产</el-button
            >
          </el-tooltip>
          <el-dropdown class="dropdownClass" trigger="click">
            <el-button class="normalBtn" type="primary" id="account_deal" style="margin-right: 10px"
              >设置更新策略<i class="el-icon-arrow-down el-icon--right"></i
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="updateCycle(true)">立即更新</el-dropdown-item>
              <el-dropdown-item @click.native="updateCycle(false)">周期更新</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <!-- <el-button
            class="normalBtnRe"
            type="primary"
            @click="importTz"
            id="keyword_more_disable"
            >导入</el-button
          > -->
          <el-dropdown class="dropdownClass" trigger="click">
            <el-button
              class="normalBtn"
              type="primary"
              id="keyword_more_disable"
              style="margin-right: 10px; margin-left: 0"
              >导入<i class="el-icon-arrow-down el-icon--right"></i
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="addDialogVisible = true">单个导入</el-dropdown-item>
              <el-dropdown-item @click.native="importTz">批量文件导入</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="exportList"
            :loading="exportLoadingBtn"
            :disabled="exportLoadingBtn"
            id="keyword_more_open"
            >导出</el-button
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="remove('more')"
            id="keyword_more_del"
            >删除</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 default-expand-all -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="keyword1"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          lazy
          :load="tableLoad"
          row-key="uniqud"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @select-all="selectAll"
          @select="selectOne"
          ref="eltable"
          height="100%"
          :tree-props="{ children: 'children', hasChildren: 'has_next' }"
          style="width: 100%"
          @expand-change="expandRow"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            v-if="!($route.path == '/index')"
            type="selection"
            align="center"
            :reserve-selection="checkedAll"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.uniqud"
            :prop="item.name"
            align="left"
            :min-width="item.name == 'domain' ? 280 : 120"
            :label="item.label"
          >
            <template slot="header">
              <span v-if="item.name == 'source'">
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="bottom"
                  popper-class="chainClass"
                  :open-delay="500"
                >
                  <div slot="content" class="userInfo">
                    <p>域名提取：指在IP资产台账中提取的域名数据;</p>
                    <p> 资产推荐：来源于单位资产测绘/云端推荐任务中涉及到的域名数据; </p>
                    <p>域名扫描：添加到系统中执行扫描任务的域名数据;</p>
                    <p>域名导入：在域名资产模块导入的数据;</p>
                    <p>域名枚举：域名发现任务中执行域名枚举任务的数据;</p>
                    <p>域名验证：域名发现任务中执行域名验证任务的数据;</p>
                  </div>
                  <span>
                    {{ item.label }}
                    <i class="el-icon-info"></i>
                  </span>
                </el-tooltip>
              </span>
              <span v-else>{{ item.label }}</span>
            </template>
            <template slot-scope="scope">
              <span v-if="item.name == 'domain' && scope.row[item.name]">
                <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                  <span slot="content">{{ scope.row[item.name] }} 点击可查看详情</span>
                  <span class="domainTextClass" @click="goDeatial(scope.row['id'])">{{
                    scope.row[item.name]
                  }}</span>
                  &nbsp;
                </el-tooltip>
                <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                  <span slot="content">点击前往本网址</span>
                  <a
                    style="margin-left: 5px"
                    :href="goToTarget(scope.row[item.name])"
                    target="_blank"
                  >
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#icon-lianjie"></use>
                    </svg>
                  </a>
                  <!-- </span> -->
                </el-tooltip>
                <span class="greenLine" v-if="scope.row['status'] == 1">可解析</span>
                <span class="redLine" v-else-if="scope.row['status'] == 0">不可解析</span>
                <span class="grayLine" v-else-if="scope.row['status'] == 2">待解析</span>
                <span class="blueLine" v-if="scope.row['open_parse'] == 1">泛解析</span>
              </span>
              <span v-else-if="item.name == 'status_code'">
                <span v-if="scope.row[item.name]">
                  <i class="greenStatus" v-if="scope.row[item.name] == 200"></i>
                  <i class="redStatus" v-else></i>
                  {{ scope.row[item.name] }}
                </span>
                <span v-else>{{ '-' }}</span>
              </span>
              <span v-else-if="item.name == 'status'">{{ getStatus(scope.row[item.name]) }}</span>
              <span v-else-if="item.name == 'type'">
                <div class="myruleItemBox" style="margin-top: 0">
                  <span class="blueRadiusBorder" v-if="scope.row[item.name] == 1">主域名</span>
                  <span class="greenRadiusBorder" v-if="scope.row[item.name] == 0">子域名</span>
                </div>
              </span>
              <span v-else-if="item.name == 'source'">
                <div class="myruleItemBox">
                  <span
                    :class="'myruleItem ' + v.class"
                    v-for="(v, i) in getSource(scope.row[item.name])"
                    :key="i"
                    >{{ v.name }}</span
                  >
                </div>
              </span>
              <span
                v-else-if="item.name == 'title'"
                class="clickPointer"
                @click="noteBlackList(scope.row.title)"
              >
                {{ scope.row.title || '-' }}
              </span>
              <span v-else>
                <el-tooltip
                  v-if="scope.row[item.name]"
                  class="item"
                  effect="dark"
                  placement="top"
                  :open-delay="500"
                >
                  <span slot="content">{{ scope.row[item.name] }}</span>
                  <span>{{ scope.row[item.name] }}</span>
                </el-tooltip>
                <span v-else>-</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="left"
            width="100"
            v-if="$route.path != '/index'"
          >
            <template slot-scope="scope">
              <span>
                <el-button
                  type="text"
                  size="small"
                  @click="remove('one', scope.row.domain)"
                  id="scan_del"
                  >删除</el-button
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-if="$route.path != '/index'"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="100px">
          <el-form-item label="域名：" prop="domain">
            <el-select
              filterable
              v-model="formInline.domain"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'domain', condition.domain, false, true)"
            >
              <el-option
                v-for="item in condition.domain"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="top_domain">
            <template #label>
              <el-tooltip
                class="item"
                effect="dark"
                content="将筛选出以筛选项为顶级域名的所有域名"
                placement="top"
              >
                <span>顶级域名<i class="el-icon-question"></i>：</span>
              </el-tooltip>
            </template>
            <el-select
              filterable
              v-model="formInline.top_domain"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'top_domain', condition.top_domain, false, true)"
            >
              <el-option
                v-for="item in condition.top_domain"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="f_domain">
            <template #label>
              <el-tooltip
                class="item"
                effect="dark"
                content="将筛选出以筛选项为父级域名的所有域名"
                placement="top"
              >
                <span>父级域名<i class="el-icon-question"></i>：</span>
              </el-tooltip>
            </template>
            <el-select
              filterable
              v-model="formInline.f_domain"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'f_domain', condition.f_domain, false, true)"
            >
              <el-option
                v-for="item in condition.f_domain"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网站标题：" prop="title">
            <el-select
              filterable
              v-model="formInline.title"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'title', condition.title, false, true)"
            >
              <el-option
                v-for="item in condition.title"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态码：" prop="status_code">
            <el-select
              filterable
              v-model="formInline.status_code"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'status_code', condition.status_code, false, true)"
            >
              <el-option
                v-for="item in condition.status_code"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="泛解析：" prop="open_parse">
            <el-select
              filterable
              v-model="formInline.open_parse"
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'open_parse', openParseArr, true, false)"
            >
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="企业名称：" prop="company_name">
            <el-select
              filterable
              v-model="formInline.company_name"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'company_name', condition.company_list, false, true)"
            >
              <el-option
                v-for="item in condition.company_list"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="域名类型：" prop="type">
            <el-select
              filterable
              v-model="formInline.type"
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'type', statusArr, true, false)"
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="域名状态：" prop="status">
            <el-select
              filterable
              v-model="formInline.status"
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'status', domainStatusArr, true, false)"
            >
              <el-option
                v-for="item in domainStatusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资产来源：" prop="source">
            <el-select
              filterable
              v-model="formInline.source"
              placeholder="请选择"
              multiple
              collapse-tags
              clearable
              @change="selectChange($event, 'source', typeArr, true, true)"
            >
              <el-option
                v-for="item in typeArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="更新时间：" prop="updated_at_range">
            <el-date-picker
              v-model="formInline.updated_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisib"
      width="400px"
    >
      <template slot="title">
        <span>批量文件导入</span>
      </template>
      <div class="dialog-body">
        <p class="downloadClass" @click="downloadForbidIpsHd()">
          <i class="el-icon-warning"></i>请点击下载
          <span>域名导入模板.xlsx</span>
        </p>
        <el-upload
          class="upload-demo"
          drag
          :action="uploadActionHd"
          :headers="uploadHeaders"
          accept=".xlsx,.csv"
          :before-upload="beforeIpUpload"
          :on-success="ipUploadSuccessHd"
          :on-remove="uploadRemoveHd"
          :on-error="ipUploadErrorHd"
          :limit="uploadMaxCount"
          :on-exceed="handleExceedHd"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip"> 支持上传xlsx 文件，且大小不超过20M </div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisib = false" id="account_check_cancel"
          >关闭</el-button
        >
        <el-button :loading="btnLoading" class="highBtn" @click="domainSave" id="account_check_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 周期更新弹窗 -->
    <el-dialog
      title="设置周期更新策略"
      class="elDialogCycleUpdate"
      :close-on-click-modal="false"
      :visible.sync="cycleUpdateDialog"
      width="400px"
      @close="closeDialog"
    >
      <el-form ref="cycleForm" :rules="rulesCycle" :model="cycleForm" label-width="135px">
        <el-form-item label="是否开启周期更新" prop="cron_status">
          <el-switch
            v-model="cycleForm.cron_status"
            :active-value="1"
            :inactive-value="2"
          ></el-switch>
        </el-form-item>
        <el-form-item label="设置周期为" prop="ex">
          <el-select :disabled="cycleBtnLoading" v-model="cycleForm.ex" placeholder="请选择周期">
            <el-option label="30天" :value="30"></el-option>
            <el-option label="60天" :value="60"></el-option>
            <el-option label="90天" :value="90"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="cycleUpdateDialog = false">关闭</el-button>
        <el-button
          :loading="cycleBtnLoading"
          class="highBtn"
          @click="cycleSave(false, 'cycleBtnLoading', 'cycleUpdateDialog')"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="设置立即更新策略"
      class="elDialogCycleUpdate"
      :close-on-click-modal="false"
      :visible.sync="immediateUpdateDialog"
      @close="closeImmediateDialog"
      width="400px"
    >
      <div v-if="!isImmediateFinish">确定对当前所选 设置立即更新策略吗</div>
      <div v-else class="result pointer" @click="goDetail('immediateUpdateDialog')">
        成功下发域名更新任务，<span>去查看</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="immediateUpdateDialog = false">关闭</el-button>
        <el-button
          v-if="!isImmediateFinish"
          :loading="immediateBtnLoading"
          class="highBtn"
          @click="cycleSave(true, 'immediateBtnLoading', 'immediateUpdateDialog')"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <prompt
      title="单个导入"
      placeholder="请输入需要导入的域名,支持输入多个,分号或换行分隔"
      :visible="addDialogVisible"
      @save="handleImportTz"
      @close="addDialogVisible = false"
      :loading="addDialogBtnLoading"
      inputType="textarea"
      label="域名"
      :rows="6"
    />
    <noteBlack
      :selectTitle="currentTitle"
      :visible="noteDialogVisible"
      ref=""
      @close="noteDialogVisible = false"
    />
  </div>
</template>

<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import hightFilter from '../../components/assets/highTab.vue'
import IpScanProgress from '@/components/ipScanProgress'
import prompt from '@/components/assets/prompt'
import noteBlack from '@/components/assets/noteBlack.vue'
import {
  addSensitiveKeyword,
  updateTypeSensitiveKeyword,
  updateDomainAssetsRow,
  updateDomainAssetsByFOFA
} from '@/api/apiConfig/api.js'
import {
  domainAssetsCron,
  domainAssetsReparse,
  domainAssetsScan,
  domainAssetDerive,
  domainAssetUpload,
  domainAssetDel,
  domainNextLevelList,
  domainAssetList
} from '@/api/apiConfig/domain.js'

export default {
  components: {
    hightFilter,
    IpScanProgress,
    prompt,
    noteBlack
  },
  data() {
    return {
      exportLoadingBtn: false,
      notifyFilterMsg: '',
      notifyFilterId: '',
      currentTitle: '',
      noteDialogVisible: false,
      addDialogVisible: false,
      addDialogBtnLoading: false,
      scanLoading: false,
      isImmediateFinish: false,
      isCycleFinish: false,
      rulesCycle: {
        cron_status: [{ required: true, message: '请填写', trigger: 'blur' }],
        ex: [{ required: true, message: '请填写', trigger: 'blur' }]
      },
      cycleForm: {
        ex: '',
        cron_status: ''
      },
      cycleBtnLoading: false,
      cycleUpdateDialog: false,
      immediateUpdateDialog: false,
      immediateBtnLoading: false,
      highTabShow: [
        {
          label: '域名',
          name: 'domain',
          type: 'select'
        },
        {
          label: '顶级域名',
          name: 'top_domain',
          type: 'select'
        },
        {
          label: '父级域名',
          name: 'f_domain',
          type: 'select'
        },
        {
          label: '网站标题',
          name: 'title',
          type: 'select'
        },
        {
          label: '状态码',
          name: 'status_code',
          type: 'select'
        },
        {
          label: '泛解析',
          name: 'open_parse',
          type: 'select'
        },
        {
          label: '企业名称',
          name: 'company_name',
          type: 'select'
        },
        {
          label: '域名类型',
          name: 'type',
          type: 'select'
        },
        {
          label: '域名状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '资产来源',
          name: 'source',
          type: 'select'
        },
        {
          label: '发现时间',
          name: 'created_at_range',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at_range',
          type: 'date'
        }
      ],
      condition: {},
      updateType: 0,
      btnLoading: false,
      updateTypeDialog: false,
      regUrlArr: [],
      highlist: null,
      formInline: {
        company_name: [],
        page: 1,
        per_page: 10,
        source: [],
        open_parse: '',
        keyword: '',
        domain: [],
        type: '',
        status: '',
        created_at_range: [],
        updated_at_range: [],
        operate_company_id: '',
        title: [],
        status_code: []
        // domain_arr:[]
      },
      openParseArr: [
        {
          name: '是',
          id: 1
        },
        {
          name: '否',
          id: 0
        }
      ],
      checkedAll: false,
      highCheckdialog: false,
      tableHeader: [
        {
          label: '域名',
          name: 'domain'
        },
        {
          label: 'ICP备案号',
          name: 'icp'
        },
        {
          label: 'ICP备案企业名称',
          name: 'company_name'
        },
        {
          label: '网站标题',
          name: 'title'
        },
        {
          label: '状态码',
          name: 'status_code'
        },
        {
          label: 'A记录（IPv4）',
          name: 'dns_a'
        },
        {
          label: 'AAAA记录（IPv6）',
          name: 'dns_aaaa'
        },

        {
          label: 'CNAME',
          name: 'cname'
        },
        {
          label: '域名类型',
          name: 'type'
        },
        {
          label: '资产来源',
          name: 'source'
        },
        {
          label: '发现时间',
          name: 'created_at'
        },
        {
          label: '更新时间',
          name: 'updated_at'
        }
      ],
      typeArr: [
        // 1域名提取、2资产推荐、3域名枚举、4域名导入、5域名验证、6域名扫描
        {
          name: '域名提取',
          id: 1
        },
        {
          name: '资产推荐',
          id: 2
        },
        {
          name: '域名枚举',
          id: 3
        },
        {
          name: '域名导入',
          id: 4
        },
        {
          name: '域名验证',
          id: 5
        },
        {
          name: '域名扫描',
          id: 6
        }
      ],
      statusArr: [
        // 状态 0/1 禁用/启用
        {
          name: '子域名',
          id: 0
        },
        {
          name: '主域名',
          id: 1
        }
      ],
      domainStatusArr: [
        {
          name: '不可解析',
          id: 0
        },
        {
          name: '可解析',
          id: 1
        }
      ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      uploadActionHd: `${this.uploadSrcIp}/domain_assets/upload`,
      uploadMaxCount: 1,
      pageSize: 10,
      uploadAction: '',
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      dialogFormVisib: false,
      dialogFormVisibleInsert: false,
      ruleForm: {
        way: 0,
        type: 0,
        name: ''
      },
      rules: {
        name: [{ required: true, message: ' ', trigger: 'blur' }],
        type: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      checkedKeys: false,
      user: {
        role: ''
      },
      userInfo: {},
      cycleForm: {}
    }
  },
  mounted() {
    this.notifyFilterId = this.$route.query.notifyFilterId
    this.notifyFilterMsg = this.$route.query.notifyFilterMsg
    this.formInline.website_message_id = this.notifyFilterId || ''
    if (sessionStorage.getItem('userMessage')) {
      this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (this.userInfo) {
        this.user = this.userInfo.user
      }
    }
    if (this.$route.query.open_parse) {
      this.formInline.open_parse = 1
      this.selectChange(1, 'open_parse', this.openParseArr, true, false)
      this.highlist = Object.assign({}, this.formInline)
      this.hightFilterIsShow()
    }
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getData()
    } else {
      this.getData()
    }
  },
  watch: {
    getterCompanyChange(val) {
      if (this.notifyFilterId) {
        this.clearNotifyFilter()
      }
    },
    getterCurrentCompany(val) {
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    updateAssetsInfoByFOFA() {
      updateDomainAssetsByFOFA({
        operate_company_id: this.currentCompany
      }).then((res) => {
        if (res.code == 0) {
          this.$message({
            type: 'success',
            message: '已成功下发fofa更新资产信息任务!'
          })
        }
      })
    },
    goBackFromBrief() {
      sessionStorage.setItem('menuId', '1-1')
      this.changeMenuId('1-1')
      this.$router.go(-1)
    },
    clearNotifyFilter() {
      this.$router.replace({ path: '/domainAsset', query: {} })
    },
    async handleImportTz(val) {
      if (!val) {
        this.$message.error('请输入需要导入的域名')
        return
      }
      this.addDialogBtnLoading = true
      let domain_list = val
        ? val
            .split(/[；|;|\r\n]/)
            .filter((item) => {
              return item.trim()
            })
            .map((item) => {
              return item.trim()
            })
        : []
      let res = await domainAssetUpload({
        operate_company_id: this.currentCompany,
        domain_list
      }).catch(() => {
        this.addDialogBtnLoading = false
      })
      if (res.code == 0) {
        let obj = {
          ...this.formInline,
          operate_company_id: this.currentCompany
        }
        obj.domain_arr = []
        await updateDomainAssetsRow(obj)
        this.getData()
        this.addDialogVisible = false
        this.addDialogBtnLoading = false
        this.$message({
          type: 'success',
          message: '导入成功'
        })
      }
    },
    expandRow(row, expanded) {
      row.isExpand = expanded
      if (row.isExpand) {
        // 展开默认不勾选
        this.setChildren(row.children, false)
      }
    },
    noteBlackList(title) {
      this.noteDialogVisible = true
      this.currentTitle = title
    },
    goToTarget(url) {
      return 'https://' + url
    },
    goDetail(dialog) {
      this[dialog] = false
      this.$router.push('/domainTask')
    },
    closeImmediateDialog() {
      this.isImmediateFinish = false
    },
    cycleSave(isImmediateUpdate, btnLoading, dialog) {
      if (isImmediateUpdate) {
        this[btnLoading] = true
        let obj = this.setUpdateRule(isImmediateUpdate)
        domainAssetsReparse(obj)
          .then((res) => {
            if (res.code == 0) {
              this[btnLoading] = false
              this.isImmediateFinish = true
            } else {
              this[btnLoading] = false
            }
          })
          .catch(() => {
            this[btnLoading] = false
          })
      } else {
        this.$refs.cycleForm.validate(async (valid) => {
          if (valid) {
            this[btnLoading] = true
            let obj = this.setUpdateRule(isImmediateUpdate)
            domainAssetsReparse(obj)
              .then((res) => {
                if (res.code == 0) {
                  this[btnLoading] = false
                  this[dialog] = false
                  this.$message.success('成功设置周期更新策略，域名将在指定周期更新')
                } else {
                  this[btnLoading] = false
                }
              })
              .catch(() => {
                this[btnLoading] = false
              })
          }
        })
      }
    },
    setUpdateRule(isImmediateUpdate) {
      let obj = {
        ...this.formInline,
        operate_company_id: this.currentCompany,
        update: isImmediateUpdate,
        ...this.cycleForm
      }
      obj.domain_arr = this.checkedAll
        ? []
        : this.checkedArr.map((item) => {
            return item.domain
          })
      obj.ids = this.checkedAll
        ? []
        : this.checkedArr.map((item) => {
            return item.id
          })
      return obj
    },
    closeDialog() {
      this.$refs.cycleForm.resetFields()
      this.cycleForm = {
        ex: '',
        cron_status: ''
      }
    },
    // 更新周期
    updateCycle(isImmediateUpdate) {
      if (!isImmediateUpdate) {
        //周期更新
        this.getCycle()
      } else {
        // 立即更新
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
        this.immediateUpdateDialog = true
      }
    },
    async getCycle() {
      let res = await domainAssetsCron({
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        let data = JSON.parse(JSON.stringify(res.data))
        const { ex, status = 2 } = data
        this.cycleForm = {
          ex,
          cron_status: status
        }
        this.cycleUpdateDialog = true
      }
    },
    async scanIp() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      // this.formInline.last_update_time = this.formInline.last_update_time ? this.formInline.last_update_time : []
      let obj = {
        ...this.formInline,
        operate_company_id: this.currentCompany
      }
      obj.domain_arr = this.checkedAll
        ? []
        : this.checkedArr.map((item) => {
            return item.domain
          })
      let res = await domainAssetsScan(obj)
      this.getData()
      if (res && res.code == 0) {
        this.scanLoading = true
        setTimeout(() => {
          this.$refs.ipScanProgress.getMergeTakList()
          // 操作成功 已下发扫描任务 请稍后
          this.$message.success('操作成功,已下发扫描任务,请稍后...')
          this.checkedAll = false
          this.checkAllChange()
        }, 2000)
      }
    },
    updateScanStatus() {
      this.scanLoading = false
    },
    // 跳转到详情
    goDeatial(id) {
      // window.location.href = `/domainDetails?id=${id}`
      window.open(`/domainDetails?id=${id}`, '_blank')
    },
    getType(type) {
      let str = ''
      switch (type) {
        case 0:
          str = '子域名'
          break
        case 1:
          str = '主域名'
          break
        default:
      }
      return str
    },
    async importTz() {
      this.fileList = []
      this.dialogFormVisib = true
    },
    beforeIpUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过 20MB!')
      }
      return isLt1M
    },
    downloadForbidIpsHd() {
      window.location.href = '/downloadTemplate/域名导入模板.xlsx'
    },
    ipUploadSuccessHd(response, file, fileList) {
      if (file.response.code == 0) {
        this.regUrlArr = []
        this.$message.success('上传成功')
        this.regUrlArr = response.data
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadRemoveHd(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.regUrlArr = []
      } else {
        this.regUrlArr = res[0]
      }
    },
    ipUploadErrorHd(err, file, fileList) {
      this.$message.error(JSON.parse(err.message).message)
    },
    handleExceedHd() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    async domainSave() {
      this.btnLoading = true
      let obj = {
        domain_list: this.regUrlArr,
        operate_company_id: this.currentCompany
      }
      let res = await domainAssetUpload(obj).catch(() => {
        this.btnLoading = false
      })
      let updateObj = {
        ...this.formInline,
        operate_company_id: this.currentCompany
      }
      obj.domain_arr = []
      await updateDomainAssetsRow(updateObj)
      this.dialogFormVisib = false
      setTimeout(() => {
        this.btnLoading = false
        this.getData() // is_sleep：1代表是台账页面导入或删除后请求列表接口，后端需要延时
      }, 2000)
    },
    getSource(source) {
      // 1域名提取、2资产推荐、3域名枚举、4域名导入、5域名验证、6域名扫描
      let arrTmp = source.split('')
      let brrTmp = []
      arrTmp.map((v, i) => {
        if (v == '1') {
          brrTmp.push({ name: '域名提取', class: 'source1' })
        } else if (v == '2') {
          brrTmp.push({ name: '资产推荐', class: 'source2' })
        } else if (v == '3') {
          brrTmp.push({ name: '域名枚举', class: 'source3' })
        } else if (v == '4') {
          brrTmp.push({ name: '域名导入', class: 'source4' })
        } else if (v == '5') {
          brrTmp.push({ name: '域名验证', class: 'source5' })
        } else if (v == '6') {
          brrTmp.push({ name: '域名扫描', class: 'source6' })
        }
      })
      return brrTmp
    },
    getStatus(status) {
      let str = ''
      switch (status) {
        case 0:
          str = '禁用'
          break
        case 1:
          str = '启用'
          break
        default:
      }
      return str
    },
    // selectAll(selection, select) {
    //   this.checkedKeys = !this.checkedKeys;
    //   this.selectFun(this.tableData, this.checkedKeys);
    // },
    // selectFun(data, flag) {
    //   data.forEach((row) => {
    //     this.$refs.eltable.toggleRowSelection(row, flag);
    //     if (row.children != undefined) {
    //       this.selectFun(row.children, flag);
    //     }
    //   });
    // },
    selectAll(selection, select) {
      // tabledata第一层只要有在selection里面就是全选
      const isSelect = selection.some((el) => {
        const tableDataIds = this.tableData.map((j) => j.uniqud)
        return tableDataIds.includes(el.uniqud)
      })
      // tableDate第一层只要有不在selection里面就是全不选
      const isCancel = !this.tableData.every((el) => {
        const selectIds = selection.map((j) => j.uniqud)
        return selectIds.includes(el.uniqud)
      })
      if (isSelect) {
        selection.map((el) => {
          if (el.children) {
            // 解决子组件没有被勾选到
            this.setChildren(el.children, true)
          }
        })
      }
      if (isCancel) {
        this.tableData.map((el) => {
          if (el.children) {
            // 解决子组件没有被勾选到
            this.setChildren(el.children, false)
          }
        })
      }
    },
    selectOne(selection, row) {
      const hasSelect = selection.some((el) => {
        return row.uniqud === el.uniqud
      })
      if (hasSelect) {
        if (row.children && row.isExpand) {
          // 解决子组件没有被勾选到
          this.setChildren(row.children, true)
        } else {
          this.toggleSelection(row, true)
        }
      } else {
        if (row.children && row.isExpand) {
          this.setChildren(row.children, false)
        } else {
          this.toggleSelection(row, false)
        }
      }
    },
    setChildren(children, type) {
      // 编辑多个子层级
      children.map((j) => {
        this.toggleSelection(j, type)
        if (j.children) {
          this.setChildren(j.children, type)
        }
      })
    },
    toggleSelection(row, select) {
      if (row) {
        this.$nextTick(() => {
          this.$refs.eltable && this.$refs.eltable.toggleRowSelection(row, select)
        })
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.checkedKeys = !this.checkedKeys
      this.currentPage = val
      this.getData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else if (this.$route.path == '/index') {
        bol = 'tableIndex tableWrap'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    uploadSucc(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.ruleForm.name = response.data
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadErr(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    uploadRemove(file, fileList) {},
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过20MB!')
      }
      return isLt1M
    },
    getData() {
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      let obj = {
        page: this.formInline.page,
        per_page: this.formInline.per_page,
        keyword: this.formInline.keyword,
        company_name: this.formInline.company_name,
        source: this.formInline.source,
        domain: this.formInline.domain,
        top_domain: this.formInline.top_domain,
        f_domain: this.formInline.f_domain,
        type: this.formInline.type,
        open_parse: this.formInline.open_parse,
        created_at_range: this.formInline.created_at_range,
        updated_at_range: this.formInline.updated_at_range,
        operate_company_id: this.currentCompany,
        title: this.formInline.title,
        status_code: this.formInline.status_code,
        status: this.formInline.status,
        website_message_id: this.notifyFilterId || ''
      }
      domainAssetList(obj)
        .then((res) => {
          this.loading = false
          // 暂时每个都可以展开
          res.data.items.map((v) => {
            v.isChecked = false
            if (v.has_next == 1) {
              v.has_next = true
            } else {
              v.has_next = false
            }
          })
          this.tableData = res.data.items
          this.total = res.data.total
          this.condition = res.data.condition // 高级筛选条件
          this.condition.f_domain = Object.values(res.data.condition.f_domain)
          this.doLayout(this, 'eltable')
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
          if ((!this.total || this.total == 0) && this.notifyFilterId) {
            this.$message.error('该批新增资产已被删除')
          }
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    // 拓展二级三级域名
    tableLoad(tree, treeNode, resolve) {
      let obj = {
        id: tree.id,
        operate_company_id: this.currentCompany
      }
      domainNextLevelList(obj).then(async (res) => {
        this.loading = false
        // 暂时每个都可以展开
        res.data.map((v) => {
          v.isChecked = false
          if (v.has_next == 1) {
            v.has_next = true
          } else {
            v.has_next = false
          }
        })
        // 单页全选后展示子级，需要加入选中数组
        if (this.checkedKeys) {
          this.checkedArr = [...new Set(this.checkedArr.concat(res.data))]
        }
        let selection = this.$refs.eltable.store.states.selection
        await setTimeout(() => {
          resolve(res.data)
        }, 1000)
        await this.setNewRowData(tree, res.data)
      })
    },
    setNewRowData(row, loadData) {
      row.children = loadData
    },
    async exportList() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      let obj = {
        ...this.formInline,
        operate_company_id: this.currentCompany
      }
      obj.domain_arr = this.checkedAll
        ? []
        : this.checkedArr.map((item) => {
            return item.domain
          })
      try {
        this.exportLoadingBtn = true
        let res = await domainAssetDerive(obj)
        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.url)
          this.exportLoadingBtn = false
          this.checkedAll = false
          this.$refs.eltable.clearSelection()
        }
      } catch (error) {
        this.exportLoadingBtn = false
      }
    },
    updateTypeClick(row) {
      this.updateTypeDialog = true
      this.updateType = row.type
      this.formInline.id = row.id
    },
    async updateTypeFun(row) {
      let obj = {
        id: this.formInline.id,
        data: {
          type: this.updateType,
          operate_company_id: this.currentCompany
        }
      }
      let res = await updateTypeSensitiveKeyword(obj)
      this.updateTypeDialog = false
      this.$message.success('操作成功！')
      this.getData()
    },
    async remove(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'keyword_del_cancel',
        confirmButtonClass: 'keyword_del_sure',
        customClass: 'keyword_del',
        type: 'warning'
      })
        .then(async () => {
          this.formInline.page = this.currentPage
          this.formInline.per_page = this.pageSize
          let domain_arr = []
          if (this.checkedAll) {
            domain_arr = []
          } else {
            domain_arr =
              icon == 'one'
                ? [id]
                : this.checkedArr.map((item) => {
                    return item.domain
                  })
          }
          let obj = Object.assign({}, this.formInline)
          obj.domain_arr = domain_arr
          let res = await domainAssetDel(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.checkedAll = false
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.$refs.eltable.clearSelection()
            this.getData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.keyword_del>.el-message-box__btns')
        del.children[0].id = 'keyword_del_cancel'
        del.children[1].id = 'keyword_del_sure'
      }, 50)
    },
    wayChange(val) {
      this.ruleForm.name = ''
    },
    downloadcluesExcel() {
      window.location.href = '/downloadTemplate/关键词管理模板.xlsx'
    },
    async insertSave(formName) {
      if (!this.ruleForm.name) {
        this.$message.error('关键词为必填')
        return
      }
      let obj = {
        type: this.ruleForm.type,
        name: this.ruleForm.name, // .split(/,|，|\s+/).filter(item => {return item})
        operate_company_id: this.currentCompany
      }
      this.otherLoading = true
      let res = await addSensitiveKeyword(obj).catch(() => {
        this.otherLoading = false
      })
      this.otherLoading = false
      this.dialogFormVisibleInsert = false
      if (res.code == 0) {
        if (res.data) {
          this.$message({
            duration: 5000,
            type: 'success',
            showClose: true,
            message: `${
              res.data.same_data > 0 ? `${res.data.same_data}个关键词已存在` : ''
            }${res.data.same_data > 0 && res.data.extra_data > 0 ? '；' : ''}${
              res.data.extra_data > 0 ? `成功更新${res.data.extra_data}个关键词类型` : ''
            }${
              (res.data.same_data > 0 || res.data.extra_data > 0) && res.data.count > 0 ? '；' : ''
            }${res.data.count > 0 ? `成功新增${res.data.count}个` : ''}`
          })
        }
        this.getData()
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        company_name: [],
        page: 1,
        per_page: 10,
        source: [],
        open_parse: '',
        keyword: '',
        domain: [],
        type: '',
        status: '',
        created_at_range: [],
        updated_at_range: [],
        operate_company_id: '',
        title: [],
        status_code: []
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    addEdit(row) {
      this.dialogFormVisibleInsert = true
      this.uploadAction = `${this.uploadSrcIp}/keyword/upload`
      this.ruleForm = {
        way: 0,
        type: 0,
        name: ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.pointer {
  cursor: pointer;
}
.result {
  color: green;
}
.container {
  position: relative;
  width: 100%;
  height: 100% !important;
  background: #fff;

  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  .progress {
    margin-right: 20px;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  /deep/.home_header {
    position: relative;
    height: 100%;

    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 221px) !important;
    }
    .tableIndex {
      height: 100% !important;
    }
    .tableWrap {
      height: calc(100% - 176px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
      .domainTextClass {
        display: inline-block;
        line-height: 16px;
        max-width: 48%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
        color: rgb(64, 158, 255);
        vertical-align: middle;
      }
    }
  }
  // 将el-table的展开图标替换为其他图标
  ::v-deep.el-table__expand-icon {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  ::v-deep .el-table__expand-icon .el-icon-arrow-right:before {
    content: '\e6d9';
    background: rgba(245, 247, 250, 0);
    border-radius: 2px;
    border: 1px solid #acb4c0;
    color: #62666c;
    z-index: 3;
    cursor: pointer;
    font-size: 8px;
  }

  ::v-deep.el-table__expand-icon--expanded .el-icon-arrow-right:before {
    content: '\e6d8';
  }
}
.myruleItem {
  display: block;
  height: 22px;
  line-height: 20px;
  border-radius: 12px;
  box-sizing: border-box;
}
.source1 {
  background: rgba(38, 119, 255, 0.12);
  color: rgba(38, 119, 255, 1);
  border: 1px solid rgba(38, 119, 255, 1);
}
.source2 {
  background: rgba(16, 213, 149, 0.12);
  color: rgba(16, 213, 149, 1);
  border: 1px solid rgba(16, 213, 149, 1);
}
.source3 {
  background: rgba(255, 121, 0, 0.12);
  color: rgba(255, 121, 0, 1);
  border: 1px solid rgba(255, 121, 0, 1);
}
.source4 {
  background: rgba(20, 169, 255, 0.12);
  color: rgba(20, 169, 255, 1);
  border: 1px solid rgba(20, 169, 255, 1);
}
.source5 {
  background: rgba(20, 214, 252, 0.12);
  color: rgba(20, 214, 252, 1);
  border: 1px solid rgba(20, 214, 252, 1);
}
.source6 {
  background: rgba(20, 24, 242, 0.12);
  color: rgba(20, 24, 262, 1);
  border: 1px solid rgba(20, 24, 262, 1);
}
/deep/.elDialogAddTextarea {
  /deep/.el-dialog__body {
    min-height: 158px !important;
  }
}
.index {
  margin-top: 30px;
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
