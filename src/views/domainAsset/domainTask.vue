<template>
  <div class="container">
    <div class="headerTitle">
      <span class="goback" @click="goBack"
        ><i class="el-icon-arrow-left"></i>返回<span class="spline">/</span></span
      >
      域名发现任务
      <span class="headerShow"
        ><i class="el-icon-warning"></i>支持对目标域名实施域名枚举以及验证</span
      >
    </div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入任务名称进行搜索"
            id="scan_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckIsShow" id="scan_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="scan_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="removeOne('more')"
            id="scan_more_del"
            >删除</el-button
          >
          <el-button class="normalBtn" type="primary" @click="addTaskDialog" id="scan_add"
            >新建任务</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="domainTask"
        @highcheck="highCheck"
      ></hightFilter>
      <domainTaskAddEditDialog
        :dialogFormVisible="dialogFormVisible"
        @insertSaveAfter="insertSaveAfter"
        @dialogFormVisibleClose="dialogFormVisibleClose"
        :ruleForm="ruleForm"
        :rules="rules"
      />
      <div :class="hightFilterIsShow()" v-loading="loading" style="padding: 0px 20px">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p
                >当前暂无扫描任务<i>，您可以<span @click="addTaskDialog">新建任务</span></i></p
              >
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :show-overflow-tooltip="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidtth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'name'">{{ scope.row[item.name] }}</span>
              <span v-else-if="item.name == 'level'">{{
                scope.row[item.name] == 0 ? '-' : scope.row[item.name]
              }}</span>
              <span v-else-if="item.name == 'status'">
                <span v-if="scope.row['status'] == 0" class="grayLine">等待扫描</span>
                <span v-if="scope.row['status'] == 1" class="blueLine">扫描中</span>
                <span v-if="scope.row['status'] == 5" class="greenLine">扫描完成</span>
                <span v-if="scope.row['status'] == 3" class="redLine">扫描失败</span>
                <span v-if="scope.row['status'] == 4" class="yellowLine">暂停扫描</span>
                <el-progress
                  v-if="scope.row['status'] == 1 || scope.row['status'] == 4"
                  :text-inside="true"
                  :stroke-width="12"
                  :percentage="parseFloat(scope.row['progress'])"
                  :status="setProgressColor(scope.row['status'])"
                ></el-progress>
              </span>
              <span v-else-if="item.name == 'updated_at'">
                <span v-if="scope.row['status'] != 0 && scope.row['status'] != 1">{{
                  scope.row[item.name]
                }}</span>
                <span v-else>-</span>
              </span>
              <span v-else-if="item.name == 'modify'">
                <span class="greenRadiusBorder" v-if="scope.row[item.name] == '0'">枚举模式</span>
                <span class="blueRadiusBorder" v-else>验证模式</span>
              </span>
              <span v-else-if="item.name == 'op'">{{
                scope.row[item.name] ? scope.row[item.name]['name'] : '-'
              }}</span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="180">
            <template slot-scope="scope">
              <!-- <span v-if="task_type == 2">
                <el-button type="text" size="small" v-if="scope.row['status'] == 0" @click="nowStartRun(scope.row.id)">置顶</el-button>
                <el-button type="text" size="small" v-if="scope.row['status'] == 0" @click="upDownOne(scope.row.id, 'up')">上移</el-button>
                <el-button type="text" size="small" style="margin-right: 10px" v-if="scope.row['status'] == 0" @click="upDownOne(scope.row.id, 'down')">下移</el-button>
              </span> -->
              <!-- v-if="scope.row['status'] == 2 || scope.row['is_audit'] == 2" -->
              <span>
                <!-- <el-button type="text" size="small" v-if="scope.row['status'] == 5" @click="againRun(scope.row.id)" id="scan_again">再次执行</el-button> -->
                <el-button
                  type="text"
                  size="small"
                  @click="viewList(scope.row.id, scope.row.modify)"
                  v-if="scope.row['status'] == 5"
                  id="scan_info"
                  >查看结果</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  @click="removeOne('one', scope.row.id)"
                  id="scan_del"
                  >删除</el-button
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="85px">
          <el-form-item label="任务名称：" prop="name">
            <el-input
              filterable
              clearable
              v-model="formInline.name"
              placeholder="请输入任务名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="任务模式：" prop="modify">
            <el-select
              filterable
              clearable
              v-model="formInline.modify"
              @change="selectChange($event, 'modify', typeArr, true, false)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in typeArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务状态：" prop="status">
            <el-select
              filterable
              clearable
              v-model="formInline.status"
              @change="selectChange($event, 'status', statusArr, true, false)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="爆破层级：" prop="level">
            <el-select
              filterable
              clearable
              v-model="formInline.level"
              @change="selectChange($event, 'level', levelSeletData, true, false)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in levelSeletData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发起人：" prop="op_id">
            <el-select
              filterable
              clearable
              v-model="formInline.op_id"
              @change="selectChange($event, 'op_id', userArr, true, false)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in userArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间：" prop="updated_at_range">
            <el-date-picker
              v-model="formInline.updated_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="scan_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="scan_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import { resetMessage } from '@/utils/resetMessage.js'
import domainTaskAddEditDialog from './domainTaskEditDialog.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { getAllUser } from '@/api/apiConfig/discovery.js'
import {
  domainAssetTaskListDel,
  domainAssetTaskList,
  domainAssetProgreeUpdate
} from '@/api/apiConfig/domain.js'

export default {
  components: {
    domainTaskAddEditDialog,
    hightFilter
  },
  data() {
    return {
      highTabShow: [
        {
          label: '任务名称',
          name: 'name',
          type: 'input'
        },
        {
          label: '任务模式',
          name: 'modify',
          type: 'select'
        },
        {
          label: '任务状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '爆破层级',
          name: 'level',
          type: 'select'
        },
        {
          label: '发起人',
          name: 'op_id',
          type: 'select'
        },
        {
          label: '开始时间',
          name: 'created_at_range',
          type: 'date'
        },
        {
          label: '结束时间',
          name: 'updated_at_range',
          type: 'date'
        }
      ],
      formInline: {
        name: '',
        keyword: '',
        modify: '',
        status: '',
        level: '',
        op_id: '',
        created_at_range: [],
        updated_at_range: []
      },
      userArr: [],
      levelSeletData: [
        {
          name: '1层',
          id: '1'
        },
        {
          name: '2层',
          id: '2'
        },
        {
          name: '3层',
          id: '3'
        }
      ],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      highCheckdialog: false,
      currentPage: 1,
      pageSizeArr: [10, 30, 50, 100],
      total: 0,
      currentId: '',
      checkedAll: false,
      checkedArr: [],
      pageSize: 10,
      tableData: [],
      loading: false,
      dialogFormVisible: false,
      highlist: null,
      statusArr: [
        {
          id: 1,
          name: '扫描中'
        },
        {
          id: 0,
          name: '等待扫描'
        },
        {
          id: 5,
          name: '扫描完成'
        }
      ],
      typeArr: [
        {
          id: '0',
          name: '枚举模式'
        },
        {
          id: '1',
          name: '验证模式'
        }
      ],
      tableHeader: [
        {
          label: '任务名称',
          name: 'name',
          fixed: 'left',
          minWidtth: '200'
        },
        {
          label: '任务状态',
          name: 'status',
          minWidtth: '90'
        },
        {
          label: '结果数量',
          name: 'num',
          minWidtth: '80'
        },
        {
          label: '任务模式',
          name: 'modify',
          minWidtth: '80'
        },
        {
          label: '爆破层级',
          name: 'level',
          minWidtth: '90'
        },
        {
          label: '开始时间',
          name: 'created_at',
          minWidtth: '120'
        },
        {
          label: '结束时间',
          name: 'updated_at',
          minWidtth: '120'
        },
        {
          label: '发起人',
          name: 'op',
          minWidth: '80'
        }
      ],
      user: {
        role: ''
      },
      ruleForm: {
        bandwidth: '1000', // 扫描带宽
        name: '',
        modify: '0',
        level: '1',
        domain_list: null,
        scan_range: 0,
        domain_type: ''
      },
      rules: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        domain_list: [{ required: true, message: '请输入或上传ip信息', trigger: 'change' }]
      }
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getMergeTakList()
      }
    },
    //  // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['socketTimestamp', 'websocketMessage', 'currentCompany', 'setTimeBox']),
    ...mapGetters([
      'getterCurrentCompany',
      'getterWebsocketMessage',
      'getterSocketTimestamp',
      'getterSettime'
    ])
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goBack() {
      sessionStorage.setItem('menuId', '2-1')
      this.changeMenuId('2-1')
      this.$router.push('/assetsTanzhi')
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    setProgressColor(status) {
      if (status == 4) {
        return 'warning'
      }
    },
    resetForm() {
      this.formInline = {
        keyword: '',
        modify: '',
        status: '',
        op_id: '',
        created_at_range: [],
        updated_at_range: []
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getMergeTakList()
    },
    againRun(id) {},
    viewList(id, status) {
      let str = ''
      if (status == '0') {
        // 枚举模式
        str = 'enumeration'
      } else {
        // 验证模式
        str = 'verification'
      }
      this.$router.push({
        path: '/domainTaskResult', // 去审核logShenhe
        query: {
          operate_company_id: this.currentCompany,
          id,
          type: str
        }
      })
    },
    getTableStatus(status) {
      let statusLabel = ''
      if (status == 0) {
        statusLabel = '等待扫描'
      } else if (status == 1) {
        statusLabel = '扫描中'
      } else if (status == 5) {
        statusLabel = '扫描完成'
      } else if (status == 3) {
        statusLabel = '扫描失败'
      } else if (status == 4) {
        statusLabel = '暂停扫描'
      } else {
        statusLabel = '扫描中'
      }
      return statusLabel
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMergeTakList()
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选查询
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getMergeTakList()
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    async insertSaveAfter() {
      this.dialogFormVisible = false
      this.getMergeTakList()
    },
    async highCheckIsShow() {
      this.highCheckdialog = true
      let res = await getAllUser()
      this.userArr = res.data
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (res.cmd == 'domain_tasks') {
        // 漏洞扫描钉钉审核通过
        this.getMergeTakList()
        this.runningFunc(res)
      }
    },
    // websocket执行
    runningFunc(res) {
      // 漏洞核查与资产、漏洞扫描区分，scan_poc_num存在是漏洞核查任务
      if (res.data.status == 5) {
        if (res.cmd == 'domain_tasks') {
          resetMessage.success('扫描成功！')
        }
        this.currentId = '' // 控制推送结束后仅执行一次
        this.getMergeTakList()
      } else if (res.data.status == 1) {
        // 正在扫描
        this.currentId = res.data.user_id
        if (res.data.progress == 100) {
          // 避免状态为1时进度已经达到100但是页面不提示完成信息，可手动变成99.9，状态为2时正常提示
          res.data.progress = 99.9
        }
        // 推送数据渲染到列表
        this.tableData.forEach((item, index) => {
          if (item.id == res.data.domain_tasks_id) {
            this.$set(this.tableData[index], 'status', res.data.status)
            this.$set(this.tableData[index], 'progress', res.data.progress)
          }
        })
      } else if (res.data.status == 4) {
        // 暂停扫描
        this.currentId = res.data.user_id
        // 推送数据渲染到列表
        this.tableData.forEach((item, index) => {
          if (item.id == res.data.domain_tasks_id) {
            this.$set(this.tableData[index], 'status', res.data.status)
            this.$set(this.tableData[index], 'progress', res.data.progress)
          }
        })
      } else {
        // 3 扫描失败
        this.getMergeTakList()
      }
    },
    removeOne(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
      }
      this.$confirm('确定删除任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'scan_del_cancel',
        confirmButtonClass: 'scan_del_sure',
        customClass: 'scan_del',
        type: 'warning'
      }).then(async () => {
        let obj = null
        if (icon == 'more') {
          obj = {
            id: this.checkedAll
              ? []
              : this.checkedArr.map((item) => {
                  return item.id
                }),
            operate_company_id: this.currentCompany,
            ...this.formInline
          }
        } else {
          obj = {
            id: [id],
            operate_company_id: this.currentCompany,
            ...this.formInline
          }
        }
        let res = await domainAssetTaskListDel(obj)
        if (res.code == 0) {
          this.$message.success('删除成功!')
          this.currentPage = this.updateCurrenPage(
            this.total,
            icon == 'more' ? this.checkedArr : [1],
            this.currentPage,
            this.pageSize
          ) // 更新页码
          this.checkedAll = false
          this.$refs.eltable.clearSelection()
          this.getMergeTakList()
        }
      })
    },

    async getMergeTakList() {
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        operate_company_id: this.currentCompany,
        ...this.formInline
      }
      this.loading = true
      let res = await domainAssetTaskList(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.tableData = res.data && res.data.items ? res.data.items : []
      this.total = res.data.total
      // 因为golang响应比较慢，所以此处做一个假进度
      clearInterval(this.setTimer)
      this.setTimer = null
      let runningTaskId = this.tableData
        .filter((item) => {
          return item.status == 1 && item.progress / 1 < 49
        })
        .map((item) => {
          return item.id
        }) // 获取页面正在进行并且进度小于49的任务id，进度超过49不入参
      if (runningTaskId.length == 0) return // 没有正在进行的任务不调用此接口
      this.setTimer = setInterval(() => {
        domainAssetProgreeUpdate({ id: runningTaskId }).then((res) => {
          if (res.code == 0) {
            res.data.forEach((item) => {
              this.tableData.forEach((to) => {
                if (item.id == to.id) {
                  to.progress = item.progress
                }
              })
            })
          }
        })
      }, 10000) // 每20秒刷新一次
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    dialogFormVisibleClose() {
      this.dialogFormVisible = false
    },
    async addTaskDialog() {
      this.ruleForm = {
        bandwidth: '1000', // 扫描带宽
        name: '',
        modify: '0',
        level: '1',
        domain_list: null,
        scan_range: 0
      }
      this.dialogFormVisible = true
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getMergeTakList()
    } else {
      this.getMergeTakList()
    }
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
    clearInterval(this.setTimer)
    this.setTimer = null
  },
  mounted() {}
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .waitNum {
    position: absolute;
    right: 0;
    top: -80px;
    font-size: 14px;
    font-weight: 400;
    color: #62666c;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 4px 10px;
    border-left: 2px solid #2677ff;
    .num {
      font-weight: 500;
      color: #2677ff;
      margin-right: 0px;
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .result {
      position: relative;
      padding: 47px 0 0 24px;
      flex: 0 0 auto;
      .normalBtn {
        position: absolute;
        top: 16px;
        right: 20px;
      }
      .scanning {
        display: flex;
        align-items: center;
        .scanning-img {
          width: 56px;
          height: 56px;
          svg {
            font-size: 56px;
            color: #ddd;
          }
        }
        .scanning-txt {
          margin-left: 15px;
          line-height: 1;
          .txt1 {
            color: #62666c;
            font-size: 16px;
            font-weight: 400;
            .name {
              font-weight: 500;
              color: #37393c;
            }
          }
          .txt2 {
            font-size: 12px;
            font-weight: 400;
            color: #62666c;
            padding-top: 10px;
            .time {
              margin-left: 21px;
            }
          }
          .txt3 {
            width: 248px;
            height: 16px;
            margin-bottom: 14px;
            background: #e5ebf4;
          }
          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 180px;
            height: 14px;
            background: #e5ebf4;
          }
          .txt5 {
            display: inline-block;
            width: 132px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }
      .handle {
        padding-top: 30px;
        padding-bottom: 29px;
        .line1 {
          overflow: hidden;
          .progress {
            float: left;
            width: 80%;
            height: 24px;
            background: #e5ebf4;
            border-radius: 12px;
            .el-progress.is-success .el-progress-bar__inner {
              top: 4px;
              left: 4px;
              height: 16px;
              background: -webkit-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -o-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -moz-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              border-radius: 12px;
            }
            ::v-deep .el-progress-bar__outer {
              background-color: #e5ebf4;
              border-radius: 0;
            }
            ::v-deep .el-progress.is-warning .el-progress-bar__inner {
              border-radius: 0;
            }
          }
          .btns {
            display: inline-block;
            padding-left: 16px;
            font-weight: 400;
            color: #2677ff;
            cursor: pointer;
            span {
              margin-right: 12px;
              i {
                margin-right: 3px;
              }
            }
          }
        }
        .line2 {
          width: 80%;
          font-size: 14px;
          color: #999;
          margin-top: 15px;
          display: flex;
          justify-content: space-between;
          .txt3 {
            width: 179px;
            height: 14px;
            background: #e5ebf4;
          }
          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 114px;
            height: 14px;
            background: #e5ebf4;
          }
          .txt5 {
            display: inline-block;
            width: 86px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      .emptyClass {
        height: 100%;
        text-align: center;
        vertical-align: middle;
        svg {
          display: inline-block;
          font-size: 120px;
        }
        p {
          line-height: 25px;
          color: #d1d5dd;
          span {
            margin-left: 4px;
            color: #2677ff;
            cursor: pointer;
          }
        }
      }
      .el-progress-bar__innerText {
        vertical-align: top !important;
      }
    }
    .el-table {
      border: 0;
      td {
        border-right: transparent !important;
      }
    }
    // 定义单元格文本超出不换行
    .el-table .cell {
      overflow: hidden !important;
      white-space: nowrap !important;
    }
  }
}
</style>
