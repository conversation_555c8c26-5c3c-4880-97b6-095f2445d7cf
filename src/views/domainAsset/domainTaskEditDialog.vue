<template>
  <el-dialog
    class="elDialogAdd"
    :close-on-click-modal="false"
    :visible.sync="dialogFormVisible"
    :before-close="dialogFormVisibleClose"
    width="950px"
  >
    <template slot="title">
      <!-- {{ruleForm.id ? '编辑任务' : '添加任务'}} -->
      添加任务
    </template>
    <div class="dialog-body" v-loading="loading">
      <div class="left">
        <el-form
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="addRuleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="任务名称" prop="name">
            <el-input v-model="ruleForm.name" placeholder="请输入任务名称"></el-input>
          </el-form-item>
          <el-form-item label="扫描目标" prop="scan_range">
            <el-select
              v-model="ruleForm.scan_range"
              @change="scan_range_change"
              placeholder="请选择"
            >
              <el-option
                v-for="item in selectDataType"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <div slot="label" class="labelText">
              <div>任务模式</div>
              <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                <div slot="content">
                  <p>枚举模式：对输入的域名进行二、三级域名发现，同时探测其解析情况；</p>
                  <p>验证模式：对输入的域名探测其解析情况；</p>
                </div>
                <i class="el-icon-question searchIcon"></i>
              </el-tooltip>
            </div>
            <el-radio-group v-model="ruleForm.modify" @change="levelChange">
              <el-radio label="0">枚举模式</el-radio>
              <el-radio label="1">验证模式</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="发现层级" prop="level" v-if="levelTmp">
            <el-select v-model="ruleForm.level" placeholder="请选择">
              <el-option
                v-for="item in levelSeletData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="扫描带宽" prop="bandwidth">
            <newInputNumber
              class="inputNumber"
              :min="100"
              :max="10000"
              :step="100"
              v-model.number="ruleForm.bandwidth"
              placeholder="大于100，小于10000 的整数"
            >
              <template slot="append">kb</template>
            </newInputNumber>
          </el-form-item>
        </el-form>
      </div>
      <div class="right">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="addRuleForm1"
          label-width="10px"
          class="demo-ruleForm"
        >
          <el-form-item
            label=""
            prop=""
            style="position: relative; height: 442px"
            v-if="ruleForm.scan_range == 0"
          >
            <el-input
              type="textarea"
              v-model="ruleForm.domain_list"
              class="placeholderIdBox"
            ></el-input>
            <div class="placeholderId" v-show="!ruleForm.domain_list">
              <div></div>
              <div>支持填写域名</div>
              <div></div>
              <div>域名格式如下：</div>
              <div>foradar.baimaohui.net</div>
              <div>最多输入200个，分号或换行分隔</div>
            </div>
          </el-form-item>
          <el-form-item label="" prop="" v-if="ruleForm.scan_range == 1">
            <p class="downloadClass" @click="downloadForbidIpsExcel">
              <i class="el-icon-warning"></i>请点击下载
              <span>域名信息导入模板</span>
            </p>
            <el-upload
              class="upload-demo"
              drag
              :action="uploadSrcIp + '/domain_assets/upload'"
              :headers="uploadHeaders"
              accept=".xlsx"
              :on-success="uploadSuccess"
              :on-error="uploadError"
              :on-remove="uploadRemove"
              :show-file-list="true"
              :before-upload="beforeUpload"
              :limit="uploadMaxCount"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过3M</div>
            </el-upload>
            <p style="color: #2677ff">上传文件的信息</p>
            <p
              class="uploadIps"
              v-html="
                !ruleForm.domain_list
                  ? ''
                  : ruleForm.domain_list.length > 0
                    ? ruleForm.domain_list.join('\n')
                    : ''
              "
            ></p>
          </el-form-item>
          <el-form-item
            label=""
            prop=""
            style="position: relative; height: 442px"
            v-if="ruleForm.scan_range == 2"
          >
            <div class="inputAndSelect"> </div>
            <div class="list">
              <new-transfer
                :titles="['未选择', '已选择']"
                filterable
                :filter-method="filterMethod"
                v-model="ruleForm.domain_list"
                :props="transferProp"
                ref="reserve"
                @left-check-change="handleChangeLeft"
                @right-check-change="handleChangeRight"
                :data="sureDomainData"
                :allData="allDomainData"
                targetOrder="originalMore"
              >
                <div slot="left-input" class="transferInput">
                  <el-select
                    v-model="domain_type"
                    slot="prepend"
                    placeholder="请选择"
                    @change="getAssetsListByType"
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="主域名" value="1"></el-option>
                    <el-option label="子域名" value="0"></el-option>
                  </el-select>
                </div>
              </new-transfer>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="dialogFormVisibleClose" id="scan_cancel">关闭</el-button>
      <el-button class="highBtn" @click="insertSave" id="scan_sure" :loading="btnLoading"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import newTransfer from '@/components/transfer/src/main'
import newInputNumber from '@/components/input-number/index'
import { domainAssetTask, downDomainScreenList } from '@/api/apiConfig/domain.js'

export default {
  components: { newTransfer, newInputNumber },
  props: ['dialogFormVisible', 'ruleForm', 'rules'],
  data() {
    return {
      filterMethod(query, item) {
        return item.domain.indexOf(query) > -1
      },
      domain_type: '',
      sureDomainData: [],
      allDomainData: [],
      transferProp: {
        key: 'domain',
        label: 'domain'
      },
      loading: false,
      uploadMaxCount: 1,
      levelTmp: true,
      btnLoading: false,
      uploadHeaders: { Authorization: localStorage.getItem('token') },
      fileList: [],
      selectDataType: [
        {
          name: '输入域名信息',
          id: 0
        },
        {
          name: '上传域名信息文件',
          id: 1
        },
        {
          name: '选择域名资产',
          id: 2
        }
      ],
      levelSeletData: [
        {
          name: '1层',
          id: '1'
        },
        {
          name: '2层',
          id: '2'
        },
        {
          name: '3层',
          id: '3'
        }
      ]
    }
  },
  watch: {
    dialogFormVisible(val) {
      this.levelTmp = true
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    handleChangeLeft(value, direction) {
      this.$nextTick(() => {
        this.$refs.reserve.addToRight() //直接执行到右事件
      })
    },
    handleChangeRight(value, direction) {
      this.$nextTick(() => {
        this.$refs.reserve.addToLeft() //直接执行到右事件
      })
    },
    levelChange(val) {
      this.levelTmp = val == '0' ? true : false
    },
    dialogFormVisibleClose() {
      this.$emit('dialogFormVisibleClose', false)
    },
    async insertSave() {
      this.ruleForm.operate_company_id = this.currentCompany
      let obj = Object.assign({}, this.ruleForm)
      if (!obj.name) {
        this.$message.error('请输入任务名称')
        return
      }
      if (!obj.bandwidth) {
        // 扫描目标不是已认领资产的都需要校验ips
        this.$message.error('请输入扫描带宽')
        return
      } else if (/^(?:[1-9]\d*)$/.test(obj.bandwidth) == false) {
        this.$message.error('扫描带宽请输入整数')
        return
      }
      if (!obj.domain_list || obj.domain_list.length == 0) {
        this.$message.error('请选择域名信息')
        return
      }
      if (obj.scan_range == 0) {
        obj.domain_list = obj.domain_list
          ? obj.domain_list
              .split(/[；|;|\r\n]/)
              .filter((item) => {
                return item.trim()
              })
              .map((item) => {
                return item.trim()
              })
          : []
      }
      if (!this.levelTmp) {
        obj.level = '1'
      }
      this.btnLoading = true
      let res = await domainAssetTask(obj).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.btnLoading = false
        this.$message.success('操作成功！')
        this.insertSaveAfter()
      }
    },
    insertSaveAfter() {
      this.$emit('insertSaveAfter')
    },
    scan_range_change(val) {
      this.ruleForm.domain_list = null
      if (val == 2) {
        //调用接口获取域名列表
        this.getAssetsListByType()
        this.domain_type = ''
        this.ruleForm.domain_list = []
      }
    },
    // filterMethod() {
    //   // this.getAssetsListByType()
    //   return this.sureDomainData
    // },
    changeDomainType() {},
    async getAssetsListByType() {
      this.ruleForm.operate_company_id = this.currentCompany
      const { domain_keyword, operate_company_id } = this.ruleForm
      // this.domain_type
      let res = await downDomainScreenList({
        type: this.domain_type,
        keyword: domain_keyword,
        operate_company_id
      })
      this.sureDomainData = res.data.domains.map((item, index) => ({ id: index, domain: item }))
      if (this.domain_type == '') {
        this.allDomainData = this.sureDomainData
      }
    },
    downloadForbidIpsExcel() {
      window.location.href = '/downloadTemplate/域名导入模板.xlsx'
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadSuccess(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.ruleForm.domain_list = []
        this.ruleForm.domain_list = response.data
        this.file_name = file.name
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadError(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.ruleForm.domain_list = []
      }
    },
    beforeUpload(file, limit = 3) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    }
  }
}
</script>
<style lang="less" scoped>
.dialog-body /deep/ {
  height: 497px;
  display: flex;
  justify-content: space-between;
  & > .el-form {
    width: 100%;
  }
  & > .el-divider--vertical {
    height: 156px;
    background: #e9ebef;
    margin-bottom: 20px;
  }
  & > span {
    display: inline-block;
    width: 48%;
    text-align: center;
    margin-top: 30px;
  }
  & > span > img {
    width: 54px;
    height: 54px;
    margin-bottom: 8px;
  }
  & > span p {
    color: #2677ff;
    font-weight: 400;
  }
  /deep/.el-input--suffix .el-input__inner {
    padding-right: 20px;
  }
  .left {
    // height: 100%;
    // overflow: auto;
    // margin-bottom: 24px;
    flex: 1;
    .tishi {
      line-height: 20px;
      color: #d1d5dd;
    }
    /deep/.el-textarea {
      width: 300px;
      height: 368px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d1d5dd;
      .el-textarea__inner {
        height: 100%;
        border: 0 !important;
      }
      .el-textarea__inner:hover {
        border: 0;
      }
    }
    .dialog-footer {
      text-align: right;
    }
  }
  .right {
    width: 55%;
    margin-bottom: 24px;
    margin-left: 33px;
    border-left: 1px solid #e9ebef;
    /deep/.tabWrap {
      .el-tabs__nav {
        height: 32px !important;
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #e4e7ed;
        margin-bottom: 0;
        padding-left: 0;
        margin-left: 33px;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        height: 32px !important;
        line-height: 32px !important;
        text-align: center;
        background: rgba(38, 119, 255, 0.1);
        border-radius: 0;
        border: 1px solid #2677ff;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        height: 0;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 0px;
        // background-color: #E9EBEF;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 32px !important;
        text-align: center;
        line-height: 32px;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
        transition: none;
      }
    }
    .downloadClass {
      margin-top: 10px;
      margin: 0px 0px 16px !important;
    }
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
    .el-transfer {
      margin-top: 10px;
      height: 434px !important;
    }
    .el-transfer-panel {
      height: 100% !important;
    }
    .uploadIps {
      height: 124px;
      overflow: auto;
      border: 1px solid #e9ebef;
      border-radius: 5px;
      padding: 10px;
      white-space: pre-line;
    }
    /deep/.el-textarea {
      width: 100%;
      height: 442px;
      margin-top: 10px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d1d5dd;
      .el-textarea__inner {
        height: 100%;
        border: 0 !important;
      }
      .el-textarea__inner:hover {
        border: 0;
      }
    }
  }
}

.placeholderIdBox {
  position: absolute;
  top: 0px;
  background-color: transparent !important;
  /deep/.el-textarea__inner {
    background-color: transparent !important;
  }
}
.placeholderId {
  margin-top: 10px;
  div {
    min-height: 22px;
    line-height: 22px;
    color: #c0c4cc;
    padding-left: 15px;
  }
}
.labelText {
  display: flex;
  align-items: center;
  div {
    width: 100px;
  }
  i {
    margin-left: 5px;
  }
}
// .inputAndSelect{
//   width: 100%;
//   /deep/.el-select .el-input {
//     width: 130px;
//   }
//   /deep/.input-with-select .el-input-group__prepend {
//     background-color: #fff;
//     // padding: 0 !important;
//   }
//   /deep/.el-input-group__prepend {
//     padding: 0;
//     .el-select{
//       margin: 0;
//     }
//   }
// }
/deep/.left-transfer-input {
  padding: 5px 10px;
  // margin-bottom: 10px;
  div {
    margin-bottom: 10px;
  }
}
/deep/.el-transfer-panel__body {
  display: flex;
  flex-direction: column;
  .left-transfer-input {
    height: 34px;
  }
  .el-checkbox-group {
    flex: 1;
  }
}
</style>
