<template>
  <div class="container" v-loading="loading">
    <div class="headerTitle"
      ><span>
        <span class="goback" @click="$router.go(-1)"
          ><i class="el-icon-arrow-left"></i>返回上一层</span
        >
        <span class="spline">/</span>
        <span>任务结果</span>
      </span></div
    >
    <div class="home_header">
      <div class="tableWrap" ref="tableWrap">
        <div class="leftTab">
          <div class="filterTab">
            <div>
              <el-input
                v-model="domain"
                placeholder="请输入域名检索"
                @keyup.enter.native="search"
                id="keyword_keycheck"
              >
                <el-button slot="append" icon="el-icon-search" @click="search"></el-button>
              </el-input>
            </div>
            <div>
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAll"
                @change="checkAllChange"
                id="scan_info_all"
                >选择全部</el-checkbox
              >
              <el-button class="normalBtnRe" type="primary" id="account_export" @click="exportList"
                >导出</el-button
              >
            </div>
          </div>
          <div style="height: 85%">
            <div v-if="tableData && tableData.length == 0" class="emptyClass">
              <div class="emptyClass">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-kong"></use>
                </svg>
                <p>暂无数据</p>
              </div>
            </div>
            <ul class="asset-list" v-show="tableData && tableData.length > 0">
              <li v-for="item in tableData" :key="item.id">
                <div>
                  <p>
                    <el-checkbox
                      @change="(val) => checkChange(val, item.id)"
                      :disabled="checkedAll"
                      v-model="item.checked"
                    >
                      <el-tooltip class="item" effect="dark" :content="item.domain" placement="top">
                        <span class="ipTitle">{{ item.domain }}</span>
                      </el-tooltip>
                    </el-checkbox>
                  </p>
                  <p v-for="(titleItem, key, index) in tableHeader" :key="index"
                    >{{ titleItem.label }}：
                    <el-tooltip
                      class="item ipContent"
                      effect="dark"
                      placement="top"
                      :open-delay="500"
                    >
                      <span slot="content">{{ item[titleItem.name] }}</span>
                      <span>{{ item[titleItem.name] }}</span>
                    </el-tooltip>
                  </p>
                </div>
              </li>
            </ul>
          </div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="pageSizeArr"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
        <ul class="recommendlLeft" style="width: 20%">
          <div class="recommendTitle">任务参数</div>
          <div class="recommendBox">
            <li v-for="item in domainTitle" :key="item.name">
              <p class="rightTitle">{{ item.label }}：</p>
              <span class="rightContent">
                <span v-if="item.name == 'modify'">
                  <span v-if="domainResultInfo[item.name] == '0'">枚举模式</span>
                  <span v-else>验证模式</span>
                </span>
                <span v-else-if="item.name == 'resultNum'">{{ total }}</span>
                <div v-else-if="item.name == 'domain_list'">
                  <p
                    v-for="(item, index) in domainResultInfo[item.name]
                      ? JSON.parse(domainResultInfo[item.name])
                      : []"
                    :key="index"
                    >{{ item }}</p
                  >
                </div>
                <span v-else>
                  {{ domainResultInfo[item.name] }}
                </span>
              </span>
            </li>
          </div>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getDomainTaskResult, downDomainTaskResult } from '@/api/apiConfig/domain.js'

export default {
  data() {
    return {
      loading: false,
      currentPage: 1,
      pageSizeArr: [10, 30, 50, 100],
      total: 0,
      pageSize: 10,
      domain: '',
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      checkedAll: false,
      tableData: [],
      domainResultInfo: {},
      tableHeader: [],
      domainTitle: [],
      checkedArr: []
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  methods: {
    search() {
      this.checkedArr = []
      this.getData('filter')
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleCurrentChange(val) {
      this.currentPage = val
      // 切换分页时拿到选中数据
      // this.tableData.forEach(item => {
      //   if (item.checked) {
      //     this.checkedArr.push(item.id)
      //   }
      // })
      this.getData()
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((item) => {
          this.$set(item, 'checked', true)
        })
      } else {
        this.tableData.forEach((item) => {
          this.$set(item, 'checked', false)
        })
      }
    },
    async getData(filter) {
      if (filter) {
        this.currentPage = 1
      }
      let obj = {
        data: {
          page: this.currentPage,
          per_page: this.pageSize,
          domain: this.domain,
          operate_company_id: this.$route.query.operate_company_id
        },
        id: this.$route.query.id
      }
      this.loading = true
      let res = await getDomainTaskResult(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.domainResultInfo = res.data && res.data.info ? res.data.info : {}
      this.tableData = res.data && res.data.items ? res.data.items : []
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((item) => {
          this.$set(item, 'checked', true)
        })
      } else {
        // 切换分页回显已勾选
        this.tableData.forEach((item) => {
          if (this.checkedArr.indexOf(item.id) != -1) {
            this.$set(item, 'checked', true)
          } else {
            this.$set(item, 'checked', false)
          }
        })
      }
      this.total = res.data.total
    },
    checkChange(value, id) {
      this.tableData.map((v) => {
        if (id == v.id) {
          v.select = value
          // 选中
          if (v.select) {
            this.checkedArr.push(id)
          } else {
            let dataTmp = this.checkedArr
            let flag = this.checkedArr.indexOf(id)
            dataTmp.splice(flag, 1)
            this.checkedArr = dataTmp
          }
        }
      })
    },
    async exportList() {
      let obj = null
      obj = {
        is_all: this.checkedAll ? '1' : '',
        id_array: this.checkedAll ? [] : this.checkedArr,
        operate_company_id: this.currentCompany,
        domain: this.domain,
        id: this.$route.query.id
      }
      let res = await downDomainTaskResult(obj)
      if (res.code == 0) {
        this.download(this.showSrcIp + res.data.url)
      }
    }
  },
  mounted() {
    if (this.$route.query.type == 'enumeration') {
      // 枚举模式
      this.tableHeader = [
        // {
        //   label: '域名',
        //   name: 'domain',
        //   minWidtth: '300'
        // },
        {
          label: 'CNAME',
          name: 'cname',
          minWidtth: '200'
        },
        {
          label: 'A记录(IPV4)',
          name: 'dns_a',
          minWidtth: '200'
        },
        {
          label: 'AAAA记录(IPV6)',
          name: 'dns_aaaa',
          minWidtth: '200'
        },
        {
          label: '被爆破的域名',
          name: 'f_domain',
          minWidtth: '200'
        }
        // {
        //   label: '开始时间',
        //   name: 'created_at',
        //   minWidtth: '200'
        // },
        // {
        //   label: '结束时间',
        //   name: 'updated_at',
        //   minWidtth: '120'
        // },
      ]
      this.domainTitle = [
        {
          label: '任务名称',
          name: 'name'
        },
        {
          label: '任务模式',
          name: 'modify'
        },
        {
          label: '爆破层级',
          name: 'level'
        },
        {
          label: '结果数量',
          name: 'resultNum'
        },
        {
          label: '开始时间',
          name: 'created_at'
        },
        {
          label: '结束时间',
          name: 'updated_at'
        },
        {
          label: '枚举目标',
          name: 'domain_list'
        }
      ]
    } else {
      // 验证模式
      this.tableHeader = [
        // {
        //   label: '域名',
        //   name: 'domain',
        //   minWidtth: '300'
        // },
        {
          label: 'CNAME',
          name: 'cname',
          minWidtth: '200'
        },
        {
          label: 'A记录(IPV4)',
          name: 'dns_a',
          minWidtth: '200'
        },
        {
          label: 'AAAA记录(IPV6)',
          name: 'dns_aaaa',
          minWidtth: '200'
        }
        // {
        //   label: '创建时间',
        //   name: 'created_at',
        //   minWidtth: '200'
        // },
        // {
        //   label: '更新时间',
        //   name: 'updated_at',
        //   minWidtth: '120'
        // },
      ]
      this.domainTitle = [
        {
          label: '任务名称',
          name: 'name'
        },
        {
          label: '任务模式',
          name: 'modify'
        },
        {
          label: '开始时间',
          name: 'created_at'
        },
        {
          label: '结束时间',
          name: 'updated_at'
        },
        {
          label: '验证目标',
          name: 'domain_list'
        }
      ]
    }
    this.getData()
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
}
.home_header {
  width: 100%;
  height: 100%;
}
.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  .checkboxAll {
    margin-left: 16px;
  }
  & > div {
    display: flex;
    align-items: center;
    .normalBtnRe {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .el-input {
      width: 240px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      margin-left: 16px;
      cursor: pointer;
    }
  }
}
.ipTitle {
  // margin-left: 10px;
  font-weight: 500;
  color: #37393c;
  font-size: 15px;
  display: inline-block;
  vertical-align: middle;
}
.emptyClass {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  font-size: 12px;
}
.asset-list {
  width: 100%;
  height: 100%;
  // padding-right: 20px;
  box-sizing: border-box;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  // justify-content:space-between;
  & > li {
    width: 24%;
    max-height: 200px;
    margin-right: 1%;
    background: #ffffff;
    box-shadow: 0px 4px 20px 0px rgba(16, 33, 62, 0.08);
    border-top: 4px solid #e4ecf8;
    border-radius: 4px;
    margin-bottom: 20px;
    & > div {
      // height: 53%;
      margin-bottom: 12px;
    }
    p {
      padding: 12px 12px 0 12px;
      color: #37393c;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: middle;
      .el-checkbox {
        vertical-align: middle;
        .el-checkbox__label {
          font-size: 16px;
          color: #37393c;
          font-weight: 500;
        }
      }
    }
    p:nth-child(1) {
      padding-bottom: 12px;
      border-bottom: 1px solid #e9ebef;
    }
  }
  & > li:nth-child(4n) {
    margin-right: 0;
  }
}
.tableWrap {
  height: 100%;
  display: flex;
  justify-content: space-between;
  .leftTab {
    width: 79%;
    padding: 0px 20px;
    box-sizing: border-box;
    background: #fff;
  }
  .recommendlLeft {
    background: #ffffff linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #f3f7ff 100%);
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    .recommendTitle {
      font-weight: 500;
      color: #37393c;
      margin: 12px 16px 0px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e9ebef;
    }
    .recommendBox {
      height: calc(100% - 44px);
      overflow: auto;
    }
    li {
      padding: 16px 16px 0px;
      color: #62666c;
      .rightTitle {
        width: 100%;
        padding-bottom: 4px;
        font-size: 14px;
        color: #111;
      }
      .rightContent {
        span {
          display: inline-block;
          padding: 0;
        }
        .clueClass {
          .el-icon-picture-outline {
            font-size: 16px;
          }
        }
      }
    }
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  text-align: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
