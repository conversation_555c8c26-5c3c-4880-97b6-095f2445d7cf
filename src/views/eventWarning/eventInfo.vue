<template>
  <div class="containerInfo">
    <div class="headerTitle">
      <span class="goback" @click="$router.go(-1)"
        ><i class="el-icon-arrow-left"></i>返回上一层</span
      >
      <span class="spline">/</span>
      <span>事件告警详情</span>
    </div>
    <div class="home_header" v-loading="loading">
      <div class="tableWrap">
        <div :class="fromPath ? 'leftTab' : 'leftTab leftTabFromIndex'">
          <ul class="ipInfo">
            <div class="infoTitle">
              <div>
                {{ ipInfoData.name }}
                <span :class="getStatusCass(ipInfoData.status)">{{
                  getStatus(ipInfoData.status)
                }}</span>
              </div>
            </div>
            <li>
              <p v-for="item in infoHeader" :key="item.name"
                >{{ item.label }}
                <el-tooltip
                  v-if="getTableItem(ipInfoData[item.name])"
                  class="item"
                  effect="dark"
                  :content="getTableItem(ipInfoData[item.name])"
                  placement="top"
                  :open-delay="500"
                >
                  <span>{{ getTableItem(ipInfoData[item.name]) }}</span>
                </el-tooltip>
              </p>
            </li>
          </ul>
          <div class="portInfo">
            <div class="infoTitle">影响资产</div>
            <div style="height: 72%; margin: 0px 16px">
              <el-table
                border
                :data="tableData"
                row-key="id"
                :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
                ref="eltable"
                height="100%"
                :span-method="arraySpanMethod"
                style="width: 100%"
              >
                <!-- <el-table-column type="expand">
                  <template slot-scope="props">
                    <el-table
                      border
                      :data="props.row.port_list"
                      row-key="port"
                      :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
                      ref="eltable"
                      height="100%"
                      style="width: 100%"
                    >
                      <el-table-column
                        v-for="item in tableHeaderExpand"
                        :key="item.id"
                        :prop="item.name"
                        align="center"
                        :show-overflow-tooltip="true"
                        :min-width="item.minWidth"
                        :label="item.label"
                      >
                        <template slot-scope="scope">
                          <p class="rules" v-if="item.name == 'rules'" style="padding: 0">
                            <span v-if="scope.row[item.name]" class="ruleItemBox">
                              <span
                                class="ruleItem"
                                v-for="(item, index) in scope.row[item.name]"
                                :key="index"
                                >{{ item }}</span
                              >
                            </span>
                            <i v-else>-</i>
                          </p>
                          <span v-else-if="item.name == 'url'">
                            <el-tooltip
                              class="item"
                              effect="dark"
                              placement="top"
                              content="可点击前往"
                              :open-delay="500"
                            >
                              <span
                                class="jumpPage"
                                style="cursor: pointer"
                                @click="jumpPage(scope.row.url)"
                                >{{ getTableItem(scope.row[item.name]) }}</span
                              >
                            </el-tooltip>
                          </span>
                          <template v-else-if="item.name == 'port'">
                            <el-tooltip
                              :open-delay="500"
                              class="item"
                              effect="dark"
                              :content="scope.row.is_open == 1 ? '在线' : '离线'"
                              placement="top"
                            >
                              <span>
                                <i class="greenStatus" v-if="scope.row.is_open == 1"></i>
                                <i class="redStatus" v-else></i>
                                <span>{{ getTableItem(scope.row[item.name]) }}</span>
                              </span>
                            </el-tooltip>
                          </template>
                          <span v-else>{{ getTableItem(scope.row[item.name]) }}</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </template>
                </el-table-column> -->
                <el-table-column
                  v-for="item in tableHeader"
                  :key="item.id"
                  :prop="item.name"
                  align="left"
                  :show-overflow-tooltip="true"
                  :min-width="item.minWidth"
                  :label="item.label"
                >
                  <template slot-scope="scope">
                    <span
                      style="cursor: pointer; color: #2677ff"
                      v-if="item.name == 'ip'"
                      @click="infoCheck(scope.row.id)"
                      >{{ scope.row[item.name] }}</span
                    >
                    <span v-else>{{ getTableItem(scope.row[item.name]) }}</span>
                  </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" align="left" prop="operate" width="100">
                  <template slot-scope="scope">
                    <span>
                      <el-button type="text" size="small" @click="remove(scope.row)" id="ip_del"
                        >删除</el-button
                      >
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { eventWaringInfo, eventWaringInfoDel } from '@/api/apiConfig/api.js'

export default {
  data() {
    return {
      loading: false,
      fromPath: false,
      activeName: 'a',
      ruleForm: {
        clue_company_name: '',
        province: '',
        lat: '',
        lon: ''
      },
      infoHeader: [
        {
          label: '事件描述：',
          name: 'event_desc'
        },
        {
          label: '事件危害：',
          name: 'event_impact'
        },
        {
          label: '解决方案：',
          name: 'event_recommendation'
        }
      ],
      portGroupsNoPageArr: [],
      formInline: {
        rank: '',
        port: ''
      },
      ipInfoData: {
        online_state: '在线'
      },
      chain_list: [],
      threatsData: [],
      tableData: [],
      tableHeaderExpand: [
        {
          label: '端口',
          name: 'port'
        },
        {
          label: '协议',
          name: 'protocol'
        },
        {
          label: '组件信息',
          name: 'rules'
        },
        {
          label: 'URL',
          name: 'url'
        }
      ],
      tableHeader: [
        {
          label: 'IP',
          name: 'ip',
          minWidth: 80
        },
        {
          label: '端口',
          name: 'port'
        },
        {
          label: '协议',
          name: 'protocol'
        },

        {
          label: 'URL',
          name: 'url'
        },
        {
          label: '组件信息',
          name: 'rules'
        },
        {
          label: '发现时间',
          name: 'created_at',
          minWidth: 80
        }
      ],
      addIsTrue: true,
      currentPage: 1,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      user: null
    }
  },
  watch: {
    getterCompanyChange(val) {
      if (this.$route.query && this.$route.query.fromIcon == 1) {
        // 查看列表-安服账号切换企业需要回到任务扫描列表
        this.$router.replace('/assetsScan')
      } else {
        this.$router.go(-1) // 其他页面ip详情，安服账号切换企业需要回到一级页面
      }
    },
    getterCurrentCompany() {
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      this.currentPage = 1
      if (this.user.role == 2) {
        if (!this.currentCompany) return
        this.getTaskResultData()
      } else {
        this.getTaskResultData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    if (this.$route.query.fromIcon == 1) {
      // 资产扫描任务ip详情
      this.fromPath = false
    } else {
      // 台账ip详情
      this.fromPath = true
    }
  },
  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getTaskResultData()
    } else {
      this.getTaskResultData()
    }
  },

  methods: {
    arraySpanMethod({ row, column, columnIndex, rowIndex }) {
      const arr = ['ip', 'rules', 'created_at', 'operate']
      console.log(row, column, columnIndex, 'arraySpanMethod')
      if (arr.includes(column.property)) {
        return {
          rowspan: row.portNum,
          colspan: 1
        }
      }

      // if (columnIndex === 0) {
      //   if (rowIndex % 2 === 0) {
      //     return {
      //       rowspan: 2,
      //       colspan: 1
      //     }
      //   } else {
      //     return {
      //       rowspan: 0,
      //       colspan: 0
      //     }
      //   }
      // }
      // console.log('row--', row, 'column--', column, 'index--', index, 'arraySpanMethod')
    },
    remove(row) {
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        eventWaringInfoDel({
          id: row.id,
          risk_type: this.ipInfoData.rule_id,
          operate_company_id: this.currentCompany
        }).then((res) => {
          if (res.code == '0') {
            this.$message.success('删除成功！')
            this.getTaskResultData()
          }
        })
      })
    },
    jumpPage(url) {
      if (url.indexOf('http://') == -1 && url.indexOf('https://') == -1) {
        window.open('http://' + url)
      } else {
        window.open(url)
      }
    },
    infoCheck(id) {
      window.open(`/alreadyTask_viewlist_ipinfo?id=${id}&fromIcon=0`, '_blank') // fromIcon: 默认'0' 已完成任务列查看ip详情，传1
    },
    getStatusCass(status) {
      let className = ''
      if (status == 0) {
        className = 'grayLine'
      } else if (status == 1) {
        className = 'greenLine'
      } else if (status == 2) {
        className = 'originLine'
      } else {
        className = 'grayLine'
      }
      return className
    },
    getStatus(status) {
      let str = ''
      switch (status) {
        case 0:
          str = '待处理'
          break
        case 1:
          str = '已确认'
          break
        case 2:
          str = '已忽略'
          break
        default:
      }
      return str
    },
    getTableItem(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length > 0) {
            return item.join('，')
          } else {
            return '-'
          }
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    async getTaskResultData() {
      let obj = {
        id: this.$route.query.id,
        data: {
          operate_company_id: this.currentCompany
        }
      }
      this.loading = true
      let res = await eventWaringInfo(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.ipInfoData = res.data

      let affect_assets = []
      let portNum
      res.data.affect_assets.forEach((item) => {
        console.log(item.ip, 'ip--')

        item.port_list.forEach((port, index) => {
          if (index === 0) {
            portNum = item.port_list.length
          } else {
            portNum = item.port_list.length > 1 ? 0 : 1
          }
          console.log(index, portNum, '---portNum---')
          affect_assets.push({
            ...port,
            portNum,
            created_at: item.created_at,
            ip: item.ip,
            id: item.id,
            rule_tags: item.rule_tags,
            user_id: item.user_id,
            _id: item._id
          })
        })
      })
      console.log(affect_assets, '---affect_assets---')
      this.tableData = affect_assets

      // this.total = res.data.total
    },
    handleNodeClick(node) {},
    highCheck() {},
    resetForm() {
      this.formInline = {
        rank: '',
        port: ''
      }
    },
    exportWord() {}
  }
}
</script>

<style lang="less" scoped>
.containerInfo /deep/ {
  position: relative;
  width: 100%;
  height: 100%;
  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left {
      height: 100%;
      display: flex;
      /deep/.el-textarea {
        width: 300px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }
  }
  /deep/.home_header {
    // position: relative;
    height: 100%;
    .el-tabs__nav {
      position: relative;
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      // width: 0 !important;
      padding: 0 16px;
      // padding: 0;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
      background: #fff;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      // padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0 16px 15px;
      margin-bottom: 20px;
      & > div {
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrap {
      height: 100%;
      display: flex;
      justify-content: space-between;
      .leftTabFromIndex {
        width: 100% !important;
      }
      .leftTab {
        height: 100%;
        width: 100%;
        .ipInfo {
          width: 100%;
          height: 195px;
          background: #ffffff linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #f3f7ff 100%);
          box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
          & > li {
            padding: 20px 16px;
            height: 51%;
            overflow: auto;
            p {
              width: 100%;
              margin-bottom: 16px;
              line-height: 20px;
              color: #62666c;
              white-space: wrap;
              span {
                color: #37393c;
              }
            }
          }
        }
        .portInfo {
          margin-top: 12px;
          height: calc(100% - 205px);
          background: #fff;
          .rules {
            display: flex;
            flex-wrap: wrap;
            padding: 0 12px;
            /* text-align:center; */
            .ruleItemBox {
              display: flex;
              flex-wrap: wrap;
              /* text-align:center; */
            }
            .ruleItem {
              display: inline-block;
              padding: 2px 10px;
              margin: 5px 8px 5px 0px;
              background: #ffffff;
              border-radius: 12px;
              border: 1px solid #d1d5dd;
              font-size: 12px;
              color: #37393c;
            }
          }
          td.el-table__expanded-cell {
            padding: 0;
            .banner {
              background: #f5f7fa;
              padding: 20px;
              line-height: 32px;
            }
          }
        }
      }
    }
    .el-table {
      border: 0;
      margin-top: 20px;
    }
  }
}
.ipInfo > li > p:last-child {
  margin-bottom: 0px !important;
}
.tiemBox {
  color: #62666c;
  .tiemContent {
    color: #37393c;
    margin-left: 16px;
  }
}
.infoTitle {
  padding-left: 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0px 16px;
}
.jumpPage {
  color: #2677ff;
}
/deep/.el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: 1px solid #e1e5ed !important;
}
</style>
