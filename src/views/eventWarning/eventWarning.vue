<template>
  <div class="container">
    <div class="headerTitle">
      事件告警
      <p class="statisticnumBox">
        <span class="statisticnum"
          >风险事件：<span class="num">{{ total }}</span></span
        >
        <span class="statisticnum"
          >受影响资产：<span class="num">{{ affect_assets_num }}</span></span
        >
      </p>
    </div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字进行搜索"
            @keyup.enter.native="Advanced"
            id="ip_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="Advanced"></el-button>
            <el-tooltip
              slot="prepend"
              class="item"
              effect="dark"
              content="支持检索字段：事件名称"
              placement="top"
              :open-delay="100"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-input>
          <span @click="highCheckdialog = true" id="ip_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="number_all"
            >选择全部</el-checkbox
          >
          <div style="display: flex; align-items: center">
            <el-button
              class="normalBtnRe"
              type="primary"
              @click="updateStatusSave('more', '')"
              id="number_more_del"
              >删除</el-button
            >
            <el-button
              class="normalBtnRe"
              type="primary"
              @click="updateStatusSave('more', 2)"
              id="number_more_ignore"
              >忽略</el-button
            >
            <el-button
              class="normalBtn"
              type="primary"
              @click="updateStatusSave('more', 1)"
              id="number_more_confirm"
              >确认</el-button
            >
            <el-button
              class="normalBtnRe"
              style="margin-left: 10px"
              type="primary"
              @click="exportExcel"
              id="user_export"
              >导出</el-button
            >
          </div>
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        pageIcon="eventwarning"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minHeight"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span
                style="color: #2677ff; cursor: pointer"
                v-if="item.name == 'name'"
                @click="eventInfo(scope.row)"
                >{{ scope.row[item.name] }}</span
              >
              <span
                v-else-if="item.name == 'status'"
                :class="getStatusCass(scope.row[item.name])"
                >{{ getStatus(scope.row[item.name]) }}</span
              >
              <div v-else-if="item.name == 'affect_assets'" class="ruleItemBox">
                <span
                  class="ruleItem"
                  v-for="ch in get_affect_assets(scope.row[item.name], 'filter')"
                  :key="ch"
                  >{{ ch }}</span
                >
                <el-popover
                  placement="top"
                  width="315"
                  style="padding-right: 0px !important; padding-left: 0px !important"
                  popper-class="rulePopover"
                  trigger="click"
                >
                  <div class="myruleItemBox">
                    <span
                      class="myruleItem"
                      v-for="(v, i) in get_affect_assets(scope.row[item.name])"
                      :key="i"
                      >{{ v }}</span
                    >
                  </div>
                  <span
                    slot="reference"
                    v-if="get_affect_assets(scope.row[item.name]).length > 2"
                    class="ruleItemNum"
                    >共{{ get_affect_assets(scope.row[item.name]).length }}条</span
                  >
                </el-popover>
              </div>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="left" width="150">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="updateStatusSave('one', 1, scope.row.id)"
                v-if="scope.row['status'] == 0 || scope.row['status'] == 2"
                id="number_confirm"
                >确认</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="updateStatusSave('one', 2, scope.row.id)"
                v-if="scope.row['status'] == 0 || scope.row['status'] == 1"
                id="number_ignore"
                >忽略</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="updateStatusSave('one', '', scope.row.id)"
                id="number_del"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="110px">
          <el-form-item label="事件名称" prop="keyword">
            <el-input v-model="formInline.keyword" placeholder="请输入事件名称"></el-input>
          </el-form-item>
          <el-form-item label="状态：" prop="event_status">
            <el-select
              filterable
              v-model="formInline.event_status"
              placeholder="请选择"
              @change="selectChange($event, 'event_status', statusArr, true, false)"
              clearable
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="首次发现时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="最新发现时间：" prop="updated_at_range">
            <el-date-picker
              v-model="formInline.updated_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="number_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="number_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  eventWaringList,
  delEventWaring,
  handleEventWaring,
  exportEventWaring
} from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip,
    hightFilter
  },
  name: 'ipManage',
  data() {
    return {
      highTabShow: [
        {
          label: '事件名称',
          name: 'keyword',
          type: 'input'
        },
        {
          label: '状态',
          name: 'event_status',
          type: 'select'
        },
        {
          label: '首次发现时间',
          name: 'created_at_range',
          type: 'date'
        },
        {
          label: '最新发现时间',
          name: 'updated_at_range',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      formInline: {
        created_at_range: [],
        updated_at_range: [],
        keyword: '',
        event_status: ''
      },
      statusArr: [
        {
          name: '待处理',
          id: 0
        },
        {
          name: '已确认',
          id: 1
        },
        {
          name: '已忽略',
          id: 2
        }
      ],
      checkedAll: false,
      highCheckdialog: false,
      tableHeader: [
        {
          label: '事件名称',
          name: 'name',
          minHeight: '100px'
        },
        {
          label: '受影响资产',
          name: 'affect_assets',
          minHeight: '200px'
        },
        {
          label: '首次发现时间',
          name: 'created_at'
        },
        {
          label: '最新发现时间',
          name: 'updated_at'
        },
        {
          label: '状态',
          name: 'status'
        }
      ],
      tableData: [],
      affect_assets_num: '',
      total: 0,
      currentPage: 1,
      pageSize: 10,
      dialogFormVisibleInsert: false,
      loading: false,
      otherLoading: false,
      ipArr: [
        {
          name: 'IPv4',
          value: 1
        },
        {
          name: 'IPv6',
          value: 2
        }
      ],
      checkedArr: [],
      user: {
        role: ''
      }
    }
  },
  mounted() {
    if (this.$route.query.createdTimeRange) {
      let createdTimeRange = this.$route.query.createdTimeRange
      this.formInline.created_at_range = createdTimeRange
      this.highlist = Object.assign({}, this.formInline)
      this.hightFilterIsShow()
    }
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    async exportExcel() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      let params = {
        ...this.formInline,
        id: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            })
      }
      let res = await exportEventWaring(params)
      if (res.code == 0) {
        this.$message.success('操作成功！请稍后…')
        this.download(this.showSrcIp + res.data.url)
      }
    },
    getStatusCass(status) {
      let className = ''
      if (status == 0) {
        className = 'grayLine'
      } else if (status == 1) {
        className = 'greenLine'
      } else if (status == 2) {
        className = 'originLine'
      } else {
        className = 'grayLine'
      }
      return className
    },
    getStatus(status) {
      let str = ''
      switch (status) {
        case 0:
          str = '待处理'
          break
        case 1:
          str = '已确认'
          break
        case 2:
          str = '已忽略'
          break
        default:
      }
      return str
    },
    get_affect_assets(val, filter) {
      let arr = []
      if (val) {
        if (filter) {
          arr = val.slice(0, 2)
        } else {
          arr = val
        }
      } else {
        arr = []
      }
      return arr
    },
    eventInfo(row) {
      this.$router.push({
        path: '/eventInfo',
        query: {
          id: row.id,
          operate_company_id: this.currentCompany
        }
      })
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    Advanced() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.getData()
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    getData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      let params = {
        ...this.formInline,
        page: this.currentPage,
        per_page: this.pageSize,
        operate_company_id: this.currentCompany
      }
      this.loading = true
      eventWaringList(params)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
          this.affect_assets_num = res.data.affect_assets_num
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async updateStatusSave(icon, status, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.set_status = status ? status : ''
      this.formInline.operate_company_id = this.currentCompany
      if (this.checkedAll) {
        this.formInline.id = icon == 'one' ? [id] : []
      } else {
        this.formInline.id =
          icon == 'one'
            ? [id]
            : this.checkedArr.map((item) => {
                return item.id
              })
      }
      if (status) {
        // 存在代表忽略、确认
        let res = await handleEventWaring(this.formInline)
        if (res.code == 0) {
          this.$message.success('操作成功！')
          this.$nextTick(() => {
            this.$refs.eltable.clearSelection()
          })
          this.getData()
        }
      } else {
        let _this = this
        this.$confirm('确定删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'cloud_info_del_cancel',
          confirmButtonClass: 'cloud_info_del_sure',
          customClass: 'cloud_info_del',
          type: 'warning'
        })
          .then(async () => {
            let res = await delEventWaring(_this.formInline)
            if (res.code == 0) {
              this.$message.success('操作成功！')
              this.$nextTick(() => {
                this.$refs.eltable.clearSelection()
              })
              this.getData()
            }
          })
          .catch(() => {})
        setTimeout(() => {
          var del = document.querySelector('.cloud_info_del>.el-message-box__btns')
          del.children[0].id = 'cloud_info_del_cancel'
          del.children[1].id = 'cloud_info_del_sure'
        }, 50)
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        created_at_range: [],
        updated_at_range: [],
        keyword: '',
        event_status: ''
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleItem(row) {}
  }
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;

  .statisticnum {
    margin-right: 12px;
    font-size: 14px;
    font-weight: 400;
    color: #62666c;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 4px 10px;
    border-left: 2px solid #2677ff;

    .num {
      font-weight: 500;
      color: #2677ff;
      margin-right: 0px;
    }
  }

  .statisticnumBox > .statisticnum:last-child {
    margin-right: 0px;
  }

  .headerTitle {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;

    .left {
      height: 100%;
      display: flex;

      /deep/.el-textarea {
        width: 300px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;

        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }

        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }
  }

  /deep/.home_header {
    position: relative;
    height: 100%;

    .el-tabs__nav {
      padding-left: 20px;
    }

    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }

    .el-tabs__active-bar {
      left: 20px;
      width: 100%;
      background: #2677ff;
    }

    .el-tabs__header {
      margin: 0;
    }

    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }

    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }

    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;

      & > div {
        display: flex;
        align-items: center;

        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .el-input {
          width: 240px;
          margin-right: 12px;
        }

        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 4px;
          cursor: pointer;
        }
      }
    }

    .tableWrapFilter {
      margin: 0 20px;
      height: calc(100% - 180px) !important;
    }

    .tableWrap {
      margin: 0 20px;
      height: calc(100% - 129px);

      .ruleItemBox {
        display: flex;
        flex-wrap: wrap !important;
        align-items: center;
        padding: 0 12px !important;
      }

      .ruleItem,
      .ruleItemNum {
        line-height: 16px;
        padding: 2px 10px;
        margin: 5px 8px 5px 0px;
        background: #ffffff;
        border-radius: 14px;
        border: 1px solid #d1d5dd;
        white-space: pre-wrap;
      }

      .ruleItemNum {
        display: inline-block;
        background: #f0f3f8;
        border: 1px solid #dce5f3;
        cursor: pointer;
      }
    }

    .el-table {
      border: 0;
    }
  }

  /deep/.el-form {
    .el-textarea textarea {
      min-height: 270px !important;
    }
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
