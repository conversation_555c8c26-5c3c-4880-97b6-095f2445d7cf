<template>
  <div class="container">
    <div class="boxTwo1">
      <div class="eltableBox">
        <ul class="ulBox">
          <li class="ultitle">线索分类</li>
          <li
            v-for="(item, index) in tabList"
            :key="index"
            @click="changeTab(item.label)"
            class="clueList"
            :style="
              tabActive == item.label ? 'background: #EAEFF6;color:#2677FF;font-weight: 500;' : ''
            "
          >
            <span class="fangkuai" v-if="item.label == 0"></span>
            <span class="fangkuai fangkuai1" v-else-if="item.label == 1"></span>
            <span class="fangkuai fangkuai2" v-else-if="item.label == 2"></span>
            <span class="fangkuai fangkuai4" v-else-if="item.label == 6"></span>
            <span class="fangkuai fangkuai5" v-else-if="item.label == 5"></span>
            <span class="fangkuai fangkuai3" v-else></span>
            {{ getType(item.name) }}
          </li>
        </ul>
        <div class="myTable" v-loading="loading">
          <div class="myTableContent" ref="myTableContent">
            <clueTable
              ref="clueTable"
              v-loading="tableLoading"
              @scrollChangeTab="scrollChangeTab"
              :handleIsShow="true"
              @checkedArr="checkedArrFun"
              :tableData="tableData"
              :tableHeader="tableHeader"
            ></clueTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import clueTable from '../organization/table'
import { organizationCompanyClues, delNextCompany, orgCluesList } from '@/api/apiConfig/api.js'

export default {
  components: { clueTable },
  data() {
    return {
      loading: false,
      tableLoading: false,
      domain_num: 0,
      ip_num: 0,
      icp_num: 0,
      cert_num: 0,
      icon_num: 0,
      subdomain_num: 0,
      tableHeader: [
        {
          label: '线索名称',
          name: 'content',
          fixed: 'left'
        },
        {
          label: '创建时间',
          name: 'created_at',
          minWidth: '150px'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: '150px'
        }
      ],
      removeLoading: false,
      tabList: [
        {
          label: '6',
          name: 'IP段'
        },
        {
          label: '0',
          name: '域名'
        },
        {
          label: '2',
          name: 'ICP'
        },
        {
          label: '1',
          name: '证书'
        },
        {
          label: '5',
          name: '子域名'
        }
      ],
      tableData: [],
      tabActive: '6', // 左侧导航默认选中IP段：6
      checkedArr0: [],
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      checkedArrAll: []
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    async getData(obj) {
      this.tableArr = []
      // let { orgId, comId } = this.$route.query;
      let res = await orgCluesList(obj)
      if (res.code == 0) {
        this.domain_num = res.data.domain.length
        this.ip_num = res.data.ip.length
        this.icp_num = res.data.icp.length
        this.cert_num = res.data.cert.length
        this.icon_num = res.data.icon.length
        this.subdomain_num = res.data.subdomain.length
        this.tableData = this.tableArr
          .concat(res.data.domain)
          .concat(res.data.ip)
          .concat(res.data.icp)
          .concat(res.data.cert)
      }
    },
    // 统计根域、icp等
    async getTab(val) {
      let obj = {
        group_id: this.taskInfoData.group_id,
        data: {
          status: val, // 线索总表和获取初始线索是1，其余为0
          operate_company_id: this.currentCompany,
          detect_task_id: this.taskInfoData.id
        }
      }
      // let res = await tabNumClues(obj)
      if (res.code == 0) {
        this.tabTotal = 0
        if (res.data.clues_count.length != 0) {
          res.data.clues_count.forEach((val) => {
            if (val.type == '0') {
              this.domain_num = val.count
            }
            if (val.type == '1') {
              this.cert_num = val.count
            }
            if (val.type == '2') {
              this.icp_num = val.count
            }
            if (val.type == '3') {
              this.icon_num = val.count
            }
            if (val.type == '5') {
              this.subdomain_num = val.count
            }
            if (val.type == '6') {
              this.ip_num = val.count
            }
          })
          this.tabTotal =
            this.ip_num +
            this.domain_num +
            this.cert_num +
            this.icp_num +
            this.subdomain_num +
            this.icon_num
        }
      }
    },
    getType(val) {
      //tabActive
      if (val == '域名') {
        return `域名(${this.domain_num})`
      } else if (val == 'ICP') {
        return `ICP(${this.icp_num})`
      } else if (val == '证书') {
        return `证书(${this.cert_num})`
      } else if (val == 'ICON') {
        return `ICON(${this.icon_num})`
      } else if (val == '子域名') {
        return `子域名(${this.subdomain_num})`
      } else {
        return `IP段(${this.ip_num})`
      }
    },
    checkedArrFun(arr) {
      this.checkedArr0 = arr
        .filter((item) => {
          return item.type == 0
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr1 = arr
        .filter((item) => {
          return item.type == 1
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr2 = arr
        .filter((item) => {
          return item.type == 2
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr3 = arr
        .filter((item) => {
          return item.type == 3
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr4 = arr
        .filter((item) => {
          return item.type == 4
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr5 = arr
        .filter((item) => {
          return item.type == 5
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr6 = arr
        .filter((item) => {
          return item.type == 6
        })
        .map((item) => {
          return item.id
        })
      this.checkedArrAll = [
        ...this.checkedArr0,
        ...this.checkedArr1,
        ...this.checkedArr2,
        ...this.checkedArr3,
        ...this.checkedArr4,
        ...this.checkedArr5,
        ...this.checkedArr6
      ]
    },
    // 滚动改变tab线索分类选中
    scrollChangeTab(type) {
      this.tabActive = String(type)
    },
    //切换线索分类
    changeTab(val) {
      var arr = this.tableData.map((item) => {
        return item.type
      })
      var num = arr.indexOf(Number(val))
      if (num != -1) {
        // 获取行高
        let dataList = document.getElementsByClassName('borderWrap')[0].clientHeight
        // 滚动距离
        document.getElementsByClassName('clueContent')[0].scrollTop = dataList * num
        setTimeout(() => {
          this.tabActive = val
        }, 100)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 100%;
}
.boxTwo1 {
  height: calc(100% - 15px) !important;
  padding-top: 2px !important;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
}

.eltableBox {
  display: flex;
  // padding: 0px 20px 0px 0px;
  height: 100% !important;
  justify-content: space-between;
}

.ulBox {
  width: 160px;
  height: 100%;
  background: linear-gradient(180deg, #ffffff 0%, #eff5ff 100%);
  border-right: 1px solid #e9ebef;
  box-sizing: border-box;
  //   padding-top: 5px;
  padding: 10px 0;
  li {
    color: #62666c;
    cursor: pointer;
    height: 44px;
    background: rgba(234, 239, 246, 0);
    display: flex;
    align-items: center;
    padding: 0 10px;
  }
  .ultitle {
    font-weight: 500;
    color: #37393c;
    padding-left: 16px;
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
  }
  .fangkuai {
    width: 6px;
    height: 6px;
    background: #2677ff;
    box-shadow: 0px 0px 4px 0px rgba(38, 119, 255, 0.74);
    border-radius: 1px;
    margin: 0px 8px 0px 16px;
  }
  .fangkuai4 {
    background: #5346ff;
  }
  .fangkuai2 {
    background: #05d4a7;
  }
  .fangkuai1 {
    background: #13b7ff;
  }
  .fangkuai3 {
    background: #ec8f3c;
  }
  .fangkuaiKeyword {
    background: #ec5f5c;
  }
}
.myTable {
  flex: 1;
  padding: 0 20px;
  // padding: 10px !important;
  height: 100%;
  background-color: #fff;
  .myTableContent {
    // padding: 10px 0 10px 10px;
    height: calc(100% - 80px) !important;
  }
}
.tableLabel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 0px 0px 4px !important;
  margin-bottom: 10px !important;
  margin-top: 10px !important;
}
.tableLabel > div {
  display: flex;
  align-items: center;
  & > p {
    margin-right: 16px;
  }
  .el-input {
    width: 240px;
  }
  & > span {
    font-weight: 400;
    color: #2677ff;
    line-height: 20px;
    margin-left: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}
</style>
