<template>
  <div class="myBox1" v-loading="changeCompanyLoading">
    <div class="content">
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="rules"
        label-width="125px"
        style="width: 100%"
      >
        <div class="module findModule">
          <div class="top">
            <div class="label">
              <div class="blueBlock"></div>
              集团资产梳理设置
            </div>
            <div class="act" v-if="isShowModule" @click="unfoldOrCollapse('', false)"
              ><span>收起</span> <img :src="topArrow" alt=""
            /></div>
            <div class="act" v-else @click="unfoldOrCollapse('', true)"
              ><span>展开</span> <img :src="bottomArrow" alt="" />
            </div>
          </div>
          <div class="moduleBottom" v-if="isShowModule">
            <el-form-item label="" prop="company_name">
              <span slot="label"> 集团名称： </span>
              <el-autocomplete
                v-model="addForm.company_name"
                :fetch-suggestions="querySearchAsync"
                @select="handleSelect"
                placeholder="请选择"
              >
                <template slot-scope="{ item }">
                  <div class="name">{{ item.name }}</div>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item label="企业架构：" prop="companyName">
              <div class="titleTip">
                <span>共{{ totalCompanyData }}个企业</span>
                <el-button type="text" @click="goToOrgManage()">企业架构管理</el-button>
              </div>
              <div class="treeDiv">
                <div class="tree" v-if="treeData.length != 0">
                  <el-tree
                    empty-text=""
                    node-key="id"
                    :data="treeData"
                    :default-expanded-keys="[treeData[0].id]"
                    :props="defaultProps"
                    :load="loadNode"
                    lazy
                  ></el-tree>
                </div>
                <div class="tree" v-else>
                  <div class="emptyClass">
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-kong"></use>
                    </svg>
                    <span>暂无数据<span v-if="!addForm.company_name">，请先选择集团</span></span>
                  </div>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="companyName">
              <template slot="label">
                控股比例：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <p slot="content" style="line-height: 24px">
                    针对企业架构中的子企业自动按照控股比例查询并测绘，同时会将关联关系自动补充至企业架构管理中。
                  </p>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-input type="input" v-model="percent" placeholder="请输入控股比例">
                <span slot="suffix">%</span>
              </el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <div class="footer">
      <el-button class="normalBtn" type="primary" :loading="sureLoading" @click="startTask"
        >开始梳理</el-button
      >
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { organizationGroupList, nextCompany, organizationTask } from '@/api/apiConfig/api.js'

export default {
  data() {
    return {
      totalCompanyData: 0,
      currentSelectCompany: {},
      activeId: '',
      currentId: '',
      defaultProps: {
        children: 'children',
        label: 'company_name',
        isLeaf: 'is_leaf'
      },
      treeData: [],
      sureLoading: false,
      rules: {},
      addForm: {},
      isShowModule: true,
      bottomArrow: require('@/assets/images/bottomArrow.png'),
      topArrow: require('@/assets/images/topArrow.png'),
      changeCompanyLoading: false,
      noData: false,
      percent: ''
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goToOrgManage() {
      this.$router.push({ path: '/organization', query: { from: true } })
      sessionStorage.setItem('menuId', '7-18')
      this.changeMenuId('7-18')
    },
    // 选择集团企业
    async handleSelect(val) {
      this.activeId = val.id
      this.currentSelectCompany = val
      let level_arr = this.currentSelectCompany.level_arr || {}
      if (Object.values(level_arr).length !== 0) {
        this.totalCompanyData = Object.values(level_arr).reduce((prev, cur) => prev + cur) + 1
      }
      // 获得子企业
      let res1 = await nextCompany({
        organization_group_id: val.id,
        no_page: '1', // 不分页
        parent_id: val.company.id,
        operate_company_id: this.currentCompany
      })
      if (res1.code == 0) {
        val.company.children = res1.data.items
        this.treeData = [val.company]
      }
    },
    async loadNode(node, resolve) {
      if (!this.activeId) return resolve([])
      if (node.level == 0) return resolve(this.treeData)
      let res = await nextCompany({
        organization_group_id: this.activeId,
        no_page: '1',
        parent_id: node.data.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        resolve(res.data)
      }
    },
    async querySearchAsync(queryString, cb) {
      let formInline = {}
      formInline.operate_company_id = this.currentCompany
      formInline.name = queryString
      // this.tableLoading = true
      // this.totalCompanyData = 1
      this.noData = false
      let res = await organizationGroupList(formInline).catch(() => {
        this.tableData = []
      })
      if (res.code == 0) {
        if (res.data && res.data.length != 0) {
          this.noData = false

          let resData = res.data.map((item) => ({
            name: item.name,
            value: item.name,
            id: String(item.id),
            level_arr: item.level_arr,
            company: item.company
          }))
          cb(resData)
        } else {
          this.noData = true
          cb([])
          // cb([{value: '无匹配数据',id:'',name:'无匹配数据'}])
        }
      }
    },
    async startTask() {
      this.sureLoading = true
      // 参数
      let res = await organizationTask({
        percent: this.percent,
        organization_group_id: this.activeId,
        operate_company_id: this.currentCompany,
        name: this.currentSelectCompany.name
      }).catch(() => {
        this.sureLoading = false
      })
      this.sureLoading = false
      if (res.code == 0) {
        // 切换进度条页面
        this.$emit('changeComponent', 'groupTask2', res.data)
      }
    },
    unfoldOrCollapse(type, isShow) {
      this['isShow' + type + 'Module'] = isShow
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  box-shadow: none !important;
}
.myBox1 {
  position: relative;
  height: calc(100% - 56px);
  width: 100%;

  .el-input__suffix-inner > i {
    margin-top: 0 !important;
  }

  .content {
    // width: calc(100% - 32px);
    height: 100%;

    // height: calc(100% - 40px);
    padding: 0 16px;
    overflow: auto;

    .module {
      width: 100%;
      margin-bottom: 20px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      background: #ffffff;
      border-radius: 4px;

      .top {
        height: 46px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e9ebef;

        // margin-bottom: 32px;
        // span {
        //   color: #a1b5d8;
        // }

        .label {
          display: flex;
          align-items: center;
          height: 2px;
          font-size: 16px;
          font-weight: 600;
          line-height: 16px;
          .blueBlock {
            display: inline-block;
            width: 4px;
            height: 16px;
            border-radius: 0 2px 2px 0;
            margin-right: 12px;
            background-color: #2677ff;
          }
        }

        .act {
          // line-height: 46px;
          display: flex;
          align-items: center;
          color: #2677ff;
          &:hover {
            cursor: pointer;
          }

          img {
            margin-right: 16px;
            margin-left: 4px;
          }
        }
      }

      .moduleBottom {
        // margin-top: 32px;
        padding: 32px 0;

        .inputNumber {
          width: 100%;
        }
        /deep/.el-autocomplete {
          width: 100% !important;
        }

        /deep/.el-select {
          width: 100% !important;

          .el-input__icon {
            // height: 30px;
            margin-top: 0;
          }
        }

        /deep/.el-form-item:last-child {
          margin-bottom: 0;
        }
      }
    }

    .findModule {
      // background:  url('../../assets/images/taskFirst1.png') no-repeat top center;
      background-image: url(../../../assets/images/taskFirst1.png);
      background-repeat: no-repeat;
      background-size: 100% auto;
      background-position: top center;
      background-color: #fff;
    }
  }
  .footer {
    // position: fixed;
    // bottom: 0;
    // left: 0;
    // width: calc(100% - 220px) !important;
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
    .normalBtn {
      width: 254px;
    }
  }
}
/deep/ .el-form {
  padding: 0 !important;

  .el-form-item {
    width: 766px;
    margin-left: 40px;

    .el-form-item__label {
      display: flex;
      align-items: center;
      text-align: left;

      img {
        &:hover {
          cursor: pointer;
        }

        // margin-left: px;
      }

      &::before {
        display: none;
      }
    }

    &.is-required {
      position: relative;

      .el-form-item__content::after {
        position: absolute;
        right: -16px;
        top: 0;
        content: '*';
        font-size: 24px;
        // width: 10px;
        color: #f56c6c;
      }
    }
  }
}

/deep/.companyDiv {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
  .select-button {
    position: relative;
    width: 100%;
    height: 32px;
    #unit_sure {
      position: absolute;
      top: 1px;
      right: 1px;
      height: 30px;
      margin-left: 0px !important;
      border-radius: 0px 4px 4px 0px;
      border: 0;
    }
  }
  .el-autocomplete {
    width: 83% !important;

    .el-input__inner {
      cursor: text;
      height: 32px;
      border-radius: 4px !important;
    }

    .el-form-item__content .el-input__icon {
      line-height: 36px !important;
    }
  }
  .el-button {
    border: 0;
    padding: 0 15px !important;
  }
  .el-select {
    position: relative;
    .el-input__suffix {
      display: none;
    }
    .el-input__inner {
      padding-right: 125px;
    }
  }
}
.treeDiv {
  position: relative;
  height: 434px;
  background: #f5f8fc;
  border: 1px solid #e9ebef;
  border-radius: 4px;
  overflow: auto;
  padding: 6px 8px;
  // .tree{
  //   position: relative;
  // }
}
/deep/.el-tree {
  background-color: transparent;
  .el-tree-node > .el-tree-node__content {
    height: 32px;
  }
  .expanded {
    color: #62666c;
  }
}

.titleTip {
  display: flex;
  justify-content: space-between;
}
.emptyClass {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #ffffff 0%, #f0f6ff 100%);
  backdrop-filter: blur(8px);
  svg {
    display: inline-block;
    font-size: 90px;
  }
  p {
    margin-top: 12px;
    color: #62666c;
  }
}
/deep/.el-autocomplete-suggestion__wrap {
  padding: 5px 0;
  ul li {
    pointer-events: none; // 阻止可点击事件
    .default {
      text-align: center;
      color: #999;
    }
    &:hover {
      background-color: #fff;
    }
  }
}
</style>
