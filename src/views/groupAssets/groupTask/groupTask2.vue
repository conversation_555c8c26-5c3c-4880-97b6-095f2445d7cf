<template>
  <div class="myBox" v-if="isShowProgress">
    <div :class="progressBar ? 'boxTwo' : 'boxTwo1'">
      <div class="eltableBox">
        <ul class="ulBox">
          <li class="ultitle">线索分类</li>
          <li
            v-for="(item, index) in tabList"
            :key="index"
            @click="changeTab(item.label)"
            class="clueList"
            :style="
              tabActive == item.label ? 'background: #EAEFF6;color:#2677FF;font-weight: 500;' : ''
            "
          >
            <span class="fangkuai" v-if="item.label == 0"></span>
            <span class="fangkuai fangkuai1" v-else-if="item.label == 1"></span>
            <span class="fangkuai fangkuai2" v-else-if="item.label == 2"></span>
            <span class="fangkuai fangkuai4" v-else-if="item.label == 6"></span>
            <span class="fangkuai fangkuai5" v-else-if="item.label == 5"></span>
            <span class="fangkuai fangkuai3" v-else></span>
            {{ getType(item.name) }}
          </li>
        </ul>
        <div class="myTable" v-loading="loading">
          <div class="tableLabel">
            <div>
              <div>{{ stepInfoArr[5].tableText }}</div>
              <div style="color: rgba(98, 102, 108, 0.7); margin-left: 8px; font-size: 12px">{{
                tableName
              }}</div>
            </div>
            <div>
              <el-button
                class="normalBtn"
                v-if="accuracyIndex >= 0"
                type="primary"
                @click="insertClueShow"
                id="unit_del"
                >新增</el-button
              >
              <el-button
                class="normalBtn"
                :loading="removeLoading"
                type="primary"
                @click="removeFun"
                id="unit_del"
                >删除</el-button
              >
            </div>
          </div>
          <div class="myTableContent" ref="myTableContent">
            <clueTable
              ref="clueTable"
              taskType="organization"
              @editCompanyName="editCompanyName"
              v-loading="tableLoading"
              @scrollChangeTab="scrollChangeTab"
              :handleIsShow="true"
              :onekey="onekey"
              @checkedArr="checkedArrFun"
              :tableData="tableData"
              :tableHeader="tableHeader"
              :progressBar="isShowProgress"
            ></clueTable>
          </div>
        </div>
      </div>
      <div class="footer">
        <el-button
          class="normalBtn"
          type="primary"
          :loading="assetsBtnLoading"
          @click="expandingTwo"
          id="unit_clue"
          >集团资产获取</el-button
        >
      </div>
    </div>
    <!-- 新建线索 -->
    <clueDialog
      taskType="organization"
      :isNotGroup="true"
      :dialogFormVisibleInsert="dialogFormVisibleInsert"
      :is_detect="{ status: accuracyIndex }"
      :taskId="taskInfoData.id"
      :currentGroupId="taskInfoData.group_id"
      :tabActiveName="tabActiveName"
      :company_name="taskInfoData.name"
      :ruleForm="clueRuleForm"
      @insertClueSave="insertClueSave"
    />
  </div>
  <div v-else class="fourthBox">
    <div class="fourthTitle">
      <div class="grayBox">
        <div>
          <!-- ><span style="color: #2677FF;font-weight: 600;">线索扩展</span> -->
          <span>资产线索获取</span
          ><img src="../../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >依据企业名称收集线索信息</span
          >
        </div>
      </div>
    </div>
    <div class="tu yuTu tuThird" lazy></div>
    <div v-if="!againIsShow" style="margin: 12px 8px 10px 0px"
      ><span style="margin: 0px 8px 0px 0px">{{ headText }}</span
      ><span style="color: #2677ff">{{ currentPercent }}%</span></div
    >
    <div v-if="!againIsShow" class="fourthProgressBox"
      ><el-progress
        :stroke-width="14"
        :percentage="currentPercent"
        style="margin-bottom: 20px"
      ></el-progress
    ></div>
    <div v-if="againIsShow" class="unitError">
      <p class="unitText"><i class="el-icon-warning"></i>{{ headText }}</p>
    </div>
  </div>
</template>
<script>
import * as animationData from '../../../assets/images/data.json'
import Lottie from 'vue-lottie/src/lottie.vue'
import clueTable from '../../unit_surveying/taskSecondTable.vue'
import clueDialog from '../../cloudRecommend/clueInsertDialog.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import {
  organizationTaskInfo,
  orgTaskRecommendAssets,
  orgTaskClueList,
  orgTaskDeleteClue
} from '@/api/apiConfig/api.js'
import { cluesUpdateSync } from '@/api/apiConfig/surveying.js'
import { exportCluesListV1 } from '@/api/apiConfig/clue.js'

export default {
  components: { Lottie, clueDialog, clueTable },
  props: ['taskInfo'],
  data() {
    return {
      assetsBtnLoading: false,
      isShowProgress: false,
      suspectedTableData: [],
      suspectedClueVisible: false,
      noteLoading: false,
      onekey: false,
      againIsShow: false,
      btnLoading: false,
      currentPercent: 0, //进度条
      defaultOptions: {
        //json动画
        animationData: animationData.default
      },
      dialogFormVisibleInsert: false,
      tabActiveName: '0',
      againIsTrue: true, // 重新获取的按钮
      clueRuleForm: {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      },
      cardContent: [],
      progressBar: true, //是否显示进度条
      tabTotal: 0,
      total: 0,
      currentPage: 1,
      pageSize: 20,
      pageSizeArr: [10, 30, 50, 100],
      domain_num: 0,
      ip_num: 0,
      icp_num: 0,
      cert_num: 0,
      icon_num: 0,
      subdomain_num: 0,
      tableName: '可手动忽略非本企业的线索内容',
      tabActive: '6', // 左侧导航默认选中IP段：6
      tabList: [
        {
          label: '6',
          name: 'IP段'
        },
        {
          label: '0',
          name: '域名'
        },
        {
          label: '2',
          name: 'ICP'
        },
        {
          label: '1',
          name: '证书'
        },
        {
          label: '5',
          name: '子域名'
        }
      ],
      tableLoading: false,
      tableData: [],
      tableArr: [],
      tableHeader: [
        {
          label: '线索',
          name: 'content',
          fixed: 'left'
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          minWidth: '150px'
        },
        {
          label: '线索源',
          name: 'chain_list',
          fixed: 'left'
        }
      ],
      clueExpansion: false, //扩展线索框
      expandType: '2', //线索扩展类型，1、2、3：标准，专家，智能
      accuracyIndex: 0,
      stepInfoArr: [
        {
          clueSecondTitle: '初始线索',
          clueText: '成功获取企业初始线索',
          clueTitle: '依据企业名称获取单位资产初始线索',
          tableText: '初始线索'
        },
        {
          clueSecondTitle: '依据IP段扩展',
          clueText: '成功获取企业初始线索',
          clueTitle:
            '通过原始线索中的IP段进行线索扩展，扩展线索已自动补充至线索库，可通过域名继续扩展',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '依据域名扩展',
          clueText: '成功获取企业初始线索',
          clueTitle: '通过域名线索扩展，扩展线索已自动补充至线索库，可通过ICP继续扩展',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '依据ICP扩展',
          clueText: '成功获取企业初始线索',
          clueTitle: '通过ICP线索扩展，扩展线索已自动补充至线索库，可通过证书继续扩展',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '依据证书扩展',
          clueText: '成功获取企业初始线索',
          clueTitle: '通过证书线索扩展，扩展线索已自动补充至线索库，扩展完成后生成线索库',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '生成线索总表',
          clueText: '共生成资产线索',
          clueTitle: '依据原始线索和扩展线索生成线索总表，可依据线索总表进行资产推荐',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '线索扩展',
          clueText: '共生成资产线索',
          clueTitle: '依据原始线索扩展其他线索，可生成线索总表',
          tableText: '资产线索'
        }
      ],
      task_id: '',
      group_id: '',
      user: {
        role: ''
      },
      userInfo: '',
      checkedArr0: [],
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      tabActiveNameStatus: 1,
      setTimer: null,
      loading: false,
      removeLoading: false,
      exportLoading: false,
      expandTypeHead: false,
      headText: '线索扩展中...'
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.user.role == 2) {
        // this.$refs['eltable'].clearSelection();
        this.tableData = []
        this.tableArr = []
        sessionStorage.removeItem('scrollTop')
      }
    },
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    taskInfo: {
      handler(val) {
        this.taskInfoData = val
        // this.getInit()
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    sessionStorage.removeItem('scrollTop')
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    // 获取任务需要的参数
    async getTaskRes() {
      this.loading = true
      let res = await organizationTaskInfo({
        id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.taskInfoData = res.data
        this.getTable(0)
      }
    },
    getData() {
      this.getInit()
    },
    // 批量以及单独修改企业名称
    editCompanyName(type, id) {
      let clueData = null
      if (type == 'more') {
        clueData = [
          ...this.checkedArr0,
          ...this.checkedArr1,
          ...this.checkedArr2,
          ...this.checkedArr3,
          ...this.checkedArr4,
          ...this.checkedArr5,
          ...this.checkedArr6
        ]
        if (clueData.length == 0) {
          this.$message.error('请选择要编辑的数据！')
          return
        }
      } else {
        clueData = [id]
      }
      this.$prompt('请输入新的企业名称', `${type == 'more' ? '批量' : ''}编辑企业名称`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'cloud_info_del_sure',
        cancelButtonClass: 'cloud_info_del_cancel',
        inputValidator: (value) => {
          if (value) {
            return true
          } else {
            return false
          }
        },
        inputErrorMessage: '请输入',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let res = await cluesUpdateSync({
              operate_company_id: this.currentCompany,
              ids: clueData,
              clue_company_name: instance.inputValue.trim()
            })
            if (res.code == 0) {
              done()
              this.checkedArr0 = []
              this.checkedArr1 = []
              this.checkedArr2 = []
              this.checkedArr3 = []
              this.checkedArr4 = []
              this.checkedArr5 = []
              this.checkedArr6 = []
              this.getTable(this.tabActiveNameStatus)
              this.getTab(this.tabActiveNameStatus)
            }
            setTimeout(() => {
              instance.confirmButtonLoading = false
            }, 300)
          } else {
            done()
          }
        }
      }).then(({ value }) => {
        this.$message({
          type: 'success',
          message: '修改成功'
        })
      })
    },
    // 滚动改变tab线索分类选中
    scrollChangeTab(type) {
      this.tabActive = String(type)
    },
    //切换线索分类
    changeTab(val) {
      var arr = this.tableData.map((item) => {
        return item.type
      })
      var num = arr.indexOf(Number(val))
      if (num != -1) {
        // 获取行高
        let dataList = document.getElementsByClassName('borderWrap')[0].clientHeight
        // 滚动距离
        document.getElementsByClassName('clueContent')[0].scrollTop = dataList * num
        setTimeout(() => {
          this.tabActive = val
        }, 100)
      }
    },
    getType(val) {
      //tabActive
      if (val == '域名') {
        return `域名(${this.domain_num})`
      } else if (val == 'ICP') {
        return `ICP(${this.icp_num})`
      } else if (val == '证书') {
        return `证书(${this.cert_num})`
      } else if (val == 'ICON') {
        return `ICON(${this.icon_num})`
      } else if (val == '子域名') {
        return `子域名(${this.subdomain_num})`
      } else {
        return `IP段(${this.ip_num})`
      }
    },
    insertClueShow() {
      this.clueRuleForm = {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: ''
      }
      this.dialogFormVisibleInsert = true
      if (this.tabActiveName == 3) {
        this.clueRuleForm.content = []
      } else {
        this.clueRuleForm.content = ''
      }
      if (this.tabActiveName == '3') {
        this.$set(this.clueRuleForm, 'way', 1)
      } else {
        this.$set(this.clueRuleForm, 'way', 0)
      }
      this.fileList = []
    },
    // 线索保存
    insertClueSave(isClose) {
      this.dialogFormVisibleInsert = false
      this.clueRuleForm = {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      }
      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []
      if (!isClose) {
        // 关闭弹层不需要执行
        if (this.accuracyIndex == 0 || this.accuracyIndex == 5) {
          // 初始线索获取，生成线索总表status:1
          this.getTable(1)
        } else {
          // 其余情况0
          this.getTable(0)
        }
      }
    },
    __transferChecked() {
      // 导出以及标记黑名单的数据结构转换
      let clueData = [
        {
          id: this.checkedArr0,
          type: '0',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr1,
          type: '1',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr2,
          type: '2',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr3,
          type: '3',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr4,
          type: '4',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr5,
          type: '5',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr6,
          type: '6',
          is_all: '',
          keyword: ''
        }
      ]
      let obj = {
        tab_status: this.tabActiveNameStatus,
        group_id: this.taskInfoData.group_id,
        data: clueData,
        keyword: {
          domain_keyword: '',
          cert_keyword: '',
          icp_keyword: '',
          key_keyword: '',
          ip_keyword: ''
        },
        operate_company_id: this.currentCompany
      }
      return obj
    },
    async exportList() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要导出的数据！')
        return
      }
      let obj = this.__transferChecked()
      this.exportLoading = true
      let res = await exportCluesListV1(obj).catch(() => {
        this.exportLoading = false
      })
      if (res.code == 0) {
        this.exportLoading = false
        this.checkedArr0 = []
        this.checkedArr1 = []
        this.checkedArr2 = []
        this.checkedArr3 = []
        this.checkedArr4 = []
        this.checkedArr5 = []
        this.checkedArr6 = []
        this.download(this.showSrcIp + res.data.url)
      }
    },
    async getInit() {
      if (
        !this.taskInfoData ||
        (this.taskInfoData &&
          (!this.taskInfoData.clue_progress || Number(this.taskInfoData.clue_progress) < 100))
      ) {
        this.isShowProgress = false // 显示进度
      } else if (this.taskInfoData && Number(this.taskInfoData.clue_progress) >= 100) {
        // 已完成
        this.getTaskRes()
        this.isShowProgress = true // 隐藏进度
      }
      this.task_id = this.taskInfoData.id
      this.group_id = this.taskInfoData.group_id
      this.currentPercent = parseFloat(this.taskInfoData.clue_progress) || 0
      this.tabActive = '6'
      this.tabActiveNameStatus = 1
    },
    async handleMessage(res, o) {
      //处理接收到的信息
      // 根据公司名称获取初始线索
      if (
        res.cmd == 'organization_detect_clue_process' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        //极速扩展
        this.accuracyIndex = 4
        if (res.data.progress == 100) {
          if (this.isShowProgress) return
          this.currentPercent = 99
          setTimeout(() => {
            this.currentPercent = 100
            this.isShowProgress = true
            this.getTaskRes()
          }, 1000)
          clearInterval(this.setTimer)
          this.setTimer = null
        } else {
          this.isShowProgress = false
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
    },
    // 获取所有线索列表
    async getTable(val) {
      this.tableArr = []
      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []
      let obj = {
        keyword: '',
        status: val, // 线索总表和获取初始线索是1，其余为0
        group_id: this.taskInfoData.group_id,
        organization_task_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany,
        // type: this.tabActive,
        is_whole: 1,
        organization_task_id: this.taskInfoData.id
      }
      // this.tableLoading = true
      this.loading = true

      let res = await orgTaskClueList(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        let data = res.data
        this.tableData_domain = []
        this.tableData_icp = []
        this.tableData_cert = []

        this.tableData = this.tableArr
          .concat(res.data.ip)
          .concat(res.data.domain)
          .concat(res.data.icp)
          .concat(res.data.cert)
          .concat(res.data.subdomain)
        this.tableData_domain = res.data.domain
        this.tableData_icp = res.data.icp
        this.tableData_cert = res.data.cert
        this.domain_num = data.domain.length
        this.ip_num = data.ip.length
        this.icp_num = data.icp.length
        this.cert_num = data.cert.length
        this.icon_num = data.icon.length
        this.subdomain_num = data.subdomain.length

        this.onekey = false // 表格数据更新重置
        if (sessionStorage.getItem('scrollTop')) {
          setTimeout(() => {
            // 获取行高
            let dataList = document.getElementsByClassName('borderWrap')[0].clientHeight
            // 滚动距离
            document.getElementsByClassName('clueContent')[0].scrollTop =
              sessionStorage.getItem('scrollTop')
            let num = parseInt(sessionStorage.getItem('scrollTop') / dataList)
            let type = this.tableData[num].type //当前类型
            this.tabActive = type
            this.loading = false
          }, 500)
        } else {
          setTimeout(() => {
            if (
              document.getElementsByClassName('clueContent') &&
              document.getElementsByClassName('clueContent')[0]
            ) {
              document.getElementsByClassName('clueContent')[0].scrollTop = 0
            }
            if (this.tableData.length != 0) {
              this.tabActive = String(this.tableData[0].type)
            }
            this.loading = false
          }, 500)
        }
      }
    },
    checkedArrFun(arr) {
      this.checkedArr0 = arr
        .filter((item) => {
          return String(item.type) == 0
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr1 = arr
        .filter((item) => {
          return String(item.type) == 1
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr2 = arr
        .filter((item) => {
          return String(item.type) == 2
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr3 = arr
        .filter((item) => {
          return String(item.type) == 3
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr4 = arr
        .filter((item) => {
          return String(item.type) == 4
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr5 = arr
        .filter((item) => {
          return String(item.type) == 5
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr6 = arr
        .filter((item) => {
          return String(item.type) == 6
        })
        .map((item) => {
          return item.id
        })
    },
    async removeFun() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let clueData = [
        ...this.checkedArr0,
        ...this.checkedArr1,
        ...this.checkedArr2,
        ...this.checkedArr3,
        ...this.checkedArr4,
        ...this.checkedArr5,
        ...this.checkedArr6
      ]
      this.removeMore(clueData)
    },
    removeMore(clueData) {
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      })
        .then(async () => {
          this.removeLoading = true
          let obj = {
            group_id: this.taskInfoData.group_id,
            id: clueData,
            operate_company_id: this.currentCompany,
            organization_task_id: this.taskInfoData.id
          }
          let res = await orgTaskDeleteClue(obj).catch(() => {
            this.removeLoading = false
          })
          if (res.code == 0) {
            this.removeLoading = false
            this.$message.success('操作成功！')
            this.checkedArr0 = []
            this.checkedArr1 = []
            this.checkedArr2 = []
            this.checkedArr3 = []
            this.checkedArr4 = []
            this.checkedArr5 = []
            this.checkedArr6 = []
            this.getTable(this.tabActiveNameStatus)
            this.$refs.suspectedCluesOperation &&
              this.$refs.suspectedCluesOperation.$refs.eltable.clearSelection()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.unit_del>.el-message-box__btns')
        del.children[0].id = 'unit_del_cancel'
        del.children[1].id = 'unit_del_sure'
      }, 50)
    },
    async expandingTwo() {
      this.assetsBtnLoading = true
      this.currentPercent = 0
      // 扩展
      let obj = {
        group_id: this.taskInfoData.group_id,
        organization_task_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      let res = await orgTaskRecommendAssets(obj).catch(() => {
        this.assetsBtnLoading = false
      })
      if (res.code == 0) {
        this.$emit('changeComponent', 'groupTask3')
      }
      this.assetsBtnLoading = false
    }
  },
  beforeDestroy() {
    clearInterval(this.setTimer)
    this.setTimer = null
    sessionStorage.removeItem('scrollTop')
  }
}
</script>
<style lang="less" scoped>
/deep/.el-dialog__body {
  min-height: 110px !important;
}
.dialog-body > div > div {
  color: #62666c;
  margin-bottom: 32px;
  .el-radio-group {
    font-size: 16px;
  }
  /deep/.el-radio {
    font-size: 16px;
    font-weight: bold;
    .el-radio__label {
      font-size: 16px;
    }
  }
}
.cardClass {
  height: 70%;
  margin-top: 2%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  img {
    display: block;
    width: 120px;
    height: 80px;
  }
  .cardWrap {
    height: 160px;
    width: 49%;
    margin-bottom: 15px;
    background: url('../../../assets/images/fiveBg.png');
    background-size: cover;
    .el-card {
      position: relative;
      height: 100%;
      width: 100%;
      padding: 20px;
      box-sizing: border-box;
      background: transparent;
      .title {
        color: rgba(55, 57, 60, 1);
        font-weight: 500;
        font-size: 14px;
        margin-bottom: 12px;
      }
      .content {
        width: 68%;
        line-height: 24px;
        color: rgba(98, 102, 108, 1);
      }
      .imgBot {
        position: absolute;
        right: 0px;
        bottom: 4px;
      }
    }
  }
}
.el-radio-group {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.modelShow {
  color: #909399;
}
.el-table .cell {
  overflow: hidden !important;
  white-space: nowrap !important;
}
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}

.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.suspected {
  color: #f2b824;
  background: rgba(248, 193, 54, 0.16);
  border-radius: 2px;
  border: 1px solid #f8c136;
  padding: 2px 6px;
}
/deep/.el-icon-warning {
  color: #e94747;
  font-size: 16px;
  vertical-align: middle;
  margin-right: 2px;
}
.eltableBox {
  display: flex;
  padding: 0px 20px 0px 0px;
  height: 100% !important;
  justify-content: space-between;
}
.ulBox {
  width: 160px;
  height: 100%;
  background: rgba(255, 255, 255, 0.36);
  border-right: 1px solid #e9ebef;
  box-sizing: border-box;
  padding-top: 5px;
  li {
    color: #62666c;
    cursor: pointer;
    height: 44px;
    background: rgba(234, 239, 246, 0);
    display: flex;
    align-items: center;
  }
  .ultitle {
    font-weight: 500;
    color: #37393c;
    padding-left: 16px;
  }
}
.fangkuai {
  width: 6px;
  height: 6px;
  background: #2677ff;
  box-shadow: 0px 0px 4px 0px rgba(38, 119, 255, 0.74);
  border-radius: 1px;
  margin: 0px 8px 0px 16px;
}
.fangkuai4 {
  background: #5346ff;
}
.fangkuai2 {
  background: #05d4a7;
}
.fangkuai1 {
  background: #13b7ff;
}
.fangkuai3 {
  background: #ec8f3c;
}
.fangkuai5 {
  background: #ff46de;
}
.myTable {
  width: calc(100% - 170px);
  padding: 0px !important;
  height: calc(100% - 12px) !important;
}
.myTableContent {
  height: calc(100% - 46px);
  background-color: #fff;
}
/deep/.el-table {
  border: none !important;
}
.eltable_domain {
  border-left: 4px solid #2677ff !important;
  border-radius: 4px 0px 0px 4px;
}
.eltable_ip {
  border-left: 4px solid #1059d5 !important;
}
/deep/.boxTwo1 {
  height: calc(100% - 56px) !important;
  padding-top: 2px !important;
}
/deep/.boxTwo {
  height: calc(100% - 56px) !important;
  padding-top: 2px !important;
}
.eltable_icp {
  border-left: 4px solid #05d4a7 !important;
}
.eltable_cert {
  border-left: 4px solid #13b7ff !important;
}
.eltable_icon {
  border-left: 4px solid #ec8f3c !important;
}
.tableLabel {
  padding: 0px 0px 0px 4px !important;
  margin-bottom: 10px !important;
  margin-top: 10px !important;
}
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.tuThird {
  background-image: url('../../../assets/images/dongtu1.png');
}
.fourthBox {
  height: 100%;

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .fourthTitle {
    position: absolute;
    top: 20px;
    left: 0px;
  }
  .fourthProgressBox {
    width: 400px;
  }
  .fourthProgressBox /deep/.el-progress__text {
    display: none;
  }

  /deep/.el-progress {
    position: relative;
    .el-progress-bar {
      padding-right: 0px !important;
    }
    .el-progress__text {
      font-size: 14px !important;
      color: #2677ff;
      position: absolute;
      bottom: 23px;
      right: 0px;
    }
  }
}

.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
// .boxTwo {
//   height: calc(100% - 208px) !important;
// }
.myBox {
  height: 100%;
}
.Box {
  height: 100%;
}

// /deep/.box {
//     // background: linear-gradient(90deg, #F0F6FF 0%, #FFFFFF 100%);
//     background-repeat: no-repeat;
//     // width: 100%;
//     background-size: 100% 100%;
//     padding: 0 0 0 16px;
// }

.tableLabel {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #37393c;
  justify-content: space-between;
  // margin-bottom: 16px;
}
.tableLabel > div {
  display: flex;
  align-items: center;
  & > p {
    margin-right: 16px;
  }
  .el-input {
    width: 240px;
  }
  & > span {
    font-weight: 400;
    color: #2677ff;
    line-height: 20px;
    margin-left: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}

/deep/.el-pagination {
  background: transparent !important;
  box-shadow: none !important;
}
.myTable {
  padding: 0px 20px;
  // height: calc( 100% - 165px )
  height: calc(100% - 178px);
}
/deep/.boxTwo {
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  padding-top: 22px;
  height: calc(100% - 213px);
}
/deep/.boxTwo1 {
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  // padding-top: 22px;
  height: 100%;
}
/deep/.footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  background: #ffffff;
  box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
}
/deep/.tu {
  width: 120px !important;
  height: 120px !important;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position-y: 0px;
}
</style>
