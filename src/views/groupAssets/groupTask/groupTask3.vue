<template>
  <div class="taskContainer" v-loading="loading">
    <div v-if="!isShowProgress" class="container">
      <div class="box">
        <div class="fourthTitle">
          <div class="grayBox">
            <div v-if="pageIcon == 'predict'">
              <span><span style="color: #2677ff; font-weight: 600">信任等级</span></span
              ><img src="../../../assets/images/triangle.png" alt="" class="graySan" /><span
                class="graylabel"
                >通过AI智能模型获取资产可信度等级，依据不同信任度进行资产分类入账</span
              >
            </div>
            <div v-if="pageIcon == 'scan'">
              <span
                ><span style="color: #2677ff; font-weight: 600">{{
                  taskInfo.is_scan == 1 ? '资产扫描' : '资产入账'
                }}</span></span
              ><img src="../../../assets/images/triangle.png" alt="" class="graySan" /><span
                class="graylabel"
                >通过AI智能模型获取资产可信度等级，依据不同信任度进行资产分类入账</span
              >
            </div>
            <div v-if="pageIcon == 'recommend'">
              <span><span style="color: #2677ff; font-weight: 600">推荐资产</span></span
              ><img src="../../../assets/images/triangle.png" alt="" class="graySan" /><span
                class="graylabel"
                >获取企业云端资产信息</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="boxTwo1">
        <div class="tableLabel">
          <div>
            <div style="margin-right: 10px">资产信息</div>
            <div class="confirmBox" v-if="pageIcon != 'recommend'" style="margin-right: 16px">
              <el-radio-group
                v-model="tabActive"
                @change="changeTab(tabActive, 'isTab', 'yesLoading')"
              >
                <el-radio-button
                  :label="item.level"
                  v-for="(item, index) in tabList[pageIcon]"
                  :key="index"
                  >{{ item.name }} ({{ tabCount[item.icon] }})</el-radio-button
                >
              </el-radio-group>
            </div>
            <el-input
              placeholder="请输入关键字检索"
              v-model="formInline.keyword"
              @keyup.enter.native="checkFuncList"
            >
              <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
              <el-tooltip
                slot="prepend"
                class="item"
                effect="dark"
                content="支持检索字段：IP、域名、URL、网站标题、ICP、证书"
                placement="top"
                :open-delay="100"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-input>
            <span
              @click="pageIcon == 'scan' ? (highCheckScanDialog = true) : (highCheckdialog = true)"
              id="account_filter"
              style="width: 80px; display: inline-block"
              ><img
                src="@/assets/images/filter.png"
                alt=""
                style="width: 16px; vertical-align: middle; margin-right: 3px"
              />高级筛选</span
            >
          </div>
          <div>
            <!-- <el-checkbox v-if="pageIcon != 'scan'" class="checkboxAll" id="keyword_all" v-model="checkedAll">选择全部</el-checkbox> -->
            <!-- <el-button v-if="pageIcon != 'scan'" class="normalBtnRe" type="primary" @click="exportList">导出</el-button> -->
            <el-button
              v-if="pageIcon != 'scan'"
              class="normalBtnRe"
              type="primary"
              @click="delAssets"
              >删除</el-button
            >
            <el-button
              v-if="pageIcon == 'scan'"
              class="normalBtnRe"
              type="primary"
              @click="delScanAssets"
              >删除</el-button
            >

            <!-- <el-button
              class="normalBtn"
              type="primary"
              @click="ruleScreenDialogVis = true"
              >规则筛选</el-button
            > -->
          </div>
        </div>
        <hightFilter
          id="hightFilter"
          :highTabShow="highTabShow"
          :highlist="highlist"
          :total="total"
          pageIcon="keyword1"
          @highcheck="highCheck"
        ></hightFilter>
        <div :class="hightFilterIsShow()">
          <template v-if="pageIcon == 'scan'">
            <detectIndexTable
              ref="detectIndexTable"
              :orgTaskId="taskInfo.id"
              :tableData="tableData"
              pageIcon="scan"
              :pageTab="tabActive"
              :checkedAll="checkedAll"
              :isClearSelection="isClearSelection"
              :levelIcon="String(tabActive)"
              @getList="getOrgScanListData"
              @handleSelectionChange="handleSelectionChange"
            />
          </template>
          <template v-else>
            <tableList
              ref="tableList"
              :pageIcon="pageIcon"
              :tableData="tableData"
              :checkedAll="checkedAll"
              @handleSelectionChange="handleSelectionChange"
            />
          </template>
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="pageSizeArr"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <div class="footer">
        <template v-if="pageIcon == 'recommend'">
          <el-button
            class="normalBtn"
            type="primary"
            id="unit_clue"
            :loading="predictBtnLoading"
            @click="createAssets"
            >信任度评估</el-button
          >
        </template>
        <template v-else-if="pageIcon == 'predict'">
          <el-button
            class="normalBtn"
            type="primary"
            id="unit_clue"
            @click="scanAssets('1')"
            :disabled="!isNotTestUser"
            >资产扫描
            <el-tooltip
              :open-delay="500"
              content="只有正式客户可用，对资产进行深度扫描探测，可发现资产的组件、地理位置等详细信息"
              placement="top"
              effect="dark"
            >
              <i
                class="el-icon-question"
                style="color: #fff; margin: 1px 0px 0px 4px; font-size: 16px"
              ></i>
            </el-tooltip>
          </el-button>
          <el-button class="normalBtn" type="primary" id="unit_clue" @click="scanAssets('0')"
            >资产入账
            <el-tooltip
              :open-delay="500"
              content="不对资产进行扫描，将关联后的结果直接入账到资产管理页面"
              placement="top"
              effect="dark"
            >
              <i
                class="el-icon-question"
                style="color: #fff; margin: 1px 0px 0px 4px; font-size: 16px"
              ></i>
            </el-tooltip>
          </el-button>
        </template>
        <template v-else-if="pageIcon == 'scan'">
          <el-button class="normalBtn" type="primary" id="unit_clue" @click="finish"
            >完成</el-button
          >
        </template>
      </div>
    </div>
    <div class="fourthBox" v-else>
      <div class="fourthTitle">
        <div class="grayBox">
          <div v-if="pageIcon == 'predict'">
            <span><span style="color: #2677ff; font-weight: 600">信任等级</span></span
            ><img src="../../../assets/images/triangle.png" alt="" class="graySan" /><span
              class="graylabel"
              >通过AI智能模型获取资产可信度等级，依据不同信任度进行资产分类入账</span
            >
          </div>
          <div v-if="pageIcon == 'scan'">
            <span
              ><span style="color: #2677ff; font-weight: 600">{{
                taskInfo.is_scan == 1 ? '资产扫描' : '资产入账'
              }}</span></span
            ><img src="../../../assets/images/triangle.png" alt="" class="graySan" /><span
              class="graylabel"
              >通过AI智能模型获取资产可信度等级，依据不同信任度进行资产分类入账</span
            >
          </div>
          <div v-if="pageIcon == 'recommend'">
            <span><span style="color: #2677ff; font-weight: 600">推荐资产</span></span
            ><img src="../../../assets/images/triangle.png" alt="" class="graySan" /><span
              class="graylabel"
              >获取企业云端资产信息</span
            >
          </div>
        </div>
      </div>
      <div class="tu yuTu tuFifth" lazy></div>
      <div v-if="!againIsShow" style="margin: 12px 8px 10px 0px"
        ><span style="margin: 0px 8px 0px 0px">{{ headText }}</span
        ><span style="color: #2677ff">{{ currentPercent }}%</span></div
      >
      <div v-if="!againIsShow" class="fourthProgressBox"
        ><el-progress
          :stroke-width="14"
          :percentage="Number(currentPercent)"
          style="margin-bottom: 20px"
        ></el-progress
      ></div>
      <div v-if="againIsShow" class="unitError">
        <p class="unitText"><i class="el-icon-warning"></i>{{ headText }}</p>
        <!-- <el-button style="margin-left: 0" class="normalBtn" type="primary">重新推荐</el-button> -->
      </div>
    </div>
    <highCheckDrawerScan
      :highCheckdialog="highCheckScanDialog"
      :selectArr="cluesList"
      :formInline="formInline"
      @highCheck="highCheck"
      @highIsClose="highCheckScanDialog = false"
      pageIcon="scan"
    />
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="ruleScreenDialogVis"
    >
      <template slot="title"> 规则筛选 </template>
      <div class="dialog-body">
        <el-form
          :model="ruleScreenForm"
          :rules="ruleScreenRules"
          ref="ruleForm"
          label-width="95px"
          class="tuijianForm"
        >
          <el-form-item prop="title_not" label="网站标题">
            <el-input
              type="textarea"
              :rows="6"
              v-model="ruleScreenForm.title_not"
              placeholder="请输入网站标题,支持输入多个,以分号或换行分隔"
            ></el-input>
          </el-form-item>
          <el-form-item prop="domain_not" label="域名">
            <el-input
              type="textarea"
              :rows="6"
              v-model="ruleScreenForm.domain_not"
              placeholder="请输入域名,支持输入多个,以分号或换行分隔"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="ruleScreenDialogVis = false" id="add_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" @click="ruleScreenOnConfirm" id="add_sure">确定</el-button>
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="100px">
          <el-form-item label="IP地址" prop="ip">
            <el-input v-model="formInline.ip" placeholder="请输入IP地址"></el-input>
          </el-form-item>
          <el-form-item label="企业名称" prop="clue_company_name">
            <el-select
              v-model="formInline.clue_company_name"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="
                selectChange(
                  $event,
                  'clue_company_name',
                  selectAllArr['clue_company_name'],
                  false,
                  true
                )
              "
            >
              <el-option
                v-for="item in selectAllArr['clue_company_name']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="端口" prop="port">
            <el-select
              v-model="formInline.port"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'port', selectAllArr['port'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['port']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="协议" prop="protocol">
            <el-select
              v-model="formInline.protocol"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'protocol', selectAllArr['protocol'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['protocol']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="URL" prop="url">
            <el-select v-model="formInline.url" filterable multiple collapse-tags clearable placeholder="请选择" @change="selectChange($event, 'url', selectAllArr['url'], false, true)">
              <el-option v-for="item in selectAllArr['url']" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="根域" prop="domain">
            <el-select
              v-model="formInline.domain"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'domain', selectAllArr['domain'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['domain']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="子域名" prop="subdomain">
            <el-select
              v-model="formInline.subdomain"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'subdomain', selectAllArr['subdomain'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['subdomain']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网站标题" prop="title">
            <el-select
              v-model="formInline.title"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'title', selectAllArr['title'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['title']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="证书" prop="cert">
            <el-select
              v-model="formInline.cert"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'cert', selectAllArr['cert'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['cert']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="ICP" prop="icp">
            <el-select
              v-model="formInline.icp"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'icp', selectAllArr['icp'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['icp']"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="ICON" prop="logo">
            <el-select
              v-model="formInline.logo"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'logo', selectAllArr['logo'], false, true)"
            >
              <el-option v-for="(v, i9) in selectAllArr['logo']" :key="i9 + '999'" :value="v.hash">
                <div style="display: flex; justify-content: space-between; align-items: center">
                  <img
                    style="max-width: 20px; max-height: 20px"
                    :src="String(v.content).includes('http') ? v.content : showSrcIp + v.content"
                    alt=""
                  />
                  <span>{{ v.hash }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现时间：" prop="created_at">
            <el-date-picker
              v-model="formInline.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="更新时间：" prop="updated_at">
            <el-date-picker
              v-model="formInline.updated_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import hightFilter from '@/components/assets/highTab.vue'
import detectIndexTable from '../../home_set/detectIndexTable.vue'
import tableList from './assetsList.vue'
import highCheckDrawerScan from '../../home_set/highCheck.vue'
import { mapGetters, mapState } from 'vuex'
import {
  orgTaskFinish,
  orgTaskScanAssetsData,
  orgTaskScanAssets,
  orgTask2Condition,
  orgTask2Delete,
  orgTask2Exports,
  organizationAssetsList,
  orgTaskUpdateLevel,
  organizationUnAssets
} from '@/api/apiConfig/api.js'
import { delCloudAssetsList } from '@/api/apiConfig/surveying.js'

export default {
  components: {
    tableList,
    hightFilter,
    detectIndexTable,
    highCheckDrawerScan
  },
  props: {
    taskInfo: {
      type: Object,
      default: () => ({})
    },
    isNotTestUser: {
      type: Boolean,
      default: false
    },
    allLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      predictBtnLoading: false,
      formInline: {
        ip: '',
        clue_company_name: '',
        province: '', // 省份名称（传汉字）,
        keyword: '', // 123123,
        asn: '', // 123123,
        cert: '', // cert,
        icp: '', // icp,
        url: '',
        title: '', // title,
        protocol: '', // protocol,
        logo: '', // logo,
        port: '', // port,
        cname: '', // cname,
        domain: [], // domain
        latitude: '',
        longitude: '',
        state: '',
        reason: '',
        reason_type: '', // 证据链
        tags: '',
        hosts: '',
        rule_tags: [],
        last_update_time: [],
        second_confirm: '' // second_confirm  0/待确认 1/已经确认
      },
      highCheckScanDialog: false,
      isClearSelection: false,
      assets_confidence_level: 0,
      tabActive: 0,
      tabCount: {},
      tabList: {
        predict: [
          {
            level: 1,
            name: '高可信度',
            icon: 'high'
          },
          {
            level: 2,
            name: '中可信度',
            icon: 'middle'
          },
          {
            level: 3,
            name: '低可信度',
            icon: 'low'
          }
        ],
        scan: [
          {
            level: 1,
            name: '资产台账',
            icon: 'konwn_table_num'
          },
          {
            level: 0,
            name: '疑似资产',
            icon: 'unknown_table_num'
          },
          {
            level: 3,
            name: '威胁资产',
            icon: 'threat_table_num'
          }
        ]
      },
      pageIcon: 'recommend',
      highlist: null,
      highTabShow: [
        {
          label: 'IP地址',
          name: 'ip',
          type: 'input'
        },
        {
          label: '企业名称',
          name: 'all_company_name',
          type: 'select'
        },
        {
          label: '端口',
          name: 'port',
          type: 'select'
        },
        {
          label: '协议',
          name: 'protocol',
          type: 'select'
        },
        // {
        //   label: "URL",
        //   name: "url",
        //   type: "select",
        // },
        {
          label: '根域',
          name: 'domain',
          type: 'select'
        },
        {
          label: '子域名',
          name: 'subdomain',
          type: 'select'
        },
        {
          label: '网站标题',
          name: 'title',
          type: 'select'
        },
        {
          label: '发现时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      selectAllArr: [],
      cluesList: {},
      versionArr: [],
      validArr: [],
      highCheckdialog: false,
      formInline: {},
      keyword: '',
      // progressBar:
      isAll: true,
      currentPercent: 0,
      isShowProgress: true,
      headText: '...',
      againIsShow: false,
      ruleScreenRules: {},
      ruleScreenForm: {
        title_not: '',
        domain_not: ''
      },
      ruleScreenDialogVis: false,
      loading: false,
      tableData: [],
      checkedAll: false,
      checkedArr: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100]
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  watch: {
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    allLoading(val) {
      this.loading = val
    }
  },
  methods: {
    getData() {
      this.getListData()
      this.getCondition()
    },
    async getListData() {
      this.isShowProgress = false
      let progress = 0
      if (this.taskInfo.step == 2) {
        // 推荐资产完成
        this.pageIcon = 'recommend'
        progress = this.taskInfo.progress
        this.headText = '集团资产获取中...'
      } else if (this.taskInfo.step == 3) {
        // 可信度评估
        this.pageIcon = 'predict'
        this.tabActive = 1
        progress = this.taskInfo.update_assets_level_progress
        this.headText = '信任度等级评估中...'
      } else if (this.taskInfo.step == 4) {
        // 入账扫描
        this.pageIcon = 'scan'
        this.tabActive = 1
        progress = this.taskInfo.scan_progress
        // 是否扫描 1 扫描 0 不扫描
        if (this.taskInfo.is_scan == 1) {
          this.headText = '资产扫描中...'
        } else {
          this.headText = '资产入账中...'
        }
      }
      if (this.taskInfo.step_status == 2 || progress == 100) {
        this.isShowProgress = false // 隐藏进度
        if (this.pageIcon == 'scan') {
          this.getOrgScanListData()
        } else {
          this.getOrgListData()
        }
      } else {
        this.isShowProgress = true // 显示进度
      }
      this.currentPercent = progress || 0
      if (!this.taskInfo || (this.taskInfo && !this.taskInfo.flag)) return
    },
    // 获取 推荐资产/信任度评级 列表
    async getOrgListData() {
      this.loading = true
      let obj = {
        flag: this.taskInfo.flag,
        assets_confidence_level: this.tabActive,
        organization_task_id: this.taskInfo.id,
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline,
        operate_company_id: this.currentCompany
      }
      let res = await organizationAssetsList(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
        this.tabCount = res.data.count
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs.tableList.$refs.eltable.toggleRowSelection(row, true)
          })
        }
      }
      this.loading = false
    },

    async getCondition() {
      if (!this.taskInfo || !this.taskInfo.flag) return
      let res = await orgTask2Condition({
        operate_company_id: this.currentCompany,
        organization_task_id: this.taskInfo.id,
        flag: this.taskInfo.flag
      })
      if (res.code == 0) {
        this.selectAllArr = res.data
      }
    },
    // 获取入账或者扫描后资产的列表
    async getOrgScanListData() {
      this.loading = true
      let obj = {
        status: this.tabActive, // 0 默认无任何状态(未知/疑似资产) 1/已认领(已确认)   2/忽略资产   3/威胁
        // flag: this.taskInfo.flag,
        organization_task_id: this.taskInfo.id,
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline,
        operate_company_id: this.currentCompany
      }
      let res = await orgTaskScanAssetsData(obj).catch(() => {
        this.loading = false
      })
      const { data, code } = res
      const { konwn_table_num, unknown_table_num, threat_table_num, total } = data
      if (code == 0) {
        this.total = total
        this.tabCount = {
          konwn_table_num,
          unknown_table_num,
          threat_table_num
        }
        res.data.items.forEach((ch) => {
          // 用于控制折叠展开
          ch.isExpand = false
          ch.myPopover = false
          ch.myPopoverFlag = true
          ch.detail &&
            ch.detail.forEach((v) => {
              v.isURLExpand = false
            })
        })
        this.tableData = res.data.items
        this.cluesList = res.data.condition
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs.tableList.$refs.eltable.toggleRowSelection(row, true)
          })
        }
      }
      this.loading = false
    },
    // highCheck () {
    //   this.highCheckdialog = false
    //   this.currentPage = 1
    //   this.getCloudAssetsAnyslate('yesLoading')
    // },
    highCheck(val) {
      this.formInline = Object.assign(val || {})
      this.currentPage = 1
      this.checkFuncList()
    },
    changeTab(val) {
      this.loading = true
      if (this.pageIcon == 'predict') {
        // this.assets_confidence_level = val
        this.getOrgListData()
      } else if (this.pageIcon == 'scan') {
        this.getOrgScanListData()
      }
    },
    async finish() {
      // 调用接口手动完成
      let res = await orgTaskFinish({
        organization_task_id: this.taskInfo.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.$message.success('集团资产梳理已完成')
        // 切换开始页面
        this.$emit('changeComponent', 'groupTask1')
      }
    },
    async scanAssets(val) {
      let obj = {
        is_scan: val, //是否扫描 1 扫描 0 不扫描
        organization_task_id: this.taskInfo.id,
        operate_company_id: this.currentCompany
      }
      let res = await orgTaskScanAssets(obj)
      if (res.code == 0) {
        this.$emit('changeComponent', 'groupTask5')
      }
    },
    async createAssets() {
      this.predictBtnLoading = true
      let obj = {
        organization_task_id: this.taskInfo.id,
        operate_company_id: this.currentCompany
      }
      let res = await orgTaskUpdateLevel(obj).catch(() => {
        this.predictBtnLoading = false
      })
      this.predictBtnLoading = false
      if (res.code == 0) {
        this.$emit('changeComponent', 'groupTask4')
      }
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {}
    },
    checkFuncList() {
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.currentPage = 1
      this.hightFilterIsShow()
      if (this.pageIcon == 'scan') {
        this.highCheckScanDialog = false
        this.getOrgScanListData()
      } else {
        this.highCheckdialog = false
        this.getOrgListData()
      }
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    async exportList() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let is_all = this.checkedAll ? '1' : '0'
      let id = this.checkedArr.map((item) => item.id)
      let res = await orgTask2Exports({
        ...this.formInline,
        id,
        flag: this.taskInfo.flag,
        is_all,
        organization_task_id: this.taskInfo.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.checkedAll = false
        this.download(this.showSrcIp + res.data.url)
        this.$refs.tableList.$refs.eltable.clearSelection()
        this.$message.success('导出成功')
      }
    },
    // 入账扫描删除
    async delScanAssets() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        let obj = {
          id: this.checkedArr.map((item) => {
            return item.id
          }),
          organization_discover_task_id: this.taskInfo.id,
          status: this.tabActive,
          operate_company_id: this.currentCompany
        }
        let res = await delCloudAssetsList(obj).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          setTimeout(() => {
            this.loading = false
            this.$message.success('删除成功！')
            this.getOrgScanListData()
          }, 3000)
        }
      })
    },
    // 删除按钮
    async delAssets() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let is_all = ''
      let id = this.checkedArr.map((item) => item.id)
      if (this.checkedAll) {
        id = []
        is_all = '1'
      } else {
        is_all = '0'
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      }).then(async () => {
        let res = await orgTask2Delete({
          ...this.formInline,
          id,
          flag: this.taskInfo.flag,
          is_all,
          organization_task_id: this.taskInfo.id,
          operate_company_id: this.currentCompany
        })
        if (res.code == 0) {
          this.checkedAll = false
          this.checkedArr = []
          // this.getData()
          if (this.pageIcon == 'scan') {
            this.getOrgScanListData()
          } else {
            this.getOrgListData()
          }
          this.$refs.tableList.$refs.eltable.clearSelection()
          this.$message.success('删除成功')
        }
      })
    },

    async ruleScreenOnConfirm() {
      const { title_not, domain_not } = this.ruleScreenForm
      let title_not1 =
        title_not &&
        title_not.lenghth != 0 &&
        title_not
          .split(/[；|;|\r\n]/)
          .filter((item) => {
            return item.trim()
          })
          .map((item) => {
            return item.trim()
          })
      let domain_not1 =
        domain_not &&
        domain_not.lenghth != 0 &&
        domain_not
          .split(/[；|;|\r\n]/)
          .filter((item) => {
            return item.trim()
          })
          .map((item) => {
            return item.trim()
          })

      let obj = {
        // assets_level: 0,
        organization_task_id: this.taskInfo.id,
        operate_company_id: this.currentCompany,
        title_not: title_not1,
        domain_not: domain_not1,
        flag: this.taskInfo.flag
      }
      let res = await organizationUnAssets(obj)
      if (res.code == 0) {
        this.$message.success('已移除')
        this.ruleScreenDialogVis = false
        this.getData()
      }
    },

    handleSizeChange(val) {
      this.pageSize = val
      if (this.taskInfo.step_status == 2 || progress == 100) {
        if (this.pageIcon == 'scan') {
          this.getOrgScanListData()
        } else {
          this.getOrgListData()
        }
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      if (this.taskInfo.step_status == 2 || progress == 100) {
        if (this.pageIcon == 'scan') {
          this.getOrgScanListData()
        } else {
          this.getOrgListData()
        }
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    async handleMessage(res, o) {
      //处理接收到的信息
      if (
        (res.cmd == 'recommend_progress' || res.cmd == 'organization_update_assets_level') &&
        res.data &&
        res.data.flag == this.taskInfo.flag
      ) {
        if (parseInt(res.data.progress) >= 100) {
          if (!this.isShowProgress) return // 防止websocket多次推送100%，进度条已经隐藏了又出来了
          this.isShowProgress = false
          this.$emit('getTaskInfo')
        } else {
          this.isShowProgress = true
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
      if (
        res.cmd == 'org_detect_assets_tasks' &&
        res.data.organization_discover_task_id == this.taskInfo.id
      ) {
        if (parseInt(res.data.progress) >= 100) {
          if (!this.isShowProgress) return // 防止websocket多次推送100%，进度条已经隐藏了又出来了
          this.isShowProgress = false
          this.$emit('getTaskInfo')
        } else {
          this.isShowProgress = true
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.tableWrap {
  height: calc(100% - 128px);
  padding: 0px 20px;
}
.tableWrapFilter {
  padding: 0px 20px;
  height: calc(100% - 165px) !important;
}
.taskContainer {
  width: 100%;
  // height: calc(100% - 64px);
  height: 100%;

  .container {
    height: 100%;
  }
}
.boxTwo1 {
  background-color: #fff;
  width: 100%;
  // height: 100%;
  height: calc(100% - 115px);
  // padding: 16px 20px 0;
}
/deep/.myTable {
  padding: 0px 20px;
  height: calc(100% - 128px);
}
/deep/.tableLabel {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #37393c;
  justify-content: space-between;
  // margin-bottom: 16px;
}

/deep/.tableLabel > div {
  display: flex;
  align-items: center;
  .confirmBox {
    margin-right: 16px;
  }
  & > p {
    margin-right: 16px;
  }
  .el-input {
    width: 240px;
  }
  & > span {
    font-weight: 400;
    color: #2677ff;
    line-height: 20px;
    margin-left: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}
/deep/.footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  background: #ffffff;
  box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
}

.tu {
  width: 120px !important;
  height: 120px !important;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position-y: 0px;
  // animation: running 6s steps(149) infinite;
  // -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.fourthBox {
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .fourthTitle {
    position: absolute;
    top: 20px;
    left: 0px;
  }
}
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
</style>
