<template>
  <div class="container">
    <div class="headerTitle">
      <div>
        <span class="goback" v-if="activeName == 'first'" @click="goBack"
          ><i class="el-icon-arrow-left"></i><span>返回</span><span class="spline">/</span></span
        >
        <span
          class="goback"
          v-if="activeName == 'second'"
          @click="$router.push({ path: '/groupAssets', query: { activeName: 'first' } })"
          ><i class="el-icon-arrow-left"></i><span>返回上一层</span
          ><span class="spline">/</span></span
        >
        集团资产梳理
        <span v-if="activeName == 'second'"><span class="spline">/</span>任务记录</span>
        <span class="headerShow" v-if="activeName == 'first'"
          ><i class="el-icon-warning"></i
          >支持针对大型集团企业进行资产的快速盘点，帮助梳理各个子公司的资产,适用于利用企业架构梳理资产的场景。</span
        >
      </div>
      <div>
        <el-button
          v-if="activeName == 'first'"
          type="text"
          @click="$router.push({ path: '/groupAssets', query: { activeName: 'second' } })"
        >
          任务记录
        </el-button>
      </div>
    </div>
    <div
      class="home_header"
      :style="
        componentTag == 'groupTask1'
          ? 'height:100%;padding:0'
          : 'height:calc(100% - 16px);padding:0 16px 16px'
      "
    >
      <component
        v-loading="loading"
        :allLoading="loading"
        @getTaskInfo="getTaskInfo"
        :taskInfo="taskInfo"
        :isNotTestUser="isNotTestUser"
        :is="componentTag"
        :ref="componentTag"
        @changeComponent="changeComponent"
        v-if="activeName == 'first'"
      ></component>
      <taskRecord @changeTab="changeToTaskTab" v-if="activeName == 'second'" />
    </div>
  </div>
</template>

<script>
import groupTask1 from './groupTask1.vue' // 输入集团名称
import groupTask2 from './groupTask2.vue' // 获得线索总库
import groupTask3 from './groupTask3.vue' // 获得推荐资产
import groupTask4 from './groupTask3.vue' // 信任度评估
import groupTask5 from './groupTask3.vue' // 入账或者扫描后的资产数据
import taskRecord from './taskRecord.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { organizationTaskInfo } from '@/api/apiConfig/api.js'

export default {
  components: { groupTask1, groupTask2, groupTask3, groupTask4, groupTask5, taskRecord },
  data() {
    return {
      loading: false,
      activeName: 'first',
      taskId: '',
      componentTag: 'groupTask1',
      taskInfo: {},
      isNotTestUser: false,
      user: {
        role: ''
      }
    }
  },
  watch: {
    getterWebsocketMessage(msg) {},
    activeName(newVal) {
      if (newVal == 'first') {
        this.getTaskInfo(this.taskId ? this.taskId : '')
      }
    },

    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getIsNotTestUser()
        // 切换用户重置 任务相关信息
        this.taskInfo = {}
        this.taskId = ''
        this.getTaskInfo(this.taskId ? this.taskId : '')
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage', 'getterCurrentCompany', 'getterCompanyChange'])
  },
  mounted() {
    if (this.$route.query.activeName) {
      this.activeName = this.$route.query.activeName
    }
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }

    if (this.user.role == 2 && !this.currentCompany) return
    this.getIsNotTestUser()

    let isGetCompany = sessionStorage.getItem('isGetCompany')
    if (Boolean(isGetCompany)) {
      this.getTaskInfo(this.taskId ? this.taskId : '')
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    getIsNotTestUser() {
      this.isNotTestUser = false
      if (this.currentCompany != -1) {
        if (sessionStorage.getItem('currentCompanyItem')) {
          let isTestUser = sessionStorage.getItem('currentCompanyItem')
          let isTestUserObj = isTestUser ? JSON.parse(isTestUser).currentCompanyItem : {}
          this.isNotTestUser =
            (isTestUserObj.role == 3 && isTestUserObj.level == 100) || isTestUserObj.role != 3
        }
      } else {
        this.isNotTestUser = true
      }
    },
    // 用于任务记录查看详情
    async getTaskInfo(taskId) {
      this.loading = true
      let res = await organizationTaskInfo({
        id: this.taskId,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.loading = false
        this.taskInfo = res.data || {}
        if (res.data) {
          if (res.data.step) {
            // 跳转步骤
            this.taskStep = Number(res.data.step) + 1
            this.loading = false
          }
        } else {
          this.taskStep = 1
        }
        this.componentTag = 'groupTask' + this.taskStep
        this.$nextTick(() => {
          if (this.taskStep != 1 && this.$refs[this.componentTag]) {
            this.$refs[this.componentTag].isShowProgress = true
          }
          if (res.data) {
            // 调用每个组件初始数据 每个组件获取的初始方法名 getData 不可更改
            this.$refs[this.componentTag] && this.$refs[this.componentTag].getData()
          }
        })
      }
    },
    goBack() {
      sessionStorage.setItem('menuId', '2-1')
      this.changeMenuId('2-1')
      this.$router.push('/assetsTanzhi')
    },
    // 切换tab页面到任务
    changeToTaskTab() {
      this.activeName = 'first'
    },
    changeComponent(task, id) {
      this.componentTag = task
      if (task == 'groupTask2') {
        this.taskInfo.id = id
        this.taskId = id
        this.getTaskInfo()
      } else if (task == 'groupTask1') {
        this.taskInfo = {}
        this.getTaskInfo()
      } else {
        this.getTaskInfo()
      }
    },
    // 切换进度
    sonData(val) {
      this.activeName = 'first'
    },
    handleClick(val) {
      this.$router.push({ query: { tabName: val.name } })
    }
  }
}
</script>

<style lang="less" scoped>
.headerTitle {
  width: calc(100% - 32px);
  display: flex;
  justify-content: space-between;
  padding: 0 16px;
  /deep/.el-button {
    padding: 0 20px;
    line-height: 28px;
  }
}
.container {
  position: relative;
  width: 100%;
  height: 100%;
  // background: #FFFFFF;
  // box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  // background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    & > .tabsWrap {
      .el-tabs__nav {
        padding-left: 20px;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        // min-width: 60px;
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        // padding: 0;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
      }
    }
    .tab_content {
      height: calc(100% - 44px) !important;
      background: rgb(255, 255, 255) !important;
    }
  }
}
/deep/.el-button--text {
  color: #2677ff;
}
/deep/.tu {
  width: 120px !important;
  height: 120px !important;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position-y: 0px;
}
/deep/.tuSecond {
  background-image: url('../../../assets/images/dongtu1.png');
}
/deep/.tuThird {
  width: 180px !important;
  height: 180px !important;
  background-image: url('../../../assets/images/dongtu2.png');
}
/deep/.tuFourth {
  background-image: url('../../../assets/images/dongtu3.png');
}
/deep/.tuFifth {
  width: 180px !important;
  height: 180px !important;
  background-image: url('../../../assets/images/dongtu4.png');
}
/deep/.fourthBox {
  background: url('../../../assets/images/loadingBgc2.png') no-repeat;
  background-size: 100% 100%;
}
</style>
