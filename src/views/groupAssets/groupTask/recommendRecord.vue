<template>
  <div class="container">
    <div class="headerTitle">
      <span>
        <span
          class="goback"
          @click="$router.push({ path: '/groupAssets', query: { activeName: 'second' } })"
          ><i class="el-icon-arrow-left"></i>返回上一层</span
        >
        <span class="spline">/</span>
        <span>查看详情</span>
      </span>
    </div>
    <div class="box">
      <div class="home_header" :style="'width: 100%;'">
        <div class="taskResults">任务推荐结果</div>
        <div class="bot">
          <div style="height: 100%">
            <div class="filterTab">
              <div style="display: flex; align-items: center">
                <el-input
                  v-model="formInlineScanScan.keyword"
                  @keyup.enter.native="checkFuncList('')"
                  clearable
                  placeholder="请输入关键字检索"
                  id="account_keycheck"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-search"
                    @click="checkFuncList('')"
                  ></el-button>
                  <el-tooltip
                    slot="prepend"
                    class="item"
                    effect="dark"
                    content="支持检索字段：IP地址、网站标题、域名、企业名称"
                    placement="top"
                    :open-delay="100"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </el-input>
                <span
                  @click="highCheckdialogScan = true"
                  id="account_filter"
                  style="width: 80px; display: inline-block; margin-left: 10px"
                  ><img
                    src="@/assets/images/filter.png"
                    alt=""
                    style="width: 16px; vertical-align: middle; margin-right: 3px"
                  />高级筛选</span
                >
              </div>
              <div>
                <el-checkbox class="checkboxAll" v-model="checkedAll" id="account_all"
                  >选择全部</el-checkbox
                >
                <el-button
                  class="normalBtnRe"
                  type="primary"
                  @click="exportList"
                  id="account_export"
                  >导出</el-button
                >
              </div>
            </div>
            <hightFilter
              id="hightFilter"
              :highTabShow="highTabShow"
              :highlist="highlist"
              :total="total"
              pageIcon="keyword1"
              @highcheck="highCheck"
            ></hightFilter>
            <div class="tableWrap" :class="hightFilterIsShow()" v-loading="loading" ref="tableWrap">
              <tableList
                :radioParams="false"
                :checkedAll="checkedAll"
                ref="tableList"
                v-loading="loading"
                :tableData="tableData"
                @handleSelectionChange="handleSelectionChange"
              />
            </div>
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="pageSizeArr"
              :page-size="pageSize"
              layout="total, prev, pager, next, sizes, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialogScan" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInlineScanScan" ref="drawerForm" label-width="100px">
          <el-form-item label="IP地址" prop="ip">
            <el-input v-model="formInlineScanScan.ip" placeholder="请输入IP地址"></el-input>
          </el-form-item>
          <el-form-item label="企业名称" prop="organization_company_name">
            <el-select
              v-model="formInlineScanScan.organization_company_name"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="
                selectChange(
                  $event,
                  'organization_company_name',
                  selectAllArr['organization_company_name'],
                  false,
                  true
                )
              "
            >
              <el-option
                v-for="item in selectAllArr['organization_company_name']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="端口" prop="port">
            <el-select
              v-model="formInlineScanScan.port"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'port', selectAllArr['port'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['port']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="协议" prop="protocol">
            <el-select
              v-model="formInlineScanScan.protocol"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'protocol', selectAllArr['protocol'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['protocol']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="URL" prop="url">
            <el-select v-model="formInlineScanScan.url" filterable multiple collapse-tags clearable placeholder="请选择" @change="selectChange($event, 'url', selectAllArr['url'], false, true)">
              <el-option v-for="item in selectAllArr['url']" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="根域" prop="domain">
            <el-select
              v-model="formInlineScanScan.domain"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'domain', selectAllArr['domain'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['domain']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="子域名" prop="subdomain">
            <el-select
              v-model="formInlineScanScan.subdomain"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'subdomain', selectAllArr['subdomain'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['subdomain']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网站标题" prop="title">
            <el-select
              v-model="formInlineScanScan.title"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'title', selectAllArr['title'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['title']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="证书" prop="cert">
            <el-select
              v-model="formInlineScanScan.cert"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'cert', selectAllArr['cert'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['cert']"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="ICP" prop="icp">
            <el-select
              v-model="formInlineScanScan.icp"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'icp', selectAllArr['icp'], false, true)"
            >
              <el-option
                v-for="item in selectAllArr['icp']"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="ICON" prop="logo">
            <el-select
              v-model="formInlineScanScan.logo"
              filterable
              multiple
              collapse-tags
              clearable
              placeholder="请选择"
              @change="selectChange($event, 'logo', selectAllArr['logo'], false, true)"
            >
              <!-- <el-option v-for="item in selectAllArr['logo']" :key="item.id" :label="item.name" :value="item.id"></el-option> -->
              <el-option v-for="(v, i9) in selectAllArr['logo']" :key="i9 + '999'" :value="v.hash">
                <div style="display: flex; justify-content: space-between; align-items: center">
                  <img
                    style="max-width: 20px; max-height: 20px"
                    :src="String(v.content).includes('http') ? v.content : showSrcIp + v.content"
                    alt=""
                  />
                  <span>{{ v.hash }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现时间：" prop="created_at">
            <el-date-picker
              v-model="formInlineScanScan.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="更新时间：" prop="updated_at">
            <el-date-picker
              v-model="formInlineScanScan.updated_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import hightFilter from '@/components/assets/highTab.vue'
import { mapGetters, mapState } from 'vuex'
import tableList from '@/views/home_set/indexTable.vue'
import highCheckDrawerScan from '@/views/home_set/highCheck.vue'
import { orgTask2Condition, orgAssetsRecommend, orgTask2Exports } from '@/api/apiConfig/api.js'

export default {
  components: {
    tableList,
    highCheckDrawerScan,
    hightFilter
  },
  data() {
    return {
      highlist: null,
      highTabShow: [
        {
          label: 'IP地址',
          name: 'ip',
          type: 'input'
        },
        {
          label: '企业名称',
          name: 'organization_company_name',
          type: 'select'
        },
        {
          label: '端口',
          name: 'port',
          type: 'select'
        },
        {
          label: '协议',
          name: 'protocol',
          type: 'select'
        },
        // {
        //   label: "URL",
        //   name: "url",
        //   type: "select",
        // },
        {
          label: '根域',
          name: 'domain',
          type: 'select'
        },
        {
          label: '子域名',
          name: 'subdomain',
          type: 'select'
        },
        {
          label: '网站标题',
          name: 'title',
          type: 'select'
        },
        {
          label: '发现时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      selectAllArr: [],
      activeName: '',
      cluesList: {}, // 线索数据
      highCheckdialogScan: false,
      checkedAll: false,
      // radioParams:'',
      loading: false,
      formInlineScanScan: {},
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      tableData: [],
      hightFilterShow: 3,
      recommendData: {
        //扫描资产核对任务参数
        name: '',
        clue: '',
        start_at: '',
        end_at: '',
        use_seconds: '',
        op: ''
      },
      recommendTitle: [
        // {
        //   label: '任务参数',
        //   name: ''
        // },
        {
          label: '任务名称',
          name: 'name'
        },
        {
          label: '开始时间',
          name: 'start_at'
        },
        {
          label: '结束时间',
          name: 'end_at'
        },
        {
          label: '任务耗时',
          name: 'use_seconds'
        },
        {
          label: '发起人',
          name: 'op'
        },
        {
          label: '推荐线索',
          name: 'clue'
        }
      ]
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany'])
  },
  watch: {
    getterCurrentCompany(val) {
      this.$router.go(-1)
    }
  },
  mounted() {
    let isGetCompany = sessionStorage.getItem('isGetCompany')
    if (Boolean(isGetCompany)) {
      this.init()
    }
  },
  methods: {
    highCheck(val) {
      this.formInlineScanScan = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highlist = Object.assign({}, this.formInlineScanScan) // 用于高级筛选标签显示
      this.currentPage = 1
      this.hightFilterIsShow()
      this.getRecommendRecordsList()
      this.highCheckdialogScan = false
    },
    async getCondition() {
      let res = await orgTask2Condition({
        operate_company_id: this.currentCompany,
        organization_task_id: this.$route.query.id,
        flag: this.$route.query.flag
      })
      if (res.code == 0) {
        this.selectAllArr = res.data
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInlineScanScan = {
        user_name: '',
        current_page: 1,
        per_page: 10,
        operate_company_id: '',
        created_at_range: []
      }
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInlineScanScan['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInlineScanScan['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInlineScanScan['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInlineScanScan['ch_' + name] = ''
        if (!String(val)) {
          this.formInlineScanScan['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInlineScanScan['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInlineScanScan['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    highCheckQuxiao(data) {
      this.formInlineScanScan = {
        keyword: this.formInlineScanScan.keyword,
        ...data
      }
      this.currentPage = 1
      this.getRecommendRecordsList()
      this.formInlineScanScan.created_at = this.formInlineScanScan.created_at
        ? this.formInlineScanScan.created_at
        : []
      this.formInlineScanScan.updated_at = this.formInlineScanScan.updated_at
        ? this.formInlineScanScan.updated_at
        : []
      let obj = {
        ...this.formInlineScanScan
      }

      // this.highCheckdialogScan()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    async exportList() {
      if (!this.checkedArr || this.checkedArr.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let id = this.checkedArr.map((item) => item.id)
      let res = await orgTask2Exports({
        ...this.formInlineScanScan,
        id,
        flag: this.$route.query.flag,
        is_all: this.checkedAll ? '1' : '0',
        organization_task_id: this.$route.query.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.download(this.showSrcIp + res.data.url)
        this.checkedAll = false
        this.$message.success('导出成功')
        this.$refs.tableList.$refs.eltable.clearSelection()
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    init() {
      this.getCondition()
      this.getRecommendRecordsList()
    },
    async getRecommendRecordsList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.tableData = []

      let obj = {
        ...this.formInlineScanScan,
        keyword: this.formInlineScanScan.keyword,
        flag: this.$route.query.flag,
        operate_company_id: this.currentCompany,
        page: this.currentPage,
        per_page: this.pageSize,
        organization_task_id: this.$route.query.id
      }
      this.loading = true
      let res = await orgAssetsRecommend(obj).catch(() => {
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      res.data.items.forEach((item) => {
        item['id'] = item.id
      })
      this.tableData = res.data.items
      this.total = res.data.total
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getRecommendRecordsList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getRecommendRecordsList()
    }
  }
}
</script>

<style lang="less" scoped>
.tableWrap {
  height: calc(100% - 169px);
  padding: 0px 20px;
}
.tableWrapFilter {
  height: calc(100% - 180px) !important;
}

.container {
  position: relative;
  width: 100%;
  height: 100%;
  .headerTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .headerTitle > span:first-child {
    margin-right: 12px;
  }
  .box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;
    .recommendlLeft {
      background: #ffffff linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #f3f7ff 100%);
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      border-radius: 4px;
      .recommendTitle {
        font-weight: 500;
        color: #37393c;
        margin: 12px 16px 0px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e9ebef;
      }
      .recommendBox {
        height: calc(100% - 44px);
        overflow: auto;
      }
      li {
        padding: 16px 16px 0px;
        color: #62666c;
        .rightTitle {
          width: 100%;
          padding-bottom: 4px;
          font-size: 14px;
          color: #111;
        }
        .rightContent {
          span {
            display: inline-block;
            // width: 100%;
            padding: 0;
          }
          .clueClass {
            .el-icon-picture-outline {
              font-size: 16px;
            }
          }
        }
      }
    }
    /deep/.home_header {
      width: 100%;
      height: 100%;
      .taskResults {
        height: 44px;
        line-height: 44px;
        padding-left: 16px;
        // position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
        font-weight: 500;
        color: #37393c;
        background-color: #fff;
        border-bottom: 1px solid #e9ebef;
        box-sizing: border-box;
      }
      .bot {
        width: 100%;
        height: calc(100% - 60px);
        background: #fff;
        .filterTab {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          & > div {
            .el-input {
              width: 240px;
            }
            .el-select {
              width: 240px;
            }
            & > span {
              font-weight: 400;
              color: #2677ff;
              line-height: 20px;
              // margin-left: 16px;
              cursor: pointer;
            }
          }
        }
        .tableWrap {
          height: calc(100% - 131px);
          padding: 0px 20px;
          // .autoHeader {
          //   font-size: 16px;
          //   color: #62666C;
          //   box-shadow: 2px 0px 8px 0px rgba(0, 0, 0, 0.08);
          //   cursor: pointer;
          // }
        }
      }
    }
  }
}
</style>
