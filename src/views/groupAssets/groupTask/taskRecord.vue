<template>
  <div class="container">
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="Advanced"
            id="keyword_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="keyword_filter" style="width: 80px"
            ><img
              src="@/assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <!-- <el-checkbox class="checkboxAll" v-model="checkedAll" @change="checkAllChange" id="keyword_all">选择全部</el-checkbox> -->
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="remove('more')"
            id="keyword_more_del"
            >删除</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="keyword"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <!-- 只有单位资产测绘才有企业名称 -->
          <el-table-column
            v-for="item in tableHeader.filter((item) => {
              return !item.path || item.path == $route.path
            })"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'step'">{{ getStep(scope.row) }}</span>
              <span v-else-if="item.name == 'percent'">{{
                scope.row[item.name] ? scope.row[item.name] + '%' : '-'
              }}</span>
              <span v-else-if="item.name == 'relate_task'">{{
                Number(scope.row.is_check_risk) + Number(scope.row.is_extract_clue)
              }}</span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
            <!-- <template  v-if="item.name == 'step'">
              <el-table-column label="台账-IP">
              </el-table-column>
              <el-table-column label="疑似-IP">
              </el-table-column>
              <el-table-column label="域名">
              </el-table-column>
              <el-table-column label="线索">
              </el-table-column>
          </template> -->
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="200">
            <template slot-scope="scope">
              <!-- 已完成的没有查看详情 -->
              <el-button
                v-if="scope.row.step != 5"
                type="text"
                size="small"
                @click="reviewInfo(scope.row, '1')"
                id="keyword_detail"
                >查看详情</el-button
              >
              <!-- 只有任务已完成的才有推荐资产 -->
              <el-button
                v-if="scope.row.step == 5"
                type="text"
                size="small"
                @click="reviewInfo(scope.row, '2')"
                id="keyword_record"
                >推荐记录</el-button
              >
              <el-button
                v-if="(scope.row.step == 4 && scope.row.step_status == 2) || scope.row.step == 5"
                type="text"
                size="small"
                @click="downloadResult(scope.row)"
                id="keyword_download"
                >下载结果</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="remove('one', scope.row.id)"
                id="keyword_del"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <!-- <el-form-item label="状态：" prop="status">
          <el-select filterable v-model="formInline.status" placeholder="请选择" clearable @change="selectChange($event, 'status', statusArr, true, false)">
            <el-option v-for="item in statusArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item> -->
          <el-form-item label="添加时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '@/components/tableTooltip/tableTooltip.vue'
import hightFilter from '@/components/assets/highTab.vue'
import { orgTaskDelete, orgTaskList, orgExportAll } from '@/api/apiConfig/api.js'
import { detectTaskInfo, cloudAssetsReport } from '@/api/apiConfig/surveying.js'

export default {
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      highTabShow: [
        {
          label: '添加时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      formInline: {
        page: 1,
        per_page: 10,
        keyword: '',
        operate_company_id: ''
      },
      checkedAll: false,
      highCheckdialog: false,
      tableHeader: [
        {
          label: '集团名称',
          name: 'name',
          minWidth: 120
        },
        // {
        //   label: '线索数量',
        //   name: 'clue_num',
        //   minWidth: 60
        // },
        {
          label: '进度',
          name: 'step',
          minWidth: 120
        },
        {
          label: '控股比例',
          name: 'percent',
          minWidth: 120
        },
        {
          label: '台账资产',
          name: 'sure_ip_num',
          minWidth: 60
        },
        {
          label: '疑似资产',
          name: 'unsure_ip_num',
          minWidth: 60
        },
        {
          label: '域名',
          name: 'domain_num',
          minWidth: 60
        },
        {
          label: '线索',
          name: 'clue_num'
        },
        {
          label: '发起人',
          name: 'user_name'
        },
        {
          label: '发起时间',
          name: 'created_at',
          minWidth: 120
        }
      ],
      statusArr: [
        // 状态 0/1 禁用/启用
        {
          name: '禁用',
          id: 0
        },
        {
          name: '启用',
          id: 1
        }
      ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      ruleForm: {
        way: '0',
        name: ''
      },
      rules: {
        name: [{ required: true, message: ' ', trigger: 'blur' }]
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      user: {
        role: ''
      },
      taskInfoData: {}
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
    // this.getcehui();
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
        // this.getcehui();
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    },
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    }
  },
  computed: {
    ...mapState(['currentCompany', 'recommentFlag']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  methods: {
    async downloadResult(row) {
      let obj = {
        id: row.id,
        operate_company_id: this.currentCompany,
        is_all: 1,
        company_id: []
      }
      let res = await orgExportAll(obj)
      if (res.code == 0) {
        this.download(this.showSrcIp + res.data)
        this.$message.success('导出成功')
      }
    },
    Advanced() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.getData()
    },
    handleMessage(res, o) {
      //处理接收到的信息
    },
    async getcehui() {
      let obj = {
        taskId: '',
        data: {
          expand_source: this.$route.meta.expand_source, // 1云端推荐，0单位资产测绘
          operate_company_id: this.currentCompany
        }
      }
      let res = await detectTaskInfo(obj)
      if (res.code == 0) {
        this.taskInfoData = res.data
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    async createReport(id) {
      let obj = {
        expend_id: id,
        operate_company_id: this.currentCompany
      }
      let res = await cloudAssetsReport(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
      }
    },
    // 查看详情
    reviewInfo(item, icon) {
      if (icon == 1) {
        this.$router.push({ path: '/groupAssets', query: { activeName: 'first' } })
      } else {
        // pageIcon: 1代表是单位测绘和云端推荐画报跳转过去的，不显示执行扫描按钮
        this.$router.push({
          path: '/groupAssets-recommend',
          query: { id: item.id, flag: item.flag }
        })
      }
    },
    // 进度详情
    getStep(row) {
      let str = ''
      // if (row.step == 1||row.step == 2) {
      //   str = '资产梳理已完成'
      //   if (row.progress<=100) {
      //     str = '资产梳理中'
      //   }
      // } else if (row.step == 3) {
      //       str = '已生成资产台账'
      // } else if (row.step == 4) {
      //   str = '已完成'
      // }
      if (row.step == 1) {
        str = '资产线索获取中...'
      } else if (row.step == 2) {
        str = '集团资产获取中...'
        if (row.step_status == 2) {
          str = '集团资产获取完成'
        }
      } else if (row.step == 3) {
        str = '信任度等级评估中...'
        if (row.step_status == 2) {
          str = '信任度等级评估完成'
        }
      } else if (row.step == 4) {
        str = '信任度等级评估中...'
        if (row.is_scan == 1) {
          str = '资产扫描中...'
          if (row.step_status == 2) {
            str = '资产扫描完成'
          }
        } else {
          str = '资产入账中...'
          if (row.step_status == 2) {
            str = '资产入账完成'
          }
        }
      } else {
        str = '已完成'
      }
      return str
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    getData() {
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.expand_source = this.$route.meta.expand_source
      orgTaskList(this.formInline)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async remove(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'keyword_del_cancel',
        confirmButtonClass: 'keyword_del_sure',
        customClass: 'keyword_del',
        type: 'warning'
      })
        .then(async () => {
          this.formInline.page = this.currentPage
          this.formInline.per_page = this.pageSize
          this.formInline.expand_source = this.$route.meta.expand_source
          if (this.checkedAll) {
            this.formInline.id = icon == 'one' ? [id] : []
          } else {
            this.formInline.id =
              icon == 'one'
                ? [id]
                : this.checkedArr.map((item) => {
                    return item.id
                  })
          }
          let obj = Object.assign({}, this.formInline)
          obj.operate_company_id = this.currentCompany
          this.loading = true
          let res = await orgTaskDelete(obj).catch(() => {
            this.loading = false
          })
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.loading = false
            this.checkedAll = false
            this.$refs.eltable.clearSelection()
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.keyword_del>.el-message-box__btns')
        del.children[0].id = 'keyword_del_cancel'
        del.children[1].id = 'keyword_del_sure'
      }, 50)
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        id: [],
        page: 1,
        per_page: 10,
        keyword: '',
        operate_company_id: ''
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    // height: calc(100% - 16px) !important;
    width: 100%;
    background: #fff;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
          display: flex;
          align-items: center;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0 16px;
    }
    .el-table {
      border: 0;
      .el-table__body td.el-table__cell div {
        padding: 12px !important;
      }
    }
  }
}

.emptyClass {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
  vertical-align: middle;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
