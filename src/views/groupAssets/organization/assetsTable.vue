<template>
  <div class="container" :class="pageType">
    <div class="headerTitle" v-if="pageType != 'other'">
      <span class="goback" @click="goOperationBack"><i class="el-icon-arrow-left"></i>返回</span
      ><span class="spline">/</span> {{ $route.query.comName }} 企业资产
    </div>
    <div class="boxTwo1" v-loading="tableLoading">
      <div class="tableLabel">
        <div class="tab">
          <div
            class="item"
            v-for="item in tabList"
            :key="item.val"
            :class="{ active: activeItemVal == item.val }"
            @click="changeTab(item)"
          >
            {{ item.label }}
            <span v-if="pageType != 'other'">({{ tabListNum[item.numVal] || 0 }})</span>
          </div>
        </div>
        <div v-if="pageType != 'other'">
          <el-checkbox class="checkboxAll" id="keyword_all" v-model="checkedAll"
            >选择全部</el-checkbox
          >
          <el-button class="normalBtnRe" type="primary" @click="exportList">导出</el-button>
          <!-- <el-button class="normalBtnRe" type="primary" @click="">一键关联</el-button> -->
        </div>
        <div v-else>
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="onKeyRelate"
            :disabled="relateOpenLoading"
            >一键关联</el-button
          >
        </div>
      </div>
      <div class="myTable">
        <template v-if="tabIcon == 'domain'">
          <tableList
            :checkedAll="checkedAll"
            ref="tableList"
            :tableData="tableData"
            :isChecked="false"
            :tabIcon="tabIcon"
            :radioParams="false"
            :isSelection="pageType != 'other'"
            @handleSelectionChange="handleSelectionChange"
          />
        </template>
        <template v-else>
          <detectIndexTable
            ref="tableList"
            :tableData="tableData"
            pageIcon="orgAssets"
            :pageTab="1"
            :checkedAll="checkedAll"
            :isClearSelection="false"
            levelIcon="1"
            @handleSelectionChange="handleSelectionChange"
          />
        </template>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      @closed="relateClosed"
      :close-on-click-modal="false"
      :visible.sync="relateDialogVisible"
      width="500px"
    >
      <template slot="title"> 一键关联 </template>
      <div class="dialog-body">
        <div class="tip">
          <i class="el-icon-question"></i>
          一键关联将会把台账IP、疑似IP、域名资产与其他集团匹配数据，并同步到对应的企业下
        </div>
        <el-form label-width="80">
          <el-form-item label="选择企业">
            <el-select v-model="selectCurrentId" placeholder="请选择分组">
              <el-option
                v-for="(item, index) in selectListData"
                :key="index"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="relateDialogVisible = false">取消</el-button>
        <el-button
          class="highBtn"
          type="primary"
          :loading="relateOpenLoading"
          @click="relateConfirm"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapMutations, mapGetters, mapState } from 'vuex'
import tableList from './assetsList.vue'
import detectIndexTable from '../../home_set/detectIndexTable.vue'
import {
  organizationGroupList,
  orgOtherSync,
  orgOtherDomainList,
  orgOtherAssetsList,
  orgCompanyDomainDel,
  orgCompanyAssetsDel,
  orgAssetsList,
  orgDomainsList,
  orgDomainsExport,
  orgAssetsExport,
  orgCompanyAssetsNum
} from '@/api/apiConfig/api.js'
import {} from '@/api/apiConfig/surveying.js'

export default {
  props: {
    pageType: {
      type: String,
      default: '' // other 代表 ‘其他’分组 非 分组下的‘其他’企业
    },
    orgId: {
      type: [String, Number],
      default: ''
    }
  },
  components: {
    tableList,
    detectIndexTable
  },
  data() {
    return {
      selectListData: [],
      selectCurrentId: '',
      // listData:[],
      relateDialogVisible: false,
      extractForm: {},
      tableLoading: false,
      checkedArr: [],
      checkedAll: false,
      activeItemVal: '1',
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 50, 100],
      total: 0,
      tabList: [
        { label: '台账-IP', val: '1', numVal: 'sure_ip_num' },
        { label: '疑似-IP', val: '2', numVal: 'un_sure_ip_num' },
        { label: '域名', val: '3', numVal: 'domain_num' }
      ],
      tabListNum: {},
      tabIcon: 'IP',
      relateOpenLoading: false
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  mounted() {
    let isGetCompany = sessionStorage.getItem('isGetCompany')
    if (Boolean(isGetCompany)) {
      this.getData()
      if (this.pageType != 'other') {
        this.getAssetsTypeNum()
      }
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.getData()
      if (this.pageType != 'other') {
        this.getAssetsTypeNum()
      }
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    relateClosed() {
      this.relateDialogVisible = false
    },
    async onKeyRelate() {
      this.relateOpenLoading = true
      this.selectCurrentId = ''
      let res = await organizationGroupList({ operate_company_id: this.currentCompany }).catch(
        () => {
          this.relateOpenLoading = false
        }
      )
      if (res.code == 0) {
        this.selectListData = res.data
        this.relateDialogVisible = true
      }
      this.relateOpenLoading = false
    },
    async relateConfirm() {
      if (!this.selectCurrentId) {
        this.$message.error('请选择集团企业')
        return
      }
      this.relateOpenLoading = true
      let res = await orgOtherSync({
        organization_group_id: this.selectCurrentId,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.relateOpenLoading = false
      })
      if (res.code == 0) {
        this.$message.success('一键关联成功')
        this.relateOpenLoading = false
        this.relateClosed()
      }
    },
    delList() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      }).then(async () => {
        let { orgId, comId } = this.$route.query
        let obj = {
          id: this.checkedArr.map((item) => {
            return item.id
          }),
          status: this.tabActive,
          is_all: this.checkedAll ? '1' : '0',
          organization_group_id: orgId,
          company_id: comId,
          assets_level: this.activeItemVal,
          operate_company_id: this.currentCompany
        }
        let funcName = this.activeItemVal == 3 ? orgCompanyDomainDel : orgCompanyAssetsDel
        let res = await funcName(obj)
        if (res.code == 0) {
          this.$refs.tableList.$refs.eltable.clearSelection()
          this.checkedAll = false
          this.$message.success('删除成功')
          this.getData()
          this.getAssetsTypeNum()
        }
      })
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    async exportList() {
      if (!this.checkedAll && this.checkedArr.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let { orgId, comId } = this.$route.query
      let is_all = this.checkedAll ? '1' : '0'
      let id = this.checkedArr.map((item) => item.id)
      let funcName = this.activeItemVal == 3 ? orgDomainsExport : orgAssetsExport
      let res = await funcName({
        id,
        is_all,
        assets_level: this.activeItemVal,
        operate_company_id: this.currentCompany,
        organization_group_id: orgId,
        company_id: comId
      })
      if (res.code == 0) {
        this.checkedAll = false
        this.download(this.showSrcIp + res.data.url)
        this.$refs.tableList.$refs.eltable.clearSelection()
        this.$message.success('导出成功')
      }
    },
    goBack() {
      // sessionStorage.setItem("menuId", "2-1");
      // this.changeMenuId("2-1");
      this.$router.go(-1)
      // this.$router.push({path:'',})
    },
    // 返回组织架构资产管理
    goOperationBack() {
      this.changeMenuId('12')
      // this.$router.go(-1)
      let preOrgId = this.$route.query.orgId
      this.$router.push({ name: 'organization', params: { id: preOrgId } })
    },
    changeTab(item) {
      this.tableLoading = true
      this.checkedAll = false
      this.checkedArr = []
      this.tableData = []
      // this.$refs.tableList.$refs.eltable.clearSelection()
      this.activeItemVal = item.val
      if (item.val == 3) {
        this.tabIcon = 'domain'
      } else {
        this.tabIcon = 'IP'
      }
      this.currentPage = 1
      this.getData()
    },
    getData() {
      this.tableData = []
      this.tableLoading = true
      if (this.pageType == 'other') {
        this.getOrgAssets()
      } else {
        this.getCompanyAssets()
      }
    },
    async getCompanyAssets() {
      let { orgId, comId } = this.$route.query
      let funcName =
        this.activeItemVal == 1 || this.activeItemVal == 2 ? orgAssetsList : orgDomainsList
      let obj = {
        organization_group_id: orgId,
        company_id: comId,
        operate_company_id: this.currentCompany,
        page: this.currentPage,
        per_page: this.pageSize,
        assets_level: this.activeItemVal
      }
      let res = await funcName(obj).catch(() => {
        this.tableLoading = false
      })
      if (res.code == 0) {
        if (this.activeItemVal == 1 || this.activeItemVal == 2) {
          res.data.items.forEach((ch) => {
            // 用于控制折叠展开
            ch.isExpand = false
            ch.myPopover = false
            ch.myPopoverFlag = true
            ch.detail &&
              ch.detail.forEach((v) => {
                v.isURLExpand = false
              })
          })
        }
        this.tableData = res.data.items
        this.total = res.data.total
      }
      this.tableLoading = false
    },
    async getOrgAssets() {
      let funcName =
        this.activeItemVal == 1 || this.activeItemVal == 2 ? orgOtherAssetsList : orgOtherDomainList
      let obj = {
        organization_group_id: this.orgId,
        operate_company_id: this.currentCompany,
        page: this.currentPage,
        per_page: this.pageSize,
        assets_level: this.activeItemVal
      }
      let res = await funcName(obj).catch(() => {
        this.tableLoading = false
      })
      if (res.code == 0) {
        if (this.activeItemVal == 1 || this.activeItemVal == 2) {
          res.data.items.forEach((ch, index) => {
            // 用于控制折叠展开
            ch.isExpand = false
            ch.myPopover = false
            ch.myPopoverFlag = true
            if (ch.chain_list) {
              //证据链去重
              let resetChainList = this.uniqChainArr(ch.chain_list) // 多条线索链去重
              res.data.items[index].chain_list = resetChainList
            }
            ch.detail &&
              ch.detail.forEach((v) => {
                v.isURLExpand = false
              })
          })
        }
        this.tableData = res.data.items
        this.total = res.data.total
      }
      this.tableLoading = false
    },
    // 多条线索链去重
    uniqChainArr(arr) {
      let newarr = [] //盛放去重后数据的新数组
      for (let el of arr) {
        //循环arr数组对象的内容, i是数组
        let flag = true //建立标记，判断数据是否重复，true为不重复
        for (let elk of newarr) {
          //循环新数组的内容
          let reg =
            String(
              el.map((item) => {
                return item.content
              })
            ) ==
            String(
              elk.map((item) => {
                return item.content
              })
            )
          if (reg) {
            flag = false
          }
        }
        if (flag) {
          //判断是否重复
          newarr.push(el) //不重复的放入新数组。  新数组的内容会继续进行上边的循环。
        }
      }
      return newarr
    },
    async getAssetsTypeNum() {
      let { orgId, comId } = this.$route.query

      let res = await orgCompanyAssetsNum({
        organization_group_id: orgId,
        company_id: comId,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.tabListNum = res.data
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData('yesLoading')
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData('yesLoading')
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 100%;
  &.other {
    width: calc(100% - 336px);
    .boxTwo1 {
      width: 100%;
      height: 100%;
    }
  }
}
.boxTwo1 {
  box-sizing: border-box;
  background-color: #fff;
  height: 100%;
  // height: calc(100% - 64px);
  padding: 16px 20px 0;
}
.myTable {
  padding: 0px 20px;
  height: calc(100% - 128px);
}
.tableLabel {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #37393c;
  justify-content: space-between;
  // margin-bottom: 16px;
  .tab {
    border-radius: 4px;
    overflow: hidden;
    .item {
      padding: 6px 12px;
      background: #e1e7f1;
      cursor: pointer;
      &.active {
        color: #fff;
        background: #2677ff;
      }
    }
  }
}

.tableLabel > div {
  display: flex;
  align-items: center;
  & > p {
    margin-right: 16px;
  }
  /deep/.el-input {
    width: 240px;
  }
  & > span {
    font-weight: 400;
    color: #2677ff;
    line-height: 20px;
    margin-left: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}
/deep/ .el-form-item__content {
  margin-left: 70px;
}
.tip {
  margin-bottom: 18px;
}
</style>
