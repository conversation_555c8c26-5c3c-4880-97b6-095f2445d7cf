<template>
  <div>
    <!-- 企业信息 -->
    <el-dialog
      class="elDialogAdd2"
      :class="{ add_img: !companyInfo }"
      :close-on-click-modal="false"
      :before-close="closeDia"
      :visible.sync="dialogFormVisibleCompany"
      width="800px"
      title="企业关系查询"
    >
      <div class="dialog-body">
        <div v-if="!companyInfo" class="company-input">
          <el-input
            v-model="companyName"
            placeholder="请输入企业名称全称进行搜索"
            @keyup.enter.native="companyInfoShow"
            id="cloud_company_enter"
          >
            <el-button
              slot="append"
              @click="companyInfoShow"
              class="search-btn"
              id="cloud_company_search"
              >搜索</el-button
            >
          </el-input>
        </div>
        <div v-else>
          <div class="company-list" v-show="companyListVisible">
            <div class="action">
              <div>
                <el-input
                  v-model="companyName"
                  @keyup.enter.native="companyInfoShow"
                  placeholder="请输入企业名称全称进行搜索"
                  style="width: 280px"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-search"
                    @click="companyInfoShow"
                  ></el-button>
                </el-input>
              </div>
              <div>
                <!-- <el-button class="normalBtn" type="primary" :loading="checkLoading" @click="check">核查备案</el-button> -->
                <el-button class="normalBtn" type="primary" @click="companyGroupShow"
                  >提取子企业</el-button
                >
                <el-button
                  class="normalBtn"
                  type="primary"
                  :loading="addLoading"
                  @click="companyGroup"
                  >新建主企业</el-button
                >
              </div>
            </div>
            <div class="">
              <div class="title">
                <span class="name" style="padding-left: 36px">企业名称</span>
                <span class="name">控股企业数量：{{ changeSum(sum) }}</span>
                <span class="text">控股比例</span>
                <!-- <span class="text">备案数量</span> -->
              </div>
              <tree
                show-checkbox
                node-key="company_name"
                :indent="0"
                :data="treeData"
                :load="loadNode"
                lazy
                :props="props"
                ref="tree"
                :check-strictly="true"
                :expand-on-click-node="false"
              >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <span class="name" :style="changeColor(data.color_flag)">{{ node.label }}</span>
                  <span>
                    <span class="text" :style="changeColor(data.color_flag)">{{
                      transferRate(data.rate)
                    }}</span>
                    <!-- 更新中不能点击 -->
                    <!-- <span v-if="data.icp_num == -1" class="text count" :style="changeColor(data.color_flag)">更新中</span> -->
                    <!-- <span v-else class="text count" @click="companyDetailShow(data)" :style="changeColor(data.color_flag)">{{data.icp_num}}</span> -->
                  </span>
                </span>
              </tree>
            </div>
          </div>
          <div class="icp-host-list" v-loading="icpHostObjLoading" v-show="!companyListVisible">
            <!-- icpHostListVisible 备用-->
            <p class="title">
              <el-button type="info" class="back" @click="back"
                ><i class="el-icon-arrow-left"></i
              ></el-button>
              {{ icpHostObj.name }}<span class="count">（{{ icpHostObj.list.length }}）</span>
            </p>
            <el-table
              :header-cell-style="{ background: '#F5F7FA', color: '#62666C' }"
              :data="icpHostObj.list"
              style="width: 100%"
            >
              <el-table-column type="index" label="序号" width="100"> </el-table-column>
              <!-- <el-table-column
                      prop="icp"
                      label="备案号"
                      width="270">
              </el-table-column> -->
              <el-table-column prop="domain" label="域名"> </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd elDialogAddClue"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleIntoGroup"
      width="500px"
      title="提取线索"
    >
      <div class="dialog-body">
        <el-form
          :model="groupRuleForm"
          :rules="groupRules"
          ref="groupRuleForm"
          style="padding: 0 !important"
          label-width="120px"
          class="demo-ruleForm"
        >
          <el-form-item label="选择场景" prop="name" placeholder="请选择场景">
            <el-select v-model="groupRuleForm.name">
              <el-option
                v-for="item in groupList"
                :label="item.name"
                :key="item.id"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleIntoGroup = false">关闭</el-button>
        <el-button class="highBtn" :loading="tiquLoading" @click="groupInsertSave('groupRuleForm')"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
// import $ from 'jquery';
import { mapGetters, mapState } from 'vuex'
import tree from '../../../components/tree/src/tree'
import { Loading } from 'element-ui'
import {
  companyGroupClue,
  companyCascadeEquity,
  companyEnquiry,
  companyIcpInfo,
  checkIcpRecord
} from '@/api/apiConfig/recommend.js'

const defaultGroupRuleForm = {
  name: ''
}

export default {
  props: ['dialogFormVisibleCompany'],
  components: {
    tree
  },
  data() {
    return {
      // 企业关系开始
      checkLoading: false,
      tiquLoading: false,
      addLoading: false,
      // dialogFormVisibleCompany: false,
      icpHostObjLoading: false,
      sum: 0,
      treeData: [],
      props: {
        label: 'company_name',
        isLeaf: 'leaf'
      },
      companyInfo: false,
      companyListVisible: false,
      icpHostListVisible: false,
      icpHostObj: {
        name: '',
        list: []
      },
      dialogFormVisibleIntoGroup: false,
      groupRuleForm: Object.assign({}, defaultGroupRuleForm),
      groupRules: {
        name: [{ required: true, message: '请选择场景', trigger: 'change' }]
      },
      companyName: '',
      groupList: []
      // 企业关系结束
    }
  },
  watch: {
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    dialogFormVisibleCompany(val) {
      this.companyName = ''
      this.relationData = []
      this.companyInfo = false
      //   if (val) {
      // this.sum = 0
      // this.treeData = [];
      //     if (this.relationData) {
      //       this.companyName = this.relationData.company_name
      //       this.treeData.push(this.relationData);
      //       this.companyInfo = true;
      //       this.companyListVisible = true;
      //     }
      //   }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange', 'getterWebsocketMessage'])
  },
  created() {
    this.activeName = sessionStorage.getItem('activeTabName')
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  methods: {
    transferRate(rate) {
      if (rate == '0.00%' || !rate) {
        return '-'
      } else {
        return rate.replace('.00', '')
      }
    },
    closeDia() {
      this.companyName = ''
      this.relationData = []
      this.companyInfo = false
      this.$emit('closeFun')
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (res.cmd == 'company_icp_info') {
        // websocket获取备案数量
        if (this.$refs.tree) {
          let nodes = this.$refs.tree.root.childNodes
          // this.setTreeItemValue(nodes,res);
        }
      } else if (res.cmd == 'company_icp_info_end') {
        // this.loadingFun(1);
      }
    },
    // 企业关系开始
    async companyInfoShow() {
      // 企业关系
      this.sum = 0
      if (this.companyName.length == 0) {
        this.$message.error('请输入企业名称全称')
        return
      }
      let params = {
        name: this.companyName,
        operate_company_id: this.currentCompany
      }
      let res = await companyEnquiry(params)
      this.treeData = []
      res.data.icp_num = -1
      this.treeData.push(res.data)
      this.companyInfo = true
      this.companyListVisible = true
    },
    async companyGroupShow() {
      let selectKeys = this.$refs.tree.getCheckedKeys()
      this.$emit('goExtract', selectKeys)
    },
    async companyDetailShow(data) {
      this.companyListVisible = false
      this.icpHostObjLoading = true
      let res = await companyIcpInfo({
        name: data.company_name,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.icpHostObjLoading = false
      })
      this.icpHostObjLoading = false
      this.icpHostObj.name = data.company_name
      this.icpHostObj.list = res.data
    },
    async companyGroup() {
      let selectKeys = this.$refs.tree.getCheckedKeys()
      this.$emit('saveNewGroup', selectKeys, this.companyName)
    },
    selectIcpList() {
      let selectNodes = this.$refs.tree.getCheckedNodes()
      let new_icp_list = []
      selectNodes.forEach((item) => {
        if (item.icp_list && item.icp_list.length > 0) {
          item.icp_list.forEach((c) => {
            c.company_name = item.company_name
            new_icp_list.push(c)
          })
        } else {
          new_icp_list.push({
            company_name: item.company_name,
            domain: '',
            name: ''
          })
        }
      })
      return new_icp_list
    },
    back() {
      this.companyListVisible = !this.companyListVisible
      if (this.companyListVisible) {
        this.companyInfo = true
      } else {
        this.companyInfo = false
      }
    },

    // 提取线索确认
    async groupInsertSave(formName) {
      let selectNodes = this.$refs.tree.getCheckedNodes()
      this.$refs[formName].validate(async (valid) => {
        if (!valid) {
          return false
        }
        let data = {
          group_id: this.groupRuleForm.name,
          icp_list: this.selectIcpList(),
          operate_company_id: this.currentCompany
        }
        this.tiquLoading = true
        let res = await companyGroupClue(data).catch(() => {
          this.tiquLoading = false
        })
        if (res.code == 0) {
          this.tiquLoading = false
          this.$message.success('操作成功！')
          this.$emit('closeFun')
          this.dialogFormVisibleIntoGroup = false
          this.$parent.getTaskResultData()
        }
      })
    },
    // 企业关系列表置灰
    changeColor(data) {
      if (data) {
        return { color: '#a7a7a7' }
      }
    },
    changeSum(data) {
      if (data == 0) {
        return '-'
      } else {
        return data
      }
    },
    async check() {
      // 企业关系
      let selectKedKeys = this.$refs.tree.getCheckedKeys()
      if (selectKedKeys.length == 0) {
        this.$message.error('请选择单位')
        return
      }
      let params = {
        company_list: selectKedKeys,
        operate_company_id: this.currentCompany
      }
      this.checkLoading = true
      let res = await checkIcpRecord(params).catch(() => {
        this.checkLoading = false
      })
      if (res.code == 0) {
        this.checkLoading = false
        this.loadingFun(0,res.message)
      }
    },
    loadingFun(type,message) {
      if (type == 0 && this.loadingInstance == null) {
        let options = {
          lock: true,
          text: '正在查询中...',
          spinner: 'el-icon-loading'
          // background: 'rgba(0, 0, 0, 0.7)'
        }
        this.loadingInstance = Loading.service(options)
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          setTimeout(() => {
            this.loadingInstance.close()
            this.loadingInstance = null
            this.$message.success(message||'操作成功！')
          }, 500)
        })
      } else {
        this.loadingInstance.text = '查询完成'
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          setTimeout(() => {
            this.loadingInstance.close()
            this.loadingInstance = null
          }, 500)
        })
      }
    },
    loadNode(node, resolve) {
      //leaf    true没有下级 false 有下级
      if (node.level === 0) {
        return resolve(this.treeData)
      }
      if (node.level > 2) return resolve([])

      let params = {
        company_id: node.data.company_id,
        operate_company_id: this.currentCompany
      }
      companyCascadeEquity(params).then((res) => {
        if (res.code == 0) {
          let data = res.data
          this.sum += res.data.length
          data.map((item) => {
            item.leaf = node.level == 1 ? true : false
            item.tree_level = node.level
            item.icp_num = -1
          })
          resolve(data)
          //结构处理
          // this.setTreeItem(this.treeData,node);
        }
      })
    },
    setTreeItem(data, node) {
      data.forEach((item) => {
        if (item.company_id == node.data.company_id) {
          let children = []
          node.childNodes.forEach((n) => {
            children.push(n.data)
          })
          item.children = children
        }
        if (item.children && item.children.length > 0) {
          this.setTreeItem(item.children, node)
        }
      })
    },
    setTreeItemValue(nodes, msg) {
      let { company_name, icp_list } = msg.data
      nodes.forEach(async (item, index) => {
        if (item.data.company_name == company_name) {
          item.data.icp_list = icp_list
          item.data.icp_num = icp_list.length
          if (item.data.icp_num == 0) {
            // 若websocket返回数据为0，则请求接口请求到子集数据
            // let res = await companyIcpInfo({name: company_name,operate_company_id: this.currentCompany});
            // item.data.icp_list = res.data;
          }
          //测试数据
          //item.data.icp_num = Math.floor(Math.random()*10+1);
        }
        if (item.childNodes && item.childNodes.length > 0) {
          this.setTreeItemValue(item.childNodes, msg)
        }
      })
    }
    // 企业关系结束
  }
}
</script>
<style lang="less" scoped>
/deep/ .elDialogAdd2 {
  .el-dialog .el-dialog__header {
    background: #f5f7fa;
    height: 44px;
    display: flex;
    align-items: center;
    padding: 0 16px !important;
    font-size: 14px !important;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #e9ebef;
    .el-dialog__title {
      font-size: 14px;
      color: #37393c !important;
    }
    .el-dialog__headerbtn {
      top: auto !important;
      right: 16px !important;
    }
  }
  .el-dialog .el-dialog__body {
    height: 500px;
    position: relative;
  }
  .back {
    margin-bottom: 10px;
    margin-right: 5px;
    padding: 0;
    width: 24px;
    height: 24px;
    border-radius: 2px;
    background-color: #e1e5ec;
    border-color: #e1e5ec;
    i {
      color: #62666c;
    }
  }
}
/deep/ .elDialogAdd2.add_img {
  .el-dialog .el-dialog__body {
    background-image: url('../../../assets/images/companyQuery.png');
    background-size: 100% 100%;
  }
}
.company-input {
  width: 400px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  margin-top: -14px;
  .search-btn {
    width: 100%;
    height: 40px;
    background: #2677ff;
    border-radius: 0px 4px 4px 0px;
    font-size: 16px;
    color: #fff;
    margin-left: 0;
  }
  /deep/ .el-input__inner {
    height: 40px;
    line-height: 40px;
    border-color: #2677ff;
  }
  /deep/ .el-input-group__append {
    padding: 0;
    width: 68px;
  }
}
.company-list {
  padding-right: 8px;
  .title {
    display: flex;
    height: 48px;
    line-height: 48px;
    background: #f5f7fa;
  }
  .name {
    flex: 1;
    white-space: normal;
    padding-right: 8px;
  }
  .text {
    display: inline-block;
    width: 100px;
    text-align: center;
  }
  .action {
    display: flex;
    justify-content: space-between;
    padding-bottom: 20px;
  }
  /deep/ .el-tree {
    max-height: 360px;
    overflow-y: auto;
  }
}
.icp-host-list {
  /deep/ .el-table__body-wrapper {
    height: 350px;
    overflow-y: auto;
  }
}
/deep/ .custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
}
.count {
  color: #2677ff;
  text-align: center;
}
</style>
