<template>
  <el-dialog
    class="elDialogAdd"
    @closed="extractClosed"
    :close-on-click-modal="false"
    :visible.sync="extractDialogVisible"
    width="500px"
  >
    <template slot="title"> 提取企业 </template>
    <div class="dialog-body">
      <el-form :model="extractForm">
        <el-form-item label="选择企业">
          <el-select v-model="selectCurrentId" placeholder="请选择分组">
            <el-option
              @click.native="changeCompany($event, item)"
              v-for="(item, index) in listData"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="隶属关系">
          <el-popover
            style="width: 100%"
            placement="bottom-start"
            v-model="isReverse"
            trigger="focus"
          >
            <el-input
              v-model="selectCompanyName"
              readonly
              style="font-size: 16px; font-weight: bold; color: #8c939d"
              placeholder="请选择隶属企业"
              slot="reference"
            >
              <i
                slot="suffix"
                v-if="!isReverse"
                class="el-input__icon el-icon-arrow-up is-reverse"
              ></i>
              <i slot="suffix" v-else class="el-input__icon el-icon-arrow-up"></i>
            </el-input>
            <div style="width: 300px; height: 200px; overflow: auto" v-if="selectCurrentId">
              <el-tree
                :data="selectTreeData"
                ref="tree"
                :props="defaultProps"
                node-key="id"
                :default-expanded-keys="[selectTreeData[0] && selectTreeData[0].id]"
                :load="(node, resolve) => loadSelectNode(node, resolve, true)"
                lazy
                @node-click="handleNodeClick"
              >
              </el-tree>
            </div>
          </el-popover>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="extractDialogVisible = false">取消</el-button>
      <el-button class="highBtn" type="primary" @click="saveCompanyLevel">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import { organizationGroupList, nextCompany, orgOtherSync } from '@/api/apiConfig/api.js'

export default {
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'company_name',
        isLeaf: 'is_leaf'
      },
      isReverse: false,
      extractForm: {},
      listData: [],
      selectTreeData: [],
      selectCurrentId: '',
      selectCompanyId: '',
      selectCompanyName: '',
      extractDialogVisible: false
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  methods: {
    open() {
      this.extractDialogVisible = true
      this.getData()
    },
    async saveCompanyLevel() {
      // await orgOtherSync()
      // this.$emit('save',)
    },
    handleNodeClick(data) {
      this.selectCompanyId = data.id
      this.selectCompanyName = data.company_name
    },
    // 获取分组列表
    async getData(isRefreshAll = false) {
      // isRefreshAll 是否刷新 提取弹窗上 的分组列表 true 更新不随筛选项的所有分组
      let res = await organizationGroupList({
        name: '',
        operate_company_id: this.currentCompany
      }).catch(() => {})
      if (res.code == 0 && res.data.length != 0) {
        this.listData = res.data
      } else {
        this.listData = []
      }
    },
    async changeCompany(e, val) {
      this.selectCompanyId = ''
      this.selectCurrentId = val.id
      this.selectCompanyName = ''
      this.currentOrgId = val.id
      // 切换组织架构获取下级企业
      this.getNextCompany(val, false, '1')
    },
    async getNextCompany(val, isDelUpdate = false, isNoPage = '', page, per_page) {
      if (isNoPage != 1) {
        this.wholeLoading = true
      }
      // this.companyAllInfoLoading = true
      if (!val || !val.company) {
        // 没有下级企业时处理
        this.operationTreeData = ''
        this.selectTreeData = []
        this.wholeLoading = false
        // this.companyAllInfoLoading = false
        return
      }

      val.company.root = true // 设置根企业
      val.company.isLeaf = val.is_leaf // 表示是否有子节点false 为有
      if (isDelUpdate || (val.company && val.id && !val.is_leaf)) {
        // 获取下一层的企业分页数据
        let res = await nextCompany({
          organization_group_id: this.selectCurrentId,
          no_page: isNoPage, // 1不分页
          parent_id: val.company.id,
          operate_company_id: this.currentCompany
        })
        val.company.page = 1
        let items = []
        if (isNoPage == 1) {
          items = res.data
        } else {
          items = res.data.items
        }
        let forLength = items.length
        for (let index = 0; index < forLength; index++) {
          const element = items[index]
          element.isLeaf = element.is_leaf // true 表示没有下级
          element.hasChildren = !element.is_leaf // true 表示没有下级
        }
        val.company.children = items || {}
        if (isNoPage == 1) {
          this.selectTreeData = [val.company]
        }
        // this.wholeLoading = false;
        // this.companyAllInfoLoading = false
      } else {
        // this.operationTreeData = val.company;
        // this.selectTreeData = [val.company];
        if (isNoPage == 1) {
          this.selectTreeData = [val.company]
        } else {
        }
        this.wholeLoading = false
        // this.companyAllInfoLoading = false
      }
    },
    extractClosed() {},

    async loadSelectNode(node, resolve, isSelectDialog) {
      if (node && node.level == 0) {
        return resolve(this.selectTreeData)
      }
      let organization_group_id = ''
      organization_group_id = this.selectCurrentId
      let res = await nextCompany({
        organization_group_id,
        no_page: '1',
        parent_id: node.data.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        resolve(res.data)
      }
    }
  }
}
</script>

<style></style>
