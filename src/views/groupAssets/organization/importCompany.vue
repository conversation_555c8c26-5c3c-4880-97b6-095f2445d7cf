<template>
  <el-dialog
    class="elDialogAdd"
    @close="$emit('dialogClose')"
    @open="opened"
    :close-on-click-modal="false"
    :visible="dialogFormVisible"
    width="600px"
  >
    <template slot="title"> 文件导入 </template>
    <div class="formItem">
      <span class="label"> 隶属关系: </span>
      <el-popover style="width: 100%" placement="bottom-start" v-model="isReverse" trigger="focus">
        <el-input
          v-model="selectCompanyName"
          readonly
          style="font-size: 16px; font-weight: bold; color: #8c939d"
          placeholder="请选择隶属企业"
          slot="reference"
        >
          <i slot="suffix" v-if="!isReverse" class="el-input__icon el-icon-arrow-up is-reverse"></i>
          <i slot="suffix" v-else class="el-input__icon el-icon-arrow-up"></i>
        </el-input>
        <div style="width: 445px; height: 200px; overflow: auto">
          <el-tree
            ref="tree"
            :props="defaultProps"
            node-key="id"
            :default-expanded-keys="[selectTreeData[0] && selectTreeData[0].id]"
            :load="(node, resolve) => loadSelectNode(node, resolve, true)"
            lazy
            @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </el-popover>
    </div>
    <div class="dialog-body">
      <p class="downloadClass" @click="download">
        <i class="el-icon-warning"></i>请点击下载
        <span>企业导入模板</span>
      </p>
      <el-upload
        class="upload-demo"
        drag
        :action="uploadAction"
        :headers="uploadHeaders"
        accept=".xlsx"
        :before-upload="beforeIpUpload"
        :on-success="ipUploadSuccess"
        :on-remove="uploadRemove"
        :on-error="ipUploadError"
        :on-exceed="handleExceed"
        :file-list="fileList"
      >
        <!-- :limit="uploadMaxCount" -->
        <!-- <i class="el-icon-upload"></i> -->
        <img class="icon-upload" src="@/assets/images/companyClue/fileUpload.png" alt="" />

        <div class="el-upload__text">{{ fileName }}</div>
        <div class="el-upload__text" v-if="!fileName"> 将文件拖到此处，或<em>点击上传</em> </div>
        <div class="el-upload__text" v-if="btnLoading">上传中请勿关闭弹窗</div>
        <div class="el-upload__text" v-if="fileName"><em>重新上传</em></div>
        <div class="el-upload__tip" slot="tip"> 支持上传xlsx 文件，且大小不超过20M </div>
      </el-upload>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="$emit('dialogClose')" id="account_check_cancel"
        >关闭</el-button
      >
      <el-button :loading="btnLoading" class="highBtn" @click="save" id="account_check_sure"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import { groupConfirm, nextCompany } from '@/api/apiConfig/api.js'

export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false
    },
    // selectTreeData: {
    //   type: [Array, Object],
    //   default: () => []
    // },
    currentOrgId: {
      type: [Number, String],
      default: null
    },
    currentRow: {
      type: Object,
      default: null
    }
  },
  mounted() {},
  data() {
    return {
      node: {},
      resolve: null,
      selectTreeData: [],
      isReverse: false, // 下拉框箭头方向
      selectCompanyId: '', // 选中归属企业的id
      selectCompanyName: '', // 选中归属企业的名称
      defaultProps: {
        children: 'children',
        label: 'company_name',
        isLeaf: 'is_leaf'
      },
      companyList: [],
      fileName: '',
      fileList: [],
      uploadMaxCount: 1,
      uploadAction: `${this.uploadSrcIp}/organization/group/import`,
      btnLoading: false,
      uploadHeaders: { Authorization: localStorage.getItem('token') }
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  watch: {
    currentRow: {
      handler(val) {
        if (val) {
          this.selectTreeData = [val.company]
          this.selectCompanyName = val.company && val.company.company_name
          this.selectCompanyId = val.company && val.company.id
        }
      },
      deep: true
    }
  },
  methods: {
    opened() {
      this.fileName = ''
      this.companyList = []
      this.fileList = []
      this.selectCompanyName = this.currentRow.company && this.currentRow.company.company_name
      this.selectCompanyId = this.currentRow.company && this.currentRow.company.id
      this.refreshNode()
    },
    refreshNode() {
      this.node.childNodes = []
      this.loadSelectNode(this.node, this.resolve)
    },
    async loadSelectNode(node, resolve, isSelectDialog) {
      if (node.level === 0) {
        this.node = node
        this.resolve = resolve
        return resolve([this.currentRow.company])
      }
      let organization_group_id = ''
      organization_group_id = this.currentOrgId
      let res = await nextCompany({
        organization_group_id,
        no_page: '1',
        parent_id: node.data.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        resolve(res.data)
      }
    },
    handleNodeClick(data) {
      this.selectCompanyId = data.id
      this.selectCompanyName = data.company_name
    },
    download() {
      window.location.href = '/downloadTemplate/导入企业模板.xlsx'
    },
    async save() {
      if (this.companyList.length == 0) {
        this.$message.error('请上传文件')
        return
      }
      this.btnLoading = true
      let res = await groupConfirm({
        organization_group_id: this.currentRow.id,
        parent_id: this.selectCompanyId,
        name: this.companyList,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        // this.$emit("changeContent");
        // 刷新页面
        this.$emit('updateList')
        this.$emit('dialogClose')
      }
      this.btnLoading = false
    },
    beforeIpUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过 20MB!')
      }
      return isLt1M
    },
    ipUploadSuccess(response, file, fileList) {
      this.companyList = []
      this.fileName = ''
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.companyList = response.data
        this.fileName = file.name
        // this.fileList.push(response.data.url)
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadRemove() {},
    ipUploadError(res) {
      let errorMsg = JSON.parse(res.message)
      this.$message.error(errorMsg)
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    }
  }
}
</script>

<style lang="less" scoped>
.icon-upload {
  width: 34px;
  margin-bottom: 12px;
}
.upload-demo /deep/.el-upload-dragger {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 168px !important;
  border: 1px dashed #c9cfdb;
  background-color: #eff2f7;
}
/deep/.el-upload__tip {
  margin-top: 12px;
}
/deep/.el-upload-list {
  display: none;
}
.formItem {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  .label {
    width: 85px;
  }
  // .
}
/deep/.el-dialog__body {
  margin-bottom: 10px;
}
/deep/.el-tree-node {
  .el-tree-node__content {
    height: 32px !important;
  }
  .el-tree-node__label {
    width: calc(100% - 50px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
