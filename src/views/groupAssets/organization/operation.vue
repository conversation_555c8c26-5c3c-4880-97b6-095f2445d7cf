<template>
  <div class="container" v-loading="wholeLoading">
    <div class="headerTitle">
      <div>
        <template v-if="preOrgId">
          <span class="goback" @click="goOperationBack">
            <i class="el-icon-arrow-left"></i>返回
          </span>
          <span class="spline">/</span>
        </template>
        企业架构管理
      </div>
      <el-button type="text" @click="dialogFormVisibleCompany = true">企业关系查询</el-button>
    </div>
    <div class="left">
      <div class="fixed">
        <div class="header">
          <div class="title">企业管理</div>
          <div class="addBtn" @click="newCompanyVisible = true">
            <img src="@/assets/images/companyClue/addCompany.png" alt="" />
            新增企业
          </div>
        </div>
        <div class="filterTab">
          <el-input
            v-model="searchName"
            placeholder="请输入关键字检索"
            id="keyword_keycheck"
            @keyup.enter.native="search"
          >
            <el-button slot="append" icon="el-icon-search"></el-button>
          </el-input>
        </div>
      </div>
      <div class="collapse" v-if="listData.length != 0">
        <el-collapse accordion v-model="currentOrgId">
          <el-collapse-item
            v-for="item in listData"
            :key="item.id"
            :name="item.id"
            @click.native="collapseChange($event, item)"
          >
            <template slot="title">
              <span class="title">{{ item.name }}</span>
              <div class="delIcon" @click.stop="delArray(item.id)">
                <i class="el-icon-delete"></i></div
            ></template>

            <div v-if="item.level_arr && item.level_arr.lenght != 0">
              <div class="info" v-for="(level, index, key) in item.level_arr" :key="key">
                <div class="label">{{ toChineseNumber(index) }}级企业：</div>
                <div class="num">{{ level }}</div>
              </div>
            </div>
            <div v-else>暂无子级企业</div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="collapse" v-else>
        <div class="emptyClass">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-kong"></use>
          </svg>
          <p>暂无数据</p>
        </div>
      </div>
    </div>
    <div class="right" v-show="listData && listData.length != 0">
      <OperationTable
        :total="total"
        :checkAll="checkAll"
        @updateCompanyNumber="updateCompanyNumber"
        @getNextCompany="getNextCompany"
        :loading="companyAllInfoLoading"
        :currentOrgId="currentOrgId"
        :currentRow="currentRow"
        :treeDataJson="operationTreeData"
        :selectTreeData="selectTreeData"
        :listData="allGroupListData"
        ref="operation"
      />
    </div>
    <div class="right" v-show="listData && listData.length == 0">
      <div class="emptyClass">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-kong"></use>
        </svg>
        <p>暂无数据</p>
      </div>
    </div>
    <prompt
      title="新增"
      placeholder="请输入要添加的企业"
      :visible="newCompanyVisible"
      @save="add"
      @close="newCompanyVisible = false"
      :loading="newCompanyLoading"
    />
    <companyRelation
      @goExtract="goExtract"
      @saveNewGroup="saveNewGroup"
      :dialogFormVisibleCompany="dialogFormVisibleCompany"
      :relationData="null"
      @closeFun="dialogFormVisibleCompany = false"
    />
    <el-dialog
      class="elDialogAdd"
      @closed="extractClosed"
      :close-on-click-modal="false"
      :visible.sync="extractDialogVisible"
      width="500px"
    >
      <template slot="title"> 提取企业 </template>
      <div class="dialog-body">
        <el-form>
          <el-form-item label="选择企业">
            <el-select v-model="selectCurrentId" placeholder="请选择分组">
              <el-option
                @click.native="changeCompany($event, item)"
                v-for="(item, index) in allGroupListData"
                :key="index"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="隶属关系">
            <el-popover
              style="width: 100%"
              placement="bottom-start"
              v-model="isReverse"
              trigger="focus"
            >
              <el-input
                v-model="selectCompanyName"
                readonly
                style="font-size: 16px; font-weight: bold; color: #8c939d"
                placeholder="请选择隶属企业"
                slot="reference"
              >
                <i
                  slot="suffix"
                  v-if="!isReverse"
                  class="el-input__icon el-icon-arrow-up is-reverse"
                ></i>
                <i slot="suffix" v-else class="el-input__icon el-icon-arrow-up"></i>
              </el-input>
              <div style="width: 300px; height: 200px; overflow: auto" v-if="selectCurrentId">
                <el-tree
                  :data="selectTreeData"
                  ref="tree"
                  :props="defaultProps"
                  node-key="id"
                  :default-expanded-keys="[selectTreeData[0] && selectTreeData[0].id]"
                  :load="(node, resolve) => loadSelectNode(node, resolve, true)"
                  lazy
                  @node-click="handleNodeClick"
                >
                </el-tree>
              </div>
            </el-popover>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="extractDialogVisible = false">取消</el-button>
        <el-button class="highBtn" type="primary" @click="saveCompanyLevel">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import companyRelation from './companyRelation.vue'
// import baseUse from '@/utils/baseUse'
import detectIndexTable from '../../home_set/detectIndexTable.vue'
import Org from './org'
import OperationTable from './operationTable'
import ImportCompany from './importCompany'
import utils from '@/utils/baseUse'
import prompt from '@/components/assets/prompt'
import { mapGetters, mapState, mapMutations } from 'vuex'
import {
  addCompany,
  organizationGroupList,
  nextCompany,
  orgGroupDel,
  groupConfirm
} from '@/api/apiConfig/api.js'

export default {
  name: '',
  components: { companyRelation, Org, OperationTable, ImportCompany, prompt, detectIndexTable },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'company_name',
        isLeaf: 'isLeaf'
      },
      extractDialogVisible: false,
      isReverse: false,
      selectCurrentId: '',
      selectCompanyName: '',
      extractSelectCompany: '',
      extractSelectArr: [],
      dialogFormVisibleCompany: false,
      total: 0,
      companyAllInfoLoading: false, // 切换分组之后 获取分组以及子企业信息加载
      newCompanyLoading: false,
      newCompanyVisible: false,
      newCompanyExtractLoading: false, // 将导入的其他企业 归属到 某个分组下的弹窗加载
      newCompanyExtractVisible: false, // 将导入的其他企业 归属到 某个分组下的弹窗展示
      allGroupListData: [], // 提取弹窗的分组列表
      checkAll: false, // 是否选中全部
      searchName: '', // 侧边搜索条件
      isReverse: false, // 提取弹窗 的下拉列表的 箭头指向
      selectTreeData: [], // 提取企业弹窗的某个分组下的企业架构
      operationTreeData: '', // 操作企业的操作表格列表

      currentOrgId: '', // 当前选中分组的id
      currentRow: {}, // 当前选中分组的信息
      listData: [], // 当前侧边分组列表

      wholeLoading: false, // 全页面加载
      preOrgId: ''
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentRow = {}
      this.currentOrgId = ''
      this.checkAll = false
      localStorage.setItem('orgId', '')
      this.preOrgId = ''
      this.operationTreeData = ''
      this.selectTreeData = []
      // 页面初始化
      this.getData(true)
    }
    // '$route.query.id'(val) {
    //   if (!val) {
    //     localStorage.setItem('orgId', '')
    //   }
    //   this.preOrgId = localStorage.getItem('orgId')
    // }
  },
  async mounted() {
    if (!this.$route.query.isFormOrg) {
      localStorage.setItem('orgId', '')
    }
    this.preOrgId = localStorage.getItem('orgId')
    // 防止刷新时 操作页面回到架构页面
    let isGetCompany = sessionStorage.getItem('isGetCompany')
    if (Boolean(isGetCompany)) {
      this.getData(true)
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    // 提取企业归属到某企业下去 并保存
    async saveCompanyLevel() {
      if (this.extractSelectArr.length == 0) return this.$message.warning('请选择要提取的企业')
      let res = await groupConfirm({
        operate_company_id: this.currentCompany,
        organization_group_id: this.selectCurrentId,
        parent_id: this.selectCompanyId,
        name: this.extractSelectArr,
        organization_group_name: this.extractSelectCompany
      })
      if (res.code == 0) {
        this.$message.success('提取成功')
        this.extractDialogVisible = false
        this.dialogFormVisibleCompany = false
        this.getNextCompany(this.currentRow, false, '0')
      }
    },
    handleNodeClick(data) {
      this.selectCompanyId = data.id
      this.selectCompanyName = data.company_name
    },
    extractClosed() {
      this.selectCurrentId = ''
      this.selectCompanyName = ''
      this.selectTreeData = []
    },
    // 树级选择框
    async changeCompany(e, val) {
      this.selectCurrentId = val.id
      this.selectCompanyName = ''
      // 切换组织架构获取下级企业
      this.getNextCompany(val, false, '1')
    },
    goExtract(selectKeys, companyName) {
      this.extractDialogVisible = true
      this.extractSelectArr = selectKeys
      this.extractSelectCompany = companyName
    },
    companyInfoShow() {
      this.$refs.operation.companyInfoShow()
    },

    // 提取企业
    saveOtherCompany() {
      this.newCompanyExtractVisible = true
      this.newCompanyExtractLoading = false
    },
    // 新建主企业
    async saveNewGroup(selectKeys, companyName) {
      if (selectKeys.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      this.$confirm(
        '将以查询企业为目标建立一个主企业，将全部勾选的子企业归属到该主企业下，是否确定',
        '新建主企业',
        {
          distinguishCancelAndClose: true, // 设置关闭和取消独立
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'keyword_del_cancel',
          confirmButtonClass: 'keyword_del_sure',
          customClass: 'keyword_del',
          type: 'warning'
        }
      ).then(async () => {
        let res = await addCompany({
          operate_company_id: this.currentCompany,
          name: [...selectKeys, companyName],
          organization_group_name: companyName
        }).catch(() => {
          // this.newCompanyLoading = false;
        })
        if (res.code == 0) {
          // 更新整个页面
          this.getData(true)
          this.$message.success('创建成功')
        }
      })
    },

    // 删除组织
    async delArray(id) {
      this.$confirm(
        `确定删除该组织吗，删除组织后，本模块以及资产台账中的数据将会同步删除，是否确认`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(async () => {
          let res = await orgGroupDel({
            id,
            operate_company_id: this.currentCompany
          })
          if (res.code == 0) {
            this.$message.success('删除成功')
            this.currentOrgId = null
            this.getData(true)
          }
        })
        .catch(() => {})
    },
    // 返回组织架构资产管理
    goOperationBack() {
      sessionStorage.setItem('menuId', '12')
      this.changeMenuId('12')
      // this.$router.go(-1)
      let preOrgId = localStorage.getItem('orgId')
      this.$router.push({ name: 'organization', params: { id: preOrgId } })
    },
    // 将英文数字转为中文数字
    toChineseNumber(val) {
      return utils.toChineseNumber(val)
    },

    // 组织架构树 分组切换
    async collapseChange(e, val) {
      this.total = 0
      this.checkAll = false // 是否全选
      this.currentRow = val // 当前展开企业的信息
      this.currentOrgId = val.id // 当前展开企业的ID
      if (this.$route.query.isFormOrg) {
        localStorage.setItem('orgId', val.id)
      }
      this.getNextCompany(val)
    },
    // 获取一级企业
    // isDelUpdate 是否是删除操作 删除后 不通过刷新列表
    // isNoPage 1 不分页 更新提取弹窗上的子企业列表 默认更新表格上的子企业列表
    async getNextCompany(val, isDelUpdate = false, isNoPage = '', page, per_page) {
      if (isNoPage != 1) {
        this.wholeLoading = true
      }
      // this.companyAllInfoLoading = true
      if (!val || !val.company) {
        // 没有下级企业时处理
        this.operationTreeData = ''
        this.selectTreeData = []
        this.wholeLoading = false
        // this.companyAllInfoLoading = false
        return
      }

      val.company.root = true // 设置根企业
      val.company.isLeaf = val.is_leaf // 表示是否有子节点false 为有
      if (isDelUpdate || (val.company && val.id && !val.is_leaf)) {
        // 获取下一层的企业分页数据
        let res = await nextCompany({
          organization_group_id: this.currentOrgId,
          page,
          per_page,
          no_page: isNoPage, // 1不分页
          parent_id: val.company.id,
          operate_company_id: this.currentCompany
          // is_first: val.is_first //is_first == 1 会返回'其他'项
        })
        val.company.page = 1
        let items = []
        if (isNoPage == 1) {
          items = res.data
        } else {
          items = res.data.items
        }
        let forLength = items.length
        for (let index = 0; index < forLength; index++) {
          const element = items[index]
          element.isLeaf = element.is_leaf // true 表示没有下级
          element.hasChildren = !element.is_leaf // true 表示没有下级
        }
        val.company.children = items || {}
        if (isNoPage == 1) {
          this.selectTreeData = [val.company]
        } else {
          this.operationTreeData = JSON.stringify(val.company)
          this.total = res.data.total
        }
        this.wholeLoading = false
        // this.companyAllInfoLoading = false
      } else {
        // this.operationTreeData = val.company;
        // this.selectTreeData = [val.company];
        if (isNoPage == 1) {
          this.selectTreeData = [val.company]
        } else {
          this.operationTreeData = JSON.stringify(val.company)
        }
        this.wholeLoading = false
        // this.companyAllInfoLoading = false
      }
    },
    // 更新分组的列表
    async updateCompanyNumber(isUpdateMainCompany = false) {
      let res = await organizationGroupList({
        name: this.searchName,
        operate_company_id: this.currentCompany
      }).catch(() => {})
      if (res.code == 0) {
        // 更新列表
        this.listData = res.data
        if (isUpdateMainCompany) {
          // 更新主企业信息
          // this.currentRow
          res.data.forEach((item) => {
            if (this.currentRow.id == item.id) {
              this.$set(this.currentRow, 'company', item.company)
              // this.currentRow.company = item.company
              this.getNextCompany(this.currentRow)
            }
          })
        }
      }
    },
    // 只更新右侧企业信息
    search() {
      this.wholeLoading = true
      this.getData()
    },
    // 获取分组信息
    async getData(isRefreshAll = false) {
      // isRefreshAll 是否刷新 提取弹窗上 的分组列表 true 更新不随筛选项的所有分组
      let res = await organizationGroupList({
        name: this.searchName,
        operate_company_id: this.currentCompany
      }).catch(() => {})
      if (res.code == 0 && res.data.length != 0) {
        let currentRow = {}
        if (!this.preOrgId) {
          res.data.forEach((item) => {
            item.is_first = 1
          })
          currentRow = res.data[0] || {}
          this.currentOrgId = res.data[0] && res.data[0].id
        } else {
          res.data.forEach((item) => {
            item.is_first = 1
            if (this.preOrgId == item.id) {
              currentRow = item
            }
          })
          this.currentOrgId = Number(this.preOrgId)
        }
        this.listData = res.data
        if (isRefreshAll) {
          // 是否刷新 不跟随筛选项得到 全部组织架构的列表
          this.allGroupListData = res.data
        }

        // this.currentOrgId = (!isRefreshAll && this.currentOrgId)? this.currentOrgId : (res.data[0] && res.data[0].id);
        this.currentRow = currentRow
        // this.currentRow = this.currentRow.id && !isRefreshAll
        //   ? this.currentRow
        //   : res.data[0] || {};
        // 从其他资产管理 跳转过来以后 默认高亮前一个选中分组项
        this.getNextCompany(this.currentRow)
      } else {
        this.listData = []
        if (isRefreshAll) {
          this.allGroupListData = res.data
        }
        this.currentOrgId = null
        this.currentRow = {}
        this.wholeLoading = false
      }
      // this.currentOrgId = 22
    },
    async add(val) {
      if (!val) {
        this.$message.error('请输入要添加的企业')
        return
      }
      this.newCompanyLoading = true
      let res = await addCompany({
        operate_company_id: this.currentCompany,
        name: [val],
        organization_group_name: val
      }).catch(() => {
        this.newCompanyLoading = false
      })
      if (res.code == 0) {
        this.getData(true)
        this.newCompanyLoading = false
        this.newCompanyVisible = false
        this.$message({
          type: 'success',
          message: '添加成功'
        })
      }
    },
    async loadSelectNode(node, resolve, isSelectDialog) {
      if (node.level === 0) {
        return resolve(this.selectTreeData)
      }
      let organization_group_id = ''
      organization_group_id = isSelectDialog ? this.selectCurrentId : this.currentOrgId
      let res = await nextCompany({
        organization_group_id,
        no_page: '1',
        parent_id: node.data.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        let items = res.data
        let forLength = items.length
        for (let index = 0; index < forLength; index++) {
          const element = items[index]
          element.isLeaf = element.is_leaf // true 表示没有下级
          element.hasChildren = !element.is_leaf // true 表示没有下级
        }

        resolve(res.data)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  display: flex;
  height: 100%;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  .left {
    position: relative;
    width: 336px;
    height: 100%;
    padding: 102px 20px 16px;
    background-color: #fff;
    box-sizing: border-box;
    border-right: 1px solid #e9ebef;
    .fixed {
      position: absolute;
      top: 16px;
      width: 100%;
      box-sizing: border-box;
      margin-left: -20px;
      padding: 0 20px;
      .header {
        display: flex;
        justify-content: space-between;
        height: 22px;
        margin-bottom: 14px;
        .addBtn {
          cursor: pointer;
          font-size: 14px;
          font-weight: normal;
          line-height: 20px;
          letter-spacing: 0px;
          color: #2677ff;
        }
      }
      .filterTab {
        margin-bottom: 20px;
      }
    }
    .collapse {
      width: 100%;
      overflow: auto;
      height: 100% !important;
      .title {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .right {
    width: calc(100% - 336px);
    display: flex;
    flex-direction: column;
    .right-header {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 64px !important;
      width: 100%;
      padding: 0 20px;
      background-color: #fff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    }
    .content {
      box-sizing: border-box;
      flex: 1;
      height: calc(100% - 64px);
      background: #fff;
      border: 1px solid #eee;
    }
  }
}
/deep/.el-collapse {
  border: 0;

  .el-collapse-item {
    box-sizing: border-box;
    // height: 56px;
    border: 1px solid #e9ebef;
    padding: 0 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    &:hover {
      .el-collapse-item__header {
        .delIcon {
          display: block;
          color: red;
        }
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
    // box-sizing: border-box;
    .el-collapse-item__header {
      display: flex;
      justify-content: space-between;
      border-bottom: none;
      transition: none;
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;
      .delIcon {
        display: none;
      }
      &.is-active {
        border-bottom: 2px solid #e9ebef;
      }
      .el-collapse-item__arrow {
        display: none;
      }
    }
    .el-collapse-item__wrap {
      border-bottom: none;
    }
    .el-collapse-item {
      div:first-child {
        margin-bottom: 39px;
      }
    }
    .el-collapse-item__content {
      padding-top: 16px;
      .info {
        display: flex;
        line-height: 20px;
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: normal;
        color: #62666c;
        .label {
          margin-right: 5px;
        }
        .num {
          color: #37393c;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    &.is-active {
      border: 2px solid #2677ff;
      background: linear-gradient(90deg, #ffffff 0%, #eff5ff 99%);
      .el-collapse-item__wrap {
        background-color: transparent;
      }
      .el-collapse-item__header {
        color: #2677ff;
        background: linear-gradient(90deg, #ffffff 0%, #eff5ff 99%);
      }
    }
  }
}

/deep/.el-dialog__body {
  min-height: 128px !important;
}

/deep/.el-form-item {
  display: flex;
  .el-form-item__content {
    flex: 1;
  }
}
/deep/.el-tree-node {
  .el-tree-node__content {
    height: 32px;
  }
}

.emptyClass {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
  vertical-align: middle;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
.el-input__icon {
  &.el-icon-arrow-up.is-reverse {
    transform: rotate(180deg);
  }
}
.headerTitle {
  width: 100%;
  display: flex;
  justify-content: space-between;
  /deep/.el-button {
    padding: 0 20px;
  }
}
</style>
