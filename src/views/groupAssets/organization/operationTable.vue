<template>
  <div class="operContainer" v-loading="loading">
    <div class="header">
      <div class="headerTop">
        <div class="info">
          <div class="logo">
            <img src="@/assets/images/companyClue/companyLogo.png" alt="" />
          </div>
          <div class="nameTag">
            <div class="name">
              {{ treeData.company_name || '-' }}
              <el-button type="text" size="small" @click="editOne(treeData, 'main')" id="user_edit"
                >编辑</el-button
              >
            </div>
            <div class="tag">
              <template v-if="treeData.tag && JSON.parse(treeData.tag).length !== 0">
                <span
                  class="tagBlock"
                  v-for="(item, index) in JSON.parse(treeData.tag).splice(0, 3)"
                  :key="index"
                >
                  {{ item }}
                </span>
                <el-popover placement="top" width="400" trigger="hover">
                  <div class="tagContainer">
                    <span
                      class="tagBlock popover"
                      v-for="(item, index) in JSON.parse(treeData.tag)"
                      :key="index"
                    >
                      {{ item }}
                    </span>
                  </div>
                  <span style="margin-right: 10px" class="tagMore" slot="reference">...更多</span>
                </el-popover>
              </template>
              <template v-else> 暂无 </template>
              <el-tooltip class="item" effect="dark" content="编辑标签" placement="top">
                <span @click="setTag(treeData, 'main')">
                  <svg class="icon svg-icon" aria-hidden="true">
                    <use xlink:href="#icon-tag"></use>
                  </svg>
                </span>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="oper">
          <el-checkbox
            class="checkboxAll"
            id="keyword_all"
            v-model="checkedAll"
            @change="checkAllChange"
            >选择全部</el-checkbox
          >
          <el-button class="normalBtnRe" type="primary" @click="delCompanyConfirm">删除</el-button>
          <el-button class="normalBtn" type="primary" @click="createCompanyBatch"
            >新建子企业</el-button
          >
        </div>
      </div>
      <div class="middleLine"></div>
      <div class="headerBottom">
        <div class="item">
          <span class="label">社会信用代码：</span>
          <span class="value">{{ treeData.credit_code || '-' }}</span>
        </div>
        <div class="item">
          <span class="label">联系人：</span>
          <span class="value">{{ treeData.oper_name || '-' }}</span>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="tableWrap">
        <el-table
          border
          :data="tableData"
          :key="treeData.id"
          v-loading="loading"
          :load="loadNode"
          @selection-change="handleSelectionChange"
          @select-all="selectAll"
          @select="selectOne"
          @expand-change="expandRow"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          lazy
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            default-expand-all
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :label="item.label"
            :fixed="item.fixed"
          >
            <template slot-scope="scope">
              <template v-if="item.name == 'company_name' && scope.row[item.name]">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="scope.row[item.name]"
                  placement="top"
                >
                  <span>
                    {{ scope.row[item.name] }}
                  </span>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="编辑标签" placement="top">
                  <span
                    class="cellTag"
                    v-if="!scope.row.tag || JSON.parse(scope.row.tag).length == 0"
                    @click="setTag(scope.row)"
                  >
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#icon-tag"></use>
                    </svg>
                  </span>
                </el-tooltip>
                <div v-if="scope.row.tag && JSON.parse(scope.row.tag).length !== 0">
                  <span
                    class="tagBlock"
                    v-for="(item, index) in JSON.parse(scope.row.tag).splice(0, 3)"
                    :key="index"
                  >
                    {{ item }}
                  </span>
                  <el-popover placement="top" width="400" trigger="hover">
                    <div class="tagContainer">
                      <span
                        class="tagBlock popover"
                        v-for="(item, index) in JSON.parse(scope.row.tag)"
                        :key="index"
                      >
                        {{ item }}
                      </span>
                    </div>
                    <span style="margin-right: 10px" class="tagMore" slot="reference">...更多</span>
                  </el-popover>
                  <el-tooltip class="item" effect="dark" content="编辑标签" placement="top">
                    <span @click="setTag(scope.row)">
                      <svg class="icon svg-icon" aria-hidden="true">
                        <use xlink:href="#icon-tag"></use>
                      </svg>
                    </span>
                  </el-tooltip>
                </div>
              </template>
              <el-tooltip
                v-else-if="scope.row[item.name]"
                class="item"
                effect="dark"
                :content="scope.row[item.name]"
                placement="top"
              >
                <span>
                  {{ scope.row[item.name] }}
                </span>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="100">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="editOne(scope.row)" id="user_edit"
                >编辑</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="delCompanyConfirm(scope.row)"
                id="user_edit"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[3, 10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <prompt
      inputType="textarea"
      :rows="5"
      label="标签"
      title="标记标签"
      placeholder="请输入标签，可以输入多个，多个标签使用分号或换行分隔"
      :visible="newTagVisible"
      :rowValue="currentTagValue"
      @save="onSubmitBtn"
      @close="newTagVisible = false"
      :loading="newTagLoading"
    />
    <ImportCompany
      @updateList="updateList"
      :currentRow="currentRow"
      :currentOrgId="currentOrgId"
      :selectTreeData="selectTreeData"
      @dialogClose="importClose"
      :dialogFormVisible="importDialogVisible"
    />

    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="editDialogVisible"
      width="500px"
    >
      <template slot="title"> 编辑 </template>
      <div class="dialog-body">
        <el-form :model="editForm" label-width="110px">
          <el-form-item label="企业名称：">
            <el-input
              v-model="editForm.company_name"
              placeholder="请输入企业名称"
              clearable
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="社会信用代码：">
            <el-input
              v-model="editForm.credit_code"
              placeholder="请输入社会信用代码"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="邮箱：">
            <el-input v-model="editForm.email" placeholder="请输入邮箱" clearable></el-input>
          </el-form-item>
          <el-form-item label="联系人：">
            <el-input v-model="editForm.oper_name" placeholder="请输入联系人" clearable></el-input>
          </el-form-item>
          <el-form-item label="联系方式：">
            <el-input
              v-model="editForm.contact_phone"
              placeholder="请输入联系方式"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="editDialogVisible = false">取消</el-button>
        <el-button class="highBtn" type="primary" @click="editCompanyConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import prompt from '@/components/assets/prompt.vue'
import ImportCompany from './importCompany.vue'
import {
  orgCompanyInfoEdit,
  delOneCompany,
  nextCompany,
  orgCompanyTag
} from '@/api/apiConfig/api.js'

export default {
  components: {
    prompt,
    ImportCompany
  },
  props: {
    total: {
      type: Number,
      default: 0
    },
    selectTreeData: {
      // 主企业 以及 分页的子企业树
      type: [Array, Object],
      default: () => []
    },
    treeDataJson: {
      // 分页的子企业树
      type: String,
      default: ''
    },
    // treeDataJson: { // 分页的子企业树
    //   type: [Array, Object],
    //   default: () => []
    // },

    currentRow: {
      // 选中的分组
      type: Object,
      default: () => ({})
    },
    currentOrgId: {
      // 选中的分组 id
      type: [Array, Number, String],
      default: ''
    },
    loading: {
      // 右侧区域loading
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      extractSelectCompany: '',
      extractSelectArr: [],
      editForm: {},
      editDialogVisible: false,
      importDialogVisible: false,
      currentTagRow: {},
      currentTagValue: '',
      currentTagType: '',
      newTagLoading: false,
      newTagVisible: false,
      // listData:[],

      operationTreeData: [],
      selectCompanyId: '',
      selectCompanyName: '',
      relationData: null,
      dialogFormVisibleCompany: false,
      checkedAll: false,
      tableData: [],
      // loading: false,
      tableHeader: [
        {
          label: '企业名称',
          name: 'company_name',
          minWidth: '200',
          fixed: 'left'
        },
        {
          label: '社会信用代码',
          name: 'credit_code',
          minWidth: '150'
        },
        {
          label: '邮箱',
          name: 'email',
          minWidth: '150'
        },
        {
          label: '联系人',
          name: 'oper_name',
          minWidth: '150'
        },
        {
          label: '联系方式',
          name: 'contact_phone',
          minWidth: '150'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      treeData: {},
      checkedArr: []
    }
  },
  watch: {
    currentRow: {
      handler(val) {},
      deep: true
    },
    treeDataJson: {
      handler(val) {
        let data = val ? JSON.parse(val) : {}
        this.treeData = data
        this.tableData = data && data.children
        this.checkAllChange()
      },
      deep: true
    },
    currentOrgId() {
      this.currentPage = 1
      this.checkedArr = []
      this.checkedAll = false
      this.$refs.eltable.clearSelection()
    }
  },
  computed: {
    ...mapState(['currentCompany'])
    // tableData() {
    //   return this.treeData.children
    // }
  },
  methods: {
    expandRow(row, expanded) {
      row.isExpand = expanded
      if (row.isExpand) {
        if (this.checkedAll) {
          this.setChildren(row.children, true)
        } else {
          // 先选中再展开时的选中状态 根据该项与已选中的项目作匹配 匹配到就要勾选没有就不勾选
          const tableDataIds = this.checkedArr.map((j) => j.id)
          let isSelect = tableDataIds.includes(row.id)
          this.setChildren(row.children, isSelect)
        }
      }
    },
    selectOne(selection, row) {
      const hasSelect = selection.some((el) => {
        return row.id === el.id
      })
      if (hasSelect) {
        if (row.children && row.isExpand) {
          // 解决子组件没有被勾选到
          this.setChildren(row.children, true)
        } else {
          this.toggleSelection(row, true)
        }
      } else {
        if (row.children && row.isExpand) {
          this.setChildren(row.children, false)
        } else {
          this.toggleSelection(row, false)
        }
      }
    },
    selectAll(selection, select) {
      // tabledata第一层只要有在selection里面就是全选
      const isSelect = selection.some((el) => {
        const tableDataIds = this.tableData.map((j) => j.id)
        return tableDataIds.includes(el.id)
      })
      // tableDate第一层只要有不在selection里面就是全不选
      const isCancel = !this.tableData.every((el) => {
        const selectIds = selection.map((j) => j.id)
        return selectIds.includes(el.id)
      })
      if (isSelect) {
        selection.map((el) => {
          if (el.children) {
            // 解决子组件没有被勾选到
            this.setChildren(el.children, true)
          }
        })
      }
      if (isCancel) {
        this.tableData.map((el) => {
          if (el.children) {
            // 解决子组件没有被勾选到
            this.setChildren(el.children, false)
          }
        })
      }
    },
    setChildren(children, type) {
      // 编辑多个子层级
      children.map((j) => {
        j.isChecked = type
        this.toggleSelection(j, type)
        if (j.children) {
          this.setChildren(j.children, type)
        }
      })
    },
    toggleSelection(row, select) {
      if (row) {
        this.$nextTick(() => {
          this.$refs.eltable && this.$refs.eltable.toggleRowSelection(row, select)
        })
      }
    },
    extractClosed() {
      this.selectCurrentId = ''
      this.selectCompanyName = ''
      this.$parent.selectTreeData = []
    },
    importClose() {
      this.importDialogVisible = false
    },
    // 刷新子企业列表
    updateList() {
      this.currentRow.is_leaf = false
      this.$parent.updateCompanyNumber()
      this.currentPage = 1
      this.$emit('getNextCompany', this.currentRow, false, '', this.currentPage, this.pageSize)
      this.$forceUpdate()
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData &&
          this.tableData.length != 0 &&
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
            if (row.children) {
              this.setChildren(row.children, true)
            }
          })
      } else {
        this.$refs.eltable.clearSelection()
        this.tableData &&
          this.tableData.length != 0 &&
          this.tableData.forEach((row) => {
            if (row.children) {
              this.setChildren(row.children, false)
            }
          })
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    async delCompanyConfirm(row) {
      let ids = []
      let is_all = '0'
      if (this.checkedAll) {
        is_all = '1'
        if (this.total == 0) {
          this.$message.error('暂无可删除的数据')
          return
        }
      } else {
        is_all = '0'
        ids = row.id ? [row.id] : this.checkedArr.map((item) => item.id)
        if (ids.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
      }
      this.$confirm(
        `确定删除该企业吗，删除企业后，本模块以及资产台账中的数据将会同步删除，是否确认`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        let res = await delOneCompany({
          is_all,
          organization_group_id: this.currentOrgId,
          company_id: ids,
          operate_company_id: this.currentCompany
        })
        if (res.code == 0) {
          this.updateList()
          this.$refs.eltable.clearSelection()
        }
      })
    },
    async editCompanyConfirm() {
      let res = await orgCompanyInfoEdit({
        ...this.editForm,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.$message.success('编辑成功')
        this.editDialogVisible = false
        // 编辑成功更新数据
        this.currentPage = 1
        if (this.currentTagType == 'main') {
          this.$emit('updateCompanyNumber', true)
        } else {
          this.$emit('getNextCompany', this.currentRow, false, '', this.currentPage, this.pageSize)
        }
      }
    },
    createCompanyBatch() {
      this.$emit('getNextCompany', this.currentRow, false, '1')
      this.importDialogVisible = true
    },
    setTag(row, type) {
      this.currentTagRow = row
      this.currentTagValue = row.tag ? JSON.parse(row.tag).join('\r') : ''
      this.newTagVisible = true
      this.currentTagType = type
    },
    async onSubmitBtn(val) {
      if (!val) {
        this.$message.error('请输入标签')
        return
      }
      let tags = val.split(/[；|;|\r\n]/).filter((item) => {
        return item.trim()
      })
      this.newTagLoading = true
      let res = await orgCompanyTag({
        tag: tags,
        company_id: this.currentTagRow.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        // 更新子企业
        // 更新主企业

        this.newTagVisible = false
        this.newTagLoading = false
        this.$message({
          type: 'success',
          message: '操作成功'
        })
        if (this.currentTagType == 'main') {
          this.$emit('updateCompanyNumber', true)
        } else {
          this.$emit('getNextCompany', this.currentRow, false, '', this.currentPage, this.pageSize)
        }
      }
    },
    editOne(row, type) {
      this.editForm = JSON.parse(JSON.stringify(row))
      this.editForm.company_id = this.editForm.id
      this.editDialogVisible = true
      this.currentTagType = type
    },
    handleSelectable(row) {
      return !(this.checkedAll || row.isChecked)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.$emit('getNextCompany', this.currentRow, false, '', this.currentPage, this.pageSize)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.$emit('getNextCompany', this.currentRow, false, '', this.currentPage, this.pageSize)
    },
    async loadNode(node, treeNode, resolve) {
      let organization_group_id = ''
      organization_group_id = this.currentOrgId
      let res = await nextCompany({
        organization_group_id,
        no_page: '1',
        parent_id: node.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        let items = res.data
        let forLength = items.length
        for (let index = 0; index < forLength; index++) {
          const element = items[index]
          element.isLeaf = element.is_leaf // true 表示没有下级
          element.hasChildren = !element.is_leaf // true 表示没有下级
        }
        node.children = items
        resolve(items)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.operContainer {
  height: 100%;
  width: 100%;
  background-color: #fff;
  .header {
    padding: 16px 20px;

    background: linear-gradient(180deg, #ffffff 0%, #e3eeff 100%);
    .headerTop {
      display: flex;
      justify-content: space-between;
    }

    .info {
      display: flex;
      align-items: center;
      flex-direction: row;
      .logo {
        width: 56px;
        height: 100%;
        margin-right: 8px;
        img {
          width: 100%;
        }
      }
      .nameTag {
        height: calc(100% - 8px);
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        padding: 4px 0;
      }
    }
    .headerBottom {
      width: 100%;
      display: flex;
      .item {
        flex: 1;
        display: inline-block;
        width: 50%;
        .label {
          color: #62666c;
        }
        .value {
          color: #37393c;
        }
      }
    }
  }
  .content {
    height: calc(100% - 170px);
    padding: 16px 20px;
    .tableWrap {
      height: calc(100% - 56px);
      border-radius: 4px;
    }
  }
}
.middleLine {
  width: 100%;
  height: 1px;
  margin: 16px 0;
  background-color: #e9ebef;
}

/deep/.el-input__icon {
  line-height: 32px;
  &.el-icon-arrow-up.is-reverse {
    transform: rotate(180deg);
  }
}

/deep/.el-tree-node {
  .el-tree-node__content {
    height: 32px !important;
  }
}
.tagContainer {
  max-height: 100px;
}
.tagBlock {
  height: 18px;
  padding: 3px 8px;
  margin-right: 8px;
  line-height: 18px;
  border-radius: 2px;
  color: #2677ff;
  background: rgba(38, 119, 255, 0.12);
  &:last-child {
    margin-right: 0;
  }
  &.popover {
    float: left;
    margin-top: 4px;
  }
}
/deep/.el-form-item__content {
  margin-left: 0 !important;
}
.tagMore {
  margin-right: 10px;
  color: #2677ff;
  cursor: pointer;
}
/deep/.el-button--text {
  color: #2677ff;
}
/deep/.cell {
  position: relative;
  width: calc(100% - 10px);

  .cellTag {
    position: absolute;
    right: -8px;
    width: 20px;
    height: 100%;
    // background-color: #fff;
  }
}
</style>
