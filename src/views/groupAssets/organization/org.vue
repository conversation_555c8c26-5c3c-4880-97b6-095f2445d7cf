<template>
  <div v-loading="orgLoading" class="treeContainer">
    <div v-if="isShow && treeDataCopy.id" id="mainContent">
      <zm-tree-org
        ref="treeRefs"
        node-key="id"
        :data="treeDataCopy"
        :horizontal="horizontal"
        :collapsable="collapsable"
        :node-draggable="false"
        :only-one-node="onlyOneNode"
        :clone-node-drag="cloneNodeDrag"
        :tool-bar="toolBar"
        :default-expand-level="1"
        :define-menus="defineMenus"
        :node-delete="handleOnNodeDelete"
        :lazy="lazy"
        :load="loadData"
        :center="true"
      >
        <!-- 利用插槽实现自定义节点 -->
        <template v-slot="{ node }">
          <div
            class="card-main"
            :class="{ active: node.isChecked }"
            @click="
              () => {
                currentId = node.id
              }
            "
          >
            <div v-if="node.last" class="more" @click="getMoreCompany(node)"> 更多 </div>
            <div :class="node.isRoot ? 'top-position top-position-root' : 'top-position'">
              <div class="logo" v-if="node.company_name == '其他'">
                <img src="@/assets/images/companyClue/companyLogo.png" alt="" />
              </div>

              <div class="check" v-if="!node.root && node.company_name != '其他'">
                <el-checkbox
                  v-model="node.isChecked"
                  :disabled="node.isDisabled"
                  @change="(val) => changeCheckArr(val, node)"
                ></el-checkbox>
              </div>
              <div class="info">
                <el-tooltip
                  slot="prepend"
                  class="item"
                  effect="dark"
                  :content="node.company_name"
                  placement="top"
                  :open-delay="100"
                >
                  <span class="companyName">{{ node.company_name }}</span>
                </el-tooltip>
                <span class="createTime" v-if="node.company_name != '其他'">{{
                  node.created_at
                }}</span>
              </div>
              <!-- <div class="del">
                <i class="el-icon-delete"></i>
              </div> -->
              <!-- node.id存在 代表不为其他项-->
              <div class="detail" v-if="node.company_name != '其他'" @click="getDetail(node)"
                >查看详情</div
              >
            </div>
            <div class="middleLine" v-if="node.company_name != '其他'"></div>
            <div class="companyInfo" v-if="node.company_name != '其他'">
              <div class="info">
                <span class="label">社会信用代码：</span>
                <span class="value">{{ node.credit_code || '-' }}</span>
              </div>
              <div class="info">
                <span class="label">联系人：</span>
                <span class="value">{{ node.oper_name || '-' }}</span>
              </div>
            </div>
            <div class="infoNumber">
              <div class="clue" @click="goToDetailPage('/organization-clue', node)">
                <span class="label">线索</span>
                <span class="num">
                  {{ node.clue_num ? node.clue_num : 0 }}
                </span>
              </div>
              <!-- <div class="middleLine"></div> -->
              <div class="assets" @click="goToDetailPage('/organization-assets', node)">
                <span class="label">资产</span>
                <span class="num">
                  {{ node.assets_num ? node.assets_num : 0 }}
                </span>
                <el-tooltip
                  slot="prepend"
                  class="item"
                  effect="dark"
                  content="只统计台账IP和疑似IP的数量"
                  placement="top"
                  :open-delay="100"
                >
                  <i style="margin-left: 8px" class="el-icon-question"></i>
                </el-tooltip>
              </div>
            </div>

            <!-- <div class="operation-btn">
              <div @click="onNodeHandle('add', node)">
                <img src="@/assets/images/companyClue/add.svg" alt="" />
                新增下属企业
              </div>
              <div @click="onNodeDeleteBtn(node, 'one')" v-if="!node.root">
                <img src="@/assets/images/companyClue/delete.svg" alt="" />
                删除
              </div>
            </div> -->
          </div>
        </template>
      </zm-tree-org>
    </div>
    <div v-if="!treeDataCopy.id" class="emptyClass">
      <!-- <div> -->
      <svg class="icon" aria-hidden="true">
        <use xlink:href="#icon-kong"></use>
      </svg>
      <p>暂无数据</p>
    </div>
    <!-- </div> -->
    <prompt
      title="新增下属企业"
      placeholder="请输入要添加的下属企业名称"
      :visible="newCompanyVisible"
      @save="onNodeHandleBtn"
      @close="newCompanyVisible = false"
      :loading="newCompanyLoading"
    />

    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="detailDialogVisible"
      width="500px"
    >
      <template slot="title"> 编辑 </template>
      <div class="dialog-body">
        <el-form :model="detailItem" label-width="110px">
          <el-form-item label="企业名称：">
            {{ detailItem.company_name || '-' }}
          </el-form-item>
          <el-form-item label="社会信用代码：">
            {{ detailItem.credit_code || '-' }}
          </el-form-item>
          <el-form-item label="邮箱：">
            {{ detailItem.email || '-' }}
          </el-form-item>
          <el-form-item label="联系人：">
            {{ detailItem.oper_name || '-' }}
          </el-form-item>
          <el-form-item label="联系方式：">
            {{ detailItem.contact_phone || '-' }}
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="detailDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { getParentNode, handleOnNodeDelete } from './common'
import prompt from '@/components/assets/prompt'
import { nextCompany, addCompany, addOneCompany, delOneCompany } from '@/api/apiConfig/api.js'

export default {
  components: { prompt },
  // props: {ZmTreeOrg},
  props: {
    orgLoading: {
      type: Boolean,
      default: false
    },
    checkAll: {
      type: Boolean,
      default: false
    },
    isShow: {
      type: Boolean,
      default: false
    },
    treeData: {
      type: Object,
      default: () => ({})
    },
    currentRow: {
      type: Object,
      default: () => ({})
    },
    currentOrgId: {
      type: [Number, String],
      default: null
    }
  },
  watch: {
    checkAll(val) {
      if (!this.treeData.id) return
      this.treeData.isChecked = val
      this.$emit('setNotShow', false)
      this.setChecked(this.treeDataCopy, val)
      this.treeDataCopy = JSON.parse(JSON.stringify(this.treeDataCopy))
      this.$emit('setNotShow', true)
    },
    isShow() {
      this.treeDataCopy = JSON.parse(JSON.stringify(this.treeData))
    },
    treeData: {
      handler(val) {
        this.treeDataCopy = JSON.parse(JSON.stringify(val))
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      detailDialogVisible: false,
      detailItem: {},
      newCompanyVisible: false,
      newCompanyLoading: false,
      checkNodeArr: [],
      checkParentArr: [],
      checkArr: [],
      treeDataCopy: {},
      defaultProps: [
        {
          id: 'id',
          pid: 'parent_id',
          label: 'company_name',
          expand: 'expand',
          children: 'children'
        }
      ],
      currentId: '',
      lazy: true,
      toolBar: {
        scale: false
      },
      horizontal: false, // 是否横向
      collapsable: true, // 是否可展开收起
      onlyOneNode: false, // 是否仅拖动当前节点
      cloneNodeDrag: false, // 是否拷贝节点拖拽
      expandAll: true, //
      dialogVisible: false, // 弹框显隐
      dialogType: '', // 弹框类型
      departmentName: '', // 部门名称
      nodeTree: '', // 当前点击的nodeTree
      secondNode: []
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  methods: {
    getDetail(row) {
      this.detailItem = row ? JSON.parse(JSON.stringify(row)) : {}
      this.detailDialogVisible = true
    },
    goToDetailPage(path, node) {
      this.$router.push({
        path,
        query: {
          comName: node.company_name,
          orgId: this.currentOrgId,
          comId: node.id
        }
      })
    },
    changeCheckArr(val, node) {
      if (!node.isLeaf) this.$message.warning('此企业有下层企业，勾选该企业下层企业将一起被勾选')
      let arr = this.checkArr
      if (val) {
        node.isChecked = true

        this.setChecked(node, true)
        arr.push(node.id)
      } else {
        node.isChecked = false

        this.setChecked(node, false)
        let index = arr.indexOf(node.id)
        if (index !== -1) {
          arr.splice(index, 1)
        }
      }
      this.checkArr = [...new Set(arr)]
    },
    setChecked(node, setChecked) {
      let nodeLength = node.children && node.children.length
      if (nodeLength && nodeLength != 0) {
        for (let index = 0; index < nodeLength; index++) {
          const element = node.children[index]
          element.isChecked = setChecked
          element.isDisabled = setChecked
          if (element.children) {
            this.setChecked(element, setChecked)
          }
        }
      }
    },
    async loadData(node, resolve) {
      if (node.$$level === 0) {
        return
      }
      if (node.children && node.children.length != 0) {
        let forLength = node.children.length
        for (let index = 0; index < forLength; index++) {
          const element = node.children[index]
          if (this.checkArr.indexOf(element.id) != -1) {
            element.isChecked = true
          }
        }
        return
      }
      this.onExpand(node)
    },
    async onExpand(data) {
      // isGetChild  true表示获取过子节点 若是获取过则不再调用接口
      // isLeaf false代表有子企业 true代表没有子企业
      if (data.isGetChild) return
      data.page = 1
      let res = await nextCompany({
        organization_group_id: this.currentOrgId,
        page: 1,
        per_page: 10,
        no_page: '0',
        parent_id: data.id,
        operate_company_id: this.currentCompany
      })
      let forLength = res.data.items.length
      for (let index = 0; index < forLength; index++) {
        const element = res.data.items[index]
        element.isLeaf = element.is_leaf
        if (data.isChecked) {
          element.isChecked = true
          element.isDisabled = true
        } else {
          element.isChecked = false
          element.isDisabled = false
        }
      }
      let data1 = res.data.items
      data.isGetChild = true
      data.isLeaf = false
      data.expand = true

      let lastData = []
      if (res.data.total > 10) {
        lastData = [
          {
            id: +new Date(),
            parent_id: data.id,
            level: data.level || -1,
            company_name: '更多',
            created_at: '---',
            expand: true,
            isLeaf: true,
            last: true
          }
        ]
      }
      if (Array.isArray(data['children'])) {
        data['children'].push(...data1, ...lastData)
      } else {
        this.$set(data, 'children', [].concat(data1).concat(...lastData))
      }
    },
    async getMoreCompany(node) {
      const parentNode = getParentNode(this.treeDataCopy, 'id', node.parent_id)
      parentNode.page = parentNode.page + 1
      let res = await nextCompany({
        organization_group_id: this.currentOrgId,
        page: parentNode.page,
        per_page: 10,
        no_page: '0',
        parent_id: node.parent_id,
        operate_company_id: this.currentCompany,
        is_first: '1'
      })
      let forLength = res.data.items.length
      for (let index = 0; index < forLength; index++) {
        const element = res.data.items[index]
        element.isLeaf = element.is_leaf // true 表示没有下级
      }
      let moreSon = res.data.items

      let lastData = []
      if (res.data.total > parentNode.page * 10) {
        lastData = [
          {
            id: +new Date(),
            parent_id: parentNode.id,
            level: parentNode.level || -1,
            company_name: '更多',
            created_at: '---',
            expand: true,
            isLeaf: true,
            last: true
          }
        ]
      }
      if (Array.isArray(parentNode['children'])) {
        parentNode['children'].pop()
        parentNode['children'].push(...moreSon, ...lastData)
      } else {
        parentNode['children'].pop()
        this.$set(parentNode, 'children', [].concat(moreSon).concat(...lastData))
      }
    },
    handleOnNodeDelete,
    // 隐藏左键菜单
    defineMenus() {
      return []
    },
    // 关闭组织架构
    closePopover() {
      document.body.click()
    },
    onNodeHandle(type, node) {
      this.newCompanyVisible = true
      this.nodeTree = node
    },
    // 增加/编辑部门节点
    async onNodeHandleBtn(val) {
      if (!val) {
        this.$message.error('请输入要添加的企业')
        return
      }
      let node = this.nodeTree
      let res = await addOneCompany({
        operate_company_id: this.currentCompany,
        parent_id: node.id,
        company_name: val
      }).catch(() => {
        instance.confirmButtonLoading = false
      })
      if (res.code == 0) {
        const params = [
          {
            id: res.data.id,
            parent_id: this.nodeTree.id,
            level: this.nodeTree.level || -1,
            company_name: val,
            created_at: '---',
            expand: true,
            isLeaf: true,
            is_leaf: true,
            isChecked: this.nodeTree.isChecked,
            isDisabled: this.nodeTree.isDisabled
          }
        ]
        // isGetChild 是否已获取过子节点 防止重复获取 is_leaf false代表其下有子节点
        if (!node.isGetChild && !node.is_leaf && !node.expand) {
          this.onExpand(node) // 获取子节点 并展开
        } else {
          this.nodeTree.isLeaf = false
          this.nodeTree.expand = true
          if (Array.isArray(this.nodeTree['children'])) {
            this.nodeTree['children'].push(...params)
          } else {
            this.$set(this.nodeTree, 'children', [].concat(params))
          }
        }
        // done();
        this.newCompanyVisible = false
        this.$message({
          type: 'success',
          message: '添加成功'
        })
        this.$emit('updateCompanyNumber')
      }
    },
    // 删除部门节点
    onNodeDeleteBtn(node, type) {
      //type： one 单个删除只删除单个节点 more 批量删除刷新页面
      const _this = this

      if (node.root && type == 'one') {
        // 根节点不允许删除
        this.$message.warning('根企业不允许删除!')
        return false
      } else if (type == 'more') {
        if (
          (this.treeDataCopy &&
            this.treeDataCopy.children &&
            this.treeDataCopy.children.length == 0) ||
          !this.treeDataCopy ||
          !this.treeDataCopy.children
        ) {
          this.$message.warning('暂无可删除的企业')
          return
        }
        if (!this.checkAll && ((this.checkArr && this.checkArr.length == 0) || !this.checkArr)) {
          this.$message.warning('请选择要删除的企业')
          return
        }
      }
      let ids = null
      let tips = null
      let is_all = null

      let has_other = 0 // has_other 1 代表 选中的删除项中有 "其他" 一项
      let confirmButtonText = ''
      if (type == 'one') {
        // 架构树上的删除按钮
        is_all = '0'
        ids = [node.id]
        if ((node.children && node.children.length > 0) || !node.is_leaf) {
          tips = `<div>系统检测到该企业下仍有相关企业信息，删除后会将该企业下相关企业一并删除!</div>`
          confirmButtonText = '直接删除'
        } else {
          tips = `<div>您确定要删除企业：<span style="color:#0469c0">${node.company_name}</span> 吗？</div>`
          confirmButtonText = '确定'
        }
      } else {
        // 勾选删除按钮
        if (this.checkAll) {
          is_all = '1'
          has_other = node.other_assets_num || node.other_clue_num ? 1 : 0
        } else {
          is_all = '0'
          ids = this.checkArr
          has_other = ids.includes(0) ? 1 : 0
        }
        tips =
          '您确定要删除所勾选的企业吗，删除组织后，本模块以及资产台账中的数据将会同步删除，是否确认'
        confirmButtonText = '确认'
      }
      _this
        .$alert(tips, '提示', {
          dangerouslyUseHTMLString: true,
          customClass: 'deleteDailog',
          showCancelButton: true,
          showConfirmButton: true,
          confirmButtonText,
          cancelButtonText: '取消'
        })
        .then(async () => {
          try {
            let res = await delOneCompany({
              is_all,
              organization_group_id: this.currentOrgId,
              company_id: ids,
              operate_company_id: this.currentCompany,
              has_other
            })
            if (res.code == 0) {
              if (type == 'more') {
                this.$emit('setNotShow', false)
                this.$emit('getNextCompany', node, true)
                this.checkArr = []
              } else {
                const parentNode = getParentNode(this.treeDataCopy, 'id', node.parent_id)
                handleOnNodeDelete(this, node, parentNode)
              }
              this.$emit('updateCompanyNumber')
            }
            this.$message.success('删除成功')
          } catch (error) {
            _this.$message({
              type: 'error',
              message: '操作失败，请重试！'
            })
          }
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="less" scoped>
.treeContainer {
  height: 100%;
  #mainContent {
    height: 100%;
  }
}
/deep/.zm-tree-org {
  box-sizing: border-box;
  background-color: #fff;
  .zm-tree-handle {
    .zm-tree-handle-item:first-child {
      display: none;
    }
  }
}
/* 每个节点样式 */
.card-main {
  position: relative;
  width: 300px;
  padding-top: 16px;
  // padding: 16px 16px 0 16px;
  font-size: 12px;
  border-radius: 4px;
  overflow: hidden;
  border-top: 4px solid #d1d5dd;
  .more {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 999;
  }

  &.active {
    border-top: 4px solid #2677ff;
  }
  .top-position {
    box-sizing: border-box;
    padding: 0 16px 12px;
    display: flex;
    width: 100%;
    // height: 44px;
    color: #62666c;
    .check {
      margin-top: 3px;
      margin-right: 8px;
    }

    .logo {
      width: 44px;
      height: 100%;
      margin-right: 8px;
      img {
        width: 100%;
      }
    }
    .info {
      flex: 1;
      width: calc(100% - 88px);
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      text-align: start;
      line-height: 22px;
      .companyName {
        width: 100%;
        margin-bottom: 4px;
        overflow: hidden;
        color: #37393c;
        font-size: 16px;
        font-weight: 700;
        white-space: nowrap;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
      }
    }
    .detail {
      font-size: 14px;
      color: #2677ff;
    }
  }
  .middleLine {
    width: calc(100% - 32px);
    height: 1px;
    margin: 0 16px;
    background-color: #e9ebef;
  }
  .top-position-root {
    justify-content: center;
  }
  .infoNumber {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    color: #62666c;
    background: #f5f8fc;

    .label {
      margin-top: 6px;
      font-size: 14px;
    }
    .num {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #2677ff;
    }
  }

  .operation-btn {
    display: flex;
    // justify-content: space-between;
    background: #f9f9f9;
    height: 40px;
    padding: 5px 10px 0;
    align-items: center;
    position: relative;
    color: #62666c;
    div {
      // width: 20px;
      height: 20px;
      margin-right: 16px;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
/* 节点操作按钮 */
.iconfont {
  width: 20px;
  height: 20px;
  font-size: 20px;
  color: #878787;
}
/* popover样式 */
.operation-btn ::v-deep.customCont {
  background: #e2f4e9;
  margin-top: 15px;
  .popper__arrow::after {
    border-bottom-color: #e2f4e9;
  }
}
.customCont-main {
  position: relative;

  &-close {
    position: absolute;
    z-index: 10;
    top: -5px;
    right: 0;
    .el-icon-close:before {
      font-size: 16px;
      font-weight: 600;
    }
  }
}
.organization {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding: 0 18px 18px;
  min-width: 30vw;
}
.organization-list {
  font-size: 12px;
  min-height: 80px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  margin-top: 18px;
  width: 45%;

  &-top {
    background: rgba(42, 178, 98, 0.88);
    color: #ffffff;
    padding: 10px;
    text-align: center;
  }
  &-content {
    padding: 20px;
    color: #000000;
    background: #ffffff;
  }
}
.organization-empty {
  min-width: 20vw;
  min-height: 50px;
  padding: 0;

  &-info {
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: #000000;
  }
}
/deep/.tree-org-node__expand {
  background: linear-gradient(270deg, #2677ff 0%, #26b3ff 100%);
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
  border: 0;
  top: auto;
  bottom: -10px;
  margin-top: 0;
}
/deep/.collapsable .tree-org-node.collapsed .tree-org-node__content:after {
  display: none;
}
</style>
<style lang="less">
/* 新增部门弹框 */
.custom-dialog {
  width: 18%;
  .el-dialog__header {
    border-bottom: 1px solid #e8e8e8;
    .el-dialog__title {
      font-size: 14px;
    }
    .el-dialog__headerbtn .el-dialog__close {
      font-weight: 600;
    }
  }
  .el-dialog__body {
    .el-input.is-active .el-input__inner,
    .el-input__inner:focus {
      border-color: #042d7c;
      outline: 0;
    }
  }

  .dialog-footer {
    text-align: center;
    .el-button {
      background: #042d7c;
      color: #ffffff;
    }
  }
  .error-tips {
    font-size: 12px;
    color: #f56c6c;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
  }
}
.tree-org-node__inner:hover {
  box-shadow: 2px 2px 5px rgba(4, 45, 124, 0.55);
}
/* 删除部门弹框 */
.deleteDailog {
  min-width: 30vw;
  .el-message-box__header {
    border-bottom: 1px solid #e8e8e8;
  }
  .el-message-box__message {
    text-align: center;
  }
}
.emptyClass {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  text-align: center;
  vertical-align: middle;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
.zoom-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  // justify-content: center;
}
.companyInfo {
  padding: 12px 16px;
  font-size: 14px;
  text-align: left;
  .info {
    .label {
      color: #62666c;
    }
    .value {
      color: #37393c;
    }
  }
  .info:first-child {
    margin-bottom: 8px;
  }
}
</style>
