<template>
  <div class="container" v-loading="wholeLoading">
    <div class="headerTitle">
      <span class="goback" v-if="$route.query.from" @click="goBack"
        ><i class="el-icon-arrow-left"></i>返回</span
      ><span class="spline" v-if="$route.query.from">/</span>
      企业资产管控
    </div>
    <div class="left">
      <div class="fixed">
        <div class="header">
          <div class="title">企业管理</div>
          <!-- <div class="addBtn" @click="newCompanyVisible = true">
            <img src="@/assets/images/companyClue/addCompany.png" alt="" />
            新增企业
          </div> -->
        </div>
        <div class="filterTab">
          <el-input
            v-model="searchName"
            placeholder="请输入关键字检索"
            id="keyword_keycheck"
            @keyup.enter.native="search"
          >
            <el-button slot="append" icon="el-icon-search"></el-button>
          </el-input>
        </div>
      </div>
      <div class="collapse" v-if="listData.length != 0">
        <el-collapse accordion v-model="currentOrgId">
          <el-collapse-item
            :class="{ other: item.name == '其他' }"
            v-for="item in listData"
            :key="item.id"
            :name="item.id"
            @click.native="collapseChange($event, item)"
          >
            <template slot="title">
              <span class="title">{{ item.name }}</span>
              <div v-if="item.name !== '其他'" class="delIcon" @click.stop="delArray(item.id)">
                <i class="el-icon-delete"></i></div
            ></template>

            <div v-if="item.level_arr && item.level_arr.length != 0 && item.name !== '其他'">
              <div class="info" v-for="(level, index, key) in item.level_arr" :key="key">
                <div class="label">{{ toChineseNumber(index) }}级企业：</div>
                <div class="num">{{ level }}</div>
              </div>
            </div>
            <div v-else-if="item.name !== '其他'">暂无子级企业</div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="collapse" v-else>
        <div class="emptyClass">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-kong"></use>
          </svg>
          <p>暂无数据</p>
        </div>
      </div>
    </div>
    <template v-if="currentRow.name == '其他'">
      <assetsTable pageType="other" :orgId="currentOrgId" :listData="listData" />
    </template>
    <template v-else>
      <div class="right">
        <div class="right-header">
          <el-checkbox class="checkboxAll" id="keyword_all" v-model="checkAll"
            >选择全部</el-checkbox
          >
          <el-button class="normalBtnRe" type="primary" @click="deleteCompany">删除</el-button>
          <el-button class="normalBtn" @click="goToOther" type="primary">企业架构管理</el-button>
        </div>
        <div class="content">
          <Org
            :orgLoading="orgLoading"
            :checkAll="checkAll"
            @setNotShow="setNotShow"
            @getNextCompany="getNextCompany"
            @updateCompanyNumber="updateCompanyNumber"
            :currentRow="currentRow"
            :isShow="isShow"
            :treeData="treeData"
            :currentOrgId="currentRow.id"
            ref="org"
          />
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import assetsTable from './assetsTable.vue'
import Org from './org'
import Operation from './operation'
import ImportCompany from './importCompany'
import utils from '@/utils/baseUse'
import prompt from '@/components/assets/prompt'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { addCompany, organizationGroupList, nextCompany, orgGroupDel } from '@/api/apiConfig/api.js'

export default {
  name: '',
  components: { Org, Operation, ImportCompany, prompt, assetsTable },
  data() {
    return {
      needRefresh: true,
      newCompanyLoading: false,
      newCompanyVisible: false,
      newCompanyExtractLoading: false,
      checkAll: false,
      searchName: '',
      isShow: false,
      listData: [],
      treeData: {},
      currentOrgId: null,
      currentRow: {},
      orgLoading: false,
      wholeLoading: false,
      preOrgId: ''
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  watch: {
    getterCurrentCompany(val) {
      this.treeData = {}
      this.currentRow = {}
      // this.currentOrgId = "";
      this.checkAll = false
      // 页面初始化
      this.getData(true)
    },
    currentRow: {
      handler(val) {
        this.currentOrgId = val.id
      },
      deep: true
    }
  },
  async mounted() {
    this.preOrgId = this.$route.params.id
    // 防止刷新时 操作页面回到架构页面
    let isGetCompany = sessionStorage.getItem('isGetCompany')
    if (Boolean(isGetCompany)) {
      this.getData(true)
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    async delArray(id) {
      this.$confirm(
        `确定删除该组织吗，删除组织后，本模块以及资产台账中的数据将会同步删除，是否确认`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(async () => {
          let res = await orgGroupDel({
            id,
            operate_company_id: this.currentCompany
          })
          if (res.code == 0) {
            this.$message.success('删除成功')
            // this.currentOrgId = null;
            this.getData(true)
          }
        })
        .catch(() => {})
    },
    goBack() {
      sessionStorage.setItem('menuId', '2-1')
      this.changeMenuId('2-1')
      this.$router.push({ path: '/groupAssets' })
    },

    toChineseNumber(val) {
      return utils.toChineseNumber(val)
    },
    goToOther() {
      localStorage.setItem('orgId', this.currentOrgId)
      this.changeMenuId('7-18')
      this.$router.push({ path: '/orgOperation', query: { isFormOrg: true } })
    },
    setNotShow(isShow) {
      this.isShow = isShow
    },
    deleteCompany() {
      // 刷新侧边企业列表

      // this.isShow = false;
      if (!this.treeData.id) return this.$message.error('暂无可删除的企业')
      this.$refs.org.onNodeDeleteBtn(this.currentRow, 'more')
      // this.getNextCompany(this.currentRow)
    },

    // 组织架构树
    async collapseChange(e, val) {
      this.treeData = {}
      this.orgLoading = true
      this.isShow = false
      this.checkAll = false // 是否全选
      this.currentRow = val // 当前展开企业的信息
      this.currentOrgId = val.id // 当前展开企业的ID 必须加 否则不能高亮侧边栏
      this.getNextCompany(val)
    },
    async getNextCompany(val, isDelUpdate = false) {
      if (!val || !val.company) {
        // 没有下级企业时处理
        this.treeData = {}
        this.isShow = true
        this.orgLoading = false
        this.wholeLoading = false
        return
      }
      val.company.root = true // 设置根企业
      val.company.isLeaf = val.is_leaf // 表示是否有子节点false 为有

      // isDelUpdate 是否是删除操作 删除后 不通过刷新列表
      if (isDelUpdate || (val.company && val.id && !val.is_leaf)) {
        // 获取下一层的企业分页数据
        let res = await nextCompany({
          organization_group_id: this.currentRow.id,
          no_page: '',
          parent_id: val.company.id,
          operate_company_id: this.currentCompany,
          is_first: val.is_first
        })
        this.isShow = true
        val.company.children = res.data.items || {}
        val.company.page = 1
        let forLength = res.data.items.length
        for (let index = 0; index < forLength; index++) {
          const element = res.data.items[index]
          element.isLeaf = element.is_leaf // true 表示没有下级
        }
        let lastData = []
        if (res.data.total > 10) {
          lastData = [
            {
              id: +new Date(),
              parent_id: val.company.id,
              level: val.company.level || -1,
              company_name: '更多',
              created_at: '---',
              expand: true,
              isLeaf: true,
              last: true
            }
          ]
        }
        if (Array.isArray(val.company['children'])) {
          val.company['children'].push(...lastData)
        } else {
          this.$set(val.company, 'children', [].concat(...lastData))
        }
        this.treeData = val.company
        this.orgLoading = false
        this.wholeLoading = false
      } else {
        this.isShow = true
        this.orgLoading = false
        this.wholeLoading = false
        this.treeData = val.company
      }
    },

    async updateCompanyNumber() {
      let res = await organizationGroupList({
        is_first: '1',
        name: this.searchName,
        operate_company_id: this.currentCompany
      }).catch(() => {})
      this.listData = res.data
    },
    search() {
      this.wholeLoading = true
      this.getData()
    },
    async getData(isRefreshAll = false) {
      // isRefreshAll 为true时代表重置整个侧边组织 清空选中缓存
      this.orgLoading = true
      this.isShow = false // 获取数据前将组织架构隐藏
      let res = await organizationGroupList({
        name: this.searchName,
        operate_company_id: this.currentCompany,
        is_first: '1'
      }).catch(() => {})
      if (res.code == 0 && res.data.length != 0) {
        let currentRow = {}
        if (!this.preOrgId) {
          res.data.forEach((item) => {
            item.is_first = 1
          })
          currentRow = res.data[0] || {}
        } else {
          res.data.forEach((item) => {
            item.is_first = 1
            if (this.preOrgId == item.id) {
              currentRow = item
            }
          })
        }
        this.listData = res.data
        this.currentRow = currentRow
        this.getNextCompany(this.currentRow)
      } else {
        this.listData = []
        // this.currentOrgId = null;
        this.currentRow = {}
        this.treeData = {}
        this.orgLoading = false
        this.wholeLoading = false
        this.isShow = true
      }
    },
    async add(val) {
      if (!val) {
        this.$message.error('请输入要添加的企业')
        return
      }
      this.newCompanyLoading = true
      let res = await addCompany({
        operate_company_id: this.currentCompany,
        name: val
      }).catch(() => {
        this.newCompanyLoading = false
      })
      if (res.code == 0) {
        this.getData(true)
        this.newCompanyLoading = false
        this.newCompanyVisible = false
        this.$message({
          type: 'success',
          message: '添加成功'
        })
      }
    }
  },
  beforeRouteLeave(to, from, next) {
    // 判断 从非资产架构页面 进入的  清空本地缓存orgId
    if (to.name != 'organization-assets' && to.name != 'organization-clue') {
      // 从organization-assets / organization-clue  跳转 需要缓存页面
      if (this.$vnode && this.$vnode.data.keepAlive) {
        if (
          this.$vnode.parent &&
          this.$vnode.parent.componentInstance &&
          this.$vnode.parent.componentInstance.cache
        ) {
          if (this.$vnode.componentOptions) {
            var key =
              this.$vnode.key == null
                ? this.$vnode.componentOptions.Ctor.cid +
                  (this.$vnode.componentOptions.tag ? `::${this.$vnode.componentOptions.tag}` : '')
                : this.$vnode.key
            var cache = this.$vnode.parent.componentInstance.cache
            var keys = this.$vnode.parent.componentInstance.keys
            if (cache[key]) {
              if (keys.length) {
                var index = keys.indexOf(key)
                if (index > -1) {
                  keys.splice(index, 1)
                }
              }

              delete cache[key]
            }
          }
        }
        this.$destroy()
      }
    }
    next()
  },
  beforeRouteEnter: (to, from, next) => {
    next()
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  display: flex;
  height: 100%;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  .left {
    position: relative;
    width: 336px;
    height: 100%;
    padding: 102px 20px 16px;
    background-color: #fff;
    box-sizing: border-box;
    border-right: 1px solid #e9ebef;
    .fixed {
      position: absolute;
      top: 16px;
      width: 100%;
      box-sizing: border-box;
      margin-left: -20px;
      padding: 0 20px;
      .header {
        display: flex;
        justify-content: space-between;
        height: 22px;
        margin-bottom: 14px;
        .addBtn {
          cursor: pointer;
          font-size: 14px;
          font-weight: normal;
          line-height: 20px;
          letter-spacing: 0px;
          color: #2677ff;
        }
      }
      .filterTab {
        margin-bottom: 20px;
      }
    }
    .collapse {
      width: 100%;
      overflow: auto;
      height: 100% !important;
      .title {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .right {
    width: calc(100% - 336px);
    display: flex;
    flex-direction: column;
    .right-header {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 64px !important;
      width: 100%;
      padding: 0 20px;
      background-color: #fff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    }
    .content {
      box-sizing: border-box;
      flex: 1;
      height: calc(100% - 64px);
      background: #fff;
      border: 1px solid #eee;
    }
  }
}
/deep/.el-collapse {
  border: 0;

  .el-collapse-item {
    // height: 56px;
    border: 1px solid #e9ebef;
    padding: 0 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    &:hover {
      .el-collapse-item__header {
        .delIcon {
          display: block;
          color: red;
        }
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
    // box-sizing: border-box;
    .el-collapse-item__header {
      display: flex;
      justify-content: space-between;
      border-bottom: none;
      transition: none;
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;
      .delIcon {
        display: none;
      }
      &.is-active {
        border-bottom: 2px solid #e9ebef;
      }
      .el-collapse-item__arrow {
        display: none;
      }
    }
    .el-collapse-item__wrap {
      border-bottom: none;
    }
    .el-collapse-item {
      div:first-child {
        margin-bottom: 39px;
      }
    }
    .el-collapse-item__content {
      padding-top: 16px;
      .info {
        display: flex;
        line-height: 20px;
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: normal;
        color: #62666c;
        .label {
          margin-right: 5px;
        }
        .num {
          color: #37393c;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    &.is-active {
      border: 2px solid #2677ff;
      background: linear-gradient(90deg, #ffffff 0%, #eff5ff 99%);
      .el-collapse-item__wrap {
        background-color: transparent;
      }
      .el-collapse-item__header {
        color: #2677ff;
        background: linear-gradient(90deg, #ffffff 0%, #eff5ff 99%);
      }
    }
  }
}

/deep/.el-dialog__body {
  min-height: 128px !important;
}

/deep/.el-tree-node {
  .el-tree-node__content {
    height: 32px;
  }
}

.emptyClass {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
  vertical-align: middle;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
.el-input__icon {
  &.el-icon-arrow-up.is-reverse {
    transform: rotate(180deg);
  }
}
.other {
  /deep/.el-collapse-item__header.is-active {
    border-bottom: none !important;
  }
  /deep/.el-collapse-item__content {
    padding-bottom: 0;
    padding-top: 0 !important;
  }
}
</style>
