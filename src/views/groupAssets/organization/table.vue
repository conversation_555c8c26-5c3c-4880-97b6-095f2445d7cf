<template>
  <div style="height: 100%">
    <div class="myTableHeader">
      <div class="chooseBox">
        <el-checkbox
          :disabled="progressBar"
          v-model="checkedAlls"
          @change="checkAllChange"
        ></el-checkbox>
      </div>
      <div v-for="(item, itemIndex) in tableHeader" :key="itemIndex" class="tableHeaderBox">
        <div v-if="item.label == '企业名称'" class="qiyeBox">{{ item.label }}</div>
        <div v-else>{{ item.label }}</div>
      </div>
      <!-- <div v-if="handleIsShow" class="operationBox">操作</div> -->
      <div class="shuBox"></div>
    </div>
    <div class="zhanwu" v-if="tableData.length == 0">暂无数据</div>
    <ul v-if="tableData.length > 0" class="clueContent" @scroll="myscroll">
      <li
        v-for="(clue, clueindex) in tableData"
        :key="clue.label"
        :class="getClass(clue.type, clue['is_highlight'])"
      >
        <div class="borderWrap">
          <el-checkbox
            v-model="clue.checked"
            @change="changeOne"
            :disabled="progressBar"
          ></el-checkbox>
          <div
            :class="tableHeader.length == 2 ? 'contentClass' : 'contentClass contentClass_28'"
            v-for="(item, index) in tableHeader"
            :key="index"
          >
            <span v-if="item.name == 'chain_list'">
              <el-tooltip
                v-if="getSourcetype(clue[item.name])"
                class="item"
                effect="light"
                placement="top"
                :open-delay="500"
              >
                <div slot="content"
                  >{{ $punyCode.toUnicode(getSource(clue[item.name], clue.parent_id, clue.source))
                  }}{{ clue.punycode_domain ? '(' + clue.punycode_domain + ')' : '' }}</div
                >
                <span>{{
                  $punyCode.toUnicode(getSource(clue[item.name], clue.parent_id, clue.source))
                }}</span>
              </el-tooltip>
              <span v-else
                ><img :src="getImg(clue[item.name])" alt="" style="width: 23px; height: 23px"
              /></span>
            </span>
            <!-- icon以及icon疑似线索展示 -->
            <span v-else-if="item.name == 'content' && clue[item.name] && clue['type'] == 3">
              <span>
                <!-- 疑似线索 -->
                <el-tooltip class="item" effect="light" placement="top" :open-delay="500">
                  <span slot="content">
                    <span v-if="clue['is_highlight']">疑似线索</span>：
                    <img
                      :src="
                        clue[item.name].includes('http')
                          ? clue[item.name]
                          : showSrcIp + clue[item.name]
                      "
                      alt=""
                      style="width: 23px; height: 23px"
                    />
                    <span>{{ clue.hash }}</span>
                  </span>
                  <span>
                    <img
                      :src="
                        clue[item.name].includes('http')
                          ? clue[item.name]
                          : showSrcIp + clue[item.name]
                      "
                      alt=""
                      style="width: 23px; height: 23px"
                    />
                    <span>{{ clue.hash }}</span>
                  </span>
                </el-tooltip>
              </span>
              <!-- ICON和证书需要展示对应线索在fofa上的资产数量 -->
              <el-tooltip class="item" effect="light" placement="top" :open-delay="500">
                <span slot="content">
                  <span>此线索在FOFA资产数量</span>：{{
                    clue.fofa_assets_num && clue.fofa_assets_num.num
                  }}
                  <span v-loading="clue.fofaLoading" v-if="clue.fofaLoading" style="color: #409eff"
                    >去FOFA查看</span
                  >
                  <a
                    v-if="
                      String(clue.fofa_assets_num && clue.fofa_assets_num.fofa_url).includes(
                        'http'
                      ) && !clue.fofaLoading
                    "
                    style="color: #409eff"
                    :href="String(clue.fofa_assets_num && clue.fofa_assets_num.fofa_url)"
                    target="_blank"
                    >去FOFA查看</a
                  >
                </span>
                <!-- 企业账号，公共线索库暂不支持 -->
                <img
                  v-if="$route.path == '/unitIndex'"
                  src="@/assets/images/fofasmall.svg"
                  class="fofaImg"
                  @mouseenter="getFofaNumV1(clue, clueindex)"
                  alt=""
                />
              </el-tooltip>
              <!-- <span class="originLine" v-if="clue['is_fake_icp'] == 1">ICP盗用</span> -->
            </span>
            <!-- 域名 -->
            <span v-else-if="item.name == 'content' && clue[item.name] && clue['type'] == 0">
              <span v-if="clue['is_highlight']">
                <!-- 疑似线索 -->
                <el-tooltip class="item" effect="light" placement="top" :open-delay="500">
                  <span slot="content"
                    ><span>疑似线索</span>：{{ $punyCode.toUnicode(String(clue[item.name]))
                    }}{{ clue.punycode_domain ? '(' + clue.punycode_domain + ')' : '' }}</span
                  >
                  <span class="maxWidthContent">
                    {{ $punyCode.toUnicode(clue[item.name] ? clue[item.name] : '-') }}
                  </span>
                </el-tooltip>
              </span>
              <span v-else>
                <!-- 中文域名给提示 -->
                <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                  <div slot="content"
                    >{{ $punyCode.toUnicode(String(clue[item.name]))
                    }}{{ clue.punycode_domain ? '(' + clue.punycode_domain + ')' : '' }}</div
                  >
                  <span class="maxWidthContent">{{
                    $punyCode.toUnicode(clue[item.name] ? clue[item.name] : '-')
                  }}</span>
                </el-tooltip>
              </span>
              <!-- 域名展示whois信息 -->
              <el-tooltip
                class="item"
                popper-class="chainClass"
                effect="light"
                placement="top"
                :open-delay="500"
              >
                <span v-loading="whoisLoading" slot="content">
                  <span>whois信息</span>：
                  <p v-for="(v, index) in whiosHeader" :key="index">{{
                    v.label + (whoisInfo[v.name] || '-')
                  }}</p>
                </span>
                <img
                  src="@/assets/images/whois.svg"
                  class="whoisImg"
                  @mouseenter="getWhois(clue, clueindex)"
                  alt=""
                />
              </el-tooltip>
              <!-- <span class="originLine" v-if="clue['is_fake_icp'] == 1">ICP盗用</span> -->
            </span>
            <!-- icon/域名 之外的线索类型 -->
            <span
              v-else-if="
                item.name == 'content' && clue[item.name] && clue['type'] != 3 && clue['type'] != 0
              "
            >
              <el-tooltip
                class="item"
                effect="light"
                placement="top"
                v-if="clue['is_highlight']"
                :open-delay="500"
              >
                <span slot="content"
                  ><span>疑似线索</span>：{{ clue[item.name] ? clue[item.name] : '-' }}</span
                >
                <span class="maxWidthContent">
                  {{ clue[item.name] ? clue[item.name] : '-' }}
                </span>
              </el-tooltip>
              <el-tooltip class="item" effect="light" placement="top" v-else :open-delay="500">
                <span slot="content">{{ clue[item.name] ? clue[item.name] : '-' }}</span>
                <span class="maxWidthContent">
                  {{ clue[item.name] ? clue[item.name] : '-' }}
                </span>
              </el-tooltip>
              <!-- 线索库ICON和证书需要展示对应线索在fofa上的资产数量 -->
              <el-tooltip class="item" effect="light" placement="top" :open-delay="500">
                <span slot="content">
                  <span>此线索在FOFA资产数量</span>：{{
                    clue.fofa_assets_num && clue.fofa_assets_num.num
                  }}
                  <div v-loading="clue.fofaLoading" v-if="clue.fofaLoading">去FOFA查看</div>
                  <a
                    v-if="
                      String(clue.fofa_assets_num && clue.fofa_assets_num.fofa_url).includes(
                        'http'
                      ) && !clue.fofaLoading
                    "
                    style="color: #409eff"
                    :href="String(clue.fofa_assets_num && clue.fofa_assets_num.fofa_url)"
                    target="_blank"
                    >去FOFA查看</a
                  >
                </span>
                <!-- 公共线索库暂不支持；企业账号不支持 -->
                <img
                  v-if="$route.path == '/unitIndex' && clue['type'] == 1"
                  src="@/assets/images/fofasmall.svg"
                  class="fofaImg"
                  @mouseenter="getFofaNumV1(clue, clueindex)"
                  alt=""
                />
              </el-tooltip>
              <!-- <span class="originLine" v-if="clue['is_fake_icp'] == 1">ICP盗用</span> -->
            </span>
            <!-- 企业名称以及控股比例展示，icon不展示控股比例 -->
            <span v-else-if="item.name == 'clue_company_name'">
              <span>
                <!-- <el-tooltip class="item" effect="light" placement="top" :open-delay="500" v-if="clue[item.name]">
                          <div slot="content">
                              <span style="padding:2px 0px;display: inline-block;">{{clue[item.name] ? clue[item.name] : '-'}}</span>
                              <span v-if="clue['equity_percent'] && clue['equity_percent'] != '-'" class="kgBox">
                                  <el-tag>
                                      <span style="color: #37393C">
                                          控股比例：{{clue['equity_percent']}}%
                                      </span>
                                  </el-tag>
                              </span>
                          </div>
                          <span>
                            <span style="padding:2px 0px;display: inline-block;">{{clue[item.name] ? clue[item.name] : '-'}}</span>
                            <span v-if="clue['equity_percent'] && clue['equity_percent'] != '-'" class="kgBox">
                              <el-tag>
                                <span style="color: #37393C">
                                    控股比例：{{clue['equity_percent']}}%
                                </span>
                              </el-tag>
                            </span>
                          </span>
                      </el-tooltip> -->
                <el-popover placement="bottom" width="200" trigger="hover" v-if="clue[item.name]">
                  <div class="popoverBtn">
                    <div
                      ><el-button type="text" @click="selectedByCompanyN(clue[item.name], true)"
                        >选中所有该公司</el-button
                      ></div
                    >
                    <div
                      ><el-button type="text" @click="selectedByCompanyN(clue[item.name], false)"
                        >取消选中所有该公司</el-button
                      ></div
                    >
                  </div>
                  <span slot="reference">
                    <span style="padding: 2px 0px; display: inline-block">{{
                      clue[item.name] ? clue[item.name] : '-'
                    }}</span>
                    <span
                      v-if="clue['equity_percent'] && clue['equity_percent'] != '-'"
                      class="kgBox"
                    >
                      <el-tag>
                        <span style="color: #37393c">
                          控股比例：{{ clue['equity_percent'] }}%
                        </span>
                      </el-tag>
                    </span>
                  </span>
                </el-popover>
                <span v-else style="padding: 2px 0px; display: inline-block">-</span>
              </span>
              <!-- icon的企业名称 疑似线索企业名称显示为‘-’ -->
              <!-- <span v-else>
                    {{ clue['is_highlight'] || !clue[item.name] ? '-' : clue[item.name] }}
                  </span> -->
            </span>
            <span v-else>{{ clue[item.name] ? clue[item.name] : '-' }}</span>
          </div>
          <!-- handleIsShow:公共线索库不需要操作 -->
          <!-- <el-button v-if="handleIsShow&&user.role == 2" type="text" size="small" @click="editCompanyName(clue.id, clue.type)">编辑</el-button> -->
          <!-- <el-button v-if="handleIsShow" type="text" size="small" @click="ignore(clue.id, clue.type)">忽略</el-button> -->
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import tableTooltip from '@/components/tableTooltip/tableTooltip.vue'
import { getFofanum, getWhoisInfo } from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip },
  props: ['tableData', 'tableHeader', 'clearCheck', 'handleIsShow', 'progressBar'],
  data() {
    return {
      value: '',
      checkedAlls: false,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      checkedArr: [],
      whoisLoading: false,
      fofaLoading: false,
      whoisInfo: {
        registration_date: '-',
        expiration_date: '-',
        sponsoring_registrar: '-',
        registrant_name: '-',
        registrant_mobile: '-',
        registrant_org: '-'
      },
      fofa_assets_num: 0,
      fofa_assets_url: '',
      whiosHeader: [
        {
          label: '域名创建时间：',
          name: 'registration_date'
        },
        {
          label: '域名更新时间：',
          name: 'expiration_date'
        },
        {
          label: '域名注册商：',
          name: 'sponsoring_registrar'
        },
        {
          label: '联系人：',
          name: 'registrant_name'
        },
        {
          label: '注册人手机：',
          name: 'registrant_mobile'
        },
        {
          label: '注册人组织：',
          name: 'registrant_org'
        }
      ],
      user: {
        role: ''
      }
    }
  },
  watch: {
    tableData() {
      this.checkedAlls = false
      this.tableData.forEach((item) => {
        this.$set(item, 'checked', false)
        this.$set(item, 'fofaLoading', false)
      })
      this.checkedArr = []
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  methods: {
    editCompanyName(id, type) {
      this.$emit('editCompanyName', 'one', id, type)
    },
    getWhois(clue, clueindex) {
      if (clue.whoisInfo) {
        // 存在直接展示
        this.whoisInfo = clue.whoisInfo
      } else {
        // 不存在需要调用接口
        this.whoisLoading = true
        getWhoisInfo({
          id: clue.id,
          operate_company_id: this.currentCompany,
          from: 2 //组织架构线索获取
        })
          .then((res) => {
            this.whoisLoading = false
            if (res.data) {
              this.whoisInfo = res.data
              // 查到后给列表fofa_assets_num赋值，避免重复查询
              this.tableData[clueindex].whoisInfo = res.data
            }
          })
          .catch(() => {
            this.whoisLoading = false
          })
      }
    },
    getFofaNum(clue, clueindex) {
      if (clue.fofa_assets_num) {
        // 存在直接展示
        this.fofa_assets_num = clue.fofa_assets_num.num
        this.fofa_assets_url = clue.fofa_assets_num.fofa_url
      } else {
        // 不存在需要调用接口
        this.fofa_assets_num = 0
        this.fofa_assets_url = ''
        this.fofaLoading = true
        this.$set(this.tableData[clueindex], 'fofaLoading', true)
        getFofanum({
          id: clue.id,
          operate_company_id: this.currentCompany
        })
          .then((res) => {
            this.$set(this.tableData[clueindex], 'fofaLoading', false)
            this.fofaLoading = false
            if (res.data) {
              this.fofa_assets_num = res.data.num
              this.fofa_assets_url = res.data.fofa_url
              // 查到后给列表fofa_assets_num赋值，避免重复查询
              this.tableData[clueindex].fofa_assets_num = {
                num: res.data.num,
                fofa_url: res.data.fofa_url
              }
            }
          })
          .catch(() => {
            this.fofaLoading = false
          })
      }
    },
    getFofaNumV1(clue, clueindex) {
      if (clue.fofa_assets_num) return
      this.$set(this.tableData[clueindex], 'fofaLoading', true)
      getFofanum({
        id: clue.id,
        operate_company_id: this.currentCompany
      }).then((res) => {
        this.$set(this.tableData[clueindex], 'fofaLoading', false)
        if (res.data) {
          this.$set(this.tableData[clueindex], 'fofa_assets_num', {
            num: res.data.num,
            fofa_url: res.data.fofa_url
          })
        }
      })
    },
    getClass(type, highIsTrue) {
      // type: 线索类型，highIsTrue：是否是疑似线索
      let className = ''
      if (type == 0) {
        className = highIsTrue ? 'eltable_domain warning-row' : 'eltable_domain'
      }
      if (type == 1) {
        className = highIsTrue ? 'eltable_cert warning-row' : 'eltable_cert'
      }
      if (type == 2) {
        className = highIsTrue ? 'eltable_icp warning-row' : 'eltable_icp'
      }
      if (type == 3) {
        // ICON疑似不高亮
        className = highIsTrue ? 'eltable_icon warning-row' : 'eltable_icon'
      }
      if (type == 4) {
        // 关键词
        className = highIsTrue ? 'eltable_keyword warning-row' : 'eltable_keyword'
      }
      if (type == 6) {
        className = highIsTrue ? 'eltable_ip warning-row' : 'eltable_ip'
      }
      if (type == 5) {
        className = highIsTrue ? 'eltable_subdomain warning-row' : 'eltable_subdomain'
      }
      return className
    },
    myscroll() {
      // 滚动监测
      if (document.getElementsByClassName('borderWrap')[0]) {
        let dataList = document.getElementsByClassName('borderWrap')[0].clientHeight
        // 滚动距离
        let scrollTop = document.getElementsByClassName('clueContent')[0].scrollTop
        sessionStorage.setItem('scrollTop', scrollTop)
        // 当前滚动条的数据
        let num = parseInt(scrollTop / dataList)
        let type = this.tableData[num].type //当前类型
        this.$emit('scrollChangeTab', type)
      }
    },
    // 取消全选
    checkAllChange(emitChecked) {
      if (this.checkedAlls) {
        this.tableData.forEach((item) => {
          this.$set(item, 'checked', true)
        })
      } else {
        this.tableData.forEach((item) => {
          this.$set(item, 'checked', false)
        })
      }
      this.checkedArr = this.tableData.filter((item) => {
        return item.checked
      })
      this.$emit('checkedArr', this.checkedArr)
    },
    changeOne() {
      let arr = this.tableData.filter((item) => {
        return item.checked
      })
      if (arr.length < this.tableData.length) {
        this.checkedAlls = false
      } else {
        this.checkedAlls = true
      }
      this.checkedArr = this.tableData.filter((item) => {
        return item.checked
      })
      this.$emit('checkedArr', this.checkedArr)
    },
    getSourcetype(val) {
      //线索来源类型
      if (val.length != 0) {
        if (val.length >= 2) {
          if (val[val.length - 2].type != 3) {
            return true
          } else {
            return false
          }
        } else {
          if (val[0].type != 3) {
            return true
          } else {
            return false
          }
        }
      } else {
        return true
      }
    },
    getImg(val) {
      //线索图片
      if (val.length != 0) {
        if (val.length >= 2) {
          return val[val.length - 2].content.includes('http')
            ? val[val.length - 2].content
            : this.showSrcIp + val[val.length - 2].content
        } else {
          return val[0].content.includes('http') ? val[0].content : this.showSrcIp + val[0].content
        }
      }
    },
    getSource(val, parent_id, source) {
      //线索来源
      if (val.length != 0) {
        if (val.length >= 2) {
          return val[val.length - 2].content
        } else {
          return val[0].content
        }
      } else {
        // 初始线索：0、3、4、5，扩展线索：2
        if (parent_id == 0 && source == 0) {
          return '初始线索'
        } else if (parent_id == 0 && source == 2) {
          return '扩展线索'
        } else if (parent_id == 0 && source == 4) {
          return '初始线索'
        }
      }
    },
    // 忽略
    ignore(id, type) {
      this.$parent.removeOne(id, type)
    },
    // 根据企业名称筛选列表的企业 并筛选
    selectedByCompanyN(companyN, isSelected) {
      this.tableData.forEach((item) => {
        if (item['clue_company_name'] == companyN) {
          this.$set(item, 'checked', isSelected)
        }
      })
      this.checkedArr = this.tableData.filter((item) => {
        return item.checked
      })
      this.$emit('checkedArr', this.checkedArr)
    }
  }
}
</script>
<style lang="less" scoped>
.popoverBtn {
  /deep/ .el-button--text {
    color: black;
    &:hover {
      color: #409eff;
    }
  }
}
.myTableHeader {
  display: flex;
  align-items: center;
  height: 52px;
  font-weight: 600;
  color: #62666c;
  font-size: 12px;
  background: #f2f3f5;
  border: 1px solid #e4e8ef;
  .chooseBox {
    padding: 0px 14px 0px 10px;
    width: 55px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
  }
  .operationBox {
    padding: 0px 10px;
    width: 55px;
    box-sizing: border-box;
  }
  .shuBox {
    width: 10px;
  }
  .tableHeaderBox {
    flex-grow: 1;
  }
  .tableHeaderBox > div {
    padding: 0px 10px;
    box-sizing: border-box;
  }
  .qiyeBox {
    padding: 0px 0px 0px 10px !important;
  }
}
ul {
  li {
    font-size: 12px !important;
    color: #37393c !important;
    & > .borderWrap {
      display: flex;
      justify-content: space-between;
      padding: 8px 16px;
      border-bottom: 1px solid #ebeef5;
      border-right: 1px solid #ebeef5;
    }
    .fofaImg {
      // vertical-align: middle;
      margin: 0 5px;
      padding: 4px 8px;
      border-radius: 4px;
      box-sizing: border-box;
      background: #dbe0e3;
    }
    .whoisImg {
      // vertical-align: middle;
      margin: 0 5px;
      padding: 6px 8px;
      border-radius: 4px;
      box-sizing: border-box;
      background: #e5eeff;
    }
    img {
      vertical-align: middle;
      margin-right: 5px;
    }
    .el-checkbox {
      width: 37px;
      line-height: 34px;
    }
    .contentClass {
      display: inline-block;
      width: 45%;
      line-height: 30px !important;
      padding: 0 5px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: #37393c;
    }
    .contentClass_28 {
      width: 31%;
    }
    .el-button--text {
      width: 55px;
    }
  }
}
.zhanwu {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
}
.clueContent {
  height: calc(100% - 60px);
  overflow: auto;
  li:first-child {
    border-radius: 4px 0px 0px 0;
  }
  li:last-child {
    border-radius: 0 0px 0 4px;
  }
}
.eltable_domain {
  border-left: 4px solid #2677ff !important;
}
.eltable_ip {
  border-left: 4px solid #1059d5 !important;
}
.eltable_icp {
  border-left: 4px solid #05d4a7 !important;
}
.eltable_cert {
  border-left: 4px solid #13b7ff !important;
}
.eltable_icon {
  border-left: 4px solid #ec8f3c !important;
}
.eltable_keyword {
  border-left: 4px solid #5346ff !important;
}
.eltable_subdomain {
  border-left: 4px solid #ff46de !important;
}
.warning-row {
  background: #fdecec;
}
.kgBox {
  margin-left: 8px;
}
/deep/.el-tag {
  height: auto !important;
  line-height: normal;
  padding: 2px 5px;
}
/deep/.el-pagination--small .arrow.disabled,
/deep/.el-table .el-table__cell.is-hidden > *,
/deep/.el-table .hidden-columns,
.el-table--hidden {
  visibility: visible !important;
}
</style>
