<template>
  <div class="containerWrap">
    <div class="menuWrap" v-if="$route.path != '/screenAssets'">
      <div class="left">
        <div class="title">
          <!-- <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-bianzu"></use>
          </svg> -->
          <img style="width: 20px;height: 20px; padding: 0 12px" src="https://**********89/api/v1/files/eyJpdiI6IlNSN3Y4TTdrSUxKckttTlhMMjFDWnc9PSIsInZhbHVlIjoiVnNPc1lIc2gyZW84VGFEK1NMSUJ6N2dlVW9ZajJIVE9HcUZEV3FJUStWZXhjeGwrN2tWSVh0V0l5UWhhZDVNc3ByRnVUV2NKbm5ucTFkd1FLdHMyeTNmdDhSb1VHSFJxYnpRaVpqMzZVU0ZZajVxL2d6Y3g0TUtxTkhHa2o4MXdMVm5Db0wxM3pWMjQ3ODJweW5BUzROSzNZU3BhYlRpOHNnWUNWckVISDhKNnIzNEsvcTFNa3JFUGZoUDJjTzNLUkJXZGNaY0tKdFkzSkM1WFN4bVZzd2xjd2NCK2tPb3NXdS9PckVNOUhqQT0iLCJtYWMiOiI5M2U1MjUzYzM2NjZmOWMwOTY2NTA0OGVhN2RhNDljZDJlZWQyMzQyOTMxMmExYTYyNWM1ZmVjMzEyNjdjYWY5IiwidGFnIjoiIn0.svg" alt="">
          <span>互联网资产攻击面管理平台</span>
          <!-- <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-bianzu"></use>
          </svg> -->
          <i
            :class="sidebarOpened ? 'el-icon-s-fold' : 'el-icon-s-unfold'"
            style="margin-left: 20px"
            @click="toggleSidebarFn"
          ></i>
        </div>
      </div>
      <div class="setter">
        <div
          class="scrolling-text"
          style="cursor: pointer"
          @click="jumpToDetail"
          v-if="intelligenceData.total !== 0"
        >
          <img src="../assets/images/intelligence/notice.png" alt="" />
          <div class="info">
            <div class="text" v-if="intelligenceData.total !== 0">
              <template v-if="intelligenceData.data_type == '2'">最新 5 条</template
              ><template v-if="intelligenceData.data_type == '1'"
                >关联到<span> {{ intelligenceData.total }} </span>条</template
              >专项情报
              <!-- {{intelligenceData.data_type == '1' ? '关联到' : '新增'}}专项情报, -->
              <span
                class="risk_name"
                v-for="(item, index) in intelligenceData.items.slice(0, 8)"
                :key="index"
              >
                {{ index + 1 }}、<span>{{ item.risk_name }}</span> &nbsp;&nbsp;
              </span>
              <span style="cursor: pointer" @click="jumpToDetail()"> ...查看更多 </span>
            </div>
          </div>
        </div>
        <img src="../assets/images/company.png" alt="" v-if="user.role != 1" />
        <el-select
          filterable
          v-if="user.role == 2"
          v-model="currentCompany"
          placeholder="请选择企业名称"
          class="tran"
        >
          <el-option
            v-for="item in companyArr"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            :disabled="item.status == 0"
            @click.native="companyChangeFun(item.id, item.owner_id, item.status, item)"
          >
            <span>{{ item.name }}</span>
            <span class="accountTimeAfter" v-if="item.status == 0">已过期</span>
          </el-option>
        </el-select>
        <span
          style="font-size: 14px; margin-right: 10px; margin-left: 10px"
          v-if="user.role == 3"
          >{{ company.name }}</span
        >
        <img src="../assets/images/touxiang1.png" alt="" class="portrait" />
        <el-dropdown trigger="click">
          <span class="el-dropdown-link">
            {{ account }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <!-- 售后人员只有个人中心/退出登录 -->
            <el-dropdown-item
              v-if="user.role != 4"
              @click.native="dialogFormVisible('dialogFormVisibleIp')"
              >禁扫IP设置</el-dropdown-item
            >
            <el-dropdown-item v-if="user.role != 4" @click.native="setTime"
              >禁扫时间设置</el-dropdown-item
            >
            <!-- <el-dropdown-item @click.native="$router.push({ path: '/docsApi' })"
              >API 文档</el-dropdown-item
            > -->
            <el-dropdown-item @click.native="personalCenter">个人中心</el-dropdown-item>
            <el-dropdown-item @click.native="gologout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div
      class="main_content"
      :class="{ hideSidebar: !sidebarOpened }"
      v-if="$route.path != '/screenAssets'"
    >
      <div class="menucontent">
        <el-menu
          class="el-menu-vertical-demo"
          :unique-opened="true"
          text-color="#ffffffb3"
          :default-active="isActive"
          active-text-color="#fff"
          :collapse="!sidebarOpened"
        >
          <div v-for="item in menuData" :key="item.id">
            <!-- 只有一级菜单 -->
            <el-menu-item
              :disabled="item.disabled == 0"
              v-if="item.menuIsShow && !item.children && (!item.quxian || item.quxian == user.role)"
              :index="item.id"
              @click="goJump(item.path, item.id)"
            >
              <span
                style="padding: 0px 20px"
                class="myTitle"
                @mouseenter="enterReport(item.id)"
                @mouseleave="leaveReport(item.id)"
              >
                <div
                  class="tuBox"
                  :id="'tuBox' + item.id"
                  :style="{ 'background-image': `url(${item.iconImg})` }"
                ></div>
                <span class="menuTitle" slot="title">{{ item.title }}</span>
              </span>
            </el-menu-item>
            <!-- 多级菜单 -->
            <el-submenu
              :disabled="item.disabled == 0"
              v-if="item.menuIsShow && item.children"
              :index="item.id"
            >
              <template slot="title">
                <span
                  class="asset"
                  @mouseenter="enterReport(item.id)"
                  @mouseleave="leaveReport(item.id)"
                >
                  <span
                    class="tuBox"
                    :id="'tuBox' + item.id"
                    :style="{ 'background-image': `url(${item.iconImg})` }"
                  ></span>
                  <span class="menuTitle" slot="title">{{ item.title }}</span>
                </span>
              </template>
              <div v-for="itemC in item.children" :index="itemC.path" :key="itemC.id">
                <el-menu-item
                  :disabled="itemC.disabled == 0"
                  v-if="
                    itemC.menuIsShow &&
                    !itemC.children &&
                    (!itemC.quxian || itemC.quxian == user.role) &&
                    (!itemC.is_show_copy_data ||
                      itemC.is_show_copy_data == userMessage.is_show_copy_data)
                  "
                  :index="itemC.id"
                  @click="goJump(itemC.path, itemC.id)"
                >
                  <span>
                    <i class="iconfont icon-lingxing" style="font-size: 8px"></i>
                    <span class="menuTitle" slot="title">{{ itemC.title }}</span>
                  </span>
                </el-menu-item>
                <el-submenu
                  :disabled="item.disabled == 0"
                  v-if="
                    itemC.menuIsShow &&
                    itemC.children &&
                    (!itemC.quxian || itemC.quxian == user.role) &&
                    (!itemC.is_show_copy_data ||
                      itemC.is_show_copy_data == userMessage.is_show_copy_data)
                  "
                  :index="itemC.id"
                >
                  <template slot="title">
                    <span style="padding-left: 40px">
                      <i class="iconfont icon-lingxing" style="font-size: 8px"></i>
                      <span class="menuTitle">{{ itemC.title }}</span>
                    </span>
                  </template>
                  <div v-for="itemD in itemC.children" :index="itemD.path" :key="itemD.id">
                    <el-menu-item :index="itemD.id" @click="goJump(itemD.path, itemD.id)">
                      <span slot="title" style="padding-left: 60px">
                        <b></b>
                        <span class="menuTitle">{{ itemD.title }}</span>
                      </span>
                    </el-menu-item>
                  </div>
                </el-submenu>
              </div>
            </el-submenu>
          </div>
          <!-- <div class="picture"><img src="../assets/images/menu.png" alt=""></div> -->
        </el-menu>
      </div>
      <div class="main_body" :class="$route.meta.bodyClassName">
        <router-view
          v-if="!$route.meta || !$route.meta.keepAlive"
          :key="$route.fullPath"
        ></router-view>
        <keep-alive>
          <!-- 这里是会被缓存的视图组件，比如 Home！ -->
          <router-view v-if="$route.meta.keepAlive" :key="$route.path"></router-view>
        </keep-alive>
      </div>
    </div>
    <div class="screenAssetsBody" v-if="$route.path == '/screenAssets'">
      <router-view :key="$route.fullPath"></router-view>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleIp"
      width="1010px"
      :before-close="forbidIpsResetForm"
    >
      <template slot="title"> 禁扫IP设置 </template>
      <div class="dialog-body">
        <div class="left">
          <el-form
            :model="ruleFormIp"
            :rules="rulesIp"
            ref="ruleFormIp"
            style="padding-left: 0 !important"
            label-width="80px"
            class="demo-ruleForm"
          >
            <el-form-item label="添加类型" prop="type">
              <el-select
                v-model="ruleFormIp.type"
                placeholder="请选择分组"
                @change="forbidIpsTypeChange"
              >
                <el-option label="上传IP信息文件" value="0"></el-option>
                <el-option label="输入IP信息" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="ips">
              <el-input
                v-if="ruleFormIp.type == '1'"
                type="textarea"
                :rows="10"
                v-model="ruleFormIp.ips"
                placeholder="请输入禁扫IP，多个值请使用分号或换行分隔
              支持格式如下：
              **********
              ***********
              "
              ></el-input>
              <div v-else>
                <p class="downloadClass" @click="downloadForbidIpsExcel">
                  <i class="el-icon-warning"></i>请点击下载
                  <span>ip信息导入模板</span>
                </p>
                <el-upload
                  ref="upload"
                  class="upload-demo"
                  drag
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  accept=".xlsx"
                  :before-upload="beforeIpUpload"
                  :on-success="ipUploadSuccess"
                  :on-error="ipUploadError"
                  :limit="uploadMaxCount"
                  :on-exceed="handleExceed"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                  <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过20M</div>
                </el-upload>
              </div>
            </el-form-item>
          </el-form>
          <div class="dialog-footer">
            <el-button
              style="margin-right: 24px"
              :loading="btnLoading"
              class="highBtn"
              @click="forbidIpsSubmit"
              >添加</el-button
            >
          </div>
        </div>
        <div class="right">
          <div class="filterTab">
            <div>
              <span>禁扫IP信息列表</span><b>{{ forbidIpsData.total }}</b>
              <el-input
                v-model="forbidIpListParams.keyword"
                placeholder="请输入IP进行搜索"
                @keyup.enter.native="forbidIpsSearch"
              >
                <el-button slot="append" icon="el-icon-search" @click="forbidIpsSearch"></el-button>
              </el-input>
            </div>
            <div>
              <el-button class="normalBtnRe" type="primary" @click="deleteForbidIpsFun"
                >删除</el-button
              >
            </div>
          </div>
          <div class="tableWrap">
            <el-table
              border
              v-load-more.expand="{
                func: loadMore,
                target: '.el-table__body-wrapper',
                delay: 500,
                distance: 50
              }"
              :data="forbidIpsData.data"
              @selection-change="forbidIpsDataChange"
              row-key="id"
              :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
              ref="eltable"
              height="100%"
              style="width: 100%"
            >
              <template slot="empty">
                <div class="emptyClass">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-kong"></use>
                  </svg>
                  <p>暂无数据</p>
                </div>
              </template>
              <template slot="append">
                <div>
                  <p class="tableInfo" v-if="forbidIpsData.data.length < forbidIpsData.total"
                    >加载中...</p
                  >
                  <p
                    class="tableInfo"
                    v-if="
                      forbidIpsData.total == forbidIpsData.data.length && forbidIpsData.total > 10
                    "
                    >没有更多了</p
                  >
                </div>
              </template>
              <el-table-column type="selection" align="left" :reserve-selection="true" width="55">
              </el-table-column>
              <el-table-column
                prop="ip_segment"
                align="left"
                :show-overflow-tooltip="true"
                min-width="120"
                label="IP"
              >
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 配置弹窗 -->
    <settingDia v-if="setDiaIsShow" :setDiaSync="setDiaSync" />
    <el-dialog
      class="elDialogAdd elDialogAdd-claim"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="dialogFormVisibleMzsm"
      width="800px"
    >
      <template slot="title">
        <div style="width: 100%; display: flex; justify-content: space-between">
          <span>免责声明</span>
          <span v-show="!showAgree" class="yzm"
            ><span style="color: #409eff; font-weight: bold">{{ count }}</span
            >s后可操作</span
          >
        </div>
      </template>
      <div>
        <mzsmText style="height: 650px" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-radio :disabled="!showAgree" v-model="agree_status" label="1">我已同意</el-radio>
        <el-radio :disabled="!showAgree" v-model="agree_status" label="0">不同意</el-radio>
        <el-button
          :disabled="!showAgree || !agree_status"
          class="highBtn"
          @click="agree_status_change"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog class="elDialogAdd elDialogNewList" :visible.sync="dialogVisible" width="30%">
      <template slot="title">
        <div style="width: 100%; display: flex; justify-content: space-between">
          <span>资产及风险变化提示</span>
        </div>
      </template>
      <span>您好！对比上次登录：</span>
      <div>
        <span v-if="newAssetChange" style="margin-left: 25px; margin-top: 10px"
          >新增<span style="color: green">{{ newAssetChange }}</span
          >条资产变化记录</span
        >
        <span v-if="newAssetChange && newRiskChange">,</span>
        <span v-if="newRiskChange"
          >新增<span style="color: red">{{ newRiskChange }}</span
          >条风险变化记录</span
        >
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goTostationMsg">查看记录</el-button>
      </span>
    </el-dialog>
    <el-dialog
      class="elDialogAddFirstLogin resetPasswordTip"
      title="提示"
      :close-on-click-modal="false"
      :visible.sync="resetPasswordTip"
      width="386px"
    >
      <img src="../assets/images/lock.png" alt="" />
      <p>初次登录系统，建议<span @click="toPersonalCenter" class="jump">修改密码</span></p>
    </el-dialog>
    <!-- 禁扫时间设置 -->
    <el-dialog
      title="禁扫时间设置"
      :visible.sync="dialogVisibleTiem"
      :close-on-click-modal="false"
      width="700px"
      class="elDialogAdd"
    >
      <div>
        <div style="color: red; margin-bottom: 20px"
          ><i class="el-icon-warning"></i
          >您可以自定义禁止扫描时间段，在该时间段内，任务将自动暂停，禁扫时间结束后，任务会继续执行。</div
        >
        <div style="color: red; margin-bottom: 20px"
          ><i class="el-icon-warning"></i
          >进度超过50%的任务，已完成扫描操作，会继续进行入库，此时不会对资产造成影响。</div
        >
        <el-form
          :model="timeParameter"
          style="padding: 0 !important"
          :rules="timeRule"
          label-width="150px"
          class="demo-ruleForm"
        >
          <el-form-item label="禁扫时间设置" prop="switch">
            <el-select v-model="timeParameter.switch" placeholder="请选择">
              <el-option label="开启" :value="1"></el-option>
              <el-option label="关闭" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="周期设置" prop="set_cycle">
            <el-select v-model="timeParameter.set_cycle" placeholder="请选择" clearable multiple>
              <el-option
                v-for="item in cycleList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="禁止扫描时间" prop="time">
            <el-time-picker
              is-range
              v-model="timeParameter.time"
              size="small"
              placeholder="选择禁止扫描时间范围"
              format="HH:mm"
              value-format="HH:mm"
              class="time"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            >
            </el-time-picker>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleTiem = false">取消</el-button>
        <el-button type="primary" @click="saveTime">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import mzsmText from './login/mzsm.vue'
import settingDia from './home_set/settingDialog.vue'
import { intelligenceReduceData, getNewList } from '@/api/apiConfig/api.js'
import {
  getColumn,
  setProhibit,
  getProhibit,
  getMyCompany,
  deleteForbidIps,
  addForbidIps,
  getForbidIps,
  getCompanyInfo,
  logout,
  personInfo,
  getSystemInfo,
  setAgree,
  setFirstTaskNotice
} from '@/api/apiConfig/person.js'

/**
 * @author: cunhang_wei
 * @description: 监听列表是否滚动到底部
 */

const debounce = function (func, delay) {
  let timer = null
  return function () {
    if (timer) clearTimeout(timer)
    timer = null
    let self = this
    let args = arguments
    timer = setTimeout(() => {
      func.apply(self, args)
    }, delay)
  }
}

export default {
  components: { mzsmText, settingDia },
  data() {
    return {
      notifyTip: false,
      intelligenceData: {
        total: 0,
        items: []
      },
      dialogVisible: false,
      message: '',
      newAssetChange: 0, // 新增资产变化记录数
      newRiskChange: 0, // 新增风险查看记录数
      dialogVisibleTiem: false, //禁扫时间
      timeParameter: {
        //禁扫时间设置传参
        switch: 1,
        set_cycle: [],
        time: [new Date(new Date()), new Date(new Date())]
      },
      cycleList: [
        {
          id: 1,
          name: '周一'
        },
        {
          id: 2,
          name: '周二'
        },
        {
          id: 3,
          name: '周三'
        },
        {
          id: 4,
          name: '周四'
        },
        {
          id: 5,
          name: '周五'
        },
        {
          id: 6,
          name: '周六'
        },
        {
          id: 7,
          name: '周日'
        }
      ],
      timeRule: {
        set_cycle: [{ required: true, message: '请选周期设置', trigger: 'change' }],
        time: [{ required: true, message: '选择禁止扫描时间范围', trigger: 'change' }]
      },
      setDiaSync: '',
      btnLoading: false,
      setDiaIsShow: false,
      agree_status: false, // 是否同意免责声明
      activeMenu: '/assetsScan',
      isActive: '9',
      menuData: [],
      menuDataCopy: [
        {
          id: '9',
          label: 'workbench',
          title: '工作台',
          path: '/workbench',
          // icon: 'icon-qiyeguanli',
          menuIsShow: true,
          iconImg: require('../assets/images/report.png')
        },
        {
          id: '1',
          label: 'assetsView',
          title: '资产总览',
          path: '',
          icon: 'icon-qiye',
          menuIsShow: true,
          iconImg: require('../assets/images/asset.png'),
          children: [
            {
              id: '1-1',
              label: 'index',
              title: '资产概览',
              path: '/index',
              // icon: 'icon-qiye',
              menuIsShow: true
            },

            {
              id: '1-3',
              label: 'assetsLedger',
              title: '资产台账',
              menuIsShow: true,
              children: [
                {
                  id: '1-3-1',
                  label: 'assetsLedger',
                  title: 'IP资产',
                  path: '/assetsLedger',
                  menuIsShow: true
                },
                {
                  id: '1-3-2',
                  label: 'loginEntry',
                  title: '登录入口',
                  path: '/loginEntry',
                  menuIsShow: true
                },
                {
                  id: '1-3-3',
                  label: 'domainAsset',
                  title: '域名资产',
                  path: '/domainAsset',
                  menuIsShow: true
                },
                {
                  id: '1-3-4',
                  label: 'certAsset',
                  title: '证书资产',
                  path: '/certAsset',
                  menuIsShow: true
                },
                {
                  id: '1-3-5',
                  label: 'businessSystem',
                  title: '业务系统',
                  path: '/businessSystem',
                  menuIsShow: true
                },
                {
                  id: '1-3-6',
                  label: 'urlAsset',
                  title: 'URL(API)资产',
                  path: '/urlAsset',
                  menuIsShow: true
                }
              ]
            },

            {
              id: '1-4',
              label: 'unclaimCloud',
              title: '疑似资产',
              path: '/unclaimCloud', // '/unclaimCloud'
              menuIsShow: true
            },
            {
              id: '1-5',
              label: 'ignoreAssets',
              title: '忽略资产',
              path: '/ignoreAssets',
              menuIsShow: true
            },
            {
              id: '1-7',
              label: 'newAssets',
              title: '数字资产',
              path: '/newAssets',
              menuIsShow: true
            }
          ]
        },
        {
          id: '12',
          label: 'organization',
          title: '企业资产管控',
          icon: 'icon-qiyeguanli',
          path: '/organization',
          menuIsShow: true,
          quxian: 2,
          iconImg: require('../assets/images/system.png')
        },
        {
          id: '1-6',
          label: 'clueOverview',
          title: '线索库',
          menuIsShow: true,
          iconImg: require('../assets/images/clueDes.png'),
          children: [
            {
              id: '1-6-4',
              label: 'clueOverview',
              title: '线索概览',
              path: '/clueOverview',
              menuIsShow: true
            },
            // {
            //   id: '1-6-1',
            //   label: 'publicClueBank',
            //   title: '公共线索库',
            //   path: '/publicClueBank',
            //   menuIsShow: true
            // },
            {
              id: '1-6-2',
              label: 'companyBank',
              title: '企业线索库',
              path: '/companyBank',
              menuIsShow: true
            },
            {
              id: '1-6-3',
              label: 'supplyChainBank',
              title: '供应链线索库',
              path: '/supplyChainBank',
              menuIsShow: true
            }
          ]
        },
        {
          id: '2',
          label: 'index',
          title: '任务管理',
          path: '',
          icon: 'icon-zichanfaxian',
          menuIsShow: true,
          iconImg: require('../assets/images/system.png'),
          children: [
            // {
            //   id: '2-0',
            //   label: 'index',
            //   title: '任务概览',
            //   path: '/assetsTaskView',
            //   menuIsShow: true
            // },
            {
              id: '2-0-1',
              label: 'index',
              title: '任务概览',
              path: '/taskOverview',
              menuIsShow: true
            },
            {
              id: '2-1',
              label: 'assetsTanzhi',
              title: '资产探知',
              path: '/assetsTanzhi',
              menuIsShow: true
            }
          ]
        },
        {
          id: '3',
          label: 'index',
          title: '风险管理',
          path: '',
          icon: 'icon-shujuxielouguanli',
          menuIsShow: true,
          iconImg: require('../assets/images/leak.png'),
          children: [
            {
              id: '3-1',
              label: 'leakScan',
              title: '漏洞管理',
              path: '',
              menuIsShow: true,
              children: [
                {
                  id: '3-1-1',
                  label: 'leakScan',
                  title: '漏洞扫描',
                  path: '/leakScan',
                  menuIsShow: true
                },
                {
                  id: '3-1-2',
                  label: 'repairLeakScan',
                  title: '漏洞跟踪',
                  path: '/repairLeakScan',
                  menuIsShow: true
                }
              ]
            },

            {
              id: '3-2',
              label: 'dataLeak',
              title: '数据泄露',
              path: '/dataLeak',
              menuIsShow: true
            },
            {
              id: '3-3',
              label: 'eventWarning',
              title: '事件告警',
              path: '/eventWarning',
              menuIsShow: true
            },
            {
              id: '3-4',
              label: 'threatAssets',
              title: '威胁资产',
              path: '/threatAssets',
              menuIsShow: true
            }
          ]
        },
        {
          id: '10',
          label: 'index',
          title: '情报中心',
          path: '',
          icon: 'icon-shujuxielouguanli',
          menuIsShow: true,
          iconImg: require('../assets/images/system.png'),
          children: [
            {
              id: '10-1',
              label: 'intelligenceBrief',
              title: '情报概览',
              path: '/intelligenceBrief',
              menuIsShow: true
            },
            {
              id: '10-2',
              label: 'intelligenceRelated',
              title: '关联情报',
              path: '/intelligenceRelated',
              menuIsShow: true
            },
            {
              id: '10-3',
              label: 'intelligenceCenterv1',
              title: '情报总览',
              path: '/intelligenceCenterv1',
              menuIsShow: true
            }
          ]
        },
        {
          id: '8',
          label: 'spatialRetrieval',
          title: '空间检索',
          path: '/spatialRetrieval',
          icon: 'icon-qiyeguanli',
          menuIsShow: true,
          iconImg: require('../assets/images/audit.png')
        },
        // {
        //   id: '4',
        //   label: 'ipBanningManage',
        //   title: '黑IP封禁管理',
        //   path: '/ipBanningManage',
        //   icon: 'icon-baogaoguanli',
        //   menuIsShow: true,
        //   iconImg: require('../assets/images/system.png')
        // },
        {
          id: '5',
          label: 'reportManage',
          title: '报告管理',
          path: '/reportManage',
          icon: 'icon-baogaoguanli',
          menuIsShow: true,
          iconImg: require('../assets/images/report.png')
        },
        {
          id: '6',
          label: 'logManage',
          title: '审计管理',
          path: '/loginLog',
          icon: 'icon-qiyeguanli',
          menuIsShow: true,
          quxian: 1,
          iconImg: require('../assets/images/report.png')
        },
        {
          id: '7',
          label: 'sysManage',
          title: '系统管理',
          path: '',
          icon: 'icon-xitongguanli',
          menuIsShow: true,
          iconImg: require('../assets/images/system.png'),
          children: [
            {
              id: '7-99',
              label: 'digitalAssets',
              title: '数字资产总库',
              path: '',
              menuIsShow: true,
              quxian: 1,
              children: [
                {
                  id: '7-99-1',
                  label: 'digitalAssets-task',
                  title: '任务管理',
                  path: '/digitalAssets-task',
                  menuIsShow: true,
                  quxian: 1
                },
                {
                  id: '7-99-2',
                  label: 'digitalAssets-data',
                  title: '资产管理',
                  path: '/digitalAssets-data',
                  menuIsShow: true,
                  quxian: 1
                }
              ]
            },
            {
              id: '7-1',
              title: '端口管理',
              path: '/portManage',
              menuIsShow: true
            },
            {
              id: '7-2',
              title: 'PoC管理',
              path: '/pocManage',
              menuIsShow: true
            },
            {
              id: '7-2-1',
              title: '漏洞审核',
              path: '/pocAudit',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-3',
              title: '事件规则管理',
              path: '/eventRuleManage',
              menuIsShow: true
            },
            // {
            //   id: '7-4',
            //   title: 'IP段管理',
            //   path: '/ipManage',
            //   menuIsShow: true
            // },
            {
              id: '7-5',
              title: '关键词管理',
              path: '/keywordManage',
              menuIsShow: true
            },
            {
              id: '7-6',
              title: '线索黑名单管理',
              path: '/clueBlack',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-7',
              title: '用户管理',
              path: '/userManage',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-8',
              title: '升级中心FD01',
              path: '/upgradeCenterUpload',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-8-1',
              title: '升级中心Foradar',
              path: '/upgradeCenterUploadForadar',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-9',
              title: '公告管理',
              path: '/noticeManage',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-10',
              title: '售后管理菜单',
              path: '/serviceManage',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-11',
              title: '线索总库',
              path: '/clueStore',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-12',
              title: '数据泄露总库',
              path: '/dataLeakStore',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-13',
              title: '在线用户',
              path: '/userOnline',
              menuIsShow: true,
              quxian: 1
            },
            // {
            //   id: '7-20',
            //   title: '网口管理',
            //   path: '/internetManage',
            //   menuIsShow: true,
            //   quxian: 1
            // },
            {
              id: '7-16',
              title: '自定义规则',
              path: '/defaultRule',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-17',
              title: '定时任务',
              path: '/cronTask',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-14',
              title: '线索扩展任务管理',
              path: '/clueExpansionManage',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-15',
              title: '配置管理',
              path: '/settingManage',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-18',
              title: '企业架构管理',
              path: '/orgOperation',
              menuIsShow: true,
              quxian: 2
            },
            {
              id: '7-20',
              title: '威胁词库',
              path: '/yellowGambling',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-21',
              title: 'ICP备案总库',
              path: '/icpDatabase',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-23',
              title: '扫描任务',
              path: '/taskViewList',
              menuIsShow: true,
              quxian: 1
            },
            {
              id: '7-24',
              title: '情报管理',
              path: '/intelligenceManage',
              menuIsShow: true,
              scope: 'intelligence_manager'
            },
            {
              id: '7-25',
              title: '站内信',
              path: '/stationMsg',
              menuIsShow: true,
              scope: 'stationMsg'
            },
            {
              id: '7-26',
              title: '数据同步',
              path: '/dataSync',
              menuIsShow: true,
              is_show_copy_data: '1'
              // quxian: 1
            },
            {
              id: '7-27',
              title: '微服务请求记录',
              path: '/microServe',
              menuIsShow: true,
              quxian: 1
            }
          ]
        }
      ],
      user: {
        role: ''
      },
      scopes: [],
      company: { name: '' },
      companyArr: [],
      currentCompany: '',
      account: '',
      transferData: [],
      forbidIpsData: {
        data: [],
        total: 0
      },
      dialogFormVisibleCon: false,
      dialogFormVisibleIp: false,
      dialogFormVisibleResetPsd: false,
      dialogFormVisibleSys: false,
      dialogFormVisibleUp: false,
      dialogFormVisiblePsd: false,
      dialogFormVisibleMzsm: false,
      ruleFormIp: {
        type: '1',
        ips: ''
      },
      rulesIp: {},
      forbidIpListParams: {
        page: 1,
        per_page: 10,
        keyword: '',
        operate_company_id: this.currentCompany
      },
      uploadHeaders: { Authorization: localStorage.getItem('token') },
      forbidIpsSelect: [],
      uploadAction: `${this.golangUploadSrcIp}/forbid/ips/upload?identifier=${new Date().getTime() + Math.round(Math.random() * 100).toString()}`,
      uploadMaxCount: 1,
      count: '',
      showAgree: false,
      showAgreetimer: null,
      resetPasswordTip: false,
      companyInfo: null,
      userMessage: {},
      sysUser: {
        role: ''
      },
      userInfo:{}
    }
  },
  async created() {
    sessionStorage.setItem('isGetCompany', '')
    // 存储用户信息
    let userMessage = await personInfo()
    sessionStorage.setItem('userMessage', JSON.stringify(userMessage.data))

    this.setOwnerIdChange(userMessage.data.user.id) // 存储当前账号id
    this.userMessage = userMessage.data
    this.getTestTip()
    if (userMessage.data) {
      this.account = userMessage.data.user.name
      this.user = userMessage.data.user
      this.company = userMessage.data.company
      if (this.user.agree_status != 1 && this.user.role != 1) {
        // 免责声明,超管不用免责声明
        this.dialogFormVisibleMzsm = true
        this.getCode()
      }
      if (this.user.role == 2) {
        // 安服， 此操作主要解决：企业的权限修改后，刷新页面就可以更新
        sessionStorage.setItem('is_service', '')
        this.menuData = [...this.menuDataCopy]
        await this.getCompanyArr()
        if (this.currentCompany == -1) {
          // 安服自己账号
          sessionStorage.setItem('companyInfo', '') // 切到安服角色清空数据
          await this.getAuthMe()
        } else {
          // 安服管理的企业
          let res = await getCompanyInfo(this.currentCompany)
          if (res.code == 0) {
            // sessionStorage.setItem('isGetCompany', 'true')
            this.companyInfo = res.data
            sessionStorage.setItem('companyInfo', JSON.stringify(res.data)) // 安服切换企业时全局存储企业权限信息
            await this.getAuthMe()
          }
        }
      } else if (this.user.role == 4) {
        // 售后,只保留此菜单
        this.menuData = [
          {
            id: '7-10',
            title: '售后管理菜单',
            path: '/serviceManage',
            menuIsShow: true
          }
        ]
        sessionStorage.setItem('is_service', 1)
        this.getAuthMe()
      } else {
        this.menuData = [...this.menuDataCopy]
        sessionStorage.setItem('is_service', '')
        this.getAuthMe()
      }
    }
    let arr = []
    for (var i = 0; i < this.menuData.length; i++) {
      arr.push(this.menuData[i].path)
      if (this.menuData[i].children) {
        for (var j = 0; j < this.menuData[i].children.length; j++) {
          arr.push(this.menuData[i].children[j].path)
        }
      }
    }
    if (arr.indexOf(this.$route.path) != -1) {
      for (var i = 0; i < this.menuData.length; i++) {
        if (this.$route.path == this.menuData[i].path) {
          this.isActive = this.menuData[i].id
        } else {
          if (this.menuData[i].children) {
            for (var j = 0; j < this.menuData[i].children.length; j++) {
              if (this.$route.path == this.menuData[i].children[j].path) {
                this.isActive = this.menuData[i].children[j].id
              }
            }
          }
        }
      }
    } else {
      if (sessionStorage.getItem('menuId')) {
        this.isActive = sessionStorage.getItem('menuId')
      } else {
        this.isActive = '9'
      }
    }
    sessionStorage.setItem('menuId', this.isActive)
    this.scopes = this.user.scopes
    this.checkNewList()
  },

  async mounted() {
    let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (userInfo) {
      this.sysUser = userInfo.user
    }
    this.getCode()
    if (this.sysUser.role == 2 && !this.currentCompany) return
    this.getIntelligenceData()
  },
  beforeDestroy() {
    sessionStorage.setItem('currentCompany', '')
    sessionStorage.setItem('currentCompanyItem', '')
    this.changeMenuId('')
    sessionStorage.removeItem('menuId')
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.sysUser.role == 2) {
        this.getIntelligenceData()
      }
    },
    getterSettimeBox(val) {
      this.setTime()
    },
    getterChangeAfterCompany(data) {
      // 修改个人中心的用户名和企业后系统右上角及时更新
      this.company = data.company
      this.account = data.user.name
    },
    getterMenuId(val) {
      this.isActive = val
    },
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    }
  },
  computed: {
    ...mapState(['websocket', 'setTimeData', 'menuId', 'scrollHeight', 'sidebarOpened']),
    ...mapGetters([
      'getterCurrentCompany',
      'getterSettime',
      'getterSettimeBox',
      'getterChangeAfterCompany',
      'getterMenuId',
      'getterWebsocketMessage'
    ])
  },
  methods: {
    ...mapMutations([
      'toggleSidebar',
      'setJumpScrollHeight',
      'setCurrentCompany',
      'setCompanyChange',
      'setOwnerIdChange',
      'setTimeChange',
      'changeMenuId'
    ]),
    async handleMessage(res, o) {
      //处理接收到的信息
      if (
        res.cmd == 'finish_auto_detect' && res.data.detect_assets_tasks_id == this.userInfo.first_detect_task_id
      ) {
        if(this.userInfo.has_notice == 2 && this.notifyTip == false){
          this.$confirm('已为您完成'+this.userInfo.target_company_name+'资产测绘，资产测绘后，资产将自动同步到资产台账', '提示', {
            confirmButtonText: '查看结果',
            cancelButtonText: '取消',
            type: 'warning',
            customClass: 'custom-confirm-modal',
            closeOnClickModal: false
          }).then(() => {
            this.changeNotifyTip()
            this.$router.push('/unitIndex')
          }).catch(() => {
            this.changeNotifyTip()
          })
        }
      }
    },
    async getTestTip() {
      this.userInfo = JSON.parse(sessionStorage.getItem('userMessage')).user
      console.log(this.userInfo,"123")
      if(this.userInfo.need_asset_mapping == 1 && this.userInfo.first_detect_task_id){
        if(this.userInfo.detect_tasks_step == 3 && this.userInfo.has_notice == 2 && this.notifyTip == false){
          this.$confirm('资产测绘失败，请重新手动下发测绘', '提示', {
            confirmButtonText: '重新测绘',
            cancelButtonText: '取消',
            type: 'warning',
            customClass: 'custom-confirm-modal',
            closeOnClickModal: false
          }).then(() => {
            this.changeNotifyTip()
            this.$router.push('/unitIndex')
          }).catch(() => {
            this.changeNotifyTip()
          })
        }else if(this.userInfo.detect_tasks_step == 4 && this.userInfo.has_notice == 2 && this.notifyTip == false){
          this.$confirm('已为您成功完成'+this.userInfo.target_company_name+'资产测绘，资产测绘后，资产将自动同步到资产台账', '提示', {
            confirmButtonText: '查看结果',
            cancelButtonText: '取消',
            type: 'warning',
            customClass: 'custom-confirm-modal',
            closeOnClickModal: false
          }).then(() => {
            this.changeNotifyTip()
            this.$router.push('/unitIndex')
          }).catch(() => {
            this.changeNotifyTip()
          })
        }
      }
    },
    changeNotifyTip() {
      let res = setFirstTaskNotice({user_id:this.userInfo.id})
      if(res.code == 0){
        this.notifyTip = true
      }
    },
    toggleSidebarFn() {
      this.toggleSidebar(!this.sidebarOpened)
    },
    jumpToDetail() {
      this.changeMenuId('10-3')
      sessionStorage.setItem('menuId', '10-3')
      this.$router.push({
        path: '/intelligenceCenterv1', // 去已修复页面
        query: {
          flag: 1,
          actTabVal: 'special'
        }
      })
    },
    async getIntelligenceData() {
      let res = await intelligenceReduceData({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.intelligenceData.total = res.data.total || 0
        this.intelligenceData.items = res.data.items || []
        this.intelligenceData.data_type = res.data.data_type || []
      }
    },
    changeCompanyRoute() {
      let prePathName = this.$route.path
      if (prePathName == '/alreadyTask_viewlist_ipinfo') {
        this.$router.push('/assetsLedger')
        sessionStorage.setItem('menuId', '1-3-1')
      } else if (prePathName == '/domainDetails') {
        this.$router.push('/domainAsset')
        sessionStorage.setItem('menuId', '1-3-3')
      } else if (prePathName == '/groupAssets-recommend') {
        this.$router.push('/groupAssets')
        // sessionStorage.setItem('menuId', '1-3-3')
      } else if (prePathName == '/organization-clue' || prePathName == '/organization-assets') {
        this.$router.replace('/organization')
        sessionStorage.setItem('menuId', '7-18')
      } else if (prePathName == '/taskBriefing') {
        this.$router.replace('/unitIndex')
      }
    },
    goJump(path, id) {
      //页面跳转
      // if(path == '/index'){
      this.setJumpScrollHeight(0)
      // }
      document.getElementsByClassName('main_body')[0].scrollTop = this.scrollHeight
      this.$router.push(path)
      this.isActive = id
      this.changeMenuId(id)
      sessionStorage.setItem('menuId', id)
    },
    // 获取菜单配置
    async getcolumnData(currentCompany) {
      let res = await getColumn({ operate_company_id: currentCompany })
      if (this.$route.path != '/assetsLedger') return
      let completeList = res.data
      for (let i = 0; i < completeList.length; i++) {
        const element = completeList[i]
        if (element.type == 1) {
          element.settings.push('最新解析域名')
        }
      }
      sessionStorage.setItem('columnList', JSON.stringify(completeList))
      //  sessionStorage.setItem('columnList', JSON.stringify(res.data))
    },
    async setTime() {
      //禁扫时间弹框
      this.dialogVisibleTiem = true
      let res = await getProhibit({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.timeParameter = {
          switch: res.data.switch,
          set_cycle: res.data.cycle,
          time: [res.data.start_at, res.data.end_at]
        }
        this.$forceUpdate()
      }
    },
    async saveTime() {
      if (this.timeParameter.set_cycle.length == 0) {
        this.$message.error('请选择周期设置')
        return
      }
      if (!this.timeParameter.time) {
        this.$message.error('请选禁扫时间段')
        return
      }
      let obj = {
        switch: this.timeParameter.switch,
        set_cycle: this.timeParameter.set_cycle,
        start_at: this.timeParameter.time[0],
        end_at: this.timeParameter.time[1],
        operate_company_id: this.currentCompany
      }
      let res = await setProhibit(obj)
      if (res.code == 0) {
        this.dialogVisibleTiem = false
        this.$message.success('设置成功')
        if (this.setTimeData) {
          this.setTimeChange(false)
        } else {
          this.setTimeChange(true)
        }
      }
    },
    getCode() {
      const TIME_COUNT = 15 // 60秒自动获取，测试阶段设置3秒
      if (!this.showAgreetimer) {
        this.count = TIME_COUNT
        this.showAgree = false
        this.showAgreetimer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.showAgree = true
            clearInterval(this.showAgreetimer)
            this.showAgreetimer = null
          }
        }, 1000)
      }
    },
    async agree_status_change() {
      if (this.agree_status == 1) {
        let res = await setAgree()
        if (res.code == 0) {
          this.dialogFormVisibleMzsm = false
          this.resetPasswordTip = true
        }
      } else {
        let res = await logout()
        if (res.code == 0) {
          sessionStorage.clear()
          localStorage.clear()
          this.$router.replace('/login')
        }
      }
    },
    async getCompanyArr() {
      let res = await getMyCompany()
      sessionStorage.setItem('isGetCompany', 'true')
      this.companyArr = res.data
      if (sessionStorage.getItem('companyInfo')) {
        this.companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      }
      let currentCompanyItem = {}
      if (sessionStorage.getItem('currentCompany')) {
        // 解决刷新问题
        this.currentCompany = JSON.parse(sessionStorage.getItem('currentCompany')).id / 1
        // this.getcolumnData(this.currentCompany)
        this.setOwnerIdChange(JSON.parse(sessionStorage.getItem('currentCompany')).owner_id / 1) // 当前选中企业切换的时候存储owner_id
        this.companyArr.forEach((item) => {
          if (item.id == this.currentCompany) {
            currentCompanyItem = item
          }
        })
      } else {
        this.currentCompany = res.data.length > 0 ? res.data[0].id : '' // 默认第一项
        currentCompanyItem = res.data.length > 0 ? res.data[0] : []
        // this.getcolumnData(this.currentCompany)
        this.setOwnerIdChange(res.data.length > 0 ? res.data[0].owner_id : '') // 当前选中企业切换的时候存储owner_id
        sessionStorage.setItem(
          'currentCompany',
          JSON.stringify({
            id: this.currentCompany,
            owner_id: res.data.length > 0 ? res.data[0].owner_id : ''
          })
        )
      }
      sessionStorage.setItem(
        'currentCompanyItem',
        JSON.stringify({ currentCompanyItem: currentCompanyItem })
      )
      this.setCurrentCompany(this.currentCompany == '' && typeof this.currentCompany == 'String' ? 0 : this.currentCompany)
    },
    async companyChangeFun(val, owner_id, status, companyItem) {
      // 切换企业名称
      if (!status) return // 已过期账号不支持点击
      this.changeCompanyRoute()
      this.setCurrentCompany(val == '' && typeof val == 'String' ? 0 : val) // mutation存储安服当前选择的企业
      this.setCompanyChange(val) // 当前选中企业切换的时候存储id
      this.setOwnerIdChange(owner_id) // 当前选中企业切换的时候存储owner_id
      this.$notify.closeAll() // 切换企业关闭所有弹窗
      sessionStorage.setItem('currentCompany', JSON.stringify({ id: val, owner_id: owner_id }))
      sessionStorage.setItem(
        'currentCompanyItem',
        JSON.stringify({ currentCompanyItem: companyItem })
      )
      // this.getcolumnData(this.currentCompany)

      if (val == -1) {
        sessionStorage.setItem('companyInfo', '') // 切到安服角色清空数据
        this.getAuthMe()
      } else {
        let res = await getCompanyInfo(val)
        if (res.code == 0) {
          // sessionStorage.setItem('isGetCompany', true)
          this.companyInfo = res.data
          sessionStorage.setItem('companyInfo', JSON.stringify(res.data)) // 安抚切换企业时全局存储企业权限信息
          this.getAuthMe()
        }
      }
    },
    // 根据path获取索引
    getIndex(arr, path) {
      let index = arr.findIndex(function (item) {
        return item.path == path
      })
      return index
    },
    async getAuthMe() {
      // 登陆成功获取个人信息,渲染菜单
      let userMessage = sessionStorage.getItem('userMessage')
      if (userMessage) {
        this.account = JSON.parse(userMessage).user.name
        if (JSON.parse(userMessage).company) {
          this.companyInfo = JSON.parse(userMessage).company // 企业用户需要从me接口获取权限信息（确保安服账号不返回company信息）
          sessionStorage.setItem('companyInfo', JSON.stringify(this.companyInfo)) // 全局存储企业权限信息
        }
      }
      // 默认显示-资产台账
      if (this.$route.path && this.$route.path == '/home') {
        if (this.menuData[1] && this.menuData[1].path) {
          // 售后账号
          this.$router.replace(this.menuData[0].path)
          sessionStorage.setItem('menuId', '7-10')
        } else {
          this.$router.replace('/workbench') // 其余正常操作账号
          sessionStorage.setItem('menuId', '9')
        }
      }
    },
    progressText(val) {
      return '使用率：' + val + '%'
    },
    gologout() {
      this.$confirm(`确定退出登录？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await logout()
          if (res.code == 0) {
            sessionStorage.clear()
            localStorage.clear()
            this.websocket.close()
            this.$notify.closeAll() // 退出登录关闭所有弹窗
            this.$router.replace('/login')
          }
        })
        .catch(() => {})
    },
    personalCenter() {
      this.$router.push('/personalCenter')
    },
    async showSystem() {
      this.setDiaSync = 'dialogFormVisibleSys' // 需要显示弹窗的变量
      let res = await getSystemInfo()
    },
    async forbidIpListData() {
      if (
        this.forbidIpsData.total == this.forbidIpsData.data.length &&
        this.forbidIpsData.total > 0
      ) {
        return
      }
      this.forbidIpListParams.operate_company_id = this.currentCompany
      let result = await getForbidIps(this.forbidIpListParams)
      this.forbidIpsData.data = [...this.forbidIpsData.data, ...result.data.items]
      this.forbidIpsData.total = result.data.total
    },
    forbidIpsDataReset() {
      this.forbidIpsData.data = []
      this.forbidIpsData.total = 0
      this.forbidIpListParams.page = 1
      this.forbidIpsSelect = []
      if (this.$refs.eltable) {
        this.$refs.eltable.clearSelection()
      }
      this.forbidIpListData()
    },
    async forbidIpsSubmit() {
      if (this.user.role == 2 && this.currentCompany != -1) {
        this.$message.error('安服账号暂不能设置企业禁扫ip')
        return
      }
      let ipArr = this.ruleFormIp.ips
        .split(/[；|;|\r\n]/)
        .filter((item) => {
          return item != ''
        })
        .map((v) => {
          return v.trim()
        })
      if (this.ruleFormIp.type == '1') {
        if (ipArr.length == 0) {
          this.$message.error('IP不能为空')
          return
        }
      } else {
        if (ipArr.length == 0) {
          this.$message.error('请上传有效的文件')
          return
        }
      }
      this.btnLoading = true
      let result = await addForbidIps({ ips: ipArr }).catch(() => {
        this.btnLoading = false
      })
      this.btnLoading = false
      if (result.code != 0) {
        // this.$message.error(result.message)
        return
      }
      if (this.ruleFormIp.type == '1') {
        this.$refs['ruleFormIp'].resetFields() // 重置表单并移除校验
      } else {
        this.$refs.upload.clearFiles()
      }
      this.ruleFormIp.ips = ''
      this.forbidIpListParams.keyword = ''
      this.$message.success('添加成功')
      this.forbidIpsDataReset()
    },
    downloadForbidIpsExcel() {
      window.location.href = '/downloadTemplate/禁扫ip导入模板.xlsx'
    },
    downloadMzsmExcel() {
      window.location.href = '/downloadTemplate/FORadar-用户注册和服务协议.docx'
    },
    forbidIpsTypeChange(value) {
      this.ruleFormIp.type = value
    },
    beforeIpUpload(file) {
      if (this.user.role == 2 && this.currentCompany != -1) {
        this.$message.error('安服账号暂不能设置企业禁扫ip')
        return false
      }
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过 20MB!')
      }
      return isLt1M
    },
    ipUploadSuccess(response, file, fileList) {
      if (file.response.code == 0) {
        this.ruleFormIp.ips = response.data.join(';')
        this.$message.success('上传成功')
        // this.forbidIpsDataReset();
      } else {
        this.$message.error(file.response.message)
      }
    },
    ipUploadError(err, file, fileList) {
      this.$message.error(JSON.parse(err.message).message)
    },
    dialogFormVisible(value) {
      // let arr = ["dialogFormVisibleIp","dialogFormVisibleCon","dialogFormVisibleUp","dialogFormVisiblePsd"];
      this.setDiaSync = value // 需要显示弹窗的变量
      this[value] = true
      if (value == 'dialogFormVisibleIp') {
        this.forbidIpsDataReset()
      }
    },
    loadMore() {
      this.forbidIpListParams.page++
      this.forbidIpListData()
    },
    forbidIpsDataChange(value) {
      this.forbidIpsSelect = value
    },
    deleteForbidIpsFun() {
      let ids = this.forbidIpsSelect.map((v) => {
        return v.id
      })
      if (ids.length == 0) {
        this.$message.error('请选择操作对象')
        return
      }
      this.$confirm('此操作不可恢复，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let result = await deleteForbidIps({ ids })
          if (result.code != 0) {
            this.$message.error(result.message)
            return
          }
          this.forbidIpsDataReset()
        })
        .catch(() => {})
    },
    forbidIpsSearch() {
      this.forbidIpsDataReset()
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    forbidIpsResetForm() {
      this.$refs.ruleFormIp.resetFields()
      this.dialogFormVisibleIp = false
      this.forbidIpListParams.keyword = ''
    },
    toPersonalCenter() {
      this.resetPasswordTip = false
      this.$router.push('/personalCenter')
    },
    enterReport(id) {
      let a = document.getElementById('tuBox' + id)
      a.className = 'tuBox animationenter'
    },
    leaveReport(id) {
      let a = document.getElementById('tuBox' + id)
      a.className = 'tuBox animationchu'
    },

    async checkNewList() {
      if (localStorage.getItem('isVisited')) return
      let result = await getNewList({ operate_company_id: this.currentCompany })
      const msgTypeTotalMap = {}
      if (result && result.data && Array.isArray(result.data.items)) {
        // 遍历items数组并构建映射
        result.data.items.forEach((item) => {
          msgTypeTotalMap[item.msg_type] = item.total
        })
      }
      this.newAssetChange = msgTypeTotalMap['1']
      this.newRiskChange = msgTypeTotalMap['3']
      localStorage.setItem('isVisited', '1') // 设置企业ID 站内信为已访问
      if (this.newAssetChange > 0 || this.newRiskChange > 0) {
        this.dialogVisible = true
      }
    },

    goTostationMsg() {
      this.$router.push('/stationMsg')
      this.dialogVisible = false
    }

    // 移入左菜单栏添加动画
    // enterReport(val){
    //   if(val == 4){
    //     let a = document.getElementById('tuBox4')
    //     a.className = 'tuBox tuBox2 animationenter'
    //   }else if(val == 5){
    //     let a = document.getElementById('tuBox5')
    //     a.className = 'tuBox tuBox3 animationenter'
    //   }else if(val == 1){
    //     let a = document.getElementById('tuBox1')
    //     a.className = 'tuBox animationenter'
    //   }else if(val == 3){
    //     let a = document.getElementById('tuBox3')
    //     a.className = 'tuBox tuBox5 animationenter'
    //   }else if(val == 2){
    //     let a = document.getElementById('tuBox2')
    //     a.className = 'tuBox tuBox1 animationenter1'
    //   }else if(val == 7){
    //     let a = document.getElementById('tuBox7')
    //     a.className = 'tuBox tuBox7 animationenter'
    //   }else {
    //     let a = document.getElementById('tuBox6')
    //     a.className = 'tuBox tuBox4 animationenter1'
    //   }

    // },
    // 移出左菜单栏添加动画
    //   leaveReport(val){
    //     if(val == 4){
    //       let a = document.getElementById('tuBox4')
    //       a.className = 'tuBox tuBox2 animationchu'
    //     }else if(val == 5){
    //       let a = document.getElementById('tuBox5')
    //       a.className = 'tuBox tuBox3 animationchu'
    //     }else if(val == 1){
    //       let a = document.getElementById('tuBox1')
    //       a.className = 'tuBox animationchu'
    //     }else if(val == 3){
    //       let a = document.getElementById('tuBox3')
    //       a.className = 'tuBox tuBox5 animationchu'
    //     }else if(val == 2){
    //       let a = document.getElementById('tuBox2')
    //       a.className = 'tuBox tuBox1 animationchu1'
    //     }else if(val == 7){
    //       let a = document.getElementById('tuBox7')
    //       a.className = 'tuBox tuBox7 animationchu'
    //     }else {
    //       let a = document.getElementById('tuBox6')
    //       a.className = 'tuBox tuBox4 animationchu1'
    //     }
    //   }
  },
  directives: {
    'load-more': {
      bind(el, binding, vnode) {
        const { expand } = binding.modifiers
        // 使用更丰富的功能，支持父组件的指令作用在指定的子组件上
        if (expand) {
          /**
           * target 目标DOM节点的类名
           * distance 减少触发加载的距离阈值，单位为px
           * func 触发的方法
           * delay 防抖时延，单位为ms
           * load-more-disabled 是否禁用无限加载
           */
          let { target, distance = 0, func, delay = 200 } = binding.value
          if (typeof target !== 'string') return
          let targetEl = el.querySelector(target)
          if (!targetEl) {
            return
          }
          binding.handler = debounce(function () {
            const { scrollTop, scrollHeight, clientHeight } = targetEl
            let disabled = el.getAttribute('load-more-disabled')
            disabled = vnode[disabled] || disabled

            if (scrollHeight <= scrollTop + clientHeight + distance) {
              if (disabled) return
              func && func()
            }
          }, delay)
          targetEl.addEventListener('scroll', binding.handler)
        } else {
          binding.handler = helper.debounce(function () {
            const { scrollTop, scrollHeight, clientHeight } = el
            if (scrollHeight === scrollTop + clientHeight) {
              binding.value && binding.value()
            }
          }, 200)
          el.addEventListener('scroll', binding.handler)
        }
      },

      unbind(el, binding) {
        let { arg } = binding
        // 使用更丰富的功能，支持父组件的指令作用在指定的子组件上
        if (arg === 'expand') {
          /**
           * target 目标DOM节点的类名
           * offset 触发加载的距离阈值，单位为px
           * method 触发的方法
           * delay 防抖时延，单位为ms
           */
          const { target } = binding.value
          if (typeof target !== 'string') return
          let targetEl = el.querySelector(target)
          targetEl && targetEl.removeEventListener('scroll', binding.handler)
          targetEl = null
        } else {
          el.removeEventListener('scroll', binding.handler)
          el = null
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
@sidebar-width-collapsed: 64px;
@sidebar-width: 220px;
.containerWrap {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  /deep/.sysWrap {
    .sysBg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    .el-dialog {
      width: 618px;
      height: 412px;
      background-color: #fff;
    }
    .sysinfo {
      position: absolute;
      top: 0;
      left: 0;
      width: calc(100% - 64px);
      height: 100%;
      padding: 44px 32px 28px 32px;
      z-index: 10;
      .title {
        font-size: 16px;
        font-weight: bold;
        color: #37393c;
        line-height: 20px;
        text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.08);
      }
      & > img {
        width: 291px;
        height: 20px;
        margin-bottom: 20px;
        margin-top: 4px;
      }
      & > div {
        width: 100%;
        display: flex;
        justify-content: space-between;
      }
      .left {
        width: 60%;
        ul {
          li {
            margin-bottom: 8px;
            span:nth-child(1) {
              color: #62666c;
              margin-right: 32px;
            }
            span:nth-child(2) {
              color: #37393c;
              margin-right: 32px;
            }
          }
          li:nth-child(4) {
            margin-bottom: 20px;
          }
        }
        .dialog-footer {
          text-align: left;
          margin-top: 12px;
          span {
            margin-right: 4px;
          }
          .highBtnRe:nth-child(1) {
            color: #2677ff;
            border: 1px solid #2677ff;
          }
          .highBtnRe:nth-child(2) {
            color: #de3737;
            border: 1px solid #de3737;
          }
        }
      }
      .right {
        width: 40%;
        & > p {
          color: #62666c;
          line-height: 20px;
        }
        ul {
          li {
            width: 217px;
            // height: 72px;
            background: #f6f9ff;
            box-shadow:
              0px 2px 12px 0px rgba(11, 44, 99, 0.08),
              inset 0px 0px 36px 2px rgba(10, 36, 81, 0.09);
            border-radius: 4px;
            border: 2px solid;
            margin-bottom: 12px;
            border-image: linear-gradient(185deg, rgba(255, 255, 255, 1), rgba(236, 243, 255, 1)) 2
              2;
            .el-progress {
              position: relative;
              .el-progress__text {
                position: absolute;
                top: 10px;
                left: 0;
                right: 0;
                margin: auto auto;
                text-align: center;
                color: #62666c !important;
                font-size: 14px !important;
              }
            }
            .el-progress-bar {
              margin-right: 0;
              padding-right: 0;
              width: 100%;
              height: 36px;
              background: rgba(209, 213, 221, 0.52);
              border-radius: 0px 0px 2px 2px;

              .el-progress-bar__outer {
                border-radius: 0;
              }
              .el-progress-bar__outer {
                border-radius: 0;
                .el-progress-bar__inner {
                  border-radius: 0;
                  background: -webkit-linear-gradient(
                    270deg,
                    rgba(38, 119, 255, 0.4) 0%,
                    rgba(38, 119, 255, 0.1) 100%
                  );
                  background: -o-linear-gradient(
                    270deg,
                    rgba(38, 119, 255, 0.4) 0%,
                    rgba(38, 119, 255, 0.1) 100%
                  );
                  background: -moz-linear-gradient(
                    270deg,
                    rgba(38, 119, 255, 0.4) 0%,
                    rgba(38, 119, 255, 0.1) 100%
                  );
                  background: linear-gradient(
                    270deg,
                    rgba(38, 119, 255, 0.4) 0%,
                    rgba(38, 119, 255, 0.1) 100%
                  );
                  border-radius: 0px 0px 0px 2px;
                }
              }
            }
            span:nth-child(1) {
              display: block;
              height: 34px;
              line-height: 34px;
              color: #37393c;
              .iconfont {
                font-size: 16px;
                color: #2677ff;
                margin: 0 4px 0 12px;
              }
            }
          }
        }
      }
    }
  }
  .dialog-body {
    display: flex;
    justify-content: space-between;
    & > .el-form {
      width: 100%;
    }
    & > .el-divider--vertical {
      height: 156px;
      background: #e9ebef;
      margin-bottom: 20px;
    }
    & > span {
      display: inline-block;
      width: 48%;
      text-align: center;
      margin-top: 30px;
    }
    & > span > img {
      width: 54px;
      height: 54px;
      margin-bottom: 8px;
    }
    & > span p {
      color: #2677ff;
      font-weight: 400;
    }
    .left {
      height: 100%;
      margin-bottom: 24px;
      /deep/.el-textarea {
        width: 310px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
      /deep/.upload-demo {
        .el-upload-dragger {
          width: 310px;
        }
        .downloadText {
          margin-left: 5px;
          color: #4285f4;
          cursor: pointer;
        }
      }
      .dialog-footer {
        text-align: right;
      }
    }
    .right {
      width: 55%;
      margin-bottom: 24px;
      padding-left: 20px;
      border-left: 1px solid #e9ebef;
      .filterTab {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 0 16px 0;
        & > div {
          .el-input {
            width: 50%;
            .el-input__inner {
              border-color: #d1d5dd;
            }
          }
          & > span {
            font-weight: 400;
            color: #62666c;
            line-height: 20px;
          }
          & > b {
            color: #2677ff;
            margin: 0 16px 0 4px;
          }
          .normalBtnRe {
            margin-right: 0;
          }
        }
      }
      .tableWrap {
        height: 419px;
        .el-table {
          border: 0 !important;
        }
        .el-table--border .el-table__cell {
          border-right: 0 !important;
        }
        .el-table--border,
        .el-table--group {
          border: 0 !important;
        }
        .tableInfo {
          margin-top: 10px;
          text-align: center;
        }
      }
    }
  }
  ::v-deep .menuWrap {
    // position: absolute;
    width: 100%;
    height: 52px;
    line-height: 52px;
    font-size: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: -webkit-linear-gradient(270deg, #ffffff 0%, #f0f6ff 100%) !important;
    background: -o-linear-gradient(270deg, #ffffff 0%, #f0f6ff 100%) !important;
    background: -moz-linear-gradient(270deg, #ffffff 0%, #f0f6ff 100%) !important;
    background: linear-gradient(270deg, #ffffff 0%, #f0f6ff 100%) !important;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08) !important;
    z-index: 99;
    .left {
      display: flex;
      align-items: center;
      .title {
        display: flex;
        align-items: center;
        span {
          font-weight: 800;
          color: #37393c;
        }
        svg {
          font-size: 28px;
          margin: 0 12px 0 11px;
          vertical-align: middle;
        }
      }
    }
    .setter {
      display: flex;
      align-items: center;
      padding-right: 21px;
      color: #37393c;
      min-width: 460px;
      justify-content: flex-end;
      height: 100%;
      background-image: url('../assets/images/header.png');
      background-size: 100% 100%;

      .scrolling-text {
        margin-right: 20px;
        width: 207px;
        padding-right: 12px;
        padding-left: 12px;
        border-radius: 4px;
        height: 28px;
        line-height: 28px !important;
        font-size: 12px;
        color: #62666c;
        border: 1px solid #ffffff;
        background: linear-gradient(270deg, #faf6ec 0%, #fffdf7 100%);
        box-shadow:
          0px 2px 4px 0px rgba(0, 0, 0, 0.06),
          inset 0px 0px 10px 0px rgba(134, 96, 0, 0.06);
        .notice-img {
          // height: 14px;
          width: 16px;
          display: inline-block;
        }
        img {
          width: 16px !important;
          height: auto;
          vertical-align: 27%;
          margin-right: 4px;
        }
        .info {
          width: calc(100% - 22px);
          color: #62666c;
          // width: 100%;
          overflow: hidden;
          white-space: nowrap;
          height: 28px;
          display: inline-block;
          // padding-left: 100%;

          .text {
            height: 100%;
            display: inline-block;
            padding-left: 100%;
            animation: marquee 50s linear infinite; /* 调整动画时长和效果 */
            &:hover {
              animation-play-state: paused;
            }
          }
        }
        @keyframes marquee {
          0% {
            // transform: translateX(-100px);
            transform: translateX(0);
          }
          100% {
            transform: translateX(-100%);
          }
        }
      }

      .el-icon-s-tools {
        color: #c8d3e7;
        font-size: 14px;
        vertical-align: middle;
      }
      .el-divider {
        background-color: #535e76;
        margin: 0 16px;
      }
      svg {
        color: #37393c;
        font-size: 32px;
      }
      .el-dropdown {
        color: #37393c;
        margin: 0 0 0 8px;
        font-size: 14px;
        line-height: 20px;
      }
      .el-icon-arrow-down {
        color: #37393c;
        margin-left: 28px;
      }
      .el-icon-s-tools,
      .el-dropdown-link {
        cursor: pointer;
      }
      /deep/.el-select {
        .el-select__caret {
          color: #37393c !important;
        }
      }
      img {
        // margin-right: 8px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .main_content {
    width: 100%;
    // margin-top: 2px;
    height: calc(100% - 54px);
    // display: flex;
    // justify-content: space-between;
    transition: 0.5s;
    &.hideSidebar {
      .menucontent {
        width: @sidebar-width-collapsed !important;
      }
      .main_body {
        margin-left: @sidebar-width-collapsed !important;
      }

      .menuTitle {
        display: none;
      }
    }
    .menucontent {
      position: fixed;
      top: 52px;
      bottom: 0;
      width: @sidebar-width;
      height: calc(100% - 52px);
      padding: 0 0 0 0;
      background: #062c6b;
      // position: relative;
      .el-menu-vertical-demo {
        background-image: url('../assets/images/menu.png');
        background-position: bottom;
        background-size: 100%;
        background-repeat: no-repeat;
      }
      .picture {
        width: 100%;
        height: 30%;
        position: absolute;
        bottom: 0px;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .main_body {
      // width: calc(100% - 252px);
      margin-left: @sidebar-width;
      transition: margin-left width 0.3s;
      height: calc(100% - 72px);
      padding: 56px 16px 16px 16px;
      overflow: auto;
    }
    .main_body_specail {
      // width: 100%;
      height: 100%;
      padding: 0;
      overflow: auto;
    }
    .main_body_unit {
      // width: calc(100% - 220px);
      height: calc(100% - 56px);
      // padding-top: 56px;
      padding: 56px 0 0 0;
      overflow: auto;
    }
  }
  .resetPasswordTip {
    text-align: center;
    p {
      padding-top: 10px;
      text-align: center;
      .jump {
        color: #2677ff;
        cursor: pointer;
        margin-top: 10px;
        padding-left: 4px;
      }
    }
  }
}

/deep/.el-menu {
  border-right: 0;
  background: #062c6b;
  .iconfont {
    font-size: 18px;
    // color: #B0C4E6;
    margin-right: 15px;
  }
  b {
    margin-right: 20px;
  }
}
// /deep/.el-submenu.is-active .el-submenu__title .menuTitle {
//   color: #fff;
//   font-weight: 700 !important;
// }
/deep/.el-menu-vertical-demo {
  height: 100%;
  // padding-top: 12px;
  overflow-y: auto;
  overflow-x: hidden;
  .el-menu--inline {
    background-color: #0c2040 !important;
    span {
      // color: rgba(255, 255, 255, 0.7);
      font-weight: 500;
    }
  }
  .el-menu-item,
  .el-submenu__title {
    height: 48px !important;
    line-height: 48px !important;
  }
  .el-menu-item.is-active {
    // width: 100%;
    box-sizing: border-box;
    // background-color: #041E4B !important;
    color: #ffffff;
    // margin-left: 4px !important;
    // border-left: 4px solid transparent;
    i {
      color: rgba(255, 255, 255, 0.8);
      margin-left: -4px;
    }
    b {
      margin-left: -4px;
    }
  }
}
/deep/.el-submenu:hover,
/deep/.el-submenu__title:hover {
  background-color: #062c6b;
}
/deep/.el-submenu__title:hover > span,
/deep/.el-submenu__title:focus > span {
  color: #ffffff !important;
}
/deep/.el-menu-item:hover,
/deep/.el-menu-item:focus {
  background-color: #062c6b;
  span {
    color: #ffffff !important;
  }
}
/deep/.el-menu--inline {
  .el-menu-item:hover,
  .el-menu-item:focus {
    background-color: #041e4b !important;
    span {
      color: #ffffff;
    }
  }
  // &.
}
/deep/.el-menu-item {
  padding: 0 !important;
  &.is-opened > .el-submenu__title > span > span {
    color: #fff !important;
    font-weight: 700 !important;
  }
  &.is-opened > .el-submenu__title > span > i {
    color: #fff !important;
  }
  &.is-active > span > span,
  &.is-active > span > i {
    // color: #fff;
    font-weight: 700 !important;
  }
}
/deep/.el-menu-vertical .el-menu-item {
  &.is-active > span > span,
  &.is-active > span > i {
    color: rgba(255, 255, 255, 0.7);
  }
}
/deep/.el-menu-item > span {
  display: inline-block;
  padding: 0px 20px 0px 40px;
  width: 100%;
  box-sizing: border-box;
}
/deep/.el-submenu__title {
  padding: 0 !important;
}
/deep/.el-submenu__title > span {
  display: inline-block;
  padding: 0px 20px;
  width: 100%;
  box-sizing: border-box;
}

.tran {
  /deep/.el-input__inner {
    border: 0 !important;
    background: transparent !important;
    padding-right: 24px;
  }
}
.tuBox {
  display: inline-block;
  width: 30px;
  height: 30px;
  background-image: url('../assets/images/asset.png');
  background-repeat: no-repeat;
  background-size: 100% !important;
  background-position-y: 0;
  margin-right: 4px;
}
.tuBox1 {
  background-image: url('../assets/images/ascertain.png');
}
.tuBox2 {
  background-image: url('../assets/images/report.png');
}
.tuBox3 {
  background-image: url('../assets/images/audit.png');
}
.tuBox4 {
  background-image: url('../assets/images/system.png');
}
.tuBox5 {
  background-image: url('../assets/images/leak.png');
}
.tuBox7 {
  background-image: url('../assets/images/clueDes.png');
}
/deep/.is-active > span {
  margin-left: 4px !important;
}
/deep/.is-active > .myTitle {
  margin-left: 0px !important;
}
.portrait {
  width: 28px !important;
  height: 28px !important;
  margin-left: 8px;
}
.animationenter {
  animation: tuing 0.2s steps(9);
  animation-fill-mode: forwards;
}
.animationenter1 {
  animation: tuing1 0.4s steps(19);
  animation-fill-mode: forwards;
}
.animationchu {
  animation: istuing 0.2s steps(9);
  animation-fill-mode: forwards;
}
.animationchu1 {
  animation: istuing1 0.4s steps(19);
  animation-fill-mode: forwards;
}
@keyframes tuing {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -270px;
  }
}
@keyframes istuing {
  0% {
    background-position-y: -270px;
  }
  100% {
    background-position-y: 0px;
  }
}
@keyframes tuing1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -570px;
  }
}
@keyframes istuing1 {
  0% {
    background-position-y: -570px;
  }
  100% {
    background-position-y: 0px;
  }
}
.screenAssetsBody {
  height: 100%;
  width: 100%;
}
.el-icon-warning {
  margin-right: 8px;
}
.elDialogNewList {
  /deep/.el-dialog__body {
    min-height: 100px;
    height: 105px;
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
