<template>
  <div class="con">
    <el-table
      v-loading="loading"
      border
      :data="tableData"
      row-key="id"
      :reserve-selection="checkedAll"
      :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
      @selection-change="handleSelectionChange"
      ref="eltable"
      height="100%"
      style="width: 100%"
    >
      <template slot="empty">
        <div class="emptyClass">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-kong"></use>
          </svg>
          <p>暂无数据</p>
        </div>
      </template>
      <!-- && pageIcon == 'scan' -->
      <el-table-column
        v-if="
          $route.path == '/unitIndex' ||
          $route.path == '/assetsCloud' ||
          $route.path == '/groupAssets' ||
          $route.path == '/organization-assets'
        "
        type="selection"
        align="center"
        :reserve-selection="checkedAll"
        :show-overflow-tooltip="true"
        :selectable="handleSelectable"
        width="55"
      >
      </el-table-column>
      <el-table-column
        v-for="(item, itemIndex) in tableHeaderIsShow"
        :key="itemIndex"
        align="left"
        :prop="item.name"
        :label="item.label"
        :fixed="item.fixed"
        :min-width="item.minWidth"
      >
        <template slot-scope="scope">
          <!-- detailIs == 1 ，信息在detail外面 -->
          <div v-if="item.detailIs == 1" class="cell-other">
            <div
              v-if="item.name == 'clue_company_name'"
              style="padding-right: 12px !important; padding-left: 12px !important"
            >
              <el-tooltip
                v-if="getCompany(scope.row[item.name])"
                class="item"
                effect="dark"
                :content="String(scope.row[item.name])"
                placement="top"
                :open-delay="500"
              >
                <p class="detail">{{ getCompany(scope.row[item.name]) }}</p>
              </el-tooltip>
              <p v-else>-</p>
            </div>
            <div
              v-else-if="item.name == 'level_reason'"
              style="padding-right: 12px !important; padding-left: 12px !important"
            >
              <el-tooltip
                v-if="scope.row[item.name]"
                class="item"
                effect="dark"
                :content="String(scope.row[item.name])"
                placement="top"
                :open-delay="500"
              >
                <p class="detail">{{ scope.row[item.name] }}</p>
              </el-tooltip>
              <p v-else>-</p>
            </div>
            <div v-else-if="item.name == 'hosts'">
              <div v-if="scope.row[item.name] && scope.row[item.name].length != 0">
                <p
                  class="detail"
                  v-for="(nameItem, nameIndex) in getExpandData(scope.row[item.name], scope.$index)"
                  :key="nameIndex"
                  style="padding-right: 12px !important; padding-left: 12px !important"
                >
                  <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                    <span slot="content"
                      >{{ $punyCode.toUnicode(String(nameItem))
                      }}{{
                        scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                      }}</span
                    >
                    <span
                      >{{ $punyCode.toUnicode(nameItem)
                      }}<span
                        class="blueRadiusBorder"
                        v-if="
                          scope.row['host_reflect'] && scope.row['host_reflect'][nameItem] == true
                        "
                        >泛解析</span
                      ></span
                    >
                  </el-tooltip>
                </p>
              </div>
              <span v-else>-</span>
            </div>
            <div
              v-else-if="item.name == 'ip'"
              style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis"
            >
              <span
                v-if="scope.row[item.name]"
                style="padding-right: 12px !important; padding-left: 12px !important"
              >
                <span>
                  <!-- 折叠按钮：端口，域名，组件信息折叠 -->
                  <i
                    v-if="
                      ((scope.row.detail && scope.row.detail.length > 3) ||
                        (scope.row.rule_tags && scope.row.rule_tags.length > 3)) &&
                      !scope.row.isExpand
                    "
                    id="expandClass"
                    class="el-icon-plus"
                    @click="getExpand(scope.$index)"
                  ></i>
                  <i
                    v-if="
                      ((scope.row.detail && scope.row.detail.length > 3) ||
                        (scope.row.rule_tags && scope.row.rule_tags.length > 3)) &&
                      scope.row.isExpand
                    "
                    id="expandClass"
                    class="el-icon-minus"
                    @click="getExpands(scope.$index)"
                  ></i>
                </span>
                <span>
                  <!-- ip -->
                  <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                    <span v-if="scope.row[item.name]" slot="content">
                      <div v-if="pageIcon == 'scan'">
                        <i v-if="scope.row.online_state == 1" class="greenStatus"></i>
                        <i v-else class="grayStatus"></i>
                      </div>
                      <span>{{ scope.row[item.name] }}</span>
                    </span>
                    <span>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
                  </el-tooltip>
                  <span v-if="pageIcon == 'scan'">
                    <span class="greenLine" v-if="scope.row.online_state == 1">在线</span>
                    <span class="grayLine" v-else>离线</span>
                  </span>
                  <span v-if="scope.row['is_cdn']" class="originLine">CDN</span>
                  <el-tooltip effect="light" class="item" placement="top" content="">
                    <div slot="content"> 云厂商：{{ getCloudName(scope.row['cloud_name']) }} </div>
                    <span v-if="getCloudName(scope.row['cloud_name'])" class="originLine"
                      ><i class="el-icon-cloudy"></i
                    ></span>
                  </el-tooltip>
                  <span
                    class="blueLine"
                    v-if="
                      scope.row['chain_list'] &&
                      scope.row['chain_list'].length &&
                      scope.row['chain_list'].length > 0
                    "
                  >
                    <el-tooltip
                      placement="top"
                      :disabled="!scope.row['chain_list']"
                      class="item"
                      effect="light"
                      popper-class="chainClass"
                      :open-delay="500"
                    >
                      <div slot="content" style="position: relative">
                        <el-tooltip
                          effect="light"
                          class="item"
                          placement="top"
                          content="一键复制"
                          v-if="scope.row['chain_list'] && scope.row['chain_list'].length != 0"
                          :open-delay="500"
                        >
                          <i
                            class="el-icon-document-copy"
                            @click="copyClick(scope.row['chain_list'])"
                            style="
                              color: #2677ff;
                              cursor: pointer;
                              position: absolute;
                              right: -6px;
                              top: 0;
                            "
                          ></i>
                        </el-tooltip>
                        证据链:{{ scope.row['chain_list'].length }}条

                        <div v-for="(item, index) in scope.row['chain_list']" :key="index">
                          <span>{{ index + 1 }}、</span>
                          <span v-for="(v, i) in getChains(item)" :key="i">
                            <span v-if="v.type && v.type == 3">
                              <el-image
                                :src="
                                  v.content.includes('http') ? v.content : showSrcIp + v.content
                                "
                                alt=""
                              >
                                <div slot="error" class="image-slot">
                                  <i class="el-icon-picture-outline"></i>
                                </div>
                              </el-image>
                            </span>
                            <span v-else>{{ v.content }}</span>
                            <span
                              v-if="i < item.length - 1 && v"
                              class="el-icon-right iconRight"
                            ></span>
                          </span>
                        </div>
                      </div>
                      <span>
                        <img
                          src="../../assets/images/chain.svg"
                          alt=""
                          style="width: 12px; vertical-align: middle"
                        />
                      </span>
                    </el-tooltip>
                  </span>
                </span>
              </span>
              <span v-else style="padding-right: 12px !important; padding-left: 12px !important"
                >-</span
              >
            </div>
            <div v-else-if="item.name == 'rule_tags'" class="ruleItemBox">
              <span
                class="ruleItem"
                v-for="ch in getExpandData(scope.row[item.name], scope.$index)"
                :key="ch.rule_id"
              >
                <img
                  :src="showimg(ch.cn_product).result ? showimg(ch.cn_product).url : ''"
                  alt=""
                  class="productImg"
                />
                <el-tooltip effect="light" class="item" placement="top" :content="ch.cn_product">
                  <span>{{ ch.cn_product }}</span>
                </el-tooltip>
              </span>
              <el-popover
                placement="top"
                width="315"
                style="padding-right: 0px !important; padding-left: 0px !important"
                popper-class="rulePopover"
                trigger="click"
              >
                <div style="font-size: 12px">
                  <div style="cursor: pointer"
                    ><i :class="scope.row.online_state == 1 ? 'greenStatus' : 'grayStatus'"></i
                    >{{ scope.row['ip'] }}</div
                  >
                  <div class="myruleItemBox">
                    <span class="myruleItem" v-for="(v, i) in scope.row[item.name]" :key="i"
                      ><img
                        :src="showimg(v.cn_product).result ? showimg(v.cn_product).url : ''"
                        alt=""
                        class="productImg"
                      />{{ v.cn_product }}</span
                    >
                  </div>
                </div>
                <div
                  slot="reference"
                  v-if="
                    scope.row['rule_tags'] &&
                    scope.row['rule_tags'].length > 3 &&
                    scope.row.myPopoverFlag
                  "
                  class="ruleItemNum"
                  >共{{ scope.row['rule_tags'] ? scope.row['rule_tags'].length : '' }}条</div
                >
              </el-popover>
            </div>
            <div
              class="detail"
              style="padding-right: 12px !important; padding-left: 12px !important"
              v-else
            >
              <el-tooltip
                v-if="scope.row[item.name]"
                class="item"
                effect="dark"
                placement="top"
                :open-delay="500"
              >
                <span slot="content"
                  >{{ $punyCode.toUnicode(String(scope.row[item.name]))
                  }}{{
                    scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                  }}</span
                >
                <span>{{ $punyCode.toUnicode(scope.row[item.name]) }}</span>
              </el-tooltip>
              <span v-else>-</span>
            </div>
          </div>
          <div v-else-if="pageIcon == 'scan' || pageIcon == 'orgAssets'">
            <!-- style="padding-right: 12px !important;padding-left: 12px !important;" -->
            <div
              class="detail"
              :class="{
                detailThree:
                  $route.path != '/scanReg' &&
                  (item.name == 'title' ||
                    item.name == 'url' ||
                    item.name == 'hosts' ||
                    item.name == 'assets_source' ||
                    item.name == 'domain' ||
                    item.name == 'subdomain' ||
                    item.name == 'assets_source')
              }"
              v-for="(detailItem, detailindex) in getExpandData(
                scope.row.detail,
                scope.$index,
                scope.row.host_list
              )"
              :key="detailindex"
              :style="{
                height: `${!detailItem.hostList || detailItem.hostList.length <= 1 ? 40 : detailItem.hostList.length * 40}px`
              }"
              style="padding-right: 12px; padding-left: 12px; box-sizing: border-box"
            >
              <div v-if="item.name == 'url'">
                <div v-if="detailItem.hostList && detailItem.hostList.length == 0">
                  <el-tooltip :open-delay="500" effect="dark" placement="top">
                    <span slot="content">{{
                      $punyCode.toUnicode(
                        String(detailItem[item.name]) ? String(detailItem[item.name]) : ''
                      )
                    }}</span>
                    <div class="portBox">
                      <a
                        class="ellipsis"
                        v-if="
                          detailItem[item.name] && String(detailItem[item.name]).includes('http')
                        "
                        style="color: #409eff"
                        :href="detailItem[item.name]"
                        target="_blank"
                      >
                        {{
                          $punyCode.toUnicode(
                            String(detailItem[item.name]) ? String(detailItem[item.name]) : '-'
                          )
                        }}
                      </a>
                      <span class="ellipsis" v-else>{{
                        detailItem[item.name]
                          ? $punyCode.toUnicode(String(detailItem[item.name]))
                          : '-'
                      }}</span>
                    </div>
                  </el-tooltip>
                </div>
                <div v-else>
                  <div
                    v-for="(itemDetailOne, i) in detailItem.hostList"
                    :key="i"
                    class="detailThreeItem"
                  >
                    <el-tooltip :open-delay="500" effect="dark" placement="top">
                      <span slot="content">{{
                        $punyCode.toUnicode(
                          String(itemDetailOne[item.name]) ? String(itemDetailOne[item.name]) : ''
                        )
                      }}</span>
                      <div class="portBox">
                        <a
                          class="ellipsis"
                          v-if="
                            itemDetailOne[item.name] &&
                            String(itemDetailOne[item.name]).includes('http')
                          "
                          style="color: #409eff"
                          :href="itemDetailOne[item.name]"
                          target="_blank"
                        >
                          {{
                            $punyCode.toUnicode(
                              String(itemDetailOne[item.name])
                                ? String(itemDetailOne[item.name])
                                : '-'
                            )
                          }}
                        </a>
                        <span class="ellipsis" v-else>{{
                          itemDetailOne[item.name]
                            ? $punyCode.toUnicode(String(itemDetailOne[item.name]))
                            : '-'
                        }}</span>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <!-- <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
            <span slot="content">{{$punyCode.toUnicode(getTableItem(detailItem[item.name]))}}</span>
            <p>{{$punyCode.toUnicode(getTableIem(detailItem[item.name]))}}</p>
          </el-tooltip> -->
              <div
                v-else-if="item.name == 'title'"
                class="clickPointer"
                @click="noteBlackList(scope.row.title)"
              >
                <div
                  v-if="detailItem.hostList && detailItem.hostList.length == 0"
                  class="detailThreeItem"
                >
                <el-image
                v-if="detailItem['logo'] && detailItem['logo']['content']"
                :src="
                  detailItem['logo']['content'].includes('http')
                    ? detailItem['logo']['content']
                    : showSrcIp + detailItem['logo']['content']
                "
                style="align-content: center;"
                lazy
              >
                <div slot="error">
                  <i class="el-icon-picture-outline" style="font-size: 20px"></i>
                </div>
              </el-image>
              <el-tooltip
                :open-delay="500"
                effect="dark"
                :content="detailItem[item.name] ? String(detailItem[item.name]) : '-'"
                placement="top"
              >
                <p style="max-width: 77%; margin-left: 3px;" v-if="detailItem[item.name]">
                  {{ detailItem[item.name] }}
                </p>
                <span v-else>{{ '-' }}</span>
              </el-tooltip>
                </div>
                <div v-else>
                  <div
                    v-for="(itemDetailOne, i) in detailItem.hostList"
                    :key="i"
                    class="detailThreeItem"
                  >
                  <el-image
                  v-if="itemDetailOne['logo'] && itemDetailOne['logo']['content']"
                  :src="
                    itemDetailOne['logo']['content'].includes('http')
                      ? imgUrl + itemDetailOne['logo']['content']
                      : showSrcIp + itemDetailOne['logo']['content']
                  "
                  lazy
                >
                  <div slot="error">
                    <i class="el-icon-picture-outline" style="font-size: 20px"></i>
                  </div>
                </el-image>
                <el-tooltip
                  :open-delay="500"
                  effect="dark"
                  :content="itemDetailOne[item.name] ? String(itemDetailOne[item.name]) : '-'"
                  placement="top"
                >
                  <p style="max-width: 77%; margin-left: 3px;" v-if="itemDetailOne[item.name]">
                    {{ itemDetailOne[item.name] }}
                  </p>
                  <span v-else>{{ '-' }}</span>
                </el-tooltip>
                  </div>
                </div>
              </div>
              <div v-else-if="item.name == 'domain' || item.name == 'subdomain'">
                <div
                  v-if="detailItem.hostList && detailItem.hostList.length == 0"
                  class="detailThreeItem"
                >
                  <el-tooltip effect="dark" placement="top" :open-delay="500">
                    <span slot="content">{{
                      $punyCode.toUnicode(getTableItem(detailItem[item.name]))
                    }}</span>
                    <div class="portBox">
                      <p class="ellipsis">{{
                        $punyCode.toUnicode(getTableItem(detailItem[item.name]))
                      }}</p>
                    </div>
                  </el-tooltip>
                </div>
                <div v-else>
                  <div
                    v-for="(itemDetailOne, i) in detailItem.hostList"
                    :key="i"
                    class="detailThreeItem"
                  >
                    <el-tooltip effect="dark" placement="top" :open-delay="500">
                      <span slot="content">{{
                        $punyCode.toUnicode(getTableItem(itemDetailOne[item.name]))
                      }}</span>
                      <div class="portBox"
                        ><p class="ellipsis">{{
                          $punyCode.toUnicode(getTableItem(itemDetailOne[item.name]))
                        }}</p></div
                      >
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <p v-else-if="item.name == 'logo'" class="portBox">
                <el-image
                  v-if="detailItem[item.name] && detailItem[item.name].content"
                  :src="
                    String(detailItem[item.name].content).includes('http')
                      ? detailItem[item.name].content
                      : showSrcIp + detailItem[item.name].content
                  "
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <span v-else>-</span>
              </p>
              <div v-else-if="item.name == 'port'" class="portBox">
                <span>
                  <i
                    v-if="
                      detailItem.hostList &&
                      detailItem.hostListLength > 3 &&
                      !detailItem.isURLExpand
                    "
                    id="expandClass"
                    class="el-icon-plus"
                    @click="getExpandUrl(scope.$index, detailindex, true)"
                  ></i>
                  <i
                    v-if="
                      detailItem.hostList && detailItem.hostListLength > 3 && detailItem.isURLExpand
                    "
                    id="expandClass"
                    class="el-icon-minus"
                    @click="getExpandUrl(scope.$index, detailindex, false)"
                  ></i>
                </span>
                <span>{{ getTableItem(detailItem[item.name]) }}</span>
              </div>
              <div
                v-else-if="item.name == 'protocol' || item.name == 'icp' || item.name == 'cert'"
                class="portBox"
              >
                <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                  <span slot="content">{{
                    $punyCode.toUnicode(getTableItem(detailItem[item.name]))
                  }}</span>
                  <p>{{ $punyCode.toUnicode(getTableItem(detailItem[item.name])) }}</p>
                </el-tooltip>
              </div>
              <!-- 安服显示数据来源 -->
              <div v-else-if="item.name == 'assets_source'">
                <div
                  v-if="detailItem.hostList && detailItem.hostList.length == 0"
                  class="detailThreeItem"
                >
                  <el-tooltip
                    v-if="detailItem['assets_source']"
                    :open-delay="500"
                    class="item"
                    effect="dark"
                    placement="top"
                  >
                    <span slot="content">
                      {{ getAssetsSource[detailItem.assets_source]
                      }}{{
                        detailItem['source_updated_at']
                          ? `（发现时间：${detailItem['source_updated_at']}）`
                          : ''
                      }}
                    </span>
                    <p>{{ getAssetsSource[detailItem.assets_source] }}</p>
                  </el-tooltip>
                  <span v-else>-</span>
                </div>
                <div v-else>
                  <div
                    v-for="(itemDetailOne, i) in detailItem.hostList"
                    :key="i"
                    class="detailThreeItem"
                  >
                    <el-tooltip
                      v-if="itemDetailOne['assets_source']"
                      :open-delay="500"
                      class="item"
                      effect="dark"
                      placement="top"
                    >
                      <span slot="content">
                        {{ getAssetsSource[itemDetailOne.assets_source]
                        }}{{
                          itemDetailOne['source_updated_at']
                            ? `（发现时间：${itemDetailOne['source_updated_at']}）`
                            : ''
                        }}
                      </span>
                      <p>{{ getAssetsSource[itemDetailOne.assets_source] }}</p>
                    </el-tooltip>
                    <span v-else>-</span>
                  </div>
                </div>
              </div>
              <el-tooltip
                class="item"
                v-else-if="item.name != 'url'"
                effect="dark"
                placement="top"
                :open-delay="500"
              >
                <span slot="content">
                  {{ $punyCode.toUnicode(getTableItem(detailItem[item.name])) }}
                  <!-- 域名需要显示punycode_domain编码字段 -->
                  <span v-if="item.name == 'domain' || item.name == 'subdomain'">{{
                    scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                  }}</span>
                </span>
                <p>{{ $punyCode.toUnicode(getTableItem(detailItem[item.name])) }}</p>
              </el-tooltip>
            </div>
          </div>
          <div v-else>
            <div
              class="detail"
              v-for="(detailItem, detailindex) in getExpandData(scope.row.detail, scope.$index)"
              :key="detailindex"
              style="padding-right: 12px !important; padding-left: 12px !important"
            >
              <el-tooltip
                class="item"
                v-if="item.name == 'url'"
                effect="dark"
                placement="top"
                :open-delay="500"
              >
                <span slot="content">{{
                  $punyCode.toUnicode(
                    String(detailItem[item.name]) ? String(detailItem[item.name]) : ''
                  )
                }}</span>
                <a
                  v-if="detailItem[item.name] && String(detailItem[item.name]).includes('http')"
                  style="color: #409eff"
                  :href="detailItem[item.name]"
                  target="_blank"
                >
                  {{
                    $punyCode.toUnicode(
                      String(detailItem[item.name]) ? String(detailItem[item.name]) : '-'
                    )
                  }}
                </a>
                <span v-else>{{
                  detailItem[item.name] ? $punyCode.toUnicode(String(detailItem[item.name])) : '-'
                }}</span>
              </el-tooltip>
              <p v-else-if="item.name == 'logo'">
                <el-image
                  v-if="detailItem[item.name] && detailItem[item.name].content"
                  :src="
                    String(detailItem[item.name].content).includes('http')
                      ? detailItem[item.name].content
                      : showSrcIp + detailItem[item.name].content
                  "
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <span v-else>-</span>
              </p>
              <div v-else-if="item.name == 'port'">
                <div class="portBox">
                  <span>{{ getTableItem(detailItem[item.name]) }}</span>
                </div>
              </div>
              <!-- 安服显示数据来源 -->
              <div v-else-if="item.name == 'assets_source'">
                <el-tooltip
                  v-if="detailItem['assets_source']"
                  :open-delay="500"
                  class="item"
                  effect="dark"
                  placement="top"
                >
                  <span slot="content">
                    {{ getAssetsSource[detailItem.assets_source]
                    }}{{
                      detailItem['source_updated_at']
                        ? `（发现时间：${detailItem['source_updated_at']}）`
                        : ''
                    }}
                  </span>
                  <p>{{ getAssetsSource[detailItem.assets_source] }} </p>
                </el-tooltip>
                <span v-else>-</span>
              </div>
              <el-tooltip class="item" v-else effect="dark" placement="top" :open-delay="500">
                <span slot="content">
                  {{ $punyCode.toUnicode(getTableItem(detailItem[item.name])) }}
                  <!-- 域名需要显示punycode_domain编码字段 -->
                  <span v-if="item.name == 'domain' || item.name == 'subdomain'">{{
                    scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                  }}</span>
                </span>
                <p>{{ $punyCode.toUnicode(getTableItem(detailItem[item.name])) }}</p>
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- v-if="$route.path == '/unitIndex' && pageIcon == 'scan'" -->
      <el-table-column
        v-if="pageIcon != 'orgAssets'"
        fixed="right"
        label="操作"
        align="left"
        :min-width="pageIcon == 'third' ? 90 : 200"
      >
        <template slot-scope="scope">
          <div style="padding: 0 10px">
            <!-- 入账完成后有此操作：1 标记为认领资产 2/标记为忽略资产 3/标记为威胁资产  0/标记未知资产 -->
            <span v-if="pageIcon == 'scan'">
              <el-button
                v-if="pageTab == 0 || pageTab == 1"
                type="text"
                size="small"
                @click.native="goFenlei(3, scope.row)"
                id="unit_scan_wx"
                >标记威胁</el-button
              >
              <el-button
                v-if="pageTab == 0 || pageTab == 3"
                type="text"
                size="small"
                @click.native="goFenlei(1, scope.row)"
                id="unit_scan_rz"
                >入台账</el-button
              >
              <el-button
                v-if="pageTab == 1 || pageTab == 3"
                type="text"
                size="small"
                @click.native="goFenlei(0, scope.row)"
                id="unit_scan_ys"
                >标记疑似</el-button
              >
            </span>
            <!-- 评估列表有此操作 -->
            <el-dropdown v-if="pageIcon == 'predict'">
              <span class="el-dropdown-link" style="color: #2677ff">
                调整等级<i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="goChangeLevel(scope.row.ip, '1')"
                  >高可信度</el-dropdown-item
                >
                <el-dropdown-item @click.native="goChangeLevel(scope.row.ip, '2')"
                  >中可信度</el-dropdown-item
                >
                <el-dropdown-item @click.native="goChangeLevel(scope.row.ip, '3')"
                  >低可信度</el-dropdown-item
                >
                <!-- <el-dropdown-item @click.native="goChangeLevel(scope.row.ip, '4')">D级</el-dropdown-item> -->
              </el-dropdown-menu>
            </el-dropdown>
            <!-- 入账扫描的删除 -->
            <el-button
              class="removeClass"
              v-if="pageIcon == 'scan'"
              type="text"
              size="small"
              @click.native="goDeleteScan(scope.row.id)"
              id="unit_scan_del"
              >删除</el-button
            >
            <!-- 评估列表的删除 -->
            <el-button
              class="removeClass"
              v-if="pageIcon == 'third' || pageIcon == 'predict'"
              type="text"
              size="small"
              @click.native="goDelete(scope.row.ip)"
              id="unit_scan_del"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <noteBlack
      :selectTitle="currentTitle"
      :visible="noteDialogVisible"
      ref=""
      @close="noteDialogVisible = false"
    />
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import noteBlack from '@/components/assets/noteBlack.vue'
import { delScanList, updateLevelListV1 } from '@/api/apiConfig/api.js'
import { delCloudAssetsList, cloudAssetsFenlei } from '@/api/apiConfig/surveying.js'
import { assetsSourceList } from '@/utils/commonData.js'

export default {
  components: { noteBlack },
  props: [
    'orgTaskId',
    'tableData',
    'pageIcon',
    'pageTab',
    'checkedAll',
    'expend_id',
    'levelIcon',
    'isClearSelection'
  ],
  data() {
    return {
      currentTitle: '',
      noteDialogVisible: false,
      labeledValueList: [
        // 钓鱼仿冒、ICP盗用、黄赌毒网站、其他类型
        {
          name: '其他类型',
          value: '0'
        },
        {
          name: '钓鱼仿冒',
          value: '1'
        },
        {
          name: '黄赌毒网站',
          value: '2'
        },
        {
          name: 'ICP盗用',
          value: '3'
        },
        {
          name: '域名混淆',
          value: '4'
        }
      ],
      loading: false,
      checkedArr: [],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      tableHeader: [
        {
          label: 'IP地址',
          name: 'ip',
          icon: 'input',
          minWidth: '210',
          detailIs: 1,
          fixed: 'left',
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          icon: 'input',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: '评级原因',
          name: 'level_reason',
          icon: 'input',
          minWidth: '120',
          detailIs: 1,
          path: ['/groupAssets', '/unitIndex', '/assetsCloud', 'predict']
        },
        {
          label: '端口',
          name: 'port',
          icon: 'select',
          minWidth: '70',
          detailIs: 2,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: '协议',
          name: 'protocol',
          icon: 'select',
          minWidth: '70',
          detailIs: 2,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: 'URL',
          name: 'url',
          icon: 'select',
          minWidth: '120',
          detailIs: 2,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: '网站标题',
          name: 'title',
          icon: 'select',
          minWidth: '120',
          detailIs: 2,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: '根域',
          name: 'domain',
          icon: 'select',
          minWidth: '120',
          detailIs: 2,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: '子域名',
          name: 'subdomain',
          icon: 'select',
          minWidth: '120',
          detailIs: 2,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: '数据来源',
          name: 'assets_source',
          minWidth: '120',
          detailIs: 2,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'predict',
            'scan'
          ]
        },
        {
          label: '组件信息',
          name: 'rule_tags',
          minWidth: '150',
          detailIs: 1,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            'scan'
          ]
        },
        {
          label: 'ICP',
          name: 'icp',
          minWidth: '120',
          detailIs: 2,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: '证书',
          name: 'cert',
          minWidth: '120',
          detailIs: 2,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: 'ICON',
          name: 'logo',
          minWidth: '80',
          detailIs: 2,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            // '/unitIndex',
            '/assetsCloud',
            'third',
            'scan',
            'predict'
          ]
        },
        {
          label: '首次发现资产时间',
          name: 'source_updated_at',
          minWidth: '120',
          detailIs: 2,
          path: ['/organization', 'orgAssets', '/groupAssets', '/unitIndex', 'third', 'predict']
        },

        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: '120',
          detailIs: 2,
          path: ['/organization', 'orgAssets', '/groupAssets', '/unitIndex', 'third', 'predict']
        },
        {
          label: '首次发现至企业资产时间',
          name: 'created_at',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            'scan'
          ]
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/organization',
            '/organization-assets',
            'orgAssets',
            '/groupAssets',
            '/unitIndex',
            'scan'
          ]
        }
        // {
        // 	label: '证据链',
        // 	name: 'chain_list',
        // 	minWidth: '80',
        // 	detailIs: 1,
        // 	path: ['/unitIndex', '/assetsCloud', 'third', 'scan', 'predict']
        // },
      ],
      user: {
        role: ''
      }
    }
  },
  watch: {
    tableData(val) {
      if (!this.isClearSelection) return
      this.$nextTick(() => {
        this.$refs.eltable.clearSelection()
        this.doLayout(this, 'eltable')
      })
    },
    checkedAll(val) {
      this.checkAllFunc()
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange', 'recommentFlag']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange']),
    tableHeaderIsShow(path) {
      let arr = []
      if (this.user.role == 1 || this.user.role == 2) {
        arr = this.tableHeader.filter((item) => {
          return item.path.indexOf(this.$route.path) != -1 && item.path.indexOf(this.pageIcon) != -1
        })
      } else {
        // 普通企业去掉assets_source
        arr = this.tableHeader
          .filter((item) => {
            return (
              item.path.indexOf(this.$route.path) != -1 && item.path.indexOf(this.pageIcon) != -1
            )
          })
          .filter((item) => {
            return item.name != 'assets_source'
          })
      }
      if (arr && arr.length > 0) {
        arr[0].fixed = 'left'
      }
      return arr
    },
    getAssetsSource() {
      let data = {}
      assetsSourceList.forEach((v) => {
        if (v.value != '-') {
          data[v.value] = v.label
        }
      })
      return data
    }
  },
  methods: {
    noteBlackList(title) {
      this.noteDialogVisible = true
      this.currentTitle = title
    },
    // 单位资产测绘分类
    async goFenlei(icon, row) {
      this.loading = true
      let obj = {}
      if (this.$route.path == '/groupAssets') {
        obj = {
          id: [row.id],
          set_status: icon, //1 标记台账 2/标记为忽略资产 3/标记为威胁资产  0/标记未知资产
          operate_company_id: this.currentCompany,
          organization_discover_task_id: this.orgTaskId
        }
      } else {
        obj = {
          id: [row.id],
          expend_id: this.expend_id ? this.expend_id.id : '',
          set_status: icon, //1 标记台账 2/标记为忽略资产 3/标记为威胁资产  0/标记未知资产
          operate_company_id: this.currentCompany
        }
      }

      let res = await cloudAssetsFenlei(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.$message.success('操作成功！')
        this.$emit('getList', 'yesLoading')
      }
    },
    showimg(url) {
      let imgUrl = ''
      url = url.replace('/', '')
      url = url.replace('.', '')
      url = url.replace(' ', '')
      let result = false
      url = './' + url + '.png'
      let urlTmp = url
      urlTmp = urlTmp.replace('./', '').replace('.png', '')
      // 目录下所有文件名
      const files = require.context('../../assets/images/componentIcon/', false, /\.png$/)
      const filedata = files.keys()
      filedata.forEach((item) => {
        if (item === url) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp + '.png')
        } else if (item === url.toLowerCase()) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp.toLowerCase() + '.png')
        }
      })
      // 返回结果
      return {
        result,
        url: imgUrl
      }
    },
    copyClick(data) {
      let text = ''
      data.map((item) => {
        this.getChains(item).map((v, i) => {
          if (v.type && v.type == 3) {
            if (i == item.length - 1 && v) {
              text += 'icon' + '\n'
            } else {
              text += 'icon' + '-->'
            }
          } else {
            if (i < item.length - 1 && v) {
              text += v.content + '-->'
            } else if (i == item.length - 1 && v) {
              text += v.content + '\n'
            } else {
              text += v.content
            }
          }
        })
      })
      this.$copyText(text).then(
        (res) => {
          console.log('复制成功：', res)
          this.$message.success('复制成功')
        },
        (err) => {
          console.log('', err)
          this.$message.error('复制失败')
        }
      )
    },
    getExpandData(data, index, hostList) {
      let arr = []
      if (Boolean(data && hostList)) {
        for (let j = 0; j < data.length; j++) {
          data[j].hostList = []
          for (let i = 0; i < hostList.length; i++) {
            if (data[j].port == hostList[i].port) {
              // details平级的host_list中存在与detail中的port的相同项
              data[j].hostList.push(hostList[i])
            }
          }
        }
        for (let j = 0; j < data.length; j++) {
          data[j].hostListLength = data[j].hostList.length
          if (!data[j].isURLExpand && data[j].hostListLength > 3) {
            // 收齐
            data[j].hostList =
              data[j].hostList && data[j].hostList.length > 0 ? data[j].hostList.slice(0, 3) : []
          } else {
            // 展开
            data[j].hostList = data[j].hostList
          }
        }
      }
      if (!this.tableData[index].isExpand) {
        arr = data && data.length > 0 ? data.slice(0, 3) : []
      } else {
        arr = data
      }
      return arr
    },
    getExpandUrl(index, detailIndex, isURLExpand) {
      this.tableData[index].detail[detailIndex].isURLExpand = isURLExpand
      this.doLayout(this, 'eltable')
    },
    // 展开
    getExpand(index) {
      this.tableData[index].isExpand = true
      this.tableData[index].myPopoverFlag = false
      this.doLayout(this, 'eltable')
    },
    // 收缩
    getExpands(index) {
      this.tableData[index].isExpand = false
      this.tableData[index].myPopoverFlag = true
      this.doLayout(this, 'eltable')
    },
    // 调整等级
    async goChangeLevel(ip, val) {
      let obj = {
        flag: this.recommentFlag,
        query: {
          set_assets_confidence_level: val,
          ip_array: [ip],
          page: this.currentPage,
          per_page: this.pageSize,
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      }
      let res = await updateLevelListV1(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.$emit('getLevelList', 'yesLoading')
      }
    },
    // 推荐/评估删除
    goDelete(ip) {
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'account_del_cancel',
        confirmButtonClass: 'account_del_sure',
        customClass: 'account_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            flag: this.recommentFlag,
            data: {
              assets_confidence_level: this.levelIcon,
              // level: this.levelIcon,
              ip_array: [ip],
              whole: '',
              operate_company_id: this.currentCompany
            }
          }
          this.loading = true
          let res = await delScanList(obj).catch(() => {
            this.loading = false
          })
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.loading = false
            if (this.pageIcon == 'third') {
              // 云端推荐结束列表
              this.$emit('getcloudAssets', 'yesLoading')
            } else {
              // 评估后的列表
              this.$emit('getLevelList', 'yesLoading')
            }
          }
        })
        .catch(() => {})
    },
    // 入账扫描删除
    goDeleteScan(id) {
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'account_del_cancel',
        confirmButtonClass: 'account_del_sure',
        customClass: 'account_del',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        let obj = {}
        if (this.$route.path == '/groupAssets') {
          obj = {
            id: [id],
            status: this.pageTab,
            operate_company_id: this.currentCompany,
            organization_discover_task_id: this.orgTaskId
          }
        } else {
          obj = {
            id: [id],
            expend_id: this.expend_id ? this.expend_id.id : '',
            status: this.pageTab,
            operate_company_id: this.currentCompany
          }
        }

        let res = await delCloudAssetsList(obj).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          setTimeout(() => {
            this.loading = false
            this.$message.success('删除成功！')
            this.$emit('getList', 'yesLoading')
          }, 3000)
        }
      })
    },
    getChains(row) {
      let arr = row.filter((item) => {
        return item.content
      })
      return arr
    },
    // 全选方法
    checkAllFunc() {
      console.log(this.checkedAll)
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        console.log(this.$refs.eltable)
        this.$refs.eltable.clearSelection()
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectionChange(val) {
      console.log(val)
      this.checkedArr = val
      this.$emit('handleSelectionChange', val)
    },
    handleSelectable() {
      return !this.checkedAll
    },
    getTableGeo(item, data) {
      if (item) {
        if (item[data]) {
          return item[data]
        } else {
          return '-'
        }
      } else {
        return '-'
      }
    },
    getTableItem(item) {
      if (Array.isArray(item)) {
        if (item.length == 0) {
          return '-'
        }
      }
      if (item) {
        if (Array.isArray(item)) {
          return item.join(',')
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    getReason(item) {
      // 推荐理由默认展示两个，鼠标移入显示更多
      if (item) {
        let str = item.split(/;|；|\s+/).splice(0, 2)
        if (str.length > 2) {
          return str.join(';') + '…'
        } else {
          return str.join(';')
        }
      } else {
        return '-'
      }
    },
    // getAssetsSource(name) {
    //   let source = ''
    //   if (name == 1) {
    //     source = 'FOFA的search/all接口返回'
    //   } else if (name == 2) {
    //     source = 'IP138'
    //   } else if (name == 3) {
    //     source = 'chaziyu'
    //   } else if (name == 4) {
    //     source = 'hunter'
    //   } else if (name == 5) {
    //     source = '线索库域名实时解析'
    //   } else if (name == 7) {
    //     source = '非线索库的域名实时解析'
    //   } else if (name == 8) {
    //     source = '基础扫描'
    //   } else if (name == 6) {
    //     source = 'FOFA纯解析域名数据'
    //   } else if (name == 9) {
    //     source = '单位测绘任务下的全端口扫描'
    //   } else if (name == 10) {
    //     source = '单位测绘下的常用端口扫描'
    //   } else if (name == 11) {
    //     source = 'oneforall'
    //   } else {
    //     source = '-'
    //   }
    //   return source
    // },
    getCompany(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length == '0') {
            return ''
          } else {
            return item.join(',')
          }
        } else {
          return String(item)
        }
      } else {
        return ''
      }
    },
    getCloudName(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length == '0') {
            return ''
          } else {
            return item.join(',')
          }
        } else {
          // 字符串的数组需要单独处理
          if (Array.isArray(JSON.parse(JSON.stringify(item)))) {
            return JSON.parse(JSON.stringify(item)).join(',')
          } else {
            return String(item)
          }
        }
      } else {
        return ''
      }
    }
  }
}
</script>
<style lang="less" scoped>
.con {
  width: 100%;
  height: 100%;
}

#expandClass {
  background: rgba(245, 247, 250, 0);
  border-radius: 2px;
  border: 1px solid #acb4c0;
  color: #62666c;
  z-index: 3;
  cursor: pointer;
  font-size: 8px;
}
/deep/.el-table th.el-table__cell::after {
  display: none;
}
/deep/.el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: 1px solid #e1e5ed !important;
}
/deep/.el-table th.el-table__cell::after {
  display: none;
}
.productImg {
  max-height: 16px;
  max-width: 16px;
  margin-right: 4px;
  vertical-align: middle;
}
.el-table {
  /deep/.removeClass {
    margin-left: 16px;
  }
  .detail {
    padding: 0 12px;
    height: 40px;
    line-height: 39px;
    box-sizing: border-box;
    border-bottom: 1px solid #ebeef5;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    p {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .hostDetail {
      max-width: 80px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  /deep/.el-table__body td.el-table__cell {
    padding: 0 !important;
    .cell {
      padding: 0 !important;
    }
  }
  .detail:last-child {
    border-bottom: 0;
  }
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.detailThree {
  padding: 0 !important;
  .detailThreeItem {
    box-sizing: border-box;
    display: flex;
    width: 100%;
    padding: 0 12px 0 8px;
    border-bottom: 1px solid #e1e5ed;
    &:last-child {
      border-bottom: 0;
      box-sizing: border-box;
    }
  }
}
.portBox {
  height: 100% !important;
  display: flex;
  align-items: center;
}
.el-table .detail div div {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.emptyClass {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
  vertical-align: middle;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
