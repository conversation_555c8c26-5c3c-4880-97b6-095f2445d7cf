<template>
  <el-drawer
    title="高级筛选"
    :visible.sync="highIsShow"
    direction="rtl"
    ref="drawer"
    @close="highIsClose"
  >
    <div class="demo-drawer__content">
      <el-form :model="formInline" ref="drawerForm" label-width="84px">
        <el-form-item
          v-for="(item, i0) in tableHeaderIsShow"
          :key="i0"
          :label="item.label"
          :prop="item.name"
        >
          <span slot="label">
            <span>{{ item.label }}：</span>
          </span>
          <el-input
            v-if="item.icon == 'input'"
            v-model="formInline[item.name]"
            placeholder="请输入"
          ></el-input>
          <el-select
            v-else-if="item.icon == 'select' && item.name == 'reason'"
            clearable
            filterable
            v-model="formInline[item.name]"
          >
            <el-option
              v-for="(item1, i1) in reasonData"
              :key="i1 + '111'"
              :label="item1.name"
              :value="item1.value"
            ></el-option>
          </el-select>
          <el-select
            v-else-if="item.icon == 'select' && item.name == 'chain_type'"
            clearable
            filterable
            v-model="formInline[item.name]"
          >
            <el-option
              v-for="(item2, i2) in reasonDataFake"
              :key="i2 + '222'"
              :label="item2.name"
              :value="item2.value"
            ></el-option>
          </el-select>
          <el-select
            v-else-if="item.icon == 'select' && item.name == 'assets_confidence_level'"
            clearable
            filterable
            v-model="formInline[item.name]"
          >
            <el-option
              v-for="(item3, i3) in levelData"
              :key="i3 + '333'"
              :label="item3.name"
              :value="item3.value"
            ></el-option>
          </el-select>
          <div v-else-if="item.icon == 'select' && item.name == 'score_type'">
            <el-select clearable filterable v-model="formInline[item.name]">
              <template>
                <el-option
                  v-for="(item4, i4) in scoreList"
                  :key="i4 + '444'"
                  :label="item4.name"
                  :value="item4.value"
                ></el-option>
              </template>
            </el-select>
            <el-input
              v-if="formInline[item.name] == 4"
              style="margin-top: 20px"
              v-model="formInline.score"
              placeholder="请输入"
            ></el-input>
          </div>
          <div v-else-if="item.icon == 'oneSelect' && item.name == 'online_state'">
            <el-select clearable filterable collapse-tags v-model="formInline[item.name]">
              <template>
                <el-option
                  v-for="(item5, i5) in stateData"
                  :key="i5 + '555'"
                  :label="item5.name"
                  :value="item5.value"
                ></el-option>
              </template>
            </el-select>
            <el-input
              v-if="formInline[item.name] == 4"
              style="margin-top: 20px"
              v-model="formInline.score"
              placeholder="请输入"
            ></el-input>
          </div>
          <el-select
            filterable
            v-else-if="item.icon == 'select'"
            v-model="formInline[item.name]"
            clearable
            multiple
            collapse-tags
            placeholder="请选择"
            :reserve-keyword="true"
          >
            <template v-if="item.name == 'online_state'">
              <el-option
                v-for="(item6, i6) in stateData"
                :key="i6 + '666'"
                :label="item6.name"
                :value="item6.value"
              ></el-option>
            </template>
            <template v-else-if="item.name == 'type'">
              <el-option
                v-for="(item7, i7) in typeData"
                :key="i7 + '777'"
                :label="item7.name"
                :value="item7.value"
              ></el-option>
            </template>
            <template v-else-if="item.name == 'status'">
              <el-option
                v-for="(item8, i8) in statusData"
                :key="i8 + '888'"
                :label="item8.name"
                :value="item8.value"
              ></el-option>
            </template>
            <template v-else-if="item.name == 'logo'">
              <el-option v-for="(v, i9) in selectArr[item.name]" :key="i9 + '999'" :value="v.hash">
                <div style="display: flex; justify-content: space-between; align-items: center">
                  <img
                    style="max-width: 20px; max-height: 20px"
                    :src="String(v.content).includes('http') ? v.content : showSrcIp + v.content"
                    alt=""
                  />
                  <span>{{ v.hash }}</span>
                </div>
              </el-option>
            </template>
            <template v-else-if="item.name == 'clue_company_name'">
              <el-option
                v-for="(v, i10) in selectArr[item.name]"
                :key="i10 + '101010'"
                :label="v"
                :value="v"
              >
                <span>{{ v }} &nbsp;&nbsp;&nbsp;</span>
                <i
                  v-show="v == '-'"
                  @mouseover="isNullItem = true"
                  @mouseout="isNullItem = false"
                  class="el-icon-question"
                ></i>
                <span v-show="isNullItem && v == '-'" style="color: rgb(96, 98, 102)"
                  >无{{ tipShow[item.name] }}</span
                >
              </el-option>
            </template>
            <template v-else>
              <el-option
                v-for="(v, i11) in selectArr[item.name]"
                :key="i11 + '111111'"
                :label="v"
                :value="v"
              >
                <span>{{ v }} &nbsp;&nbsp;&nbsp;</span>
                <i
                  v-show="v == '-'"
                  @mouseover="isNullItem = true"
                  @mouseout="isNullItem = false"
                  class="el-icon-question"
                ></i>
                <span v-show="isNullItem && v == '-'" style="color: rgb(96, 98, 102)"
                  >无{{ tipShow[item.name] }}</span
                >
              </el-option>
            </template>
          </el-select>
          <el-date-picker
            v-if="item.icon == 'date'"
            v-model="formInline[item.name]"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="account_filter_repossess"
          >重置</el-button
        >
        <el-button class="highBtn" @click="highCheck" id="account_filter_select">筛选</el-button>
      </div>
    </div>
  </el-drawer>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
export default {
  components: { tableTooltip },
  props: {
    highCheckdialog: {
      type: Boolean,
      default: false
    },
    formInline: {
      type: Object,
      default: () => {}
    },
    selectArr: {
      type: Object,
      default: () => {}
    },
    stateData: {
      type: Array,
      default: () => [
        {
          name: '离线',
          value: 0
        },
        {
          name: '在线',
          value: 1
        }
      ]
    }
  },

  data() {
    return {
      tipShow: {
        clue_company_name: '企业名称',
        title: '网站标题',
        domain: '域名'
      },
      isNullItem: false,
      highIsShow: false,
      tableHeader: [
        {
          label: 'IP地址',
          name: 'ip',
          icon: 'input',
          detailIs: 1,
          fixed: 'left',
          path: [
            '/groupAssets-recommend',
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/phishingTask',
            '/phishingTaskRecordInfo'
          ]
        },
        {
          label: '仿冒目标',
          name: 'name',
          icon: 'select',
          path: ['/phishingTask', '/phishingTaskRecordInfo']
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          icon: 'select',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: '端口',
          name: 'port',
          icon: 'select',
          detailIs: 1,
          path: [
            '/groupAssets-recommend',
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/phishingTask',
            '/phishingTaskRecordInfo'
          ]
        },
        {
          label: '协议',
          name: 'protocol',
          icon: 'select',
          detailIs: 1,
          path: [
            '/groupAssets-recommend',
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/phishingTask',
            '/phishingTaskRecordInfo'
          ]
        },
        {
          label: 'URL',
          name: 'url',
          icon: 'select',
          detailIs: 1,
          path: [
            '/groupAssets-recommend',
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: 'URL',
          name: 'url',
          icon: 'input',
          detailIs: 1,
          path: ['/phishingTask', '/phishingTaskRecordInfo']
        },
        {
          label: '是否可访问',
          name: 'online_state',
          icon: 'oneSelect',
          detailIs: 1,
          path: ['/phishingTask', '/phishingTaskRecordInfo']
        },
        {
          label: '根域',
          name: 'domain',
          icon: 'select',
          detailIs: 1,
          path: [
            '/assetsLedger',
            '/groupAssets-recommend',
            '/scanReg',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            '/phishingTask',
            '/phishingTaskRecordInfo'
          ]
        },
        {
          label: '子域名',
          name: 'subdomain',
          icon: 'select',
          detailIs: 1,
          path: [
            '/groupAssets-recommend',
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/groupAssets',
            '/unitIndex',
            '/assetsCloud',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/phishingTask',
            '/phishingTaskRecordInfo'
          ]
        },
        {
          label: '网站标题',
          name: 'title',
          icon: 'select',
          detailIs: 1,
          path: ['/groupAssets-recommend', '/scanReg', '/groupAssets', '/unitIndex', '/assetsCloud']
        },
        {
          label: '网站标题',
          name: 'title',
          icon: 'input',
          detailIs: 1,
          path: ['/phishingTask', '/phishingTaskRecordInfo']
        },
        {
          label: '组件信息',
          name: 'rule_tags',
          icon: 'select',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: '状态码',
          name: 'http_status_code',
          icon: 'select',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: '证书',
          name: 'cert',
          icon: 'select',
          path: ['/groupAssets-recommend', '/scanReg', '/groupAssets', '/unitIndex', '/assetsCloud']
        },
        {
          label: 'ICP',
          name: 'icp',
          icon: 'select',
          path: ['/groupAssets-recommend', '/scanReg', '/groupAssets', '/unitIndex', '/assetsCloud']
        },
        {
          label: 'ICON',
          name: 'logo',
          icon: 'select',
          path: [
            '/groupAssets-recommend',
            '/scanReg',
            '/groupAssets',
            // '/unitIndex',
            '/assetsCloud',
            '/phishingTask',
            '/phishingTaskRecordInfo'
          ]
        },
        {
          label: '状态',
          name: 'status',
          icon: 'select',
          path: []
        },
        {
          label: '操作时间',
          name: 'created_at',
          icon: 'date',
          path: []
        },
        {
          label: '域名备案企业',
          name: 'icp_company',
          icon: 'select',
          path: ['/phishingTask', '/phishingTaskRecordInfo']
        },
        {
          label: '运营商',
          name: 'isp',
          names: 'geo',
          icon: 'select',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: '企业名称',
          name: 'organization_company_name',
          icon: 'input',
          detailIs: 1,
          path: ['/groupAssets-recommend']
        },
        {
          label: '地理位置',
          name: 'province',
          names: 'geo',
          icon: 'select',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        // {
        //   label: 'ASN',
        //   name: 'asn',
        //   names: 'geo',
        //   icon: 'select',
        //   detailIs: 1,
        //   path: [ '/intelligenceRelated', '/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        // },
        // {
        //   label: '经度',
        //   name: 'lon',
        //   names: 'geo',
        //   icon: 'input',
        //   detailIs: 1,
        //   path: [
        //     '/intelligenceRelated',
        //     // '/assetsLedger',
        //     '/unclaimCloud',
        //     '/ignoreAssets',
        //     '/threatAssets'
        //   ]
        // },
        // {
        //   label: '纬度',
        //   name: 'lat',
        //   names: 'geo',
        //   icon: 'input',
        //   detailIs: 1,
        //   path: [
        //     '/intelligenceRelated',
        //     // '/assetsLedger',
        //     '/unclaimCloud',
        //     '/ignoreAssets',
        //     '/threatAssets'
        //   ]
        // },
        {
          label: '资产状态',
          name: 'online_state',
          icon: 'select',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: '探测方式',
          name: 'reason',
          icon: 'select',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        // {
        //   label: '可信度值',
        //   name: 'score_type',
        //   names: 'score',
        //   icon: 'select',
        //   detailIs: 1,
        //   path: ['/unclaimCloud']
        //   // path: [ '/intelligenceRelated', '/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        // },
        {
          label: '更新时间',
          name: 'updated_at',
          icon: 'date',
          path: ['/intelligenceRelated', '/assetsLedger'],
          detailIs: 1
        },
        {
          label: '扫描时间',
          name: 'created_at',
          icon: 'date',
          path: [],
          detailIs: 1
        },
        {
          label: '推荐时间',
          name: 'created_at',
          icon: 'date',
          path: ['/unclaimCloud'],
          detailIs: 1
        },
        {
          label: '添加时间',
          name: 'updated_at',
          icon: 'date',
          path: [],
          detailIs: 1
        },
        {
          label: '首次发现时间',
          name: 'created_at',
          icon: 'date',
          path: ['/phishingTask', '/phishingTaskRecordInfo']
        },
        {
          label: '更新时间',
          name: 'updated_at',
          icon: 'date',
          path: ['/phishingTask', '/phishingTaskRecordInfo']
        },
        {
          label: '云厂商',
          name: 'cloud_name',
          icon: 'select',
          detailIs: 1,
          path: ['/scanReg']
        },
        {
          label: '证据链',
          name: 'chain_type',
          icon: 'select',
          path: ['/scanReg', '/phishingTask', '/phishingTaskRecordInfo']
        },
        {
          label: '忽略时间',
          name: 'updated_at',
          icon: 'date',
          path: ['/ignoreAssets'],
          detailIs: 1
        },
        {
          label: '标记威胁时间',
          name: 'updated_at',
          icon: 'date',
          path: ['/threatAssets'],
          detailIs: 1
        },
        {
          label: '信任度',
          name: 'assets_confidence_level',
          icon: 'select',
          minWidth: '120',
          detailIs: 1,
          path: ['/scanReg']
        }
      ],
      levelData: [
        {
          name: '高可信度',
          value: 1
        },
        {
          name: '中可信度',
          value: 2
        },
        {
          name: '低可信度',
          value: 3
        }
      ],
      // levelData: [{
      //   name: 'A级',
      //   value: 1
      // }, {
      //   name: 'B级',
      //   value: 2
      // }, {
      //   name: 'C级',
      //   value: 3
      // }, {
      //   name: 'D级',
      //   value: 4
      // }],
      reasonData: [
        {
          name: '根域',
          value: 0
        },
        {
          name: '证书',
          value: 1
        },
        {
          name: 'ICP',
          value: 2
        },
        {
          name: 'ICON',
          value: 3
        },
        {
          name: '关键词',
          value: 4
        },
        {
          name: '子域名',
          value: 5
        },
        {
          name: '已知资产IP',
          value: 6
        }
      ],
      reasonDataFake: [
        {
          name: '根域',
          value: '0'
        },
        {
          name: 'ICON',
          value: '3'
        },
        {
          name: '关键词',
          value: '4'
        }
      ],
      typeData: [
        {
          name: '云端推荐',
          value: 1
        },
        {
          name: '资产扫描',
          value: 0
        }
      ],
      statusData: [
        {
          name: '待处理',
          value: 0
        },
        {
          name: '处理中',
          value: 1
        },
        {
          name: '处理完成',
          value: 2
        },
        {
          name: '执行失败',
          value: 3
        }
      ],
      scoreList: [
        {
          name: '高可信度',
          value: 2
        },
        {
          name: '中可信度',
          value: 1
        },
        {
          name: '低可信度',
          value: 3
        },
        {
          name: '根据输入的分数搜索',
          value: 4
        }
      ]
    }
  },
  watch: {
    highCheckdialog(val) {
      this.highIsShow = val
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange']),
    tableHeaderIsShow(path) {
      let arr = this.tableHeader.filter((item) => {
        return item.path.indexOf(this.$route.path) != -1
      })
      return arr
    }
  },
  methods: {
    resetForm() {
      this.$nextTick(() => {
        this.$refs.drawerForm.resetFields()
      })
    },
    highIsClose() {
      this.$emit('highIsClose')
    },
    highCheck() {
      this.$emit('highCheck', this.formInline)
    }
  }
}
</script>
<style lang="less" scoped>
.con {
  width: 100%;
  height: 100%;
}
/deep/.el-form-item__content {
  margin-left: 110px !important;
}
/deep/.el-form-item__label {
  width: 110px !important;
}
</style>
