<template>
  <el-drawer
    title="高级筛选"
    :visible.sync="highIsShow"
    direction="rtl"
    ref="drawer"
    @open="highIsOpen"
    @close="highIsClose"
  >
    <div class="demo-drawer__content">
      <el-form :model="formInline" ref="drawerForm" label-width="84px">
        <el-form-item v-for="item in tableHeaderIsShow" :key="item.id" :prop="item.name">
          <span
            slot="label"
            v-if="
              (item.name == 'assets_source' && (user.role == 1 || user.role == 2)) ||
              item.name != 'assets_source'
            "
          >
            <span>{{ item.label }}：</span>
          </span>
          <el-input
            v-if="item.icon == 'input'"
            v-model="formInline[item.name]"
            placeholder="请输入"
          ></el-input>
          <!-- 证据链 -->
          <el-select
            v-else-if="item.icon == 'select' && item.name == 'reason_type'"
            clearable
            filterable
            v-model="formInline[item.name]"
          >
            <el-option
              v-for="item in reasonData"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
          <!-- 域名 -->
          <el-select
            v-else-if="item.icon == 'select' && item.name == 'domain'"
            clearable
            filterable
            multiple
            collapse-tags
            v-model="formInline[item.name]"
          >
            <el-option v-for="v in selectArr.domain" :key="v" :label="v" :value="v">
              <span>{{ v }} &nbsp;&nbsp;&nbsp;</span>
              <i
                v-show="v == '-'"
                @mouseover="isNullItem = true"
                @mouseout="isNullItem = false"
                class="el-icon-question"
              ></i>
              <span v-show="isNullItem && v == '-'" style="color: rgb(96, 98, 102)"
                >无{{ tipShow[item.name] }}</span
              >
            </el-option>
          </el-select>
          <!-- 云厂商 -->
          <el-select
            v-else-if="item.icon == 'select' && item.name == 'cloud_name'"
            clearable
            filterable
            v-model="formInline[item.name]"
          >
            <el-option
              v-for="item in selectArr.cloud_name"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>

          <!-- 是否泛解析 -->
          <el-select
            v-else-if="item.icon == 'select' && item.name == 'open_parse'"
            clearable
            filterable
            v-model="formInline[item.name]"
            placeholder="请选择"
          >
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
          <!-- 是否CDN -->
          <el-select
            v-else-if="item.icon == 'select' && item.name == 'is_cdn'"
            clearable
            filterable
            v-model="formInline[item.name]"
            placeholder="请选择"
          >
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
          <!-- 是否ip匹配 -->
          <el-select
            v-else-if="item.icon == 'select' && item.name == 'ip_match'"
            clearable
            filterable
            v-model="formInline[item.name]"
            placeholder="请选择"
          >
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="2"></el-option>
          </el-select>
          <!-- 是否企业匹配 -->
          <el-select
            v-else-if="item.icon == 'select' && item.name == 'company_match'"
            clearable
            filterable
            v-model="formInline[item.name]"
            placeholder="请选择"
          >
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="2"></el-option>
          </el-select>
          <div v-else-if="item.icon == 'select' && item.name == 'score_type'">
            <el-select clearable filterable v-model="formInline[item.name]">
              <template>
                <el-option
                  v-for="item in scoreList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                ></el-option>
              </template>
            </el-select>
            <el-input
              v-if="formInline[item.name] == 4"
              style="margin-top: 20px"
              v-model="formInline.score"
              placeholder="请输入"
            ></el-input>
          </div>
          <!-- :multiple="item.name == 'online_state' ? false : true" -->
          <el-select
            filterable
            v-else-if="
              item.icon == 'select' &&
              ((item.name == 'assets_source' && (user.role == 1 || user.role == 2)) ||
                item.name != 'assets_source')
            "
            v-model="formInline[item.name]"
            clearable
            multiple
            collapse-tags
            placeholder="请选择"
            :reserve-keyword="true"
          >
            <template v-if="item.name == 'online_state'">
              <el-option
                v-for="item in stateData"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </template>
            <template v-else-if="item.name == 'type'">
              <el-option
                v-for="item in typeData"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </template>
            <template v-else-if="item.name == 'tags'">
              <el-option
                v-for="item in tagsArr"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </template>
            <template v-else-if="item.name == 'status'">
              <el-option
                v-for="item in statusData"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </template>
            <template v-else-if="item.name == 'threaten_type_arr'">
              <el-option
                v-for="item in threaten_type_arr"
                :key="item.id"
                :label="item.type_name"
                :value="item.id"
              ></el-option>
            </template>
            <template
              v-else-if="
                item.name == 'clue_company_name' ||
                item.name == 'title' ||
                item.name == 'domain' ||
                item.name == 'not_in_clue_domain' ||
                item.name == 'customer_tags'
              "
            >
              <el-option v-for="(v, i) in selectArr[item.name]" :key="i" :label="v" :value="v">
                <span>{{ v }} &nbsp;&nbsp;&nbsp;</span>
                <i
                  v-show="v == '-'"
                  @mouseover="isNullItem = true"
                  @mouseout="isNullItem = false"
                  class="el-icon-question"
                ></i>
                <span v-show="isNullItem && v == '-'" style="color: rgb(96, 98, 102)"
                  >无{{ tipShow[item.name] }}</span
                >
              </el-option>
            </template>
            <template
              v-else-if="item.name == 'assets_source' && (user.role == 1 || user.role == 2)"
            >
              <el-option
                v-for="(v, i) in assetsSourceList"
                :key="i"
                :label="v.label"
                :value="v.value"
              >
                <span>{{ v.label }} &nbsp;&nbsp;&nbsp;</span>
                <i
                  v-show="v.value == '-'"
                  @mouseover="isNullItem = true"
                  @mouseout="isNullItem = false"
                  class="el-icon-question"
                ></i>
                <span v-show="isNullItem && v.value == '-'" style="color: rgb(96, 98, 102)"
                  >无{{ tipShow[item.name] }}</span
                >
              </el-option>
            </template>
            <!-- <template v-else-if="item.name == 'customer_tags'">
                <el-option v-for="item in threaten_type_arr" :key="item.id" :label="item.type_name" :value="item.id"></el-option>
                <el-option v-for="(v,i) in selectArr[item.name]" :key="i" :label="v" :value="v">
                  <span>{{ v }} &nbsp;&nbsp;&nbsp;</span>
                  <i v-show = "v == '-'" @mouseover="isNullItem = true" @mouseout="isNullItem = false" class="el-icon-question"></i>
                  <span v-show="isNullItem && v == '-'" style="color: rgb(96, 98, 102);">无{{ tipShow[item.name] }}</span>
                </el-option>
              </template> -->
            <template v-else>
              <el-option
                v-for="(v, i) in selectArr[item.name]"
                :key="i"
                :label="v"
                :value="v"
              ></el-option>
            </template>
          </el-select>
          <el-date-picker
            v-if="item.icon == 'date'"
            v-model="formInline[item.name]"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="account_filter_repossess"
          >重置</el-button
        >
        <el-button class="highBtn" @click="highCheck" id="account_filter_select">筛选</el-button>
      </div>
    </div>
  </el-drawer>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { assetsSourceList } from '@/utils/commonData.js'

export default {
  components: { tableTooltip },
  props: [
    'loading',
    'highCheckdialog',
    'formInlineOutput',
    'selectArr',
    'threaten_type_arr',
    'user'
  ],
  data() {
    return {
      formInline: {},
      // assetsSourceList: [
      //   { label: '-', value: '-' },
      //   { label: 'FOFA的search/all接口返回', value: 1 },
      //   { label: 'IP138', value: 2 },
      //   { label: 'chaziyu', value: 3 },
      //   { label: 'hunter', value: 4 },
      //   { label: '线索库域名实时解析', value: 5 },
      //   { label: 'FOFA纯解析域名数据', value: 6 },
      //   { label: '非线索库的域名实时解析', value: 7 },
      //   { label: '基础扫描', value: 8 },
      //   { label: '单位测绘任务下的全端口扫描', value: 9 },
      //   { label: '单位测绘下的常用端口扫描', value: 10 },
      //   { label: 'oneforall', value: 11 }
      // ],
      tipShow: {
        clue_company_name: '企业名称',
        title: '网站标题',
        domain: '域名',
        assets_source: '数据来源'
      },
      isNullItem: false,
      highIsShow: false,
      tableHeader: [
        {
          label: 'IP地址',
          name: 'ip',
          icon: 'input',
          detailIs: 1,
          fixed: 'left',
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/scanReg',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          icon: 'select',
          detailIs: 1,
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/scanReg',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: '端口',
          name: 'port',
          icon: 'select',
          detailIs: 1,
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/scanReg',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: '威胁类型',
          name: 'threaten_type_arr',
          icon: 'select',
          path: ['/threatAssets'],
          detailIs: 1
        },
        {
          label: '协议',
          name: 'protocol',
          icon: 'select',
          detailIs: 1,
          path: [
            '/assetsLedger',
            '/riskAssets',
            '/scanReg',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: '网站标题',
          name: 'title',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '状态码',
          name: 'http_status_code',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: 'host',
          name: 'hosts',
          icon: 'input',
          detailIs: 1,
          path: ['/assetsLedger', '/riskAssets']
        },
        {
          label: '域名',
          name: 'domain',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets', '/riskAssets']
        },
        {
          label: '非企业线索库域名',
          name: 'not_in_clue_domain',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/unclaimCloud']
        },
        {
          label: '数据来源',
          name: 'assets_source',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets', '/riskAssets']
        },
        {
          label: '子域名',
          name: 'subdomain',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/scanReg', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '组件信息',
          name: 'rule_tags',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '地理位置',
          name: 'province',
          names: 'geo',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '资产状态',
          name: 'online_state',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/riskAssets', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '资产标签',
          name: 'tags',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/unclaimCloud']
        },
        {
          label: '是否ip匹配',
          name: 'ip_match',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger']
        },
        {
          label: '是否企业匹配',
          name: 'company_match',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger']
        },
        {
          label: '创建时间',
          name: 'created_at',
          icon: 'date',
          detailIs: 1,
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '更新时间',
          name: 'updated_at',
          icon: 'date',
          detailIs: 1,
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '证据链',
          name: 'reason_type',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: 'CDN',
          name: 'is_cdn',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '泛解析',
          name: 'open_parse',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger']
        },
        {
          label: '云厂商',
          name: 'cloud_name',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        },
        {
          label: '自定义标签',
          name: 'customer_tags',
          icon: 'select',
          detailIs: 1,
          path: ['/assetsLedger']
        }
      ],
      tagsArr: [
        {
          name: '用户-扫描',
          value: 0
        },
        {
          name: '安服-扫描',
          value: 1
        },
        {
          name: '用户-推荐',
          value: 2
        },
        {
          name: '安服-推荐',
          value: 3
        },
        {
          name: '安服-导入',
          value: 4
        }
      ],
      stateData: [
        {
          name: '离线',
          value: 0
        },
        {
          name: '在线',
          value: 1
        }
      ],
      reasonData: [
        {
          name: '根域',
          value: 0
        },
        {
          name: '证书',
          value: 1
        },
        {
          name: 'ICP',
          value: 2
        },
        {
          name: 'ICON',
          value: 3
        },
        {
          name: '关键词',
          value: 4
        },
        {
          name: 'IP段',
          value: 6
        }
      ],
      typeData: [
        {
          name: '云端推荐',
          value: 1
        },
        {
          name: '资产扫描',
          value: 0
        }
      ],
      statusData: [
        {
          name: '待处理',
          value: 0
        },
        {
          name: '处理中',
          value: 1
        },
        {
          name: '处理完成',
          value: 2
        },
        {
          name: '执行失败',
          value: 3
        }
      ],
      scoreList: [
        {
          name: '高可信度',
          value: 2
        },
        {
          name: '中可信度',
          value: 1
        },
        {
          name: '低可信度',
          value: 3
        },
        {
          name: '根据输入的分数搜索',
          value: 4
        }
      ],
      labeledValueList: [
        // 钓鱼仿冒、ICP盗用、黄赌毒网站、其他类型
        {
          name: '钓鱼仿冒',
          value: '1'
        },
        {
          name: '黄赌毒网站',
          value: '2'
        },
        {
          name: 'ICP盗用',
          value: '3'
        },
        {
          name: '域名混淆',
          value: '4'
        },
        {
          name: '其他类型',
          value: '0'
        }
      ]
    }
  },
  watch: {
    highCheckdialog(val) {
      this.highIsShow = val
      // if(val){
      //   this.loadingInstance = this.$loading({
      //     target: '.el-drawer'
      //   });
      // }
    },
    formInlineOutput: {
      handler(val) {
        this.formInline = (val && JSON.parse(JSON.stringify(val))) || {}
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange']),
    assetsSourceList() {
      return assetsSourceList
    },
    tableHeaderIsShow(path) {
      let arr = this.tableHeader.filter((item) => {
        return item.path.indexOf(this.$route.path) != -1
      })
      return arr
    }
  },
  methods: {
    highIsOpen() {
      this.formInline =
        (this.formInlineOutput && JSON.parse(JSON.stringify(this.formInlineOutput))) || {}
    },
    resetForm() {
      this.$nextTick(() => {
        this.$refs.drawerForm.resetFields()
      })
    },
    highIsClose() {
      this.$emit('highIsClose')
    },
    highCheck() {
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      this.$emit('highCheck', this.formInline)
    }
  }
}
</script>
<style lang="less" scoped>
.con {
  width: 100%;
  height: 100%;
}
/deep/.el-form-item__content {
  margin-left: 110px !important;
}
/deep/.el-form-item__label {
  width: 110px !important;
}
.tipText {
  color: rgb(96, 98, 102);
}
</style>
