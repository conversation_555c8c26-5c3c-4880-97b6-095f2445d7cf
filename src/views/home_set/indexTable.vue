<template>
  <div class="con">
    <customColumn
      v-if="isChecked"
      :dataList="tableDataList"
      :key="componentKey"
      :coumnId="columnId"
      @columnChecked="columnChecked"
    ></customColumn>

    <el-table
      border
      :data="tableData"
      row-key="id"
      :header-cell-style="{ background: '#F5F7FA', color: '#62666C' }"
      @selection-change="handleSelectionChange"
      @cell-mouse-enter="showTooltip"
      @cell-mouse-leave="hiddenTooltip"
      ref="eltable"
      height="100%"
      style="width: 100%"
    >
      <template slot="empty">
        <div class="emptyClass">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-kong"></use>
          </svg>
          暂无数据
        </div>
      </template>
      <el-table-column
        v-if="isChecked"
        type="selection"
        align="center"
        :reserve-selection="true"
        :show-overflow-tooltip="true"
        :selectable="handleSelectable"
        width="55"
      >
      </el-table-column>
      <el-table-column
        v-for="item in tableHeaderIsShow"
        :key="item.id"
        align="left"
        :prop="item.name"
        :label="item.label"
        :fixed="item.fixed"
        :min-width="item.minWidth"
      >
        <template slot-scope="scope">
          <span v-if="item.name == 'clue_company_name' || item.name == 'all_company_name'">
            <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
              <span slot="content">{{ getCompany(scope.row[item.name]) }}</span>
              <span>{{ getCompany(scope.row[item.name]) }}</span>
            </el-tooltip>
          </span>
          <span v-else-if="item.name == 'ip'">
            <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
              <span slot="content">
                <span>{{ scope.row[item.name] }}</span>
              </span>
              <span>
                <span>{{ scope.row[item.name] }}</span>
                <span v-if="scope.row['is_cdn']" class="originLine">CDN</span>
                <el-tooltip effect="light" class="item" placement="top" content="">
                  <div slot="content"> 云厂商：{{ getCloudName(scope.row['cloud_name']) }} </div>
                  <span v-if="getCloudName(scope.row['cloud_name'])" class="originLine"
                    ><i class="el-icon-cloudy"></i
                  ></span>
                </el-tooltip>
                <span class="blueLine" v-if="scope.row['chain_list']">
                  <el-tooltip
                    placement="top"
                    :disabled="!scope.row['chain_list']"
                    class="item"
                    effect="light"
                    popper-class="chainClass"
                    :open-delay="500"
                  >
                    <div slot="content" style="position: relative">
                      <el-tooltip
                        effect="light"
                        class="item"
                        placement="top"
                        content="一键复制"
                        v-if="scope.row['chain_list'] && scope.row['chain_list'].length != 0"
                        :open-delay="500"
                      >
                        <i
                          class="el-icon-document-copy"
                          @click="copyClick(scope.row['chain_list'])"
                          style="
                            color: #2677ff;
                            cursor: pointer;
                            position: absolute;
                            right: -6px;
                            top: 0;
                          "
                        ></i>
                      </el-tooltip>
                      证据链:{{ scope.row['chain_list'].length }}条

                      <div v-for="(item, index) in scope.row['chain_list']" :key="index">
                        <span>{{ index + 1 }}、</span>
                        <span v-for="(v, i) in getChains(item)" :key="i">
                          <span v-if="v.type && v.type == 3">
                            <el-image
                              :src="v.content.includes('http') ? v.content : showSrcIp + v.content"
                              alt=""
                            >
                              <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i>
                              </div>
                            </el-image>
                          </span>
                          <span v-else>{{ v.content }}</span>
                          <span
                            v-if="i < item.length - 1 && v"
                            class="el-icon-right iconRight"
                          ></span>
                        </span>
                      </div>
                    </div>
                    <span>
                      <img
                        src="../../assets/images/chain.svg"
                        alt=""
                        style="width: 12px; vertical-align: middle"
                      />
                    </span>
                  </el-tooltip>
                </span>
              </span>
            </el-tooltip>
          </span>
          <span v-else-if="item.name == 'url'">
            <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
              <span slot="content">{{
                $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '')
              }}</span>
              <a
                v-if="
                  scope.row[item.name] &&
                  (String(scope.row[item.name]).includes('http') ||
                    ((scope.row.protocol == 'tls' || scope.row.protocol == 'unknown') &&
                      (/[a-zA-Z]+/.test(scope.row[item.name]) ||
                        String(scope.row[item.name]).includes(':'))))
                "
                style="color: #409eff"
                :href="
                  scope.row[item.name].includes('http')
                    ? scope.row[item.name]
                    : `http://${scope.row[item.name]}`
                "
                target="_blank"
              >
                {{ $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '-') }}
              </a>
              <span v-else>{{
                $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '-')
              }}</span>
            </el-tooltip>
          </span>
          <p v-else-if="item.name == 'logo'">
            <el-image
              v-if="scope.row[item.name] && scope.row[item.name].content"
              :src="
                String(scope.row[item.name].content).includes('http')
                  ? scope.row[item.name].content
                  : showSrcIp + scope.row[item.name].content
              "
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <span v-else>-</span>
          </p>
          <span v-else-if="item.name == 'subdomain'">
            <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
              <span slot="content"
                >{{ $punyCode.toUnicode(String(scope.row[item.name]))
                }}{{ scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : '' }}</span
              >
              <span>
                <span
                  :class="scope.row['open_parse'] == 1 ? 'hostDetail hostDetailHas' : 'hostDetail'"
                  >{{ $punyCode.toUnicode(scope.row[item.name]) }}</span
                >
                <span v-if="scope.row['open_parse'] == 1" class="blueRadiusBorder">泛解析</span>
              </span>
            </el-tooltip>
          </span>
          <!-- 推荐理由 -->
          <span v-else-if="item.name == 'reason'">
            <el-tooltip
              class="reasonClass"
              popper-class="reasonClass"
              effect="dark"
              placement="top"
              :open-delay="500"
            >
              <span slot="content" v-html="scope.row[item.name].replace(/[\\]/g, '')"></span>
              <span v-html="scope.row[item.name].replace(/[\\]/g, '')"></span>
            </el-tooltip>
          </span>
          <span
            v-else-if="
              item.name == 'isp' ||
              item.name == 'province' ||
              item.name == 'asn' ||
              item.name == 'lat' ||
              item.name == 'lon'
            "
          >
            <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
              <span slot="content">{{ getTableGeo(scope.row[item.names], item.name) }}</span>
              <span>{{ getTableGeo(scope.row[item.names], item.name) }}</span>
            </el-tooltip>
          </span>
          <span v-else-if="item.name == 'reliability_score'">
            <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row[item.names]"
              placement="top"
              :open-delay="500"
            >
              <span>{{ score(scope.row[item.name]) }}</span>
            </el-tooltip>
          </span>
          <span v-else-if="item.name == 'online_state'">
            <span class="greenLine" v-if="scope.row[item.name] == 1">在线</span>
            <span class="grayLine" v-else>离线</span>
          </span>
          <span v-else-if="item.name == 'type'">{{ typeCell(scope.row[item.name]) }}</span>
          <!-- 安服显示数据来源 -->
          <div v-else-if="item.name == 'assets_source'">
            <el-tooltip
              v-if="scope.row['assets_source']"
              :open-delay="500"
              class="item"
              effect="dark"
              placement="top"
            >
              <span slot="content">
                {{ getAssetsSource[scope.row.assets_source]
                }}{{
                  scope.row['source_updated_at']
                    ? `（最新发现时间：${scope.row['source_updated_at']}）`
                    : ''
                }}
              </span>
              <p class="detail">{{ getAssetsSource[scope.row.assets_source] }}</p>
            </el-tooltip>
            <span v-else>-</span>
          </div>
          <!-- 推荐记录资产分类 -->
          <div v-else-if="item.name == 'ip_status'">
            <el-tooltip
              :transition="'0'"
              v-if="String(scope.row[item.name])"
              class="item"
              effect="dark"
              :content="getAssetsType(scope.row[item.name])"
              placement="top"
              :open-delay="500"
            >
              <div class="ellipsis">{{ getAssetsType(scope.row[item.name]) }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </div>
          <div v-else-if="item.name == 'assets_confidence_level'">
            <el-tooltip
              v-if="scope.row['assets_confidence_level']"
              class="item"
              effect="dark"
              placement="top"
              :open-delay="500"
            >
              <span slot="content">{{ levelDataMap[scope.row[item.name]] }}</span>
              <span>{{ levelDataMap[scope.row[item.name]] }}</span>
            </el-tooltip>
            <span v-else>-</span>
          </div>
          <span
            v-else-if="item.name == 'title'"
            class="clickPointer"
            @click="noteBlackList(scope.row.title)"
          >
            <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
              <span slot="content">{{
                $punyCode.toUnicode(getTableItem(scope.row[item.name]))
              }}</span>
              <span>{{ $punyCode.toUnicode(getTableItem(scope.row[item.name])) }}</span>
            </el-tooltip>
          </span>
          <div v-else-if="item.name == 'threaten_type'">
            <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
              <span slot="content">{{
                scope.row.threaten_type_name || getTheatTypeName(scope.row[item.name])
              }}</span>
              <span>{{
                scope.row.threaten_type_name || getTheatTypeName(scope.row[item.name])
              }}</span>
            </el-tooltip>
          </div>
          <span v-else>
            <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
              <span slot="content">{{
                $punyCode.toUnicode(getTableItem(scope.row[item.name]))
              }}</span>
              <span>{{ $punyCode.toUnicode(getTableItem(scope.row[item.name])) }}</span>
            </el-tooltip>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$route.path == '/unitIndex' && pageIcon == 'fourth'"
        fixed="right"
        label="操作"
        align="left"
        min-width="200"
      >
        <template slot-scope="scope">
          <!-- 1 标记为认领资产 2/标记为忽略资产 3/标记为威胁资产  5/标记未知资产 -->
          <el-button
            type="text"
            size="small"
            @click.native="goFenlei(3, scope.row)"
            id="cloud_recommend_info"
            >标记威胁</el-button
          >
          <el-button
            type="text"
            size="small"
            @click.native="goFenlei(1, scope.row)"
            id="cloud_recommend_info"
            >入台账</el-button
          >
          <el-button
            type="text"
            size="small"
            @click.native="goFenlei(5, scope.row)"
            id="cloud_recommend_info"
            >标记疑似</el-button
          >
          <el-button
            type="text"
            size="small"
            @click.native="goDelete(scope.row)"
            id="cloud_recommend_del"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip> -->
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import customColumn from '../../components/assets/customColumn.vue'
import noteBlack from '@/components/assets/noteBlack.vue'
import { cloudAssetsFenlei } from '@/api/apiConfig/surveying.js'
import { assetsSourceList } from '@/utils/commonData.js'

export default {
  components: { tableTooltip, customColumn, noteBlack },
  // props: ['tableData', 'pageIcon', 'checkedAll', 'radioParams','isChecked'],
  props: {
    tableData: Array,
    pageIcon: String,
    checkedAll: Boolean,
    clearSelectionTrigger: Boolean, //触发器
    radioParams: {
      type: [Object, Boolean, String],
      default: ''
    },
    isChecked: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      currentTitle: '',
      noteDialogVisible: false,
      levelDataMap: {
        1: '高可信度',
        2: '中可信度',
        3: '低可信度'
      },
      // levelDataMap: {
      //   1:'A级',
      //   2:'B级',
      //   3:'C级',
      //   4:'D级',
      // },
      checkedArr: [],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      tableDataList: [],
      tableHeaderCopy: [],
      componentKey: 0,
      user: {
        role: ''
      },
      tableHeader: [
        {
          label: 'IP地址',
          name: 'ip',
          icon: 'input',
          minWidth: '240',
          detailIs: 1,
          fixed: 'left',
          path: [
            '/organization-assets',
            '/groupAssets-recommend',
            '/groupAssets',
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/unitIndex',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '端口',
          name: 'port',
          icon: 'select',
          minWidth: '70',
          detailIs: 1,
          path: [
            '/groupAssets-recommend',
            '/groupAssets',
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/unitIndex',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '协议',
          name: 'protocol',
          icon: 'select',
          minWidth: '70',
          detailIs: 1,
          path: [
            '/groupAssets-recommend',
            '/groupAssets',
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/unitIndex',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '数据来源',
          name: 'assets_source',
          icon: 'input',
          minWidth: '120',
          detailIs: 1,
          path: ['/groupAssets', '/intelligenceRelated', '/assetsLedger']
        },
        {
          label: 'URL',
          name: 'url',
          icon: 'select',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/groupAssets-recommend',
            '/groupAssets',
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/unitIndex',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '根域',
          name: 'domain',
          icon: 'select',
          minWidth: '120',
          detailIs: 1,
          path: ['/groupAssets-recommend', '/scanReg', '/unitIndex']
        },
        {
          label: '子域名',
          name: 'subdomain',
          icon: 'select',
          minWidth: '180',
          detailIs: 1,
          path: [
            '/groupAssets-recommend',
            '/groupAssets',
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/unitIndex',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ]
        },
        {
          label: '网站标题',
          name: 'title',
          icon: 'select',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/groupAssets-recommend',
            '/intelligenceRelated',
            '/assetsLedger',
            '/scanReg',
            '/unitIndex',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '威胁类型',
          name: 'threaten_type',
          minWidth: '90',
          detailIs: 1,
          path: ['/threatAssets']
        },
        {
          label: '资产分类',
          name: 'ip_status',
          minWidth: '100',
          detailIs: 2,
          path: ['/scanReg']
        },
        {
          label: '组件信息',
          name: 'rule_tags',
          icon: 'input',
          minWidth: '150',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '状态码',
          name: 'http_status_code',
          icon: 'select',
          minWidth: '70',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: 'ICP',
          name: 'icp',
          minWidth: '120',
          path: ['/groupAssets-recommend', '/scanReg', '/unitIndex']
        },
        {
          label: 'ICON',
          name: 'logo',
          minWidth: '80',
          path: ['/groupAssets-recommend', '/scanReg', '/unitIndex']
        },
        {
          label: '证书',
          name: 'cert',
          minWidth: '120',
          path: ['/groupAssets-recommend', '/scanReg', '/unitIndex']
        },
        {
          label: '运营商',
          name: 'isp',
          names: 'geo',
          minWidth: '100',
          icon: 'select',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          icon: 'input',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/scanReg',
            '/index'
          ]
        },
        {
          label: '企业名称',
          name: 'all_company_name',
          icon: 'input',
          minWidth: '120',
          detailIs: 1,
          path: ['/groupAssets-recommend']
        },
        {
          label: '地理位置',
          name: 'province',
          names: 'geo',
          minWidth: '80',
          icon: 'select',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '经度',
          name: 'lon',
          names: 'geo',
          minWidth: '100',
          icon: 'input',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '纬度',
          name: 'lat',
          names: 'geo',
          minWidth: '100',
          icon: 'input',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '资产状态',
          name: 'online_state',
          icon: 'select',
          minWidth: '80',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        {
          label: '探测方式',
          name: 'reason',
          icon: 'input',
          minWidth: '120',
          detailIs: 1,
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets',
            '/index'
          ]
        },
        // {
        //   label: '可信度值',
        //   name: 'reliability_score',
        //   names: 'score_source',
        //   icon: 'input',
        //   minWidth: '120',
        //   detailIs: 1,
        //   path: ['/unclaimCloud']
        //   // path: ['/intelligenceRelated', '/assetsLedger', '/unclaimCloud', '/ignoreAssets', '/threatAssets']
        // },
        {
          label: '首次发现时间',
          name: 'source_updated_at',
          minWidth: '120',
          path: ['/groupAssets-recommend', '/scanReg', '/unitIndex']
        },
        {
          label: '发现时间',
          name: 'created_at',
          icon: 'date',
          minWidth: '120',
          path: [
            '/intelligenceRelated',
            '/assetsLedger',
            '/unclaimCloud',
            '/ignoreAssets',
            '/threatAssets'
          ],
          detailIs: 1
        },
        {
          label: '更新时间',
          name: 'created_at',
          minWidth: '120',
          path: ['/groupAssets-recommend', '/scanReg', '/unitIndex', '/index']
        },
        {
          label: '更新时间',
          name: 'updated_at',
          icon: 'date',
          minWidth: '120',
          path: ['/intelligenceRelated', '/assetsLedger'],
          detailIs: 1
        },
        {
          label: '扫描时间',
          name: 'created_at',
          icon: 'date',
          minWidth: '120',
          path: [],
          detailIs: 1
        },
        {
          label: '推荐时间',
          name: 'created_at',
          icon: 'date',
          minWidth: '120',
          path: ['/unclaimCloud'],
          detailIs: 1
        },
        {
          label: '添加时间',
          name: 'updated_at',
          icon: 'date',
          minWidth: '120',
          path: [],
          detailIs: 1
        },
        {
          label: '忽略时间',
          name: 'updated_at',
          icon: 'date',
          minWidth: '120',
          path: ['/ignoreAssets'],
          detailIs: 1
        },
        {
          label: '标记威胁时间',
          name: 'updated_at',
          icon: 'date',
          minWidth: '120',
          path: ['/threatAssets'],
          detailIs: 1
        },
        {
          label: '信任度',
          name: 'assets_confidence_level',
          icon: 'select',
          minWidth: '120',
          detailIs: 1,
          path: ['/scanReg']
        }
        // {
        //   label: '证据链',
        //   name: 'chain_list',
        //   minWidth: '80',
        //   path: ['/scanReg', '/unitIndex']
        // }
      ]
    }
  },
  watch: {
    tableData(val) {
      this.$nextTick(() => {
        this.doLayout(this, 'eltable')
      })
    },
    checkedAll(val) {
      this.checkAllFunc()
    },
    getterCurrentCompany(val) {
      this.tableDataList = this.tableHeaderCopy
      this.componentKey += 1
    },
    clearSelectionTrigger(newVal) {
      if (newVal && this.$refs.eltable) {
        this.$refs.eltable.clearSelection()
        // 清除后，可能需要通知父组件重置这个触发器，避免重复触发
        this.$emit('selection-cleared')
      }
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.tableHeaderCopy = [...this.tableHeaderIsShow]
    this.tableDataList = [...this.tableHeaderIsShow]
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange']),
    tableHeaderIsShow(path) {
      let arr = []
      if (this.user.role == 1 || this.user.role == 2) {
        // alert(11)
        arr = this.tableHeader.filter((item) => {
          return item.path.indexOf(this.$route.path) != -1
        })
      } else {
        // 普通企业去掉assets_source字段
        arr = this.tableHeader
          .filter((item) => {
            return item.path.indexOf(this.$route.path) != -1
          })
          .filter((item) => {
            return item.name != 'assets_source'
          })
      }
      if (arr && arr.length > 0) {
        arr[0].fixed = 'left'
      }
      return arr
    },
    columnId() {
      let id = ''
      if (this.$route.path == '/assetsLedger') {
        id = '2'
      } else if (this.$route.path == '/unclaimCloud') {
        id = '4'
      } else if (this.$route.path == '/ignoreAssets') {
        id = '6'
      } else if (this.$route.path == '/threatAssets') {
        id = '8'
      } else if (this.$route.path == '/assetsCloud') {
        id = '12'
      } else if (this.$route.path == '/scanReg') {
        id = '13'
      } else if (this.$route.path == '/groupAssets') {
        id = '14'
      } else if (this.$route.path == '/groupAssets-recommend') {
        id = '15'
      }
      return id
    },
    getAssetsSource() {
      let data = {}
      assetsSourceList.forEach((v) => {
        if (v.value != '-') {
          data[v.value] = v.label
        }
      })
      return data
    }
  },
  methods: {
    noteBlackList(title) {
      this.noteDialogVisible = true
      this.currentTitle = title
    },
    getAssetsType(type) {
      // 资产分类
      let str = ''
      if (type == 0) {
        str = '疑似资产'
      } else if (type == 1 || type == 4) {
        str = '资产台账'
      } else if (type == 2) {
        str = '忽略资产'
      } else if (type == 3) {
        str = '威胁资产'
      }
      return str
    },
    // 单位资产测绘分类
    async goFenlei(icon, row) {
      let obj = {
        id: [row.id],
        set_status: icon, //1 标记为认领资产 2/标记为忽略资产 3/标记为威胁资产  5/标记未知资产
        operate_company_id: this.currentCompany
      }
      let res = await cloudAssetsFenlei(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
      }
    },
    // 单位资产测绘-资产删除
    goDelete(row) {},
    columnChecked(value) {
      this.tableHeader = value
    },
    getChains(row) {
      let arr = row.filter((item) => {
        return item.content
      })
      return arr
    },
    copyClick(data) {
      let text = ''
      data.map((item) => {
        this.getChains(item).map((v, i) => {
          if (v.type && v.type == 3) {
            if (i == item.length - 1 && v) {
              text += 'icon' + '\n'
            } else {
              text += 'icon' + '-->'
            }
          } else {
            if (i < item.length - 1 && v) {
              text += v.content + '-->'
            } else if (i == item.length - 1 && v) {
              text += v.content + '\n'
            } else {
              text += v.content
            }
          }
        })
      })
      this.$copyText(text).then(
        (res) => {
          console.log('复制成功：', res)
          this.$message.success('复制成功')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    // 全选方法
    checkAllFunc() {
      if (!this.radioParams) {
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
          })
        } else {
          this.$refs.eltable.clearSelection()
        }
      } else {
        if (this.radioParams && this.radioParams.id && this.radioParams.id.length > 0) {
          // 报告自定义回显
          this.tableData.forEach((row) => {
            this.radioParams.id.forEach((item) => {
              if (item == row.id) {
                this.$refs.eltable.toggleRowSelection(row, true)
              }
            })
          })
        } else {
          this.$refs.eltable.clearSelection()
        }
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectionChange(val) {
      this.checkedArr = val
      this.$emit('handleSelectionChange', val)
    },
    handleSelectable() {
      return !this.checkedAll
    },
    // getAssetsSource(name) {
    //   let source = ''
    //   if (name == 1) {
    //     source = 'FOFA的search/all接口返回'
    //   } else if (name == 2) {
    //     source = 'IP138'
    //   } else if (name == 3) {
    //     source = 'chaziyu'
    //   } else if (name == 4) {
    //     source = 'hunter'
    //   } else if (name == 5) {
    //     source = '线索库域名实时解析'
    //   } else if (name == 6) {
    //     source = 'FOFA纯解析域名数据'
    //   } else if (name == 7) {
    //     source = '非线索库的域名实时解析'
    //   } else if (name == 8) {
    //     source = '基础扫描'
    //   } else if (name == 9) {
    //     source = '单位测绘任务下的全端口扫描'
    //   } else if (name == 10) {
    //     source = '单位测绘下的常用端口扫描'
    //   } else if (name == 11) {
    //     source = 'oneforall'
    //   } else {
    //     source = '-'
    //   }
    //   return source
    // },
    getTableGeo(item, data, names) {
      if (item) {
        if (item[data]) {
          return item[data]
        } else {
          return '-'
        }
      } else {
        return '-'
      }
    },
    getTableItem(item) {
      if (Array.isArray(item)) {
        if (item.length == 0) {
          return '-'
        }
      }
      if (item) {
        if (Array.isArray(item)) {
          return item.join(',')
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    getCompany(item) {
      if (Array.isArray(item)) {
        if (item.length == '0') {
          return '-'
        }
      }
      if (item) {
        if (Array.isArray(item)) {
          return item.join(',')
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    getCloudName(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length == '0') {
            return ''
          } else {
            return item.join(',')
          }
        } else {
          // 字符串的数组需要单独处理
          if (Array.isArray(JSON.parse(JSON.stringify(item)))) {
            return JSON.parse(JSON.stringify(item)).join(',')
          } else {
            return String(item)
          }
        }
      } else {
        return ''
      }
    },
    // 地理位置
    async getprovinceData() {
      // let res = await this.areaIndex('')
      // this.provinceData = res.data
    },
    typeCell(val) {
      if (val == 1) {
        return '云端推荐'
      } else {
        return '资产扫描'
      }
    },
    score(data) {
      if (data >= 80) {
        return '高可信度'
      } else if (data >= 50) {
        return '中可信度'
      } else {
        return '低可信度'
      }
    },
    getTheatTypeName(name) {
      // 钓鱼仿冒、ICP盗用、黄赌毒网站、其他类型
      let type = ''
      if (name == 1) {
        type = '钓鱼仿冒'
      } else if (name == 2) {
        type = '黄赌毒网站'
      } else if (name == 3) {
        type = 'ICP盗用'
      } else if (name == 4) {
        type = '域名混淆'
      } else if (name == 0) {
        type = '其他类型'
      }
      return type
    }
  }
}
</script>
<style lang="less" scoped>
.con {
  position: relative;
  width: 100%;
  height: 100%;
}
.hostDetail {
  display: inline-block;
  vertical-align: middle;
  line-height: 32px !important;
  max-width: 160px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.hostDetailHas {
  max-width: 90px;
}
.columnBox {
  right: 1px !important;
}
.detail {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.yunBox {
  margin-left: 8px !important;
  white-space: nowrap;
  line-height: 16px;
  padding: 2px 5px;
  background: #ffffff;
  border-radius: 14px;
  border: 1px solid #d1d5dd;
}
.myOnlinecdn {
  padding: 2px 6px;
  background: rgba(255, 121, 0, 0.12);
  border-radius: 2px;
  border: 1px solid rgba(255, 121, 0, 1);
  color: rgba(255, 121, 0, 1);
  margin-left: 8px;
}

.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
