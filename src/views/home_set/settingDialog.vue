<template>
    <div>
      <el-dialog class="elDialogAdd" :close-on-click-modal="false" :visible.sync="dialogFormVisibleCon" width="24%">
        <template slot="title">
          管控平台设置
        </template>
        <div class="dialog-body">
          <el-form label-width="120px" :model="formLabelAlign" :rules="formLabelAlignRules" ref="ruleFormcon">
            <el-form-item label="设备ID" prop="mid">
                <el-input v-model="formLabelAlign.mid" :disabled="formLabelAlign.mid != ''"></el-input>
            </el-form-item>
            <el-form-item label="管控平台地址" prop="hosts">
                <el-input v-model.trim="formLabelAlign.hosts"></el-input>
            </el-form-item>
            <el-form-item label="授权码" prop="key">
                <el-input v-model.trim="formLabelAlign.key"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button class="highBtnRe" @click="dialogFormVisibleCon = false">关闭</el-button>
          <el-button class="highBtn">确定</el-button>
        </div>
      </el-dialog>
      <el-dialog class="elDialogAdd" :close-on-click-modal="false" :visible.sync="dialogFormVisibleUp" width="23%">
        <template slot="title">
          系统升级
        </template>
        <div class="dialog-body">
          <span>
            <img src="../../assets/images/online.png" alt="">
            <p>在线升级</p>
          </span>
          <el-divider direction="vertical"></el-divider>
          <span>
            <img src="../../assets/images/outline.png" alt="">
            <p>离线升级</p>
            <span style="color: #B8BCC4;margin-top: 4px;">上传程序升级包</span>
          </span>
        </div>
      </el-dialog>
      <el-dialog class="sysWrap" :close-on-click-modal="false" :show-close="false" :visible.sync="dialogFormVisibleSys">
        <template slot="title">
        </template>
        <div class="sysinfo">
          <p class="title">互联网资产攻击面管理平台</p>
          <img src="../../assets/images/sysEn.png" alt="">
          <div>
            <div class="left">
              <ul>
                <li v-for="item in sysLeftData" :key="item.prop"><span>{{item.name}}</span><span>{{item.prop}}</span></li>
              </ul>
              <div class="dialog-footer">
                <el-button class="highBtnRe" @click="dialogFormVisibleSys = false">
                  <span class="el-icon-refresh-left"></span>重启</el-button>
                <el-button class="highBtnRe"><span class="el-icon-switch-button"></span>确定</el-button>
              </div>
            </div>
            <div class="right">
              <ul>
                <li v-for="item in sysRightData" :key="item.prop">
                  <span><i :class="item.icon"></i>{{item.name}}</span>
                  <el-progress :text-inside="false" :stroke-width="36" :percentage="item.prop" :format="progressText"></el-progress>
                </li>
              </ul>
              <p>测试到期时间：<span>2022年8月15日</span></p>
            </div>
          </div>
        </div>
        <img class="sysBg" src="../../assets/images/sys.png" alt="">
      </el-dialog>
      <el-dialog class="elDialogAdd" :close-on-click-modal="false" :visible.sync="dialogFormVisibleResetPsd" width="24%">
        <template slot="title">
          修改密码<i class="el-icon-question" style="color: #C2CDE0;margin-left: 2px;"></i>
        </template>
        <div class="dialog-body">
          <el-form :model="ruleFormResetPsd" :rules="rulesResetPsd" ref="ruleFormResetPsd" label-width="80px" class="demo-ruleForm">
            <el-form-item label="KEY" prop="port">
              <el-input v-model="ruleFormResetPsd.port" placeholder="请输入分组名称"></el-input>
            </el-form-item>
            <el-form-item label="原密码" prop="pass">
              <el-input type="password" v-model="ruleFormResetPsd.pass" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="新密码" prop="pass">
              <el-input type="password" v-model="ruleFormResetPsd.pass" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="checkPass">
              <el-input type="password" v-model="ruleFormResetPsd.checkPass" autocomplete="off"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button class="highBtnRe" @click="dialogFormVisibleResetPsd = false">关闭</el-button>
          <el-button class="highBtn">确定</el-button>
        </div>
      </el-dialog>
      <el-dialog class="elDialogAdd" :close-on-click-modal="false" :visible.sync="dialogFormVisiblePsd" width="24%">
        <template slot="title">
          密码配置
        </template>
        <div class="dialog-body">
          <el-form :model="ruleFormResetPsd" :rules="rulesResetPsd" ref="ruleFormResetPsd" label-width="100px" class="demo-ruleForm">
            <el-form-item label="密码最短字符" prop="port">
              <el-input v-model="ruleFormResetPsd.port" placeholder="请输入分组名称">
                <template slot="append">个</template>
              </el-input>
            </el-form-item>
            <el-form-item label="密码有效时间" prop="pass">
              <el-input type="password" v-model="ruleFormResetPsd.pass" autocomplete="off">
                <template slot="append">天</template>
              </el-input>
            </el-form-item>
            <el-form-item label="密码锁定阈值" prop="pass">
              <el-input type="password" v-model="ruleFormResetPsd.pass" autocomplete="off">
                <template slot="append">次</template>
              </el-input>
            </el-form-item>
            <el-form-item label="密码锁定时间" prop="checkPass">
              <el-input type="password" v-model="ruleFormResetPsd.checkPass" autocomplete="off">
                <template slot="append">小时</template>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <!-- <el-button class="highBtnRe" @click="dialogFormVisiblePsd = false">关闭</el-button> -->
          <el-button class="highBtn">确定</el-button>
        </div>
      </el-dialog>
    </div>
</template>
<script>

export default {
  data () {
    return {
      sysLeftData: [
        {
          name: '产品型号',
          prop: 'foradar-1'
        },
        {
          name: '系统版本',
          prop: '2.23'
        },
        {
          name: 'PoC版本',
          prop: '22.21900'
        },
        {
          name: '管理上线',
          prop: '10000'
        },
        {
          name: '厂商信息',
          prop: ''
        },
        {
          name: '服务电话',
          prop: ''
        },
        {
          name: '服务邮箱',
          prop: ''
        },
        {
          name: '服务到期',
          prop: ''
        }
      ],
      sysRightData: [
        {
          icon: 'iconfont icon-a-IntelR',
          name: 'intel(R)',
          prop: 70
        },
        {
          icon: 'iconfont icon-neicun',
          name: '15.5G',
          prop: 0
        },
        {
          icon: 'iconfont icon-chucun',
          name: '322GB',
          prop: 20
        }
      ],
      ruleFormResetPsd: {

      },
      rulesResetPsd: {

      },
      formLabelAlign: {
        id: '',
        mid: "",
        hosts: "",
        key: ""
      },
      formLabelAlignRules: {
        mid: [{ required: true, message: '请输入设备ID', trigger: 'change' }],
        hosts: [
            { required: true, message: '请输入管控平台地址', trigger: 'change' },
            {
              required: true,
              message: "请输入正确的地址,格式127.0.0.1",
              pattern: /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/, // /^[0-9.:]+$/u,
              trigger: "blur"
            }
        ],
        key: [{ required: true, message: '请输入授权码', trigger: 'change' }] 
      },
    }
  }
}
</script>
