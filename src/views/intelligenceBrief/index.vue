<template>
  <div class="container">
    <div class="headerTitle">
      <div>情报概览</div>
    </div>
    <div class="home_header">
      <div class="top-info">
        <div class="left">
          <div class="title">
            <!-- 热点漏洞分布 -->
            <div class="text">最新热点漏洞</div>
          </div>
          <div class="content">
            <div class="item-box" v-if="hotListData && hotListData.length !== 0">
              <div class="item" v-for="item in hotListData" :key="item.id">
                <el-tooltip effect="dark" :content="item.name" placement="top">
                  <span class="tag">{{ item.name }}</span>
                </el-tooltip>
                <span class="count">{{ item.fofa_count }}</span>
                <el-tooltip effect="dark" :content="item.found_at" placement="top">
                  <span class="time">{{ item.found_at }}</span>
                </el-tooltip>
              </div>
            </div>
            <div v-else class="emptyClass" style="margin-left: 20%">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </div>
        </div>
        <div class="center">
          <div class="title"> 专项情报 </div>
          <div class="text">
            <svg class="icon svg-icon serial" aria-hidden="true">
              <use xlink:href="#icon-diamondNum"></use>
            </svg>
            <template v-if="eventListData && eventListData.length !== 0">
              {{
                eventListData[0]
                  ? eventListData[0].name +
                    '互联网影响面有' +
                    (eventListData[0].ip_count || 0) +
                    '个，披露时间为' +
                    eventListData[0].creation_time
                  : ''
              }}
            </template>
            <template v-else>暂无数据</template>
          </div>
          <img class="content" src="../../assets/images/intelligence/center-bg.png" alt="" />
        </div>
        <div class="right">
          <div class="title"> 专项情报统计 </div>
          <div class="content">
            <div class="module top">
              <span>
                <span class="module-img">
                  <img src="../../assets/images/intelligence/ip-count.png" alt="" />
                </span>
                <span class="module-text">
                  <span class="count">{{ eventCount.all_count || 0 }}</span>
                  <span class="title">互联网影响面总数</span>
                </span>
              </span>
              <span>
                <span class="module-img">
                  <img src="../../assets/images/intelligence/new-quantity.png" alt="" />
                </span>
                <span class="module-text">
                  <span class="count">{{ eventCount.latest_count || 0 }}</span>
                  <span class="title">最新新增</span>
                </span>
              </span>
            </div>
            <div class="module bottom">
              <div class="module-title">
                <svg class="icon svg-icon serial" aria-hidden="true">
                  <use xlink:href="#icon-diamondNum"></use>
                </svg>
                热点漏洞等级统计
              </div>
              <div class="progress-module">
                <div class="progress-block" v-for="(item, index) in progressItems" :key="index">
                  <div class="type">
                    <span class="type-text">{{ item.label }}</span>
                    <span class="type-count">{{ dataCount[item.value].count }}</span>
                  </div>
                  <el-progress
                    :percentage="Number(dataCount[item.value].percent.toFixed(2))"
                    :show-text="false"
                    :color="item.color"
                  ></el-progress>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom-content">
        <div class="tab-modules">
          <div
            class="tab-pane"
            :class="{ active: item.value == activeItem.value }"
            v-for="item in tabList"
            :key="item.value"
            @click="handleClick(item)"
          >
            <img :src="require(`../../assets/images/intelligence/${item.icon}.png`)" alt="" />
            <span class="text">
              &nbsp;&nbsp;{{ item.label }}({{ typeCount[item.countName] }})
            </span>
          </div>
        </div>
        <div class="tip">
          <span class="tip-text">
            <svg class="icon svg-icon" aria-hidden="true">
              <use xlink:href="#icon-tip"></use>
            </svg>
            本系统通过互联网进行风险检查，为了确保全网安全，建议使用FOEYE进行一次深度内网攻击面扫描，全网排查风险隐患，提高安全防御力
          </span>
        </div>
        <div class="table" v-loading="pageLoading">
          <component
            :scopes="scopes"
            :userInfo="userInfo"
            :user="user"
            :is="activeItem.components"
            :currentCompany="currentCompany"
            :pageType="pageType"
            @updatePageLoading="updatePageLoading"
            @jumpToSummaryPageFn="jumpToSummaryPage"
          ></component>
        </div>
        <div class="moreModule" @click="jumpToSummaryPage(activeItem.components)">
          查看全部情报
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-more-right"></use>
          </svg>
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-more-right"></use>
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import tableList from '../intelligenceCenterv1/table.vue'
import hot from '../intelligenceCenterv1/hot.vue'
import pinishing from '../intelligenceCenterv1/pinishing.vue'
import risk from '../intelligenceCenterv1/risk.vue'
import other from '../intelligenceCenterv1/other.vue'
import special from '../intelligenceCenterv1/special.vue'
import dataLeak from '../intelligenceCenterv1/dataLeak.vue'
import {
  eventIpCount,
  hotLevelCount,
  eventList,
  intelligenceTypeCount,
  hotList
} from '@/api/apiConfig/api.js'

export default {
  components: {
    tableList,
    hot,
    pinishing,
    risk,
    other,
    special,
    dataLeak
  },
  data() {
    return {
      pageLoading: false,
      pageType: 'brief',
      userInfo: {},
      user: {},
      scopes: [],
      eventListData: [],
      hotListData: [],
      progressItems: [
        { value: 'deepRed', color: 'rgba(153, 5, 4, 1)', label: '严重' },
        { value: 'red', color: 'rgba(255, 70, 70, 1)', label: '高危' },
        { value: 'origin', color: 'rgba(255, 121, 0, 1)', label: '中危' },
        { value: 'yellow', color: 'rgba(248, 193, 54, 1)', label: '低危' }
      ],
      tabList: [
        {
          value: 'data',
          countName: 'Data',
          label: '数据情报',
          icon: 'data',
          components: 'dataLeak'
        },
        { value: 'hot', countName: 'HotPoc', label: '热点漏洞', icon: 'hot', components: 'hot' },
        {
          value: 'punishing',
          countName: 'Fake',
          label: '钓鱼仿冒',
          icon: 'punishing',
          components: 'pinishing'
        },
        { value: 'risk', countName: 'Threat', label: '风险情报', icon: 'risk', components: 'risk' },
        {
          value: 'special',
          countName: 'Event',
          label: '专项情报',
          icon: 'special',
          components: 'special'
        },
        {
          value: 'other',
          countName: 'Other',
          label: '其他情报',
          icon: 'other',
          components: 'other'
        }
      ],
      activeItem: {
        value: 'data',
        count: '120',
        label: '数据情报',
        icon: 'data',
        components: 'dataLeak'
      },
      eventCount: {
        all_count: 0,
        latest_count: 0
      },
      dataCount: {
        deepRed: {
          count: 0,
          percent: 0,
          level: ''
        },
        red: {
          count: 0,
          percent: 0,
          level: ''
        },
        origin: {
          count: 0,
          percent: 0,
          level: ''
        },
        yellow: {
          count: 0,
          percent: 0,
          level: ''
        }
      },
      typeCount: {}
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    jumpToSummaryPage(actTabVal, keyword) {
      this.changeMenuId('10-3')
      sessionStorage.setItem('menuId', '10-3')
      this.$router.push({ path: '/intelligenceCenterv1', query: { actTabVal, keyword } })
    },
    handleClick(item) {
      this.activeItem = item
    },
    updatePageLoading(val) {
      this.pageLoading = val
    },
    async getEventIpCount() {
      let res = await eventIpCount({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.eventCount.all_count = res.data.all_count
        this.eventCount.latest_count = res.data.latest_count
      }
    },
    async geHotLevelCount() {
      let res = await hotLevelCount({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        res.data.items.forEach((item) => {
          if (item.level == '严重') {
            this.dataCount.deepRed = item
          } else if (item.level == '高危') {
            this.dataCount.red = item
          } else if (item.level == '中危') {
            this.dataCount.origin = item
          } else if (item.level == '低危') {
            this.dataCount.yellow = item
          }
        })
      }
    },
    async getEventList() {
      let res = await eventList({
        page: 1,
        per_page: 5,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.eventListData = res.data.items
      }
    },
    async getHotList() {
      let res = await hotList({
        page: 1,
        per_page: 5,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.hotListData = res.data.items || []
      }
    },
    async getIntelligenceTypeCount() {
      let res = await intelligenceTypeCount({
        page: 1,
        per_page: 5,
        operate_company_id: this.currentCompany
      })
      res.data.items.forEach((item) => {
        this.$set(this.typeCount, item.module, item.count || 0)
      })
    }
  },
  mounted() {
    // this.pageType = this.$route.meta.type || 'center'
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      this.scopes = this.user.scopes
    }
    this.getEventIpCount()
    this.geHotLevelCount()
    this.getIntelligenceTypeCount()
    this.getHotList()
    this.getEventList()
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, #ffffff 63%, rgba(255, 255, 255, 0) 100%),
    linear-gradient(100deg, #dfeafe 0%, #fafcff 48%, #dfeafe 93%);
  box-shadow:
    0px 2px 6px 0px rgba(0, 0, 0, 0.08),
    inset 0px 0px 62px 0px rgba(0, 42, 112, 0.08);

  /deep/.home_header {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 2px solid #ffffff;
  }
}

.top-info {
  display: flex;
  width: 100%;
  aspect-ratio: 50/11;

  .left {
    flex: 3;
    background: url('../../assets/images/intelligence/left-bg.png') no-repeat;
    background-size: 100% 100%;

    .title {
      width: 121px;
      height: 42px;
      box-shadow: 0px -2px 10px 0px rgba(0, 30, 80, 0.06);

      .text {
        position: relative;
        width: 121px;
        height: 56px;
        line-height: 42px;
        padding-left: 14px;
        margin-top: -2px;
        margin-left: -2px;
        font-weight: 800;
        color: #2677ff;
        background: url('../../assets/images/intelligence/top-left-text-bgc.png') no-repeat 100%
          100%;
        background-size: 100% 100%;
      }
    }
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      height: calc(100% - 45px);
      .item-box {
        width: 65%;
      }
      .item {
        display: flex;
        // justify-content: space-between;
        height: 28px;
        margin-bottom: 8px;
        padding: 0 12px;
        color: #37393c;
        font-size: 12px;
        line-height: 28px;
        border-radius: 4px;
        background: linear-gradient(
          180deg,
          rgba(237, 244, 255, 0.7) 0%,
          rgba(248, 251, 255, 0.7) 100%
        );
        border: 1px solid #deeaff;
        backdrop-filter: blur(8px);
        box-shadow:
          0px 2px 6px 0px rgba(0, 30, 80, 0.06),
          inset 0px 0px 20px 0px rgba(0, 62, 167, 0.08);
        .tag {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .count {
          flex: 1;
          // width: 60px;
          color: #2677ff;
          overflow: hidden;
          white-space: nowrap;
          -o-text-overflow: ellipsis;
          text-overflow: ellipsis;
        }
        .time {
          flex: 1;
          // width: 128px;
          color: #62666c;
          overflow: hidden;
          white-space: nowrap;
          -o-text-overflow: ellipsis;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .center {
    flex: 4;
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-left: 22px;
    margin-right: 29px;
    position: relative;

    .title {
      width: 134px;
      height: 56px;
      margin-top: -2px;
      line-height: 42px;
      font-weight: 800;
      color: #2677ff;
      background: url('../../assets/images/intelligence/top-center-text-bgc.png') no-repeat 100%
        100%;
      background-size: 100% 100%;
      text-align: center;
    }

    .text {
      height: 34px;
      font-size: 12px;
      color: rgba(98, 102, 108, 1);

      .icon {
        font-size: 6px;
        vertical-align: 0.3em;
        color: #2677ff;
      }
    }

    .content {
      position: absolute;
      width: 100%;
      height: calc(100% - 76px);
      bottom: -11%;
    }
  }

  .right {
    flex: 3;
    position: relative;
    padding-top: 61px;

    & > .title {
      position: absolute;
      right: 0;
      top: 0;
      width: 121px;
      height: 56px;
      margin-top: -2px;
      margin-right: -2px;
      text-align: right;
      padding-right: 14px;
      font-weight: 800;
      line-height: 42px;
      color: #2677ff;
      background: url('../../assets/images/intelligence/top-right-text-bgc.png') no-repeat 100% 100%;
      background-size: 100% 100%;
      // clip-path: polygon(0 0, 100% 0, 100% 100%, 31px 100%);
      // border-radius: 0.3em 0 0 0.3em;
      // background: linear-gradient(
      //     56deg,
      //     #fff 25px,
      //     rgba(225, 234, 250, 0.108) 0
      //   )
      //   bottom left;
      // border: solid 2px #fff;
      // box-shadow: 0px -2px 10px 0px rgba(0, 30, 80, 0.06);
      // backdrop-filter: blur(8px);
    }

    .content {
      padding-right: 20px;
      .module {
        display: flex;

        &:last-child {
          margin-top: 20px;
        }

        & > span {
          flex: 1;
          &:first-child {
            margin-right: 16px;
          }
        }

        .module-img {
          float: left;
          width: 46px;
          height: 46px;
          margin-right: 12px;
          border-radius: 4px;
          background: rgba(38, 119, 255, 0.08);
          text-align: center;
          line-height: 46px;

          img {
            width: 30px;
            vertical-align: middle;
          }
        }

        .module-text {
          height: 46px;
          display: flex;
          justify-content: space-between;
          flex-direction: column;

          span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .count {
            font-size: 20px;
            font-weight: 600;
          }

          .title {
            font-size: 12px;
            color: #62666c;
          }
        }

        .module-title {
          height: 20px;
          line-height: 20px;
          color: #37393c;
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 8px;

          .icon {
            font-size: 8px;
            color: #2677ff;
          }
        }

        &.bottom {
          flex-direction: column;
        }

        .progress-module {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          .progress-block {
            box-sizing: border-box;
            // float: left;
            width: 48%;
            padding: 8px 12px;
            margin-bottom: 12px;
            border-radius: 4px;
            background: linear-gradient(180deg, #edf4ff 0%, #f8fbff 100%);
            border: 1px solid #deeaff;

            &:nth-child(2n) {
              margin-left: 4%;
            }

            .type {
              display: flex;
              justify-content: space-between;
              align-items: center;
              height: 20px;
              line-height: 20px;
              margin-bottom: 8px;

              .type-text {
                font-size: 12px;
                color: #62666c;
              }

              .type-count {
                font-size: 14px;
                color: #ff4646;
              }
            }
          }
        }
      }
    }
  }
}

.bottom-content {
  position: relative;

  flex: 1;
  padding: 15px 20px 0;

  .tab-modules {
    display: flex;
    width: 100%;
    height: 48px;
    border: 2px solid #ffffff;
    border-radius: 4px;
    background: rgba(249, 250, 253, 0.4);
    box-shadow:
      0px 2px 6px 0px rgba(0, 0, 0, 0.08),
      inset 0px 0px 32px 0px rgba(0, 68, 181, 0.12);

    .tab-pane {
      flex: 1;
      color: #62666c;
      line-height: 48px;
      text-align: center;
      cursor: pointer;

      img {
        width: 20px;
        vertical-align: middle;
      }
      .text {
        vertical-align: middle;
        display: inline-block;
        height: 20px;
        line-height: 20px;
      }

      &.active {
        color: #2677ff;
        font-weight: 600;
        background: rgba(0, 39, 103, 0.06);
      }
    }
  }
  .tip {
    text-align: center;
    .tip-text {
      display: inline-block;
      height: 28px;
      line-height: 28px;
      margin: 18px 0;
      padding: 0 8px;
      border-radius: 4px;
      color: #62666c;
      background: #ebeef5;
    }
  }
  .table {
    height: calc(100% - 134px);
    /deep/.tableList {
      height: 100%;
      .tableComponent {
        height: 100%;
      }
      .tableContainer {
        height: 100%;
      }
      .tableWrap {
        height: calc(100% - 12px);
      }
    }
  }
  .moreModule {
    box-sizing: border-box;
    z-index: 99;
    color: #2677ff;
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 122px;
    padding-top: 80px;
    margin: 0 -20px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 64%);
    text-align: center;
    cursor: pointer;
    .icon {
      height: 12px;
      width: 6px;
      margin-right: 0;
    }
  }
}
.emptyClass {
  height: 80%;
  text-align: center;
  vertical-align: middle;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  svg {
    display: inline-block;
    font-size: 120px;
    // margin-top: 25%;
  }

  p {
    line-height: 25px;
    color: #d1d5dd;

    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
