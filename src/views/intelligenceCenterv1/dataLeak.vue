<template>
  <div class="tableList">
    <div class="filterTab" v-if="pageType != 'brief'">
      <div class="filterInput">
        <el-input
          v-model="formInline.keyword"
          placeholder="请输入数据情报名称检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <span @click="highCheckdialog = true" id="user_filter" style="width: 80px"
          ><img
            src="../../assets/images/filter.png"
            alt=""
            style="width: 16px; vertical-align: middle; margin-right: 3px"
          />高级筛选</span
        >
      </div>
      <div>
        <el-button
          class="normalBtnRe"
          v-if="pageType != 'manage'"
          style="margin-left: 10px"
          :disabled="checkBtnLoading || progressLoading"
          :loading="progressLoading"
          @click="oneKeyCheck([])"
          type="primary"
          >{{ progressLoading ? progress + '%' : '' }}&nbsp;一键检测</el-button
        >
      </div>
    </div>
    <hightFilter
      id="hightFilter"
      :highTabShow="highTabShow"
      :highlist="highlist"
      :total="total"
      @highcheck="highCheck"
    >
    </hightFilter>
    <div :class="hightFilterIsShow()">
      <tableList
        :pageType="pageType"
        :tableHeader="tableHeader"
        :tableData="tableData"
        @updateList="updateList"
        :total="total"
      >
        <template v-slot:judge="{ row, itemName }">
          <span v-if="itemName == 'entities' && row[itemName]">
            {{ row[itemName].replace(/\,/g, '&nbsp;&nbsp;，') || '-' }}
          </span>
          <span v-else>
            {{ row[itemName] || '-' }}
          </span>
        </template>
        <template slot="other">
          <el-table-column fixed="right" label="操作" align="left" :width="operationWidth">
            <template slot-scope="{ row, $index }">
              <template v-if="pageType != 'brief'">
                <el-button
                  v-if="pageType != 'manage'"
                  type="text"
                  size="small"
                  :disabled="checkBtnLoading || progressLoading"
                  :loading="checkBtnLoading && currentIndex == $index"
                  @click="oneKeyCheck([row.id], $index)"
                  >检测</el-button
                >
                <el-button type="text" size="small" @click="getDetail(row)">详情</el-button>
                <el-button
                  v-if="pageType != 'manage'"
                  type="text"
                  size="small"
                  @click="jumpToAssetsRelated(row)"
                  >相关资产</el-button
                >
              </template>
              <template v-else>
                <el-button type="text" size="small" @click="jumpToSummaryPage(row)">详情</el-button>
              </template>
            </template>
          </el-table-column>
        </template>
      </tableList>
    </div>
    <!-- 详情 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      @close="closeDetail()"
      :visible.sync="detailDialogFormVisible"
      width="880px"
    >
      <template slot="title"> 数据情报详情 </template>
      <div class="dialog-body" style="max-height: 813px">
        <div class="top High">
          <div class="left">
            <img :src="require(`../../assets/images/riskHigh.png`)" alt="" />
          </div>
          <div class="right">
            <div class="title">{{ currentRow.special_project_name }}</div>
          </div>
        </div>
        <div class="middleLine"></div>
        <div class="bottom">
          <div class="ipTable">
            <div class="title"
              >涉及企业
              <span class="tip">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tip"></use>
                </svg>
                此处展示的内容是和用户关联企业相关的信息
              </span>
            </div>
            <div class="content">
              <tableList
                :tableHeader="ipTableHeader"
                :tableData="ipTableData"
                @updateList="updateIpList"
                :total="ipTotal"
              >
                <template v-slot:judge="{ row, itemName }">
                  <span v-if="itemName == 'operation'">
                    <el-button
                      v-if="pageType && pageType == 'manage'"
                      type="text"
                      size="small"
                      @click="uploadReport(row)"
                      >上传报告</el-button
                    >
                    <el-button
                      type="text"
                      size="small"
                      @click="reportPreview(row)"
                      :disabled="pageType && pageType != 'manage' && !row.has_masked_report"
                      >查看报告</el-button
                    >
                  </span>
                  <span v-else>
                    {{ row[itemName] || '-' }}
                  </span>
                </template>
              </tableList>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <uploadReport
      :addDialogFormVisible="addDialogFormVisible"
      :action="`${golangUploadSrcIp}/intelligence/data/${dataId}/report/upload`"
      :dataId="dataId"
      @close="addDialogFormVisible = false"
    />
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="企业名称：" prop="entities">
            <el-select
              v-model="formInline.entities"
              placeholder="请选择企业名称"
              multiple
              collapse-tags
              @change="selectChange($event, 'entities', conditionList.entities, false, true)"
            >
              <el-option
                :label="v"
                :value="v"
                v-for="v in conditionList.entities"
                :key="v"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="情报名称：" prop="special_project_name">
            <el-select
              v-model="formInline.special_project_name"
              placeholder="请选择情报名称"
              multiple
              collapse-tags
              @change="
                selectChange(
                  $event,
                  'special_project_name',
                  conditionList.special_project_name,
                  false,
                  true
                )
              "
            >
              <el-option
                :label="v"
                :value="v"
                v-for="v in conditionList.special_project_name"
                :key="v"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="更新时间：" prop="last_update_time">
            <el-date-picker
              v-model="formInline.last_update_time"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="user_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="user_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  dataListCondition,
  dataListDetailManage,
  dataListManage,
  dataListDetail,
  dataListMatch,
  dataList,
  dataListMatchProgress,
  dataListReport,
  dataListMaskedReport
} from '@/api/apiConfig/api.js'

import tableList from './table.vue'
import uploadReport from './uploadReport.vue'
import hightFilter from '../../components/assets/highTab.vue'

export default {
  components: {
    tableList,
    uploadReport,
    hightFilter
  },
  props: {
    scopes: {
      type: Array,
      default: () => []
    },
    userInfo: {
      type: Object,
      default: () => ({})
    },
    user: {
      type: Object,
      default: () => ({})
    },
    currentCompany: {
      type: [String, Number],
      default: ''
    },
    pageType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      highTabShow: [
        {
          label: '企业名称',
          name: 'entities',
          type: 'select'
        },
        {
          label: '情报名称',
          name: 'special_project_name',
          type: 'select'
        },
        {
          label: '更新时间',
          name: 'last_update_time',
          type: 'date'
        }
      ],
      highlist: null,
      highCheckdialog: false,
      ipSearchParams: {
        page: 1,
        per_page: 10
      },
      ipTableData: [],
      ipTotal: 0,
      ipTableHeader: [
        {
          label: '涉及企业名称',
          name: 'data_entity',
          fixed: 'left',
          minWidth: 140
        },
        {
          label: '更新时间',
          name: 'disclosure_time',
          fixed: 'left',
          minWidth: 140
        },
        {
          label: '专项分类',
          name: 'special_project_category',
          fixed: 'left',
          minWidth: 100
        },
        {
          label: '泄漏原因',
          name: 'title',
          fixed: 'left',
          minWidth: 100
        },
        {
          label: '操作',
          name: 'operation',
          fixed: 'left',
          minWidth: this.pageType != 'manage' ? 100 : 180
        }
      ],
      currentIndex: null,
      progress: 0,
      progressLoading: false,
      checkBtnLoading: false,
      uploadPath: '', // 数据泄露的上传后路径
      btnLoading: false,
      uploadAction: '',
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      evaluatePictureList: [],
      isPublicMap: {
        1: '公开',
        2: '非公开'
      },
      detailDialogFormVisible: false,
      formInline: {
        keyword: ''
      },
      currentRow: {},
      addDialogFormVisible: false,
      searchParams: {
        page: 1,
        per_page: 10
      },
      total: 0,
      tableData: [],
      // 数据情报
      tableHeader: [
        {
          label: '数据情报名称',
          name: 'special_project_name',
          fixed: 'left',
          minWidth: 180
        },
        {
          label: '涉及企业数量',
          name: 'company_num',
          minWidth: 100
        },
        {
          label: '全部涉及企业',
          name: 'entities',
          minWidth: 100
        },
        {
          label: '互联网影响面',
          name: 'data_volume',
          minWidth: 100
        },
        {
          label: '影响资产事件数量',
          name: 'asset_num',
          minWidth: 90
        },
        {
          label: '最新更新时间',
          name: 'last_update_time',
          minWidth: 100
        }
      ],
      dataId: '',
      updateTimer: null,
      conditionList: {}
    }
  },
  mounted() {
    if (this.$route.query.keyword) {
      let { keyword } = this.$route.query
      this.formInline.keyword = keyword
      Object.assign(this.searchParams, { page: 1, per_page: 10, ...this.formInline })
    }

    if (this.user.role == 2 && !this.currentCompany) {
      return
    }

    this.getData()
    this.getDataListCondition()
    if (this.pageType && this.pageType != 'manage') {
      this.updateProgress(true)
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getData()
        this.getDataListCondition()
        if (this.pageType && this.pageType != 'manage') {
          this.updateProgress(true)
        }
      }
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany']),
    operationWidth() {
      let widthMap = {
        brief: '90px',
        manage: '90px'
      }
      return widthMap[this.pageType] || '190px'
    }
  },
  methods: {
    async getDataListCondition() {
      let res = await dataListCondition({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.conditionList = res.data
      }
    },
    jumpToSummaryPage(row) {
      this.$emit('jumpToSummaryPageFn', 'dataLeak', row.special_project_name || '')
    },
    jumpToAssetsRelated(row) {
      this.$emit('jumpToAssetsRelated', 'dataLeak', row.special_project_name || '')
    },
    async oneKeyCheck(id = [], index) {
      this.currentIndex = index
      if (id && id.length !== 0) {
        this.checkBtnLoading = true
        this.progressLoading = false
      } else {
        this.progressLoading = true
        this.checkBtnLoading = false
      }
      let res = await dataListMatch({
        id,
        operate_company_id: this.currentCompany,
        ...this.searchParams
      }).catch(() => {
        this.progressLoading = false
        this.checkBtnLoading = false
      })
      if (res.code == 0) {
        if (id && id.length !== 0) {
          // 单个检测
          this.progressLoading = false
          this.checkBtnLoading = true
          setTimeout(() => {
            this.$message.success('一键检测已完成')
            this.checkBtnLoading = false
            this.getData()
          }, 2000)
        } else {
          // 全部检测
          this.progressLoading = true
          this.checkBtnLoading = false
          setTimeout(() => {
            this.updateProgress()
          }, 500)
        }
      } else {
        this.progressLoading = false
        this.checkBtnLoading = false
      }
    },
    async updateProgress(isNotMsg) {
      let res = await dataListMatchProgress({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        let { progress = 0 } = res.data
        if ((progress && progress < 100) || progress === 0) {
          this.progress = progress
          this.progressLoading = true
          this.checkBtnLoading = false
          this.updateTimer = setTimeout(() => {
            this.updateProgress()
          }, 2000)
        } else {
          this.checkBtnLoading = false
          this.progressLoading = false
          this.progress = 0
          if (!isNotMsg) {
            this.$message.success('一键检测已完成')
          }
          if (this.updateTimer) {
            clearTimeout(this.updateTimer)
          }

          if (!isNotMsg) {
            setTimeout(() => {
              this.getData()
            }, 1000)
          }
        }
      }
    },
    uploadReport(row) {
      this.dataId = row.id
      this.addDialogFormVisible = true
    },
    async reportPreview(row) {
      let funcName = null
      if (this.pageType && this.pageType == 'manage') {
        // 原始
        funcName = dataListReport
      } else {
        // 加密
        funcName = dataListMaskedReport
      }

      let res = await funcName({ data_id: row.id, operate_company_id: this.currentCompany })
      this.judgeUpload(res)
    },
    judgeUpload(res, fileName) {
      var reader = new FileReader()
      reader.readAsText(res)
      const _this = this
      reader.addEventListener('loadend', function (e) {
        console.log(e.target.result, '---e.target.result')
        if (e.target.result.indexOf('"code":400') != -1) {
          let result = JSON.parse(e.target.result)
          _this.$message({
            message: result.message,
            type: 'error'
          })
        } else {
          const href = window.URL.createObjectURL(
            new Blob([res], {
              type: 'application/pdf'
            })
          )
          window.open(href)
        }
      })
    },
    clickevaluatePicture(url) {
      var srclist = []
      srclist.push(this.showSrcIp + url)
      this.evaluatePictureList = srclist // 赋值
    },
    updateIpList(data) {
      let ipSearchParams = JSON.parse(JSON.stringify(this.ipSearchParams))
      this.ipSearchParams = Object.assign(ipSearchParams, data)
      this.getCompanyDetail()
    },
    getDetail(row) {
      this.currentRow = JSON.parse(JSON.stringify(row))
      this.detailDialogFormVisible = true

      this.getCompanyDetail()
    },
    getCompanyDetail() {
      if (this.pageType != 'manage') {
        this.ipSearchParams.operate_company_id = this.currentCompany
      } else {
        this.ipSearchParams.operate_company_id = ''
      }
      this.ipTableData = []
      let funcName =
        this.pageType && this.pageType == 'manage' ? dataListDetailManage : dataListDetail
      funcName({ dataSummaryId: this.currentRow.id, ...this.ipSearchParams }).then((res) => {
        this.ipTableData = res.data.items || []
        this.ipTotal = res.data.total || 0
      })
    },
    closeDetail() {
      this.detailDialogFormVisible = false
      this.ipSearchParams = {
        page: 1,
        per_page: 10
      }
      this.ipTableData = []
      this.ipTotal = 0
    },
    highCheck(data) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
      // this.updateList({page:1,per_page:10,...this.formInline})
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline)
      this.hightFilterIsShow()
      this.updateList({ page: 1, per_page: 10, ...this.formInline })
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableComponent tableComponentFilter'
      } else {
        bol = 'tableComponent'
      }
      return bol
    },
    download(URL) {
      window.location.href = this.showSrcIp + URL
      this.$message.success('下载成功')
    },
    updateList(data) {
      let searchParam = JSON.parse(JSON.stringify(this.searchParams))
      this.searchParams = Object.assign(searchParam, data)
      this.getData()
    },
    async getData(data) {
      this.$emit('updatePageLoading', true)
      let funcName = this.pageType && this.pageType == 'manage' ? dataListManage : dataList
      let res = await funcName({
        ...this.searchParams,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.$emit('updatePageLoading', false)
      })
      if (res.code == 0) {
        this.tableData = res.data.items || []
        this.total = res.data.total || 0
      } else {
        this.$message.error(res.message)
      }
      this.$emit('updatePageLoading', false)
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.tableList {
  height: 100%;
}
/deep/.el-image {
  position: relative;
  overflow: visible;
  display: block;
}
.imgCardContainer {
  position: relative;
}
.imgKongBtn {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto 0;
  left: calc(100% + 10px);
  // z-index: 9999;
}
.imgCardBox {
  height: 30px;
  // width: 100%;
  // height: 100%;
  /deep/.image-slot {
    height: 100%;
    width: 100%;
    color: #606266;
    background: #e9ebef;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    text-align: center;
    svg {
      display: inline-block;
      font-size: 25px;
    }

    p {
      width: 100%;
      line-height: 25px;
      color: #d1d5dd;
      span {
        margin-left: 4px;
        color: #2677ff;
      }
      .el-button {
        padding: 0;
      }
    }
  }
}

.clearfix::after {
  content: '';
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}

.screenshot {
  width: 200px;
  height: 200px;
  position: relative;
  /deep/.el-image {
    width: 100%;
    height: 100%;
  }
}
.download {
  span {
    margin-left: 20px;
    &:hover {
      cursor: pointer;
      color: #2677ff;
    }
  }
}
/deep/.elDialogAdd {
  .el-dialog__body {
    padding: 0;
    max-height: 613px;
  }

  /deep/.dialog-item {
    margin-top: 13px;
    margin-left: 28px;
  }

  .top {
    box-sizing: border-box;
    display: flex;
    height: 157px;
    padding: 20px 24px;

    .left {
      width: 110px;
      margin-right: 16px;
    }

    .right {
      width: 0;
      flex: 1;
      display: flex;
      justify-content: center;
      flex-direction: column;

      .title {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
      }

      .levelDiv {
        box-sizing: border-box;
        width: 86px;
        padding: 4px 12px;
        border-radius: 14px;
      }

      .level {
        display: inline-block;
        margin-right: 4px;

        span {
          display: inline-block;
          width: 6px;
          height: 10px;
          background: #eaceba;
          transform: skewX(-25deg);
          margin-right: 1px;
        }
      }
    }
    &.High {
      background: linear-gradient(180deg, #fff3f3 0%, #ffffff 100%), #ffffff;

      .levelDiv {
        border: 1px solid #ff4646;
        color: #ff4646;
        background: rgba(255, 70, 70, 0.12);

        .level {
          span:nth-child(-n + 3) {
            background: #ff4646;
          }
        }
      }
    }
  }

  .middleLine {
    margin: 0 20px;
    height: 1px;
    width: calc(100% - 40px);
    background: #e3e5ea;
  }

  .bottom {
    padding: 20px 20px 24px;

    .title {
      margin-bottom: 12px;

      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 4px;
        margin-right: 4px;
        transform: rotate(135deg);
        background-color: #2677ff;
        vertical-align: middle;
      }
    }

    .base {
      margin-bottom: 12px;

      .item {
        width: 50%;
        float: left;
        margin-bottom: 12px;

        &.one {
          width: 100%;
        }

        .label {
          color: rgba(98, 102, 108, 0.6);
        }

        .value {
          color: #37393c;
        }
      }
    }

    .solution {
      .content {
        border-radius: 4px;
        box-sizing: border-box;
        height: 96px;
        padding: 16px;
        background-color: #f5f8fc;

        div:first-child {
          margin-bottom: 12px;
        }
      }
    }

    .effect {
      margin-top: 24px;

      .content {
        border-radius: 4px;
        box-sizing: border-box;
        padding: 16px;
        background-color: #f5f8fc;

        div:first-child {
          margin-bottom: 12px;
        }
      }
    }

    .ipTable {
      margin-top: 24px;
      .title .tip {
        .icon {
          color: #37393c;
        }
        display: inline-block;
        height: 22px;
        line-height: 22px;
        padding-left: 8px;
        padding-right: 12px;
        border-radius: 2px;
        font-size: 12px;
        color: #62666c;
        background-color: #ebeef5;
      }
      .content {
        height: 570px;
      }
    }
  }
}
/deep/.el-tooltip__popper {
  line-height: 30px;
}
.tableComponentFilter {
  height: calc(100% - 62px) !important;
}
</style>
