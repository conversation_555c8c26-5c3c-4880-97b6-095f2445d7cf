<template>
  <el-dialog class="elDialogAdd" :close-on-click-modal="false" :visible.sync="dialogVisible" :width="dialogWidth"
    @close="close">
    <template slot="title">
      {{ title }}
    </template>
    <div class="search_input">
      <el-form class="clearfix" :size="size" inline :model="addForm" :rules="rules" ref="FormData"
        :label-width="labelWidth">
        <div class="item" :class="[item.type,item.className]" v-for="(item,key) in FormData" :key="key">
          <el-form-item :label="item.label" :key="item.prop" :prop="item.prop">
            <!-- 输入框 -->
            <el-input v-if="item.type === 'Input'" v-model="addForm[item.prop]"
              :placeholder="item.placeholder"></el-input>
            <el-input v-if="item.type === 'number'" type="number" v-model.number="addForm[item.prop]"
              :placeholder="item.placeholder"></el-input>
            <el-input v-if="item.type === 'textarea'" v-model="addForm[item.prop]" :placeholder="item.placeholder"
              :maxlength="item.maxlength" :show-word-limit="item.showWordLimit" type="textarea"></el-input>
            <!-- 密码框 -->
            <el-input v-if="item.type === 'password'" v-model="addForm[item.prop]" :placeholder="item.placeholder"
              auto-complete="off" show-password></el-input>
            <!-- 滑块 -->
            <el-slider v-if="item.type === 'Slider'" v-model="addForm[item.prop]"></el-slider>
            <tagsInput v-if="item.type === 'tagsInput'" ref="tagsInput" :parentTags="addForm[item.prop]" @onChange="(val)=>changeDefaultTag(item.prop,val)"></tagsInput>

            <!-- 单选 -->
            <el-radio-group v-if="item.type === 'Radio'" v-model="addForm[item.prop]">
              <el-radio v-for="ra in item.radios" :label="ra.value" :key="ra.value">{{ ra.label }}</el-radio>
            </el-radio-group>

            <!-- 组合单选按钮 -->
            <el-radio-group v-if="item.type === 'RadioRadio'" v-model="addForm[item.prop]"
              @change="item.change && item.change(addForm[item.prop])">
              <el-radio-button v-for="ra in item.radios" :label="ra.value" :key="ra.value">{{ ra.label
                }}</el-radio-button>
            </el-radio-group>

            <!-- 复选框 -->
            <el-checkbox-group v-if="item.type === 'Checkbox'" v-model="addForm[item.prop]">
              <el-checkbox v-for="ch in item.checkboxs" :label="ch.value" :key="ch.value">{{ ch.label }}</el-checkbox>
            </el-checkbox-group>

            <!-- 日期 -->
            <el-date-picker v-if="item.type === 'Date'" v-model="addForm[item.prop]" value-format="yyyy-MM-dd"
              @change="item.change(addForm[item.prop])" :placeholder="item.placeholder"></el-date-picker>

            <!-- 时间 -->
            <el-time-select v-if="item.type === 'Time'" v-model="addForm[item.prop]"></el-time-select>

            <!-- 日期时间 -->
            <el-date-picker v-if="item.type === 'DateTime'" type="datetime" v-model="addForm[item.prop]"
              :placeholder="item.placeholder" value-format="yyyy-MM-dd HH:mm:ss"
              :disabled="item.disable && item.disable(addForm[item.prop])"
              @change="item.change(addForm[item.prop])"></el-date-picker>

            <!-- 起止时间 -->
            <el-date-picker v-if="item.type === 'Daterange'" v-model="addForm[item.prop]" type="daterange"
              start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
              @change="item.change(addForm[item.prop])"></el-date-picker>

            <!-- 开关 -->
            <el-switch v-if="item.type === 'Switch'" v-model="addForm[item.prop]" :active-value="item.activeVal"
              :inactive-value="item.inactiveVal"></el-switch>
            <!-- 下拉框 -->
            <!-- $forceUpdate() 下拉刷新,修复数据改变下拉框不变的bug -->
            <el-select v-if="item.type === 'Select'" v-model="addForm[item.prop]" :allow-create="item.allowCreate"
              :multiple="item.multiple" filterable>
              <el-option v-for="(op,index) in item.options" :label="item.labelName?op[item.labelName]:op"
                :value="item.valueName?op[item.valueName]:op" :key="index">{{ item.labelName?op[item.labelName]:op
                }}</el-option>
            </el-select>
            <el-upload :ref="item.url" v-if="item.type === 'Upload'&& !addForm[item.prop]" drag :action="uploadAction"
              :headers="uploadHeaders" :before-upload="beforeIpUpload"
              :on-success="(response, file, fileList)=>uploadSuccess(item.prop, item.url, item.name, response, file, fileList)"
              :on-remove="(file, fileList)=>uploadRemove( item.url, item.name, file, fileList)" :on-error="uploadError"
              :limit="uploadMaxCount" :on-exceed="handleExceed" :show-file-list="item.isImage"
              :file-list="fileList[item.url]">
              <template v-if="item.isImage">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
                支持上传.png,.ico,.bmp,.jpg,.jpeg文件
              </template>
              <template v-else>
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                支持上传xlsx格式文件
              </template>
            </el-upload>
            <template v-if="item.type === 'Upload'&& addForm[item.prop]">
              <div v-if="item.isImage" class="screenshot">
                <el-image :src="showSrcIp+addForm[item.prop]" lazy></el-image>
                <span @click="uploadRemove(item.url, item.name)" class="delBtn"> <i class="el-icon-close"></i> </span>
              </div>
              <div v-else>{{ addForm[item.name] }} <span @click="uploadRemove(item.url, item.name)" class="delBtn"> <i
                    class="el-icon-close"></i> </span> <span @click="download(addForm[item.url])"
                  class="downloadBtn">下载</span> </div>
            </template>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer" v-if="isHandle">
      <el-button class="search_input_button" :class="item.className" v-for="item in searchHandle" :key="item.label"
        :type="item.type" @click="item.handle()">
        <span>{{ item.label }}</span>
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import tagsInput from "@/views/assetsView/tagsInput.vue";
export default {
  components:{
    tagsInput
  },
  data () {
    return {
      uploadAction: `${this.golangUploadSrcIp}/public/upload`,
      uploadHeaders: {
        is_golang: 1,
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: {}
    }
  },
  props: {
    dialogWidth:{
      type: String,
      default: "880px"
    },
    isHandle: {
      type: Boolean,
      default: true
    },
    dialogFormVisible: {
      type: Boolean,
      default: true
    },
    labelWidth: {
      type: String,
      default: "125px"
    },
    title: {
      type: String,
      default: ""
    },
    size: {
      type: String,
      default: "medium"
    },
    // FormData: {
    //   type: Array,
    //   default: () => []
    // },
    FormData: {
      type: Object,
      default: () => ({})
    },
    addForm: {
      type: Object,
      default: () => ({ })
    },
    searchHandle: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    /** 解析表单的正则验证***/
    rules() {
      let rules = Object.values(this.FormData).reduce((map, i) => {
        if (i.rules) {
          map[i.prop] = i.rules;
        }
        return map;
      }, {});
      return rules;
    },
    dialogVisible: {
      get() {
        return this.dialogFormVisible;
      },
      set() {}
    }
  },
  methods: {
    changeDefaultTag(item,val){
      this.addForm[item] = val
    },
    close() {
      this.fileList = {}
      this.$emit('closeDialog')
    },
    download(URL) {
      window.location.href = this.showSrcIp + URL
    },
    // 验证表单
    sublime() {
      console.log(this.addForm);
      // 父组件的点击事件
      this.$refs["FormData"].validate(valid => {
        if (valid) {
          this.$emit("sublime", this.addForm);
        } else {
          return false;
        }
      });
    },
    // 重置表单
    reset() {
      this.$refs["FormData"].clearValidate();
    },
    beforeIpUpload(file) {
      // if (!this.checkoutType) {
      //   this.$message.error('请选择检测类型')
      //   return false
      // }
      let isLt1M = ''
      isLt1M = file.size / 1024 / 1024 < 20;
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过20M!`);
      }
      return isLt1M;
    },
    uploadSuccess(prop,urlPrams,nameParams,response, file, fileList){
      if (file.response.code == 0) {
        if (file.response.data && file.response.data.name) {
          this.$set(this.addForm, urlPrams, file.response.data.path)
          this.$set(this.addForm, nameParams, file.response.data.name)
          this.$refs.FormData.validateField(prop)
        }
      } else {
        this.$message.error(file.response.message)
        this.$refs[prop][0].clearFiles()
      }
    },
    uploadError(err, file, fileList) {
      let myError = err.toString();//转字符串
      myError = myError.replace("Error: ","") // 去掉前面的" Error: "
      myError = JSON.parse(myError);//转对象
      if (myError.code == 401) {
        this.$router.push('/login')
        sessionStorage.clear()
        localStorage.clear()
      } else {
        this.$message.error(myError.message)
      }
    },
    handleExceed() {

      this.$message.error(`最多上传${this.uploadMaxCount}个文件`);
    },
    uploadRemove(urlPrams, nameParams, file, fileList) {
        this.$set(this.addForm,urlPrams,'')
        this.$set(this.addForm,nameParams,'')
    },
    getNowTime() {
	       var now = new Date();
	       var year = now.getFullYear(); //得到年份
	       var month = now.getMonth(); //得到月份
        var date = now.getDate(); //得到日期
        let hour = now.getHours(); //获取当前小时数(0-23)
        let minute = now.getMinutes(); //获取当前分钟数(0-59)
        let second = now.getSeconds(); //获取当前秒数(0-59)
	      //  var hour =" 00:00:00"; //默认时分秒 如果传给后台的格式为年月日时分秒，就需要加这个，如若不需要，此行可忽略
	       month = month + 1;
	       month = month.toString().padStart(2, "0");
	       date = date.toString().padStart(2, "0");
	       hour = hour.toString().padStart(2, "0");
	       minute = minute.toString().padStart(2, "0");
	       second = second.toString().padStart(2, "0");
	       var defaultDate = `${year}-${month}-${date} ${hour}:${minute}:${second}`;
	       return defaultDate;
	    },
  }
};
</script>
<style lang="less" scoped>
.item{
  float: left;
  width: 50%;
  height: 50px;
  /deep/.el-form-item{
    margin-right: 0 !important;
  }
  /deep/.el-form-item__content{
    width: calc(100% - 135px);
  }
}
.one{
  width: 100%;
  height: auto;
}
.two{
  width: 50%;
  height: 50px;
}
.three{
  width: 33%;
  height: auto;
}
/deep/.el-dialog__body{
  padding: 32px;
}
.search_input{
  padding: 32px 8px;
}
/deep/.el-upload-list__item{
  height: 40px !important;
}

.screenshot{
  width: 200px;
  height: 200px;
  position: relative;
  /deep/.el-image{
    width: 100%;
    height: 100%;

  }
  .delBtn{
    position: absolute;
    right: -30px;
    top: -5px;
  }
}
.el-icon-close{
  font-weight: 700;
}

.el-select {
  height: auto;
}
.clearfix::after {
  content: '';
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}
.delBtn,
.downloadBtn{
  margin-left: 20px;
  font-size: 16px;
  &:hover{
    cursor: pointer;
    color: #2677FF;
  }
}
</style>
