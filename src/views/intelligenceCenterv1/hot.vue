<template>
  <div class="tableList">
    <div class="filterTab" v-if="pageType != 'brief'">
      <div class="filterInput">
        <el-input
          v-model="formInline.name"
          placeholder="请输入完整的漏洞名称检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-input
          v-model="formInline.cve"
          placeholder="请输入完整的CVE编号检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-select
          v-model="formInline.risk_level"
          placeholder="请选择威胁等级检索"
          @change="checkFuncList"
          clearable
          collapse-tags
          id="user_select"
        >
          <el-option
            :label="v.name"
            :value="v.value"
            v-for="v in riskLevelArr"
            :key="v.value"
          ></el-option>
        </el-select>
        <el-select
          v-model="formInline.tag"
          multiple
          placeholder="请选择标签检索"
          @change="checkFuncList"
          clearable
          collapse-tags
          id="user_select"
        >
          <el-option :label="v" :value="v" v-for="v in selectData.tag" :key="v"></el-option>
        </el-select>
      </div>
      <div>
        <el-checkbox
          class="checkboxAll"
          v-if="pageType && pageType == 'manage'"
          v-model="checkedAll"
          @change="checkAllChange"
          id="user_all"
          >选择全部</el-checkbox
        >
        <el-button
          class="normalBtnRe"
          v-if="pageType && pageType == 'manage'"
          style="margin-left: 10px"
          @click="changeCurrentLevel()"
          type="primary"
          >调整威胁等级</el-button
        >
        <el-button
          class="normalBtnRe"
          v-if="pageType != 'manage' && userInfo.is_show_copy_data != 1"
          style="margin-left: 10px"
          :disabled="checkBtnLoading || progressLoading"
          :loading="progressLoading"
          @click="oneKeyCheck([])"
          type="primary"
          >{{ progressLoading ? progress + '%' : '' }}&nbsp;一键检测</el-button
        >
        <!-- <el-button class="normalBtnRe" v-if="pageType != 'manage' && userInfo.is_show_copy_data != 1" style="margin-left: 10px;" @click="$router.push('/hotCheckResList')" type="primary" >一键检测结果</el-button> -->
        <el-button
          class="normalBtnRe"
          v-if="pageType && pageType == 'manage'"
          :disabled="user.role != 1 && !scopes.includes('intelligence_manager')"
          style="margin-left: 10px"
          @click="add()"
          type="primary"
          >新增</el-button
        >
      </div>
    </div>
    <div class="tableComponent">
      <tableList
        ref="tableList"
        :checkedAll="checkedAll"
        :isSelectData="pageType && pageType == 'manage'"
        :pageType="pageType"
        :tableHeader="
          tableHeader.filter(
            (item) => !item.pageType || (item.pageType && item.pageType == pageType)
          )
        "
        :tableData="tableData"
        @updateList="updateList"
        :total="total"
        @selectChange="selectChange"
      >
        <template v-slot:tip="{ item }">
          <span v-if="item.name == 'risk_count'">
            <el-tooltip
              class="item"
              content="每次检测结束才会更新"
              effect="dark"
              placement="top"
              popper-class="chainClass"
              :open-delay="500"
            >
              <i class="el-icon-question" style="color: #2677ff"></i>
            </el-tooltip>
          </span>
        </template>
        <template v-slot:judge="{ row, itemName }">
          <span v-if="itemName == 'risk_level'">
            <span :class="riskLevelArrClassMap[row[itemName]]">
              {{ row[itemName] || '-' }}
            </span>
          </span>
          <span v-else-if="itemName == 'tag' && row[itemName]">
            <span>
              {{ row[itemName].join(',') || '-' }}
            </span>
          </span>
          <span v-else> {{ row[itemName] || '-' }} </span>
        </template>
        <template slot="other">
          <el-table-column fixed="right" label="操作" align="left" :width="operationWidth">
            <template slot-scope="{ row, $index }">
              <div class="optionDiv" v-if="pageType != 'brief'">
                <el-button
                  v-if="pageType != 'manage' && userInfo.is_show_copy_data != 1"
                  type="text"
                  size="small"
                  :disabled="checkBtnLoading || progressLoading"
                  :loading="checkBtnLoading && currentIndex == $index"
                  @click="oneKeyCheck([row.id], $index)"
                  >检测</el-button
                >
                <el-button
                  v-if="pageType && pageType == 'manage'"
                  type="text"
                  size="small"
                  @click="edit(row)"
                  >编辑</el-button
                >
                <el-button
                  v-if="pageType && pageType == 'manage'"
                  type="text"
                  size="small"
                  @click="uploadReport(row)"
                  >上传报告</el-button
                >
                <el-button type="text" size="small" @click="openDetail(row)">详情</el-button>
                <el-button
                  v-if="pageType && pageType == 'manage'"
                  type="text"
                  size="small"
                  @click="changeCurrentLevel(row)"
                  >调整威胁等级</el-button
                >
                <el-button
                  :disabled="pageType && pageType != 'manage' && !row.has_masked_report"
                  type="text"
                  size="small"
                  @click="reportPreview(row)"
                  >查看报告</el-button
                >
                <el-button
                  v-if="pageType != 'manage'"
                  type="text"
                  size="small"
                  @click="jumpToAssetsRelated(row)"
                  >相关资产</el-button
                >
              </div>
              <div class="optionDiv" v-else>
                <el-button type="text" size="small" @click="jumpToSummaryPage(row)">详情</el-button>
              </div>
            </template>
          </el-table-column>
        </template>
      </tableList>
    </div>
    <formData
      ref="formData"
      :title="currentIPRow.id ? '热点漏洞编辑' : '热点漏洞录入'"
      :FormData="FormData"
      :addForm="currentIPRow"
      :searchHandle="searchHandle"
      :dialogFormVisible="addDialogFormVisible"
      @sublime="submit"
      @closeDialog="addDialogFormVisible = false"
    ></formData>
    <prompt
      label="威胁等级"
      title="调整威胁等级"
      placeholder="请选择要调整的威胁等级"
      :visible="levelVisible"
      @save="changeLevelFn"
      @close="levelVisible = false"
      :loading="levelVisibleLoading"
      inputType="select"
      :inputValueBack="currentIPRow.risk_level"
    />
    <uploadReport
      :addDialogFormVisible="uploadDialogFormVisible"
      :action="`${golangUploadSrcIp}/intelligence/hot-poc/${dataId}/report/upload`"
      @close="uploadDialogFormVisible = false"
    />
    <hotDetail ref="hotDetail" :currentCompany="currentCompany"> </hotDetail>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import tableList from './table.vue'
import formData from './formData.vue'
import prompt from '@/components/assets/prompt.vue'
import uploadReport from './uploadReport.vue'
import hotDetail from './hotDetail.vue'
import {
  hotTagList,
  hotListReport,
  hotListMaskedReport,
  hotListManage,
  hotList,
  hotCheck,
  hotCheckProgress,
  hotAdd,
  hotEdit,
  changeLevel
} from '@/api/apiConfig/api.js'

export default {
  components: {
    tableList,
    formData,
    prompt,
    uploadReport,
    hotDetail
  },
  props: {
    scopes: {
      type: Array,
      default: () => []
    },
    userInfo: {
      type: Object,
      default: () => ({})
    },
    user: {
      type: Object,
      default: () => ({})
    },
    currentCompany: {
      type: [String, Number],
      default: ''
    },
    pageType: {
      // 是否为管理页面
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uploadDialogFormVisible: false,
      dataId: '',
      checkedArr: [],
      checkedAll: false,
      levelVisibleLoading: false,
      levelVisible: false,
      currentIPRowCopy: {},
      currentIndex: null,
      checkBtnLoading: false,
      riskLevelArrClassMap: {
        低危: 'yellowRadiusBorder',
        中危: 'originRadiusBorder',
        高危: 'redRadiusBorder',
        严重: 'deepRedRadiusBorder'
      },
      riskLevelArr: [
        { name: '低危', value: '低危' },
        { name: '中危', value: '中危' },
        { name: '高危', value: '高危' },
        { name: '严重', value: '严重' }
      ],
      formInline: {
        name: ''
      },
      addForm: {},
      searchHandle: [
        {
          type: '',
          label: '取消',
          className: 'highBtnRe',
          handle: () => {
            this.addDialogFormVisible = false
            this.addForm = {}
            this.$refs.formData.reset()
          }
        },
        {
          type: '',
          label: '发布',
          className: 'highBtn',
          handle: () => {
            this.$refs.formData.sublime()
          }
        },
        {
          type: '',
          label: '重置',
          className: 'highBtnRe',
          handle: () => {
            this.currentIPRow = JSON.parse(JSON.stringify(this.currentIPRowCopy))
            this.$refs.formData.reset()
          }
        }
      ],
      FormData: {
        name: {
          type: 'Input',
          label: '漏洞名称',
          prop: 'name',
          placeholder: '请输入漏洞名称',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        impact_product: {
          type: 'Input',
          label: '受影响产品名称',
          prop: 'impact_product',
          placeholder: '请输入受影响产品名称',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        impact_version: {
          type: 'Input',
          label: '受影响产品版本',
          prop: 'impact_version',
          placeholder: '请输入受影响产品版本',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        risk_level: {
          type: 'Select',
          label: '风险等级',
          prop: 'risk_level',
          placeholder: '请输入风险等级',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          options: [
            { label: '低危', value: '低危' },
            { label: '中危', value: '中危' },
            { label: '高危', value: '高危' },
            { label: '严重', value: '严重' }
          ],
          labelName: 'label',
          valueName: 'value',
          change: () => {}
        },
        cve: {
          type: 'Input',
          label: 'CVE编号',
          prop: 'cve',
          placeholder: '请输入CVE编号',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        cnnvd: {
          type: 'Input',
          label: 'CNNVD编号',
          prop: 'cnnvd',
          placeholder: '请输入CNNVD编号',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },

        fofa_count: {
          type: 'number',
          label: '影响数量',
          prop: 'fofa_count',
          placeholder: '请输入影响数量',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        impact_range: {
          type: 'textarea',
          label: '影响范围',
          prop: 'impact_range',
          placeholder: '请输入影响范围',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          className: 'one'
        },
        fofa_query: {
          type: 'textarea',
          label: '指纹',
          prop: 'fofa_query',
          placeholder: '请输入指纹，限制3000字符',
          showWordLimit: true,
          maxlength: 3000,
          rules: [
            { required: true, message: '该选项不能为空', trigger: 'blur' },
            {
              max: 3000,
              message: '您输入的指纹内容超3000字符上限！请重新输入！',
              trigger: 'change'
            }
          ],
          className: 'one'
        },
        introduce: {
          type: 'textarea',
          label: '漏洞介绍',
          prop: 'introduce',
          placeholder: '请输入漏洞介绍',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          className: 'one'
        },
        solution: {
          type: 'textarea',
          label: '解决方案',
          prop: 'solution',
          placeholder: '请输入解决方案',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          className: 'one'
        },
        tag: {
          type: 'tagsInput',
          label: '标签',
          prop: 'tag',
          placeholder: '请输入标签',
          className: 'one'
        },
        found_at: {
          type: 'DateTime',
          label: '录入时间',
          prop: 'found_at',
          placeholder: '请选择录入时间',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          change: () => {},
          className: 'two'
        }
      },
      addDialogFormVisible: false,
      // pocInfo: {},
      currentIPRow: {},
      // ipSearchParams: {
      //   page: 1,
      //   per_page: 10,
      // },
      // ipTableHeader: [
      //   {
      //     label: 'IP',
      //     name: 'ip',
      //     fixed: 'left',
      //     minWidth: 140,
      //   },
      //   {
      //     label: '端口',
      //     name: 'port',
      //     fixed: 'left',
      //     minWidth: 140,
      //   },
      //   {
      //     label: 'URL',
      //     name: 'url',
      //     fixed: 'left',
      //     minWidth: 280,
      //   },
      //   {
      //     label: '标题',
      //     name: 'title',
      //     fixed: 'left',
      //     minWidth: 260,
      //   },
      // ],
      // ipTableData: [],
      // ipTotal: 0,
      dialogFormVisible: false,
      updateTimer: null,
      progress: 0,
      progressLoading: false,
      searchParams: {
        page: 1,
        per_page: 10
      },
      total: 0,
      tableData: [],
      // 热点漏洞
      tableHeader: [
        {
          label: '披露时间',
          name: 'found_at',
          fixed: 'left',
          minWidth: 100
        },
        {
          label: '漏洞名称',
          name: 'name',
          minWidth: 100
        },
        {
          label: '标签',
          name: 'tag',
          minWidth: 100
        },
        {
          label: 'CVE',
          name: 'cve',
          minWidth: 100
        },
        {
          label: '影响资产名称',
          name: 'impact_product',
          minWidth: 100
        },
        {
          label: '影响范围',
          name: 'impact_range',
          minWidth: 100
        },
        {
          label: '指纹',
          name: 'fofa_query',
          minWidth: 100,
          pageType: 'manage'
        },
        {
          label: '威胁等级',
          name: 'risk_level',
          minWidth: 100
        },
        {
          label: '影响资产数量',
          name: 'risk_count',
          minWidth: 100,
          pageType: 'center'
        },
        {
          label: '互联网影响面',
          name: 'fofa_count',
          minWidth: 100
        }
      ],
      selectData: {}
    }
  },
  mounted() {
    if (this.$route.query.keyword) {
      let { keyword } = this.$route.query
      this.formInline.name = keyword
      Object.assign(this.searchParams, { page: 1, per_page: 10, ...this.formInline })
    }
    if (this.user.role == 2 && !this.currentCompany) return
    this.getData()
    this.getTagList()
    if (this.pageType && this.pageType != 'manage') {
      this.updateProgress(true)
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getData()
        this.getTagList()
        if (this.pageType && this.pageType != 'manage') {
          this.updateProgress(true)
        }
      }
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany']),
    operationWidth() {
      let widthMap = {
        brief: '90px',
        manage: '290px'
      }
      return widthMap[this.pageType] || '200px'
    }
  },
  methods: {
    getTagList() {
      hotTagList({ operate_company_id: this.currentCompany }).then((res) => {
        this.selectData = res.data
      })
    },
    jumpToSummaryPage(row) {
      this.$emit('jumpToSummaryPageFn', 'hot', row.name)
    },
    jumpToAssetsRelated(row) {
      this.$emit('jumpToAssetsRelated', 'hot', row.name)
    },
    uploadReport(row) {
      this.dataId = row.id
      this.uploadDialogFormVisible = true
    },
    async reportPreview(row) {
      let funcName = null
      if (this.pageType && this.pageType == 'manage') {
        // 原始
        funcName = hotListReport
      } else {
        // 加密
        funcName = hotListMaskedReport
      }
      let res = await funcName({ data_id: row.id })
      this.judgeUpload(res)
    },
    judgeUpload(res, fileName) {
      var reader = new FileReader()
      reader.readAsText(res)
      const _this = this
      reader.addEventListener('loadend', function (e) {
        if (e.target.result.indexOf('"code":400') != -1) {
          let result = JSON.parse(e.target.result)
          _this.$message({
            message: result.message,
            type: 'error'
          })
        } else {
          const href = window.URL.createObjectURL(
            new Blob([res], {
              type: 'application/pdf'
            })
          )
          window.open(href)
        }
      })
    },
    selectChange(val) {
      this.checkedArr = val
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.tableList.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.tableList.$refs.eltable.clearSelection()
      }
    },
    changeCurrentLevel(row) {
      this.currentIPRow = row || {}
      if (!row && !this.checkedAll && this.checkedArr.length == 0) {
        return this.$message.error('请选择数据')
      }
      this.levelVisible = true
    },
    async changeLevelFn(val) {
      let ids = []
      if (this.checkedAll) {
        ids = []
      } else {
        if (!this.currentIPRow.id) {
          ids = this.checkedArr.map((item) => item.id)
        } else {
          ids = [this.currentIPRow.id]
        }
      }
      let res = await changeLevel({ id: ids, set_risk_level: val, ...this.searchParams })
      if (res.code == 0) {
        this.$message.success('修改等级成功')
        this.levelVisible = false
        this.getData()
        this.checkedAll = false
        this.$refs.tableList.$refs.eltable.clearSelection()
      }
    },
    add() {
      this.addDialogFormVisible = true
      let row = {
        found_at: this.$refs.formData.getNowTime()
      }
      this.currentIPRowCopy = JSON.parse(JSON.stringify(row))
      this.currentIPRow = JSON.parse(JSON.stringify(this.currentIPRowCopy))
    },
    edit(row) {
      this.addDialogFormVisible = true
      this.currentIPRowCopy = JSON.parse(JSON.stringify(row))
      this.currentIPRow = JSON.parse(JSON.stringify(this.currentIPRowCopy))
    },
    checkFuncList() {
      this.updateList({ page: 1, per_page: 10, ...this.formInline })
    },
    async submit(addForm) {
      let funcName = addForm.id ? hotEdit : hotAdd
      let res = await funcName({ ...addForm, operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.$refs.formData.reset()
        this.addDialogFormVisible = false
        // this.addForm = {}
        this.getData()
        this.getTagList()
      }
    },
    openDetail(row) {
      this.$refs.hotDetail.getIpDetail(row.id)
    },
    // async getIpDetail(isPoc = false) {
    //   let row =  this.currentIPRow
    //   let res = await hotIpDetail({ id: row.id, query: {...this.ipSearchParams,operate_company_id: this.currentCompany}})
    //   if (res.code == 0) {
    //     if (isPoc) {
    //       let poc_info = res.data.poc_info
    //       this.pocInfo = JSON.parse(JSON.stringify(poc_info))
    //       this.pocInfo.risk_level_default = 'Low'
    //       if (poc_info.risk_level == '严重') {
    //         this.pocInfo.risk_level_default = 'Critical'
    //       } else if (poc_info.risk_level == '低危') {
    //         this.pocInfo.risk_level_default = 'Low'
    //       } else if (poc_info.risk_level == '中危') {
    //         this.pocInfo.risk_level_default = 'Medium'
    //       } else if (poc_info.risk_level == '高危') {
    //         this.pocInfo.risk_level_default = 'High'
    //       }
    //     }

    //     this.ipTableData = res.data.items
    //     this.ipTotal = res.data.total

    //   }
    // },
    async oneKeyCheck(id, index) {
      this.currentIndex = index
      if (id && id.length !== 0) {
        this.checkBtnLoading = true
        this.progressLoading = false
      } else {
        this.progressLoading = true
        this.checkBtnLoading = false
      }
      let res = await hotCheck({
        id,
        operate_company_id: this.currentCompany,
        ...this.searchParams
      }).catch(() => {
        this.progressLoading = false
        this.checkBtnLoading = false
      })
      if (res.code == 0) {
        if (id && id.length !== 0) {
          // 单个检测
          this.progressLoading = false
          this.checkBtnLoading = true
          setTimeout(() => {
            this.$message.success('一键检测已完成')
            this.checkBtnLoading = false
            this.getData()
          }, 2000)
        } else {
          // 全部检测
          this.progressLoading = true
          this.checkBtnLoading = false
          this.updateProgress()
        }
      }
    },
    async updateProgress(isNotMsg) {
      let res = await hotCheckProgress({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        if ((res.data && res.data < 100) || res.data === 0) {
          this.progress = res.data
          this.progressLoading = true
          this.checkBtnLoading = false
          this.updateTimer = setTimeout(() => {
            this.updateProgress()
          }, 2000)
        } else {
          this.checkBtnLoading = false
          this.progressLoading = false
          this.progress = 0
          if (!isNotMsg) {
            this.$message.success('一键检测已完成')
          }
          if (this.updateTimer) {
            clearTimeout(this.updateTimer)
          }

          if (!isNotMsg) {
            setTimeout(() => {
              this.getData()
            }, 1000)
          }
        }
      }
    },
    updateList(data) {
      let searchParam = JSON.parse(JSON.stringify(this.searchParams))
      this.searchParams = Object.assign(searchParam, data)
      this.getData()
    },
    getData() {
      this.$emit('updatePageLoading', true)
      let funcName = this.pageType && this.pageType == 'manage' ? hotListManage : hotList
      funcName({ ...this.searchParams, operate_company_id: this.currentCompany })
        .then((res) => {
          if (res.code == 0) {
            this.tableData = res.data.items
            this.total = res.data.total
          } else {
            this.$message.error(res.message)
          }
          this.$emit('updatePageLoading', false)
        })
        .catch(() => {
          this.$emit('updatePageLoading', false)
        })
    }
  },
  beforeDestroy() {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer)
      this.updateTimer = null
    }
  }
}
</script>

<style lang="less" scoped>
.filterInput {
  width: 50%;
  & > div {
    margin-right: 16px;
  }
  .el-input {
    width: 30% !important;
  }
  /deep/.el-select {
    width: 30% !important;
    .el-input {
      width: 100% !important;
    }
  }
}
</style>
