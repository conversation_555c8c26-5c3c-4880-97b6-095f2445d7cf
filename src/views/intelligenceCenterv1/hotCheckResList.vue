<template>
  <div class="container">
    <div class="headerTitle">
      <span class="goback" @click="$router.go(-1)"
        ><i class="el-icon-arrow-left"></i>返回<span class="spline">/</span></span
      >
      一键检测结果
    </div>
    <div class="filterTab">
      <div>
        <el-input
          v-model="formInline.ip"
          placeholder="请输入ip检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-input
          v-model="formInline.hot_poc_name"
          placeholder="请输入漏洞名称检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-select
          v-model="formInline.status"
          placeholder="请选择状态检索"
          @change="checkFuncList"
          clearable
          collapse-tags
          id="user_select"
        >
          <el-option
            :label="v.label"
            :value="v.value"
            v-for="v in statusArr"
            :key="v.value"
          ></el-option>
        </el-select>
      </div>
      <div>
        <el-checkbox class="checkboxAll" v-model="checkedAll" @change="checkAllChange" id="user_all"
          >选择全部</el-checkbox
        >
        <el-button
          class="normalBtnRe"
          style="margin-left: 10px"
          type="primary"
          @click="exportExcel"
          id="user_export"
          >导出</el-button
        >
      </div>
    </div>
    <div class="tableWrap">
      <el-table
        border
        :data="tableData"
        v-loading="loading"
        row-key="id"
        :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
        @cell-mouse-enter="showTooltip"
        @cell-mouse-leave="hiddenTooltip"
        @selection-change="handleSelectionChange"
        ref="eltable"
        height="100%"
        style="width: 100%"
      >
        <el-table-column
          type="selection"
          align="center"
          :reserve-selection="true"
          :selectable="handleSelectable"
          width="55"
        >
        </el-table-column>
        <el-table-column
          v-for="item in tableHeader"
          :key="item.id"
          :prop="item.name"
          align="left"
          :min-width="item.minWidth"
          :fixed="item.fixed"
          :label="item.label"
        >
          <template slot-scope="{ row }">
            <span v-if="item.name == 'hot_pocs'">{{ translateHotPoc(row[item.name]) }}</span>
            <span v-else-if="item.name == 'rules'">{{ translateRules(row[item.name]) }}</span>
            <span v-else>
              {{ row[item.name] || '-' }}
            </span>
          </template>
        </el-table-column>
        <slot name="other"></slot>
      </el-table>
      <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 30, 50, 100]"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { mapGetters, mapState } from 'vuex'
import { hotCheckIPList, hotCheckIPExport } from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip
  },
  data() {
    return {
      statusArr: [
        { label: '在线', value: 1 },
        { label: '离线', value: 2 }
      ],
      checkedArr: [],
      checkedAll: false,
      formInline: {
        page: 1,
        per_page: 10
      },
      tableData: [],
      total: 0,
      loading: false,
      // tableData: [],
      currentPage: 1,
      pageSize: 10,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      tableHeader: [
        {
          label: 'ip',
          name: 'ip',
          fixed: 'left',
          minWidth: 100
        },
        {
          label: '端口',
          name: 'port',
          minWidth: 100
        },
        {
          label: 'url',
          name: 'url',
          minWidth: 100
        },
        {
          label: '状态',
          name: 'status',
          minWidth: 100
        },
        {
          label: '热点漏洞',
          name: 'hot_pocs',
          minWidth: 100
        },
        {
          label: '标题',
          name: 'title',
          minWidth: 100
        },
        {
          label: '组件',
          name: 'rules',
          minWidth: 100
        }
      ],
      userInfo: {},
      user: {}
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getData()
      }
    }
  },
  mounted() {
    if (this.user.role == 2 && !this.currentCompany) {
      return
    }
    this.getData()
  },
  methods: {
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    async exportExcel() {
      let id = []
      if (!this.checkedAll && this.checkedArr.length == 0) {
        this.$message.error('请选择操作数据')
        return
      } else if (!this.checkedAll && this.checkedArr.length != 0) {
        id = this.checkedArr.map((item) => item.id)
      }
      let res = await hotCheckIPExport({
        ...this.formInline,
        id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.download(this.showSrcIp + res.data)
        this.$refs.eltable.clearSelection()
        this.checkedAll = false
        this.$message.success('导出成功')
      }
    },
    checkFuncList() {
      this.getData()
    },
    async getData() {
      let res = await hotCheckIPList({
        ...this.formInline,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
          })
        }
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSizeChange(val) {
      this.formInline.per_page = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.formInline.page = val
      this.getData()
    },
    translateHotPoc(poc) {
      let pocString = ''
      if (poc && poc.length != 0) {
        pocString = poc.map((item) => item.hot_poc_name).join(',')
        return pocString
      } else {
        return '-'
      }
    },
    translateRules(rules) {
      let rulesString = ''
      if (rules && rules.length != 0) {
        rulesString = rules.map((item) => item.cn_product).join(',')
        return rulesString
      } else {
        return '-'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  height: calc(100% - 54px);
  // height: 100%;
  position: relative;
  background-color: #fff;
  padding: 20px;
  .tableWrap {
    height: calc(100% - 92px);
    // padding:16px 20px;
  }
}
.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 16px;
  .el-input {
    margin-right: 16px;
  }
  & > div {
    display: flex;
    align-items: center;
    .normalBtnRe {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .el-input {
      width: 240px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      margin-left: 20px;
      cursor: pointer;
    }
  }
}
</style>
