<template>
  <el-dialog
    class="elDialogAdd"
    :close-on-click-modal="false"
    :visible.sync="dialogFormVisible"
    width="880px"
  >
    <template slot="title"> 热点漏洞情报详情 </template>
    <div class="dialog-body" v-loading="loading" style="max-height: 813px">
      <div class="top" :class="pocInfo.risk_level_default">
        <div class="left">
          <img
            :src="require(`../../assets/images/risk${pocInfo.risk_level_default || 'Low'}.png`)"
            alt=""
          />
        </div>
        <div class="right">
          <div class="title">{{ pocInfo.name }}</div>
          <div class="levelDiv">
            <div class="level">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span>{{ pocInfo.risk_level }}</span>
          </div>
        </div>
      </div>
      <div class="middleLine"></div>
      <div class="bottom">
        <div class="base clearfix">
          <div class="title">基础信息</div>
          <div class="item">
            <span class="label">风险等级：</span>
            <span class="value">{{ pocInfo.risk_level || '-' }}</span>
          </div>
          <div class="item">
            <span class="label">CVE编号：</span>
            <span class="value">{{ pocInfo.cve || '-' }}</span>
          </div>
          <div class="item">
            <span class="label">CNNVD编号：</span>
            <span class="value">{{ pocInfo.cnnvd || '-' }}</span>
          </div>
          <div class="item">
            <span class="label">首次披露时间：</span>
            <span class="value">{{ pocInfo.found_at || '-' }}</span>
          </div>

          <div class="item one">
            <span class="label">漏洞介绍：</span>
            <span class="value">{{ pocInfo.introduce || '-' }}</span>
          </div>
        </div>
        <div class="solution">
          <div class="title">解决方案</div>
          <div class="content">{{ pocInfo.solution || '-' }}</div>
        </div>
        <div class="effect">
          <div class="title">影响组件：{{ pocInfo.impact_product || '-' }}</div>
          <div class="content">
            <div>
              <span>影响版本：</span>
              <span>{{ pocInfo.impact_version || '-' }}</span>
            </div>
            <div>
              <span>具体范围：</span>
              <span>{{ pocInfo.impact_range || '-' }}</span>
            </div>
          </div>
        </div>
        <!-- <div class="ipTable">
            <div class="title">攻击面管理</div>
            <div class="content">
              <tableList :tableHeader="ipTableHeader" :tableData="ipTableData" @updateList="updateIpList" :total="ipTotal">
                <template v-slot:judge = "{ row,itemName }">
                  {{ row[itemName] || '-' }}
                </template>
              </tableList>
            </div>
          </div> -->
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { hotIpDetail } from '@/api/apiConfig/api.js'

export default {
  props: {
    currentCompany: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      pocInfo: {},
      dialogFormVisible: false
    }
  },
  methods: {
    async getIpDetail(id) {
      this.dialogFormVisible = true
      this.loading = true
      let res = await hotIpDetail({
        id,
        operate_company_id: this.currentCompany,
        query: {
          page: 1,
          per_page: 10
        }
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        let poc_info = res.data.poc_info
        this.pocInfo = JSON.parse(JSON.stringify(poc_info))
        this.pocInfo.risk_level_default = 'Low'
        if (poc_info.risk_level == '严重') {
          this.pocInfo.risk_level_default = 'Critical'
        } else if (poc_info.risk_level == '低危') {
          this.pocInfo.risk_level_default = 'Low'
        } else if (poc_info.risk_level == '中危') {
          this.pocInfo.risk_level_default = 'Medium'
        } else if (poc_info.risk_level == '高危') {
          this.pocInfo.risk_level_default = 'High'
        }
        //  this.ipTableData = res.data.items
        //  this.ipTotal = res.data.total
      }
      this.loading = false
    }
    // updateIpList(data) {
    //   let ipSearchParams = JSON.parse(JSON.stringify(this.ipSearchParams))
    //   this.ipSearchParams = Object.assign(ipSearchParams, data)
    //   this.getIpDetail()
    // },
  }
}
</script>

<style lang="less" scoped>
.elDialogAdd {
  /deep/.el-dialog__body {
    padding: 0;
    // max-height: 613px;
  }

  /deep/.dialog-item {
    margin-top: 13px;
    margin-left: 28px;
  }

  .top {
    box-sizing: border-box;
    display: flex;
    height: 157px;
    padding: 20px 24px;
    .left {
      width: 110px;
      margin-right: 16px;
    }

    .right {
      width: 0;
      flex: 1;
      display: flex;
      justify-content: center;
      flex-direction: column;

      .title {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
      }

      .levelDiv {
        box-sizing: border-box;
        width: 86px;
        padding: 4px 12px;
        border-radius: 14px;
      }

      .level {
        display: inline-block;
        margin-right: 4px;

        span {
          display: inline-block;
          width: 6px;
          height: 10px;
          background: #eaceba;
          transform: skewX(-25deg);
          margin-right: 1px;
        }
      }
    }

    &.Low {
      background: linear-gradient(180deg, #fffbef 0%, #ffffff 100%), #ffffff;

      .levelDiv {
        background: rgba(248, 193, 54, 0.12);
        border: 1px solid #f8c136;
        color: #f8c136;

        .level {
          span:nth-child(-n + 1) {
            background: #f8c136;
          }
        }
      }
    }

    &.Medium {
      background: linear-gradient(180deg, #fff4eb 0%, #ffffff 100%), #ffffff;

      .levelDiv {
        border: 1px solid #ff7900;
        color: #ff7900;
        background: rgba(255, 121, 0, 0.12);

        .level {
          span:nth-child(-n + 2) {
            background: #ff7900;
          }
        }
      }
    }

    &.High {
      background: linear-gradient(180deg, #fff3f3 0%, #ffffff 100%), #ffffff;

      .levelDiv {
        border: 1px solid #ff4646;
        color: #ff4646;
        background: rgba(255, 70, 70, 0.12);

        .level {
          span:nth-child(-n + 3) {
            background: #ff4646;
          }
        }
      }
    }

    &.Critical {
      background: linear-gradient(180deg, #f1e2e2 0%, #ffffff 100%), #ffffff;

      .levelDiv {
        border: 1px solid #bf1919;
        color: #bf1919;
        background: rgba(255, 70, 70, 0.12);

        .level {
          span:nth-child(-n + 3) {
            background: #bf1919;
          }
        }
      }
    }
  }

  .middleLine {
    margin: 0 20px;
    height: 1px;
    width: calc(100% - 40px);
    background: #e3e5ea;
  }

  .bottom {
    padding: 20px 20px 24px;

    .title {
      margin-bottom: 12px;

      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 4px;
        margin-right: 4px;
        transform: rotate(135deg);
        background-color: #2677ff;
        vertical-align: middle;
      }
    }

    .base {
      margin-bottom: 12px;

      .item {
        width: 50%;
        float: left;
        margin-bottom: 12px;

        &.one {
          width: 100%;
        }

        .label {
          color: rgba(98, 102, 108, 0.6);
        }

        .value {
          color: #37393c;
        }
      }
    }

    .solution {
      .content {
        border-radius: 4px;
        box-sizing: border-box;
        height: 96px;
        padding: 16px;
        background-color: #f5f8fc;

        div:first-child {
          margin-bottom: 12px;
        }
      }
    }

    .effect {
      margin-top: 24px;

      .content {
        border-radius: 4px;
        box-sizing: border-box;
        padding: 16px;
        background-color: #f5f8fc;

        div:first-child {
          margin-bottom: 12px;
        }
      }
    }

    .ipTable {
      margin-top: 24px;

      .content {
        height: 570px;
      }
    }
  }
}
.clearfix::after {
  content: '';
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}
</style>
