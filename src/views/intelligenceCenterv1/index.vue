<template>
  <div class="container">
    <div class="headerTitle">
      {{ $route.path == '/intelligenceManage' ? '情报管理' : '情报总览' }}
    </div>
    <div class="tab_container">
      <div
        class="tab_title"
        v-for="item in tabList"
        :key="item.value"
        :class="{ active: actTabVal == item.value }"
        @click="handleClick(item)"
      >
        <div class="tab-content">
          <div class="title"
            >{{ item.name }} <span class="titleCount">{{ typeCount[item.countName] }}</span></div
          >
          <span class="count">{{ typeCount[item.countName] }}</span>
          <div class="content">
            <el-tooltip class="item" effect="dark" :content="item.description" placement="top">
              <span>
                {{ item.description }}
              </span>
            </el-tooltip>
          </div>
          <div class="tag">
            <span class="block" v-for="(item, index) in item.tags" :key="index">{{ item }}</span>
          </div>
          <div class="logo">
            <img :src="require(`../../assets/images/intell-${item.components}.png`)" alt="" />
          </div>
        </div>
        <div class="pointer"></div>
      </div>
    </div>
    <div class="table" v-loading="pageLoading">
      <component
        @jumpToAssetsRelated="jumpToAssetsRelated"
        :scopes="scopes"
        :userInfo="userInfo"
        :user="user"
        :is="componentTag"
        :currentCompany="currentCompany"
        :pageType="pageType"
        @updatePageLoading="updatePageLoading"
      >
      </component>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import tableList from './table.vue'
import hot from './hot.vue'
import pinishing from './pinishing.vue'
import risk from './risk.vue'
import other from './other.vue'
import dataLeak from './dataLeak.vue'
import special from './special.vue'
import { intelligenceTypeCount } from '@/api/apiConfig/api.js'

export default {
  components: {
    tableList,
    hot,
    pinishing,
    risk,
    other,
    dataLeak,
    special
  },
  data() {
    return {
      typeCount: {},
      pageLoading: false,
      pageType: '',
      componentTag: 'dataLeak',
      actTabVal: '5',
      tabList: {
        dataLeak: {
          name: '数据情报',
          value: 5,
          description:
            '检测已知资产泄露数据情况，提供泄露服务/组件、泄露内容和泄露原因等重要信息。',
          tags: ['泄露数据', '数据情报'],
          components: 'dataLeak',
          countName: 'Data',
          path: ['/intelligenceCenterv1', '/intelligenceBrief']
        },
        hot: {
          name: '热点漏洞',
          value: 1,
          description: '跟踪最新漏洞，并自动碰撞最新漏洞关联的IP以及相关影响。',
          tags: ['漏洞情报', '行业漏洞'],
          components: 'hot',
          countName: 'HotPoc',
          path: ['/intelligenceCenterv1', '/intelligenceBrief', '/intelligenceManage']
        },
        pinishing: {
          name: '钓鱼仿冒',
          value: 2,
          description:
            '基于FOFA实时获取最新的钓鱼仿冒网站，并自动关联仿冒主体，实时预计钓鱼方面威胁。',
          tags: ['仿冒网站', '恶意仿冒'],
          components: 'pinishing',
          countName: 'Fake',
          path: ['/intelligenceCenterv1', '/intelligenceBrief', '/intelligenceManage']
        },
        risk: {
          name: '风险情报',
          value: 3,
          description: '威胁IP和URL漏洞和风险，联动FOFA关联IP相关组件和风险类型。',
          tags: ['风险IP', '风险域名'],
          components: 'risk',
          countName: 'Threat',
          path: ['/intelligenceCenterv1', '/intelligenceBrief', '/intelligenceManage']
        },
        special: {
          name: '专项情报',
          value: 6,
          description:
            '支持获取专项检查、风险事件和0Day预警类型的专项情报，提供关联设备及漏洞描述并支持根据内置指纹关联已知资产。',
          tags: ['安全事件', '专项检查'],
          components: 'special',
          countName: 'Event',
          path: ['/intelligenceCenterv1', '/intelligenceBrief']
        },
        other: {
          name: '其他情报',
          value: 4,
          description:
            '通过动态收集数据交易情报，探测和识别相关数据的泄露信息，应对来自数据交易和黑产带来的安全威胁。',
          tags: ['黑产信息', '数据交易'],
          components: 'other',
          countName: 'Other',
          path: ['/intelligenceCenterv1', '/intelligenceBrief', '/intelligenceManage']
        }
      },
      userInfo: {},
      user: {},
      scopes: []
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  created() {
    if (this.$route.query.actTabVal) {
      let actTabItem = this.tabList[this.$route.query.actTabVal]
      this.actTabVal = actTabItem.value
      this.componentTag = actTabItem.components
    }

    this.getIntelligenceTypeCount()
    this.pageType = this.$route.meta.type || 'center'
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      this.scopes = this.user.scopes
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    jumpToAssetsRelated(actTabVal, keyword) {
      this.changeMenuId('10-2')
      sessionStorage.setItem('menuId', '10-2')
      this.$router.push({ path: '/intelligenceRelated', query: { actTabVal, keyword } })
    },
    async getIntelligenceTypeCount() {
      let res = await intelligenceTypeCount({
        operate_company_id: this.currentCompany
      })
      res.data.items.forEach((item) => {
        this.$set(this.typeCount, item.module, item.count)
      })
    },
    updatePageLoading(val) {
      this.pageLoading = val
    },
    handleClick(item) {
      this.componentTag = item.components
      this.actTabVal = item.value
      if (this.$route.path == '/intelligenceCenterv1') {
        // this.$router.replace({ query: {}})
        this.$router.replace({ query: { actTabVal: item.components } })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 100%;
}
.tab_container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}
.tab_title {
  transition: all 500ms ease;
  position: relative;
  box-sizing: border-box;
  height: 142px;
  padding: 20px;
  padding-bottom: 12px;
  margin-right: 16px;
  border: 2px solid #fff;
  border-radius: 4px;
  background: linear-gradient(141deg, #dfeafe 42%, rgba(223, 234, 254, 0) 100%);
  flex: 1;
  width: 0;
  .tab-content {
    height: 100%;
    overflow: hidden;
  }
  &:last-child {
    margin-right: 0;
  }
  &.active {
    padding: 20px 103px 12px 20px;
    width: 334px !important;
    border: 2px solid #2677ff;
    background:
      linear-gradient(141deg, #dfeafe 42%, rgba(223, 234, 254, 0) 100%),
      url('../../assets/images/intellBgi.png') no-repeat top right;

    .title {
      color: #2677ff;
    }
    .logo {
      transition: all 100ms ease;
      bottom: 0;
      width: 80px;
      height: 70px;
    }
    .tag,
    .content,
    .titleCount,
    .pointer {
      opacity: 1;
    }
    .count {
      display: none;
    }
  }

  &:hover {
    cursor: pointer;
    padding: 20px 103px 12px 20px;
    width: 334px !important;
    border: 2px solid #2677ff;
    background:
      linear-gradient(141deg, #dfeafe 42%, rgba(223, 234, 254, 0) 100%),
      url('../../assets/images/intellBgi.png') no-repeat top right;

    .title {
      color: #2677ff;
    }
    .logo {
      transition: all 100ms ease;
      bottom: 0;
      width: 80px;
      height: 70px;
    }
    .tag,
    .content,
    .titleCount {
      opacity: 1;
    }
    .count {
      display: none;
    }
  }
  .title {
    height: 22px;
    font-size: 16px;
    font-weight: 600;

    color: #37393c;
    line-height: 22px;
  }
  .count {
    font-size: 24px;
    font-weight: 600;
  }
  .content {
    color: #62666c;
    margin: 6px 0 10px;
    // 超出两行省略
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .tag {
    width: 100%;
    max-width: 170px;

    .block {
      box-sizing: border-box;
      display: inline-block;
      width: 40%;
      margin-right: 5px;
      padding: 3px 8px;
      border-radius: 2px;
      border: 1px solid #2677ff;
      font-size: 12px;
      color: #2677ff;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .logo {
    position: absolute;
    right: 10px;
    top: 0;
    bottom: -44px;
    margin: auto 0;
    width: 70px;
    height: 60px;
    img {
      width: 100%;
    }
  }
  .pointer {
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    margin: auto;
    width: 0;
    height: 0;
    border-top: 8px solid #2677ff;
    border-right: 8px solid transparent;
    border-left: 8px solid transparent;
  }
  .tag,
  .content,
  .titleCount,
  .pointer {
    opacity: 0;
  }
}
.table {
  height: calc(100% - 158px);
}
/deep/.tableList {
  box-sizing: border-box;
  height: 100%;
  padding: 16px 20px;
  background-color: #fff;
  .tableComponent {
    height: calc(100% - 20px);
  }
  .filterTab {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 16px;
    .el-input {
      margin-right: 16px;
    }
    & > div {
      display: flex;
      align-items: center;
      .normalBtnRe {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .el-input {
        width: 240px;
      }
      & > span {
        font-weight: 400;
        color: #2677ff;
        line-height: 20px;
        margin-left: 20px;
        cursor: pointer;
      }
    }
  }
}
</style>
