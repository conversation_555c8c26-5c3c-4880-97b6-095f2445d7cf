<template>
  <div class="tableList">
    <div class="filterTab" v-if="pageType != 'brief'">
      <div class="filterInput">
        <el-input
          v-model="formInline.title"
          placeholder="请输入完整的标题检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-input
          v-model="formInline.keyword"
          placeholder="请输入完整的关键词检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-input
          v-model="formInline.url"
          placeholder="请输入访问URL检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-input
          v-model="formInline.platform"
          placeholder="请输入完整的平台检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
      </div>
      <div>
        <el-button
          v-if="pageType && pageType == 'manage'"
          :disabled="user.role != 1 && !scopes.includes('intelligence_manager')"
          class="normalBtnRe"
          style="margin-left: 10px"
          @click="add()"
          type="primary"
          >新增</el-button
        >
      </div>
    </div>
    <div class="tableComponent">
      <!-- <tableList :tableHeader="tableHeader"></tableList> -->
      <tableList
        :pageType="pageType"
        :tableHeader="tableHeader"
        :tableData="tableData"
        @updateList="updateList"
        :total="total"
      >
        <template v-slot:judge="{ row, itemName }">
          <div v-if="itemName == 'screenshot'">
            <el-image
              @click="clickevaluatePicture(row.screenshot)"
              :preview-src-list="evaluatePictureList"
              class="imgCardBox"
              :src="
                row.screenshot && row.screenshot.includes('http')
                  ? row.screenshot
                  : showSrcIp + row.screenshot
              "
              lazy
            >
              <div slot="error" class="image-slot">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-kong"></use>
                </svg>
                <div class="imgKongBtn">加载失败</div>
              </div>
            </el-image>
          </div>
          <span v-else-if="itemName == 'sample'">
            <el-button v-if="row.sample" type="text" @click="download(row.sample)">下载</el-button>
            <span v-else>-</span>
          </span>
          <span v-else-if="itemName == 'url'" style="display: flex">
            <a
              class="ellipsis"
              v-if="row[itemName] && String(row[itemName]).includes('http')"
              style="color: #409eff"
              :href="row[itemName]"
              target="_blank"
              >{{ row[itemName] }}</a
            >
            <span class="ellipsis" v-else>{{ row[itemName] || '-' }}</span>
            <span class="greenLine" v-if="row.is_public == 1">公开</span>
            <span class="blueLine" v-else>非公开</span>
          </span>
          <span v-else>
            {{ row[itemName] || '-' }}
          </span>
        </template>
        <template slot="other">
          <el-table-column fixed="right" label="操作" align="left" :width="operationWidth">
            <template slot-scope="{ row }">
              <template v-if="pageType != 'brief'">
                <el-button
                  v-if="pageType && pageType == 'manage'"
                  type="text"
                  size="small"
                  @click="edit(row)"
                  >编辑</el-button
                >
                <el-button type="text" size="small" @click="getDetail(row)">详情</el-button>
              </template>
              <template v-else>
                <el-button type="text" size="small" @click="jumpToSummaryPage(row)">详情</el-button>
              </template>
            </template>
          </el-table-column>
        </template>
      </tableList>
    </div>
    <formData
      ref="formData"
      :title="currentRow.id ? '其他情报编辑' : '其他情报录入'"
      :FormData="FormData"
      :addForm="currentRow"
      :searchHandle="searchHandle"
      :dialogFormVisible="addDialogFormVisible"
      @sublime="submit"
      @closeDialog="addDialogFormVisible = false"
    ></formData>
    <!-- 详情 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="detailDialogFormVisible"
      width="880px"
    >
      <template slot="title"> 其他情报详情 </template>
      <div class="dialog-body" style="max-height: 813px">
        <div class="bottom">
          <div class="base clearfix">
            <div
              class="item"
              :class="[item.type, item.className]"
              v-for="(item, key) in detailFormData"
              :key="key"
            >
              <template v-if="(item.pageType && item.pageType == pageType) || !item.pageType">
                <span class="title">{{ item.label }}：</span>
                <template v-if="item.type === 'Upload'">
                  <template v-if="currentRow[item.prop]">
                    <div class="screenshot" v-if="item.isImage">
                      <el-image
                        @click="clickevaluatePicture(currentRow[item.prop])"
                        :preview-src-list="evaluatePictureList"
                        :src="showSrcIp + currentRow[item.prop]"
                        lazy
                      ></el-image>
                    </div>
                    <div class="download" v-else
                      >{{ currentRow[item.name]
                      }}<span @click="download(currentRow[item.url])" class="downloadBtn"
                        >下载</span
                      ></div
                    >
                  </template>
                  <template v-else> 暂无 </template>
                </template>
                <template v-else-if="item.type === 'textarea'">
                  <div class="content" style="white-space: pre-line">{{
                    currentRow[item.prop]
                  }}</div>
                </template>
                <template v-else>
                  <span class="value" v-if="item.prop === 'is_public'">{{
                    currentRow[item.prop] == 1 ? '公开' : '非公开'
                  }}</span>
                  <span class="value" v-else-if="item.prop === 'url'">
                    <el-tooltip effect="dark" :content="currentRow[item.prop]" placement="top">
                      <a
                        v-if="
                          currentRow[item.prop] && String(currentRow[item.prop]).includes('http')
                        "
                        style="color: #409eff"
                        :href="currentRow[item.prop]"
                        target="_blank"
                        >{{ currentRow[item.prop] }}</a
                      >
                      <span v-else>{{ currentRow[item.prop] }}</span>
                    </el-tooltip>
                  </span>
                  <!-- <span class="value" v-else-if="item.prop === 'title'">
                      <el-tooltip effect="dark" :content="currentRow[item.prop]" placement="top">
                          {{ currentRow[item.prop] || '-'}}
                      </el-tooltip>
                    </span> -->
                  <span class="value" v-else>{{ currentRow[item.prop] || '-' }}</span>
                </template>
              </template>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import formData from './formData.vue'

import tableList from './table.vue'
import {
  otherListManage,
  otherList,
  intellCompanyList,
  otherAdd,
  otherEdit
} from '@/api/apiConfig/api.js'

export default {
  components: {
    tableList,
    formData
  },
  props: {
    scopes: {
      type: Array,
      default: () => []
    },
    userInfo: {
      type: Object,
      default: () => ({})
    },
    user: {
      type: Object,
      default: () => ({})
    },
    currentCompany: {
      type: [String, Number],
      default: ''
    },
    pageType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      evaluatePictureList: [],
      isPublicMap: {
        1: '公开',
        2: '非公开'
      },
      detailDialogFormVisible: false,
      formInline: {
        url: ''
      },
      currentRow: {},
      currentRowCopy: {},
      addDialogFormVisible: false,
      addForm: {},
      searchHandle: [
        {
          type: '',
          label: '取消',
          className: 'highBtnRe',
          handle: () => {
            this.addDialogFormVisible = false
            this.addForm = {}
            this.$refs.formData.reset()
          }
        },
        {
          type: '',
          label: '发布',
          className: 'highBtn',
          handle: () => {
            this.$refs.formData.sublime()
          }
        },
        {
          type: '',
          label: '重置',
          className: 'highBtnRe',
          handle: () => {
            this.currentRow = JSON.parse(JSON.stringify(this.currentRowCopy))
            this.$refs.formData.reset()
          }
        }
      ],
      FormData: {
        url: {
          type: 'Input',
          label: 'URL',
          prop: 'url',
          placeholder: '请输入URL',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },

        platform: {
          type: 'Input',
          label: '来源平台',
          prop: 'platform',
          placeholder: '请输入来源平台',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        keyword: {
          type: 'Input',
          label: '关键字',
          prop: 'keyword',
          placeholder: '请输入关键字',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        poster: {
          type: 'Input',
          label: '发帖人',
          prop: 'poster',
          placeholder: '请输入发帖人'
          // rules: [
          //   { required: true, message: "该选项不能为空", trigger: "blur" }
          // ]
        },
        // companyType: {
        //   type: "Select",
        //   label: "单位类型",
        //   prop: "companyType",
        //   rules: [
        //     { required: true, message: "该选项不能为空", trigger: "blur" }
        //   ],
        //   options: [
        //     {
        //       label:"SaaS用户",
        //       value:"SaaS",
        //       Select:true
        //     },
        //     {
        //       label:"本地化用户",
        //       value:"local"
        //     },
        //   ],
        //   labelName: 'label',
        //   valueName: 'value',
        //   className: 'one'
        // },
        company: {
          type: 'Select',
          label: '主体单位',
          prop: 'company',
          allowCreate: true,
          multiple: true,
          placeholder: '请输入主体单位',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          options: [],
          labelName: '',
          valueName: '',
          className: 'one'
        },
        article_id: {
          type: 'Input',
          label: '编号',
          prop: 'article_id',
          placeholder: '请输入编号'
          // rules: [
          //   { required: true, message: "该选项不能为空", trigger: "blur" }
          // ]
        },
        title: {
          type: 'Input',
          label: '标题',
          prop: 'title',
          placeholder: '请输入标签',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          className: 'one'
        },
        article_context: {
          type: 'textarea',
          label: '详情',
          prop: 'article_context',
          placeholder: '请输入详情',
          // rules: [
          //   { required: true, message: "该选项不能为空", trigger: "blur" }
          // ],
          className: 'one'
        },

        sample: {
          type: 'Upload',
          isImage: false, // 是否是图片上传，必设置参数
          label: '上传样本',
          prop: 'sample',
          placeholder: '请上传样本',
          // rules: [
          //   { required: true, message: "该选项不能为空", trigger: "blur" }
          // ],
          url: 'sample',
          name: 'sample_file_name',
          className: 'one'
        },
        screenshot: {
          type: 'Upload',
          isImage: true,
          label: '上传截图',
          prop: 'screenshot',
          placeholder: '请上传截图',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'change' }],
          url: 'screenshot',
          name: 'screenshotName',
          className: 'one'
        },
        // {
        //   type: "Switch",
        //   label: "置顶",
        //   prop: "article_context",
        //   // placeholder: "请输入详情",
        //   rules: [
        //     { required: true, message: "该选项不能为空", trigger: "blur" }
        //   ],
        //   className: 'one'
        // },
        found_at: {
          type: 'DateTime',
          label: '暗网发布时间',
          prop: 'found_at',
          placeholder: '请选择暗网发布时间',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          change: () => {},
          className: 'two'
        },
        is_public: {
          type: 'Switch',
          label: '是否公开',
          prop: 'is_public',
          activeVal: 1,
          inactiveVal: 0,
          placeholder: '请输入详情',
          className: 'one'
        }
      },
      detailFormData: {
        url: {
          type: 'Input',
          label: 'URL',
          prop: 'url',
          placeholder: '请输入URL',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },

        platform: {
          type: 'Input',
          label: '来源平台',
          prop: 'platform',
          placeholder: '请输入来源平台',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        keyword: {
          type: 'Input',
          label: '关键字',
          prop: 'keyword',
          placeholder: '请输入关键字',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        poster: {
          type: 'Input',
          label: '发帖人',
          prop: 'poster',
          placeholder: '请输入发帖人'
          // rules: [
          //   { required: true, message: "该选项不能为空", trigger: "blur" }
          // ]
        },
        // companyType: {
        //   type: "Select",
        //   label: "单位类型",
        //   prop: "companyType",
        //   placeholder: "请输入主体单位",
        //   rules: [
        //     { required: true, message: "该选项不能为空", trigger: "blur" }
        //   ],
        //   options: [
        //     {
        //       label:"SaaS用户",
        //       value:"SaaS",
        //       Select:true
        //     },
        //     {
        //       label:"本地化用户",
        //       value:"local"
        //     },
        //   ],
        //   labelName: 'label',
        //   valueName: 'value',
        //   className: 'one'
        // },
        company: {
          type: 'Select',
          label: '主体单位',
          prop: 'company',
          allowCreate: true,
          multiple: true,
          placeholder: '请输入主体单位',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          options: [],
          labelName: '',
          valueName: '',
          className: 'one',
          pageType: 'manage'
        },
        article_id: {
          type: 'Input',
          label: '编号',
          prop: 'article_id',
          placeholder: '请输入编号'
        },
        title: {
          type: 'Input',
          label: '标题',
          prop: 'title',
          placeholder: '请输入标签',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          className: 'one'
        },
        article_context: {
          type: 'textarea',
          label: '详情',
          prop: 'article_context',
          placeholder: '请输入详情',
          // rules: [
          //   { required: true, message: "该选项不能为空", trigger: "blur" }
          // ],
          className: 'one'
        },

        sample: {
          type: 'Upload',
          isImage: false, // 是否是图片上传，必设置参数
          label: '样本',
          prop: 'sample',
          placeholder: '请上传样本',
          // rules: [
          //   { required: true, message: "该选项不能为空", trigger: "blur" }
          // ],
          url: 'sample',
          name: 'sample_file_name',
          className: 'one'
        },
        screenshot: {
          type: 'Upload',
          isImage: true,
          label: '截图',
          prop: 'screenshot',
          placeholder: '请上传截图',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'change' }],
          url: 'screenshot',
          name: 'screenshotName',
          className: 'one'
        },
        // {
        //   type: "Switch",
        //   label: "置顶",
        //   prop: "article_context",
        //   // placeholder: "请输入详情",
        //   rules: [
        //     { required: true, message: "该选项不能为空", trigger: "blur" }
        //   ],
        //   className: 'one'
        // },
        found_at: {
          type: 'DateTime',
          label: '其他情报平台帖子发布时间',
          prop: 'found_at',
          placeholder: '请选择录入时间',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          change: () => {},
          className: 'two'
        },
        is_public: {
          type: 'Switch',
          label: '是否公开',
          prop: 'is_public',
          activeVal: 1,
          inactiveVal: 0,
          placeholder: '请输入详情',
          className: 'one'
        }
      },
      searchParams: {
        page: 1,
        per_page: 10
      },
      total: 0,
      tableData: [],
      // 其他情报
      tableHeader: [
        {
          label: '其他情报平台帖子发布时间',
          name: 'found_at',
          fixed: 'left',
          minWidth: 180
        },
        {
          label: '访问URL',
          name: 'url',
          minWidth: 150
        },
        {
          label: '平台',
          name: 'platform',
          minWidth: 100
        },
        {
          label: '关键词',
          name: 'keyword',
          minWidth: 100
        },
        {
          label: '发帖人',
          name: 'poster',
          minWidth: 100
        },
        {
          label: '标题',
          name: 'title',
          minWidth: 100
        },
        {
          label: '样本',
          name: 'sample',
          minWidth: 100
        },
        {
          label: '截图',
          name: 'screenshot',
          minWidth: 100
        },
        {
          label: '编号',
          name: 'article_id',
          minWidth: 100
        },
        {
          label: '创建时间',
          name: 'article_created_at',
          minWidth: 100
        },
        {
          label: '详情',
          name: 'article_context',
          minWidth: 100
        }
      ]
    }
  },
  mounted() {
    if (this.$route.query.keyword) {
      let { keyword } = this.$route.query
      this.formInline.url = keyword
      Object.assign(this.searchParams, { page: 1, per_page: 10, ...this.formInline })
    }
    if (this.user.role == 2 && !this.currentCompany) {
      return
    }
    this.getData()
    this.getIntellCompanyList()
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getData()
        this.getIntellCompanyList()
      }
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany']),
    operationWidth() {
      let widthMap = {
        brief: '90px',
        manage: '90px'
      }
      return widthMap[this.pageType] || '90px'
    }
  },
  methods: {
    jumpToSummaryPage(row) {
      this.$emit('jumpToSummaryPageFn', 'other', row.url)
    },
    clickevaluatePicture(url) {
      var srclist = []
      srclist.push(this.showSrcIp + url)
      this.evaluatePictureList = srclist // 赋值
    },
    getDetail(row) {
      // let rowCopy = JSON.parse(JSON.stringify(row))
      // rowCopy.company = rowCopy.company ? rowCopy.company.split(',') : ''
      this.currentRow = JSON.parse(JSON.stringify(row))
      this.detailDialogFormVisible = true
      console.log(row)
    },
    checkFuncList() {
      this.updateList({ page: 1, per_page: 10, ...this.formInline })
    },
    download(URL) {
      window.location.href = this.showSrcIp + URL
      this.$message.success('下载成功')
    },
    async getIntellCompanyList() {
      let res = await intellCompanyList({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.$set(this.FormData.company, 'options', res.data)
      }
    },
    edit(row) {
      let rowCopy = JSON.parse(JSON.stringify(row))
      rowCopy.company = rowCopy.company ? rowCopy.company.split(',') : []
      this.currentRowCopy = JSON.parse(JSON.stringify(rowCopy))
      this.currentRow = JSON.parse(JSON.stringify(this.currentRowCopy))
      this.addDialogFormVisible = true
      // console.log(this.FormData.company)
    },
    add() {
      let row = {
        found_at: this.$refs.formData.getNowTime()
      }
      this.currentRowCopy = JSON.parse(JSON.stringify(row))
      this.currentRow = JSON.parse(JSON.stringify(this.currentRowCopy))
      this.addDialogFormVisible = true
    },
    async submit(addForm) {
      let funcName = addForm.id ? otherEdit : otherAdd
      let params = JSON.parse(JSON.stringify(addForm))
      params.company = params.company && params.company.join(',')
      let res = await funcName({ ...params, operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.addDialogFormVisible = false
        this.addForm = {}
        this.$refs.formData.reset()
        this.getData()
      }
    },
    updateList(data) {
      let searchParam = JSON.parse(JSON.stringify(this.searchParams))
      this.searchParams = Object.assign(searchParam, data)
      this.getData()
    },
    async getData(data) {
      let funcName = this.pageType && this.pageType == 'manage' ? otherListManage : otherList
      this.$emit('updatePageLoading', true)
      let res = await funcName({
        ...this.searchParams,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.$emit('updatePageLoading', false)
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
      } else {
        this.$message.error(res.message)
      }
      this.$emit('updatePageLoading', false)
    }
  }
}
</script>

<style lang="less" scoped>
.tableList {
  height: 100%;
}
/deep/.el-image {
  position: relative;
  overflow: visible;
  display: block;
}
.imgCardContainer {
  position: relative;
}
.imgKongBtn {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto 0;
  left: calc(100% + 10px);
  // z-index: 9999;
}
.imgCardBox {
  height: 30px;
  // width: 100%;
  // height: 100%;
  /deep/.image-slot {
    height: 100%;
    width: 100%;
    color: #606266;
    background: #e9ebef;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    text-align: center;
    svg {
      display: inline-block;
      font-size: 25px;
    }

    p {
      width: 100%;
      line-height: 25px;
      color: #d1d5dd;
      span {
        margin-left: 4px;
        color: #2677ff;
      }
      .el-button {
        padding: 0;
      }
    }
  }
}

.elDialogAdd {
  /deep/.el-dialog__body {
    padding: 0;
    // max-height: 613px;
  }
  .top {
    box-sizing: border-box;
    display: flex;
    height: 157px;
    padding: 20px 24px;

    // background: linear-gradient(180deg, #FFF4EB 0%, #FFFFFF 100%), #FFFFFF;

    // background: linear-gradient(180deg, #FFF3F3 0%, #FFFFFF 100%), #FFFFFF;

    // background: linear-gradient(180deg, #F1E2E2 0%, #FFFFFF 100%), #FFFFFF;
    .left {
      width: 110px;
      margin-right: 16px;
    }
    .right {
      width: 0;
      flex: 1;
      display: flex;
      justify-content: center;
      flex-direction: column;
      .title {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
      }
      .levelDiv {
        box-sizing: border-box;
        width: 86px;
        padding: 4px 12px;
        border-radius: 14px;
      }
      .level {
        display: inline-block;
        margin-right: 4px;
        span {
          display: inline-block;
          width: 6px;
          height: 10px;
          background: #eaceba;
          transform: skewX(-25deg);
          margin-right: 1px;
        }
      }
    }
  }
  .middleLine {
    margin: 0 20px;
    height: 1px;
    width: calc(100% - 40px);
    background: #e3e5ea;
  }
  .bottom {
    padding: 20px 20px 24px;
    .title {
      margin-bottom: 12px;
    }
    .base {
      margin-bottom: 12px;
      .item {
        float: left;
        width: 50%;
        height: 20px;
        // line-height: 50px;
        margin-bottom: 12px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &.one {
          width: 100%;
          height: auto;
        }
        &.two {
          width: 50%;
          height: 20px;
        }
        &.three {
          width: 33%;
          height: auto;
        }
        .label {
          color: rgba(98, 102, 108, 0.6);
        }
        .value {
          color: #37393c;
        }
        .content {
          border-radius: 4px;
          box-sizing: border-box;
          // height: 96px;
          padding: 16px;
          max-height: 200px;
          overflow: auto;
          background-color: #f5f8fc;
          div:first-child {
            margin-bottom: 12px;
          }
        }
      }
    }
    // .solution{

    // }
    .effect {
      margin-top: 24px;
      .content {
        border-radius: 4px;
        box-sizing: border-box;
        padding: 16px;
        background-color: #f5f8fc;
        div:first-child {
          margin-bottom: 12px;
        }
      }
    }
    .ipTable {
      margin-top: 24px;
      .content {
        height: 570px;
      }
    }
  }
}
.clearfix::after {
  content: '';
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}

.screenshot {
  width: 200px;
  height: 200px;
  position: relative;
  /deep/.el-image {
    width: 100%;
    height: 100%;
  }
}
.download {
  span {
    margin-left: 20px;
    &:hover {
      cursor: pointer;
      color: #2677ff;
    }
  }
}
</style>
