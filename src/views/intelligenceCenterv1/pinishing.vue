<template>
  <div class="tableList">
    <div class="filterTab" v-if="pageType != 'brief'">
      <div class="filterInput">
        <el-input
          v-model="formInline.url"
          placeholder="请输入完整的仿冒url检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-input
          v-model="formInline.target"
          placeholder="请输入完整的仿冒目标检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-select
          v-model="formInline.status"
          placeholder="请选择状态"
          @change="checkFuncList"
          clearable
          collapse-tags
          id="user_select"
        >
          <el-option
            :label="v.name"
            :value="v.value"
            v-for="v in statusArr"
            :key="v.value"
          ></el-option>
        </el-select>
      </div>
      <div>
        <el-button
          v-if="pageType && pageType == 'manage'"
          :disabled="user.role != 1 && !scopes.includes('intelligence_manager')"
          class="normalBtnRe"
          style="margin-left: 10px"
          @click="add({})"
          type="primary"
          >新增</el-button
        >
      </div>
    </div>
    <div class="tableComponent">
      <tableList
        :pageType="pageType"
        :tableHeader="tableHeader"
        :isSelectData="isSelectData"
        :tableData="tableData"
        @updateList="updateList"
        :total="total"
      >
        <template v-slot:judge="{ row, itemName }">
          <span v-if="itemName == 'status'">{{
            statusArrMap[row[itemName]] ? statusArrMap[row[itemName]] : '-'
          }}</span>
          <span v-else-if="itemName == 'url'">
            <a
              v-if="row[itemName] && String(row[itemName]).includes('http')"
              style="color: #409eff"
              :href="row[itemName]"
              target="_blank"
              >{{ row[itemName] }}</a
            >
            <span v-else>{{ row[itemName] || '-' }}</span>
          </span>
          <span v-else>{{ row[itemName] || '-' }}</span>
        </template>
        <template slot="other">
          <el-table-column
            v-if="pageType !== 'center'"
            fixed="right"
            label="操作"
            align="left"
            width="120"
          >
            <template slot-scope="{ row }">
              <el-button
                v-if="pageType && pageType == 'manage'"
                type="text"
                size="small"
                @click="add(row)"
                >编辑</el-button
              >
              <el-button
                v-if="pageType == 'brief'"
                type="text"
                size="small"
                @click="jumpToSummaryPage(row)"
                >详情</el-button
              >
            </template>
          </el-table-column>
        </template>
      </tableList>
    </div>
    <formData
      ref="formData"
      :title="currentRow.id ? '钓鱼仿冒编辑' : '钓鱼仿冒录入'"
      :FormData="FormData"
      :addForm="currentRow"
      :searchHandle="searchHandle"
      :dialogFormVisible="addDialogFormVisible"
      @sublime="submit"
      @closeDialog="addDialogFormVisible = false"
    ></formData>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import formData from './formData.vue'

import tableList from './table.vue'
import { fakeListManage, fakeList, fakeAdd, fakeEdit } from '@/api/apiConfig/api.js'

export default {
  components: {
    tableList,
    formData
  },
  props: {
    scopes: {
      type: Array,
      default: () => []
    },
    userInfo: {
      type: Object,
      default: () => ({})
    },
    user: {
      type: Object,
      default: () => ({})
    },
    currentCompany: {
      type: [String, Number],
      default: ''
    },
    pageType: {
      type: String,
      default: ''
    },
    isSelectData: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let statusArr = [
      { name: '在线', value: 1 },
      { name: '离线', value: 2 }
    ]
    return {
      statusArr,
      statusArrMap: {
        1: '在线',
        2: '离线'
      },
      formInline: {
        url: ''
      },
      addDialogFormVisible: false,
      addForm: {},
      searchHandle: [
        {
          type: '',
          label: '取消',
          className: 'highBtnRe',
          handle: () => {
            this.addDialogFormVisible = false
            this.addForm = {}
            this.$refs.formData.reset()
          }
        },
        {
          type: '',
          label: '发布',
          className: 'highBtn',
          handle: () => {
            this.$refs.formData.sublime()
          }
        },
        {
          type: '',
          label: '重置',
          className: 'highBtnRe',
          handle: () => {
            this.currentRow = JSON.parse(JSON.stringify(this.currentRowCopy))
            this.$refs.formData.reset()
          }
        }
      ],
      FormData: {
        target: {
          type: 'Input',
          label: '仿冒目标',
          prop: 'target',
          placeholder: '请输入仿冒目标',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        source: {
          type: 'Input',
          label: '数据来源',
          prop: 'source',
          placeholder: '请输入数据来源',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        url: {
          type: 'Input',
          label: 'URL',
          prop: 'url',
          placeholder: '请输入URL',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        title: {
          type: 'Input',
          label: '标题',
          prop: 'title',
          placeholder: '请输入标题',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        cloud_name: {
          type: 'Input',
          label: '云厂商',
          prop: 'cloud_name',
          placeholder: '请输入云厂商',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        country: {
          type: 'Input',
          label: '国家',
          prop: 'country',
          placeholder: '请输入国家',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        ip: {
          type: 'Input',
          label: 'IP',
          prop: 'ip',
          placeholder: '请输入IP',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        status: {
          type: 'Select',
          label: '状态',
          prop: 'status',
          placeholder: '请输入状态',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          options: statusArr,
          labelName: 'name',
          valueName: 'value'
        },
        found_at: {
          type: 'DateTime',
          label: '录入时间',
          prop: 'found_at',
          placeholder: '请选择录入时间',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          change: () => {},
          className: 'two'
        }
      },
      searchParams: {
        page: 1,
        per_page: 10
      },
      total: 0,
      tableData: [],
      // 钓鱼仿冒
      tableHeader: [
        {
          label: '发现时间',
          name: 'found_at',
          fixed: 'left',
          minWidth: 100
        },
        {
          label: '钓鱼URL',
          name: 'url',
          minWidth: 100
        },
        {
          label: 'IP',
          name: 'ip',
          minWidth: 100
        },
        {
          label: 'IP所在地（国家）',
          name: 'country',
          minWidth: 100
        },
        {
          label: '仿冒目标',
          name: 'target',
          minWidth: 100
        },
        {
          label: '云服务商',
          name: 'cloud_name',
          minWidth: 100
        },
        {
          label: '状态',
          name: 'status',
          minWidth: 100
        }
      ],
      currentRow: {},
      currentRowCopy: {}
    }
  },
  mounted() {
    if (this.$route.query.keyword) {
      let { keyword } = this.$route.query
      this.formInline.url = keyword
      Object.assign(this.searchParams, { page: 1, per_page: 10, ...this.formInline })
    }
    if (this.user.role == 2 && !this.currentCompany) {
      return
    }
    this.getData()
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getData()
      }
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    jumpToSummaryPage(row) {
      this.$emit('jumpToSummaryPageFn', 'pinishing', row.url)
    },
    checkFuncList() {
      this.updateList({ page: 1, per_page: 10, ...this.formInline })
    },
    add(row) {
      if (!row.id) {
        row = {
          found_at: this.$refs.formData.getNowTime()
        }
      }
      this.currentRowCopy = JSON.parse(JSON.stringify(row))
      this.currentRow = JSON.parse(JSON.stringify(this.currentRowCopy))
      this.addDialogFormVisible = true
    },
    async submit(addForm) {
      let funcName = addForm.id ? fakeEdit : fakeAdd
      let res = await funcName({ ...addForm, operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.addDialogFormVisible = false
        this.addForm = {}
        this.$refs.formData.reset()
        this.getData()
      }
    },
    updateList(data) {
      let searchParam = JSON.parse(JSON.stringify(this.searchParams))
      this.searchParams = Object.assign(searchParam, data)
      this.getData()
    },
    async getData() {
      this.$emit('updatePageLoading', true)

      let funcName = this.pageType && this.pageType == 'manage' ? fakeListManage : fakeList
      let res = await funcName({
        ...this.searchParams,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.$emit('updatePageLoading', false)
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
      } else {
        this.$message.error(res.message)
      }
      this.$emit('updatePageLoading', false)
    }
  }
}
</script>

<style lang="less" scoped>
.tableList {
  height: 100%;
}
</style>
