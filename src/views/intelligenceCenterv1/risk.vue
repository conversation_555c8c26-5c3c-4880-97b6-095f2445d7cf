<template>
  <div class="tableList">
    <div class="filterTab" v-if="pageType != 'brief'">
      <div class="filterInput">
        <el-input
          v-model="formInline.domain"
          placeholder="请输入完整的域名检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-input
          v-model="formInline.type"
          placeholder="请输入完整的风险类型检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-input
          v-model="formInline.url"
          placeholder="请输入风险URL检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <el-select
          v-model="formInline.status"
          placeholder="请选择状态"
          @clear="searchParams.status = 0"
          @change="checkFuncList"
          clearable
          collapse-tags
          id="user_select"
        >
          <el-option
            :label="v.label"
            :value="v.value"
            v-for="v in statusArr"
            :key="v.value"
          ></el-option>
        </el-select>
      </div>
      <div>
        <el-button
          v-if="pageType && pageType == 'manage'"
          :disabled="user.role != 1 && !scopes.includes('intelligence_manager')"
          class="normalBtnRe"
          style="margin-left: 10px"
          @click="add({})"
          type="primary"
          >新增</el-button
        >
        <el-button
          v-if="pageType != 'manage' && userInfo.is_show_copy_data != 1"
          class="normalBtnRe"
          style="margin-left: 10px"
          :disabled="checkBtnLoading || progressLoading"
          :loading="progressLoading"
          @click="oneKeyMatch([])"
          type="primary"
          >{{ progressLoading ? progress + '%' : '' }}&nbsp;一键匹配</el-button
        >
      </div>
    </div>
    <div class="tableComponent">
      <tableList
        :pageType="pageType"
        :tableHeader="tableHeaderCopy"
        :tableData="tableData"
        @updateList="updateList"
        :total="total"
      >
        <template v-slot:judge="{ row, itemName }">
          <span v-if="itemName == 'status'">{{
            statusArrMap[row[itemName]] ? statusArrMap[row[itemName]] : '-'
          }}</span>
          <span v-else-if="itemName == 'hit'">{{
            row[itemName] ? hitArrMap[row[itemName]] : '-'
          }}</span>
          <span v-else-if="itemName == 'url'">
            <a
              v-if="row[itemName] && String(row[itemName]).includes('http')"
              style="color: #409eff"
              :href="row[itemName]"
              target="_blank"
              >{{ row[itemName] }}</a
            >
            <span v-else>{{ row[itemName] || '-' }}</span>
          </span>
          <span v-else>{{ row[itemName] || '-' }}</span>
        </template>
        <template slot="other" v-if="pageType && pageType == 'manage'">
          <el-table-column fixed="right" label="操作" align="left" width="120">
            <template slot-scope="{ row }">
              <el-button type="text" size="small" @click="add(row)">编辑</el-button>
            </template>
          </el-table-column>
        </template>
        <template slot="other" v-if="pageType && pageType == 'brief'">
          <el-table-column fixed="right" label="操作" align="left" width="90">
            <template slot-scope="{ row }">
              <el-button type="text" size="small" @click="jumpToSummary(row)">详情</el-button>
            </template>
          </el-table-column>
        </template>
        <template
          slot="other"
          v-if="pageType != 'manage' && userInfo.is_show_copy_data != 1 && pageType != 'brief'"
        >
          <el-table-column fixed="right" label="操作" align="left" width="120">
            <template slot-scope="{ row, $index }">
              <el-button
                type="text"
                size="small"
                :loading="checkBtnLoading && currentIndex == $index"
                :disabled="checkBtnLoading || progressLoading"
                @click="oneKeyMatch([row.id], $index)"
                >匹配</el-button
              >
              <el-button
                v-if="pageType != 'manage'"
                type="text"
                size="small"
                @click="jumpToAssetsRelated(row)"
                >相关资产</el-button
              >
            </template>
          </el-table-column>
        </template>
      </tableList>
    </div>
    <formData
      ref="formData"
      :title="currentRow.id ? '风险情报编辑' : '风险情报录入'"
      :FormData="FormData"
      :addForm="currentRow"
      :searchHandle="searchHandle"
      :dialogFormVisible="addDialogFormVisible"
      @sublime="submit"
      @closeDialog="addDialogFormVisible = false"
    ></formData>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import formData from './formData.vue'
import tableList from './table.vue'
import {
  threatMatchProgress,
  threatMatch,
  threatListManage,
  threatList,
  threatAdd,
  threatEdit
} from '@/api/apiConfig/api.js'

export default {
  components: {
    tableList,
    formData
  },
  props: {
    scopes: {
      type: Array,
      default: () => []
    },
    userInfo: {
      type: Object,
      default: () => ({})
    },
    user: {
      type: Object,
      default: () => ({})
    },
    currentCompany: {
      type: [String, Number],
      default: ''
    },
    pageType: {
      type: String,
      default: ''
    }
  },
  data() {
    let statusArr = [
      { label: '在线', value: 1 },
      { label: '离线', value: 2 }
    ]
    return {
      currentIndex: null,
      checkBtnLoading: false,
      updateTimer: null,
      progressLoading: false,
      progress: 0,
      statusArr,
      statusArrMap: {
        1: '在线',
        2: '离线'
      },
      hitArrMap: {
        1: '命中',
        2: '未命中'
      },
      formInline: {
        url: ''
      },
      currentRow: {},
      currentRowCopy: {},
      addDialogFormVisible: false,
      addForm: {},
      options: [
        { label: '低危', value: '低危' },
        { label: '中危', value: '中危' },
        { label: '高危', value: '高危' },
        { label: '严重', value: '严重' }
      ],
      searchHandle: [
        {
          type: '',
          label: '取消',
          className: 'highBtnRe',
          handle: () => {
            this.addDialogFormVisible = false
            this.currentRow = {}
            this.$refs.formData.reset()
          }
        },
        {
          type: '',
          label: '发布',
          className: 'highBtn',
          handle: () => {
            this.$refs.formData.sublime()
          }
        },
        {
          type: '',
          label: '重置',
          className: 'highBtnRe',
          handle: () => {
            this.currentRow = JSON.parse(JSON.stringify(this.currentRowCopy))
            this.$refs.formData.reset()
          }
        }
      ],
      FormData: {
        url: {
          type: 'Input',
          label: 'URL',
          prop: 'url',
          placeholder: '请输入URL',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        ip: {
          type: 'Input',
          label: 'IP',
          prop: 'ip',
          placeholder: '请输入IP',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        source: {
          type: 'Input',
          label: '来源',
          prop: 'source',
          placeholder: '请输入来源',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        source: {
          type: 'Input',
          label: '国家',
          prop: 'country',
          placeholder: '请输入国家',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },

        domain: {
          type: 'Input',
          label: '域名',
          prop: 'domain',
          placeholder: '请输入域名'
        },
        status: {
          type: 'Select',
          label: '状态',
          prop: 'status',
          placeholder: '请选择状态',
          options: statusArr,
          labelName: 'label',
          valueName: 'value',
          change: () => {}
        },
        type: {
          type: 'Input',
          label: '风险类型',
          prop: 'type',
          placeholder: '请输入风险类型',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        },
        found_at: {
          type: 'DateTime',
          label: '录入时间',
          prop: 'found_at',
          placeholder: '请选择录入时间',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          change: () => {},
          className: 'two'
        },
        tags: {
          type: 'textarea',
          label: '标签',
          prop: 'tags',
          placeholder: '请输入标签,支持输入多个,分号或换行分隔',
          rules: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
          className: 'one'
        }
      },
      searchParams: {
        page: 1,
        per_page: 10
      },
      total: 0,
      tableData: [],
      // 风险情报
      tableHeader: [
        {
          label: '发现时间',
          name: 'found_at',
          fixed: 'left',
          minWidth: 100
        },
        {
          label: '风险URL',
          name: 'url',
          minWidth: 100
        },
        {
          label: '状态',
          name: 'status',
          minWidth: 100
        },
        {
          label: '风险类型',
          name: 'type',
          minWidth: 100
        },
        {
          label: '标签',
          name: 'tags',
          minWidth: 100
        },
        {
          label: '域名',
          name: 'domain',
          minWidth: 100
        },
        {
          label: 'IP地址',
          name: 'ip',
          minWidth: 100
        },
        {
          label: '国家',
          name: 'country',
          minWidth: 100
        },
        {
          label: '关联威胁资产库',
          name: 'hit',
          minWidth: 100,
          pageType: 'center'
        }
      ]
    }
  },
  mounted() {
    if (this.$route.query.keyword) {
      let { keyword } = this.$route.query
      this.formInline.url = keyword
      Object.assign(this.searchParams, { page: 1, per_page: 10, ...this.formInline })
    }

    if (this.user.role == 2 && !this.currentCompany) {
      return
    }
    this.getData()
    this.updateProgress(true)
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getData()
        this.updateProgress(true)
      }
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany']),
    tableHeaderCopy() {
      let a = this.tableHeader.filter(
        (item) => !item.pageType || (item.pageType && item.pageType == this.pageType)
      )
      return a
    }
  },
  methods: {
    jumpToSummary(row) {
      this.$emit('jumpToSummaryPageFn', 'risk', row.url)
    },
    jumpToAssetsRelated(row) {
      this.$emit('jumpToAssetsRelated', 'risk', row.url)
    },
    // 一键匹配
    async oneKeyMatch(id, index) {
      this.currentIndex = index
      if (id && id.length !== 0) {
        this.checkBtnLoading = true
        this.progressLoading = false
      } else {
        this.progressLoading = true
        this.checkBtnLoading = false
      }
      let res = await threatMatch({
        id,
        operate_company_id: this.currentCompany,
        ...this.searchParams
      }).catch(() => {
        this.progressLoading = false
        this.checkBtnLoading = false
      })
      if (res.code == 0) {
        if (id && id.length !== 0) {
          this.progressLoading = false
          this.checkBtnLoading = true
          setTimeout(() => {
            this.checkBtnLoading = false
            this.getData()
            this.$message.success('一键匹配已完成')
          }, 2000)
        } else {
          this.progressLoading = true
          this.checkBtnLoading = false
          this.updateProgress()
        }
      }
    },
    async updateProgress(isNotMsg) {
      let res = await threatMatchProgress({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        if ((res.data && res.data < 100) || res.data === 0) {
          this.progress = res.data
          this.progressLoading = true
          this.checkBtnLoading = false
          this.updateTimer = setTimeout(() => {
            this.updateProgress()
          }, 2000)
        } else {
          this.checkBtnLoading = false
          this.progressLoading = false
          this.progress = 0
          if (!isNotMsg) {
            this.$message.success('一键匹配已完成')
          }
          if (this.updateTimer) {
            clearTimeout(this.updateTimer)
          }
          if (!isNotMsg) {
            setTimeout(() => {
              this.getData()
            }, 1000)
          }
        }
      }
    },
    checkFuncList() {
      this.updateList({ page: 1, per_page: 10, ...this.formInline })
    },
    add(row) {
      row.tags = row.tags ? row.tags.split(',').join('\r') : ''
      if (!row.id) {
        row = {
          found_at: this.$refs.formData.getNowTime()
        }
      }
      this.currentRowCopy = JSON.parse(JSON.stringify(row))
      this.currentRow = JSON.parse(JSON.stringify(this.currentRowCopy))
      this.addDialogFormVisible = true
    },
    async submit(addForm) {
      let funcName = addForm.id ? threatEdit : threatAdd
      let params = JSON.parse(JSON.stringify(addForm))
      params.tags = params.tags && params.tags.split(/[；|;|\r\n]/).join(',')
      let res = await funcName({ ...params, operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.addDialogFormVisible = false
        this.addForm = {}
        this.$refs.formData.reset()
        this.getData()
      }
    },
    updateList(data) {
      let searchParam = JSON.parse(JSON.stringify(this.searchParams))
      this.searchParams = Object.assign(searchParam, data)
      this.getData()
    },
    async getData(data) {
      this.$emit('updatePageLoading', true)
      let funcName = this.pageType && this.pageType == 'manage' ? threatListManage : threatList
      let res = await funcName({
        ...this.searchParams,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.$emit('updatePageLoading', false)
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
      } else {
        this.$message.error(res.message)
      }
      this.$emit('updatePageLoading', false)
    }
  },
  beforeDestroy() {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer)
      this.updateTimer = null
    }
  }
}
</script>

<style lang="less" scoped>
.tableList {
  height: 100%;
}
</style>
