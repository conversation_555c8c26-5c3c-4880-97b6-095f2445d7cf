<template>
  <div class="tableList">
    <div class="filterTab" v-if="pageType != 'brief'">
      <div class="filterInput">
        <el-input
          v-model="formInline.keyword"
          placeholder="请输入专项情报名称检索"
          @keyup.enter.native="checkFuncList"
        >
          <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
        </el-input>
        <!-- 高级筛选 -->
        <span @click="highCheckdialog = true" id="user_filter" style="width: 80px"
          ><img
            src="../../assets/images/filter.png"
            alt=""
            style="width: 16px; vertical-align: middle; margin-right: 3px"
          />高级筛选</span
        >
      </div>
      <div>
        <el-button
          class="normalBtnRe"
          v-if="pageType != 'manage'"
          style="margin-left: 10px"
          :disabled="checkBtnLoading || progressLoading"
          :loading="progressLoading"
          @click="oneKeyCheck([])"
          type="primary"
          >{{ progressLoading ? progress + '%' : '' }}&nbsp;一键检测</el-button
        >
      </div>
    </div>
    <hightFilter
      id="hightFilter"
      :highTabShow="highTabShow"
      :highlist="highlist"
      :total="total"
      @highcheck="highCheck"
    >
    </hightFilter>
    <div :class="hightFilterIsShow()">
      <tableList
        :pageType="pageType"
        :tableHeader="tableHeader"
        :tableData="tableData"
        @updateList="updateList"
        :total="total"
      >
        <template v-slot:judge="{ row, itemName }">
          <span v-if="itemName == 'company_list' && row[itemName]">
            {{ row[itemName].replace(/\,/g, '&nbsp;&nbsp;，') || '-' }}
          </span>
          <span v-else>
            {{ row[itemName] || '-' }}
          </span>
        </template>
        <template slot="other">
          <el-table-column fixed="right" label="操作" align="left" :width="operationWidth">
            <template slot-scope="{ row, $index }">
              <template v-if="pageType != 'brief'">
                <el-button
                  v-if="pageType != 'manage'"
                  type="text"
                  size="small"
                  :disabled="checkBtnLoading || progressLoading"
                  :loading="checkBtnLoading && currentIndex == $index"
                  @click="oneKeyCheck([row.id], $index)"
                  >检测</el-button
                >
                <el-button
                  v-if="pageType != 'manage'"
                  type="text"
                  size="small"
                  @click="getDetail(row)"
                  >详情</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  :disabled="pageType && pageType != 'manage' && !row.has_masked_report"
                  @click="reportPreview(row)"
                  >查看报告</el-button
                >
                <el-button
                  v-if="pageType != 'manage'"
                  type="text"
                  size="small"
                  @click="jumpToAssetsRelated(row)"
                  >相关资产</el-button
                >
                <el-button
                  v-if="pageType && pageType == 'manage'"
                  type="text"
                  size="small"
                  @click="uploadReport(row)"
                  >上传报告</el-button
                >
                <el-button
                  v-if="pageType && pageType == 'manage'"
                  type="text"
                  size="small"
                  @click="edit(row)"
                  >编辑</el-button
                >
              </template>
              <template v-else>
                <el-button type="text" size="small" @click="jumpToSummaryPage(row)">详情</el-button>
              </template>
            </template>
          </el-table-column>
        </template>
      </tableList>
    </div>
    <el-dialog
      class="elDialogAdd edit-dialog"
      title="编辑"
      :close-on-click-modal="false"
      :visible.sync="addDialogFormVisible"
      width="480px"
    >
      <div class="dialog-body">
        <el-form class="search_input" label-width="84px">
          <el-form-item label="漏洞类型：">
            <el-select v-model="formData.category" allow-create filterable>
              <el-option
                v-for="(item, index) in categoryList"
                :label="item"
                :value="item"
                :key="index"
                >{{ item }}</el-option
              >
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="addDialogFormVisible = false" id="number_upload_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" @click="submit()" id="number_upload_sure">确定</el-button>
      </div>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="detailDialogFormVisible"
      width="880px"
    >
      <template slot="title"> 专项情报详情 </template>
      <div class="dialog-body" style="max-height: 813px">
        <div class="top High">
          <div class="left">
            <img :src="require(`../../assets/images/riskHigh.png`)" alt="" />
          </div>
          <div class="right">
            <div class="title">{{ currentRow.name }}</div>
          </div>
        </div>
        <div class="middleLine"></div>
        <div class="bottom">
          <div class="base clearfix">
            <div class="title">基础信息</div>
            <div class="item">
              <span class="label">漏洞类型：</span>
              <span class="value">{{ currentRow.category || '-' }}</span>
            </div>
          </div>
          <div class="solution">
            <div class="title">漏洞描述：</div>
            <div class="content">{{ currentRow.summary || '-' }}</div>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- <el-dialog class="elDialogAdd upload-dialog" :close-on-click-modal="false" :visible.sync="uploadDialogVisible"
      width="400px">
      <template slot="title">
        上传报告
      </template>
      <div class="dialog-body">
        <el-upload class="upload-demo" ref="upload" drag
          :action="`${golangUploadSrcIp}/intelligence/event/${dataId}/report/upload`" :headers="uploadHeaders"
          accept=".pdf" :before-upload="beforeIpUpload" :on-success="uploadSuccess" :on-remove="uploadRemove"
          :on-error="uploadError" :limit="uploadMaxCount" :on-exceed="handleExceed" :file-list="fileList"
          :auto-upload="false " :on-change="onChange">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">支持上传pdf文件，且大小不超过45M</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="uploadDialogVisible = false" id="number_upload_cancel">关闭</el-button>
        <el-button class="highBtn" :loading="btnLoading" @click="uploadSave" id="number_upload_sure">确定</el-button>
      </div>
    </el-dialog> -->
    <uploadReport
      :addDialogFormVisible="uploadDialogVisible"
      :action="`${golangUploadSrcIp}/intelligence/event/${dataId}/report/upload`"
      :dataId="dataId"
      @close="uploadDialogVisible = false"
    />
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="漏洞类型：" prop="role">
            <el-select
              v-model="formInline.category"
              placeholder="请选择漏洞类型"
              multiple
              collapse-tags
              @change="selectChange($event, 'category', categoryList, false, true)"
            >
              <el-option :label="v" :value="v" v-for="v in categoryList" :key="v"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="披露时间：" prop="disclosure_time">
            <el-date-picker
              v-model="formInline.disclosure_time"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="user_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="user_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import tableList from './table.vue'
import hightFilter from '../../components/assets/highTab.vue'
import uploadReport from './uploadReport.vue'
import {
  eventCategoryList,
  editEventCategory,
  eventListManage,
  eventListMatch,
  eventListMatchProgress,
  eventList,
  eventListReport,
  eventListMaskedReport
} from '@/api/apiConfig/api.js'

export default {
  components: {
    tableList,
    hightFilter,
    uploadReport
  },
  props: {
    scopes: {
      type: Array,
      default: () => []
    },
    userInfo: {
      type: Object,
      default: () => ({})
    },
    user: {
      type: Object,
      default: () => ({})
    },
    currentCompany: {
      type: [String, Number],
      default: ''
    },
    pageType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      highTabShow: [
        {
          label: '漏洞类型',
          name: 'category',
          type: 'select'
        },
        {
          label: '披露时间',
          name: 'disclosure_time',
          type: 'date'
        }
      ],
      formData: {
        category: ''
      },
      highlist: null,
      uploadDialogVisible: false,
      currentIndex: null,
      progress: 0,
      progressLoading: false,
      checkBtnLoading: false,
      highCheckdialog: false,
      uploadPath: '', // 数据泄露的上传后路径
      btnLoading: false,
      uploadAction: '',
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      detailDialogFormVisible: false,
      formInline: {
        keyword: ''
      },
      currentRow: {},
      addDialogFormVisible: false,
      searchParams: {
        page: 1,
        per_page: 10
      },
      total: 0,
      tableData: [],
      // 专项情报
      tableHeader: [
        {
          label: '披露时间',
          name: 'disclosure_time',
          fixed: 'left',
          minWidth: 180
        },
        {
          label: '专项名称',
          name: 'name',
          fixed: 'left',
          icon: 'select',
          minWidth: 180
        },
        {
          label: '漏洞类型',
          name: 'category',
          minWidth: 150
        },
        {
          label: '涉及企业数量',
          name: 'company_num',
          minWidth: 100
        },
        {
          label: '涉及企业',
          name: 'company_list',
          minWidth: 100
        },
        {
          label: '关联资产数量',
          name: 'asset_num',
          minWidth: 100
        },
        {
          label: '互联网影响资产',
          name: 'ip_count',
          minWidth: 100
        }
      ],
      dataId: '',
      categoryList: []
    }
  },
  mounted() {
    if (this.$route.query.keyword) {
      let { keyword } = this.$route.query
      this.formInline.keyword = keyword
      Object.assign(this.searchParams, { page: 1, per_page: 10, ...this.formInline })
    }
    if (this.user.role == 2 && !this.currentCompany) {
      return
    }
    this.getData()
    if (this.pageType && this.pageType != 'manage') {
      this.updateProgress(true)
    }
    if (this.pageType != 'brief') {
      this.getCategoryList()
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getData()
        if (this.pageType && this.pageType != 'manage') {
          this.updateProgress(true)
        }
        if (this.pageType != 'brief') {
          this.getCategoryList()
        }
      }
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany']),
    operationWidth() {
      let widthMap = {
        brief: '90px',
        manage: '230px'
      }
      return widthMap[this.pageType] || '230px'
    }
  },
  methods: {
    jumpToSummaryPage(row) {
      this.$emit('jumpToSummaryPageFn', 'special', row.name)
    },
    getCategoryList() {
      eventCategoryList({ operate_company_id: this.currentCompany }).then((res) => {
        this.categoryList = res.data.category
      })
    },
    jumpToAssetsRelated(row) {
      this.$emit('jumpToAssetsRelated', 'special', row.name)
    },
    async oneKeyCheck(id = [], index) {
      this.currentIndex = index
      if (id && id.length !== 0) {
        this.checkBtnLoading = true
        this.progressLoading = false
      } else {
        this.progressLoading = true
        this.checkBtnLoading = false
      }
      let res = await eventListMatch({
        id,
        operate_company_id: this.currentCompany,
        ...this.searchParams
      }).catch(() => {
        this.progressLoading = false
        this.checkBtnLoading = false
      })
      if (res.code == 0) {
        if (id && id.length !== 0) {
          // 单个检测
          this.progressLoading = false
          this.checkBtnLoading = true
          setTimeout(() => {
            this.$message.success('一键检测已完成')
            this.checkBtnLoading = false
            this.getData()
          }, 2000)
        } else {
          // 全部检测
          this.progressLoading = true
          this.checkBtnLoading = false
          setTimeout(() => {
            this.updateProgress()
          }, 500)
        }
      } else {
        this.progressLoading = false
        this.checkBtnLoading = false
      }
    },
    async updateProgress(isNotMsg) {
      let res = await eventListMatchProgress({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        let { progress } = res.data
        if ((progress && progress < 100) || progress === 0) {
          this.progress = progress
          this.progressLoading = true
          this.checkBtnLoading = false
          this.updateTimer = setTimeout(() => {
            this.updateProgress()
          }, 2000)
        } else {
          this.checkBtnLoading = false
          this.progressLoading = false
          this.progress = 0
          if (!isNotMsg) {
            this.$message.success('一键检测已完成')
          }
          if (this.updateTimer) {
            clearTimeout(this.updateTimer)
          }

          if (!isNotMsg) {
            setTimeout(() => {
              this.getData()
            }, 1000)
          }
        }
      }
    },
    resetForm(ref) {
      this.$refs[ref].resetFields()
      this.formInline = {
        keyword: '',
        name: '',
        mobile: '',
        email: '',
        role: [],
        status: '',
        disclosure_time: [],
        company: ''
      }
    },
    uploadReport(row) {
      this.dataId = row.id
      this.uploadDialogVisible = true
    },
    async reportPreview(row) {
      let funcName = null
      if (this.pageType && this.pageType == 'manage') {
        // 原始
        funcName = eventListReport
      } else {
        // 加密
        funcName = eventListMaskedReport
      }
      let res = await funcName({ data_id: row.id })
      this.judgeUpload(res)
    },
    judgeUpload(res, fileName) {
      var reader = new FileReader()
      reader.readAsText(res)
      const _this = this
      reader.addEventListener('loadend', function (e) {
        if (e.target.result.indexOf('"code":400') != -1) {
          let result = JSON.parse(e.target.result)
          _this.$message({
            message: result.message,
            type: 'error'
          })
        } else {
          const href = window.URL.createObjectURL(
            new Blob([res], {
              type: 'application/pdf'
            })
          )
          window.open(href)
        }
      })
    },

    getDetail(row) {
      this.currentRow = JSON.parse(JSON.stringify(row))
      this.detailDialogFormVisible = true
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableComponent tableComponentFilter'
      } else {
        bol = 'tableComponent'
      }
      return bol
    },
    highCheck(data) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline)
      this.hightFilterIsShow()
      this.updateList({ page: 1, per_page: 10, ...this.formInline })
    },
    download(URL) {
      window.location.href = this.showSrcIp + URL
      this.$message.success('下载成功')
    },
    edit(row) {
      this.currentRow = JSON.parse(JSON.stringify(row))
      this.formData.category = this.currentRow.category || ''
      this.addDialogFormVisible = true
    },
    async submit() {
      let funcName = editEventCategory
      let params = JSON.parse(JSON.stringify(this.formData))
      let res = await funcName({
        category: params.category,
        event_id: this.currentRow.id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.addDialogFormVisible = false
        this.formData = {
          category: ''
        }
        this.getData()
      }
    },
    updateList(data) {
      let searchParam = JSON.parse(JSON.stringify(this.searchParams))
      this.searchParams = Object.assign(searchParam, data)
      this.getData()
    },
    async getData(data) {
      this.$emit('updatePageLoading', true)
      let funcName = this.pageType && this.pageType == 'manage' ? eventListManage : eventList
      let res = await funcName({
        ...this.searchParams,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.$emit('updatePageLoading', false)
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
      } else {
        this.$message.error(res.message)
      }
      this.$emit('updatePageLoading', false)
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.tableList {
  height: 100%;
}
/deep/.el-image {
  position: relative;
  overflow: visible;
  display: block;
}
.imgCardContainer {
  position: relative;
}
.imgKongBtn {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto 0;
  left: calc(100% + 10px);
  // z-index: 9999;
}
.imgCardBox {
  height: 30px;
  // width: 100%;
  // height: 100%;
  /deep/.image-slot {
    height: 100%;
    width: 100%;
    color: #606266;
    background: #e9ebef;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    text-align: center;
    svg {
      display: inline-block;
      font-size: 25px;
    }

    p {
      width: 100%;
      line-height: 25px;
      color: #d1d5dd;
      span {
        margin-left: 4px;
        color: #2677ff;
      }
      .el-button {
        padding: 0;
      }
    }
  }
}
.upload-dialog {
  /deep/.el-dialog__body {
    padding: 20px !important;
    max-height: 213px !important;
    min-height: 213px !important;
    .el-upload__tip {
      margin-top: 10px;
    }
  }
}
.edit-dialog {
  /deep/.el-dialog__body {
    padding: 0;
    max-height: 213px !important;
    min-height: 213px !important;
  }
}
.elDialogAdd {
  /deep/.el-dialog__body {
    padding: 0;
  }
  .top {
    box-sizing: border-box;
    display: flex;
    height: 157px;
    padding: 20px 24px;
    .left {
      width: 110px;
      margin-right: 16px;
    }
    .right {
      width: 0;
      flex: 1;
      display: flex;
      justify-content: center;
      flex-direction: column;
      .title {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
      }
      .levelDiv {
        box-sizing: border-box;
        width: 86px;
        padding: 4px 12px;
        border-radius: 14px;
      }
      .level {
        display: inline-block;
        margin-right: 4px;
        span {
          display: inline-block;
          width: 6px;
          height: 10px;
          background: #eaceba;
          transform: skewX(-25deg);
          margin-right: 1px;
        }
      }
    }
  }
  .middleLine {
    margin: 0 20px;
    height: 1px;
    width: calc(100% - 40px);
    background: #e3e5ea;
  }
  .bottom {
    padding: 20px 20px 24px;
    .title {
      margin-bottom: 12px;
    }

    .base {
      margin-bottom: 12px;
      .item {
        float: left;
        width: 50%;
        height: 20px;
        // line-height: 50px;
        margin-bottom: 12px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &.one {
          width: 100%;
          height: auto;
        }
        &.two {
          width: 50%;
          height: 20px;
        }
        &.three {
          width: 33%;
          height: auto;
        }
        .label {
          color: rgba(98, 102, 108, 0.6);
        }
        .value {
          color: #37393c;
        }
        .content {
          border-radius: 4px;
          box-sizing: border-box;
          // height: 96px;
          padding: 16px;
          max-height: 200px;
          overflow: auto;
          background-color: #f5f8fc;
          div:first-child {
            margin-bottom: 12px;
          }
        }
      }
    }
    .solution {
      margin-top: -10px;
      .title {
        color: rgba(98, 102, 108, 0.6);
      }
      .content {
        border-radius: 4px;
        box-sizing: border-box;
        max-height: 196px;
        padding: 16px;
        background-color: #f5f8fc;
        overflow: auto;
        div:first-child {
          margin-bottom: 12px;
        }
      }
    }
    .effect {
      margin-top: 24px;
      .content {
        border-radius: 4px;
        box-sizing: border-box;
        padding: 16px;
        background-color: #f5f8fc;
        div:first-child {
          margin-bottom: 12px;
        }
      }
    }
    .ipTable {
      margin-top: 24px;
      .content {
        height: 570px;
      }
    }
  }
}
.clearfix::after {
  content: '';
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}

.screenshot {
  width: 200px;
  height: 200px;
  position: relative;
  /deep/.el-image {
    width: 100%;
    height: 100%;
  }
}
.download {
  span {
    margin-left: 20px;
    &:hover {
      cursor: pointer;
      color: #2677ff;
    }
  }
}
/deep/.edit-dialog {
  .dialog-body {
    padding-top: 38px;
  }
}
.tableComponentFilter {
  height: calc(100% - 62px) !important;
}
/deep/.el-tooltip__popper {
  line-height: 30px;
}
</style>
