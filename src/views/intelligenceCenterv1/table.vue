<template>
  <div class="tableContainer">
    <div class="tableWrap">
      <el-table
        :data="tableData"
        v-loading="loading"
        row-key="id"
        :header-cell-style="{background: '#F2F3F5',color: '#62666C'}"
        @selection-change="handleSelectionChange"

        ref="eltable"
        height="100%"
        style="width: 100%">
        <template slot="empty">
          <div class="emptyClass">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-kong"></use>
            </svg>
            暂无数据
          </div>
        </template>
        <el-table-column
          v-if="isSelectData"
          type="selection"
          align="center"
          :reserve-selection="true"
          :selectable="handleSelectable"
          width="55">
        </el-table-column>
        <el-table-column
          v-for="item in tableHeader"
          :key="item.id"
          :prop="item.name"
          align="left"
          :min-width="item.minWidth"
          :fixed="item.fixed"
          show-overflow-tooltip
          >
            <span slot="header" >
              {{ item.label }}
              <slot :item="item" name="tip"></slot>
            </span>
            <template slot-scope="{ row }">
              <slot name="judge" :row="row" :itemName="item.name" ></slot>
            </template>
          </el-table-column>
          <slot name="other"></slot>
      </el-table>
      <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
    </div>
    <el-pagination
      v-if="pageType != 'brief'"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 30, 50, 100]"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total">
    </el-pagination>
  </div>
</template>

<script>
import tableTooltip from "../../components/tableTooltip/tableTooltip.vue";

export default {
  props: {
    // actTabVal: {
    //   type: [Number,String],
    //   default:1
    // },
    pageType: { // 是否为管理页面
      type: String,
      default:''
    },
    tableHeader: {
      type: Array,
      default: () => []
    },
    tableData: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    },
    isSelectData: {
      type: Boolean,
      default: false
    },
    checkedAll: {
      type: Boolean,
      default: false
    },
  },
  components: {
    tableTooltip
  },
  data () {
    return {
      loading: false,
      // tableData: [],
      currentPage: 1,
      pageSize: 10,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null, // 行数据
      },
    }
  },
  methods: {
    handleSelectable(row, index) {
      return !this.checkedAll;
    },
    handleSelectionChange(val) {
      this.$emit('selectChange',val)
    },
     // 鼠标移入cell
     showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell;
      this.tableCellMouse.row = row;
      this.tableCellMouse.hidden = false;
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true;
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.$emit('updateList', { per_page: val })
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.$emit('updateList', { page: val })
    },
  }
}
</script>

<style lang="less" scoped>
.tableContainer{
  // height: calc(100% - 64px);
  height: 100%;
  .tableWrap{
    height: calc(100% - 92px);
  }
}
.emptyClass {
  height: 100%;
  text-align: center;
  vertical-align: middle;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #D1D5DD;
    span {
      margin-left: 4px;
      color: #2677FF;
      cursor: pointer;
    }
  }
}
</style>
