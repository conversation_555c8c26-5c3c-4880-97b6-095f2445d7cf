<template>
  <el-dialog
    class="elDialogAdd upload-dialog"
    :close-on-click-modal="false"
    :visible="addDialogFormVisible"
    width="400px"
    @close="close()"
  >
    <template slot="title">
      上传报告
    </template>
    <div class="dialog-body">
      <el-upload
        class="upload-demo"
        ref="upload"
        drag
        :action="action"
        :headers="uploadHeaders"
        accept=".pdf"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        :on-remove="uploadRemove"
        :on-error="uploadError"
        :on-change="onChange"
        :limit="uploadMaxCount"
        :on-exceed="handleExceed"
        :file-list="fileList"
        :auto-upload="false"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          支持上传pdf文件，且大小不超过45M
        </div>
      </el-upload>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button
        class="highBtnRe"
        id="number_upload_cancel"
        @click="$emit('close')"
        >关闭</el-button
      >
      <el-button
        class="highBtn"
        :loading="btnLoading"
        @click="uploadSave"
        id="number_upload_sure"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    action:{
      type: String,
      default: ""
    },
    addDialogFormVisible:{
      type: Boolean,
      default: false
    }
  },
  data(){
    return {
      file: null,
      btnLoading: false,
      uploadMaxCount: 1,
      fileList: [],
      uploadPath: '',
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
    }
  },
  methods:{
    close(){
      this.fileList = []
      this.$emit('close')
    },
    uploadSave(){
      if(!this.file){
        this.$message.error('请上传文件')
        return
      }
      this.btnLoading = true
      this.$refs.upload.submit()
    },
    beforeUpload(file){
      let isLt1M = ''
        isLt1M = file.size / 1024 / 1024 < 45;
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过45MB!`);
      }
      return isLt1M;
    },
    onChange(file){
      this.file = file
    },
    uploadSuccess(response, file, fileList){
      if (file.response && file.response.data && file.response.data.url) {
        this.uploadPath = file.response.data.url
      }
      if (file.response.code == 0){
        this.$message.success('上传成功！')
        this.$emit('close')
      } else {
        this.$message.error(file.response.message)
      }
      this.btnLoading = false
    },
    uploadRemove (file, fileList) {
      let res = fileList.map(item => {return item.response.data})
      if (res.length == 0) {
        this.file = null
        this.uploadPath = ''
      }
    },
    uploadError(err, file, fileList){
      this.btnLoading = false
      let myError = err.toString();//转字符串
      myError = myError.replace("Error: ","") // 去掉前面的" Error: "
      myError = JSON.parse(myError);//转对象
      if (myError.status == 401) {
        this.$router.replace('/login')
        sessionStorage.clear()
        localStorage.clear()
      } else {
        this.$message.error(myError.message)
      }
    },
    handleExceed(){
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`);
    },
  }
};
</script>
<style lang="less" scoped>
.upload-dialog{
  /deep/.el-dialog__body{
    padding: 28px 28px 0 !important;
    min-height: 0 !important;
  }
}
</style>
