<template>
  <div class="container" v-loading="loading">
    <div class="headerTitle">关联情报</div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="热点漏洞" name="hot"> </el-tab-pane>
        <el-tab-pane label="风险情报" name="risk"> </el-tab-pane>
        <el-tab-pane label="专项情报" name="special"> </el-tab-pane>
        <el-tab-pane label="数据情报" name="dataLeak"> </el-tab-pane>
      </el-tabs>

      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
            id="user_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            <el-tooltip slot="prepend" class="item" effect="dark" placement="top" :open-delay="500">
              <span slot="content">
                <span>{{ riskSearchMap[activeName] }}</span>
              </span>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-input>
          <span
            @click="highCheckClick"
            id="account_filter"
            style="width: 80px; display: inline-block"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="poc_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            :loading="exportLoadingBtn"
            @click="exportList"
            id="cloud_export"
            >导出</el-button
          >
        </div>
      </div>
      <!-- 高级筛选 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="tableHeaderIsShow"
        :highlist="highlist"
        :total="total"
        @highcheck="highCheck"
      >
      </hightFilter>
      <div :class="hightFilterIsShow()" v-loading="tableLoading" ref="tableWrap">
        <el-table
          border
          :data="tableData"
          @selection-change="handleSelectionChange"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltable"
          height="100%"
        >
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="checkedAll"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p style="font-size: 18px"
                >暂无数据，
                <span style="color: #2677ff; cursor: pointer" @click="jumpToIntelligence()"
                  >去关联</span
                >
              </p>
            </div>
          </template>
          <el-table-column
            v-for="(item, index) in tableHeaderIsShow"
            show-overflow-tooltip
            :key="index"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="{ row }">
              <span v-if="item.name == 'asset_status'">
                {{ row[item.name] == '1' ? '在线' : '离线' }}
              </span>
              <span v-else-if="item.name == 'asset_url'">
                <a
                  v-if="row[item.name] && String(row[item.name]).includes('http')"
                  style="color: #409eff; cursor: pointer"
                  :href="row[item.name]"
                  target="_blank"
                  >{{ row[item.name] }}</a
                >
                <span v-else>{{ row[item.name] || '-' }}</span>
              </span>
              <span
                v-else-if="item.name == 'risk_name' && activeName == 'hot'"
                style="color: #2677ff; cursor: pointer"
                @click="openDetail(row)"
              >
                {{ row[item.name] || '-' }}
              </span>
              <span
                v-else-if="item.name == 'special_project_name' && activeName == 'special'"
                style="color: #2677ff; cursor: pointer"
                @click="getSpecialDetail(row)"
              >
                {{ row[item.name] || '-' }}
              </span>
              <span
                v-else-if="item.name == 'special_project_name' && activeName == 'dataLeak'"
                style="color: #2677ff; cursor: pointer"
                @click="getDataLeakDetail(row)"
              >
                {{ row[item.name] || '-' }}
              </span>
              <span v-else>{{ row[item.name] || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>

      <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
        <div class="demo-drawer__content">
          <el-form :model="formInline" ref="drawerForm" label-width="84px">
            <el-form-item
              v-for="item in tableHeaderIsShow"
              :key="item.id"
              :label="item.label"
              :prop="item.name"
            >
              <el-select
                v-if="item.type == 'select' && item.multiple"
                clearable
                filterable
                :multiple="item.multiple"
                v-model="formInline[item.name]"
                @change="
                  selectChange($event, item.name, selectData[item.name] || [], false, item.multiple)
                "
              >
                <el-option
                  v-for="item in selectData[item.name]"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
              <el-select
                v-else-if="item.type == 'select' && !item.multiple"
                clearable
                filterable
                :multiple="item.multiple"
                v-model="formInline[item.name]"
                @change="
                  selectChange($event, item.name, selectData[item.name] || [], true, item.multiple)
                "
              >
                <el-option
                  v-for="item in selectData[item.name]"
                  :key="item.name"
                  :label="item.name"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-date-picker
                v-else-if="item.type == 'date'"
                v-model="formInline[item.name]"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
              <el-input v-else v-model="formInline[item.name]" placeholder="请输入"></el-input>
            </el-form-item>
          </el-form>
          <div class="demo-drawer__footer">
            <el-button
              class="highBtnRe"
              @click="resetForm('drawerForm')"
              id="cloud_info_filter_repossess"
              >重置</el-button
            >
            <el-button class="highBtn" @click="checkFuncList" id="cloud_info_filter_select"
              >筛选</el-button
            >
          </div>
        </div>
      </el-drawer>
    </div>
    <hotDetail ref="hotDetail" :currentCompany="currentCompany"> </hotDetail>
    <el-dialog
      class="elDialogAdd"
      @close="detailDialogFormVisible = false"
      :close-on-click-modal="false"
      :visible.sync="detailDialogFormVisible"
      width="880px"
    >
      <template slot="title"> 详情 </template>
      <div class="dialog-body" style="max-height: 813px">
        <div class="top High">
          <div class="left">
            <img :src="require(`../../assets/images/riskHigh.png`)" alt="" />
          </div>
          <div class="right">
            <div class="title">{{ currentRow.special_project_name }}</div>
            <div class="title">{{ detailInfo.event_name }}</div>
          </div>
        </div>
        <div class="middleLine"></div>
        <div class="bottom" v-if="activeName == 'special'">
          <div class="base clearfix">
            <div class="title">基础信息</div>
            <div class="item">
              <span class="label">专项名称：</span>
              <span class="value">{{ detailInfo.name || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">漏洞类型：</span>
              <span class="value">{{ detailInfo.category || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">共涉及企业数量：</span>
              <span class="value">{{ detailInfo.company_num || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">互联网影响资产：</span>
              <span class="value">{{ detailInfo.asset_num || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">披露时间：</span>
              <span class="value">{{ detailInfo.creation_time || '-' }}</span>
            </div>
          </div>
        </div>
        <div class="bottom" v-else>
          <div class="base clearfix">
            <div class="title">基础信息</div>
            <div class="item">
              <span class="label">泄漏服务/组件：</span>
              <span class="value">{{ detailInfo.leak_service_component || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">泄漏原因：</span>
              <span class="value">{{ detailInfo.leak_reason || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">泄漏内容：</span>
              <span class="value">{{ detailInfo.data_content || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">数据量：</span>
              <span class="value">{{ detailInfo.data_volume || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">披露时间：</span>
              <span class="value">{{ detailInfo.disclosure_time || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">IP关联地理位置：</span>
              <span class="value">{{ detailInfo.ip_related_location || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">数据所属实体：</span>
              <span class="value">{{ detailInfo.data_entity || '-' }}</span>
            </div>
            <div class="item">
              <span class="label">数据所属实体地点：</span>
              <span class="value">{{ detailInfo.data_entity_location || '-' }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState, mapMutations, mapGetters } from 'vuex'
// import hightFilter from "../../components/assets/hightFilter.vue";
import hightFilter from '../../components/assets/highTab.vue'
import tableList from '../home_set/indexTable.vue'
import hotDetail from '@/views/intelligenceCenterv1/hotDetail.vue'
import {
  intellDataDetail,
  intellRelatedList,
  intellRelatedListExport,
  intellRelatedListCondition,
  eventList
} from '@/api/apiConfig/api.js'

export default {
  components: { tableList, hightFilter, hotDetail },
  data() {
    return {
      user: {
        role: ''
      },
      detailDialogFormVisible: false,
      detailInfo: {},
      currentRow: {},
      highlist: null,
      exportLoadingBtn: false,
      tableLoading: false,
      riskTypeMap: {
        hot: 1,
        risk: 2,
        special: 3,
        dataLeak: 4
      },
      riskSearchMap: {
        hot: '支持检索字段：IP、热点漏洞名称',
        risk: '支持检索字段：IP、完整域名、完整风险类型',
        special: '支持检索字段：IP、专项情报、情报类型',
        dataLeak: '支持检索字段：IP、专项名称'
      },
      exportLoading: false,
      selectData: {},
      statusArrMap: {
        1: '在线',
        2: '离线'
      },
      tableHeader: [
        {
          label: 'IP',
          name: 'asset_ip',
          minWidth: 110,
          type: 'input',
          fixed: 'left'
        },
        {
          label: '端口',
          name: 'asset_port',
          type: 'select',
          multiple: true,
          minWidth: 110
        },
        {
          label: 'URL',
          name: 'asset_url',
          type: 'select',
          multiple: true,
          minWidth: 110,
          path: ['hot', 'special']
        },
        {
          label: '域名',
          name: 'asset_domain',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['risk']
        },
        {
          label: '风险URL',
          name: 'asset_url',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['risk']
        },
        {
          label: '状态',
          name: 'asset_status',
          type: 'select',
          minWidth: 110,
          path: ['hot', 'risk', 'special']
        },
        {
          label: '专项情报',
          name: 'special_project_name',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['special']
        },
        {
          label: '热点漏洞',
          name: 'risk_name',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['hot']
        },
        {
          label: '情报类型',
          name: 'intelligence_type',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['special']
        },
        // {
        //   label: "关联设备",
        //   name: "associated_device",
        //   multiple: true,
        //   type: "select",
        //   minWidth: 110,
        //   path: ["special"]
        // },
        {
          label: '标题',
          name: 'asset_title',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['hot', 'special']
        },
        {
          label: '组件',
          name: 'service_component',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['hot', 'special', 'dataLeak']
        },
        {
          label: '风险类型',
          name: 'intelligence_type',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['risk']
        },
        {
          label: '标签',
          name: 'intelligence_tags',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['risk']
        },
        {
          label: '国家',
          name: 'intelligence_country',
          multiple: true,
          minWidth: 110,
          type: 'select',
          path: ['risk']
        },
        {
          label: '企业名称',
          name: 'enterprise_name',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['dataLeak']
        },
        {
          label: '专项名称',
          name: 'special_project_name',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['dataLeak']
        },
        {
          label: '事件名称',
          name: 'risk_name',
          multiple: true,
          type: 'select',
          minWidth: 110,
          path: ['dataLeak']
        },
        {
          label: '发现时间',
          name: 'found_time',
          minWidth: 110,
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'update_time',
          minWidth: 110,
          type: 'date'
        }
      ],

      selectArr: {},
      highCheckdialog: false,
      filterCondition: {}, //高级设置状态值
      hightFilterShow: 3,
      formInline: {
        keyword: ''
      },
      checkedAll: false,
      loading: false,
      checkedArr: [],
      activeName: 'hot',
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100]
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany']),
    risk_type() {
      return this.riskTypeMap[this.activeName]
    },
    tableHeaderIsShow(path) {
      // 'hot'  'risk'  'special'  'dataLeak'
      let arr = []
      arr = this.tableHeader.filter((item) => {
        return !item.path || item.path.indexOf(this.activeName) != -1
      })
      if (arr && arr.length > 0) {
        arr[0].fixed = 'left'
      }
      return arr
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.user.role == 2) {
        this.getData()
        // this.getCondition();
      }
    }
  },
  mounted() {
    if (this.$route.query.actTabVal) {
      let { actTabVal, keyword } = this.$route.query
      this.activeName = actTabVal
      this.formInline.keyword = keyword
    }
    if (sessionStorage.getItem('userMessage')) {
      let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (userInfo) {
        this.user = userInfo.user
      }
    }
    if (this.user.role == 2 && !this.currentCompany) return
    this.getData()
    // this.getCondition();
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    openDetail(row) {
      this.$refs.hotDetail.getIpDetail(row.intelligence_id)
    },
    getDataLeakDetail(row) {
      this.currentRow = row
      this.detailDialogFormVisible = true
      intellDataDetail({
        id: row.intelligence_id,
        operate_company_id: this.currentCompany
      }).then((res) => {
        this.detailInfo = res.data || {}
      })
    },
    getSpecialDetail(row) {
      this.currentRow = row
      this.detailDialogFormVisible = true
      eventList({
        name: row.special_project_name,
        page: 1,
        per_page: 10,
        operate_company_id: this.currentCompany
      }).then((res) => {
        this.detailInfo = res.data.items[0] || {}
      })
    },
    getCondition() {
      if (Object.keys(this.selectData).length !== 0) return
      intellRelatedListCondition({
        operate_company_id: this.currentCompany,
        risk_type: this.risk_type
      }).then((res) => {
        this.selectData = res.data || {}
        this.selectData.asset_status = [
          { name: '在线', value: 1 },
          { name: '离线', value: 0 }
        ]
      })
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    async exportList() {
      let obj = {}
      if (this.checkedAll) {
        obj = {
          id: [],
          operate_company_id: this.currentCompany,
          risk_type: this.risk_type,
          ...this.formInline
        }
      } else {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
        obj = {
          id: this.checkedArr.map((item) => item.id),
          operate_company_id: this.currentCompany,
          risk_type: this.risk_type,
          ...this.formInline
        }
      }
      try {
        this.exportLoadingBtn = true
        let res = await intellRelatedListExport(obj)
        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.url)
          this.$message.success('导出成功')
          this.$refs.eltable.clearSelection()
          this.exportLoadingBtn = false
          this.checkedAll = false
        }
      } catch (error) {
        this.exportLoadingBtn = false
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    jumpToIntelligence() {
      this.changeMenuId('10-3')
      sessionStorage.setItem('menuId', '10-3')
      this.$router.push({ path: '/intelligenceCenterv1', query: { actTabVal: this.activeName } })
    },
    getData() {
      this.tableLoading = true
      intellRelatedList({
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline,
        risk_type: this.risk_type,
        operate_company_id: this.currentCompany,
        sort: ['update_time desc']
      })
        .then((res) => {
          this.tableLoading = false
          this.tableData = res.data.items || []
          this.total = res.data.total || 0
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    handleClick() {
      this.tableLoading = true
      this.formInline = {
        keyword: ''
      }
      this.selectData = {}
      this.tableData = []
      this.highlist = null
      this.$nextTick(() => {
        this.$refs.eltable && this.$refs.eltable.clearSelection()
      })
      this.currentPage = 1
      this.checkedAll = false
      this.$router.replace({ query: { actTabVal: this.activeName } })
      // this.getData()
    },
    highCheckClick() {
      this.getCondition()
      this.highCheckdialog = true
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    highCheck(data) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    resetForm(formName) {
      this.formInline = {
        keyword: ''
      }
      this.$refs[formName].resetFields()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;

  /deep/.home_header {
    position: relative;
    height: 100%;

    .el-tabs__nav {
      padding-left: 20px;
    }

    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      /* width: 60px; */
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }

    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }

    .el-tabs__header {
      margin: 0;
    }

    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }

    .el-tabs__item {
      /* width: 60px; */
      height: 44px;
      text-align: center;
      line-height: 44px;
      /* padding: 0; */
      padding-left: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }

    .downloadClass {
      position: relative;
      height: 40px;
      line-height: 40px;
      margin: 16px 20px 0 20px;
      background: #f0f3f8;
      color: #62666c;
      border-radius: 4px;
      // border: 1px solid rgba(38, 119, 255, 0.44);
      cursor: pointer;

      i {
        font-size: 14px;
        color: #2677ff;
        margin: 0 8px 0 16px;
      }

      .el-icon-close {
        position: absolute;
        right: 10px;
        top: 12px;
        font-size: 16px;
        color: #9fa6af;

        &:hover,
        &:focus {
          background-color: transparent;
          color: rgba(159, 166, 175, 1) !important;
        }
      }

      span {
        color: #2677ff;
      }
    }

    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;

      & > div {
        display: flex;
        align-items: center;

        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .el-input {
          width: 240px;
        }

        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }

    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }

    .tableWrap {
      height: calc(100% - 169px);
      padding: 0px 20px;

      .icon {
        font-size: 120px;
      }
    }

    .el-table {
      border: 0;
    }
  }
}
.elDialogAdd {
  /deep/.el-dialog__body {
    padding: 0;
    // max-height: 613px;
  }

  /deep/.dialog-item {
    margin-top: 13px;
    margin-left: 28px;
  }

  .top {
    box-sizing: border-box;
    display: flex;
    height: 157px;
    padding: 20px 24px;

    .left {
      width: 110px;
      margin-right: 16px;
    }

    .right {
      width: 0;
      flex: 1;
      display: flex;
      justify-content: center;
      flex-direction: column;

      .title {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
      }

      .levelDiv {
        box-sizing: border-box;
        width: 86px;
        padding: 4px 12px;
        border-radius: 14px;
      }

      .level {
        display: inline-block;
        margin-right: 4px;

        span {
          display: inline-block;
          width: 6px;
          height: 10px;
          background: #eaceba;
          transform: skewX(-25deg);
          margin-right: 1px;
        }
      }
    }

    &.High {
      background: linear-gradient(180deg, #fff3f3 0%, #ffffff 100%), #ffffff;

      .levelDiv {
        border: 1px solid #ff4646;
        color: #ff4646;
        background: rgba(255, 70, 70, 0.12);

        .level {
          span:nth-child(-n + 3) {
            background: #ff4646;
          }
        }
      }
    }
  }

  .middleLine {
    margin: 0 20px;
    height: 1px;
    width: calc(100% - 40px);
    background: #e3e5ea;
  }

  .bottom {
    padding: 20px 20px 24px;

    .title {
      margin-bottom: 12px;

      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 4px;
        margin-right: 4px;
        transform: rotate(135deg);
        background-color: #2677ff;
        vertical-align: middle;
      }
    }

    .base {
      margin-bottom: 12px;

      .item {
        width: 50%;
        float: left;
        margin-bottom: 12px;

        &.one {
          width: 100%;
        }

        .label {
          color: rgba(98, 102, 108, 0.6);
        }

        .value {
          color: #37393c;
        }
      }
    }

    .solution {
      .content {
        border-radius: 4px;
        box-sizing: border-box;
        height: 96px;
        padding: 16px;
        background-color: #f5f8fc;

        div:first-child {
          margin-bottom: 12px;
        }
      }
    }

    .effect {
      margin-top: 24px;

      .content {
        border-radius: 4px;
        box-sizing: border-box;
        padding: 16px;
        background-color: #f5f8fc;

        div:first-child {
          margin-bottom: 12px;
        }
      }
    }

    .ipTable {
      margin-top: 24px;

      .content {
        height: 570px;
      }
    }
  }
}

.clearfix::after {
  content: '';
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}
</style>
