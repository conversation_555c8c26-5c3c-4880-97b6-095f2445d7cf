<template>
  <div class="container">
    <div class="headerTitle">
      <span>黑IP封禁管理</span>
      <span @click="collapsClick" class="collapsBox"
        >{{ collapseFlag ? '收起流程视图说明' : '展开流程视图说明'
        }}<i :class="collapseFlag ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i
      ></span>
    </div>
    <div v-if="collapseFlag" class="picShow">
      <img src="../../assets/images/banneriptext.png" alt="" />
    </div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="黑IP列表" name="first"> </el-tab-pane>
        <el-tab-pane label="任务记录" name="second"> </el-tab-pane>
      </el-tabs>
      <div class="tab_content">
        <ipList v-if="activeName == 'first'" />
        <taskLog v-if="activeName == 'second'" />
      </div>
    </div>
  </div>
</template>

<script>
import taskLog from './taskLogList.vue'
import ipList from './ipBanningList.vue' // 列表

export default {
  components: { ipList, taskLog },

  data() {
    return {
      collapseFlag: true,
      activeName: 'first'
    }
  },
  async mounted() {
    if (this.$route.query && this.$route.query.flag == 1) {
      // 报告生成后提示
      this.activeName = 'first'
    }
  },
  beforeDestroy() {
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    collapsClick() {
      this.collapseFlag = !this.collapseFlag
    },
    handleClick() {
      sessionStorage.setItem('activeTabName', this.activeName)
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .headerTitle {
    position: relative;
    .collapsBox {
      position: absolute;
      right: 0;
      cursor: pointer;
      font-size: 14px;
      font-weight: normal;
      color: rgba(38, 119, 255, 1);
    }
  }
  .picShow {
    width: 98%;
    // height: 268px;
    opacity: 1;
    text-align: center;
    background: url(../../assets/images/banneripbg.png) no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
    margin-bottom: 12px;
    padding-bottom: 10px;
    overflow: hidden;
    img {
      width: 75%;
      // min-width: 900px;
      // height: 247px;
      margin-top: 5px;
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      padding: 0 16px;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      // padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .tab_content {
      height: calc(100% - 44px);
    }
    .tab_content_tip {
      height: calc(100% - 101px);
    }
    .downloadClass {
      // width: 100%;
      height: 40px;
      line-height: 40px;
      background: rgba(38, 119, 255, 0.18);
      border-radius: 2px;
      border: 1px solid rgba(38, 119, 255, 0.44);
      cursor: pointer;
      i {
        font-size: 14px;
        color: #2677ff;
        margin: 0 8px 0 16px;
      }
      span {
        color: #2677ff;
      }
    }
  }
}
</style>
