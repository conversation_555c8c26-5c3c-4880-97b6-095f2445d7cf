<template>
  <div class="container">
    <div class="home_header">
      <div class="filterTab">
        <div>
          <span v-if="taskInfoId" class="goback" @click="$emit('backList')"
            ><i class="el-icon-arrow-left"></i>返回上一层</span
          >
          <el-input
            v-model="formInline.keyword"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入IP地址进行搜索"
            id="scan_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckIsShow" id="scan_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div v-if="!taskInfoId">
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="scan_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="removeOne('more')"
            id="scan_more_del"
            :disabled="!userIsOpen"
            >删除</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            @click="addTaskDialog"
            id="scan_add"
            :disabled="!userIsOpen"
            >下发任务</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="task"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()" v-loading="loading" style="padding: 0px 20px">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :show-overflow-tooltip="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="(item, itemIndex) in tableHeader"
            :key="itemIndex"
            align="left"
            :prop="item.name"
            :label="item.label"
            :fixed="item.fixed"
            :min-width="item.minWidth"
          >
            <template slot-scope="scope">
              <div class="detail" v-if="item.name == 'port'">
                <span>{{ getTableItem(scope.row[item.name]) }}</span>
              </div>
              <div class="detail" v-else-if="item.name == 'protocol'">
                <span>{{ getTableItem(scope.row[item.name]) }}</span>
              </div>
              <div v-else-if="item.name == 'category'" class="ruleItemBox">
                <span
                  class="ruleItem"
                  v-for="ch in get_category(scope.row[item.name], 'filter')"
                  :key="ch"
                  >{{ ch }}</span
                >
                <el-popover
                  placement="top"
                  width="315"
                  style="padding-right: 0px !important; padding-left: 0px !important"
                  popper-class="rulePopover"
                  trigger="click"
                >
                  <div class="myruleItemBox">
                    <span
                      class="myruleItem"
                      v-for="(v, i) in get_category(scope.row[item.name])"
                      :key="i"
                      >{{ v }}</span
                    >
                  </div>
                  <div
                    slot="reference"
                    v-if="get_category(scope.row[item.name]).length > 2"
                    class="ruleItemNum"
                    >共{{ get_category(scope.row[item.name]).length }}条</div
                  >
                </el-popover>
              </div>
              <span v-else-if="item.name == 'duration_time'">{{
                assetsDeal(scope.row['duration_time'], scope.row['forbid_level'])
              }}</span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="!taskInfoId" fixed="right" label="操作" align="left" width="180">
            <template slot-scope="scope">
              <span>
                <el-button
                  type="text"
                  size="small"
                  @click="removeOne('one', scope.row.id)"
                  id="scan_del"
                  >删除</el-button
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        v-if="currentPageIsShow"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      class="elDialogAdd"
      width="600px"
    >
      <template slot="title"> 下发任务 </template>
      <div class="dialog-body">
        <el-form
          :model="ruleFormAdd"
          ref="ruleFormAdd"
          style="padding: 0 !important"
          :rules="ruleFormRules"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="任务名称" prop="name">
            <el-input v-model="ruleFormAdd.name" placeholder="请输入任务名称"></el-input>
          </el-form-item>
          <el-form-item label="添加方式" prop="banning_ways">
            <el-select
              v-model="ruleFormAdd.banning_ways"
              @change="banningWayChange"
              placeholder="请选择"
            >
              <el-option
                v-for="item in upWays"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="ips" style="position: relative">
            <el-input
              type="textarea"
              :rows="10"
              v-if="ruleFormAdd.banning_ways == 0"
              v-model="ruleFormAdd.ips"
              class="placeholderIdBox"
            ></el-input>
            <div class="placeholderId" v-if="ruleFormAdd.banning_ways == 0 && !ruleFormAdd.ips">
              <div></div>
              <div>支持下发IPV4及IPV6</div>
              <div>IP段支持格式如下</div>
              <div>**********</div>
              <div>**********-100</div>
              <div>**********/24</div>
              <div></div>
              <!-- <div>多个连续网段支持格式：</div>
              <div>192.168.1-10.*（代表***********-************共10个网段）</div> -->
              <div>最多输入1000个ip，分号或换行分隔</div>
            </div>
            <p
              v-if="ruleFormAdd.banning_ways == 1"
              class="downloadClass"
              @click="downloadAssetsExcel"
            >
              <i class="el-icon-warning"></i>请点击下载
              <span>黑IP模板</span>
            </p>
            <el-upload
              v-if="ruleFormAdd.banning_ways == 1"
              class="upload-demo"
              drag
              :action="uploadSrcIp + '/black_ips/upload'"
              :headers="uploadHeaders"
              accept=".xlsx"
              :before-upload="beforeIpUpload"
              :on-success="uploadSuccess"
              :on-remove="uploadRemove"
              :on-error="uploadError"
              :limit="uploadMaxCount"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">支持上传xlsx文件，且大小不超过20M</div>
            </el-upload>
          </el-form-item>
          <el-form-item label="封禁级别" prop="forbid_level">
            <el-select v-model="ruleFormAdd.forbid_level" placeholder="请选择">
              <el-option
                v-for="item in levelArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisible = false" id="report_add_cancel"
          >取消</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="insertSave" id="report_add_sure"
          >确认</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="IP地址：" prop="ip">
            <el-input v-model="formInline.ip" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="端口：" prop="port">
            <el-select
              filterable
              v-model="formInline.port"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'port', condition.port, false, true)"
            >
              <el-option
                v-for="item in condition.port"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="协议：" prop="protocol">
            <el-select
              filterable
              v-model="formInline.protocol"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'protocol', condition.protocol, false, true)"
            >
              <el-option
                v-for="item in condition.protocol"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类别：" prop="category">
            <el-select
              filterable
              v-model="formInline.category"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'category', condition.category, false, true)"
            >
              <el-option
                v-for="item in condition.category"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="地理位置：" prop="country_name">
            <el-select
              filterable
              v-model="formInline.country_name"
              multiple
              collapse-tags
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'country_name', condition.country_name, false, true)"
            >
              <el-option
                v-for="item in condition.country_name"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="提交人：" prop="op">
            <el-input v-model="formInline.op" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="发起时间：" prop="created_at">
            <el-date-picker
              v-model="formInline.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="封禁时间：" prop="duration_time">
            <el-date-picker
              v-model="formInline.duration_time"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'

import { createBlackIpTask, delBlackIpList, blackIpList } from '@/api/apiConfig/api.js'
import { getAllUser } from '@/api/apiConfig/discovery.js'

export default {
  components: { tableTooltip, hightFilter },
  props: ['taskInfoId'],
  data() {
    return {
      highTabShow: [
        {
          label: 'IP地址',
          name: 'ip',
          type: 'input'
        },
        {
          label: '端口',
          name: 'ip',
          type: 'select'
        },
        {
          label: '协议',
          name: 'protocol',
          type: 'select'
        },
        {
          label: '类别',
          name: 'category',
          type: 'select'
        },
        {
          label: '地理位置',
          name: 'country_name',
          type: 'select'
        },
        {
          label: '提交人',
          name: 'op',
          type: 'input'
        },
        {
          label: '发起时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '封禁时间',
          name: 'duration_time',
          type: 'date'
        }
      ],
      // 存储下发任务文件上传数据
      fileData: [],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      upWays: [
        {
          id: 0,
          name: '手动输入'
        },
        {
          id: 1,
          name: '文件上传'
        }
      ],
      loading: false,
      checkedAll: false,
      highCheckdialog: false,
      btnLoading: false,
      editIs: true,
      zhouqiIs: false,
      changtingFlag: false,
      addIsTrue: true,
      transferProp: {},
      transferData: [],
      checkedArr: [],
      userArr: [],
      condition: {},
      levelArr: [
        {
          id: 3,
          name: '高'
        },
        {
          id: 2,
          name: '中'
        },
        {
          id: 1,
          name: '低'
        }
      ],
      statusArr: [
        {
          id: 1,
          name: '扫描中'
        },
        {
          id: 0,
          name: '等待扫描'
        },
        {
          id: 2,
          name: '扫描完成'
        }
      ],
      ruleFormAdd: {
        name: '',
        banning_ways: 0,
        ips: '',
        forbid_level: ''
      },
      ruleFormRules: {
        name: [{ required: true, message: '请填写任务名称', trigger: 'blur' }],
        ips: [{ required: true, message: '请填写黑IP', trigger: 'blur' }],
        forbid_level: [{ required: true, message: '请选择封禁等级', trigger: 'change' }]
      },
      fileList: [],
      uploadMaxCount: 1,
      formInline: {
        keyword: '',
        ip: '',
        category: [],
        country_name: [],
        op: '',
        protocol: [],
        port: [],
        created_at: '',
        duration_time: ''
      },
      dialogFormVisible: false,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      currentPage: 1,
      dialogFormVisibleFenzu: false,
      tableData: [],
      currentPageIsShow: true,
      tableHeader: [
        {
          label: 'IP地址',
          name: 'ip',
          icon: 'input',
          minWidth: '120'
        },
        {
          label: '端口',
          name: 'port',
          icon: 'input',
          minWidth: '80'
        },
        {
          label: '协议',
          name: 'protocol',
          icon: 'input',
          minWidth: '90'
        },
        {
          label: '类别',
          name: 'category',
          icon: 'input',
          minWidth: '200'
        },
        {
          label: '地理位置',
          name: 'country_name',
          icon: 'input',
          minWidth: '100'
        },
        {
          label: '发起时间',
          name: 'created_at',
          icon: 'select',
          minWidth: '140'
        },
        {
          label: '封禁时间',
          name: 'duration_time',
          minWidth: '140'
        },
        {
          label: '提交人',
          name: 'op'
        }
      ],
      user: {
        role: ''
      },
      currentId: '',
      highlist: null,
      uploadPath: '',
      userIsOpen: true
    }
  },
  computed: {
    ...mapState(['socketTimestamp', 'websocketMessage', 'currentCompany', 'setTimeBox']),
    ...mapGetters([
      'getterCurrentCompany',
      'getterWebsocketMessage',
      'getterSocketTimestamp',
      'getterSettime'
    ])
  },
  watch: {
    getterSettime(val) {
      // 监听禁扫时间
      this.getMergeTakList()
    },
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (val == -1) {
        this.userIsOpen = false
      } else {
        setTimeout(() => {
          // 权限控制
          if (sessionStorage.getItem('companyInfo')) {
            let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
            if (companyInfo.owner.black_ip_switch == 1) {
              this.userIsOpen = true
            } else {
              this.userIsOpen = false
            }
          }
        }, 1000)
      }

      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getMergeTakList()
      }
    },
    taskInfoId(val) {
      if (val) {
        this.getMergeTakList()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  created() {
    if (sessionStorage.getItem('queryParam')) {
      // 从查看列表页面返回的保留筛选条件等
      let queryParam = JSON.parse(sessionStorage.getItem('queryParam'))
      this.currentPageIsShow = false
      this.currentPageIsShow = true
      this.currentPage = queryParam.page
      this.pageSize = queryParam.per_page
      this.formInline.keyword = queryParam.keyword
      this.formInline.status = queryParam.status
      this.formInline.created_at_range = queryParam.created_at_range
      this.formInline.user_id = queryParam.user_id
    }
    if (sessionStorage.getItem('companyInfo')) {
      // 权限控制
      let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      this.userIsOpen = companyInfo.black_ip_switch == 0 ? false : true
    } else {
      this.userIsOpen = true
    }
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      if (this.user.role == 2) {
        if (!this.currentCompany) {
          return
        }
        this.getMergeTakList()
      } else {
        this.getMergeTakList()
      }
    }
    // 权限控制
    if (sessionStorage.getItem('companyInfo')) {
      let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      if (companyInfo.owner.black_ip_switch == 1) {
        this.userIsOpen = true
      } else {
        this.userIsOpen = false
      }
    } else {
      this.companyInfo = null
    }
  },
  async mounted() {
    // this.getMergeTakList()
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
  },
  methods: {
    ...mapMutations(['setTimeChangeBox']),
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    downloadAssetsExcel() {
      window.location.href = `/downloadTemplate/黑IP导入模板.xlsx`
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    getTableItem(item) {
      if (item) {
        if (Array.isArray(item)) {
          return item.join(',')
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    get_category(val, filter) {
      let arr = []
      if (val) {
        if (filter) {
          arr = val.slice(0, 2)
        } else {
          arr = val
        }
      } else {
        arr = []
      }
      return arr
    },
    // 切换黑ip输入方式
    banningWayChange() {
      this.ruleFormAdd.ips = ''
      this.uploadPath = ''
    },
    changSetTimeBox() {
      //禁扫时间弹框
      if (this.setTimeBox) {
        this.setTimeChangeBox(false)
      } else {
        this.setTimeChangeBox(true)
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选查询
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getMergeTakList()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    getTableStatus(status) {
      let statusLabel = ''
      if (status == 0) {
        statusLabel = '等待扫描'
      } else if (status == 1) {
        statusLabel = '扫描中'
      } else if (status == 2) {
        statusLabel = '扫描完成'
      } else if (status == 3) {
        statusLabel = '扫描失败'
      } else if (status == 4) {
        statusLabel = '暂停扫描'
      }
      return statusLabel
    },
    setProgressColor(status) {
      if (status == 4) {
        return 'warning'
      }
    },
    async highCheckIsShow() {
      this.highCheckdialog = true
      let res = await getAllUser()
      this.userArr = res.data
    },
    async getMergeTakList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      let obj = {
        ...this.formInline,
        page: this.currentPage,
        per_page: this.pageSize,
        black_task_id: this.taskInfoId,
        keyword: this.formInline.keyword,
        created_at_range: this.formInline.created_at ? this.formInline.created_at : [],
        dispatched_at_range: this.formInline.duration_time ? this.formInline.duration_time : [],
        operate_company_id: this.currentCompany
      }
      this.loading = true
      let res = await blackIpList(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
        this.wait_num = 0
      })
      if (res.code == 0) {
        this.loading = false
        this.tableData = res.data && res.data.items ? res.data.items : []
        this.condition = res.data && res.data.condition ? res.data.condition : {}
        this.total = res.data.total
      }
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else if (this.radioParams && this.radioParams.id) {
        // 报告自定义回显
        this.tableData.forEach((row) => {
          this.radioParams.id.forEach((item) => {
            if (item == row.id) {
              this.$refs.eltable.toggleRowSelection(row, true)
            }
          })
        })
      }
    },
    async insertSave() {
      this.$refs.ruleFormAdd.validate(async (valid) => {
        if (valid) {
          this.btnLoading = true
          let res = await createBlackIpTask({
            name: this.ruleFormAdd.name,
            ips:
              this.ruleFormAdd.banning_ways == 0
                ? this.ruleFormAdd.ips
                    .split(/[；|;|\r\n]/)
                    .filter((item) => {
                      return item.trim()
                    })
                    .map((item) => {
                      return item.trim()
                    })
                : this.fileData,
            forbid_level: this.ruleFormAdd.forbid_level,
            operate_company_id: this.currentCompany
          }).catch(() => {
            this.btnLoading = false
          })
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.btnLoading = false
            this.dialogFormVisible = false
          }
        }
      })
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    // websocket执行
    runningFunc(res) {
      if (!('scan_poc_num' in res.data) || res.data.user_id == this.currentCompany) {
        // 漏洞核查与资产、漏洞扫描区分，scan_poc_num存在是漏洞核查任务
        if (res.data.status == 2) {
          if (res.cmd == 'scan_task_progress') {
            resetMessage.success('扫描成功！')
          }
          this.currentId = '' // 控制推送结束后仅执行一次
          this.getMergeTakList()
        } else if (res.data.status == 1) {
          // 正在扫描
          this.currentId = res.data.user_id
          if (res.data.progress == 100) {
            // 避免状态为1时进度已经达到100但是页面不提示完成信息，可手动变成99.9，状态为2时正常提示
            res.data.progress = 99.9
          }
          // 推送数据渲染到列表
          this.tableData.forEach((item, index) => {
            if (item.id == res.data.task_id) {
              this.$set(this.tableData[index], 'status', res.data.status)
              this.$set(this.tableData[index], 'progress', res.data.progress)
              this.$set(this.tableData[index], 'use_seconds', res.data.use_seconds)
              this.$set(this.tableData[index], 'start_at', res.data.start_at)
            }
          })
        } else {
          // 3 扫描失败
          this.getMergeTakList()
        }
      }
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (res.cmd == 'type_threat_audit' && this.$route.path == '/leakScan') {
        // 漏洞扫描钉钉审核通过
        this.getMergeTakList()
      }
      this.runningFunc(res)
    },
    // beforeIpUpload(file){
    //   let isLt1M = ''
    //   if (this.routePath == 'newAssets') {
    //     isLt1M = file.size / 1024 / 1024 < 20;
    //   } else {
    //     isLt1M = file.size / 1024 / 1024 < 45;
    //   }
    //   if (!isLt1M) {
    //     this.$message.error(`上传文件不能超过${this.routePath == 'newAssets' ? '20M' : '45MB'}!`);
    //   }
    //   return isLt1M;
    // },
    beforeIpUpload(file, limit = 20) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    },
    uploadSuccess(response, file, fileList) {
      if (file.response && file.response.data && file.response.data.url) {
        this.uploadPath = file.response.data.url
      }
      if (file.response.code == 0) {
        this.$message.success('上传成功！')
        this.fileData = file.response.data
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadError(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      if (myError.status == 401) {
        this.$router.replace('/login')
        sessionStorage.clear()
        localStorage.clear()
      } else {
        this.$message.error(myError.message)
      }
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.uploadPath = ''
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getMergeTakList(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMergeTakList(true)
    },
    async addTaskDialog() {
      this.fileData = []
      this.dialogFormVisible = true
      this.ruleFormAdd = {
        name: '',
        banning_ways: 0,
        ips: '',
        forbid_level: ''
      }
    },
    resetForm() {
      this.formInline = {
        keyword: '',
        status: '',
        created_at_range: [],
        user_id: ''
      }
    },
    // 处理数据函数
    assetsDeal(time, level) {
      let timer = this.secondsFormat(time)
      let levelTmp = level == '3' ? '高' : level == '2' ? '中' : '低'
      if (timer == undefined) {
        return ''
      } else {
        return levelTmp + '(' + timer + ')'
      }
    },
    removeOne(icon, id) {
      let obj
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
        obj = {
          ids: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          operate_company_id: this.currentCompany,
          keyword: this.formInline.keyword
        }
      } else {
        obj = { ids: [id], operate_company_id: this.currentCompany }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'scan_del_cancel',
        confirmButtonClass: 'scan_del_sure',
        customClass: 'scan_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await delBlackIpList(obj)
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.currentPage = this.updateCurrenPage(
              this.total,
              [1],
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getMergeTakList()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.scan_del>.el-message-box__btns')
        del.children[0].id = 'scan_del_cancel'
        del.children[1].id = 'scan_del_sure'
      }, 50)
    },
    viewList(id, status) {}
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .result {
      position: relative;
      padding: 47px 0 0 24px;
      flex: 0 0 auto;
      .normalBtn {
        position: absolute;
        top: 16px;
        right: 20px;
      }
      .scanning {
        display: flex;
        align-items: center;
        .scanning-img {
          width: 56px;
          height: 56px;
          svg {
            font-size: 56px;
            color: #ddd;
          }
        }
        .scanning-txt {
          margin-left: 15px;
          line-height: 1;
          .txt1 {
            color: #62666c;
            font-size: 16px;
            font-weight: 400;
            .name {
              font-weight: 500;
              color: #37393c;
            }
          }
          .txt2 {
            font-size: 12px;
            font-weight: 400;
            color: #62666c;
            padding-top: 10px;
            .time {
              margin-left: 21px;
            }
          }
          .txt3 {
            width: 248px;
            height: 16px;
            margin-bottom: 14px;
            background: #e5ebf4;
          }
          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 180px;
            height: 14px;
            background: #e5ebf4;
          }
          .txt5 {
            display: inline-block;
            width: 132px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }
      .handle {
        padding-top: 30px;
        padding-bottom: 29px;
        .line1 {
          overflow: hidden;
          .progress {
            float: left;
            width: 80%;
            height: 24px;
            background: #e5ebf4;
            border-radius: 12px;
            .el-progress.is-success .el-progress-bar__inner {
              top: 4px;
              left: 4px;
              height: 16px;
              background: -webkit-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -o-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -moz-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              border-radius: 12px;
            }
            ::v-deep .el-progress-bar__outer {
              background-color: #e5ebf4;
              border-radius: 0;
            }
            ::v-deep .el-progress.is-warning .el-progress-bar__inner {
              border-radius: 0;
            }
          }
          .btns {
            display: inline-block;
            padding-left: 16px;
            font-weight: 400;
            color: #2677ff;
            cursor: pointer;
            span {
              margin-right: 12px;
              i {
                margin-right: 3px;
              }
            }
          }
        }
        .line2 {
          width: 80%;
          font-size: 14px;
          color: #999;
          margin-top: 15px;
          display: flex;
          justify-content: space-between;
          .txt3 {
            width: 179px;
            height: 14px;
            background: #e5ebf4;
          }
          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 114px;
            height: 14px;
            background: #e5ebf4;
          }
          .txt5 {
            display: inline-block;
            width: 86px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      .emptyClass {
        height: 100%;
        text-align: center;
        vertical-align: middle;
        svg {
          display: inline-block;
          font-size: 120px;
        }
        p {
          line-height: 25px;
          color: #d1d5dd;
          span {
            margin-left: 4px;
            color: #2677ff;
            cursor: pointer;
          }
        }
      }
      .el-progress-bar__innerText {
        vertical-align: top !important;
      }
    }
    .ruleItemBox {
      display: flex;
      flex-wrap: wrap !important;
      padding-right: 12px !important;
      padding-left: 12px !important;
      // max-height: 50px;
    }
    .ruleItem,
    .ruleItemNum {
      line-height: 16px;
      padding: 2px 10px;
      // margin-bottom: 10px;
      margin: 5px 8px 5px 0px;
      background: #ffffff;
      border-radius: 14px;
      border: 1px solid #d1d5dd;
      cursor: pointer;
      white-space: pre-wrap;
      // margin-right: 8px;
    }
    .ruleItemNum {
      display: inline-block;
      background: #f0f3f8;
      border: 1px solid #dce5f3;
      cursor: pointer;
    }
    .el-table {
      border: 0;
      td {
        border-right: transparent !important;
      }
    }
    // 定义单元格文本超出不换行
    .el-table .cell {
      overflow: hidden !important;
      white-space: nowrap !important;
    }
  }
}
.placeholderIdBox {
  background-color: transparent !important;
  /deep/.el-textarea__inner {
    background-color: transparent !important;
  }
  z-index: 1;
}
.placeholderId {
  position: absolute;
  top: 0px;
  div {
    min-height: 22px;
    line-height: 22px;
    color: #c0c4cc;
    padding-left: 15px;
  }
}
</style>
