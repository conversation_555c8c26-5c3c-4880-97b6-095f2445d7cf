<template>
  <div class="container">
    <div v-if="!taskInfoId" class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入任务名称进行搜索"
            id="scan_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <!-- <span @click="highCheckIsShow" id="scan_filter" style="width:80px"><img src="../../assets/images/filter.png" alt="" style="width:16px;vertical-align: middle;margin-right:3px">高级筛选</span> -->
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="scan_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="removeOne('more')"
            id="scan_more_del"
            :disabled="!userIsOpen"
            >删除</el-button
          >
          <!-- <el-button class="normalBtn" type="primary" @click="addTaskDialog" id="scan_add">任务</el-button> -->
        </div>
      </div>
      <div :class="hightFilterIsShow()" v-loading="loading" style="padding: 0px 20px">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :show-overflow-tooltip="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidtth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <!-- 0：等待扫描 1：扫描中 2：扫描完成 3：扫描失败 4：暂停扫描 -->
              <span v-if="item.name == 'name'">{{ scope.row[item.name] }}</span>
              <span v-else-if="item.name == 'status'">
                <span v-if="scope.row['status'] == 0" class="grayLine">等待扫描</span>
                <span v-if="scope.row['status'] == 1" class="blueLine">扫描中</span>
                <span v-if="scope.row['status'] == 2" class="greenLine">扫描完成</span>
                <span v-if="scope.row['status'] == 3" class="redLine">扫描失败</span>
                <span v-if="scope.row['status'] == 4" class="yellowLine">
                  <el-tooltip placement="top" v-if="scope.row['status'] == 4" :open-delay="500">
                    <div slot="content">
                      <span>当前处于禁扫时间内，如需继续执行任务，请重新</span
                      ><span @click="changSetTimeBox" style="color: #409eff; cursor: pointer"
                        >设置禁扫时间</span
                      >
                    </div>
                    <i class="el-icon-question" style="cursor: pointer">暂停扫描</i>
                  </el-tooltip>
                </span>
                <el-progress
                  v-if="scope.row['status'] == 1 || scope.row['status'] == 4"
                  :text-inside="true"
                  :stroke-width="12"
                  :percentage="parseFloat(scope.row['progress'])"
                  :status="setProgressColor(scope.row['status'])"
                ></el-progress>
              </span>
              <span v-else-if="item.name == 'use_seconds'">
                {{ secondsFormat(get_use_seconds(scope.row)) }}
              </span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="180">
            <template slot-scope="scope">
              <span>
                <el-button type="text" size="small" @click="viewList(scope.row.id)" id="scan_info"
                  >查看详情</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  @click="removeOne('one', scope.row.id)"
                  id="scan_del"
                  >删除</el-button
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        v-if="currentPageIsShow"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <ipList v-if="taskInfoId" :taskInfoId="taskInfoId" @backList="taskInfoId = ''" />
  </div>
</template>

<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import ipList from './ipBanningList.vue' // 列表
import { delBlackIpTask, blackIpTaskList, remind } from '@/api/apiConfig/api.js'
import { getAllUser } from '@/api/apiConfig/discovery.js'

export default {
  components: { tableTooltip, hightFilter, ipList },
  props: ['task_type'],
  data() {
    return {
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      taskInfoId: '',
      loading: false,
      checkedAll: false,
      highCheckdialog: false,
      editIs: true,
      zhouqiIs: false,
      changtingFlag: false,
      addIsTrue: true,
      transferProp: {},
      transferData: [],
      checkedArr: [],
      userArr: [],
      statusArr: [
        {
          id: 1,
          name: '扫描中'
        },
        {
          id: 0,
          name: '等待扫描'
        },
        {
          id: 2,
          name: '扫描完成'
        }
        // {
        //   id: 3,
        //   name: '扫描失败'
        // },
        // {
        //   id: 4,
        //   name: '暂停扫描'
        // }
      ],
      typeArr: [
        {
          id: 0,
          name: '立即执行'
        },
        {
          id: 1,
          name: '周期任务'
        }
      ],
      portGroupsNoPageArr: [],
      portGroupsNoPageArrs: [],
      scanId: '', //全部常用端口的id
      isScanId: '', //0-65535端口的id
      isdisabled: false,
      formInline: {
        name: '',
        type: '',
        type_name: '',
        status: '',
        created_at_range: [],
        user_id: '',
        op_id: '',
        keyword: ''
      },
      dialogFormVisible: false,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      currentPage: 1,
      data: [],
      filterMethod(query, item) {
        return item.pinyin.indexOf(query) > -1
      },
      rules: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        ips: [{ required: true, message: '请输入或上传ip信息', trigger: 'change' }]
      },
      userIsOpen: false,
      ruleForm: {
        bandwidth: '1000', // 扫描带宽
        name: '',
        ip_type: 1, // ipv4: 1; ipv6:2
        protocol_concurrency: 0, // 协议识别并发数，整型，0,2,4,8,10,12,14,16,18,20,22,24,26,28,30,100,200,300,
        scan_type: 1, // 扫描类型 0为精准 1为极速,
        type: 5, // 类型 默认6 2周期扫描 3月 4周 5天 6一次,
        poc_group_ids: '', // poc分组id,
        port_group_ids: '', // port分组id,
        // poc_scan_type: 0, // "0全部poc  1指定poc",
        poc_ids: [], // poc扫描指定poc，若poc范围选择[全部poc],则不传此字段
        ips: null,
        ping_switch: false, // 开启ping资产识别 默认0 关闭 1 打开,
        web_logo_switch: false, // 0不开启 1开启
        operate_company_id: '', // 安服角色需要此id
        define_port_protocols: [], //自定义协议
        define_ports: null //自定义端口
      },
      dialogFormVisibleFenzu: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      tableData: [],
      currentPageIsShow: true,
      tableHeader: [
        {
          label: '任务名称',
          name: 'name',
          fixed: 'left',
          minWidtth: '200'
        },
        {
          label: '任务状态',
          name: 'status',
          minWidtth: '90'
        },
        {
          label: '开始时间',
          name: 'created_at',
          minWidtth: '120'
        },
        {
          label: '结束时间',
          name: 'updated_at',
          minWidtth: '120'
        },
        {
          label: '任务耗时',
          name: 'use_seconds',
          minWidtth: '60',
          minWidtth: '80'
        },
        {
          label: '发起人',
          name: 'op',
          minWidtth: '80'
        }
      ],
      runningTask: null,
      user: {
        role: ''
      },
      currentId: '',
      runningFuncTimer: null,
      highlist: null,
      isTypes: 'true'
    }
  },
  computed: {
    ...mapState(['socketTimestamp', 'websocketMessage', 'currentCompany', 'setTimeBox']),
    ...mapGetters([
      'getterCurrentCompany',
      'getterWebsocketMessage',
      'getterSocketTimestamp',
      'getterSettime'
    ])
  },
  watch: {
    getterSettime(val) {
      // 监听禁扫时间
      this.getMergeTakList()
    },
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (val == -1) {
        this.userIsOpen = false
      } else {
        setTimeout(() => {
          // 权限控制
          if (sessionStorage.getItem('companyInfo')) {
            let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
            if (companyInfo.owner.black_ip_switch == 1) {
              this.userIsOpen = true
            } else {
              this.userIsOpen = false
            }
          }
        }, 1000)
      }

      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getMergeTakList()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  created() {
    if (sessionStorage.getItem('queryParam')) {
      // 从查看列表页面返回的保留筛选条件等
      let queryParam = JSON.parse(sessionStorage.getItem('queryParam'))
      this.currentPageIsShow = false
      this.currentPageIsShow = true
      this.currentPage = queryParam.page
      this.pageSize = queryParam.per_page
      this.formInline.name = queryParam.name
      this.formInline.keyword = queryParam.keyword
      this.formInline.status = queryParam.status
      this.formInline.created_at_range = queryParam.created_at_range
      this.formInline.user_id = queryParam.user_id
    }
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      if (this.user.role == 2) {
        if (!this.currentCompany) {
          return
        }
        this.getMergeTakList()
      } else {
        this.getMergeTakList()
      }
    }
    // 权限控制
    if (sessionStorage.getItem('companyInfo')) {
      let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      if (companyInfo.owner.black_ip_switch == 1) {
        this.userIsOpen = true
      } else {
        this.userIsOpen = false
      }
    } else {
      this.companyInfo = null
    }
  },
  async mounted() {
    // this.getMergeTakList()
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
  },
  methods: {
    ...mapMutations(['setTimeChangeBox']),
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    changSetTimeBox() {
      //禁扫时间弹框
      if (this.setTimeBox) {
        this.setTimeChangeBox(false)
      } else {
        this.setTimeChangeBox(true)
      }
    },
    get_use_seconds(row) {
      return new Date(row.updated_at).getTime() / 1000 - new Date(row.created_at).getTime() / 1000
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    goIndex(data) {
      this.$router.push({ path: '/assetsLedger' })
    },
    async remindAgain(id) {
      //再次提醒
      let res = await remind(id)
      if (res.code == 0) {
        this.$message({
          message: '已提醒',
          type: 'success'
        })
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选查询
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getMergeTakList()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    getTableStatus(status) {
      let statusLabel = ''
      if (status == 0) {
        statusLabel = '等待扫描'
      } else if (status == 1) {
        statusLabel = '扫描中'
      } else if (status == 2) {
        statusLabel = '扫描完成'
      } else if (status == 3) {
        statusLabel = '扫描失败'
      } else if (status == 4) {
        statusLabel = '暂停扫描'
      }
      return statusLabel
    },
    setProgressColor(status) {
      if (status == 4) {
        return 'warning'
      }
    },
    async highCheckIsShow() {
      this.highCheckdialog = true
      let res = await getAllUser()
      this.userArr = res.data
    },
    async getMergeTakList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        type: this.formInline.type,
        name: this.formInline.name,
        status: this.formInline.status, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
        created_at_range: this.formInline.created_at_range ? this.formInline.created_at_range : [],
        operate_company_id: this.currentCompany,
        keyword: this.formInline.keyword
      }
      this.loading = true
      let res = await blackIpTaskList(obj).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
        this.wait_num = 0
      })
      this.loading = false
      this.tableData = res.data && res.data.items ? res.data.items : []
      this.total = res.data.total
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else if (this.radioParams && this.radioParams.id) {
        // 报告自定义回显
        this.tableData.forEach((row) => {
          this.radioParams.id.forEach((item) => {
            if (item == row.id) {
              this.$refs.eltable.toggleRowSelection(row, true)
            }
          })
        })
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    // websocket执行
    runningFunc(res) {
      if (res.cmd == 'black_ips_search_host') {
        if (res.data.status == 2) {
          resetMessage.success('扫描成功！')
          this.currentId = '' // 控制推送结束后仅执行一次
          this.getMergeTakList()
        } else if (res.data.status == 1) {
          // 正在扫描
          this.currentId = res.data.user_id
          if (res.data.progress == 100) {
            // 避免状态为1时进度已经达到100但是页面不提示完成信息，可手动变成99.9，状态为2时正常提示
            res.data.progress = 99.9
          }
          // 推送数据渲染到列表
          this.tableData.forEach((item, index) => {
            if (item.id == res.data.task_id) {
              this.$set(this.tableData[index], 'status', res.data.status)
              this.$set(this.tableData[index], 'progress', res.data.progress)
              this.$set(this.tableData[index], 'use_seconds', res.data.use_seconds)
              this.$set(this.tableData[index], 'start_at', res.data.start_at)
            }
          })
        } else {
          // 3 扫描失败
          this.getMergeTakList()
        }
      }
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (res.cmd == 'type_threat_audit' && this.$route.path == '/leakScan') {
        // 漏洞扫描钉钉审核通过
        this.getMergeTakList()
      }
      this.runningFunc(res)
    },
    // socket 关闭
    socketClose() {
      if (this.socket) {
        this.socket.close()
        this.socket = null
      }
    },
    dialogFormVisibleClose() {
      this.dialogFormVisible = false
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getMergeTakList(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMergeTakList(true)
    },
    goToView(res) {},
    async addTaskDialog() {},
    resetForm() {
      this.formInline = {
        name: '',
        status: '',
        created_at_range: [],
        user_id: ''
      }
    },
    removeOne(icon, id) {
      let obj
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
        obj = {
          ids: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          operate_company_id: this.currentCompany,
          keyword: this.formInline.keyword
        }
      } else {
        obj = { ids: [id], operate_company_id: this.currentCompany }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'scan_del_cancel',
        confirmButtonClass: 'scan_del_sure',
        customClass: 'scan_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await delBlackIpTask(obj)
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.currentPage = this.updateCurrenPage(
              this.total,
              [1],
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getMergeTakList()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.scan_del>.el-message-box__btns')
        del.children[0].id = 'scan_del_cancel'
        del.children[1].id = 'scan_del_sure'
      }, 50)
    },
    viewList(id, status) {
      this.taskInfoId = id
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 0;
          margin-right: 20px;
          cursor: pointer;
        }
      }
    }
    .result {
      position: relative;
      padding: 47px 0 0 24px;
      flex: 0 0 auto;
      .normalBtn {
        position: absolute;
        top: 16px;
        right: 20px;
      }
      .scanning {
        display: flex;
        align-items: center;
        .scanning-img {
          width: 56px;
          height: 56px;
          svg {
            font-size: 56px;
            color: #ddd;
          }
        }
        .scanning-txt {
          margin-left: 15px;
          line-height: 1;
          .txt1 {
            color: #62666c;
            font-size: 16px;
            font-weight: 400;
            .name {
              font-weight: 500;
              color: #37393c;
            }
          }
          .txt2 {
            font-size: 12px;
            font-weight: 400;
            color: #62666c;
            padding-top: 10px;
            .time {
              margin-left: 21px;
            }
          }
          .txt3 {
            width: 248px;
            height: 16px;
            margin-bottom: 14px;
            background: #e5ebf4;
          }
          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 180px;
            height: 14px;
            background: #e5ebf4;
          }
          .txt5 {
            display: inline-block;
            width: 132px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }
      .handle {
        padding-top: 30px;
        padding-bottom: 29px;
        .line1 {
          overflow: hidden;
          .progress {
            float: left;
            width: 80%;
            height: 24px;
            background: #e5ebf4;
            border-radius: 12px;
            .el-progress.is-success .el-progress-bar__inner {
              top: 4px;
              left: 4px;
              height: 16px;
              background: -webkit-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -o-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -moz-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              border-radius: 12px;
            }
            ::v-deep .el-progress-bar__outer {
              background-color: #e5ebf4;
              border-radius: 0;
            }
            ::v-deep .el-progress.is-warning .el-progress-bar__inner {
              border-radius: 0;
            }
          }
          .btns {
            display: inline-block;
            padding-left: 16px;
            font-weight: 400;
            color: #2677ff;
            cursor: pointer;
            span {
              margin-right: 12px;
              i {
                margin-right: 3px;
              }
            }
          }
        }
        .line2 {
          width: 80%;
          font-size: 14px;
          color: #999;
          margin-top: 15px;
          display: flex;
          justify-content: space-between;
          .txt3 {
            width: 179px;
            height: 14px;
            background: #e5ebf4;
          }
          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 114px;
            height: 14px;
            background: #e5ebf4;
          }
          .txt5 {
            display: inline-block;
            width: 86px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      .emptyClass {
        height: 100%;
        text-align: center;
        vertical-align: middle;
        svg {
          display: inline-block;
          font-size: 120px;
        }
        p {
          line-height: 25px;
          color: #d1d5dd;
          span {
            margin-left: 4px;
            color: #2677ff;
            cursor: pointer;
          }
        }
      }
      .el-progress-bar__innerText {
        vertical-align: top !important;
      }
    }
    .el-table {
      border: 0;
      td {
        border-right: transparent !important;
      }
    }
    // 定义单元格文本超出不换行
    .el-table .cell {
      overflow: hidden !important;
      white-space: nowrap !important;
    }
  }
}
</style>
