<template>
  <div class="container">
    <p class="checkMore"><span @click="checkMoreData">查看更多</span></p>
    <el-table
      :data="tableData"
      v-loading="loading"
      row-key="id"
      :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
      ref="eltable"
      height="91%"
      style="width: 100%"
    >
      <!-- 只有单位资产测绘才有企业名称 -->
      <el-table-column
        v-for="item in tableHeader.filter((item) => {
          return item.path.indexOf(pageIcon) > -1
        })"
        :key="item.id"
        :prop="item.name"
        align="left"
        :min-width="item.minWidth"
        :label="item.label"
      >
        <template slot-scope="scope">
          <span>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import { domainAssetTaskList } from '@/api/apiConfig/domain.js'
import { getAllTaskList } from '@/api/apiConfig/discovery.js'
import { detectTaskList } from '@/api/apiConfig/surveying.js'

export default {
  props: ['pageIcon'],
  data() {
    return {
      tableHeader: [
        {
          label: '企业名称',
          name: 'name',
          minWidth: 100,
          path: [1, 2]
        },
        {
          label: '线索数量',
          name: 'clues_count',
          minWidth: 60,
          path: [1, 2]
        },
        {
          label: '进度',
          name: 'step',
          minWidth: 120,
          path: [1, 2]
        },
        {
          label: '发起人',
          name: 'username',
          path: [1, 2]
        },
        {
          label: '任务名称',
          name: 'name',
          fixed: 'left',
          minWidth: '200',
          path: [3, 6]
        },
        {
          label: '任务状态',
          name: 'status',
          minWidth: '90',
          path: [3, 6]
        },
        {
          label: '任务耗时',
          name: 'use_seconds',
          minWidth: '60',
          path: [3]
        },
        {
          label: '任务模式',
          name: 'modify',
          minWidtth: '70',
          path: [6]
        },
        {
          label: '爆破层级',
          name: 'level',
          minWidtth: '90',
          path: [6]
        }
      ],
      loading: false,
      tableData: []
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getTableData()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    },
    getterWebsocketMessage(msg) {}
  },
  computed: {
    ...mapState(['currentCompany', 'recommentFlag']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    getTableData() {
      let obj = {}
      let runFunc = ''
      if (this.pageIcon == 1 || this.pageIcon == 2) {
        obj = {
          expand_source: this.pageIcon == 1 ? '0' : '1' // 0单位，1云端
        }
        runFunc = detectTaskList
      } else if (this.pageIcon == 3) {
        obj = {
          type: '',
          task_type: 1, // 任务分类 1 资产扫描 2 漏洞扫描,
          status: 2, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
          sort_order: 'asc',
          is_schedule: 0
        }
        runFunc = getAllTaskList
      } else if (this.pageIcon == 4) {
        runFunc = detectTaskList
      } else if (this.pageIcon == 5) {
        runFunc = detectTaskList
      } else if (this.pageIcon == 6) {
        obj.status = '5' // 已完成
        runFunc = domainAssetTaskList
      }
      obj.page = 1
      obj.per_page = 10
      obj.operate_company_id = this.currentCompany
      this.loading = true
      runFunc(obj)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    checkMoreData() {
      if (this.pageIcon == 1) {
        sessionStorage.setItem('menuId', '2-3')
        this.changeMenuId('2-3')
        this.$router.push('/unitIndex')
      } else if (this.pageIcon == 2) {
        sessionStorage.setItem('menuId', '2-2')
        this.changeMenuId('2-2')
        this.$router.push('/assetsCloud')
      } else if (this.pageIcon == 3) {
        sessionStorage.setItem('menuId', '2-1')
        this.changeMenuId('2-1')
        this.$router.push('/assetsScan')
      } else if (this.pageIcon == 4) {
        sessionStorage.setItem('menuId', '2-4')
        this.changeMenuId('2-4')
        this.$router.push('/checkTask')
      } else if (this.pageIcon == 5) {
        sessionStorage.setItem('menuId', '2-5')
        this.changeMenuId('2-5')
        this.$router.push('/statusTask')
      } else if (this.pageIcon == 6) {
        sessionStorage.setItem('menuId', '2-6')
        this.changeMenuId('2-6')
        this.$router.push('/domainTask')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  .checkMore {
    padding: 0 0 12px 0;
    color: rgba(38, 119, 255, 1);
    text-align: right;
    cursor: pointer;
  }
}
</style>
