<template>
  <div class="containerWrap">
    <div class="headerTitle">
      <span class="goback" @click="goBack"
        ><i class="el-icon-arrow-left"></i>返回<span class="spline">/</span></span
      >
      {{ headerTitle }}
      <span v-if="task_type == 1" class="headerShow"
        ><i class="el-icon-warning"></i
        >针对目标IP以及域名进行深度探测，发现其端口、协议、组件等指纹信息</span
      >
    </div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="单次任务" name="first"> </el-tab-pane>
        <el-tab-pane label="周期任务" name="second"> </el-tab-pane>
      </el-tabs>
      <p class="downloadClass" v-if="wait_confirm > 0 && $route.path == '/leakScan'">
        <!-- `推荐资产需要进行确权才可下发漏洞扫描任务，当前有已确认推荐数据56条，待确认推荐数据 -->
        <i class="el-icon-warning myGray"></i
        >{{
          `推荐资产需要进行确权才可下发漏洞扫描任务，当前有已确认数据${already_confirm}条，待确认推荐数据${wait_confirm}条，`
        }}<span @click="goConfirm">去确权</span>
        <i class="el-icon-close" @click="closeTip"></i>
      </p>
      <div
        :class="wait_confirm > 0 && $route.path == '/leakScan' ? 'tab_content_tip' : 'tab_content'"
      >
        <taskScan
          key="first"
          v-if="activeName == 'first'"
          :task_type="task_type"
          :wait_confirm="wait_confirm"
        />
        <zhouqiTask key="second" v-if="activeName == 'second'" :task_type="task_type" />
      </div>
    </div>
  </div>
</template>

<script>
import taskScan from './taskLeakScan.vue'
import zhouqiTask from './zhouqiManage.vue'
import { mapMutations, mapGetters, mapState } from 'vuex'
import { sureassetsstatistics } from '@/api/apiConfig/asset.js'

export default {
  components: { zhouqiTask, taskScan },

  data() {
    return {
      headerTitle: '',
      task_type: '',
      activeName: '',
      socket: null,
      already_confirm: 0,
      wait_confirm: 0,
      user: {
        role: ''
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  watch: {
    $route(to, from) {
      sessionStorage.setItem('queryParam', '')
      sessionStorage.setItem('currentPageMy', 1)
      // if(this.$route.query.activeName){
      //   sessionStorage.setItem('activeTabName', this.$route.query.activeName)
      // }else {
      //   sessionStorage.setItem('activeTabName', '')
      // }
      this.init()
    },
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.init()
      }
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.init()
  },
  async mounted() {
    if (sessionStorage.getItem('activeTabName')) {
      this.activeName = sessionStorage.getItem('activeTabName')
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == '/alreadyTask_viewlist') {
        // 从已完成任务返回
        vm.activeName = 'first'
      } else {
        vm.activeName = 'first'
        sessionStorage.setItem('queryParam', '')
      }
    })
  },
  beforeDestroy() {
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goBack() {
      if (this.$route.query.fromPath == '/unitIndex') {
        this.$router.go(-1)
      } else {
        sessionStorage.setItem('menuId', '2-1')
        this.changeMenuId('2-1')
        this.$router.push('/assetsTanzhi')
      }
    },
    closeTip() {
      this.wait_confirm = 0
    },
    init() {
      // this.headerTitle = this.$route.name
      this.task_type = this.$route.meta.task_type
      this.headerTitle = this.task_type == 1 ? '资产扫描任务' : '漏洞扫描任务'
      if (sessionStorage.getItem('activeTabName')) {
        this.activeName = sessionStorage.getItem('activeTabName')
      } else {
        this.activeName = 'first'
      }
      if (this.$route.path == '/leakScan') {
        // 获取待确权列表
        this.getknownAssetsList()
      }
    },
    goConfirm() {
      sessionStorage.setItem('menuId', '1-3-1')
      this.changeMenuId('1-3-1')
      this.$router.push({
        path: '/assetsLedger',
        query: {
          confirm: 1
        }
      })
    },
    // 获取待确权数量统计
    async getknownAssetsList() {
      let res = await sureassetsstatistics({ operate_company_id: this.currentCompany }).catch(
        () => {
          this.already_confirm = 0
          this.wait_confirm = 0
        }
      )
      if (res.code == 0) {
        this.already_confirm = res.data.already_confirm
        this.wait_confirm = res.data.wait_confirm
      }
    },
    handleClick(tab) {
      sessionStorage.setItem('activeTabName', this.activeName)
    }
  }
}
</script>

<style lang="less" scoped>
.containerWrap {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      padding: 0 16px;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .tab_content {
      height: calc(100% - 44px);
    }
    .tab_content_tip {
      height: calc(100% - 101px);
    }
    .downloadClass {
      position: relative;
      height: 40px;
      line-height: 40px;
      margin: 16px 20px 0 20px;
      background: #f0f3f8;
      color: #62666c;
      border-radius: 4px;
      // border: 1px solid rgba(38, 119, 255, 0.44);
      cursor: pointer;
      i {
        font-size: 14px;
        color: #2677ff;
        margin: 0 8px 0 16px;
      }
      .el-icon-close {
        position: absolute;
        right: 10px;
        top: 12px;
        font-size: 16px;
        color: #9fa6af;
        &:hover,
        &:focus {
          background-color: transparent;
          color: rgba(159, 166, 175, 1) !important;
        }
      }
      span {
        color: #2677ff;
      }
    }
  }
}
.myGray {
  color: #bac6da !important;
}
/deep/.el-tabs__active-bar {
  width: 57px !important;
}
</style>
