<template>
  <div class="container" v-loading="loading">
    <!-- v-loading="loading" -->
    <div class="headerTitle">
      <div>
        <span
          class="goback"
          v-if="$route.query.url || $route.query.vulnerability"
          @click="goBackFromBrief()"
          ><i class="el-icon-arrow-left"></i>返回上一层 / &nbsp;&nbsp;</span
        >
        <span class="goback" v-if="$route.path == '/taskRepairLeakScan'" @click="$router.go(-1)"
          ><i class="el-icon-arrow-left"></i>返回上一层</span
        >
        <span class="spline" v-if="$route.path == '/taskRepairLeakScan'">/</span>
        <span v-if="$route.path == '/taskRepairLeakScan'">查看详情</span>
        <span v-else>{{ activeName == 'first' ? '未修复漏洞' : '已修复漏洞' }}</span>
        <span v-if="notifyFilterId">
          / {{ notifyFilterMsg }}
          <span style="color: #2677ff; cursor: pointer" @click="clearNotifyFilter">恢复默认</span>
        </span>
      </div>
      <p v-if="$route.path != '/taskRepairLeakScan'" class="statisticnumBox">
        <span v-for="item in selectData.level_arr" :key="item.id" class="statisticnum"
          >{{ item.name }}：<span class="num">{{ item.num }}</span></span
        >
      </p>
    </div>
    <div class="result">
      <div class="leftParameter" v-if="$route.path == '/taskRepairLeakScan'">
        <div class="parameterList">
          <div class="parameterTitle">任务参数</div>
          <div v-for="(item, index) in parameterList" :key="index" class="parameterBox">
            <div v-if="item.label == '扫描目标'">
              <div class="parameterBox2">
                <div class="parameterBoxTitle">
                  {{ item.label }}：
                  <el-tooltip
                    effect="light"
                    class="item"
                    placement="top"
                    content="一键复制"
                    v-if="ips && ips.length != 0"
                    :open-delay="500"
                  >
                    <i
                      class="el-icon-document-copy"
                      @click="copyIp(ips)"
                      style="color: #2677ff; cursor: pointer"
                    ></i>
                  </el-tooltip>
                </div>
                <!-- <el-tooltip effect="dark" enterable placement="top" :content="getScan(item.name)" :disabled="item.name.length <= 10" :open-delay="500"> -->
                <div class="parameterBoxContent">{{ getScan(item.name) }}</div>
                <!-- </el-tooltip> -->
              </div>
              <el-tooltip
                class="item"
                effect="light"
                popper-class="chainClass"
                placement="top"
                :disabled="ipsArrNum <= 3"
                :open-delay="500"
              >
                <div slot="content" v-for="(v, i) in ips" :key="i">{{ v }}</div>
                <div>
                  <div class="parameterBoxContent1" v-for="(v, i) in ipsArr" :key="i">{{ v }}</div>
                </div>
              </el-tooltip>
              <div v-if="ipsArrNum > 3" style="margin-top: -15px; margin-bottom: 16px">...</div>
            </div>
            <div v-else-if="item.label == 'PoC范围'">
              <div class="parameterBox2">
                <div class="parameterBoxTitle">{{ item.label }}：</div>
                <!-- <el-tooltip effect="dark" enterable placement="top" :content="getPoc(item.name)" :disabled="item.name.length <= 10"> -->
                <div class="parameterBoxContent">{{ poc_scan_typeMap[item.name] }}</div>
                <!-- <div class="parameterBoxContent">{{getPoc(item.name)}}</div> -->
                <!-- </el-tooltip> -->
              </div>
              <el-tooltip
                effect="light"
                popper-class="chainClass"
                placement="top"
                :disabled="pocNum <= 3"
                :open-delay="500"
              >
                <div slot="content" v-for="(v, i) in poc" :key="i">{{ v }}</div>
                <div>
                  <div class="parameterBoxContent1" v-for="(v, i) in pocArr" :key="i">{{ v }}</div>
                </div>
              </el-tooltip>
              <div v-if="pocNum > 3" style="margin-top: -15px">...</div>
            </div>

            <div class="parameterBox1" v-else>
              <div class="parameterBoxTitle">{{ item.label }}：</div>
              <!-- <el-tooltip effect="dark" enterable placement="top" :content="secondsFormat(item.name)" v-if="item.label == '任务耗时'"> -->
              <div class="parameterBoxContent" v-if="item.label == '任务耗时'">{{
                secondsFormat(item.name)
              }}</div>
              <!-- <div class="parameterBoxContent" v-else-if="item.label == '扫描类型'">{{
                scanTypeMap[item.name]
              }}</div> -->
              <!-- </el-tooltip> -->
              <div v-else-if="item.label == '扫描引擎'">
                <span v-if="item.name == 1"> Trascanner </span>
                <span v-else>{{
                  item.name && item.name == 2 ? 'Goscanner,Trascanner' : 'Goscanner'
                }}</span>
              </div>
              <div v-else-if="item.label == '扫描场景'">
                {{ item.name || item.name == 0 ? poc_scan_typeMap[item.name] : '-' }}
              </div>
              <!-- <el-tooltip effect="dark" enterable placement="top" :content="item.name" v-else> -->
              <div class="parameterBoxContent" v-else>{{ item.name }}</div>
              <!-- </el-tooltip> -->
            </div>
          </div>
        </div>
      </div>
      <div
        class="home_header"
        :style="$route.path == '/taskRepairLeakScan' ? 'width : 79%' : 'width : 100%'"
      >
        <div v-if="$route.path == '/taskRepairLeakScan'" class="taskResults">任务扫描结果</div>
        <el-tabs
          v-if="$route.path != '/taskRepairLeakScan'"
          v-model="activeName"
          @tab-click="handleClick"
        >
          <el-tab-pane
            label="未修复漏洞"
            name="first"
            v-if="!notifyFilterId || (notifyFilterId && activeName == 'first')"
          >
            <span slot="label">未修复漏洞</span>
            <div class="filterTab">
              <div>
                <el-input
                  v-model="formInline.keyword"
                  @keyup.enter.native="checkFuncList"
                  placeholder="请输入IP或漏洞名称进行搜索"
                  id="leak_keycheck"
                >
                  <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
                </el-input>
                <span v-if="userIsOpen" @click="highcheckShow" id="leak_filter" style="width: 80px"
                  ><img
                    src="../../assets/images/filter.png"
                    alt=""
                    style="width: 16px; vertical-align: middle; margin-right: 3px"
                  />高级筛选</span
                >
              </div>
              <div>
                <el-checkbox
                  class="checkboxAll"
                  v-model="checkedAll"
                  @change="checkAllChange"
                  id="leak_all"
                  >选择全部</el-checkbox
                >
                <!-- <el-button class="normalBtn" type="primary" @click="leakTb">漏洞通报</el-button> -->
                <el-button
                  :disabled="!userIsOpen"
                  class="normalBtnRe"
                  type="primary"
                  @click="leakDel"
                  id="leak_del"
                  >删除</el-button
                >
                <el-button
                  :disabled="!userIsOpen"
                  class="normalBtnRe"
                  type="primary"
                  @click="exportList"
                  id="leak_export"
                  >导出</el-button
                >
                <el-button
                  :disabled="!userIsOpen"
                  class="normalBtn"
                  type="primary"
                  @click="leakCheck"
                  :loading="loadingCheck"
                  id="leak_repair"
                  >{{ loadingCheck ? loadingCheckProgress + '%' : '修复核查' }}</el-button
                >
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane
            label="已修复漏洞"
            name="second"
            v-if="!notifyFilterId || (notifyFilterId && activeName == 'second')"
          >
            <span slot="label">已修复漏洞</span>
            <div class="filterTab">
              <div>
                <el-input
                  v-model="formInline.keyword"
                  @keyup.enter.native="checkFuncList"
                  placeholder="请输入IP或漏洞名称进行搜索"
                  id="leak_keycheck"
                >
                  <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
                </el-input>
                <span v-if="userIsOpen" @click="highcheckShow" id="leak_filter" style="width: 80px"
                  ><img
                    src="../../assets/images/filter.png"
                    alt=""
                    style="width: 16px; vertical-align: middle; margin-right: 3px"
                  />高级筛选</span
                >
              </div>
              <div>
                <el-checkbox
                  class="checkboxAll"
                  v-model="checkedAll"
                  @change="checkAllChange"
                  id="leak_all"
                  >选择全部</el-checkbox
                >
                <el-button
                  :disabled="!userIsOpen"
                  class="normalBtnRe"
                  type="primary"
                  @click="leakDel"
                  id="leak_del"
                  >删除</el-button
                >
                <el-button
                  :disabled="!userIsOpen"
                  class="normalBtnRe"
                  type="primary"
                  @click="exportList"
                  id="leak_export"
                  >导出</el-button
                >
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="filterTab" v-if="$route.path == '/taskRepairLeakScan'">
          <div>
            <el-input
              v-model="formInline.keyword"
              @keyup.enter.native="checkFuncList"
              placeholder="请输入关键字检索"
              id="leak_keycheck"
            >
              <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            </el-input>
            <span v-if="userIsOpen" @click="highcheckShow" id="leak_filter" style="width: 80px"
              ><img
                src="../../assets/images/filter.png"
                alt=""
                style="width: 16px; vertical-align: middle; margin-right: 3px"
              />高级筛选</span
            >
          </div>
          <div>
            <el-checkbox
              class="checkboxAll"
              v-model="checkedAll"
              @change="checkAllChange"
              id="leak_all"
              >选择全部</el-checkbox
            >
            <el-button
              :disabled="!userIsOpen"
              class="normalBtnRe"
              type="primary"
              @click="exportList"
              id="leak_export"
              >导出</el-button
            >
          </div>
        </div>
        <!-- 高级筛选条件 -->
        <hightFilter
          id="hightFilter"
          :highTabShow="highTabShow"
          :highlist="highlist"
          :total="total"
          pageIcon="repair"
          @highcheck="highCheck"
        ></hightFilter>
        <div :class="hightFilterIsShow()">
          <el-table
            border
            :data="tableData"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            @selection-change="handleSelectionChange"
            @cell-mouse-enter="showTooltip"
            @cell-mouse-leave="hiddenTooltip"
            :default-expand-all="false"
            ref="eltable"
            height="100%"
            style="width: 100%"
          >
            <template slot="empty">
              <div class="emptyClass">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-kong"></use>
                </svg>
                <p>暂无数据</p>
              </div>
            </template>
            <el-table-column
              type="selection"
              align="center"
              :reserve-selection="true"
              :selectable="handleSelectable"
              width="55"
            >
            </el-table-column>
            <el-table-column
              v-for="item in tableHeaderIsShow"
              :key="item.id"
              :prop="item.name"
              align="left"
              :label="item.label"
              :min-width="item.minWidth"
              :fixed="item.fixed"
            >
              <template slot-scope="scope">
                <span
                  style="color: #2677ff; cursor: pointer"
                  v-if="item.name == 'common_title'"
                  @click="leakInfo(scope.row)"
                  >{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span
                >
                <span v-else-if="item.name == 'poc_scan_engine'">
                  {{ scope.row.poc_scan_engine ? 'Trascanner' : 'Goscanner' }}
                </span>
                <span v-else-if="item.name == 'is_poc_scan'">
                  {{ scope.row.is_poc_scan == 2 ? '版本漏洞' : 'PoC漏洞' }}
                </span>
                <span v-else-if="item.name == 'tags'">
                  {{ (scope.row.tags && scope.row.tags.join('/')) || '-' }}
                </span>
                <p v-else-if="item.name == 'level'">
                  <span v-if="scope.row[item.name] == 3" class="deepRedRadiusBorder">严重</span>
                  <span v-if="scope.row[item.name] == 2" class="redRadiusBorder">高危</span>
                  <span v-if="scope.row[item.name] == 1" class="originRadiusBorder">中危</span>
                  <span v-if="scope.row[item.name] == 0" class="yellowRadiusBorder">低危</span>
                </p>
                <span v-else-if="item.name == 'ip'">
                  {{ scope.row.poc_scan_engine == 3 ? scope.row.object_target : scope.row.ip }}
                </span>
                <span v-else-if="item.name == 'is_domain_scan'">
                  {{ scope.row.is_domain_scan && scope.row.is_domain_scan == 1 ? '域名信息漏洞扫描' : scope.row.is_domain_scan && scope.row.is_domain_scan == 2 ? 'IP信息漏洞扫描' : '-' }}
                </span>
                <span v-else-if="scope.row[item.name]" v-html="scope.row[item.name]"></span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column v-if="activeName == 'first'" label="操作" align="left" min-width="80">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.has_exp"
                  type="text"
                  size="small"
                  @click="leakYz(scope.row)"
                  id="leak_verify"
                  >漏洞验证</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="pageSizeArr"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="IP地址段：" prop="ip">
            <el-input
              v-model="formInline.ip"
              placeholder="例如：**********-100或者**********/24"
            ></el-input>
          </el-form-item>
          <el-form-item label="CVE编号：" prop="cve_id">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.cve_id"
              @change="selectChange($event, 'cve_id', condition.cve_id, true, true)"
              placeholder="请选择CVE编号"
            >
              <el-option
                v-for="item in condition.cve_id"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="漏洞来源：" prop="is_domain_scan">
            <el-select
              filterable
              clearable
              v-model="formInline.is_domain_scan"
              @change="selectChange($event, 'is_domain_scan', selectData.is_domain_scan_arr, true, false)"
              placeholder="请选择漏洞来源"
            >
              <el-option
                v-for="item in selectData.is_domain_scan_arr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="企业名称：" prop="clue_company_name">
            <el-select
              filterable
              collapse-tags
              clearable
              v-model="formInline.clue_company_name"
              @change="
                selectChange($event, 'clue_company_name', condition.clue_company_name, false, false)
              "
              placeholder="请选择企业名称"
            >
              <el-option
                v-for="item in condition.clue_company_name"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="标签：" prop="tags">
            <el-select
              filterable
              collapse-tags
              clearable
              v-model="formInline.tags"
              @change="selectChange($event, 'tags', condition.tags, false, false)"
              placeholder="请选择标签"
            >
              <el-option
                v-for="item in condition.tags"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="漏洞等级：" prop="level">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.level"
              @change="selectChange($event, 'level', selectData.level_arr, true, true)"
              placeholder="请选择等级"
            >
              <el-option
                v-for="item in selectData.level_arr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="漏洞分类：" prop="level">
            <el-select
              clearable
              v-model="formInline.is_poc_scan"
              @change="selectChange($event, 'is_poc_scan', isPocType, true, false)"
              placeholder="请选择等级"
            >
              <el-option
                v-for="item in isPocType"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- 已修复不需要此项 -->
          <el-form-item v-if="activeName == 'first'" label="漏洞验证：" prop="has_exp">
            <el-select
              filterable
              v-if="activeName == 'first'"
              clearable
              v-model="formInline.has_exp"
              @change="selectChange($event, 'has_exp', selectData.reg_arr, true, false)"
              placeholder="请选择漏洞验证"
            >
              <el-option
                v-for="item in selectData.reg_arr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="漏洞类型：" prop="vul_type">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.vul_type"
              @change="selectChange($event, 'vul_type', condition.vul_type, false, true)"
              placeholder="请选择漏洞类型"
            >
              <el-option
                v-for="(item, index) in condition.vul_type"
                :key="index"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="漏洞地址：" prop="url">
            <el-input v-model="formInline.url" placeholder="请输入漏洞地址"></el-input>
          </el-form-item>
          <el-form-item label="上次扫描：" prop="last_update_time">
            <el-date-picker
              v-model="formInline.last_update_time"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="发现时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="leak_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="leak_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="520px"
    >
      <template slot="title">
        <el-tooltip
          effect="light"
          class="item"
          placement="top"
          content="一键复制"
          :open-delay="500"
        >
          <div slot="content">
            漏洞验证的时候，如果存在验证参数是下拉框选择的时候，可以选择其中一个下拉框的值进行漏洞验证（不可以选择多个）。
          </div>
          <span>
            漏洞验证
            <i class="el-icon-question"></i>
          </span>
        </el-tooltip>
      </template>
      <div class="dialog-body">
        <el-form
          style="padding: 0 !important"
          :model="ruleGroupForm"
          :rules="rulesGroup"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
          @close="closeDialog"
        >
          <el-form-item label="漏洞名称" prop="port">
            <p v-html="ruleGroupForm.common_title"></p>
          </el-form-item>
          <el-form-item label="漏洞地址" prop="port">
            <p v-html="ruleGroupForm.url"></p>
          </el-form-item>
          <span class="spideLine"></span>
          <div v-for="(item, index) in regProp" :key="index">
            <el-form-item :label="item.name" v-if="ifShowItem(item)">
              <el-input
                v-if="item.type == 'input'"
                v-model="cmdsForm[item.name]"
                placeholder=""
              ></el-input>
              <el-select
                v-if="item.type == 'select'"
                v-model="cmdsForm[item.name]"
                clearable
                collapse-tags
                placeholder=""
              >
                <el-option v-for="sel in item.selectArr" :key="sel" :value="sel"></el-option>
              </el-select>
              <el-select
                v-if="item.type == 'createSelect'"
                v-model="cmdsForm[item.name]"
                clearable
                collapse-tags
                placeholder=""
              >
                <el-option v-for="sel in item.selectArr" :key="sel" :value="sel"></el-option>
              </el-select>
            </el-form-item>
          </div>

          <el-form-item label="输出结果" prop="resultData">
            <el-input type="textarea" v-model="ruleGroupForm.resultData" placeholder=""></el-input>
            <!-- <p v-html="ruleGroupForm.resultData"></p> -->
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisible = false" id="leak_verify_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="insertSave" id="leak_verify_sure"
          >验证</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInfo"
      width="850px"
    >
      <template slot="title"> 漏洞详情 </template>
      <div class="dialog-body">
        <el-form
          :model="ruleFormInfo"
          ref="ruleFormInfo"
          label-width="70px"
          class="demo-ruleForm"
          style="padding: 0 !important"
        >
          <el-form-item label="漏洞名称" prop="port">
            <p v-html="ruleFormInfo.common_title"></p>
          </el-form-item>
          <el-form-item label="漏洞等级" prop="port">
            <span v-if="ruleFormInfo.level == 3" class="deepRedRadiusBorder">严重</span>
            <span v-if="ruleFormInfo.level == 2" class="redRadiusBorder">高危</span>
            <span v-if="ruleFormInfo.level == 1" class="originRadiusBorder">中危</span>
            <span v-if="ruleFormInfo.level == 0" class="yellowRadiusBorder">低危</span>
          </el-form-item>
          <el-form-item label="CVE编号" prop="port">
            <p v-html="ruleFormInfo.cve_id"></p>
          </el-form-item>
          <el-form-item label="漏洞响应" prop="last_response">
            <p style="height: 100px; overflow: auto" v-html="ruleFormInfo.last_response"></p>
          </el-form-item>
          <el-form-item label="漏洞描述" prop="port">
            <p v-html="ruleFormInfo.common_description"></p>
          </el-form-item>
          <el-form-item label="漏洞危害" prop="port">
            <p v-html="ruleFormInfo.common_impact"></p>
          </el-form-item>
          <el-form-item label="解决方案" prop="port">
            <p v-html="ruleFormInfo.recommendation"></p>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleInfo = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { getVulnerabilityDetails } from '@/api/apiConfig/api.js'
import {
  getPocTaskResults,
  delPocs,
  exportPocTaskResult,
  leakcheck,
  pocCheckDetail,
  countPocTypesNum,
  pocCheck
} from '@/api/apiConfig/poc.js'

export default {
  components: { tableTooltip, hightFilter },

  data() {
    return {
      cmdsForm: {},
      notifyFilterId: '',
      notifyFilterMsg: '',
      isPocType: [
        { id: '2', name: '版本漏洞' },
        { id: '1', name: 'Poc漏洞' }
      ],
      poc_scan_typeMap: {
        0: '全部PoC',
        1: '指定PoC范围',
        2: '指定PoC分组',
        3: '暴力破解',
        4: '系统扫描',
        5: 'web扫描',
        6: '默认'
      },
      scanTypeMap: {
        0: '深度扫描',
        1: '极速扫描'
        // 2: 'fofa联动扫描'
      },
      highTabShow: [
        {
          label: 'IP地址段',
          name: 'ip',
          type: 'input'
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          type: 'select'
        },
        {
          label: '标签',
          name: 'tags',
          type: 'select'
        },
        {
          label: 'CVE编号',
          name: 'cve_id',
          type: 'select'
        },
        {
          label: '漏洞来源',
          name: 'is_domain_scan',
          type: 'select'
        },
        {
          label: '漏洞等级',
          name: 'level',
          type: 'select'
        },
        {
          label: '漏洞分类',
          name: 'is_poc_scan',
          type: 'select'
        },
        {
          label: '漏洞验证',
          name: 'has_exp',
          type: 'select'
        },
        {
          label: '漏洞类型',
          name: 'vul_type',
          type: 'select'
        },
        {
          label: '漏洞地址',
          name: 'url',
          type: 'input'
        },
        {
          label: '上次扫描',
          name: 'last_update_time',
          type: 'date'
        },
        {
          label: '发现时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      condition: {},
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      btnLoading: false,
      dialogFormVisible: false,
      dialogFormVisibleInfo: false,
      checkedAll: false, // 是否全选
      formInline: {
        level: [], // 漏洞等级：0 是低危，1 是中危，2 是高危，3 是严重
        state: '1', // 漏洞状态：1(默认)未修复，2经核查已修复，3已修复，4经核查未修复
        created_at_range: [],
        last_update_time: [],
        name: '',
        has_exp: '', // 1 支持验证 0 不支持
        vul_type: [],
        ip: '',
        is_domain_scan: '',
        url: '',
        keyword: '',
        page: 1,
        per_page: 10
      },
      formInlineGroup: {
        keyword: '',
        page: 1,
        size: 10
      },
      regProp: [
        {
          name: '',
          value: ''
        }
      ],
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      currentPage: 1,
      transferData: [],
      group_portlist: [],
      selectData: {
        level_arr: [
          {
            name: '严重',
            id: 3,
            prop: 'critical',
            num: ''
          },
          {
            name: '高危',
            id: 2,
            prop: 'high',
            num: ''
          },
          {
            name: '中危',
            id: 1,
            prop: 'medium',
            num: ''
          },
          {
            name: '低危',
            id: 0,
            prop: 'low',
            num: ''
          }
        ],
        reg_arr: [
          {
            name: '支持',
            id: 1
          },
          {
            name: '不支持',
            id: 0
          }
        ],
        is_domain_scan_arr: [
          {
            name: '域名信息漏洞扫描',
            id: 1
          },
          {
            name: 'IP信息漏洞扫描',
            id: 2
          }
        ]
      },
      transferProp: {
        key: 'id',
        label: 'protocol'
      },
      transferPropGroup: {
        key: 'id',
        label: 'port'
      },
      loading: false,
      loadingCheck: false,
      loadingCheckProgress: '0',
      filterMethod(query, item) {
        return item.protocol.indexOf(query) > -1
      },
      filterMethodGroup(query, item) {
        return item.port.indexOf(query) > -1
      },
      rules: {
        port: [{ required: true, message: '请输入端口', trigger: 'change' }],
        protocols: [{ required: true, message: '请选择协议', trigger: 'change' }]
      },
      rulesGroup: {
        name: [{ required: true, message: '请输入端口分组名称', trigger: 'change' }],
        ports: [{ required: true, message: '请选择端口', trigger: 'change' }]
      },
      ruleForm: {
        port: '',
        protocols: [],
        groups: ''
      },
      ruleGroupForm: {
        ports: [],
        name: ''
      },
      ruleFormInfo: {},
      portGroupsNoPageArr: [],
      checkedArr: [],
      highCheckdialog: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      activeName: 'first',
      addIsTrue: true, // 判断新增或编辑
      tableData: [],
      tableHeader: {
        first: [
          {
            label: 'IP/URL 地址',
            name: 'ip',
            minWidth: 120,
            fixed: 'left'
          },
          {
            label: '企业名称',
            name: 'clue_company_name',
            minWidth: 120
          },
          {
            label: '漏洞名称',
            name: 'common_title',
            minWidth: 100
          },
          {
            label: '扫描引擎',
            name: 'poc_scan_engine',
            minWidth: 100,
            path: ['/repairLeakScan']
          },
          {
            label: '漏洞分类',
            name: 'is_poc_scan',
            minWidth: 100
          },
          {
            label: 'CVE编号',
            name: 'cve_id',
            minWidth: 85
          },
          {
            label: '漏洞来源',
            name: 'is_domain_scan',
            minWidth: 85,
            path: ['/repairLeakScan']
          },
          {
            label: '漏洞类型',
            name: 'vul_type',
            minWidth: 85
          },
          {
            label: '漏洞等级',
            name: 'level',
            minWidth: 85
          },
          {
            label: '漏洞地址',
            name: 'url',
            minWidth: 130
          },
          {
            label: '标签',
            name: 'tags',
            minWidth: 120
          },
          {
            label: '发现时间',
            name: 'created_at',
            minWidth: 100
          },
          {
            label: '上次扫描',
            name: 'last_update_time',
            minWidth: 100
          }
        ],
        second: [
          {
            label: 'IP/URL 地址',
            name: 'ip',
            minWidth: 120
          },
          {
            label: '企业名称',
            name: 'clue_company_name',
            minWidth: 120
          },
          {
            label: '漏洞名称',
            name: 'common_title',
            minWidth: 100
          },
          {
            label: '扫描引擎',
            name: 'poc_scan_engine',
            minWidth: 100,
            path: ['/repairLeakScan']
          },
          {
            label: '漏洞分类',
            name: 'is_poc_scan',
            minWidth: 100
          },
          {
            label: 'CVE编号',
            name: 'cve_id',
            minWidth: 85
          },
          {
            label: '漏洞来源',
            name: 'is_domain_scan',
            minWidth: 85,
            path: ['/repairLeakScan']
          },
          {
            label: '漏洞类型',
            name: 'vul_type',
            minWidth: 85
          },
          {
            label: '漏洞等级',
            name: 'level',
            minWidth: 85
          },
          {
            label: '漏洞地址',
            name: 'url',
            minWidth: 130
          },
          {
            label: '标签',
            name: 'tags',
            minWidth: 120
          },
          {
            label: '发现时间',
            name: 'created_at',
            minWidth: 100
          },
          {
            label: '上次扫描',
            name: 'last_update_time',
            minWidth: 100
          }
        ]
      },
      user: {
        role: ''
      },
      currentId: '',
      parameterList: [
        //任务参数
        {
          label: '任务名称',
          name: 'name'
        },
        {
          label: '扫描引擎',
          name: 'scan_engine'
        },
        {
          label: '扫描场景',
          name: 'poc_scan_type'
        },
        {
          label: '任务计划',
          name: 'type'
        },
        // {
        //   label: '扫描带宽',
        //   name: 'bandwidth'
        // },
        // {
        //   label: '扫描类型',
        //   name: 'scan_type'
        // },
        {
          label: '开始时间',
          name: 'start_at'
        },
        {
          label: '结束时间',
          name: 'end_at'
        },
        {
          label: '任务耗时',
          name: 'use_seconds'
        },
        {
          label: '发起人',
          name: 'op'
        },
        {
          label: '扫描目标',
          name: 'poc_scan_range_arr'
        },
        {
          label: 'PoC范围',
          name: 'poc_range'
        }
      ],
      ips: '',
      ipsArrNum: 0,
      ipsArr: [],
      poc: '',
      pocNum: 0,
      pocArr: [],
      userIsOpen: true // 权限控制
    }
  },
  watch: {
    $route(val) {},
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    getterCompanyChange(val) {
      // 安服账号切换企业需要回到一级页面
      if (this.$route.query.id) {
        // 查看列表过来的
        this.$router.go(-1)
      }
      if (this.notifyFilterId) {
        this.clearNotifyFilter()
      }
    },
    getterCurrentCompany(val) {
      this.loadingCheck = false // 切换企业，修复核查进度重置
      this.loadingCheckProgress = ''
      setTimeout(() => {
        // 权限控制
        if (sessionStorage.getItem('companyInfo')) {
          let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
          this.userIsOpen = companyInfo.limit_poc_scan == 0 ? false : true
        } else {
          this.userIsOpen = true
        }
      }, 500)
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      // 核查进度展示：核查没完成，切换页面再次展示进度，防止重复操作
      setTimeout(() => {
        if (sessionStorage.getItem('checkProgress' + this.currentCompanyId)) {
          if (
            JSON.parse(sessionStorage.getItem('checkProgress' + this.currentCompanyId)).id ==
            this.currentCompanyId
          ) {
            if (JSON.parse(sessionStorage.getItem('checkProgress' + this.currentCompanyId)).value) {
              this.loadingCheck = true
              this.loadingCheckProgress = JSON.parse(
                sessionStorage.getItem('checkProgress' + this.currentCompanyId)
              ).value
            }
          }
        }
      }, 510)
      this.getTaskResultData()
      this.getCountPocTypesNum()
      if (this.$route.path == '/taskRepairLeakScan') {
        this.getDetails(this.$route.query.id)
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange', 'currentCompanyId']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage', 'getterCompanyChange']),
    tableHeaderIsShow() {
      let arr = []
      arr = this.tableHeader[this.activeName].filter((item, index) => {
        return !item.path || (item.path && item.path.indexOf(this.$route.path) != -1)
      })
      return arr
    }
  },
  beforeDestroy() {
    sessionStorage.setItem('activeTabName', '')
  },
  created() {
    if (this.$route.query.vulnerability && this.$route.query.vulnerability !== '-') {
      this.formInline.keyword = this.$route.query.vulnerability
    }
    if (this.$route.query.url) {
      this.formInline.url = this.$route.query.url
      this.highlist = Object.assign({}, this.formInline)
      this.hightFilterIsShow()
    }
    if (this.$route.query.createdTimeRange) {
      let createdTimeRange = this.$route.query.createdTimeRange
      this.formInline.created_at_range = createdTimeRange
      this.highlist = Object.assign({}, this.formInline)
      this.hightFilterIsShow()
    }
    this.notifyFilterId = this.$route.query.notifyFilterId
    this.notifyFilterMsg = this.$route.query.notifyFilterMsg
    this.formInline.website_message_id = this.notifyFilterId || ''
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    this.activeName = this.$route.query.notifyFilterTab || 'first'
    if (sessionStorage.getItem('companyInfo')) {
      // 权限控制
      let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      this.userIsOpen = companyInfo.limit_poc_scan == 0 ? false : true
    } else {
      this.userIsOpen = true
    }
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    if (this.$route.query.flag == '1') {
      // 代表是核查弹窗跳转过来的，直接到已修复列表
      this.activeName = 'second'
    } else {
      this.activeName = 'first'
    }
    if (this.activeName == 'first') {
      this.formInline.state = '1'
    } else if (this.activeName == 'second') {
      this.formInline.state = '3'
    }

    if (this.user.role == 2) {
      // 安服账号切换企业需要回到一级页面
      if (!this.currentCompany) return
      this.getTaskResultData()
      this.getCountPocTypesNum()
      if (this.$route.path == '/taskRepairLeakScan') {
        this.getDetails(this.$route.query.id)
      }
    } else {
      this.getTaskResultData()
      this.getCountPocTypesNum()
      if (this.$route.path == '/taskRepairLeakScan') {
        this.getDetails(this.$route.query.id)
      }
    }
    // 核查进度展示：核查没完成，切换页面再次展示进度，防止重复操作
    if (sessionStorage.getItem('checkProgress' + this.currentCompanyId)) {
      if (
        JSON.parse(sessionStorage.getItem('checkProgress' + this.currentCompanyId)).id ==
        this.currentCompanyId
      ) {
        if (JSON.parse(sessionStorage.getItem('checkProgress' + this.currentCompanyId)).value) {
          this.loadingCheck = true
          this.loadingCheckProgress = JSON.parse(
            sessionStorage.getItem('checkProgress' + this.currentCompanyId)
          ).value
        }
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 从详情等页面回来不清空搜索条件，其他的都要清掉
      if (!to.query.fanhui) {
        sessionStorage.setItem('queryParam', '')
        sessionStorage.setItem('currentPageMy', 1)
      }
    })
  },
  async mounted() {
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
    // this.getDetails(this.$route.query.id)
  },

  methods: {
    ...mapMutations(['changeMenuId']),
    goBackFromBrief() {
      sessionStorage.setItem('menuId', '9')
      this.changeMenuId('9')
      this.$router.go(-1)
    },
    clearNotifyFilter() {
      this.$router.replace({ path: '/repairLeakScan', query: {} })
    },
    getScan(data) {
      if (data == 0) {
        return '根据ip段扫描'
      } else if (data == 7) {
        return '上传URL信息文件'
      } else {
        return '已认领资产'
      }
    },
    getPoc(data) {
      if (data == 0) {
        return '全部PoC'
      } else if (data == 1) {
        return '指定PoC范围'
      } else if (data == 2) {
        return '指定PoC分组'
      } else if (data == 3) {
        return '暴力破解'
      }
    },
    async getDetails(id) {
      let data = {
        id: id,
        operate_company_id: this.currentCompany
      }
      let res = await getVulnerabilityDetails(data)
      let ips1 = []
      if (res.data.is_domain_scan == 1) {
        this.tableHeader.first[0].label = 'URL地址'
      } else {
        this.tableHeader.first[0].label = 'IP地址'
      }
      if (res.data.ips.length > 0) {
        ips1 = res.data.ips.map((item) => {
          return item.ip
        })
        this.ips = res.data.ips.map((item) => {
          return item.ip
        })
        this.ipsArrNum = res.data.ips.length
        if (res.data.ips.length > 3) {
          for (var i = 0; i < 3; i++) {
            this.ipsArr.push(ips1[i])
          }
        } else {
          this.ipsArr = ips1
        }
      }
      let poc = []
      if (res.data.poc_scan_type == 1) {
        poc = res.data.poc.map((item) => {
          return item.name
        })
      } else if (res.data.poc_scan_type == 2) {
        poc = res.data.poc_group.map((item) => {
          return item.name
        })
      } else {
        poc = []
      }
      this.poc = poc
      this.pocNum = poc.length
      if (poc.length > 3) {
        for (var i = 0; i < 3; i++) {
          this.pocArr.push(poc[i])
        }
      } else {
        this.pocArr = poc
      }
      if (res.code == 0) {
        this.parameterList = [
          //任务参数
          {
            label: '任务名称',
            name: res.data.name ? res.data.name : '-'
          },
          {
            label: '扫描引擎',
            name: res.data.scan_engine
          },
          {
            label: '扫描场景',
            name: res.data.poc_scan_type
          },
          {
            label: '任务计划',
            name: res.data.type == 1 ? '周期任务' : '立即执行'
          },
          // {
          //   label: '扫描带宽',
          //   name: res.data.bandwidth
          // },
          // {
          //   label: '扫描类型',
          //   name: res.data.scan_type
          // },
          {
            label: '开始时间',
            name: res.data.start_at
          },
          {
            label: '结束时间',
            name: res.data.end_at
          },
          {
            label: '任务耗时',
            name: res.data.use_seconds
          },
          {
            label: '发起人',
            name: res.data.op ? res.data.op.name : ''
          },
          {
            label: '扫描目标',
            name: res.data.scan_range
          },
          {
            label: 'PoC范围',
            name: res.data.poc_scan_type
          }
        ]
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getTaskResultData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (this.$route.path != '/taskRepairLeakScan') {
        // 漏洞修复
        if (
          document.getElementById('hightFilter') &&
          document.getElementById('hightFilter').offsetHeight > 0
        ) {
          bol = 'tableWrap tableWrapFilter'
        } else {
          bol = 'tableWrap'
        }
      } else {
        // 漏洞任务详情
        if (
          document.getElementById('hightFilter') &&
          document.getElementById('hightFilter').offsetHeight > 0
        ) {
          bol = 'tableWrap taskTablewrapFilter'
        } else {
          bol = 'tableWrap taskTablewrap'
        }
      }
      return bol
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    handleMessage(res) {
      // if (this.currentId == res.data.task_id) return // 相同的id,提示一次成功
      if (
        res.cmd == 'check_poc_scan_task_progress' &&
        sessionStorage.getItem('leakCheckFlag') &&
        sessionStorage.getItem('leakCheckFlag') == res.data.task_id
      ) {
        // 修复核查
        this.runningData = res.data
        if (res.data.status == 2) {
          // this.currentId = res.data.task_id
          this.loadingCheck = false // 修复完成
          this.loadingCheckProgress = ''
          // 核查成功后刷新列表
          this.getTaskResultData()
          this.getCountPocTypesNum()
        } else if (res.data.status == 1) {
          this.loadingCheck = true
          this.loadingCheckProgress = res.data.progress
        } else {
          this.loadingCheck = false // 修复完成
          this.loadingCheckProgress = ''
        }
      }
    },
    async getCountPocTypesNum(sleep) {
      // sleep有值代表删除或导入成功后去请求列表，需要延时
      let res = await countPocTypesNum({
        id: this.$route.query.id,
        is_sleep: sleep,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.selectData.level_arr.forEach((item) => {
          item.num = 0
        })
      })
      if (res && res.code == 0) {
        let obj = {
          first: res.data.unrepaired,
          second: res.data.repaired
        }
        this.selectData.level_arr.forEach((item) => {
          item.num = obj[this.activeName][item.prop]
        })
      }
    },
    async leakInfo(row) {
      this.dialogFormVisibleInfo = true
      this.ruleFormInfo = row
      this.ruleFormInfo.last_response = row.last_response
        .replace(/\ +/g, '')
        .replace(/[\r\n]/g, '<br/>')
    },
    leakYz(row) {
      this.ruleGroupForm.resultData = ''
      this.dialogFormVisible = true
      this.ruleGroupForm = row
      this.getRegProp(row.id)
    },
    // 漏洞验证获取需要展示的字段
    async getRegProp(id) {
      let res = await pocCheckDetail({ id: id, operate_company_id: this.currentCompany })
      res.data.exp_params.forEach((item) => {
        if (item.type == 'input') {
          this.$set(this.cmdsForm, item.name, item.value)
        } else if (item.type == 'select' || item.type == 'createSelect') {
          // 漏洞验证要根据type渲染
          item['selectArr'] = item.value.split(',')
          this.$set(this.cmdsForm, item.name, item.value.split(',')[0])
        }
      })
      this.regProp = res.data.exp_params
    },
    ifShowItem(item) {
      if (item.show) {
        let show = item.show && item.show.split('=')
        return this.cmdsForm[show[0]] == show[1]
      } else {
        return true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.cmdsForm = {}
      this.regProp = []
    },
    async insertSave() {
      let obj = {
        id: this.ruleGroupForm.id,
        url: this.ruleGroupForm.url,
        cmds: this.cmdsForm,
        operate_company_id: this.currentCompany
      }
      this.btnLoading = true
      let res = await pocCheck(obj).catch(() => {
        this.btnLoading = false
      })
      this.btnLoading = false
      if (res.code == 0) {
        this.$message.success('验证成功！')
        this.$set(this.ruleGroupForm, 'resultData', res.data)
      }
    },
    async getTaskResultData(sleep, tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.highCheckdialog = false
      this.tableData = []
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.is_sleep = sleep // sleep有值代表删除或导入成功后去请求列表，需要延时
      this.formInline.task_id = this.$route.query.id // 已完成查看列表
      this.loading = true
      let res = await getPocTaskResults(this.formInline).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      if (res && res.code == 0) {
        this.loading = false
        this.total = res.data ? res.data.total : 0
        this.tableData = res.data.items
        this.condition = res.data.condition
        // 全选操作
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
          })
        }
        if ((!this.total || this.total == 0) && this.notifyFilterId) {
          this.$message.error('该批未修复漏洞已被修复或已被删除')
        }
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    // ------------------------------------------全选操作---结束---------------------
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getTaskResultData('', true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTaskResultData('', true)
    },
    resetForm() {
      this.formInline = {
        level: [], // 漏洞等级：0 是低危，1 是中危，2 是高危，3 是严重
        state: this.activeName == 'first' ? 1 : 3, // 漏洞状态：1(默认)未修复，2经核查已修复，3已修复，4经核查未修复
        created_at_range: [],
        last_update_time: [],
        name: '',
        has_exp: '', // 1 支持验证 0 不支持
        vul_type: [],
        url: '',
        ip: '',
        is_domain_scan: '',
        page: 1,
        per_page: 10,
        size: 10
      }
    },
    handleClick() {
      sessionStorage.setItem('activeTabName', this.activeName)
      this.currentPage = 1
      this.formInline = {
        level: [], // 漏洞等级：0 是低危，1 是中危，2 是高危，3 是严重
        state: '1', // 漏洞状态：1(默认)未修复，2经核查已修复，3已修复，4经核查未修复
        created_at_range: [],
        last_update_time: [],
        name: '',
        has_exp: '', // 1 支持验证 0 不支持
        url: '',
        vul_type: [],
        ip: '',
        is_domain_scan: '',
        keyword: '',
        page: 1,
        per_page: 10
      }
      this.highlist = Object.assign({}, this.formInline)
      this.hightFilterIsShow()
      this.$nextTick(() => {
        this.$refs.eltable.clearSelection()
      })
      if (this.activeName == 'first') {
        this.formInline.state = '1'
      } else if (this.activeName == 'second') {
        this.formInline.state = '3'
      }
      this.checkedAll = false
      this.getCountPocTypesNum()
      this.getTaskResultData()
    },
    viewList(id) {
      this.$router.push({
        path: '/alreadyTask_viewlist',
        query: {
          id: id,
          task_type: this.task_type
        }
      })
    },
    async highcheckShow() {
      this.highCheckdialog = true
      // // 获取POC类型
      // let vulTypeList = await pocTypeList()
      // this.selectData.type_arr = Object.values(vulTypeList.data)
    },
    async exportList() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
      } else {
        let obj = {
          id: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          ...this.formInline,
          operate_company_id: this.currentCompany
        }
        let res = await exportPocTaskResult(obj)
        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.url)
          this.$refs.eltable.clearSelection()
        }
      }
    },
    // 漏洞通报
    leakTb() {},
    // 漏洞删除
    async leakDel() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
      } else {
        this.$confirm('确定删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'leak_del_cancel',
          confirmButtonClass: 'leak_del_sure',
          customClass: 'leak_del',
          type: 'warning'
        })
          .then(async () => {
            let res = await delPocs({
              id: this.checkedAll
                ? []
                : this.checkedArr.map((item) => {
                    return item.id
                  }),
              all: this.checkedAll ? 1 : '', // 全选操作：1
              operate_company_id: this.currentCompany,
              ...this.formInline
            })
            if (res.code == 0) {
              this.$message.success('操作成功！')
              this.currentPage = this.updateCurrenPage(
                this.total,
                this.checkedArr,
                this.currentPage,
                this.pageSize
              ) // 更新页码
              this.$nextTick(() => {
                this.$refs.eltable.clearSelection()
              })
              this.getCountPocTypesNum(1)
              this.getTaskResultData(1)
              // 删除完数据清空页面选中
              this.checkedAll = false
            }
          })
          .catch(() => {})
        setTimeout(() => {
          var del = document.querySelector('.leak_del>.el-message-box__btns')
          del.children[0].id = 'leak_del_cancel'
          del.children[1].id = 'leak_del_sure'
        }, 50)
      }
    },
    // 修复核查
    async leakCheck() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择核查数据！')
      } else {
        let res = await leakcheck({
          id: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          all: this.checkedAll ? 1 : '', // 全选操作：1
          operate_company_id: this.currentCompany,
          ...this.formInline
        })
        if (res.code == 0) {
          sessionStorage.setItem('leakCheckFlag', res.data)
          this.$message.success('核查任务下发成功！')
          this.loadingCheck = true
        }
      }
    },
    // 一键复制
    copyIp(val) {
      let ipText = val.join('\n')
      let that = this
      this.$copyText(ipText).then(
        function (e) {
          that.$message.success('ip已复制到剪贴板！')
        },
        function (e) {}
      )
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  // background: #fff;
  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;
    & > .el-form {
      width: 100%;
      .el-form-item__content {
        span {
          color: #37393c;
        }
        & > p {
          color: #37393c;
          // line-height: 27px;
        }
      }
    }

    /deep/.el-textarea {
      width: 100%;
      height: 200px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d1d5dd;
      .el-textarea__inner {
        height: 100% !important;
        border: 0 !important;
      }
      .el-textarea__inner:hover {
        border: 0;
      }
    }
  }
  .result {
    display: flex;
    justify-content: space-between;
    height: 100%;
    width: 100%;
    flex-direction: row-reverse;
    // background-color: #f1f3f8;
    // background-color: #fff;
  }
  /deep/.leftParameter {
    width: 20%;
    height: 100%;
    overflow: auto;
    background: #ffffff linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #f3f7ff 100%);
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    .parameterTitle {
      height: 52px;
      line-height: 52px;
      // padding: 0px 16px;
      margin: 0px 12px 16px;
      border-bottom: 1px solid #e9ebef;
      font-weight: 500;
      color: #37393c;
    }
    .parameterBox {
      padding: 0px 16px;
      // border-bottom: 1px solid #e9ebef;
    }
    .parameterBox1 {
      // display: flex;
      // align-items: center;
      width: 100%;
      margin-bottom: 16px;
      // justify-content: space-between;
      // height: 52px;
    }
    .parameterBox2 {
      // display: flex;
      // justify-content: space-between;
      height: 52px;
      // align-items: center;
    }
    .parameterBoxContent1 {
      // text-align: right;
      margin-bottom: 10px;
      word-break: break-word;
    }
    .parameterBoxTitle {
      width: 100%;
      font-size: 14px;
      font-weight: 500;
      color: #37393c;
      margin-bottom: 4px;
    }
    .parameterBoxContent {
      width: 100%;
      color: #62666c;
      // width: calc(100% - 90px);
      // overflow: hidden;
      // text-align: right;
      white-space: normal;
      // text-overflow: ellipsis;
    }
    .parameterBox:last-child {
      border-bottom: none;
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    width: 79%;
    background-color: white;
    .el-tabs__nav.is-top {
      margin-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      padding: 0;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      & > span {
        margin-left: 0 !important;
      }
    }
    .el-tabs__active-bar {
      width: 100%;
      padding: 0;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      // padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
      & > span {
        display: inline-block;
        height: 100%;
        padding: 0 16px;
      }
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background-color: #fff;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 223px) !important;
    }
    .taskTablewrap {
      height: calc(100% - 177px) !important;
    }
    .taskTablewrapFilter {
      height: calc(100% - 229px) !important;
    }
    .tableWrap {
      height: calc(100% - 169px);
      padding: 0px 20px;
      .el-table__expanded-cell {
        padding: 0 0 !important;
        border-bottom: 0;
      }
      // & > .el-table .el-table__body td.el-table__cell {
      //   border-right: 0;
      // }
      .childTable {
        .el-table__body td.el-table__cell {
          background: #f5f9ff;
          border-right: 0;
        }
      }
    }
    .el-table {
      border: 0;
      td {
        border-right: transparent !important;
      }
    }
  }
}
.taskResults {
  // width: 100%;
  font-weight: 500;
  color: #37393c;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  margin: 12px 16px 0px 12px;
  padding-bottom: 16px;
  // box-sizing: border-box;
  border-bottom: 1px solid #e9ebef;
}
.statisticnum {
  margin-right: 12px;
  font-size: 14px;
  font-weight: 400;
  color: #62666c;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  padding: 4px 10px;
  border-left: 2px solid #2677ff;
  .num {
    font-weight: 500;
    color: #2677ff;
    margin-right: 0px;
  }
}
.statisticnumBox > .statisticnum:last-child {
  margin-right: 0px;
}
.headerTitle {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.copy {
  margin-left: 10px;
  cursor: pointer;
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  text-align: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
