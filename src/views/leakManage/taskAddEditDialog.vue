<template>
  <el-dialog
    class="elDialogAdd"
    @opened="open"
    :close-on-click-modal="false"
    :visible.sync="dialogFormVisible"
    :before-close="dialogFormVisibleClose"
    :width="
      (ruleForm.scan_range != 2 && ruleForm.scan_range != 4 && ruleForm.scan_range != 5) ||
      (ruleForm.poc_scan_type && (ruleForm.poc_scan_type == 1 || ruleForm.poc_scan_type == 2)) ||
      !ruleForm.port_group_ids
        ? '950px'
        : '585px'
    "
  >
    <template slot="title">
      {{ ruleForm.id ? '编辑任务' : '添加任务' }}
    </template>
    <div class="dialog-body" v-loading="loading">
      <!-- 新建任务左侧 -->
      <div class="left">
        <el-form
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="addRuleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="任务名称" prop="name">
            <el-input v-model="ruleForm.name" placeholder="请输入任务名称"></el-input>
          </el-form-item>
          <el-form-item v-if="task_type == 1" label="IP类型" prop="ip_type">
            <el-select v-model="ruleForm.ip_type" @change="iptype_change" placeholder="请选择">
              <el-option label="IPV4" :value="1"></el-option>
              <el-option label="IPV6" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="task_type == 2" label="引擎选择" prop="scan_range">
            <span slot="label"
              >引擎选择
              <el-tooltip
                class="item"
                effect="dark"
                placement="bottom"
                popper-class="chainClass"
                :open-delay="500"
              >
                <div slot="content" class="userInfo">
                  <p>Goscanner：默认利用系统管理中的高价值PoC进行扫描</p>
                  <p v-if="user.role == 2 && userInfo.is_show_transanner == '1'"
                    >Trascanner：调用云端传统漏扫进行扫描，支持系统漏扫和web漏扫
                  </p>
                  <p v-if="user.role == 2 && userInfo.is_show_transanner == '1'"
                    >二者全部勾选，默认选择系统漏扫进行扫描</p
                  >
                </div>
                <i class="el-icon-question" style="color: #2677ff"></i>
              </el-tooltip>
            </span>
            <el-checkbox-group
              :disabled="Boolean(ruleForm.id)"
              v-model="engineVal"
              :min="1"
              @change="changeEngineVal"
            >
              <el-checkbox label="go">Goscanner</el-checkbox>
              <el-checkbox label="tra" v-if="user.role == 2 && userInfo.is_show_transanner == '1'"
                >Trascanner</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item v-if="task_type == 2" label="场景选择" prop="poc_scan_type">
            <el-select
              filterable
              v-model="ruleForm.poc_scan_type"
              @change="poc_scan_range_change"
              placeholder="请选择"
            >
              <el-option
                v-for="item in selectData.poc_range"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="扫描目标" prop="scan_range">
            <el-select
              v-if="task_type == 1"
              v-model="ruleForm.scan_range"
              @change="scan_range_change"
              placeholder="请选择"
              :disabled="isdisabled"
            >
              <el-option
                v-for="item in selectData.scan_range_arr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                @click.native="changeScanRange(item.id)"
              ></el-option>
            </el-select>
            <el-select
              v-if="task_type == 2"
              v-model="ruleForm.scan_range"
              @change="scan_range_change"
              placeholder="请选择"
            >
              <el-option
                v-for="item in selectData.poc_scan_range_arr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="IP获取方式"
            v-if="(JSON.parse(isType) || zhouqiIs) && task_type == 1 && ruleForm.scan_range == 4"
            prop="table_assets_type"
          >
            <span slot="label"
              >IP获取方式
              <el-tooltip
                class="item"
                effect="dark"
                placement="bottom"
                popper-class="chainClass"
                :open-delay="500"
              >
                <div slot="content" class="userInfo">
                  <p>固定模式：使用创建任务时的台账IP</p>
                  <p>动态模式：每次执行时实时获取最新台账IP</p>
                </div>
                <i class="el-icon-question" style="color: #2677ff"></i>
              </el-tooltip>
            </span>
            <el-radio-group v-model="ruleForm.table_assets_type">
              <el-radio :label="0">固定模式</el-radio>
              <el-radio :label="1">动态模式</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="task_type == 1" label="扫描端口" prop="port_group_ids">
            <el-select
              filterable
              v-model="ruleForm.port_group_ids"
              placeholder="请选择"
              :disabled="isdisabled"
            >
              <el-option
                v-for="item in portGroupsNoPageArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                @click.native="changeProt(item.id)"
              ></el-option>
            </el-select>
            <div class="tip">(若要对资产执行漏洞扫描，请选择全端口扫描)</div>
          </el-form-item>

          <!-- <el-form-item v-if="task_type == 2" label="PoC范围" prop="poc_scan_type">
              <el-select filterable v-model="ruleForm.poc_scan_type" @change="poc_scan_range_change" placeholder="请选择">
                <el-option v-for="item in selectData.poc_range" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item> -->
          <el-form-item label="任务计划" v-if="editIs">
            <el-radio v-model="isType" label="false">立即执行</el-radio>
            <el-radio
              v-model="isType"
              label="true"
              v-if="isDomain"
              :disabled="ruleForm.scan_range == 1 && task_type != 2"
              :title="ruleForm.scan_range == 1 ? '周期任务暂不支持上传IP信息文件' : ''"
              >周期任务</el-radio
            >
          </el-form-item>
          <el-form-item v-if="JSON.parse(isType) || zhouqiIs" label="周期类型" prop="type">
            <el-select v-model="ruleForm.type" @change="scan_date_rang_change" placeholder="请选择">
              <el-option
                v-for="item in selectData.scan_date_range"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- {每天重复5 每周重复4 每月重复3 定时6 -->
          <el-form-item
            v-if="JSON.parse(isType) || zhouqiIs"
            class="time"
            label="执行时间"
            :prop="ruleForm.type == '5' ? '' : 'day_of_x'"
          >
            <el-col :span="11">
              <el-select
                v-model="ruleForm.day_of_x"
                placeholder="请选择日期"
                class="type"
                v-if="ruleForm.type == '5'"
                disabled
              >
              </el-select>
              <el-select
                v-model="ruleForm.day_of_x"
                placeholder="请选择日期"
                class="type"
                v-if="ruleForm.type == '4'"
              >
                <el-option
                  v-for="item in selectData.weekly"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-select
                v-show="ruleForm.type == '3'"
                v-model="ruleForm.day_of_x"
                placeholder="请选择日期"
                class="type"
              >
                <el-option
                  v-for="item in selectData.monthArr"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
              <el-date-picker
                v-model="ruleForm.day_of_x"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :clearable="false"
                :picker-options="pickerOptions"
                placeholder="选择日期"
                v-if="ruleForm.type == '6'"
                class="type"
              >
              </el-date-picker>
            </el-col>
            <el-col :span="2" style="text-align: center">-</el-col>
            <el-col :span="11">
              <el-time-picker
                :clearable="false"
                v-model="ruleForm.schedule_time"
                size="small"
                placeholder="请选择开始时间"
                format="HH:mm"
                value-format="HH:mm"
                class="time"
              ></el-time-picker>
            </el-col>
          </el-form-item>
          <el-form-item
            label="扫描带宽"
            prop="bandwidth"
            v-if="task_type == 1 && ruleForm.scan_type != 2"
          >
            <newInputNumber
              class="inputNumber"
              :min="100"
              :max="5000"
              :step="100"
              v-model.number="ruleForm.bandwidth"
              placeholder="大于100，小于5000 的整数"
            >
              <template slot="append">kb</template>
            </newInputNumber>
            <!-- <p class="tishi">带宽设置高可以提高扫描速度，但是过高可能会影响到网络正常使用</p> -->
          </el-form-item>
          <el-form-item
            v-if="task_type == 1 && ruleForm.scan_type != 2"
            label="识别并发"
            prop="protocol_concurrency"
          >
            <el-select filterable v-model="ruleForm.protocol_concurrency" placeholder="请选择">
              <el-option label="根据带宽动态分配协议识别并发数" :value="0" :key="0"></el-option>
              <el-option
                v-for="item in selectData.protocol_concurrency_arr"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="task_type == 1"
            label="扫描类型"
            prop="scan_type"
            style="margin-bottom: 0px !important"
          >
            <el-select v-model="ruleForm.scan_type" placeholder="请选择">
              <el-option
                v-for="item in selectData.scan_type_arr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <!-- <el-select v-if="task_type == 2" v-model="ruleForm.scan_type" placeholder="请选择">
              <el-option
                v-for="item in selectData.poc_scan_type_arr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select> -->
          </el-form-item>
          <el-form-item
            v-if="task_type == 1 && ruleForm.scan_type != 2"
            label=""
            prop="ping_switch"
            style="margin-bottom: 0px !important; margin-top: 12px"
          >
            <el-checkbox v-model="ruleForm.ping_switch">开启ping识别资产</el-checkbox>
          </el-form-item>
          <!-- <el-form-item v-if="task_type == 1" label="" prop="web_logo_switch">
              <el-checkbox v-model="ruleForm.web_logo_switch">开启网站首页截图</el-checkbox>
            </el-form-item> -->
        </el-form>
      </div>
      <!-- 新建任务右侧 -->
      <div
        class="right"
        v-if="
          ruleForm.scan_range == 0 ||
          ruleForm.scan_range == 1 ||
          ruleForm.scan_range == 3 ||
          ruleForm.scan_range == 7 ||
          ruleForm.scan_range == 8 ||
          ruleForm.poc_scan_type == 1 ||
          ruleForm.poc_scan_type == 2 ||
          ruleForm.scan_range == 6 ||
          !ruleForm.port_group_ids
        "
      >
        <div class="tabWrap">
          <!-- 漏洞扫描：扫描目标选择【选择台账资产、上传文件IP信息】，POC范围选择【指定POC范围、指定POC分组】 -->
          <el-tabs
            v-if="
              task_type == 2 &&
              (ruleForm.scan_range == 0 ||
                ruleForm.scan_range == 1 ||
                ruleForm.scan_range == 4 ||
                ruleForm.scan_range == 7 ||
                ruleForm.scan_range == 8 ||
                ruleForm.poc_scan_type == 1 ||
                ruleForm.poc_scan_type == 2)
            "
            v-model="activeName"
          >
            <!-- 选择台账资产 -->
            <el-tab-pane v-if="ruleForm.scan_range == 0" label="根据IP筛选" name="first">
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
              >
                <el-form-item label="" prop="">
                  <new-transfer
                    :titles="['未选择', '已选择']"
                    v-loading="pocdiaLoading"
                    filterable
                    :filter-method="filterMethodsureIp"
                    filter-placeholder="请输入关键字"
                    v-model="ruleForm.ips"
                    :props="transferPropsureIps"
                    ref="reserve1"
                    @left-check-change="() => handleChangeLeft(1)"
                    @right-check-change="() => handleChangeRight(1)"
                    :data="sureIpsData"
                  >
                  </new-transfer>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <!-- 指定POC范围、指定POC分组 -->
            <el-tab-pane
              v-if="ruleForm.poc_scan_type == 1 || ruleForm.poc_scan_type == 2"
              :label="ruleForm.poc_scan_type == 1 ? '指定PoC范围' : '指定PoC分组'"
              name="second"
            >
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
              >
                <el-form-item label="" prop="poc_ids">
                  <new-transfer
                    :titles="['未选择', '已选择']"
                    v-loading="pocdiaLoading"
                    filterable
                    :filter-method="filterMethod"
                    filter-placeholder="请输入关键字"
                    v-model="ruleForm.poc_ids"
                    :props="transferPropPoc"
                    ref="reserve2"
                    @left-check-change="() => handleChangeLeft(2)"
                    @right-check-change="() => handleChangeRight(2)"
                    :data="pocTransferData"
                  >
                    <span slot-scope="{ option }">
                      <span :title="option.name">{{ option.name }}</span>
                    </span>
                  </new-transfer>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <!-- 上传文件IP信息 -->
            <el-tab-pane v-if="ruleForm.scan_range == 1" label="上传ip信息文件" name="first">
              <el-form :model="ruleForm" ref="addRuleForm" label-width="10px" class="demo-ruleForm">
                <el-form-item label="" prop="">
                  <p
                    class="downloadClass"
                    style="margin-top: 10px !important"
                    @click="downloadForbidIpsExcelPoc"
                  >
                    <i class="el-icon-warning"></i>请点击下载
                    <span>漏洞扫描IP信息导入模板</span>
                  </p>
                  <el-upload
                    class="upload-demo"
                    drag
                    :action="uploadSrcIp + '/poc_assets/task/import_ip_file'"
                    :headers="uploadHeaders"
                    accept=".xlsx"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :on-remove="uploadRemove"
                    :show-file-list="true"
                    :before-upload="beforeUpload"
                    :limit="uploadMaxCount"
                    :on-exceed="handleExceed"
                    :file-list="fileList"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过3M</div>
                  </el-upload>
                  <div>
                    <div style="display: flex; justify-content: space-between">
                      <p style="width: 43%; color: #2677ff"
                        >已确权IP
                        <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                          <span slot="content">支持下发漏洞扫描</span>
                          <i class="el-icon-question"></i>
                        </el-tooltip>
                      </p>
                      <p style="width: 43%"
                        >未确权/未入账IP
                        <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                          <span slot="content">需要确权或扫描入账后再下发</span>
                          <i class="el-icon-question"></i>
                        </el-tooltip>
                      </p>
                    </div>
                    <div style="display: flex; justify-content: space-between">
                      <p
                        class="uploadIps"
                        style="width: 43%"
                        v-html="confirmedIp.length > 0 ? confirmedIp.join('\r') : ''"
                      ></p>
                      <p
                        class="uploadIps"
                        style="width: 43%"
                        v-html="noConfirmedIp.length > 0 ? noConfirmedIp.join('\r') : ''"
                      ></p>
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane v-if="ruleForm.scan_range == 8" label="上传域名信息文件" name="first">
              <el-form :model="ruleForm" ref="addRuleForm" label-width="10px" class="demo-ruleForm">
                <el-form-item label="" prop="">
                  <p
                    class="downloadClass"
                    style="margin-top: 10px !important"
                    @click="downloadForbidDomainExcelPoc"
                  >
                    <i class="el-icon-warning"></i>请点击下载
                    <span>漏洞扫描域名信息导入模板</span>
                  </p>
                  <el-upload
                    class="upload-demo"
                    drag
                    :action="uploadSrcIp + '/poc_assets/task/import_domain_file'"
                    :headers="uploadHeaders"
                    accept=".xlsx"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :on-remove="uploadRemove"
                    :show-file-list="true"
                    :before-upload="beforeUpload"
                    :limit="uploadMaxCount"
                    :on-exceed="handleExceed"
                    :file-list="fileList"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过3M</div>
                  </el-upload>
                  <div>
                    <div style="display: flex; justify-content: space-between">
                      <p style="width: 43%; color: #2677ff"
                        >已确权域名
                        <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                          <span slot="content">支持下发漏洞扫描</span>
                          <i class="el-icon-question"></i>
                        </el-tooltip>
                      </p>
                      <p style="width: 43%"
                        >未确权/未入账域名
                        <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                          <span slot="content">需要确权或扫描入账后再下发</span>
                          <i class="el-icon-question"></i>
                        </el-tooltip>
                      </p>
                    </div>
                    <div style="display: flex; justify-content: space-between">
                      <p
                        class="uploadIps"
                        style="width: 43%"
                        v-html="confirmedDomain.length > 0 ? confirmedDomain.join('\r') : ''"
                      ></p>
                      <p
                        class="uploadIps"
                        style="width: 43%"
                        v-html="noConfirmedDomain.length > 0 ? noConfirmedDomain.join('\r') : ''"
                      ></p>
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <!-- 上传URL信息文件 -->
            <el-tab-pane label="上传URL信息文件" name="second" v-if="ruleForm.scan_range == 7">
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
                style="margin-top: 10px"
              >
                <el-form-item label="" prop="">
                  <p class="downloadClass" @click="downloadForbidUrlsExcel">
                    <i class="el-icon-warning"></i>请点击下载
                    <span>URL信息导入模板</span>
                  </p>
                  <el-upload
                    class="upload-demo"
                    drag
                    :action="uploadSrcIp + '/domain_assets/upload'"
                    :headers="uploadHeaders"
                    accept=".xlsx"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :on-remove="uploadRemove"
                    :show-file-list="true"
                    :before-upload="beforeUpload"
                    :limit="uploadMaxCount"
                    :on-exceed="handleExceed"
                    :file-list="fileList"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过3M</div>
                  </el-upload>
                  <p style="color: #2677ff">上传的URL信息</p>
                  <p
                    class="uploadIps"
                    v-html="ruleForm.ips && ruleForm.ips.length > 0 ? ruleForm.ips.join('\n') : ''"
                  >
                  </p>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
          <!-- 资产扫描： 0 手动输入的ip 1上传的ip 3根据ip段扫描 6输入域名信息 -->
          <el-tabs
            v-else-if="
              task_type == 1 &&
              (ruleForm.scan_range == 0 ||
                ruleForm.scan_range == 1 ||
                ruleForm.scan_range == 3 ||
                ruleForm.scan_range == 6 ||
                !ruleForm.port_group_ids)
            "
            v-model="activeNameScan"
          >
            <!-- 0 手动输入的ip -->
            <el-tab-pane label="IP" name="first" v-if="ruleForm.scan_range == 0">
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
              >
                <el-form-item label="" prop="" style="position: relative; height: 442px">
                  <el-input
                    type="textarea"
                    v-model="ruleForm.ips"
                    class="placeholderIdBox"
                  ></el-input>
                  <div class="placeholderId" v-show="!ruleForm.ips">
                    <div></div>
                    <div>IP段支持格式如下</div>
                    <div>**********</div>
                    <div>**********-100</div>
                    <div>**********/24</div>
                    <div></div>
                    <!-- <div>多个连续网段支持格式：</div>
                    <div>192.168.1-10.*（代表***********-************共10个网段）</div> -->
                    <div>最多输入1000个ip，分号或换行分隔</div>
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <!-- 6输入域名信息 -->
            <el-tab-pane label="域名" name="first" v-else-if="ruleForm.scan_range == 6">
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
              >
                <el-form-item label="" prop="" style="position: relative; height: 442px">
                  <el-input
                    type="textarea"
                    v-model="ruleForm.ips"
                    class="placeholderIdBox"
                  ></el-input>
                  <div class="placeholderId" v-show="!ruleForm.ips" style="margin-top: 15px">
                    <div>支持填写域名</div>
                    <div></div>
                    <div>域名格式如下：</div>
                    <div>foradar.baimaohui.net</div>
                    <div>最多输入200个，分号或换行分隔</div>
                    <div
                      >IP类型选择IPV4的话，域名就只解析IPV4的ip数据，同样，选择IPV6类型，域名就只解析IPV6格式的ip数据。</div
                    >
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <!-- 3根据ip段扫描 -->
            <el-tab-pane label="根据IP筛选" name="first" v-else-if="ruleForm.scan_range == 3">
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
              >
                <el-form-item label="" prop="ips">
                  <new-
                    :titles="['未选择', '已选择']"
                    filterable
                    :filter-method="filterMethod"
                    filter-placeholder="请输入关键字"
                    v-model="ruleForm.ips"
                    :props="transferProp"
                    ref="reserve3"
                    @left-check-change="() => handleChangeLeft(3)"
                    @right-check-change="() => handleChangeRight(3)"
                    :data="transferData"
                  >
                  </new->
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <!-- 1上传的ip文件 -->
            <el-tab-pane label="上传ip信息文件" name="first" v-else-if="ruleForm.scan_range == 1">
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
                style="margin-top: 10px"
              >
                <el-form-item label="" prop="">
                  <p class="downloadClass" @click="downloadForbidIpsExcel">
                    <i class="el-icon-warning"></i>请点击下载
                    <span>IP信息导入模板</span>
                  </p>
                  <el-upload
                    class="upload-demo"
                    drag
                    :action="golangUploadSrcIp + '/assets/task/import_ip_file'"
                    :headers="uploadHeaders"
                    accept=".xlsx"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :on-remove="uploadRemove"
                    :show-file-list="true"
                    :before-upload="beforeUpload"
                    :limit="uploadMaxCount"
                    :on-exceed="handleExceed"
                    :file-list="fileList"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过3M</div>
                  </el-upload>
                  <p style="color: #2677ff">上传的IP信息</p>
                  <p
                    class="uploadIps"
                    v-html="ruleForm.ips.length > 0 ? ruleForm.ips.join('\n') : ''"
                  ></p>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <!-- 扫描端口选择【自定义】时 -->
            <el-tab-pane v-if="!ruleForm.port_group_ids" label="端口/协议" name="second">
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
              >
                <div style="margin-left: 10px; margin-top: 10px">端口号：</div>
                <el-form-item label="" prop="">
                  <el-input
                    type="textarea"
                    style="height: 60px"
                    v-model="ruleForm.define_ports"
                    placeholder="请输入端口号，若有多个，请使用分号或换行分隔开，单次最多可以添加20个端口号。如果不输入端口，将默认扫描0-65535端口。"
                  ></el-input>
                </el-form-item>
              </el-form>
              <el-form
                :model="ruleForm"
                :rules="rules"
                ref="addRuleForm"
                label-width="10px"
                class="demo-ruleForm"
              >
                <div style="margin-left: 10px; margin-top: 10px">协议：</div>
                <el-form-item label="" prop="define_port_protocols">
                  <new-transfer
                    :titles="['未选择', '已选择']"
                    v-loading="protocoldiaLoading"
                    filterable
                    :filter-method="filterMethodProtocol"
                    filter-placeholder="请输入关键字"
                    v-model="ruleForm.define_port_protocols"
                    :props="transferPropProtocol"
                    ref="reserve4"
                    @left-check-change="() => handleChangeLeft(4)"
                    @right-check-change="() => handleChangeRight(4)"
                    :data="protocolTransferData"
                  >
                  </new-transfer>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
          <!-- 资产扫描：扫描目标0，1，3，6除外的 -->
          <!-- <el-form v-else :model="ruleForm" :rules="rules" ref="addRuleForm" label-width="10px" class="demo-ruleForm">
              <el-form-item v-if="ruleForm.scan_range == 0" label="" prop="" style="position: relative">
                <el-input type="textarea" v-model="ruleForm.ips" :disabled="isdisabled" class="placeholderIdBox"></el-input>
                <div class="placeholderId" v-show="!ruleForm.ips">
                  <div></div>
                  <div>IP段支持格式如下</div>
                  <div>**********-100</div>
                  <div>**********/24</div>
                  <div></div>
                  <div>多个连续网段支持格式：</div>
                  <div>192.168.1-10.*（代表***********-************共10个网段）</div>
                  <div>最多输入1000个ip，分号或换行分隔</div>
                </div>
              </el-form-item>
              <el-form-item v-if="task_type == 1 && ruleForm.scan_range == 6" label="" prop="" style="position: relative">
                <el-input type="textarea" v-model="ruleForm.ips" class="placeholderIdBox"></el-input>
                <div class="placeholderId" v-show="!ruleForm.ips" style="margin-top:15px">
                  <div>支持填写域名</div>
                  <div></div>
                  <div>域名格式如下：</div>
                  <div>foradar.baimaohui.net</div>
                  <div>最多输入200个，分号或换行分隔</div>
                  <div>IP类型选择IPV4的话，域名就只解析IPV4的ip数据，同样，选择IPV6类型，域名就只解析IPV6格式的ip数据。</div>
                </div>
              </el-form-item>
              <el-form-item v-if="ruleForm.scan_range == 3" label="" prop="ips">
                <new-transfer
                  filterable
                  :filter-method="filterMethod"
                  filter-placeholder="请输入关键字"
                  v-model="ruleForm.ips"
                  :props="transferProp"
                  ref="reserve"
                  @left-check-change="handleChangeLeft"
                  @right-check-change="handleChangeRight"
                  :data="transferData"
                  >
                </new-transfer>
              </el-form-item>
            </el-form> -->
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="dialogFormVisibleClose" id="scan_cancel">关闭</el-button>
      <el-button class="highBtn" :loading="btnLoading" @click="insertSave" id="scan_sure"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import newTransfer from '../../components/transfer/src/main'
import newInputNumber from '@/components/input-number/index'
import { getIps, getDomains, ipListNoPage, addVulnerability, addCycle } from '@/api/apiConfig/api.js'
import { getPortProList } from '@/api/apiConfig/port.js'
import { getPocGroupListNoPage, getPocListNoPage } from '@/api/apiConfig/poc.js'
import {
  editGrontabTask,
  addGrontabTask,
  addTask,
  crontabTaskSwitch
} from '@/api/apiConfig/discovery.js'

export default {
  props: [
    'showList',
    'dialogFormVisible',
    'addIsTrue',
    'ruleForm',
    'task_type',
    'zhouqiIs',
    'editIs',
    'rules',
    'zhouQiSwitch',
    'portGroupsNoPageArr',
    'isdisabled',
    'isScanId',
    'isTypes',
    'user'
  ],
  components: {
    newTransfer,
    newInputNumber
  },
  data() {
    return {
      userInfo: {},
      engineVal: ['go'],
      engineGo: true,
      engineTra: false,
      loading: false,
      isType: 'false', //控制是不是周期任务
      btnLoading: false,
      file_name: '',
      activeName: 'first',
      activeNameScan: 'first',
      pocdiaLoading: false,
      // portGroupsNoPageArr: [],
      pocTransferData: [],
      pocGroupTransferData: [],
      uploadHeaders: { Authorization: localStorage.getItem('token') },
      fileList: [],
      uploadMaxCount: 1,
      pickerOptions: {
        // 限制仅可选择当天及以后
        disabledDate(v) {
          return v.getTime() < new Date().getTime() - 24 * 60 * 60 * 1000
        }
      },
      transferProp: {
        key: 'ip',
        label: 'ip'
      },
      transferPropPoc: {
        key: 'id',
        label: 'name'
      },
      transGroupferPropPoc: {
        key: 'id',
        label: 'name'
      },
      transferData: [],
      confirmedIp: [],
      noConfirmedIp: [],
      confirmedDomain: [],
      noConfirmedDomain: [],
      transferPropsureIps: {
        key: 'ip',
        label: 'ip'
      },
      sureIpsData: [],
      sureDomainsData: [],
      filterMethodsureIp(query, item) {
        return item.ip.indexOf(query) > -1
      },
      filterMethod(query, item) {
        return item.name.indexOf(query) > -1
      },
      selectData: {
        scan_range_arr: [
          // 0 手动输入的ip 1上传的ip 3根据ip段扫描 4资产台账扫描 5资产台账及ip段扫描 6输入域名信息
          {
            name: '输入IP信息',
            id: 0
          },
          {
            name: '上传ip信息文件',
            id: 1
          },
          {
            name: '资产台账扫描', // '已认领资产',
            id: 4
          },
          
          // {
          //   name: '根据ip段扫描',
          //   id: 3
          // },
          // {
          //   name: '资产台账及ip段扫描',
          //   id: 5
          // },
          {
            name: '输入域名信息',
            id: 6
          },
        ],
        poc_scan_range_arr: [
          {
            name: '全部台账资产',
            id: 4
          },
          {
            name: '选择台账资产',
            id: 0
          },
          {
            name: '上传ip信息文件',
            id: 1
          },
          {
            name: '上传域名信息文件',
            id: 8
          }
        ],
        poc_scan_type_arr: [
          {
            name: '极速扫描',
            id: 1
          }
        ],
        scan_type_arr: [
          {
            name: '极速扫描',
            id: 1
          },
          {
            name: '深度扫描',
            id: 0
          }
        ],
        monthArr: [
          1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25,
          26, 27, 28, 29, 30, 31
        ],
        weekly: [
          {
            value: '1',
            label: '周一'
          },
          {
            value: '2',
            label: '周二'
          },
          {
            value: '3',
            label: '周三'
          },
          {
            value: '4',
            label: '周四'
          },
          {
            value: '5',
            label: '周五'
          },
          {
            value: '6',
            label: '周六'
          },
          {
            value: '0',
            label: '周日'
          }
        ],
        poc_range: [
          {
            id: 0,
            name: '全部PoC'
          },
          {
            id: 1,
            name: '指定PoC范围'
          },
          {
            id: 2,
            name: '指定PoC分组'
          },
          {
            id: 3,
            name: '暴力破解'
          }
        ],
        scan_date_range: [
          {
            name: '每月重复',
            id: 3
          },
          {
            name: '每周重复',
            id: 4
          },
          {
            name: '每天重复',
            id: 5
          },
          {
            name: '仅执行一次',
            id: 6
          }
        ],
        protocol_concurrency_arr: [
          2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 100, 200, 300
        ]
      },
      isDomain: true, //控制是否能选择周期任务
      protocolTransferData: [], //协议列表
      transferPropProtocol: {
        key: 'id',
        label: 'protocol'
      },
      protocoldiaLoading: false,
      filterMethodProtocol(query, item) {
        return item.protocol.indexOf(query) > -1
      },
      testUser: '', //测试用户
      role: '' //安服
    }
  },
  watch: {
    showList(val) {
      this.$set(this.ruleForm, 'ips', val)
    },
    'ruleForm.ip_type': {
      handler(val) {
        if (val == 2) {
          // 选择IPV6时，扫描类型自动切换为深度扫描 去除急速扫描
          this.selectData.scan_type_arr = [
            {
              name: '深度扫描',
              id: 0
            }
          ]
          this.ruleForm.scan_type = 0
        } else {
          this.selectData.scan_type_arr = [
            //扫描类型
            {
              name: '极速扫描',
              id: 1
            },
            {
              name: '深度扫描',
              id: 0
            }
          ]
          this.ruleForm.scan_type = 1
        }
      },
      immediate: true
    },
    isType(val) {
      if (val == 'true') {
        this.selectData.scan_range_arr.pop()
      } else {
        this.selectData.scan_range_arr.push({
          name: '输入域名信息',
          id: 6
        })
      }
    },
    ruleForm(val) {
      this.confirmedIp = []
      this.noConfirmedIp = []
      this.confirmedDomain = []
      this.noConfirmedDomain = []
      if (!this.ruleForm.name) {
        this.ruleForm.name =
          this.task_type == 1
            ? `资产扫描任务-${this.transferTime(new Date())}`
            : `漏洞扫描任务-${this.transferTime(new Date()).replace(/:/g, '')}`
      }
      if (val.is_define_port == 1 && this.task_type == 1) {
        this.changeProts()
        this.ruleForm.port_group_ids = ''
      } else {
        if (val.port_range == 1 && this.task_type == 1) {
          this.ruleForm.port_group_ids = -2
        }
      }
      if (this.ruleForm.define_ports && this.ruleForm.define_ports.length != 0) {
        if (this.ruleForm.define_ports == '65536') {
          this.ruleForm.define_ports = ''
        } else {
          this.ruleForm.define_ports = this.ruleForm.define_ports.split(',').join('\r')
        }
      }
      this.activeName = [1, 2, 5].includes(val.poc_scan_type) ? 'second' : 'first'
      if (val.scan_range == 1) {
        // 扫描目标为上传文件，需要回显文件
        if (!val.file_name && !this.file_name) {
          this.fileList = []
        } else {
          this.fileList = [{ name: val.file_name ? val.file_name : this.file_name, url: '' }]
        }
      }
      if(val.scan_range == 8){
        if(val.file_name){
          this.fileList = [{ name: val.file_name ? val.file_name : this.file_name, url: '' }]
        }else{
          this.fileList = []
        }
      }
      // val.port_group_ids = val.port_group_ids / 1
      val.protocol_concurrency = val.protocol_concurrency / 1
      // 漏扫周期编辑时，扫描目标为根据IP段筛选，需要请求已知资产ip段
      if (
        (this.task_type == 2 && val.scan_range == 0) ||
        (this.task_type == 2 && val.scan_range == 1)
      ) {
        this.getSureIpData()
      }
      if(this.task_type == 2 && val.scan_range == 8){
        this.getSureDomainData()
      }
      if (this.task_type == 1 && val.scan_range == 3) {
        this.getIpData()
      }
      if (this.task_type == 2 && val.poc_scan_type == 1) {
        this.getPOCData()
      }
      if (this.task_type == 2 && val.poc_scan_type == 2) {
        this.getPOCGroupData()
      }
      if (val.ips) {
        if (this.task_type == 1) {
          if (val.ips.indexOf(',') != '-1') {
            val.ips = val.ips.replace(/,/g, '\n')
          }
        } else {
          val.ips = val.ips
        }
      }
    },
    dialogFormVisible(val) {
      this.isType = this.isTypes
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.getPortId()
  },
  methods: {
    changeEngineVal(val) {
      // 根据引擎变换场景选择的下拉列表
      if (val.includes('go') && val.includes('tra')) {
        this.selectData.poc_range = [
          {
            id: 6,
            name: '默认'
          }
        ]
        this.ruleForm.poc_scan_type = 6
        this.ruleForm.scan_engine = 2
      } else if (val.includes('tra')) {
        this.selectData.poc_range = [
          {
            id: 4,
            name: '系统扫描'
          },
          {
            id: 5,
            name: 'web扫描'
          }
        ]

        this.ruleForm.poc_scan_type = 4
        this.ruleForm.scan_engine = 1
      } else if (val.includes('go')) {
        this.selectData.poc_range = [
          {
            id: 0,
            name: '全部PoC'
          },
          {
            id: 1,
            name: '指定PoC范围'
          },
          {
            id: 2,
            name: '指定PoC分组'
          },
          {
            id: 3,
            name: '暴力破解'
          }
        ]
        this.ruleForm.poc_scan_type = 0
        this.ruleForm.scan_engine = 0
      }
      // 重置表单
      // this.fileList = []
      // this.confirmedIp = []
      // this.noConfirmedIp = []
      this.$set(this.selectData, 'poc_scan_range_arr', [
        {
          name: '全部台账资产',
          id: 4
        },
        {
          name: '选择台账资产',
          id: 0
        },
        {
          name: '上传ip信息文件',
          id: 1
        },
        {
          name: '上传域名信息文件',
          id: 8
        }
      ])
      if (this.ruleForm.scan_range == 7) {
        this.ruleForm.scan_range = 4
      }
    },
    open() {
      let scan_engine = this.ruleForm.scan_engine
      if (scan_engine == 2) {
        this.selectData.poc_range = [
          {
            id: 6,
            name: '默认'
          }
        ]
        this.engineVal = ['go', 'tra']
      } else if (scan_engine == 1) {
        this.selectData.poc_range = [
          {
            id: 4,
            name: '系统扫描'
          },
          {
            id: 5,
            name: 'web扫描'
          }
        ]
        this.engineVal = ['tra']
      } else if (!scan_engine) {
        this.selectData.poc_range = [
          {
            id: 0,
            name: '全部PoC'
          },
          {
            id: 1,
            name: '指定PoC范围'
          },
          {
            id: 2,
            name: '指定PoC分组'
          },
          {
            id: 3,
            name: '暴力破解'
          }
        ]
        this.engineVal = ['go']
      }
      let poc_scan_type = this.ruleForm.poc_scan_type

      if (poc_scan_type == 5) {
        // web扫描
        this.activeName = 'second'
        this.$set(this.selectData, 'poc_scan_range_arr', [
          {
            name: '上传URL信息文件',
            id: 7
          }
        ])
      } else {
        this.activeName = 'first'
        this.$set(this.selectData, 'poc_scan_range_arr', [
          {
            name: '全部台账资产',
            id: 4
          },
          {
            name: '选择台账资产',
            id: 0
          },
          {
            name: '上传ip信息文件',
            id: 1
          },
          {
            name: '上传域名信息文件',
            id: 8
          }
        ])
      }
    },
    getPortId() {
      //获取全部常用端口的id和用户是不是测试用户
      let user = JSON.parse(sessionStorage.getItem('userMessage'))
      this.userInfo = user
      this.testUser = user.user.level
      this.role = user.user.role
    },
    handleChangeLeft(value) {
      this.$nextTick(() => {
        this.$refs['reserve' + value].addToRight() //直接执行到右事件
      })
    },
    handleChangeRight(value) {
      this.$nextTick(() => {
        this.$refs['reserve' + value].addToLeft() //直接执行到右事件
      })
    },
    dialogFormVisibleClose() {
      this.$emit('dialogFormVisibleClose', false)
    },
    insertSaveAfter() {
      this.$emit('insertSaveAfter')
    },
    scan_date_rang_change() {
      this.$set(this.ruleForm, 'day_of_x', '')
      // this.$refs['addRuleForm'].clearValidate('day_of_x');
    },
    chooseMonth(monthVal) {
      this.ruleForm.day_of_x = monthVal
    },
    downloadForbidUrlsExcel() {
      window.location.href = '/downloadTemplate/漏洞扫描URL信息导入模板.xlsx'
    },
    downloadForbidIpsExcel() {
      window.location.href = '/downloadTemplate/IP信息导入模板.xlsx'
    },
    downloadForbidIpsExcelPoc() {
      window.location.href = '/downloadTemplate/漏洞扫描IP信息导入模板.xlsx'
    },
    downloadForbidDomainExcelPoc() {
      window.location.href = '/downloadTemplate/漏洞扫描域名信息导入模板.xlsx'
    },

    async insertSave() {
      this.ruleForm.operate_company_id = this.currentCompany
      let obj = Object.assign({}, this.ruleForm)
      if (this.task_type == 1) {
        if (obj.port_group_ids == this.isScanId && this.testUser == 0 && this.role == 3) {
          //扫描端口是全部端口和用户是测试用户时
          this.$message.error('如需下发全部端口扫描，请升级为正式用户！')
          return
        }
      }
      if (!obj.name) {
        // 扫描目标不是已认领资产的都需要校验ips
        this.$message.error('请输入任务名称')
        return
      }
      if (JSON.parse(this.isType) && obj.type != 5 && !obj.day_of_x) {
        // 重复类型：每天不需要填写
        this.$message.error('请选择执行日期')
        return
      }
      if (JSON.parse(this.isType) && !obj.schedule_time) {
        // 扫描目标不是已认领资产的都需要校验ips
        this.$message.error('请选择执行时间')
        return
      }
      if (!obj.bandwidth) {
        // 扫描目标不是已认领资产的都需要校验ips
        this.$message.error('请输入扫描带宽')
        return
      } else if (/^(?:[1-9]\d*)$/.test(obj.bandwidth) == false) {
        this.$message.error('扫描带宽请输入整数')
        return
      }
      // scan_range：0 手动输入的ip 1上传的ip  3根据ip段扫描 4资产台账扫描 5资产台账及ip段扫描 6输入域名信息
      if (
        this.task_type == 1 &&
        obj.scan_range != 3 &&
        obj.scan_range != 4 &&
        obj.scan_range != 5 &&
        obj.scan_range != 6 &&
        (!obj.ips || obj.ips.length == 0)
      ) {
        // 资产扫描扫描目标除了4，5其余的都需要校验ips
        this.$message.error('请输入或上传ip信息')
        return
      }

      if (this.task_type == 1 && obj.scan_range == 3 && (!obj.ips || obj.ips.length == 0)) {
        this.$message.error('请选择ip信息')
        return
      }
      if (
        this.task_type == 1 &&
        obj.scan_range != 4 &&
        obj.scan_range != 5 &&
        obj.scan_range == 6 &&
        (!obj.ips || obj.ips.length == 0)
      ) {
        // 资产扫描扫描目标除了4，5其余的都需要校验ips
        this.$message.error('请输入域名信息')
        return
      }
      if (this.task_type == 2 && obj.scan_range == 0 && (!obj.ips || obj.ips.length == 0)) {
        // 扫描目标是【选择台账】
        this.$message.error('请选择ip信息')
        return
      } else if (this.task_type == 2 && obj.scan_range == 0 && obj.ips.length != 0) {
        let sureIpsData = this.sureIpsData.map((item) => item.ip)
        let ips = this.ruleForm.ips.filter((item) => {
          return sureIpsData.includes(item)
        })
        if (!ips || (ips && ips.length == 0)) {
          this.$message.error('请选择ip信息')
          return
        }
        obj.ips = ips
      }

      if (this.task_type == 2 && obj.scan_range == 1 && (!obj.ips || obj.ips.length == 0)) {
        // 扫描目标是【上传文件信息】
        this.$message.error('已确权IP不可为空')
        return
      }
      if (this.task_type == 2 && obj.scan_range == 8 && this.confirmedDomain.length == 0) {
        // 扫描目标是【上传文件信息】
        this.$message.error('已确权域名不可为空')
        return
      }
      if (this.task_type == 2 && obj.poc_scan_type == 1 && obj.poc_ids.length == 0) {
        // 指定POC范围
        this.$message.error('请指定PoC范围')
        return
      }
      if (this.task_type == 2 && obj.poc_scan_type == 2 && obj.poc_ids.length == 0) {
        // 指定poc分组
        this.$message.error('请指定PoC分组')
        return
      }

      if (
        this.task_type == 1 &&
        obj.define_ports &&
        obj.port_group_ids == '' &&
        obj.define_port_protocols.length == 0
      ) {
        this.$message.error('请选择协议')
        return
      }
      if (this.task_type == 1) {
        if (obj.port_group_ids == '') {
          obj.is_define_port = 1
        } else {
          obj.is_define_port = 0
        }
      }

      // if (this.task_type == 1 && JSON.parse(this.isType) && obj.is_define_port == 1) {
      //   //自定义端口 周期扫描端口号必填
      //   this.$message.error('请填写端口号')
      //   return
      // }
      if (this.task_type == 2 && obj.poc_scan_type == 0) {
        delete obj.poc_ids
      }
      // 仅资产扫描是否开启首页截图
      if (this.task_type == 1) {
        // 扫描目标：输入ip信息，要转换成数组
        if (obj.scan_range == 0) {
          obj.ips = obj.ips
            ? obj.ips
                .split(/[；|;|\r\n]/)
                .filter((item) => {
                  return item.trim()
                })
                .map((item) => {
                  return item.trim()
                })
            : [] // 中英文逗号或者空格切割字符串
        }
        if (obj.scan_range == 6) {
          obj.ips = obj.ips
            ? obj.ips
                .split(/[；|;|\r\n]/)
                .filter((item) => {
                  return item.trim()
                })
                .map((item) => {
                  return item.trim()
                })
            : [] // 中英文逗号或者空格切割字符串
        }
        // 是否开启ping识别
        obj.ping_switch = obj.ping_switch ? 1 : 0
        // 是否开启首页截图
        obj.web_logo_switch = obj.web_logo_switch ? 1 : 0
      } else {
        // delete obj.ping_switch
        delete obj.web_logo_switch
      }
      obj.define_ports = obj.define_ports
        ? obj.define_ports
            .split(/；|;|[\r\n]/)
            .filter((item) => {
              return item.trim()
            })
            .map((item) => {
              return item.trim()
            })
        : []
      if (obj.define_ports.length > 0) {
        let isPass = true
        obj.define_ports.forEach((item) => {
          if (item > 65535 || item < 0) {
            isPass = false
          }
        })
        if (!isPass) return this.$message.error('需要输入0-65535之间的端口号')
      }

      if (
        this.task_type == 1 &&
        (!obj.define_ports || obj.define_ports.length == 0) &&
        obj.port_group_ids == ''
      ) {
        obj.define_ports = ['65536']
      }

      if (this.task_type == 1 && obj.define_ports.length > 20 && obj.port_group_ids == '') {
        this.$message.error('输入的端口信息过多，最多输入20个端口信息')
        return
      }
      obj.ips = obj.ips ? obj.ips : [] // 传给后端的必须是个数组
      obj.is_domain_scan = obj.is_domain_scan == '域名' ?  1 : 2// 传给后端的必须是个数组
      obj.file_name = this.file_name ? this.file_name : obj.file_name // 上传文件的名称回显
      this.btnLoading = true
      if (this.task_type == 1) {
        // 资产扫描
        if (this.addIsTrue) {
          // 新增
          if (!JSON.parse(this.isType)) {
            // 非周期新增
            let res = await addTask(obj).catch(() => {
              this.btnLoading = false
            })
            if (res.code == 0) {
              res.data && this.judgeErrorList(res.data)
            } else {
              this.btnLoading = false
            }
          } else {
            // 周期新增
            let res = await addGrontabTask(obj).catch(() => {
              this.btnLoading = false
            })
            if (res.code == 0) {
              res.data && this.judgeErrorList(res.data)
            }
          }
        } else if (this.zhouqiIs) {
          // 编辑(周期)
          if (this.zhouQiSwitch == 1) {
            // 关闭任务的编辑
            let res = await editGrontabTask(obj).catch(() => {
              this.btnLoading = false
            })
            if (res.code == 0) {
              res.data && this.judgeErrorList(res.data)
            }
          } else {
            // 打开任务的编辑
            this.$confirm('是否关闭当前任务?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(async () => {
                let res = await editGrontabTask(obj).catch(() => {
                  this.btnLoading = false
                })
                let data = {
                  id: this.ruleForm.id,
                  switch: 1,
                  operate_company_id: this.currentCompany
                }
                let response = await crontabTaskSwitch(data)
                if (res.code == 0 && response.code == 0) {
                  res.data && this.judgeErrorList(res.data)
                }
              })
              .catch(async () => {
                let res = await editGrontabTask(obj).catch(() => {
                  this.btnLoading = false
                })
                if (res.code == 0) {
                  res.data && this.judgeErrorList(res.data)
                }
              })
          }
        } else {
          //单次任务编辑
          if (obj.port_range == 1) {
            obj.port_group_ids = ''
            obj.ips = []
          }
          let res = await addTask(obj).catch(() => {
            this.btnLoading = false
          })
          if (res.code == 0) {
            res.data && this.judgeErrorList(res.data)
          }
        }
      } else {
        // 漏洞扫描
        console.log(this.ruleForm.is_domain_scan)
        if (this.ruleForm.is_domain_scan == '域名') {
          obj.is_domain_scan = 1
        } else {
          obj.is_domain_scan = 2
        }
        obj.port_group_ids = ''
        if (this.addIsTrue) {
          // 新增
          if (!JSON.parse(this.isType)) {
            // 非周期
            let res = await addVulnerability(obj).catch(() => {
              this.btnLoading = false
            })
            if (res.code == 0) {
              this.btnLoading = false
              this.$message.success('操作成功！')
              this.insertSaveAfter()
            }
          } else {
            // 周期
            let res = await addCycle(obj).catch(() => {
              this.btnLoading = false
            })
            if (res.code == 0) {
              this.btnLoading = false
              this.$message.success('操作成功！')
              this.insertSaveAfter()
            }
          }
        } else if (this.zhouqiIs) {
          // 编辑(周期)
          if (this.zhouQiSwitch == 1) {
            let res = await addCycle(obj).catch(() => {
              this.btnLoading = false
            })
            if (res.code == 0) {
              this.btnLoading = false
              this.$message.success('操作成功！')
              this.insertSaveAfter()
            }
          } else {
            this.$confirm('是否关闭当前任务?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(async () => {
                let res = await addCycle(obj).catch(() => {
                  this.btnLoading = false
                })
                let data = {
                  id: this.ruleForm.id,
                  switch: 1,
                  operate_company_id: this.currentCompany
                }
                let response = await crontabTaskSwitch(data)
                if (res.code == 0 && response.code == 0) {
                  this.btnLoading = false
                  this.$message.success('操作成功！')
                  this.insertSaveAfter()
                }
              })
              .catch(async () => {
                let res = await addCycle(obj).catch(() => {
                  this.btnLoading = false
                })
                if (res.code == 0) {
                  this.btnLoading = false
                  this.$message.success('操作成功！')
                  this.insertSaveAfter()
                }
              })
          }
        } else {
          //单次任务编辑
          let res = await addVulnerability(obj).catch(() => {
            this.btnLoading = false
          })
          if (res.code == 0) {
            this.btnLoading = false
            this.$message.success('操作成功！')
            this.insertSaveAfter()
          }
        }
        // 初始化漏洞ip上传
        this.confirmedIp = []
        this.noConfirmedIp = []
        this.confirmedDomain = []
        this.noConfirmedDomain = []
      }
    },
    // 判断返回结果是否有错误资产 并弹窗展示出来
    judgeErrorList(data) {
      if (data.error_list && data.error_list.length !== 0) {
        // 存在错误格式资产
        this.btnLoading = false
        let validType = ''
        if (this.task_type == 1 && this.ruleForm.scan_range == 1) {
          validType = 'upload'
        } else if (this.task_type == 1 && this.ruleForm.scan_range != 1) {
          validType = 'textarea'
        }
        this.$emit('validateConfirm', data.error_list, data.data_list, validType)
        return
      } else {
        this.btnLoading = false
        this.$message.success('操作成功！')
        this.insertSaveAfter()
      }
    },
    poc_scan_range_change(val) {
      // 0全部POC,1指定POC范围,2指定POC分组
      this.activeName = val > 0 && val != 3 ? 'second' : 'first'

      if (this.engineVal == 'tra') {
        if (val == 5) {
          // web扫描
          this.$set(this.selectData, 'poc_scan_range_arr', [
            {
              name: '上传URL信息文件',
              id: 7
            }
          ])
          this.ruleForm.scan_range = 7
        } else {
          this.$set(this.selectData, 'poc_scan_range_arr', [
            {
              name: '全部台账资产',
              id: 4
            },
            {
              name: '选择台账资产',
              id: 0
            },
            {
              name: '上传ip信息文件',
              id: 1
            },
            {
              name: '上传域名信息文件',
              id: 8
            }
          ])
          this.ruleForm.scan_range = 4
        }
        this.ruleForm.ips = []
      }
      // 重置表单数据
      this.confirmedIp = []
      this.noConfirmedIp = []
      this.confirmedDomain = []
      this.noConfirmedDomain = []
      // this.ruleForm.ips = []
      this.fileList = []
      if (val > 0) {
        this.ruleForm.poc_ids = []
      }
      if (this.task_type == 2 && val == 1) {
        this.getPOCData()
      }
      if (this.task_type == 2 && val == 2) {
        this.getPOCGroupData()
      }
    },
    async getPOCData() {
      this.pocdiaLoading = true
      this.pocTransferData = []
      let pocList = await getPocListNoPage({
        keyword: '',
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.pocdiaLoading = false
      })
      if (pocList && pocList.code == 0) {
        this.pocTransferData = pocList.data
      }
      this.pocdiaLoading = false
    },
    async getPOCGroupData() {
      this.pocdiaLoading = true
      this.pocTransferData = []
      let pocList = await getPocGroupListNoPage({
        keyword: '',
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.pocdiaLoading = false
      })
      if (pocList && pocList.code == 0) {
        this.pocTransferData = pocList.data
      }
      this.pocdiaLoading = false
    },
    iptype_change(val) {
      // 资产下发时
      if (val == 2) {
        // 选择IPV6时，扫描类型自动切换为深度扫描 去除急速扫描
        this.selectData.scan_type_arr = [
          {
            name: '深度扫描',
            id: 0
          }
        ]
        this.ruleForm.scan_type = 0
      } else {
        if (this.role == 2) {
          this.selectData.scan_type_arr = [
            //扫描类型
            {
              name: '极速扫描',
              id: 1
            },
            {
              name: '深度扫描',
              id: 0
            }
            // {
            //   name: 'fofa联动扫描',
            //   id: 2
            // }
          ]
        } else {
          this.selectData.scan_type_arr = [
            //扫描类型
            {
              name: '极速扫描',
              id: 1
            },
            {
              name: '深度扫描',
              id: 0
            }
          ]
        }
        this.ruleForm.scan_type = 1
      }
    },
    scan_range_change(val) {
      this.fileList = []
      this.confirmedIp = []
      this.noConfirmedIp = []
      this.confirmedDomain = []
      this.noConfirmedDomain = []
      this.activeName = val == 0 || val == 1 || val == 8 ? 'first' : 'second'
      switch (val) {
        case 0:
          this.ruleForm.ips = this.task_type == 1 ? '' : []
          break
        case 1:
          this.ruleForm.ips = []
          break
        case 2:
          this.ruleForm.ips = []
          break
        case 3:
          this.ruleForm.ips = []
          break
        case 6:
          this.ruleForm.ips = ''
          break
        case 8:
          this.ruleForm.ips = []
          break
        default:
          this.ruleForm.ips = []
      }
      if ((this.task_type == 2 && val == 0) || (this.task_type == 2 && val == 1)) {
        // 新建漏洞任务，扫描目标：IP段筛选,上传IP信息
        this.getSureIpData()
      }
      if (this.task_type == 1 && val == 3) {
        // 新建漏洞任务，扫描目标：IP段筛选
        this.getIpData()
      }
      if (this.task_type == 2 && val == 8) {
        // 新建漏洞任务，扫描目标：IP段筛选
        this.getSureDomainData()
      }
    },
    async getSureIpData(isEdit) {
      this.pocdiaLoading = true
      let data = {
        operate_company_id: this.currentCompany
      }
      let res = await getIps(data)
      if (res.code == 0) {
        this.pocdiaLoading = false
        let arr = []
        res.data.forEach((item, index) => {
          arr.push({ id: index, ip: item })
        })
        this.sureIpsData = arr
        if (this.ruleForm.ips.length !== 0) {
          this.ruleForm.ips.forEach((item) => {
            if (
              this.sureIpsData
                .map((su) => {
                  return su.ip
                })
                .indexOf(item) != -1
            ) {
              this.confirmedIp.push(item)
            } else {
              this.noConfirmedIp.push(item)
            }
          })
        }
      }
    },
    async getSureDomainData(isEdit) {
      this.pocdiaLoading = true
      let data = {
        operate_company_id: this.currentCompany
      }
      let res = await getDomains(data)
      if (res.code == 0) {
        this.pocdiaLoading = false
        this.ruleForm.is_domain_scan = '域名'
        let arr = []
        res.data.forEach((item, index) => {
          arr.push({ id: index, domain: item })
        })
        this.sureDomainsData = arr
        if (this.ruleForm.ips.length !== 0) {
          this.ruleForm.ips.forEach((item) => {
            if (
              this.sureDomainsData
                .map((su) => {
                  return su.domain
                })
                .indexOf(item) != -1
            ) {
              this.confirmedDomain.push(item)
            } else {
              this.noConfirmedDomain.push(item)
            }
          })
        }
      }
    },
    uploadSuccess(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.ruleForm.ips = []
        this.file_name = file.name
        if (this.task_type == 2) {
          // 漏洞管理需要对比出已确权、未确权ip,this.sureIpsData是台账已确权列表
          if (this.ruleForm.scan_range == 7) {
            this.ruleForm.ips = file.response.data // url上传回显
          } else {
            this.confirmedIp = []
            this.noConfirmedIp = []
            this.confirmedDomain = []
            this.noConfirmedDomain = []
            response.data.forEach((item) => {
              if (
                this.sureIpsData
                  .map((su) => {
                    return su.ip
                  })
                  .indexOf(item) != -1
              ) {
                this.confirmedIp.push(item)
              }else{
                this.noConfirmedIp.push(item)
              }
              if(
                this.sureDomainsData
                  .map((su) => {
                    return su.domain
                  })
                  .indexOf(item) != -1
              ){
                this.confirmedDomain.push(item)
              }else{
                this.noConfirmedDomain.push(item)
              }
            })
            this.ruleForm.ips = [...this.confirmedIp]
            if(this.ruleForm.scan_range == 8){
              this.ruleForm.ips = [...this.confirmedDomain]
            }
            console.log(this.ruleForm,'444444')
          }
        } else {
          // 资产扫描
          if (response.data.error_list && response.data.error_list.length !== 0) {
            // 存在错误格式资产
            this.$emit(
              'validateConfirm',
              response.data.error_list,
              response.data.data_list,
              'upload'
            )
          } else {
            this.ruleForm.ips = response.data.data_list
          }
        }
      } else {
        this.$message.error(file.response.message)
      }
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadError(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.ruleForm.ips = []
        this.confirmedIp = []
        this.noConfirmedIp = []
        this.confirmedDomain = []
        this.noConfirmedDomain = []
      }
    },
    beforeUpload(file, limit = 3) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    },
    async getIpData() {
      let params = {
        type: this.ruleForm.ip_type, // 1:IPV4 2:IPV6
        operate_company_id: this.currentCompany
      }
      let res = await ipListNoPage(params)
      let arr = []
      res.data.forEach((item, index) => {
        arr.push({ id: item.ip, ip: item.ip })
      })
      this.transferData = res.data
    },
    //资产单次任务扫描的时候扫描目标为输入域名时不能选择周期任务
    changeScanRange(val) {
      if (val == 6) {
        this.isDomain = false
      } else {
        this.isDomain = true
      }
    },
    async changeProt(val) {
      //获取协议列表
      if (val == '') {
        // 扫描端口选择自定义
        this.protocoldiaLoading = true
        this.activeNameScan = 'second'
        let prores = await getPortProList()
        this.protocoldiaLoading = false
        this.protocolTransferData = prores.data
      } else {
        // 扫描端口选择全部等非自定义
        this.activeNameScan = 'first'
      }
    },
    async changeProts() {
      //获取协议列表
      this.activeNameScan = 'second'
      this.protocoldiaLoading = true
      let prores = await getPortProList()
      this.protocoldiaLoading = false
      this.protocolTransferData = prores.data
    }
  }
}
</script>
<style scoped lang="less">
.dialog-body /deep/ {
  height: 497px;
  display: flex;
  justify-content: space-between;
  & > .el-form {
    width: 100%;
  }
  & > .el-divider--vertical {
    height: 156px;
    background: #e9ebef;
    margin-bottom: 20px;
  }
  & > span {
    display: inline-block;
    width: 48%;
    text-align: center;
    margin-top: 30px;
  }
  & > span > img {
    width: 54px;
    height: 54px;
    margin-bottom: 8px;
  }
  & > span p {
    color: #2677ff;
    font-weight: 400;
  }
  /deep/.el-input--suffix .el-input__inner {
    padding-right: 20px;
  }
  .left {
    // height: 100%;
    // overflow: auto;
    // margin-bottom: 24px;
    flex: 1;
    .tishi {
      line-height: 20px;
      color: #d1d5dd;
    }
    /deep/.el-textarea {
      width: 300px;
      height: 368px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d1d5dd;
      .el-textarea__inner {
        height: 100%;
        border: 0 !important;
      }
      .el-textarea__inner:hover {
        border: 0;
      }
    }
    .dialog-footer {
      text-align: right;
    }
  }
  .right {
    width: 55%;
    margin-bottom: 24px;
    margin-left: 33px;
    border-left: 1px solid #e9ebef;
    /deep/.tabWrap {
      .el-tabs__nav {
        height: 32px !important;
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #e4e7ed;
        margin-bottom: 0;
        padding-left: 0;
        margin-left: 33px;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        height: 32px !important;
        line-height: 32px !important;
        text-align: center;
        background: rgba(38, 119, 255, 0.1);
        border-radius: 0;
        border: 1px solid #2677ff;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        height: 0;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 0px;
        // background-color: #E9EBEF;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 32px !important;
        text-align: center;
        line-height: 32px;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
        transition: none;
      }
    }
    .downloadClass {
      margin-top: 10px;
      margin: 0px 0px 16px !important;
    }
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .el-upload-list {
        height: 50px !important;
        min-height: 50px;
        overflow-y: auto;
      }
      .el-upload-list__item {
        height: 50px !important;
      }
    }
    .el-transfer {
      margin-top: 10px;
      height: 434px !important;
    }
    .el-transfer-panel {
      height: 100% !important;
    }
    .uploadIps {
      height: 118px;
      overflow: auto;
      border: 1px solid #e9ebef;
      border-radius: 5px;
      padding: 10px;
      white-space: pre-line;
    }
    /deep/.el-textarea {
      width: 100%;
      height: 442px;
      margin-top: 10px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d1d5dd;
      .el-textarea__inner {
        height: 100%;
        border: 0 !important;
      }
      .el-textarea__inner:hover {
        border: 0;
      }
    }
  }
}

.placeholderIdBox {
  position: absolute;
  top: 0px;
  background-color: transparent !important;
  /deep/.el-textarea__inner {
    background-color: transparent !important;
  }
}
.placeholderId {
  margin-top: 10px;
  div {
    min-height: 22px;
    line-height: 22px;
    color: #c0c4cc;
    padding-left: 15px;
  }
}
.tip {
  height: 20px;
  margin-top: 8px;
  font-size: 12px;
  line-height: 20px;
}
</style>
