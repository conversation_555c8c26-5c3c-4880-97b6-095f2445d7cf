<template>
  <div class="container">
    <!-- 漏洞扫描有待确权数量，需要单独计算top:numWrapWait -->
    <div :class="wait_confirm ? 'numWrap numWrapWait' : 'numWrap'">
      <el-button class="normalBtn" type="primary" v-if="task_type == 2"
        :disabled="!userIsOpen || UpdatePocRiskBtnLoading" @click="goUpdatePocRiskIp('btn')">更新风险资产IP</el-button>
      <span class="waitNum" v-if="wait_num > 0">等待执行任务数量：<span class="num">{{ wait_num }}</span></span>
      <span class="waitNum" v-if="last_rist_time">风险资产更新时间：<span class="num">{{ last_rist_time }}</span></span>
    </div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input v-model="formInline.name" @keyup.enter.native="checkFuncList" placeholder="请输入任务名称进行搜索"
            id="scan_keycheck">
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span v-if="userIsOpen" @click="highCheckIsShow" id="scan_filter" style="width: 80px"><img
              src="../../assets/images/filter.png" alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px" />高级筛选</span>
        </div>
        <div>
          <el-checkbox class="checkboxAll" v-model="checkedAll" @change="checkAllChange"
            id="scan_all">选择全部</el-checkbox>
          <el-button :disabled="!userIsOpen" class="normalBtnRe" type="primary" @click="removeOne('more')"
            id="scan_more_del">删除</el-button>
          <el-button :disabled="!userIsOpen" class="normalBtn" type="primary" @click="addTaskDialog"
            id="scan_add">新建任务</el-button>
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter id="hightFilter" :highTabShow="highTabShow" :highlist="highlist" :total="total" pageIcon="task"
        @highcheck="highCheck"></hightFilter>
      <div :class="hightFilterIsShow()" v-loading="loading" style="padding: 0px 20px">
        <el-table border :data="tableData" row-key="id" :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange" @cell-mouse-enter="showTooltip" @cell-mouse-leave="hiddenTooltip"
          ref="eltable" height="100%" style="width: 100%">
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>当前暂无扫描任务<i>，您可以<span @click="addTaskDialog">新建任务</span></i></p>
            </div>
          </template>
          <el-table-column type="selection" align="center" :reserve-selection="true" :show-overflow-tooltip="true"
            :selectable="handleSelectable" width="55">
          </el-table-column>
          <el-table-column v-for="item in tableHeaderIsShow" :key="item.id" :prop="item.name" align="left"
            :min-width="item.minWidth" :fixed="item.fixed" :label="item.label">
            <template slot-scope="scope">
              <!-- 0：等待扫描 1：扫描中 2：扫描完成 3：扫描失败 4：暂停扫描 -->
              <span v-if="item.name == 'name'">{{ scope.row[item.name] }}</span>
              <span v-else-if="item.name == 'status'">
                <!-- 资产扫描 -->
                <span v-if="scope.row['task_type'] == 1">
                  <span v-if="scope.row['status'] == 0" class="grayLine">等待扫描</span>
                  <span v-if="scope.row['status'] == 1" class="blueLine">扫描中</span>
                  <span v-if="scope.row['status'] == 2" class="greenLine">扫描完成</span>
                  <span v-if="scope.row['status'] == 3" class="redLine">扫描失败</span>
                  <span v-if="scope.row['status'] == 4" class="yellowLine">
                    <el-tooltip placement="top" v-if="scope.row['status'] == 4" :open-delay="500">
                      <div slot="content">
                        <span v-if="Math.floor(scope.row['progress']) <= 50">
                          <span>当前处于禁扫时间内，如需继续执行任务，请重新</span><span @click="changSetTimeBox"
                            style="color: #409eff; cursor: pointer">设置禁扫时间</span>
                        </span>
                        <span v-else>扫描已完成，正在进行入库操作，不会对资产造成影响</span>
                      </div>
                      <i class="el-icon-question" style="cursor: pointer">暂停扫描</i>
                    </el-tooltip>
                  </span>
                  <el-progress v-if="scope.row['status'] == 1 || scope.row['status'] == 4" :text-inside="true"
                    :stroke-width="12" :percentage="parseFloat(scope.row['progress'])"
                    :status="setProgressColor(scope.row['status'])"></el-progress>
                </span>
                <span v-else>
                  <!-- 漏洞扫描 -->
                  <span v-if="scope.row['is_audit'] == 1">
                    <span v-if="scope.row['status'] == 0" class="grayLine">等待扫描</span>
                    <span v-if="scope.row['status'] == 1" class="blueLine">扫描中</span>
                    <span v-if="scope.row['status'] == 2" class="greenLine">扫描完成</span>
                    <span v-if="scope.row['status'] == 3" class="redLine">扫描失败</span>
                    <span v-if="scope.row['status'] == 4" class="yellowLine">
                      <el-tooltip placement="top" v-if="scope.row['status'] == 4" :open-delay="500">
                        <div slot="content">
                          <span>当前处于禁扫时间内，如需继续执行任务，请重新</span><span @click="changSetTimeBox"
                            style="color: #409eff; cursor: pointer">设置禁扫时间</span>
                        </div>
                        <i class="el-icon-question" style="cursor: pointer">暂停扫描</i>
                      </el-tooltip>
                    </span>
                    <el-progress v-if="scope.row['status'] == 1 || scope.row['status'] == 4" :text-inside="true"
                      :stroke-width="12" :percentage="parseFloat(scope.row['progress'])"
                      :status="setProgressColor(scope.row['status'])"></el-progress>
                  </span>
                  <span class="grayLine" v-else-if="scope.row['is_audit'] == 0">等待审核</span>
                  <span v-else>
                    <el-tooltip placement="top" class="item" effect="light" popper-class="chainClass" :open-delay="500">
                      <span slot="content">
                        <span>
                          <span>驳回原因：</span><br />
                          <span>{{ scope.row.reason }}</span><br />
                          <span>
                            请进入<el-button @click="goIndex(scope.row.reason)" type="text"
                              style="font-size: 12px">资产台账</el-button>进行核查
                          </span>
                        </span>
                      </span>
                      <span class="redLine"><i class="el-icon-question" style="cursor: pointer">审核驳回</i></span>
                    </el-tooltip>
                  </span>
                </span>
              </span>
              <span v-else-if="item.name == 'type'">{{
                scope.row['type'] == 1 ? '周期任务' : '立即执行'
              }}</span>
              <span v-else-if="item.name == 'task_type' && scope.row[item.name] == 1">资产扫描{{ scope.row['type'] == 1 ?
                '-周期任务' : '' }}</span>
              <span v-else-if="item.name == 'task_type' && scope.row[item.name] == 2">漏洞扫描{{ scope.row['type'] == 1 ?
                '-周期任务' : '' }}</span>
              <span v-else-if="item.name == 'use_seconds'">{{
                secondsFormat(scope.row[item.name] > 0 ? scope.row[item.name] : 1)
              }}</span>
              <span v-else-if="item.name == 'result'">{{ getResult(scope.row) }}</span>
              <span v-else-if="item.name == 'op'">{{
                scope.row[item.name] ? scope.row[item.name]['name'] : '-'
              }}</span>
              <span v-else-if="item.name == 'poc_scan_type'">{{
                scope.row[item.name] || scope.row[item.name] == 0
                  ? poc_scan_typeMap[scope.row[item.name]]
                  : '-'
              }}</span>
              <span v-else-if="item.name == 'is_domain_scan'">{{
                scope.row[item.name] == 1 ? '域名' : 'IP'
              }}</span>
              <span v-else-if="item.name == 'scan_engine'">
                <span v-if="scope.row[item.name] == 1"> Trascanner </span>
                <span v-else>{{
                  scope.row[item.name] && scope.row[item.name] == 2
                    ? 'Goscanner,Trascanner'
                    : 'Goscanner'
                }}</span>
              </span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="180">
            <template slot-scope="scope">
              <!-- <span v-if="task_type == 2">
                <el-button type="text" size="small" v-if="scope.row['status'] == 0" @click="nowStartRun(scope.row.id)">置顶</el-button>
                <el-button type="text" size="small" v-if="scope.row['status'] == 0" @click="upDownOne(scope.row.id, 'up')">上移</el-button>
                <el-button type="text" size="small" style="margin-right: 10px" v-if="scope.row['status'] == 0" @click="upDownOne(scope.row.id, 'down')">下移</el-button>
              </span> -->
              <!-- v-if="scope.row['status'] == 2 || scope.row['is_audit'] == 2" -->
              <span>
                <!-- <el-button type="text" size="small" @click="remindAgain(scope.row.id)" id="scan_remind" v-if="scope.row['is_audit'] == 0 && task_type == 2">再次提醒</el-button> -->
                <!-- <el-button type="text" size="small" v-if="task_type == 2 && (scope.row['status'] == 2 || scope.row['is_audit'] == 2) && ( scope.row.scan_engine != 1 && scope.row.scan_engine != 2 )" @click="againRun(scope.row.id)" id="scan_again">再次执行</el-button> -->
                <el-button type="text" size="small" v-if="
                  task_type == 2 &&
                  (scope.row['status'] == 2 || scope.row['is_audit'] == 2) &&
                  (user.role == 2 ||
                    (user.role != 2 && scope.row.scan_engine != 1 && scope.row.scan_engine != 2))
                " @click="againRun(scope.row.id)" id="scan_again">再次执行</el-button>
                <!-- <el-button type="text" size="small" v-if="task_type == 2 && (scope.row['status'] == 2 || scope.row['is_audit'] == 2)" @click="againRun(scope.row.id)" id="scan_again">再次执行</el-button> -->
                <el-button type="text" size="small" v-if="
                  task_type == 1 &&
                  (scope.row['status'] == 2 || scope.row['is_audit'] == 2) &&
                  !(scope.row['is_define_port'] == 0 && scope.row['port_range'] == 1)
                " @click="againRun(scope.row.id)" id="scan_again">再次执行</el-button>
                <el-button type="text" size="small" v-if="task_type == 1"
                  @click="viewList(scope.row.id, scope.row.status)" id="scan_info">查看详情</el-button>
                <el-button type="text" size="small" v-if="task_type == 2" @click="viewList(scope.row.id)"
                  id="scan_info">查看详情</el-button>
                <el-button type="text" size="small" @click="removeOne('one', scope.row.id)" id="scan_del">删除</el-button>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" v-if="currentPageIsShow"
        :current-page="currentPage" :page-sizes="pageSizeArr" :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper" :total="total">
      </el-pagination>
    </div>
    <taskAddEditDialog :user="user" @validateConfirm="validateConfirm" :showList="showValidateList"
      :dialogFormVisible="dialogFormVisible" @insertSaveAfter="insertSaveAfter" :task_type="task_type"
      :zhouqiIs="zhouqiIs" :editIs="editIs" :isTypes="isTypes" :ruleForm="ruleForm" :rules="rules"
      :addIsTrue="addIsTrue" @dialogFormVisibleClose="dialogFormVisibleClose" :portGroupsNoPageArr="portGroupsNoPageArr"
      :isdisabled="isdisabled" :isScanId="isScanId" />
    <assetsValidate :dialogVisible="assetsValidDialogVisible" :validateType="validateType" @copyText="copyText"
      :list="errorList" @close="assetsValidDialogVisible = false" />
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="85px">
          <el-form-item label="任务名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入任务名称"></el-input>
          </el-form-item>
          <el-form-item label="任务计划：" prop="type">
            <el-select filterable clearable v-model="formInline.type"
              @change="selectChange($event, 'type', typeArr, true, false)" placeholder="请选择">
              <el-option v-for="item in typeArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务状态：" prop="status">
            <el-select filterable clearable v-model="formInline.status"
              @change="selectChange($event, 'status', statusArr, true, false)" placeholder="请选择">
              <el-option v-for="item in statusArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发起人：" prop="op_id">
            <el-select filterable clearable v-model="formInline.op_id"
              @change="selectChange($event, 'op_id', userArr, true, false)" placeholder="请选择">
              <el-option v-for="item in userArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间：" prop="created_at_range">
            <el-date-picker v-model="formInline.created_at_range" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间：" prop="end_at_range">
            <el-date-picker v-model="formInline.end_at_range" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="scan_filter_repossess">重置</el-button>
          <el-button class="highBtn" @click="checkFuncList" id="scan_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
const throttle = (func, wait = 50) => {
  // 上一次执行该函数的时间
  let lastTime = 0
  return function (...args) {
    // 当前时间
    let now = +new Date()
    // 将当前时间和上一次执行函数时间对比
    // 如果差值大于设置的等待时间就执行函数
    if (now - lastTime > wait) {
      lastTime = now
      func.apply(this, args)
    }
  }
}
import assetsValidate from '../assetsView/assetsValidate.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import taskAddEditDialog from './taskAddEditDialog.vue'
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  nowStartRunningTask,
  orderTask,
  delScansRunning,
  getVulnerability,
  remind,
  getVulnerabilityDetails,
  deleteVulnerability
} from '@/api/apiConfig/api.js'
import { portGroupsNoPage } from '@/api/apiConfig/port.js'
import { updatePocRiskIp } from '@/api/apiConfig/poc.js'
import {
  startRunningTask,
  pauseRunningTask,
  getAllUser,
  scanTaskDetail,
  deleteTask,
  getAllTaskList
} from '@/api/apiConfig/discovery.js'

export default {
  components: { taskAddEditDialog, tableTooltip, hightFilter, assetsValidate },
  props: ['task_type', 'wait_confirm'],
  data() {
    return {
      againRun: null,
      poc_scan_typeMap: {
        0: '全部PoC',
        1: '指定PoC范围',
        2: '指定PoC分组',
        3: '暴力破解',
        4: '系统扫描',
        5: 'web扫描',
        6: '默认'
      },
      UpdatePocRiskBtnLoading: false,
      showValidateList: [],
      correctList: [],
      errorList: [],
      assetsValidDialogVisible: false,
      validateType: '',
      highTabShow: [
        {
          label: '任务名称',
          name: 'name',
          type: 'input'
        },
        {
          label: '任务计划',
          name: 'type',
          type: 'select'
        },
        {
          label: '任务状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '发起人',
          name: 'op_id',
          type: 'select'
        },
        {
          label: '开始时间',
          name: 'created_at_range',
          type: 'date'
        },
        {
          label: '结束时间',
          name: 'end_at_range',
          type: 'date'
        }
      ],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      currentPercent: 0,
      progressBar: false,
      wait_num: 0,
      last_rist_time: 0,
      loading: false,
      checkedAll: false,
      highCheckdialog: false,
      pauseAndStart: true,
      finshed_num: 0,
      wtLength: 0,
      editIs: true,
      zhouqiIs: false,
      changtingFlag: false,
      addIsTrue: true,
      transferProp: {},
      transferData: [],
      checkedArr: [],
      userArr: [],
      statusArr: [
        {
          id: 1,
          name: '扫描中'
        },
        {
          id: 0,
          name: '等待扫描'
        },
        {
          id: 2,
          name: '扫描完成'
        },
        {
          id: 3,
          name: '扫描失败'
        }
        // {
        //   id: 4,
        //   name: '暂停扫描'
        // }
      ],
      typeArr: [
        {
          id: 0,
          name: '立即执行'
        },
        {
          id: 1,
          name: '周期任务'
        }
      ],
      portGroupsNoPageArr: [],
      portGroupsNoPageArrs: [],
      scanId: '', //全部常用端口的id
      isScanId: '', //0-65535端口的id
      isdisabled: false,
      formInline: {
        name: '',
        type: '',
        type_name: '',
        status: '',
        created_at_range: [],
        end_at_range: [],
        user_id: '',
        op_id: ''
      },
      dialogFormVisible: false,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      currentPage: 1,
      data: [],
      filterMethod(query, item) {
        return item.pinyin.indexOf(query) > -1
      },
      rules: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        ips: [{ required: true, message: '请输入或上传ip信息', trigger: 'change' }],
        domains: [{ required: true, message: '请上传域名信息', trigger: 'change' }],
      },
      ruleForm: {
        bandwidth: '1000', // 扫描带宽
        name: '',
        task_type: '', // 任务分类 1 资产扫描 2 漏洞扫描,
        ip_type: 1, // ipv4: 1; ipv6:2
        protocol_concurrency: 0, // 协议识别并发数，整型，0,2,4,8,10,12,14,16,18,20,22,24,26,28,30,100,200,300,
        scan_type: 1, // 扫描类型 0为精准 1为极速,
        type: 5, // 类型 默认6 2周期扫描 3月 4周 5天 6一次,
        poc_group_ids: '', // poc分组id,
        port_group_ids: '', // port分组id,
        is_domain_scan: '', // 1域名 2ip
        // poc_scan_type: 0, // "0全部poc  1指定poc",
        poc_ids: [], // poc扫描指定poc，若poc范围选择[全部poc],则不传此字段
        ips: null,
        domains: null,
        ping_switch: false, // 开启ping资产识别 默认0 关闭 1 打开,
        web_logo_switch: false, // 0不开启 1开启
        scan_range: this.task_type == 1 ? 0 : 2, // 扫描范围,0手动输入的ip 1上传的ip 2已经认证的资产ip 3根据ip段筛选, 给前端回显用的字段 4 全部poc 5 指定poc
        operate_company_id: '', // 安服角色需要此id
        define_port_protocols: [], //自定义协议
        define_ports: null, //自定义端口
        scan_engine: '',
        day_of_x: '', // 日期 仅仅月和周的时候传
        schedule_time: '', // 时间
        table_assets_type: 0 // ip获取方式
      },
      dialogFormVisibleFenzu: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      tableData: [],
      currentPageIsShow: true,
      tableHeader: [
        {
          label: '任务名称',
          name: 'name',
          fixed: 'left',
          minWidth: '200'
        },
        {
          label: '任务计划',
          name: 'type',
          minWidth: '80'
        },
        {
          label: '扫描引擎',
          name: 'scan_engine',
          minWidth: '90',
          path: ['/leakScan']
        },
        {
          label: '扫描场景',
          name: 'poc_scan_type',
          minWidth: '90',
          path: ['/leakScan']
        },
        {
          label: '扫描目标',
          name: 'is_domain_scan',
          minWidth: '90',
          path: ['/leakScan']
        },
        {
          label: '任务状态',
          name: 'status',
          minWidth: '90'
        },
        {
          label: '开始时间',
          name: 'start_at',
          minWidth: '120'
        },
        {
          label: '结束时间',
          name: 'end_at',
          minWidth: '120'
        },
        {
          label: '任务耗时',
          name: 'use_seconds',
          minWidth: '80'
        },
        {
          label: '扫描结果',
          name: 'result',
          minWidth: '160'
        },
        {
          label: '发起人',
          name: 'op',
          minWidth: '80'
        }
      ],
      ww: 0, //下面为扫描动画
      wh: 0,
      c: '',
      ctx: {},
      center: {},
      color_gold: '',
      deg_to_pi: '',
      time: 0,
      enemies: [],
      runningTask: null,
      user: {
        role: ''
      },
      currentId: '',
      runningFuncTimer: null,
      highlist: null,
      userIsOpen: true, // 权限控制
      isTypes: 'true',
      is_domain_scan: 2
    }
  },
  computed: {
    ...mapState([
      'socketTimestamp',
      'websocketMessage',
      'currentCompany',
      'setTimeBox',
      'currentCompanyId'
    ]),
    ...mapGetters([
      'getterCurrentCompany',
      'getterWebsocketMessage',
      'getterSocketTimestamp',
      'getterSettime'
    ]),
    tableHeaderIsShow() {
      let arr = []
      arr = this.tableHeader.filter((item, index) => {
        return !item.path || (item.path && item.path.indexOf(this.$route.path) != -1)
      })
      return arr
    }
  },
  watch: {
    getterSettime(val) {
      // 监听禁扫时间
      setTimeout(() => {
        this.getMergeTakList()
      }, 3000)
    },
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    getterCurrentCompany(val) {
      setTimeout(() => {
        // 权限控制
        if (sessionStorage.getItem('companyInfo')) {
          let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
          this.userIsOpen = this.task_type == 2 && companyInfo.limit_poc_scan == 0 ? false : true
        } else {
          this.userIsOpen = true
        }
      }, 1000)
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getMergeTakList()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  created() {
    if (sessionStorage.getItem('queryParam')) {
      // 从查看列表页面返回的保留筛选条件等
      let queryParam = JSON.parse(sessionStorage.getItem('queryParam'))
      this.currentPageIsShow = false
      this.currentPageIsShow = true
      this.currentPage = queryParam.page
      this.pageSize = queryParam.per_page
      this.formInline.name = queryParam.name
      this.formInline.status = queryParam.status
      this.formInline.created_at_range = queryParam.created_at_range
      this.formInline.end_at_range = queryParam.end_at_range
      this.formInline.user_id = queryParam.user_id
    }
    if (sessionStorage.getItem('companyInfo')) {
      // 权限控制
      let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      this.userIsOpen = this.task_type == 2 && companyInfo.limit_poc_scan == 0 ? false : true
    } else {
      this.userIsOpen = true
    }
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      if (this.user.role == 2) {
        if (!this.currentCompany) {
          return
        }
        this.getMergeTakList()
      } else {
        this.getMergeTakList()
      }
    }
  },
  async mounted() {
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
    this.againRun = throttle(function (id) {
      this.againRunFun(id)
    }, 3000)
    this.changePortGroupsNoPageArrs()
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
  },
  methods: {
    ...mapMutations(['setTimeChangeBox', 'changeMenuId']),

    validateConfirm(errorList, correctList, validType) {
      this.assetsValidDialogVisible = true
      this.errorList = errorList
      this.correctList = correctList

      this.validateType = validType
      if (validType == 'upload') {
        this.showValidateList = errorList.concat(correctList)
      } else {
        this.showValidateList = errorList.concat(correctList).join('\n')
      }
    },
    copyText(val) {
      // 点击确定移除并复制按钮
      // 需要将输入框中的数量更新
      if (this.validateType == 'upload') {
        this.showValidateList = this.correctList
      } else {
        this.showValidateList = this.correctList.join('\n')
      }
      this.$copyText(val.join('\r')).then(
        (res) => {
          this.$message.success('复制成功')
          this.assetsValidDialogVisible = false
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    async handleMessage(res, o) {
      //处理接收到的信息
      if (
        res.cmd == 'finish_risk_ip_count' &&
        res.data &&
        res.data.user_id == this.currentCompanyId
      ) {
        // 更新风险资产
        if (res.data.progress == 100) {
          this.currentPercent = 100
        } else {
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
      if (res.cmd == 'type_threat_audit' && this.$route.path == '/leakScan') {
        // 漏洞扫描钉钉审核通过
        this.getMergeTakList()
      }
      if (
        this.task_type == 1 &&
        (res.cmd == 'scan_task_progress' || res.cmd == 'scan_task_recpmmand_progress') &&
        res.data.type != 1
      ) {
        this.runningFunc(res)
      } else if (this.task_type == 2 && res.cmd == 'scan_task_progress' && res.data.type == 1) {
        this.runningFunc(res)
      }
      if (
        this.task_type == 2 &&
        this.currentCompanyId == res.data.user_id &&
        res.cmd == 'finish_risk_ip_count'
      ) {
        // 更新风险IP数量完成
        this.getMergeTakList()
      }
    },
    // 手动更新风险资产ip
    async goUpdatePocRiskIp(isBtn) {
      this.UpdatePocRiskBtnLoading = true
      let res = await updatePocRiskIp({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.UpdatePocRiskBtnLoading = false
      })
      this.UpdatePocRiskBtnLoading = false
      if (res.code == 0) {
        // 处理刷新页面有未完成的更新任务
        if (res.data) {
          this.$message.error('正在更新风险资产IP，请勿重复操作！')
        } else {
          if (isBtn) {
            this.$message.success('操作成功！')
          }
        }
      }
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    changSetTimeBox() {
      //禁扫时间弹框
      if (this.setTimeBox) {
        this.setTimeChangeBox(false)
      } else {
        this.setTimeChangeBox(true)
      }
    },
    goIndex(data) {
      sessionStorage.setItem('menuId', '1-3-1')
      this.changeMenuId('1-3-1')
      this.$router.push({ path: '/assetsLedger' })
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    async remindAgain(id) {
      //再次提醒
      let res = await remind(id)
      if (res.code == 0) {
        this.$message({
          message: '已提醒',
          type: 'success'
        })
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选查询
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getMergeTakList()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    setProgressColor(status) {
      if (status == 4) {
        return 'warning'
      }
    },
    async highCheckIsShow() {
      this.highCheckdialog = true
      let res = await getAllUser()
      this.userArr = res.data
    },
    async getMergeTakList(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        type: this.formInline.type,
        task_type: this.task_type, // 任务分类 1 资产扫描 2 漏洞扫描,
        name: this.formInline.name,
        status: this.formInline.status, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
        created_at_range: this.formInline.created_at_range ? this.formInline.created_at_range : [],
        end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
        sort_field: 'status',
        sort_order: 'asc',
        is_schedule: 0,
        op_id: this.formInline.op_id,
        operate_company_id: this.currentCompany
      }
      this.loading = true
      let res
      if (this.task_type == 1) {
        res = await getAllTaskList(obj).catch(() => {
          this.loading = false
          this.tableData = []
          this.total = 0
          this.wait_num = 0
        })
        console.log('res1:', res);

      } else {
        res = await getVulnerability(obj).catch(() => {
          this.loading = false
          this.tableData = []
          this.total = 0
          this.wait_num = 0
        })
        console.log('res2:', res);
      }
      this.loading = false
      this.tableData = res.data && res.data.items ? res.data.items : []
      console.log('tableData2:', this.tableData);

      this.total = res.data.total
      this.wait_num = res.data.wait_num
      this.last_rist_time = res.data.last_finish_count_risk_ip_time
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    getResult(row) {
      // asset_num、rule_num都不存在或者都等于0，显示【未发现网络资产】
      // threat_num不存在或者都等于0，显示【未发现漏洞】
      let str = ''
      if (row.status == 2 || row.status == 3) {
        // 任务扫描成功或失败才显示结果
        if (this.task_type == 1) {
          // 资产
          let strSp = row.asset_num && row.asset_num / 1 > 0 ? ',' : ''
          if (row.asset_num && row.asset_num / 1 > 0) {
            str += '发现IP：' + row.asset_num
          }
          if (row.rule_num && row.rule_num / 1 > 0) {
            str += strSp + '发现组件：' + row.rule_num
          }
          if (
            (!row.asset_num || row.asset_num / 1 == 0) &&
            (!row.rule_num || row.rule_num / 1 == 0)
          ) {
            str = '未发现网络资产'
          }
        } else {
          // 漏洞
          if (row.threat_num && row.threat_num / 1 > 0) {
            str = '发现漏洞：' + row.threat_num
          } else {
            str = '未发现漏洞'
          }
        }
      } else {
        str = '-'
      }
      return str
    },
    // websocket执行
    runningFunc(res) {
      if (!('scan_poc_num' in res.data) || res.data.user_id == this.currentCompany) {
        // 漏洞核查与资产、漏洞扫描区分，scan_poc_num存在是漏洞核查任务
        if (res.data.status == 2) {
          if (res.cmd == 'scan_task_progress') {
            resetMessage.success('扫描成功！')
          }
          this.currentId = '' // 控制推送结束后仅执行一次
          this.getMergeTakList()
        } else if (res.data.status == 1) {
          // 正在扫描
          this.currentId = res.data.user_id
          if (res.data.progress == 100) {
            // 避免状态为1时进度已经达到100但是页面不提示完成信息，可手动变成99.9，状态为2时正常提示
            res.data.progress = 99.9
          }
          // 推送数据渲染到列表
          this.tableData.forEach((item, index) => {
            if (item.id == res.data.task_id) {
              this.$set(this.tableData[index], 'is_audit', 1) // 漏洞扫描审核通过后，审核状态要改变
              this.$set(this.tableData[index], 'status', res.data.status)
              this.$set(this.tableData[index], 'progress', res.data.progress)
              this.$set(this.tableData[index], 'use_seconds', res.data.use_seconds)
              this.$set(this.tableData[index], 'start_at', res.data.start_at)
            }
          })
        } else if (res.data.status == 4) {
          // 暂停扫描
          this.currentId = res.data.user_id
          // 推送数据渲染到列表
          this.tableData.forEach((item, index) => {
            if (item.id == res.data.task_id) {
              this.$set(this.tableData[index], 'status', res.data.status)
              this.$set(this.tableData[index], 'progress', res.data.progress)
              this.$set(this.tableData[index], 'use_seconds', res.data.use_seconds)
              this.$set(this.tableData[index], 'start_at', res.data.start_at)
            }
          })
        } else {
          // 3 扫描失败
          this.getMergeTakList()
        }
      }
    },
    // socket 关闭
    socketClose() {
      if (this.socket) {
        this.socket.close()
        this.socket = null
      }
    },
    dialogFormVisibleClose() {
      this.dialogFormVisible = false
    },
    async tContinue(id) {
      let obj = {
        id: id,
        operate_company_id: this.currentCompany
      }
      let res = await startRunningTask(obj)
      if (res.code == 0) {
        this.getMergeTakList()
      }
    },
    async pause(id) {
      let obj = {
        id: id,
        operate_company_id: this.currentCompany
      }
      let res = await pauseRunningTask(obj)
      if (res.code == 0) {
        this.getMergeTakList()
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getMergeTakList(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMergeTakList(true)
    },
    goToView(res) { },
    async addTaskDialog() {
      this.isTypes = 'false'
      this.isdisabled = false
      this.editIs = true
      this.zhouqiIs = false
      this.ruleForm = {
        bandwidth: '1000', // 扫描带宽
        name: '',
        task_type: '', // 任务分类 1 资产扫描 2 漏洞扫描,
        ip_type: 1, // ipv4: 1; ipv6:2
        protocol_concurrency: 0, // 协议识别并发数，整型，0,2,4,8,10,12,14,16,18,20,22,24,26,28,30,100,200,300,
        scan_type: 1, // 扫描类型 0为精准 1为极速,
        type: 6, // 类型 默认6 2周期扫描 3月 4周 5天 6一次,
        poc_group_ids: '', // poc分组id,
        is_domain_scan: this.is_domain_scan == 1 ? '域名' : 'IP', // 1域名 2ip
        port_group_ids: this.scanId, // port分组id,
        poc_scan_type: this.task_type == 2 ? 0 : '', // "漏洞扫描默认全部  0全部poc  1指定poc",
        poc_ids: [], // poc扫描指定poc，若poc范围选择[全部poc],则不传此字段
        ips: null,
        ping_switch: false, // 开启ping资产识别 默认0 关闭 1 打开,
        web_logo_switch: false, // 0不开启 1开启
        scan_range: this.task_type == 1 ? 0 : 4, // 扫描范围,0手动输入的ip 1上传的ip 2已经认证的资产ip 3根据ip段筛选, 给前端回显用的字段 4 全部poc 5 指定poc
        operate_company_id: '', // 安服角色需要此id
        define_port_protocols: [], //自定义协议
        define_ports: null, //自定义端口
        scan_engine: '',
        day_of_x: '', // 日期 仅仅月和周的时候传
        schedule_time: '', // 时间
        table_assets_type: 0 // ip获取方式
      }
      this.$forceUpdate()
      this.addIsTrue = true
      this.dialogFormVisible = true
      this.ruleForm.task_type = this.task_type
      if (this.task_type == 1) {
        this.changePortGroupsNoPageArr(this.ruleForm.port_group_ids)
      }
    },
    async insertSaveAfter() {
      this.dialogFormVisible = false
      this.getMergeTakList()
    },
    resetForm() {
      this.formInline = {
        name: '',
        status: '',
        created_at_range: [],
        user_id: ''
      }
    },
    // 立即执行
    async nowStartRun(id) {
      let res = await nowStartRunningTask({ id: id, operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.$message.success('操作成功!')
        this.getMergeTakList()
      }
    },
    async upDownOne(id, action) {
      let obj = {
        id: id,
        action: action,
        operate_company_id: this.currentCompany
      }
      let res = await orderTask(obj)
      if (res.code == 0) {
        this.getMergeTakList()
      }
    },
    removeOne(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
      }
      // 0：等待扫描 1：扫描中 2：扫描完成 3：扫描失败 4：暂停扫描
      this.$confirm('确定删除任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'scan_del_cancel',
        confirmButtonClass: 'scan_del_sure',
        customClass: 'scan_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = null
          if (icon == 'more') {
            obj = {
              id: this.checkedAll
                ? []
                : this.checkedArr.map((item) => {
                  return item.id
                }),
              task_type: this.task_type, // 任务分类 1 资产扫描 2 漏洞扫描,
              name: this.formInline.name,
              status: this.formInline.status, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
              created_at_range: this.formInline.created_at_range
                ? this.formInline.created_at_range
                : [],
              end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
              is_schedule: 0,
              user_id: this.formInline.user_id,
              operate_company_id: this.currentCompany
            }
          } else {
            obj = {
              id: [id],
              task_type: this.task_type, // 任务分类 1 资产扫描 2 漏洞扫描
              name: this.formInline.name,
              status: this.formInline.status, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
              created_at_range: this.formInline.created_at_range
                ? this.formInline.created_at_range
                : [],
              end_at_range: this.formInline.end_at_range ? this.formInline.end_at_range : [],
              is_schedule: 0,
              user_id: this.formInline.user_id,
              operate_company_id: this.currentCompany
            }
          }
          let res
          if (this.$route.path == '/assetsScan') {
            res = await deleteTask(obj)
          } else {
            res = await deleteVulnerability(obj)
          }
          if (res.code == 0) {
            this.$message.success('删除成功!')
            this.currentPage = this.updateCurrenPage(
              this.total,
              icon == 'more' ? this.checkedArr : [1],
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getMergeTakList()
          }
        })
        .catch(() => { })
      setTimeout(() => {
        var del = document.querySelector('.scan_del>.el-message-box__btns')
        del.children[0].id = 'scan_del_cancel'
        del.children[1].id = 'scan_del_sure'
      }, 50)
    },
    async againRunFun(id) {
      this.isTypes = 'false'
      this.editIs = false
      this.zhouqiIs = false
      this.ruleForm = {
        bandwidth: '1000', // 扫描带宽
        name: '',
        task_type: '', // 任务分类 1 资产扫描 2 漏洞扫描,
        ip_type: 1, // ipv4: 1; ipv6:2
        protocol_concurrency: 0, // 协议识别并发数，整型，0,2,4,8,10,12,14,16,18,20,22,24,26,28,30,100,200,300,
        scan_type: 1, // 扫描类型 0为精准 1为极速,
        type: 6, // 类型 默认6 2周期扫描 3月 4周 5天 6一次,
        poc_group_ids: '', // poc分组id,
        port_group_ids: '', // port分组id,
        poc_scan_type: this.task_type == 2 ? 0 : '', // "漏洞扫描默认全部  0全部poc  1指定poc",
        is_domain_scan: this.is_domain_scan == 1 ? '域名' : 'IP', // 1域名 2ip
        poc_ids: [], // poc扫描指定poc，若poc范围选择[全部poc],则不传此字段
        ips: null,
        ping_switch: false, // 开启ping资产识别 默认0 关闭 1 打开,
        web_logo_switch: false, // 0不开启 1开启
        scan_range: this.task_type == 1 ? 0 : 2, // 扫描范围,0手动输入的ip 1上传的ip 2已经认证的资产ip 3根据ip段筛选, 给前端回显用的字段 4 全部poc 5 指定poc
        operate_company_id: '', // 安服角色需要此id
        define_port_protocols: [], //自定义协议
        define_ports: null, //自定义端口
        is_define_port: 0,
        scan_engine: '',
        day_of_x: '', // 日期 仅仅月和周的时候传
        schedule_time: '', // 时间
        table_assets_type: 0 // ip获取方式
      }
      this.addIsTrue = false
      // this.dialogFormVisible = true

      let res
      if (this.task_type == 1) {
        let data = {
          id: id,
          operate_company_id: this.currentCompany
        }
        res = await scanTaskDetail(data)
      } else {
        let data = {
          id: id,
          operate_company_id: this.currentCompany
        }
        res = await getVulnerabilityDetails(data)
      }
      let port_group_ids = ''
      if (this.task_type == 1) {
        if (res.data.is_define_port == 0) {
          if (res.data.port_range == 1) {
            port_group_ids = -2
            this.isdisabled = true
          } else {
            this.isdisabled = false
            if (res.data.port_group.length > 0) {
              port_group_ids = res.data.port_group[0].id
            }
          }
        } else {
          port_group_ids = -1
          this.isdisabled = false
        }
      } else {
        if (res.data.port_group.length > 0) {
          port_group_ids = res.data.port_group[0].id
        }
      }
      // 处理ips字段
      let ips = null
      if (this.task_type == 1) {
        // 资产扫描
        if (res.data.port_range == 1) {
          let arr = res.data.probe_infos.map((item) => {
            return item.ip
          }) //推荐资产默认端口
          // 数组去重
          let set = new Set(arr)
          let Newarr = Array.from(set)
          ips = Newarr ? Newarr.join(',') : ''
        } else if (res.data.scan_range == 3 || res.data.scan_range == 1) {
          // 根据ip段扫描
          ips = res.data.ips.map((item) => {
            return item.ip
          })
        } else if (res.data.scan_range == 0 || res.data.scan_range == 6) {
          ips = res.data.ips
            ? res.data.ips
              .map((item) => {
                return item.ip
              })
              .join(',')
            : ''
        } else {
          ips = []
        }
      } else {
        // 漏扫目标：IP段扫描，不需要转成字符串
        let newips = []
        if (res.data.scan_range == 0 || res.data.scan_range == 7 || res.data.scan_range == 1) {
          // IP段
          newips = res.data.ips.map((item) => {
            return item.ip
          })
        }
        if (res.data.scan_range == 8) {
          newips = res.data.ips.map((item) => {
            return item.ip
          })
        }
        ips = newips
      }
      let poc_ids = []
      if (res.data.poc_scan_type == 1) {
        poc_ids = res.data.poc
          ? res.data.poc.map((item) => {
            return item.id
          })
          : []
      } else if (res.data.poc_scan_type == 2) {
        poc_ids = res.data.poc
          ? res.data.poc_group.map((item) => {
            return item.id
          })
          : []
      } else {
        poc_ids = []
      }
      let define_ports = []
      if (res.data.define_ports) {
        define_ports = res.data.define_ports.join(',')
      } else {
        define_ports = ''
      }
      let rawgrab_ports = []
      if (res.data.port_range == 1 && res.data.probe_infos) {
        rawgrab_ports = res.data.probe_infos
        rawgrab_ports.forEach((val) => {
          delete val.id
          delete val.task_id
        })
      }
      let urls = []
      if (res.data.scan_range != 6 && res.data.hosts) {
        urls = res.data.hosts.map((item) => {
          return item.urls.toString()
        })
      }
      this.ruleForm = {
        id: res.data.id,
        bandwidth: res.data.bandwidth, // 扫描带宽
        name: res.data.name,
        task_type: res.data.task_type, // 任务分类 1 资产扫描 2 漏洞扫描,
        ip_type: res.data.ip_type, // ipv4: 1; ipv6:2
        protocol_concurrency: res.data.protocol_concurrency, // 协议识别并发数，整型，0,2,4,8,10,12,14,16,18,20,22,24,26,28,30,100,200,300,
        scan_type: res.data.scan_type, // 扫描类型 0为精准 1为极速,
        type: res.data.type, // 类型 默认6 2周期扫描 3月 4周 5天 6一次,
        poc_group_ids: '', // poc分组id,
        is_domain_scan: res.data.is_domain_scan == 1 ? '域名' : 'IP', // 1域名 2ip
        port_group_ids: port_group_ids, // port分组id,
        poc_scan_type: this.task_type == 1 ? 0 : res.data.poc_scan_type, // "漏洞扫描默认全部  0全部poc  1指定poc",
        ips: ips,
        file_name: res.data.file_name,
        ping_switch: res.data.ping_switch == 1 ? true : false, // 开启ping资产识别 默认0 关闭 1 打开,
        web_logo_switch: res.data.web_logo_switch == 1 ? true : false, // 0不开启 1开启
        scan_range: res.data.scan_range == 6 ? 0 : res.data.scan_range, // 扫描范围,0手动输入的ip 1上传的ip 2已经认证的资产ip 3根据ip段筛选, 给前端回显用的字段 4 全部poc 5 指定poc
        poc_ids: poc_ids,
        operate_company_id: this.currentCompany, // 安服角色需要此id
        define_port_protocols: res.data.define_port_protocols ? res.data.define_port_protocols : [], //自定义协议
        define_ports: define_ports, //自定义端口
        is_define_port: res.data.is_define_port,
        urls: urls,
        port_range: res.data.port_range,
        rawgrab_ports: rawgrab_ports,
        scan_engine: res.data.scan_engine,
        day_of_x: '', // 日期 仅仅月和周的时候传
        schedule_time: '', // 时间
        table_assets_type: 0 // ip获取方式
      }
      this.is_domain_scan = res.data.is_domain_scan
      if (this.task_type == 1) {
        this.changePortGroupsNoPageArr(port_group_ids)
      }
      this.dialogFormVisible = true
    },
    async changePortGroupsNoPageArrs() {
      let groupres = await portGroupsNoPage({ operate_company_id: this.currentCompany }).catch(
        () => {
          this.loading = false
        }
      )
      this.portGroupsNoPageArr = Object.assign([], groupres.data)
      this.portGroupsNoPageArrs = Object.assign([], groupres.data)
      if (this.portGroupsNoPageArr) {
        this.portGroupsNoPageArr.forEach((item) => {
          if (item.name == '全部常用端口') {
            this.ruleForm.port_group_ids = item.id
            this.scanId = item.id
          }
          if (item.name == '0-65535') {
            this.isScanId = item.id
          }
        })
      }
    },
    // 扫描端口的选择列表
    async changePortGroupsNoPageArr(val) {
      this.portGroupsNoPageArr = Object.assign([], this.portGroupsNoPageArrs)
      // this.portGroupsNoPageArr.shift({id:'',name:'自定义'},{id:-2,name:'列表端口'})
      if (this.portGroupsNoPageArr) {
        if (val == -2) {
          this.portGroupsNoPageArr.unshift({ id: '', name: '自定义' }, { id: -2, name: '列表端口' })
        } else {
          this.portGroupsNoPageArr.unshift({ id: '', name: '自定义' })
        }
      }
    },
    viewList(id, status) {
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        task_type: this.task_type, // 任务分类 1 资产扫描 2 漏洞扫描,
        name: this.formInline.name,
        status: this.formInline.status, // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
        created_at_range: this.formInline.created_at_range,
        end_at_range: this.formInline.end_at_range,
        user_id: this.formInline.user_id,
        operate_company_id: this.currentCompany
      }
      sessionStorage.setItem('queryParam', JSON.stringify(obj))
      if (this.task_type == 1) {
        this.$router.push({
          path: '/alreadyTask_viewlist',
          query: {
            id: id,
            task_type: this.task_type,
            status: status
          }
        })
      } else if (this.task_type == 2) {
        this.$router.push({
          path: '/taskRepairLeakScan',
          query: {
            id: id,
            task_type: this.task_type
          }
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;

  .numWrap {
    position: absolute;
    right: 0;
    top: -80px;

    .waitNum {
      display: inline-block;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
      border-radius: 2px;
      padding: 4px 10px;
      margin-left: 10px;
      border-left: 2px solid #2677ff;

      .num {
        font-weight: 500;
        color: #2677ff;
        margin-right: 0px;
      }
    }
  }

  .numWrapWait {
    top: -140px;
  }

  /deep/.home_header {
    position: relative;
    height: 100%;

    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;

      &>div {
        display: flex;
        align-items: center;

        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .el-input {
          width: 240px;
        }

        &>span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }

    .result {
      position: relative;
      padding: 47px 0 0 24px;
      flex: 0 0 auto;

      .normalBtn {
        position: absolute;
        top: 16px;
        right: 20px;
      }

      .scanning {
        display: flex;
        align-items: center;

        .scanning-img {
          width: 56px;
          height: 56px;

          svg {
            font-size: 56px;
            color: #ddd;
          }
        }

        .scanning-txt {
          margin-left: 15px;
          line-height: 1;

          .txt1 {
            color: #62666c;
            font-size: 16px;
            font-weight: 400;

            .name {
              font-weight: 500;
              color: #37393c;
            }
          }

          .txt2 {
            font-size: 12px;
            font-weight: 400;
            color: #62666c;
            padding-top: 10px;

            .time {
              margin-left: 21px;
            }
          }

          .txt3 {
            width: 248px;
            height: 16px;
            margin-bottom: 14px;
            background: #e5ebf4;
          }

          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 180px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt5 {
            display: inline-block;
            width: 132px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }

      .handle {
        padding-top: 30px;
        padding-bottom: 29px;

        .line1 {
          overflow: hidden;

          .progress {
            float: left;
            width: 80%;
            height: 24px;
            background: #e5ebf4;
            border-radius: 12px;

            .el-progress.is-success .el-progress-bar__inner {
              top: 4px;
              left: 4px;
              height: 16px;
              background: -webkit-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -o-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: -moz-linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
              border-radius: 12px;
            }

            ::v-deep .el-progress-bar__outer {
              background-color: #e5ebf4;
              border-radius: 0;
            }

            ::v-deep .el-progress.is-warning .el-progress-bar__inner {
              border-radius: 0;
            }
          }

          .btns {
            display: inline-block;
            padding-left: 16px;
            font-weight: 400;
            color: #2677ff;
            cursor: pointer;

            span {
              margin-right: 12px;

              i {
                margin-right: 3px;
              }
            }
          }
        }

        .line2 {
          width: 80%;
          font-size: 14px;
          color: #999;
          margin-top: 15px;
          display: flex;
          justify-content: space-between;

          .txt3 {
            width: 179px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt4 {
            display: inline-block;
            margin-right: 20px;
            width: 114px;
            height: 14px;
            background: #e5ebf4;
          }

          .txt5 {
            display: inline-block;
            width: 86px;
            height: 14px;
            background: #e5ebf4;
          }
        }
      }
    }

    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }

    .tableWrap {
      height: calc(100% - 129px);

      .emptyClass {
        height: 100%;
        text-align: center;
        vertical-align: middle;

        svg {
          display: inline-block;
          font-size: 120px;
        }

        p {
          line-height: 25px;
          color: #d1d5dd;

          span {
            margin-left: 4px;
            color: #2677ff;
            cursor: pointer;
          }
        }
      }

      .el-progress-bar__innerText {
        vertical-align: top !important;
      }
    }

    .el-table {
      border: 0;

      td {
        border-right: transparent !important;
      }
    }

    // 定义单元格文本超出不换行
    .el-table .cell {
      overflow: hidden !important;
      white-space: nowrap !important;
    }
  }
}
</style>
