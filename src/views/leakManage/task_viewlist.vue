<template>
  <div class="container">
    <div class="headerTitle">
      <span>
        <span class="goback" @click="$router.go(-1)"
          ><i class="el-icon-arrow-left"></i>返回上一层</span
        >
        <span class="spline">/</span>
        <span>{{ headerTitle }}</span>
      </span>
      <p class="statisticnumBox">
        <span class="statisticnum"
          >存活IP：<span class="num">{{ surviveIp }}</span></span
        >
      </p>
    </div>
    <div class="home_header">
      <div class="tableWrap">
        <div class="leftTab">
          <el-tree
            :data="treeData"
            :default-expanded-keys="expandedKeys"
            node-key="name"
            accordion
            :props="defaultProps"
          >
            <div style="width: 89%" slot-scope="{ data }" v-if="data.name == '扫描目标'">
              <div class="taskContentName">
                <!-- <el-button class="normalBtnRe" type="primary" @click="copyIp(ips)" id="account_check" size="mini">一键复制</el-button> -->
                <div style="font-size: 14px; font-weight: 500; color: #37393c; margin-bottom: 4px">
                  {{ data.name }}：
                  <el-tooltip
                    effect="light"
                    class="item"
                    placement="top"
                    content="一键复制"
                    v-if="ips && ips.length != 0"
                    :open-delay="500"
                  >
                    <i
                      class="el-icon-document-copy"
                      @click="copyIp(ips)"
                      style="color: #2677ff; cursor: pointer"
                    ></i>
                  </el-tooltip>
                </div>
                <div class="taskContent">
                  {{ taskContentVeiw(data.taskContent) }}
                </div>
              </div>
              <div class="ips">
                <el-tooltip
                  effect="light"
                  popper-class="chainClass"
                  class="item myItem"
                  placement="top"
                  :disabled="ipsArrNum <= 3"
                  :open-delay="500"
                >
                  <div slot="content">
                    <div v-for="(item, index) in ips" :key="index">{{ item }}</div>
                  </div>
                  <div>
                    <div v-for="(item, index) in ipsArr" :key="index">{{ item }}</div>
                  </div>
                </el-tooltip>
                <div v-if="ipsArrNum > 3" style="margin-top: -15px; margin-bottom: 16px">...</div>
              </div>
            </div>
            <span
              class="custom-tree-node"
              slot-scope="{ data }"
              v-else-if="!data.taskContent && data.id != 1 && !data.isShow"
            >
              <!-- <el-tooltip effect="dark" enterable placement="top" :content="String(data.name)" :disabled="String(data.name).length <= 14" v-if="data.id"> -->
              <span class="title" v-if="data.id">{{ data.name }}({{ data.num }})</span>
              <!-- </el-tooltip> -->
              <span v-else class="flexBox">
                <el-tooltip
                  effect="dark"
                  enterable
                  placement="top"
                  :content="String(data.name)"
                  :disabled="String(data.name).length <= 14"
                  :open-delay="500"
                >
                  <span class="title">{{ data.name }}</span>
                </el-tooltip>
                <span class="num">{{ data.num }}</span>
              </span>
            </span>
            <div style="width: 89%" slot-scope="{ data }" v-else-if="!data.isShow">
              <div v-if="!data.taskContent" class="myMargin">{{ data.name }}</div>
              <div v-else>
                <div class="mytitle">{{ data.name }}：</div>
                <!-- <el-tooltip effect="dark" enterable placement="top" :content="String(data.taskContent)" :disabled="String(data.taskContent).length <= 8"> -->
                <span class="taskContentBox">
                  <span v-if="data.name == '任务耗时'" class="taskContent">{{
                    secondsFormat(data.taskContent)
                  }}</span>
                  <span v-else-if="data.name == '扫描类型'" class="taskContent">{{
                    scanTypeMap[data.taskContent]
                  }}</span>
                  <span v-else class="taskContent">{{ data.taskContent }}</span>
                </span>
                <!-- </el-tooltip> -->
              </div>
            </div>
          </el-tree>
        </div>
        <div class="rightWrap">
          <div class="taskResults"> 任务扫描结果 </div>
          <div class="filterTab">
            <div style="display: flex; align-items: center">
              <div class="confirmBox">
                <el-radio-group v-model="showType">
                  <el-radio-button
                    :label="item.value"
                    v-for="(item, index) in showTypeOptions"
                    :key="index"
                    @click="showTypeChange(v)"
                    >{{ item.label }}</el-radio-button
                  >
                </el-radio-group>
              </div>
              <!-- <span>
                <i class="icon-switch" :class="[v.icon, {'active': v.value == showType}]"
                v-for="v in showTypeOptions" :key="v.value" @click="showTypeChange(v)"></i>
              </span> -->
              <el-input
                v-model="formInline.keyword"
                @keyup.enter.native="checkFuncList"
                clearable
                placeholder="请输入IP或组件查询"
                id="scan_info_keycheck"
              >
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
              </el-input>
              <span
                @click="highCheckIsShow"
                id="scan_info_filter"
                style="width: 80px; display: inline-block"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <div>
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAll"
                @change="checkAllChange"
                id="scan_info_all"
                >选择全部</el-checkbox
              >
              <el-button
                class="normalBtnRe"
                type="primary"
                @click="exportList"
                id="scan_info_export"
                >导出</el-button
              >
            </div>
          </div>
          <hightFilter
            id="hightFilter"
            :highTabShow="highTabShow"
            :highlist="highlist"
            @highcheck="highCheck"
          ></hightFilter>
          <div class="rightInfo" v-loading="loading" style="padding: 0px 20px 0px 15px">
            <div v-if="tableData && tableData.length == 0" class="emptyClass">暂无数据</div>
            <ul class="asset-list" v-show="tableData && tableData.length > 0 && showType == 'card'">
              <li v-for="item in tableData" :key="item.id">
                <div>
                  <p>
                    <el-checkbox v-model="item.checked"></el-checkbox>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="item.ip"
                      placement="top"
                      :open-delay="500"
                    >
                      <span
                        class="ipTitle"
                        @click="getipInfo(item.id)"
                        v-html="changeColor(item.ip, highLightKeyword)"
                      ></span>
                    </el-tooltip>
                  </p>
                  <p
                    >备注信息：
                    <el-tooltip
                      class="item ipContent"
                      effect="dark"
                      placement="top"
                      v-if="item.name"
                      :open-delay="500"
                    >
                      <span slot="content">{{ item.name }}</span>
                      <span>{{ item.name }}</span>
                    </el-tooltip>
                    <span v-else>-</span>
                  </p>
                  <p
                    >开放端口：
                    <!-- <el-tooltip class="item ipContent" effect="dark" placement="top" v-if="item.port_size" :open-delay="500"> -->
                    <!-- <span slot="content">{{item.port_size}}</span> -->
                    <span v-if="item.port_size">{{ item.port_size }}</span>
                    <!-- </el-tooltip> -->
                    <span v-else>-</span>
                  </p>
                  <p
                    >地理位置：
                    <el-tooltip
                      class="item ipContent"
                      effect="dark"
                      placement="top"
                      v-if="item.city"
                      :open-delay="500"
                    >
                      <span slot="content">{{ item.city }}</span>
                      <span>{{ item.city }}</span>
                    </el-tooltip>
                    <span v-else>-</span>
                  </p>
                  <!-- <p>管理单元：<span>{{item.info}}</span></p>
                  <p>业务系统：<span>{{item.info}}</span></p>
                  <p>负责人：<span>{{item.info}}</span></p>
                  <p>机房信息：<span>{{item.info}}</span></p>
                  <p>自定义标签：<span>{{item.info}}</span></p>
                  <p>管理单元：<span>{{item.info}}</span></p> -->
                </div>
                <ul>
                  <li>
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-a-bianzu13beifen4"></use>
                    </svg>
                    <div v-if="item.rule_infos.rule5.length > 0">
                      <div class="ruleInfos">
                        <span
                          v-for="(ru, index) in item.rule_infos.rule5"
                          :key="index"
                          v-html="changeColor(ru, highLightKeyword)"
                        ></span>
                      </div>
                      <el-popover
                        placement="top"
                        width="315"
                        style="padding-right: 0px !important; padding-left: 0px !important"
                        popper-class="rulePopover"
                        trigger="click"
                      >
                        <div style="font-size: 12px">
                          <div>"业务层"</div>
                          <div class="myruleItemBox">
                            <span
                              class="myruleItem"
                              v-for="(ru, index) in item.rule_infos.rule5"
                              :key="index"
                              >{{ ru }}</span
                            >
                          </div>
                        </div>
                        <div slot="reference" class="ruleSumBox ruleSumBox1"
                          ><span class="ruleSum"
                            >共
                            <span style="font-weight: 500; color: #2677ff">{{
                              item.rule_infos.rule5.length
                            }}</span>
                            条</span
                          ></div
                        >
                      </el-popover>
                    </div>
                    <span v-else>业务层没有组件</span>
                  </li>
                  <li>
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-a-bianzu13beifen3"></use>
                    </svg>
                    <div v-if="item.rule_infos.rule4.length > 0">
                      <div class="ruleInfos">
                        <span
                          v-for="(ru, index) in item.rule_infos.rule4"
                          :key="index"
                          v-html="changeColor(ru, highLightKeyword)"
                        ></span>
                      </div>
                      <el-popover
                        placement="top"
                        width="315"
                        style="padding-right: 0px !important; padding-left: 0px !important"
                        popper-class="rulePopover"
                        trigger="click"
                      >
                        <div style="font-size: 12px">
                          <div>"支撑层"</div>
                          <div class="myruleItemBox">
                            <span
                              class="myruleItem"
                              v-for="(ru, index) in item.rule_infos.rule4"
                              :key="index"
                              >{{ ru }}</span
                            >
                          </div>
                        </div>
                        <div slot="reference" class="ruleSumBox ruleSumBox2"
                          ><span class="ruleSum"
                            >共
                            <span style="font-weight: 500; color: #2677ff">{{
                              item.rule_infos.rule4.length
                            }}</span>
                            条</span
                          ></div
                        >
                      </el-popover>
                    </div>
                    <span v-else>支撑层没有组件</span>
                  </li>
                  <li>
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-a-bianzu13beifen2"></use>
                    </svg>
                    <div v-if="item.rule_infos.rule3.length > 0">
                      <div class="ruleInfos">
                        <span
                          v-for="(ru, index) in item.rule_infos.rule3"
                          :key="index"
                          v-html="changeColor(ru, highLightKeyword)"
                        ></span>
                      </div>
                      <el-popover
                        placement="top"
                        width="315"
                        style="padding-right: 0px !important; padding-left: 0px !important"
                        popper-class="rulePopover"
                        trigger="click"
                      >
                        <div style="font-size: 12px">
                          <div>"服务层"</div>
                          <div class="myruleItemBox">
                            <span
                              class="myruleItem"
                              v-for="(ru, index) in item.rule_infos.rule3"
                              :key="index"
                              >{{ ru }}</span
                            >
                          </div>
                        </div>
                        <div slot="reference" class="ruleSumBox ruleSumBox3"
                          ><span class="ruleSum"
                            >共
                            <span style="font-weight: 500; color: #2677ff">{{
                              item.rule_infos.rule3.length
                            }}</span>
                            条</span
                          ></div
                        >
                      </el-popover>
                    </div>
                    <span v-else>服务层没有组件</span>
                  </li>
                  <li>
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-a-bianzu13beifen"></use>
                    </svg>
                    <div v-if="item.rule_infos.rule2.length > 0">
                      <div class="ruleInfos">
                        <span
                          v-for="(ru, index) in item.rule_infos.rule2"
                          :key="index"
                          v-html="changeColor(ru, highLightKeyword)"
                        ></span>
                      </div>
                      <el-popover
                        placement="top"
                        width="315"
                        style="padding-right: 0px !important; padding-left: 0px !important"
                        popper-class="rulePopover"
                        trigger="click"
                      >
                        <div style="font-size: 12px">
                          <div>"系统层"</div>
                          <div class="myruleItemBox">
                            <span
                              class="myruleItem"
                              v-for="(ru, index) in item.rule_infos.rule2"
                              :key="index"
                              >{{ ru }}</span
                            >
                          </div>
                        </div>
                        <div slot="reference" class="ruleSumBox ruleSumBox4"
                          ><span class="ruleSum"
                            >共
                            <span style="font-weight: 500; color: #2677ff">{{
                              item.rule_infos.rule2.length
                            }}</span>
                            条</span
                          ></div
                        >
                      </el-popover>
                    </div>
                    <span v-else>系统层没有组件</span>
                  </li>
                  <li>
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-a-bianzu13"></use>
                    </svg>
                    <div v-if="item.rule_infos.rule1.length > 0">
                      <div class="ruleInfos">
                        <span
                          v-for="(ru, index) in item.rule_infos.rule1"
                          :key="index"
                          v-html="changeColor(ru, highLightKeyword)"
                        ></span>
                      </div>
                      <el-popover
                        placement="top"
                        width="315"
                        style="padding-right: 0px !important; padding-left: 0px !important"
                        popper-class="rulePopover"
                        trigger="click"
                      >
                        <div style="font-size: 12px">
                          <div>"硬件层"</div>
                          <div class="myruleItemBox">
                            <span
                              class="myruleItem"
                              v-for="(ru, index) in item.rule_infos.rule1"
                              :key="index"
                              >{{ ru }}</span
                            >
                          </div>
                        </div>
                        <div slot="reference" class="ruleSumBox ruleSumBox5"
                          ><span class="ruleSum"
                            >共
                            <span style="font-weight: 500; color: #2677ff">{{
                              item.rule_infos.rule1.length
                            }}</span>
                            条</span
                          ></div
                        >
                      </el-popover>
                    </div>
                    <span v-else>硬件层没有组件</span>
                  </li>
                </ul>
              </li>
            </ul>
            <el-table
              border
              :data="tableData"
              row-key="id"
              :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
              ref="eltable"
              height="100%"
              style="width: 100%"
              @selection-change="handleSelectionChange"
              @cell-mouse-enter="showTooltip"
              @cell-mouse-leave="hiddenTooltip"
              v-show="tableData && tableData.length > 0 && showType == 'list'"
            >
              <el-table-column
                type="selection"
                align="center"
                :reserve-selection="true"
                :selectable="handleSelectable"
                width="55"
              >
              </el-table-column>
              <el-table-column
                v-for="item in tableHeader"
                :key="item.id"
                :prop="item.name"
                align="left"
                min-width="120"
                :fixed="item.fixed"
                :label="item.label"
              >
                <template v-if="item.children">
                  <el-table-column
                    v-for="v in item.children"
                    :key="v.id"
                    :prop="v.name"
                    align="left"
                    min-width="220"
                    :label="v.label"
                  >
                    <template slot-scope="scope">
                      <span
                        v-if="scope.row[item.name][v.name]"
                        v-html="changeColor(scope.row[item.name][v.name], highLightKeyword)"
                      ></span>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                </template>
                <template slot-scope="scope">
                  <span v-if="item.name == 'ip'">
                    <span
                      style="cursor: pointer"
                      @click="getipInfo(scope.row.id)"
                      v-html="changeColor(scope.row[item.name], highLightKeyword)"
                    ></span>
                  </span>
                  <span v-else>
                    {{ scope.row[item.name] || '-' }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
          </div>
          <el-pagination
            v-if="paginationIsShow"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="pageSizeArr"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="组件类型：" prop="second_cat_tag">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.second_cat_tag"
              placeholder="请选择"
              @change="selectChange($event, 'second_cat_tag', selectData.rule_type, false, true)"
            >
              <el-option
                v-for="item in selectData.rule_type"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="组件名称：" prop="rule_tags">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.rule_tags"
              placeholder="请选择"
              @change="selectChange($event, 'rule_tags', selectData.rule_name, false, true)"
            >
              <el-option
                v-for="item in selectData.rule_name"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资产状态：" prop="state">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.state"
              placeholder="请选择"
              @change="selectChange($event, 'state', stateArr, true, true)"
            >
              <el-option v-if="selectData.state.includes(1)" label="在线" :value="1"></el-option>
              <el-option v-if="selectData.state.includes(0)" label="离线" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开放端口：" prop="ports">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.ports"
              placeholder="请选择"
              @change="selectChange($event, 'ports', selectData.port_list, false, true)"
            >
              <el-option
                v-for="item in selectData.port_list"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开放服务：" prop="protocols">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.protocols"
              placeholder="请选择"
              @change="selectChange($event, 'protocols', selectData.open_service, false, true)"
            >
              <el-option
                v-for="item in selectData.open_service"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="厂商品牌：" prop="company_tags">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.company_tags"
              placeholder="请选择"
              @change="selectChange($event, 'company_tags', selectData.company_list, false, true)"
            >
              <el-option
                v-for="item in selectData.company_list"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="地理位置：" prop="city">
            <el-cascader
              ref="myCascaderCity"
              @change="city_type_change($event, 'city', selectData.area, false, true)"
              v-model="formInline.city"
              :props="netTypeProps"
              collapse-tags
              :show-all-levels="false"
              :options="selectData.area"
              clearable
              placeholder="请选择"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="IP地址段：" prop="ip">
            <el-col :span="20">
              <el-input
                v-model="formInline.ip"
                placeholder="例如：**********-100或者**********/24"
              ></el-input>
            </el-col>
            <el-col style="text-align: center" :span="4">
              <el-checkbox v-model="expect_ip" @change="expect_ip_change">去除</el-checkbox>
            </el-col>
          </el-form-item> -->
          <el-form-item v-if="expect_ip" label="" prop="expect_ip">
            <el-input
              type="textarea"
              v-model="formInline.expect_ip"
              :rows="5"
              placeholder="请输入IP段内需要去除的IP"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="scan_info_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="scan_info_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { mapGetters, mapState } from 'vuex'
import {
  taskResultExport,
  getTaskResultCondition,
  taskResultAnalyse,
  getTaskResults,
  scanTaskDetail
} from '@/api/apiConfig/discovery.js'

export default {
  components: { tableTooltip, hightFilter },

  data() {
    return {
      scanTypeMap: {
        0: '深度扫描',
        1: '极速扫描'
        // '2': 'fofa联动扫描'
      },
      stateArr: [
        { id: 1, name: '在线' },
        { id: 0, name: '离线' }
      ],
      ips: '',
      ipsArrNum: 0,
      ipsArr: [],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      highLightKeyword: '',
      isShowTooltip: false,
      paginationIsShow: true,
      checkedAll: false,
      expect_ip: false, // 去除是否选中
      netTypeProps: {
        value: 'name',
        label: 'name',
        children: 'list',
        multiple: true
      },
      portGroupsNoPageArr: [],
      city: [],
      highlist: null,
      highTabShow: [
        {
          label: '组件类型',
          name: 'second_cat_tag',
          type: 'select'
        },
        {
          label: '组件名称',
          name: 'rule_tags',
          type: 'select'
        },
        {
          label: '资产状态',
          name: 'state',
          type: 'select'
        },
        {
          label: '开放端口',
          name: 'ports',
          type: 'select'
        },
        {
          label: '开放服务',
          name: 'protocols',
          type: 'select'
        },
        {
          label: '厂商品牌',
          name: 'company_tags',
          type: 'select'
        },
        {
          label: '地理位置',
          name: 'city',
          type: 'select'
        },
        {
          label: 'IP地址段',
          name: 'ip',
          type: 'input'
        }
      ],
      formInline: {
        id: '',
        ip: '',
        expect_ip: '',
        state: [],
        rule_tags: [],
        city: [],
        ports: [],
        protocols: [],
        company_tags: [],
        second_cat_tag: [],
        keyword: ''
      },
      highCheckdialog: false,
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      expandedKeys: ['任务参数'],
      treeData: [
        {
          name: '任务参数',
          children: [
            {
              name: '任务名称',
              taskContent: ''
            },
            {
              name: '扫描端口',
              taskContent: ''
            },
            {
              name: '扫描带宽',
              taskContent: ''
            },
            {
              name: '识别并发',
              taskContent: ''
            },
            {
              name: '扫描类型',
              taskContent: ''
            },
            {
              name: '是否ping',
              taskContent: ''
            },
            // {
            //   name: '是否截图',
            //   taskContent: ''
            // },
            {
              name: '任务计划',
              taskContent: ''
            },
            {
              name: '开始时间',
              taskContent: ''
            },
            {
              name: '结束时间',
              taskContent: ''
            },
            {
              name: '任务耗时',
              taskContent: ''
            },
            {
              name: '发起人',
              taskContent: ''
            },
            {
              name: 'IP获取方式',
              taskContent: ''
            },
            {
              name: '节点',
              taskContent: ''
            },
            {
              name: '扫描目标',
              taskContent: ''
            }
          ]
        },
        // {
        //   name: '存活IP',
        //   num: ''
        // },
        {
          name: '组件',
          children: [],
          num: ''
        },
        {
          name: '端口',
          children: [],
          num: ''
        },
        {
          name: '厂商',
          children: [],
          num: ''
        }
      ],
      tableData: [],
      loading: false,
      state: [
        {
          id: 1,
          name: '在线'
        },
        {
          id: 2,
          name: '离线'
        }
      ],
      selectData: {
        rule_type: [],
        rule_name: [],
        state: [],
        port_list: [],
        open_service: [],
        company_list: [],
        area: []
      },
      checkedArr: [],
      addIsTrue: true,
      currentPage: 1,
      pageSizeArr: [12, 30, 50, 100],
      pageSize: 12,
      total: 0,
      user: null,
      tableHeader: [
        {
          label: 'IP地址',
          name: 'ip',
          fixed: 'left'
        },
        {
          label: '开放端口',
          name: 'port_size'
        },
        {
          label: '地理位置',
          name: 'city'
        },
        {
          label: '备注信息',
          name: 'name'
        },
        {
          label: '组件信息',
          name: 'rule_infos',
          children: [
            {
              label: '业务层',
              name: 'rule5'
            },
            {
              label: '支撑层',
              name: 'rule4'
            },
            {
              label: '服务层',
              name: 'rule3'
            },
            {
              label: '系统层',
              name: 'rule2'
            },
            {
              label: '硬件层',
              name: 'rule1'
            }
          ]
        }
      ],
      showTypeOptions: [
        {
          value: 'list',
          label: '列表',
          icon: 'el-icon-s-operation'
        },
        {
          value: 'card',
          label: '卡片',
          icon: 'el-icon-menu'
        }
      ],
      showType: 'card',
      surviveIp: 0 // 存活ip数
    }
  },
  watch: {
    getterCompanyChange(val) {
      this.$router.go(-1) // 安服账号切换企业需要回到一级页面
    },
    getterCurrentCompany(val) {
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      this.currentPage = 1
      if (this.user.role == 2) {
        if (this.$route.query.status == '2') {
          this.getTaskResultAnalyse(this.$route.query.id)
          this.getTaskResultData()
        } else {
          this.getScanTaskDetail(this.$route.query.id)
        }
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  created() {
    this.headerTitle = this.$route.name
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      if (this.$route.query.status == '2') {
        this.getTaskResultAnalyse(this.$route.query.id)
        this.getTaskResultData()
      } else {
        this.getScanTaskDetail(this.$route.query.id)
      }
    } else {
      if (this.$route.query.status == '2') {
        this.getTaskResultAnalyse(this.$route.query.id)
        this.getTaskResultData()
      } else {
        this.getScanTaskDetail(this.$route.query.id)
      }
    }
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
  },

  methods: {
    taskContentVeiw(val) {
      if (val == 0) {
        return '输入IP信息'
      } else if (val == 1) {
        return '上传ip信息文件'
      } else if (val == 3) {
        return '根据ip段扫描'
      } else if (val == 4) {
        return '资产台账扫描'
      } else if (val == 5) {
        return '资产台账及ip段扫描'
      }
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      if (this.$route.query.status == '2') {
        this.getTaskResultData()
      }
    },
    highCheck() {
      this.currentPage = 1
      this.formInline = Object.assign(this.highlist)
      // 地理位置单独处理
      this.city = this.formInline.city.map((item) => {
        return item[1]
      })
      if (this.$route.query.status == '2') {
        this.getTaskResultData()
      }
    },
    async getScanTaskDetail(id) {
      let obj = {
        id: id,
        operate_company_id: this.currentCompany
      }
      let res1 = await scanTaskDetail(obj)
      let ips1 = []
      if (res1.data.port_range == 1) {
        ips1 = this.goHeavy(
          res1.data.probe_infos.map((item) => {
            return item.ip
          })
        )
        this.ips = this.goHeavy(
          res1.data.probe_infos.map((item) => {
            return item.ip
          })
        )
        this.ipsArrNum = ips1.length
        if (ips1.length > 3) {
          for (var i = 0; i < 3; i++) {
            this.ipsArr.push(ips1[i])
          }
        } else {
          this.ipsArr = ips1
        }
      } else {
        if (res1.data.ips.length > 0) {
          ips1 = res1.data.ips.map((item) => {
            return item.ip
          })
          this.ips = res1.data.ips.map((item) => {
            return item.ip
          })
          this.ipsArrNum = res1.data.ips.length
          if (res1.data.ips.length > 3) {
            for (var i = 0; i < 3; i++) {
              this.ipsArr.push(ips1[i])
            }
          } else {
            this.ipsArr = ips1
          }
        }
      }
      let port_group_ids = ''
      if (res1.data.is_define_port == 1) {
        port_group_ids = '自定义'
      } else if (res1.data.port_range == 1) {
        port_group_ids = '列表端口'
      } else {
        if (res1.data.port_group.length > 0) {
          port_group_ids = res1.data.port_group[0].name
        }
      }
      if (res1.code == 0) {
        this.treeData = [
          {
            id: '1',
            name: '任务参数',
            children: [
              {
                name: '任务名称',
                taskContent: res1.data.name ? res1.data.name : ''
              },
              {
                name: '扫描端口',
                taskContent: port_group_ids
              },
              {
                name: '扫描带宽',
                taskContent: res1.data.bandwidth
              },
              {
                name: '识别并发',
                taskContent:
                  res1.data.protocol_concurrency == 0
                    ? '根据带宽动态分配协议识别并发数'
                    : res1.data.protocol_concurrency
              },
              {
                name: '扫描类型',
                taskContent: res1.data.scan_type
              },
              {
                name: '是否ping',
                taskContent: res1.data.ping_switch == 1 ? '是' : '否'
              },
              // {
              //   name: '是否截图',
              //   taskContent: res1.data.web_logo_switch == 1 ? '是' : '否'
              // },
              {
                name: '任务计划',
                taskContent: res1.data.type == 1 ? '周期任务' : '立即执行'
              },
              {
                name: '开始时间',
                taskContent: res1.data.start_at
              },
              {
                name: '结束时间',
                taskContent: res1.data.end_at
              },
              {
                name: '任务耗时',
                taskContent: res1.data.use_seconds
              },
              {
                name: '发起人',
                taskContent: res1.data.op ? res1.data.op.name : ''
              },
              {
                name: 'IP获取方式',
                taskContent: res1.data.table_assets_type == 0 ? '固定模式' : '动态模式',
                isShow: res1.data.type != 1 // false时展示该项 周期任务时展示IP获取方式
              },
              {
                name: '节点',
                taskContent: res1.data.assets_sync_node
              },
              {
                name: '扫描目标',
                taskContent: res1.data.scan_range
              }
            ]
          },
          // {
          // id: '2',
          // name: '存活IP',
          // num: ''
          // },
          {
            id: '3',
            name: '组件',
            num: '',
            children: []
          },
          {
            id: '4',
            name: '端口',
            num: '',
            children: []
          },
          {
            id: '5',
            name: '厂商',
            num: '',
            children: []
          }
        ]
      }
    },
    // 数组去重
    goHeavy(val) {
      let arr = val
      let setArr = new Set(arr)
      let newArr = Array.from(setArr)
      return newArr
    },
    async getTaskResultAnalyse(id) {
      let obj = {
        id: id,
        operate_company_id: this.currentCompany
      }
      let res1 = await scanTaskDetail(obj)
      let ips1 = []
      if (res1.data.port_range == 1) {
        ips1 = this.goHeavy(
          res1.data.probe_infos.map((item) => {
            return item.ip
          })
        )
        this.ips = this.goHeavy(
          res1.data.probe_infos.map((item) => {
            return item.ip
          })
        )
        this.ipsArrNum = ips1.length
        if (ips1.length > 3) {
          for (var i = 0; i < 3; i++) {
            this.ipsArr.push(ips1[i])
          }
        } else {
          this.ipsArr = ips1
        }
      } else {
        if (res1.data.ips.length > 0) {
          ips1 = res1.data.ips.map((item) => {
            return item.ip
          })
          this.ips = res1.data.ips.map((item) => {
            return item.ip
          })
          this.ipsArrNum = res1.data.ips.length
          if (res1.data.ips.length > 3) {
            for (var i = 0; i < 3; i++) {
              this.ipsArr.push(ips1[i])
            }
          } else {
            this.ipsArr = ips1
          }
        }
      }

      let port_group_ids = ''
      if (res1.data.is_define_port == 1) {
        port_group_ids = '自定义'
      } else if (res1.data.port_range == 1) {
        port_group_ids = '列表端口'
      } else {
        if (res1.data.port_group.length > 0) {
          port_group_ids = res1.data.port_group[0].name
        }
      }
      let res = await taskResultAnalyse({ id: id, operate_company_id: this.currentCompany })
      if (res.code == 0 && res.data && res1.code == 0) {
        this.treeData = [
          {
            id: '1',
            name: '任务参数',
            children: [
              {
                name: '任务名称',
                taskContent: res1.data.name ? res1.data.name : ''
              },
              {
                name: '扫描端口',
                taskContent: port_group_ids
              },
              {
                name: '扫描带宽',
                taskContent: res1.data.bandwidth
              },
              {
                name: '识别并发',
                taskContent:
                  res1.data.protocol_concurrency == 0
                    ? '根据带宽动态分配协议识别并发数'
                    : res1.data.protocol_concurrency
              },
              {
                name: '扫描类型',
                taskContent: res1.data.scan_type
              },
              {
                name: '是否ping',
                taskContent: res1.data.ping_switch == 1 ? '是' : '否'
              },
              // {
              //   name: '是否截图',
              //   taskContent: res1.data.web_logo_switch == 1 ? '是' : '否'
              // },
              {
                name: '任务计划',
                taskContent: res1.data.type == 1 ? '周期任务' : '立即执行'
              },
              {
                name: '开始时间',
                taskContent: res1.data.start_at
              },
              {
                name: '结束时间',
                taskContent: res1.data.end_at
              },
              {
                name: '任务耗时',
                taskContent: res1.data.use_seconds
              },
              {
                name: '发起人',
                taskContent: res1.data.op ? res1.data.op.name : ''
              },
              {
                name: 'IP获取方式',
                taskContent: res1.data.type == 1 ? (res1.data.table_assets_type == 0 ? '固定模式' : '动态模式') : '',
                isShow: res1.data.type != 1 // false时展示该项
              },
              {
                name: '节点',
                taskContent: res1.data.assets_sync_node
              },
              {
                name: '扫描目标',
                taskContent: res1.data.scan_range
              }
            ]
          },
          // {
          //   id: '2',
          //   name: '存活IP',
          //   num: res.data.ip_count ? res.data.ip_count : ''
          // },
          {
            id: '3',
            name: '组件',
            num: res.data.tag
              ? res.data.tag
                  .map((item) => {
                    return Number(item.num)
                  })
                  .reduce((pre, cur) => {
                    return pre + cur
                  }, 0)
              : '',
            children: res.data.tag ? res.data.tag : []
          },
          {
            id: '4',
            name: '端口',
            num: res.data.port
              ? res.data.port
                  .map((item) => {
                    return Number(item.num)
                  })
                  .reduce((pre, cur) => {
                    return pre + cur
                  }, 0)
              : '',
            children: res.data.port ? res.data.port : []
          },
          {
            id: '5',
            name: '厂商',
            num: res.data.company
              ? res.data.company
                  .map((item) => {
                    return Number(item.num)
                  })
                  .reduce((pre, cur) => {
                    return pre + cur
                  }, 0)
              : '',
            children: res.data.company ? res.data.company : []
          }
        ]
        this.surviveIp = res.data.ip_count ? res.data.ip_count : 0
      }
    },
    async getTaskResultData() {
      this.highLightKeyword = this.formInline.keyword
      this.highCheckdialog = false
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.id = this.$route.query.id
      this.formInline.operate_company_id = this.currentCompany
      let obj = Object.assign({}, this.formInline)
      obj.city = this.city // 地理位置
      obj.expect_ip = obj.expect_ip
        ? obj.expect_ip.split(/,|，|\s+/).filter((item) => {
            return item
          })
        : []
      this.paginationIsShow = false
      this.loading = true
      let res = await getTaskResults(obj).catch(() => {
        this.loading = false
      })
      this.loading = false
      this.paginationIsShow = true
      let arr = [] // 数据层处理
      res.data.items.forEach((item) => {
        let rule_infos = {
          rule1: [],
          rule2: [],
          rule3: [],
          rule4: [],
          rule5: []
        }
        if (item.rules) {
          item['rules'].forEach((ru) => {
            if (ru.level == '1') {
              rule_infos.rule1.push(ru.cn_product)
            } else if (ru.level == '2') {
              rule_infos.rule2.push(ru.cn_product)
            } else if (ru.level == '3') {
              rule_infos.rule3.push(ru.cn_product)
            } else if (ru.level == '4') {
              rule_infos.rule4.push(ru.cn_product)
            } else if (ru.level == '5') {
              rule_infos.rule5.push(ru.cn_product)
            }
          })
        }
        arr.push({
          ip: item.ip,
          id: item['_id'],
          name: item.name,
          port_size: item.port_size,
          rule_infos: rule_infos,
          isCheck: false
        })
      })
      this.tableData = arr
      this.total = res.data.total
      setTimeout(() => {
        //超出固定的宽度才显示共几条
        var ruleSum = document.getElementsByClassName('ruleSum')
        if (ruleSum.length != 0) {
          ruleSum.forEach((item) => {
            item.style.display = 'none'
          })
        }
        if (document.getElementsByClassName('ruleInfos')) {
          var allWidth = document.getElementsByClassName('ruleInfos')[0].clientWidth
          var ruleSums = document.getElementsByClassName('ruleInfos')
        }
        if (ruleSums.length != 0) {
          ruleSums.forEach((item, index) => {
            var spanWidth = 0
            if (item.children.length != 0) {
              item.children.forEach((v, i) => {
                spanWidth = spanWidth + v.clientWidth + 8
              })
            }
            if (spanWidth > allWidth) {
              ruleSum[index].style.display = 'block'
            }
          })
        }
      }, 50)

      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((item) => {
          if (this.showType == 'card') {
            this.$set(item, 'checked', true)
          } else {
            this.$refs.eltable.toggleRowSelection(item, true)
          }
        })
      }
    },
    // 切换地理位置
    async city_type_change(val) {
      this.city = this.$refs['myCascaderCity'].getCheckedNodes(true).map((item) => {
        return item.label
      }) // 地理位置
      this.formInline['ch_city'] = []
      val.forEach((ar) => {
        this.formInline['ch_city'].push(ar.join('/'))
      })
      this.highlist = null
    },
    handleSizeChange(val) {
      this.pageSize = val
      if (this.$route.query.status == '2') {
        this.getTaskResultData()
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      if (this.$route.query.status == '2') {
        this.getTaskResultData()
      }
    },
    async highCheckIsShow() {
      this.highCheckdialog = true
      let res = await getTaskResultCondition({
        id: this.$route.query.id,
        operate_company_id: this.currentCompany
      }) //
      this.selectData = res.data
    },
    expect_ip_change(val) {
      this.formInline.expect_ip = val ? this.formInline.expect_ip : ''
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    resetForm() {
      this.formInline = {
        ip: '',
        state: '',
        rule_tags: '',
        city: '',
        ports: '',
        protocols: '',
        company_tags: '',
        second_cat_tag: '',
        keyword: ''
      }
      this.city = []
    },
    getipInfo(id) {
      window.open(`/alreadyTask_viewlist_ipinfo?id=${id}&fromIcon=1`, '_blank') // fromIcon: 默认'0' 已完成任务列查看ip详情，传1
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((item) => {
          if (this.showType == 'card') {
            this.$set(item, 'checked', true)
          } else {
            this.$refs.eltable.toggleRowSelection(item, true)
          }
        })
      } else {
        this.tableData.forEach((item) => {
          if (this.showType == 'card') {
            this.$set(item, 'checked', false)
          } else {
            this.$refs.eltable.clearSelection()
          }
        })
      }
    },
    async exportList() {
      if (this.showType == 'card') {
        this.checkedArr = []
        this.tableData.forEach((item) => {
          if (item.checked) {
            this.checkedArr.push(item.id)
          }
        })
      }

      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      let obj = {
        id: this.$route.query.id,
        ip_id: this.checkedAll ? [] : this.checkedArr,
        operate_company_id: this.currentCompany,
        ...this.formInline
      }
      let res = await taskResultExport(obj)
      if (res.code == 0) {
        this.checkedAll = false
        this.tableData.forEach((item) => {
          this.$set(item, 'checked', false)
        })
        this.download(this.showSrcIp + res.data.url)
      }
    },
    showTypeChange(item) {
      this.showType = item.value
      this.checkAllChange()
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectionChange(val) {
      this.checkedArr = val.map((item) => item.id)
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    // 一键复制
    copyIp(val) {
      let ipText = val.join('\n')
      let that = this
      this.$copyText(ipText).then(
        function (e) {
          that.$message.success('ip已复制到剪贴板！')
        },
        function (e) {}
      )
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  // background: #fff;
  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left {
      height: 100%;
      display: flex;
      /deep/.el-textarea {
        width: 300px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 20px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px 16px 15px;
      & > div {
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrap {
      height: 100%;
      display: flex;
      justify-content: space-between;
      flex-direction: row-reverse;
      .leftTab {
        height: 100%;
        width: 20%;
        // overflow: auto;
        .el-tree {
          height: 100%;
          background-color: transparent !important;
          // overflow: auto;
          & > .el-tree-node {
            // margin-bottom: 16px;
            box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
            border-radius: 4px;
            color: #37393c;
            // max-height: calc(100% - 192px);
            // overflow: auto;
            & > .el-tree-node__content {
              background-color: #fff;
              height: 52px;
              justify-content: space-around;
              position: relative;
              .el-tree-node__expand-icon {
                position: absolute;
                top: 28%;
                right: 4px;
              }
              .is-leaf {
                display: none !important;
              }
            }
            // &:last-child > .el-tree-node__content {
            //   height: auto;
            // }
          }
          .is-expanded {
            background: #ffffff linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #f3f7ff 100%);
            height: calc(100% - 192px);
          }
        }
        .el-tree
          > .el-tree-node:first-child
          > .el-tree-node__children
          > .el-tree-node:last-child
          > .el-tree-node__content {
          height: auto;
        }
        .el-tree-node__children > .el-tree-node > .el-tree-node__content {
          // height: 36px;
          justify-content: space-around !important;
          padding-left: 0px !important;
          cursor: auto;
          .is-leaf {
            display: none !important;
          }
        }
        .custom-tree-node {
          width: 89%;
          height: 100%;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          .title {
            display: inline-block;
            width: 80%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .num {
            display: inline-block;
            width: 10%;
            text-align: right;
            padding-right: 10px;
          }
        }
      }
      .rightWrap {
        position: relative;
        width: 79%;
        box-sizing: border-box;
        // height: 100%;
        // padding: 16px 0px 16px 20px;
        background: #fff;
        .el-pagination {
          position: absolute;
          bottom: 0;
          left: 0;
          padding-right: 20px;
        }
        .rightInfo {
          height: calc(100% - 179px);
        }
        .emptyClass {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #909399;
          font-size: 12px;
        }
        .asset-list {
          width: 100%;
          height: 100%;
          padding-right: 20px;
          box-sizing: border-box;
          overflow: auto;
          display: flex;
          flex-wrap: wrap;
          // justify-content:space-between;
          & > li {
            width: 24%;
            height: 311px;
            margin-left: 1%;
            background: #ffffff;
            box-shadow: 0px 4px 20px 0px rgba(16, 33, 62, 0.08);
            border-top: 4px solid #e4ecf8;
            border-radius: 4px;
            margin-bottom: 20px;
            & > div {
              // height: 53%;
              margin-bottom: 12px;
            }
            p {
              padding: 12px 12px 0 12px;
              color: #62666c;
              display: flex;
              align-items: center;
              .el-checkbox {
                .el-checkbox__label {
                  font-size: 16px;
                  color: #37393c;
                  font-weight: 500;
                }
              }
            }
            p:nth-child(1) {
              padding-top: 16px;
              padding-bottom: 12px;
              border-bottom: 1px solid #e9ebef;
              word-break: keep-all;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            & > ul {
              height: calc(100% - 155px);
              overflow: auto;
              display: flex;
              flex-direction: column;
              width: 100%;
              border-radius: 0px 0px 4px 4px;
              li {
                display: flex;
                align-items: center;
                height: 25%;
                color: #fff;
                font-size: 12px;
                width: 100%;
                & > .icon {
                  // position: absolute;
                  // top: 6px;
                  // left: 12px;
                  font-size: 20px;
                  margin-left: 12px;
                  margin-right: 8px;
                }
                & > div {
                  width: calc(100% - 40px);
                  height: 100%;
                  position: relative;
                }
              }
              li:nth-child(1) {
                background: #d4e4ff;
              }
              li:nth-child(2) {
                background: #a8c9ff;
              }
              li:nth-child(3) {
                background: #7dadff;
              }
              li:nth-child(4) {
                background: #5192ff;
              }
              li:nth-child(5) {
                background: #2677ff;
              }
            }
          }
        }
        .el-table {
          height: 100%;
        }
      }
    }
    .el-table {
      border: 0;
    }
    .icon-switch {
      font-size: 18px;
      padding: 7px;
      vertical-align: top;
      cursor: pointer;
      border: 1px solid #d1d5dd;
      color: #a7a9ac;
      &.active {
        background: #2677ff;
        color: #fff;
        border-color: #2677ff;
      }
      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
    }
  }
}
/deep/.el-tree-node__children {
  max-height: calc(100% - 52px) !important;
  overflow: auto !important;
}
.taskContentName {
  margin-top: 16px;
  width: 100%;
  .taskContent {
    color: #62666c;
  }
}
.ips {
  color: #62666c;
  font-size: 14px;
}
.taskContentBox {
  display: inline-block;
}
.taskContentBox > span {
  margin-top: 4px;
  display: inline-block;
  width: 100%;
  color: #62666c;
  white-space: normal;
}
.confirmBox {
  margin-left: 0px !important;
  margin-right: 16px;
}
.mytitle {
  margin-top: 16px;
  font-size: 14px;
  color: #111;
}
.flexBox {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0px;
  box-sizing: border-box;
  .title {
    color: #62666c;
    font-weight: 500;
    font-size: 12px;
  }
}
.myItem > div {
  margin-top: 12px;
  margin-bottom: 12px;
}
.myItem > div:first-child {
  margin-top: 0px;
}
/deep/.el-tree > div {
  margin-top: 12px;
}
/deep/.el-tree > div:first-child {
  margin-top: 0px;
}
/deep/.is-expanded > div {
  position: relative;
}
/deep/.is-expanded > div:first-child::after {
  position: absolute;
  display: block;
  content: '';
  bottom: 0px;
  left: 5.5%;
  background-color: #e9ebef;
  height: 1px;
  width: 89%;
}
.statisticnum {
  margin-right: 12px;
  font-size: 14px;
  font-weight: 400;
  color: #62666c;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  padding: 4px 10px;
  border-left: 2px solid #2677ff;
  .num {
    font-weight: 500;
    color: #2677ff;
    margin-right: 0px;
  }
}
.statisticnumBox > .statisticnum:last-child {
  margin-right: 0px;
}
.headerTitle {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.taskResults {
  // width: 100%;
  font-weight: 500;
  color: #37393c;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  margin: 12px 16px 0px 12px;
  padding-bottom: 16px;
  // box-sizing: border-box;
  border-bottom: 1px solid #e9ebef;
}
/deep/.el-tree-node > .el-tree-node__content {
  height: auto;
}
.ipTitle {
  cursor: pointer;
  margin-left: 10px;
  font-weight: 500;
  color: #37393c;
  font-size: 15px;
  max-width: calc(100% - 25px);
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ipContent {
  color: #37393c;
  max-width: calc(100% - 80px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  // vertical-align: middle;
}
.ruleInfos {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  span {
    display: inline-block;
    padding: 4px 8px;
    margin-bottom: 5px;
    margin-top: 5px;
    margin-right: 8px;
    color: #37393c;
    font-size: 12px;
    background: #ffffff;
    border-radius: 10px;
    white-space: nowrap;
  }
}
.ruleSumBox {
  height: 100%;
  background: #7dadff;
  position: absolute;
  right: 0;
  bottom: 0;
  // padding: 0px 20px 0px 12px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  box-shadow: -2px 0px 4px 0px rgba(0, 0, 0, 0.16);
  backdrop-filter: blur(3px);
  cursor: pointer;
}
.ruleSumBox1 {
  background: rgba(212, 228, 255, 0.6);
}
.ruleSumBox2 {
  background: rgba(168, 201, 255, 0.6);
}
.ruleSumBox3 {
  background: rgba(125, 173, 255, 0.6);
}
.ruleSumBox4 {
  background: rgba(81, 146, 255, 0.6);
}
.ruleSumBox5 {
  background: rgba(38, 119, 255, 0.6);
}
.ruleSum {
  margin: 0px 12px 0px 12px;
  padding: 4px 8px;
  color: #37393c;
  font-size: 12px;
  background: #fff;
  border-radius: 10px;
  box-sizing: border-box;
  display: none;
}
.myruleItemBox {
  margin-top: 12px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.myruleItem {
  line-height: 16px;
  padding: 2px 10px;
  margin-bottom: 10px;
  background: #ffffff;
  border-radius: 14px;
  border: 1px solid #d1d5dd;
  white-space: pre-wrap;
  margin-right: 8px;
}
/deep/.el-tree > .el-tree-node > .el-tree-node__content,
/deep/.el-tree > .el-tree-node > {
  border-radius: 4px;
}
/deep/.el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: 1px solid #e1e5ed !important;
}
/deep/.el-table th.el-table__cell::after {
  display: none;
}
.copy {
  margin-left: 10px;
  cursor: pointer;
}
</style>
