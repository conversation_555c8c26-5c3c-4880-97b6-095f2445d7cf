<template>
  <div style="height: 100%">
    <div class="header">
      <span class="headerLeft" @click="$router.go(-1)"
        ><i class="el-icon-arrow-left"></i>返回上一层</span
      >
      <span>/</span>
      <span class="headerRight">未知资产详情</span>
    </div>
    <div class="recommendDetailsBox">
      <div class="recommendlLeft">
        <div class="title">任务参数</div>
        <div v-for="(item, index) in parameterList" :key="index">
          <div v-if="item.label == '扫描目标'">
            <div class="parameterList border">
              <div>{{ item.label }}</div>
              <div>{{ taskContentVeiw(item.name) }}</div>
            </div>
            <div class="ips">
              <el-tooltip effect="dark" enterable placement="top" :disabled="ipsArrNum < 3">
                <div slot="content">{{ ips }}</div>
                <div>
                  <div v-for="(v, i) in ipsArr" :key="i">{{ v }}</div>
                  <div v-if="ipsArrNum > 3">...</div>
                </div>
              </el-tooltip>
            </div>
          </div>
          <div class="parameterList" v-else>
            <div>{{ item.label }}</div>
            <el-tooltip
              effect="dark"
              enterable
              placement="top"
              :content="String(item.name)"
              :disabled="String(item.name).length <= 10"
            >
              <div v-if="item.lable == '周期类型'">{{ typeView(item.name) }}</div>
              <div v-else-if="item.lable == '扫描类型'">{{ scanTypeMap[data.taskContent] }}</div>
              <div v-else class="content">{{ item.name }}</div>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="recommendright">
        <div class="recommendHeader">
          <div>
            <el-checkbox class="checkboxAll" v-model="checkedAll" @change="checkAllChange"
              >选择全部</el-checkbox
            >
            <el-button class="normalBtnRe" type="primary">导出</el-button>
          </div>
        </div>
        <div class="recommendList">
          <el-table
            :data="tableData"
            v-loading="loading"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            @selection-change="handleSelectionChange"
            ref="eltable"
            height="100%"
            style="width: 100%"
          >
            <el-table-column
              type="selection"
              align="center"
              :reserve-selection="true"
              :show-overflow-tooltip="true"
              :selectable="handleSelectable"
              width="55"
            >
            </el-table-column>
            <el-table-column
              v-for="item in recommedData"
              :key="item.id"
              :prop="item.name"
              align="left"
              :label="item.label"
              :fixed="item.fixed"
            >
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="left">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="goDetails(scope.row.id)"
                  >查看详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="pageSizeArr"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { scanTaskDetail } from '@/api/apiConfig/discovery.js'

export default {
  name: 'zhouqiDetails',
  data() {
    return {
      scanTypeMap: {
        0: '深度扫描',
        1: '极速扫描'
        //   '2': 'fofa联动扫描'
      },
      parameterList: [
        {
          label: '任务名称',
          name: 'name'
        },
        {
          label: '扫描端口',
          name: 'start_at'
        },
        {
          label: '扫描带宽',
          name: 'bandwidth'
        },
        {
          label: '识别并发',
          name: 'protocol_concurrency'
        },
        {
          label: '扫描类型',
          name: 'scan_type'
        },
        {
          label: '是否ping',
          name: 'ping_switch'
        },
        {
          label: '是否截图',
          name: 'web_logo_switch'
        },
        {
          label: '周期类型',
          name: 'type'
        },
        {
          label: '下发时间',
          name: 'schedule_time'
        },
        {
          label: '发起人',
          name: 'op'
        },
        {
          label: '任务开关',
          name: 'switch'
        },
        {
          label: '扫描目标',
          name: 'scan_range'
        }
      ],
      keyword: '',
      checkedAll: false,
      tableData: [
        {
          ip: '213'
        }
      ],
      loading: false,
      recommedData: [
        {
          id: 1,
          label: '任务名称',
          name: 'name',
          fixed: 'left'
        },
        {
          id: 2,
          label: '任务状态',
          name: 'prot'
        },
        {
          id: 3,
          label: '开始时间',
          name: 'protocol'
        },
        {
          id: 4,
          label: '结束时间',
          name: 'subdomain'
        },
        {
          id: 5,
          label: '任务耗时',
          name: 'url'
        },
        {
          id: 6,
          label: '扫描结果',
          name: 'rule_infos'
        }
      ],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      user: {
        role: ''
      },
      ips: '',
      ipsArrNum: '',
      ipsArr: []
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      if (val) {
        // 切换账号去除全选
        this.checkedAll = false
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, false)
        })
        // this.getRecommendCluesData()
        // this.getRecommendRecordsList()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getDetails(this.$route.query.id)
      // this.getTaskResultData()
    } else {
      this.getDetails(this.$route.query.id)
      // this.getTaskResultData()
    }
  },
  methods: {
    handleCurrentChange(val) {
      this.currentPage = val
    },
    handleSizeChange(val) {
      this.pageSize = val
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    typeView(value) {
      if (value == 3) {
        return '每月重复'
      } else if (value == 4) {
        return '每周重复'
      } else if (value == 5) {
        return '每天重复'
      } else {
        return '仅执行一次'
      }
    },
    taskContentVeiw(val) {
      if (val == 0) {
        return '输入IP信息'
      } else if (val == 1) {
        return '上传ip信息文件'
      } else if (val == 3) {
        return '根据ip段扫描'
      } else if (val == 4) {
        return '资产台账扫描'
      } else if (val == 5) {
        return '资产台账及ip段扫描'
      }
    },
    // 获取详情
    async getDetails(id) {
      let data = {
        id: id,
        operate_company_id: this.currentCompany
      }
      let res = await scanTaskDetail(data)
      if (res.code == 0) {
        let ips1 = []
        if (res.data.ips.length > 0) {
          ips1 = res.data.ips.map((item) => {
            return item.ip
          })
          this.ips = res.data.ips
            .map((item) => {
              return item.ip
            })
            .join(',')
          this.ipsArrNum = res.data.ips.length
          if (res.data.ips.length > 3) {
            for (var i = 0; i < 3; i++) {
              this.ipsArr.push(ips1[i])
            }
          } else {
            this.ipsArr = ips1
          }
        }
        let port_group_ids = ''
        if (res.data.port_group.length > 0) {
          port_group_ids = res.data.port_group[0].name
        }
        this.parameterList = [
          {
            label: '任务名称',
            name: res.data.name ? res.data.name : '-'
          },
          {
            label: '扫描端口',
            name: port_group_ids
          },
          {
            label: '扫描带宽',
            name: res.data.bandwidth
          },
          {
            label: '识别并发',
            name: res.data.protocol_concurrency
          },
          {
            label: '扫描类型',
            name: res.data.scan_type
          },
          {
            label: '是否ping',
            name: res.data.ping_switch == 1 ? '是' : '否'
          },
          {
            label: '是否截图',
            name: res.data.web_logo_switch == 1 ? '是' : '否'
          },
          {
            label: '周期类型',
            name: res.data.type
          },
          {
            label: '下发时间',
            name: res.data.schedule_time
          },
          {
            label: '发起人',
            name: res.data.op ? res.data.op.name : '-'
          },
          {
            label: '任务开关',
            name: res.data.switch
          },
          {
            label: '扫描目标',
            name: res.data.scan_range
          }
        ]
      }
    }
  }
}
</script>

<style lang="less" scoped>
.header {
  position: absolute;
  top: 73px;
  .headerLeft {
    color: #2677ff;
    margin-right: 8px;
    cursor: pointer;
  }
  .headerRight {
    margin-left: 8px;
    font-weight: 500;
    color: #37393c;
    font-size: 16px;
  }
}
.recommendDetailsBox {
  display: flex;
  height: 100%;
  justify-content: space-between;
  .recommendlLeft {
    width: 20%;
    height: 100%;
    background-color: #fff;
    .title {
      height: 56px;
      width: 100%;
      padding: 0 10%;
      margin: auto;
      box-sizing: border-box;
      line-height: 56px;
      border-bottom: 1px solid #e9ebef;
    }
    .parameterList {
      height: 56px;
      width: 100%;
      padding: 0 10%;
      margin: auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e9ebef;
      box-sizing: border-box;
      .content {
        width: 50%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: right;
      }
    }
    .border {
      border-bottom: none;
    }
  }
  .recommendright {
    width: 79%;
    height: 100%;
    background-color: #fff;
    .recommendHeader {
      // width: 100%;
      text-align: right;
      align-items: center;
      padding: 16px 20px;
      // box-sizing: border-box;
      & > div {
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .recommendList {
      // width: 100%;
      height: calc(100% - 129px);
    }
  }
}
.ips {
  text-align: right;
  margin-right: 5%;
  div {
    // margin-top: 12px;
    margin-bottom: 12px;
    // white-space: pre-wrap;
  }
}
</style>
