<template>
  <div class="container">
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.name"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入任务名称检索"
            id="scan_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
        </div>
        <div>
          <el-button
            :disabled="!userIsOpen"
            class="normalBtn"
            type="primary"
            @click="addTaskDialog"
            id="scan_add"
            >新建任务</el-button
          >
        </div>
      </div>
      <div class="tableWrap" v-loading="loading">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            v-for="item in tableHeaderList"
            :key="item.id"
            :prop="item.name"
            align="left"
            :show-overflow-tooltip="true"
            :min-width="item.minWidtth"
            :label="item.label"
          >
            <template slot-scope="scope">
              <el-switch
                @change="switchChange($event, scope.row.id)"
                v-if="item.name == 'switch'"
                v-model.number="scope.row[item.name]"
                active-color="#2677FF"
                inactive-color="#E5EBF4"
                :active-value="0"
                :inactive-value="1"
                id="scan_switch"
              >
              </el-switch>
              <span v-else-if="item.name == 'name'">{{ scope.row[item.name] }}</span>
              <span v-else-if="item.name == 'type'">{{ typeView(scope.row[item.name]) }}</span>
              <span v-else-if="item.name == 'is_audit'">
                <span class="greenLine" v-if="scope.row['is_audit'] == 1"> 审核通过 </span>
                <span class="grayLine" v-else-if="scope.row['is_audit'] == 0"> 等待审核 </span>
                <span v-else>
                  <el-tooltip
                    placement="top"
                    class="item"
                    effect="light"
                    popper-class="chainClass"
                    :open-delay="500"
                  >
                    <span slot="content">
                      <span>
                        <span>驳回原因：</span><br />
                        <span>{{ scope.row.reason }}</span
                        ><br />
                        <span>
                          请进入<el-button
                            @click="goIndex(scope.row.reason)"
                            type="text"
                            style="font-size: 12px"
                            >资产台账</el-button
                          >进行核查
                        </span>
                      </span>
                    </span>
                    <span class="redLine"
                      ><i class="el-icon-question" style="cursor: pointer">审核驳回</i></span
                    >
                  </el-tooltip>
                </span>
              </span>
              <span v-else-if="item.name == 'task_type' && scope.row[item.name] == 1"
                >资产扫描</span
              >
              <span v-else-if="item.name == 'task_type' && scope.row[item.name] == 2"
                >漏洞扫描</span
              >
              <span v-else-if="item.name == 'op'">{{
                scope.row[item.name] ? scope.row[item.name]['name'] : ''
              }}</span>
              <span v-else-if="item.name == 'poc_scan_type'">{{
                scope.row[item.name] ? poc_scan_typeMap[scope.row[item.name]] : '-'
              }}</span>
              <span v-else-if="item.name == 'scan_engine'">
                <span v-if="scope.row[item.name] == 1"> Trascanner </span>
                <span v-else>{{
                  scope.row[item.name] && scope.row[item.name] == 2
                    ? 'Goscanner,Trascanner'
                    : 'Goscanner'
                }}</span>
              </span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="180">
            <template slot-scope="scope">
              <!-- <el-button type="text" size="small" @click="remindAgain(scope.row.id)" id="scan_remind" v-if="scope.row['is_audit'] == 0 && scope.row['switch'] == 0 && task_type == 2">再次提醒</el-button> -->
              <el-button
                v-if="task_type == 1"
                type="text"
                size="small"
                @click="editOne(scope.row)"
                id="scan_edit"
                >编辑</el-button
              >
              <el-button
                v-if="
                  task_type == 2 &&
                  (user.role == 2 ||
                    (user.role != 2 && scope.row.scan_engine != 1 && scope.row.scan_engine != 2))
                "
                type="text"
                size="small"
                @click="editOne(scope.row)"
                id="scan_edit"
                >编辑</el-button
              >
              <!-- <el-button type="text" size="small" @click="goDeials(scope.row.id)">查看详情</el-button> -->
              <el-button type="text" size="small" @click="removeOne(scope.row.id)" id="scan_del"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <taskAddEditDialog
      :user="user"
      @validateConfirm="validateConfirm"
      :showList="showValidateList"
      :dialogFormVisible="dialogFormVisible"
      @insertSaveAfter="insertSaveAfter"
      :task_type="task_type"
      :zhouqiIs="zhouqiIs"
      :editIs="editIs"
      :ruleForm="ruleForm"
      :rules="rules"
      :addIsTrue="addIsTrue"
      :isTypes="isTypes"
      :zhouQiSwitch="zhouQiSwitch"
      @dialogFormVisibleClose="dialogFormVisibleClose"
      :portGroupsNoPageArr="portGroupsNoPageArr"
    />
    <assetsValidate
      :dialogVisible="assetsValidDialogVisible"
      :validateType="validateType"
      @copyText="copyText"
      :list="errorList"
      @close="assetsValidDialogVisible = false"
    />
  </div>
</template>

<script>
const throttle = (func, wait = 50) => {
  // 上一次执行该函数的时间
  let lastTime = 0
  return function (...args) {
    // 当前时间
    let now = +new Date()
    // 将当前时间和上一次执行函数时间对比
    // 如果差值大于设置的等待时间就执行函数
    if (now - lastTime > wait) {
      lastTime = now
      func.apply(this, args)
    }
  }
}
import assetsValidate from '../assetsView/assetsValidate.vue'
import taskAddEditDialog from './taskAddEditDialog.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import {
  getVulnerabilityCycle,
  remindCycle,
  deleteVulnerabilityCycle,
  getVulnerabilityCycleDetails
} from '@/api/apiConfig/api.js'
import { portGroupsNoPage } from '@/api/apiConfig/port.js'
import {
  crontabTaskSwitch,
  delGrontabTask,
  scanCronTaskDetail,
  grontabTaskList
} from '@/api/apiConfig/discovery.js'

export default {
  components: { taskAddEditDialog, assetsValidate },
  props: ['task_type'], // 1 资产扫描 2 漏洞扫描,
  data() {
    return {
      loading: false,
      editOne: null,
      poc_scan_typeMap: {
        0: '全部PoC',
        1: '指定PoC范围',
        2: '指定PoC分组',
        3: '暴力破解',
        4: '系统扫描',
        5: 'web扫描',
        6: '默认'
      },
      showValidateList: [],
      correctList: [],
      errorList: [],
      assetsValidDialogVisible: false,
      validateType: '',
      userIsOpen: true, // 权限控制
      formInline: {
        rank: '',
        name: ''
      },
      ruleForm: {
        bandwidth: '1000', // 扫描带宽
        name: '',
        task_type: '', // 任务分类 1 资产扫描 2 漏洞扫描,
        ip_type: 1, // ipv4: 1; ipv6:2
        protocol_concurrency: 0, // 协议识别并发数，整型，0,2,4,8,10,12,14,16,18,20,22,24,26,28,30,100,200,300,
        scan_type: 1, // 扫描类型 0为精准 1为极速,
        type: 5, // 类型 默认0 全量扫 1:企业资产上传后扫描 2周期扫描 3月 4周 5天 6一次,
        day_of_x: '', // 日期 仅仅月和周的时候传
        schedule_time: '', // 时间
        poc_group_ids: '', // poc分组id,
        port_group_ids: '', // port分组id,
        poc_scan_type: 0, // "0全部poc  1指定poc",
        ips: null,
        scan_range: this.task_type == 1 ? 0 : 2, // 0 手动输入的ip 1上传的ip 2已经认证的资产ip 3根据ip段筛选
        ping_switch: false, // 开启ping资产识别 默认0 关闭 1 打开,
        web_logo_switch: false, // 0不开启 1开启
        define_port_protocols: [], //自定义协议
        define_ports: null, //自定义端口
        scan_engine: '',
        table_assets_type: 0 // ip获取方式
      },
      rules: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        ips: [{ required: true, message: '请输入或上传ip信息', trigger: 'change' }],
        day_of_x: [{ required: true, message: '请选择执行时间', trigger: 'change' }]
      },
      editIs: true,
      zhouqiIs: false,
      tableData: [],
      tableHeader: [
        {
          label: '任务名称',
          name: 'name',
          task_type: '',
          minWidtth: '200'
        },
        {
          label: '扫描引擎',
          name: 'scan_engine',
          minWidth: '100',
          task_type: '2'
        },
        {
          label: '扫描场景',
          name: 'poc_scan_type',
          minWidth: '70',
          task_type: '2'
        },
        {
          label: '周期类型',
          name: 'type',
          task_type: '',
          minWidtth: '80'
        },
        {
          label: '任务状态',
          name: 'is_audit',
          task_type: '2',
          minWidtth: '90'
        },
        {
          label: '下发时间',
          name: 'schedule_time', // 'created_at',
          task_type: '',
          minWidtth: '120'
        },
        {
          label: '发起人',
          name: 'op',
          task_type: '',
          minWidtth: '120'
        },
        {
          label: '任务开关',
          name: 'switch',
          task_type: '',
          minWidtth: '120'
        }
      ],
      addIsTrue: true,
      dialogFormVisible: false,
      currentPage: 1,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      user: {
        role: ''
      },
      zhouQiSwitch: '',
      portGroupsNoPageArr: [],
      isTypes: 'true'
    }
  },
  watch: {
    getterCurrentCompany(val) {
      setTimeout(() => {
        // 权限控制
        if (sessionStorage.getItem('companyInfo')) {
          let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
          this.userIsOpen = this.task_type == 2 && companyInfo.limit_poc_scan == 0 ? false : true
        } else {
          this.userIsOpen = true
        }
      }, 1000)
      this.currentPage = 1
      if (this.user.role == 2) {
        this.getTaskResultData()
      }
    },
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage']),
    tableHeaderList() {
      let arr = this.tableHeader.filter((item) => {
        return item.task_type == this.task_type || !item.task_type
      })
      arr[0].fixed = 'left'
      return arr
    }
  },
  created() {
    if (sessionStorage.getItem('companyInfo')) {
      // 权限控制
      let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      this.userIsOpen = this.task_type == 2 && companyInfo.limit_poc_scan == 0 ? false : true
    } else {
      this.userIsOpen = true
    }
    this.ruleForm.task_type = this.task_type
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      if (this.user.role == 2) {
        if (!this.currentCompany) {
          return
        }
        this.getTaskResultData()
      } else {
        this.getTaskResultData()
      }
    }
  },
  async mounted() {
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
    this.editOne = throttle(function (row) {
      this.editOneFun(row)
    }, 3000)
  },

  methods: {
    ...mapMutations(['changeMenuId']),
    validateConfirm(errorList, correctList, validType) {
      this.assetsValidDialogVisible = true
      this.errorList = errorList
      this.correctList = correctList

      this.validateType = validType
      if (validType == 'upload') {
        this.showValidateList = errorList.concat(correctList)
      } else {
        this.showValidateList = errorList.concat(correctList).join('\n')
      }
    },
    copyText(val) {
      // 点击确定移除并复制按钮
      // 需要将输入框中的数量更新
      if (this.validateType == 'upload') {
        this.showValidateList = this.correctList
      } else {
        this.showValidateList = this.correctList.join('\n')
      }
      this.$copyText(val.join('\r')).then(
        (res) => {
          this.$message.success('复制成功')
          this.assetsValidDialogVisible = false
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (res.cmd == 'type_threat_audit' && this.$route.path == '/leakScan') {
        // 漏洞扫描钉钉审核通过
        this.getTaskResultData()
      }
    },
    goIndex(data) {
      sessionStorage.setItem('menuId', '1-3-1')
      this.changeMenuId('1-3-1')
      this.$router.push({ path: '/assetsLedger' })
    },
    async remindAgain(id) {
      //再次提醒
      let res = await remindCycle(id)
      if (res.code == 0) {
        this.$message({
          message: '已提醒',
          type: 'success'
        })
      }
    },
    goDeials(id) {
      this.$router.push({
        path: '/zhouqiDetails',
        query: {
          id: id
        }
      })
    },
    typeView(value) {
      if (value == 3) {
        return '每月重复'
      } else if (value == 4) {
        return '每周重复'
      } else if (value == 5) {
        return '每天重复'
      } else {
        return '仅执行一次'
      }
    },
    checkFuncList() {
      this.currentPage = 1
      this.getTaskResultData()
    },
    async switchChange(val, id) {
      let obj = {
        id: id,
        switch: val / 1,
        operate_company_id: this.currentCompany
      }
      let res = await crontabTaskSwitch(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.getTaskResultData()
      }
    },
    async addTaskDialog() {
      this.isTypes = 'true'
      this.editIs = true
      this.zhouqiIs = false
      this.ruleForm = {
        bandwidth: '1000', // 扫描带宽
        name: '',
        task_type: '', // 任务分类 1 资产扫描 2 漏洞扫描,
        ip_type: 1, // ipv4: 1; ipv6:2
        protocol_concurrency: 0, // 协议识别并发数，整型，0,2,4,8,10,12,14,16,18,20,22,24,26,28,30,100,200,300,
        scan_type: 1, // 扫描类型 0为精准 1为极速,
        type: 5, // 类型 默认0 全量扫 1:企业资产上传后扫描 2周期扫描 3月 4周 5天 6一次,
        day_of_x: '', // 日期 仅仅月和周的时候传
        schedule_time: '', // 时间
        poc_group_ids: '', // poc分组id,
        port_group_ids: -3, // port分组id,
        poc_scan_type: 0, // "0全部poc  1指定poc",
        poc_ids: [], // poc扫描指定poc，若poc范围选择[全部poc],则不传此字段
        ips: null,
        scan_range: this.task_type == 1 ? 0 : 4, // 0 手动输入的ip 1上传的ip 2已经认证的资产ip 3根据ip段筛选
        ping_switch: false, // 开启ping资产识别 默认0 关闭 1 打开,
        web_logo_switch: false, // 0不开启 1开启
        define_port_protocols: [], //自定义协议
        define_ports: null, //自定义端口
        scan_engine: '',
        table_assets_type: 0 // ip获取方式
      }
      this.ruleForm.task_type = this.task_type
      this.addIsTrue = true
      this.dialogFormVisible = true
      if (this.task_type == 1) {
        this.changePortGroupsNoPageArr(this.ruleForm.port_group_ids)
      }
    },
    dialogFormVisibleClose() {
      this.dialogFormVisible = false
    },
    insertSaveAfter() {
      this.dialogFormVisible = false
      this.getTaskResultData()
    },
    async getTaskResultData() {
      this.loading = true
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        task_type: this.task_type, // 任务分类 1 资产扫描 2 漏洞扫描,
        sort_field: 'created_at',
        sort_order: 'desc',
        name: this.formInline.name,
        operate_company_id: this.currentCompany
      }
      let res
      if (this.task_type == 1) {
        res = await grontabTaskList(obj).catch(() => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
      } else {
        res = await getVulnerabilityCycle(obj).catch(() => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
      }
      if (res && res.code == 0 && res.data) {
        this.tableData = res.data.items
        this.total = res.data.total
      }
      this.loading = false
    },
    handleSelectionChange() {},
    handleSizeChange(val) {
      this.pageSize = val
      this.getTaskResultData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTaskResultData()
    },
    highCheck() {},
    resetForm() {
      this.formInline = {
        rank: '',
        name: ''
      }
    },
    async editOneFun(row) {
      this.zhouQiSwitch = row.switch
      this.editIs = false
      this.zhouqiIs = true
      this.ruleForm = {
        bandwidth: '1000', // 扫描带宽
        name: '',
        task_type: '', // 任务分类 1 资产扫描 2 漏洞扫描,
        ip_type: 1, // ipv4: 1; ipv6:2
        protocol_concurrency: 0, // 协议识别并发数，整型，0,2,4,8,10,12,14,16,18,20,22,24,26,28,30,100,200,300,
        scan_type: 1, // 扫描类型 0为精准 1为极速,
        type: 5, // 类型 默认0 全量扫 1:企业资产上传后扫描 2周期扫描 3月 4周 5天 6一次,
        day_of_x: '', // 日期 仅仅月和周的时候传
        schedule_time: '', // 时间
        poc_group_ids: '', // poc分组id,
        port_group_ids: '', // port分组id,
        poc_scan_type: 0, // "0全部poc  1指定poc",
        ips: null,
        scan_range: this.task_type == 1 ? 0 : 2, // 0 手动输入的ip 1上传的ip 2已经认证的资产ip 3根据ip段筛选
        ping_switch: false, // 开启ping资产识别 默认0 关闭 1 打开,
        web_logo_switch: false, // 0不开启 1开启
        operate_company_id: '', // 安服角色需要此id
        define_port_protocols: [], //自定义协议
        define_ports: null, //自定义端口
        scan_engine: '',
        table_assets_type: 0 // ip获取方式
      }
      this.addIsTrue = false
      let res
      if (this.$route.path == '/assetsScan') {
        res = await scanCronTaskDetail({ id: row.id, operate_company_id: this.currentCompany })
      } else {
        let data = {
          id: row.id,
          operate_company_id: this.currentCompany
        }
        res = await getVulnerabilityCycleDetails(data)
      }
      // ports参数说明：1.判断ports数组有值 2.判断数组[0]项的cronports_type是否包含Group字符串，包含则代表是端口组，cronports是个对象，直接取值id,不包含代表多选端口，cronports是端口数组
      let port_group_ids = ''
      if (this.task_type == 1) {
        if (res.data.is_define_port == 0) {
          if (res.data.ports.length > 0) {
            if (res.data.ports[0].cronports_type.indexOf('Group') != -1) {
              port_group_ids = res.data.ports[0].cronports.id
            } else {
              port_group_ids = res.data.ports[0].cronports.map((item) => {
                return item.id
              })
            }
          }
        } else {
          port_group_ids = 0
        }
      } else {
        if (res.data.ports.length > 0) {
          if (res.data.ports[0].cronports_type.indexOf('Group') != -1) {
            port_group_ids = res.data.ports[0].cronports.id
          } else {
            port_group_ids = res.data.ports[0].cronports.map((item) => {
              return item.id
            })
          }
        }
      }
      // 处理ips字段
      let ips = null
      if (this.task_type == 1) {
        // 资产扫描
        if (res.data.scan_range == 3) {
          // 根据ip段扫描
          ips = res.data.ips.map((item) => {
            return item.ip
          })
        } else if (res.data.scan_range == 1) {
          // 文件上传回显上传的ip
          ips = res.data.ips.map((item) => {
            return item.ip
          })
        } else if (res.data.scan_range == 0) {
          ips = res.data.ips
            ? res.data.ips
                .map((item) => {
                  return item.ip
                })
                .join(',')
            : ''
        } else {
          ips = []
        }
      } else {
        // 漏扫目标：IP段扫描，不需要转成字符串
        let newips = []
        if (res.data.scan_range == 0 || res.data.scan_range == 1 || res.data.scan_range == 7) {
          // IP段
          newips = res.data.ips.map((item) => {
            return item.ip
          })
        }
        ips = newips
      }
      let define_ports = []
      if (res.data.define_ports) {
        define_ports = res.data.define_ports.join(',')
      } else {
        define_ports = ''
      }
      this.ruleForm = {
        id: res.data.id,
        bandwidth: res.data.bandwidth, // 扫描带宽
        name: res.data.name,
        task_type: res.data.task_type, // 任务分类 1 资产扫描 2 漏洞扫描,
        ip_type: res.data.ip_type, // 任务分类 1 资产扫描 2 漏洞扫描,
        protocol_concurrency: res.data.protocol_concurrency, // 协议识别并发数，整型，0,2,4,8,10,12,14,16,18,20,22,24,26,28,30,100,200,300,
        scan_type: res.data.scan_type, // 扫描类型 0为精准 1为极速,
        type: res.data.type, // 类型 默认0 全量扫 1:企业资产上传后扫描 2周期扫描 3月 4周 5天 6一次,
        day_of_x: res.data.day_of_x, // 日期 仅仅月和周的时候传
        schedule_time: res.data.schedule_time, // 时间
        poc_ids: res.data.pocs.map((item) => {
          return item.cronpocs_id
        }), // 指定POC的id,
        poc_group_ids: res.data.poc_group_ids, // poc分组id,
        port_group_ids: port_group_ids, // 扫描端口,
        poc_scan_type: this.task_type == 1 ? 0 : res.data.poc_scan_type,
        ips: ips,
        file_name: res.data.file_name, // 用户编辑回显上传文件
        scan_range: res.data.scan_range, // 0 手动输入的ip 1上传的ip 2已经认证的资产ip 3根据ip段筛选
        ping_switch: res.data.ping_switch == 1 ? true : false, // 开启ping资产识别 默认0 关闭 1 打开,
        web_logo_switch: res.data.web_logo_switch == 1 ? true : false, // 0不开启 1开启
        operate_company_id: this.currentCompany, // 安服角色需要此id
        define_port_protocols: res.data.define_port_protocols ? res.data.define_port_protocols : [], //自定义协议
        define_ports: define_ports, //自定义端口
        is_define_port: res.data.is_define_port,
        scan_engine: res.data.scan_engine,
        table_assets_type: res.data.table_assets_type || 0 // ip获取方式
      }
      if (this.task_type == 1) {
        this.changePortGroupsNoPageArr(port_group_ids)
      }
      this.dialogFormVisible = true
    },
    async changePortGroupsNoPageArr(val) {
      let groupres = await portGroupsNoPage({ operate_company_id: this.currentCompany }).catch(
        () => {
          this.loading = false
        }
      )
      this.portGroupsNoPageArr = groupres.data
      if (this.portGroupsNoPageArr) {
        if (val == -3) {
          this.portGroupsNoPageArr.forEach((item) => {
            if (item.name == '全部常用端口') {
              this.ruleForm.port_group_ids = item.id
            }
          })
        }
        if (val == -2) {
          this.portGroupsNoPageArr.unshift({ id: '', name: '自定义' }, { id: -2, name: '列表端口' })
        } else {
          this.portGroupsNoPageArr.unshift({ id: '', name: '自定义' })
        }
      }
    },
    removeOne(val) {
      this.$confirm('确定删除此任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res
          if (this.$route.path == '/assetsScan') {
            res = await delGrontabTask({ id: [val], operate_company_id: this.currentCompany })
          } else {
            res = await deleteVulnerabilityCycle({
              id: [val],
              operate_company_id: this.currentCompany
            })
          }
          if (res.code == 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.currentPage = this.updateCurrenPage(
              this.total,
              [1],
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getTaskResultData()
          }
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body /deep/ {
    height: 496px;
    display: flex;
    justify-content: space-between;
    & > .el-form {
      width: 100%;
    }
    & > .el-divider--vertical {
      height: 156px;
      background: #e9ebef;
      margin-bottom: 20px;
    }
    & > span {
      display: inline-block;
      width: 48%;
      text-align: center;
      margin-top: 30px;
    }
    & > span > img {
      width: 54px;
      height: 54px;
      margin-bottom: 8px;
    }
    & > span p {
      color: #2677ff;
      font-weight: 400;
    }
    .left {
      // height: 100%;
      // overflow: auto;
      // margin-bottom: 24px;
      .tishi {
        line-height: 20px;
        color: #d1d5dd;
      }
      /deep/.el-textarea {
        width: 310px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
      .dialog-footer {
        text-align: right;
      }
    }
    .right {
      width: 55%;
      margin-bottom: 24px;
      margin-left: 20px;
      border-left: 1px solid #e9ebef;
      /deep/.el-textarea {
        width: 300px;
        height: 472px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 20px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        .el-input {
          width: 240px;
          margin-right: 12px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 22px;
          cursor: pointer;
        }
      }
    }
    .tableWrap {
      height: calc(100% - 128px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }

    .el-table .el-table__header th:first-child,
    .el-table .el-table__body td:first-child {
      padding-left: 10px;
    }
  }
}
/deep/.el-table th.el-table__cell:first-child::after {
  display: block;
}
.emptyClass {
  height: 100%;
  text-align: center;
  vertical-align: middle;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
