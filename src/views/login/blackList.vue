<template>
  <div class="container">
    <div class="headerTitle">已标记敏感词：</div>
    <div class="home_header">
      <div class="tableWrap">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltable"
          height="100%"
          style="width: 100%"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column align="center" prop="keyword" label="敏感词"> </el-table-column>
          <el-table-column align="center" label="威胁类型">
            <template slot-scope="{ row }">
              <span v-if="row.type">{{ row.type && row.type.name }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="审核状态">
            <template slot-scope="{ row }">
              {{ statusArrMap[row.status] }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="使用状态">
            <template slot-scope="{ row }">
              {{ row.enable == '1' ? '启用' : '禁用' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="created_at" label="创建时间">
            <template slot-scope="{ row }">
              {{ row.created_at ? row.created_at : '-' }}
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { getBlackList } from '@/api/apiConfig/person.js'

export default {
  components: { tableTooltip },
  data() {
    return {
      statusArrMap: {
        0: '待审核',
        1: '审核通过',
        2: '审核驳回',
        3: '禁用'
      },
      enableArrMap: {
        1: '启用',
        2: '禁用'
      },
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      tableData: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      data: []
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany'])
  },
  mounted() {
    let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (userInfo) {
      this.user = userInfo.user
    }
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getData()
    } else {
      this.getData()
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      this.getData()
    }
  },
  methods: {
    async getData() {
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        operate_company_id: this.currentCompany
      }
      this.loading = true
      let res = await getBlackList(obj).catch(() => {
        this.tableData = []
        this.total = 0
        this.loading = false
      })
      this.loading = false
      this.tableData = res.data.items
      this.total = res.data.total
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },

    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
}

.tableWrap {
  margin-top: 20px;
  height: calc(100% - 129px);
  padding: 0px 20px;
  /deep/.el-table__body-wrapper {
    height: calc(100% - 39px) !important;
  }
}
/deep/.home_header {
  padding-top: 10px;
  position: relative;
  height: 100%;
}
.headerTitle {
  top: 20px;
  left: 50px;
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
