<template>
  <div class="login">
    <div class="top">
      <!-- <img src="../../assets/images/logo.png" alt=""> -->
      <svg class="icon" aria-hidden="true">
        <use xlink:href="#icon-bianzu"></use>
      </svg>
      <span>互联网资产攻击面管理平台</span>
    </div>
    <div class="bot">
      <p class="title">忘记密码</p>
      <span class="goback" @click="$router.replace('/login')"
        ><i class="el-icon-back"></i>返回登录</span
      >
      <el-form ref="form" :model="form" label-width="0">
        <el-form-item v-if="currentActive == '1'" label="">
          <el-input v-model="form.account" placeholder="请输入手机号码"></el-input>
        </el-form-item>
        <el-form-item v-if="currentActive == '1'" label="">
          <el-col :span="16">
            <el-input v-model="form.yz" placeholder="请输入手机验证码"></el-input>
          </el-col>
          <el-col :span="8">
            <span class="yzm">获取短信验证码</span>
          </el-col>
        </el-form-item>
        <div class="emailSuccess" v-if="currentActive == '2-1'">
          <i class="el-icon-success"></i>
          <p>我们已经向您的邮箱发送了一封找回密码邮件，请登录邮箱找回密码！</p>
        </div>
        <p class="showPhoneEmail" v-if="currentActive == '1-1'"
          >手机号码：<span>{{ *********** }}</span></p
        >
        <p class="showPhoneEmail" v-if="currentActive == '2-2'"
          >邮箱地址：<span>{{ '<EMAIL>' }}</span></p
        >
        <el-form-item v-if="currentActive == '1-1' || currentActive == '2-2'" label="">
          <el-input v-model="form.password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item v-if="currentActive == '1-1' || currentActive == '2-2'" label="">
          <el-input v-model="form.password" placeholder="请再次输入密码"></el-input>
        </el-form-item>
        <el-form-item v-if="currentActive == '2'" label="">
          <el-input v-model="form.account" placeholder="请输入邮箱地址"></el-input>
        </el-form-item>
        <el-button
          v-if="currentActive == '1' || currentActive == '2'"
          class="loginBtn"
          type="primary"
          @click="nextStep"
          >下一步</el-button
        >
        <el-button v-if="currentActive == '1-1'" class="loginBtn" type="primary" @click="onSubmit"
          >确定</el-button
        >
        <div v-if="currentActive == '1-1'" class="lastBtn">
          <span></span>
          <span @click="goBack">返回上一步</span>
        </div>
        <el-divider v-if="currentActive == '1' || currentActive == '2'"
          >其他找回密码方式</el-divider
        >
        <span class="otherFun" v-if="currentActive == '1'" @click="currentActive = '2'"
          ><i class="el-icon-message"></i>邮箱</span
        >
        <span class="otherFun" v-if="currentActive == '2'" @click="currentActive = '1'"
          ><i class="el-icon-mobile-phone"></i>手机号码</span
        >
      </el-form>
    </div>
  </div>
</template>

<script>
import sha1 from '@/utils/sha1Encrypt'
import { login } from '@/api/apiConfig/person.js'
export default {
  data() {
    return {
      currentActive: '1', // 手机找回：1 第一步 1-1 第二步； 邮箱找回：2 第一步 2-1 第二步
      keycode: '',
      form: {
        account: '', // foradar1
        password: '', // 123456
        captcha: '',
        captcha_key: '',
        yz: '',
        checked: false
      }
    }
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
  methods: {
    async onSubmit() {
      let objData = {
        account: this.form.account,
        password: sha1(this.form.password),
        captcha: this.form.captcha,
        captcha_key: this.form.captcha_key
      }
      login(objData)
        .then(async (res) => {
          // let token = res.data.token_type + ' ' + res.data.access_token;
          let token = 'Bearer' + ' ' + res.data.access_token
          localStorage.setItem('token', token)
          localStorage.setItem('websocketToken', res.data.access_token)
          localStorage.setItem('tokenTime', res.data.expires_at)
          this.$router.push('/home')
        })
        .catch(() => {})
    },
    nextStep() {
      if (this.currentActive == '1') {
        this.currentActive = '1-1'
      } else if (this.currentActive == '2') {
        this.currentActive = '2-1'
      }
    },
    goBack() {
      this.currentActive = '1'
    }
  }
}
</script>

<style lang="less" scoped>
.login {
  width: 100%;
  height: 100%;
  background: #f1f3f8;
  .top {
    width: 100%;
    height: 52px;
    line-height: 52px;
    background: #0d1d3f;
    span {
      font-weight: bold;
      color: #ebedf2;
    }
    svg {
      font-size: 28px;
      margin: 0 12px 0 11px;
      vertical-align: middle;
    }
  }
  .bot {
    position: relative;
    width: 68%;
    height: 75%;
    margin: 29px auto;
    background: #ffffff;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.04);
    .goback {
      position: absolute;
      top: 24px;
      right: 32px;
      font-weight: 400;
      color: #37393c;
      cursor: pointer;
    }
    .title {
      text-align: center;
      font-weight: 400;
      color: #37393c;
      font-size: 22px;
      padding: 44px 0 92px 0;
    }
    .el-form {
      width: 44%;
      background: #fff;
      margin: 0 auto;
      .el-form-item {
        height: 44px;
        background: rgba(232, 238, 244, 0.76);
        border-radius: 2px;
        border: 1px solid #dce5f0;
        margin-bottom: 32px;
        /deep/.el-input .el-input__inner {
          border: 0;
          height: 44px;
          background: transparent;
        }
      }
      .loginBtn {
        width: 100%;
        height: 48px;
        margin-top: 52px;
        background: #2677ff;
        box-shadow: 0px 4px 12px 0px rgba(11, 113, 244, 0.4);
        border-radius: 4px;
      }
      .showPhoneEmail {
        color: #62666c;
        margin-bottom: 28px;
      }
      .emailSuccess {
        width: 100%;
        text-align: center;
        i {
          font-size: 58px;
          color: #52c41a;
          margin-bottom: 22px;
        }
        p {
          font-weight: 400;
          color: #62666c;
          line-height: 40px;
        }
      }
      .lastBtn {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        color: #37393c;
        & > span {
          cursor: pointer;
        }
      }
      // 其他找回方式
      .otherFun {
        position: absolute;
        left: 0;
        right: 0;
        margin: 0 auto;
        bottom: 50px;
        text-align: center;
        color: #62666c;
        cursor: pointer;
      }
      .el-divider {
        position: absolute;
        width: 44%;
        left: 0;
        right: 0;
        margin: 0 auto;
        bottom: 98px;
        .el-divider__text {
          color: #c1c6cf;
        }
      }
      .yzm {
        // display: inline-block;
        vertical-align: middle;
        font-weight: 400;
        color: #2677ff;
        padding: 10px 10px;
        border-left: 1px solid #ddd;
        cursor: pointer;
      }
      .imgcode {
        width: 96px;
        height: 44px;
        background: #a0c2e3;
        border-radius: 0px 1px 1px 0px;
        cursor: pointer;
      }
    }
  }
}
</style>
