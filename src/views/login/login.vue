<template>
  <el-container class="login">
    <video
      src="../../assets/images/index_video.webm"
      id="video"
      muted
      autoplay="autoplay"
      loop
      disablePictureInPicture
    ></video>
    <!-- poster="../../assets/images/index.png" -->
    <div class="left">
      <img src="../../assets/images/index_title.png" alt="" />
    </div>
    <div class="right loginClass">
      <p>
        <span class="yangText">欢迎登录</span>
        <span class="yang"></span>
      </p>
      <el-form ref="loginform" :model="form" :rules="rules" label-width="0">
        <el-form-item label="" prop="account">
          <el-input
            id="login-username"
            v-model="form.account"
            placeholder="请输入手机号/邮箱"
          ></el-input>
        </el-form-item>
        <el-form-item label="" prop="password">
          <el-input
            id="login-password"
            type="password"
            v-model="form.password"
            placeholder="请输入密码"
            show-password
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label=""
          prop="captcha"
          :style="shortMessage ? 'position: relative;' : 'margin-bottom: 0px;position: relative;'"
        >
          <el-input id="login-verify" v-model="form.captcha" placeholder="请输入验证码"></el-input>
          <img
            class="imgcode"
            :src="keycode"
            @click="getcaptcha()"
            width="100%"
            height="100%"
            alt=""
          />
        </el-form-item>
        <el-form-item
          label=""
          prop="code"
          v-if="shortMessage"
          style="margin-bottom: 0px; position: relative"
        >
          <el-input v-model="form.code" placeholder="请输入手机验证码"></el-input>
          <span v-show="show" class="yzm" @click="getcodeData">获取短信验证码</span>
          <span v-show="!show" class="yzm">{{ count }}s后重新获取</span>
        </el-form-item>
        <el-button id="login-btn" class="loginBtn" type="primary" @click="onSubmit">登录</el-button>
        <div class="lastBtn">
          <!-- <el-checkbox v-model="form.checked">下次自动登录</el-checkbox> -->
          <!-- <span @click="$router.push('/forgetPsd')">忘记密码</span> -->
        </div>
        <div class="footerLogin">
          <div class="wexinBox" @click="wxLogin">
            <img src="../../assets/images/wechat.png" alt="" />
            <el-button type="text">微信登录</el-button>
          </div>
          <div style="width: 1px; height: 16px; background: #c2cfe0"></div>
          <div @click="shortMessageChange" class="messageBox">
            <img src="../../assets/images/shortmessage.png" alt="" />
            <el-button type="text">{{ shortText }}</el-button>
          </div>
        </div>
      </el-form>
    </div>
    <p class="botShow"
      >Copyright {{ getCurYear() }} 华顺信安 All Rights Reserved |
      <a style="color: #808ea5" target="_blank" href="https://beian.miit.gov.cn/#/Integrated/index">
        京ICP备********号-3
      </a>
      | {{ versionData }}</p
    >
  </el-container>
</template>

<script>
import { mapMutations } from 'vuex'
import { latestPublicNotice, has_logged_in } from '@/api/apiConfig/api.js'
import { login, captcha, getVersion, getcode } from '@/api/apiConfig/person.js'
export default {
  data() {
    var checkAccount = (rule, value, callback) => {
      let account = value.trim()
      if (account === '') {
        callback(new Error('请输入账号'))
      } else {
        let emailReg =
          /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        let mobileReg = /^1(3[0-9]|4[********]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/
        let regHongkongPhone = /^(5|6|8|9)\d{7}$/
        let accountSpecial = account.toUpperCase()
        if (
          !mobileReg.test(account) &&
          !regHongkongPhone.test(account) &&
          !(typeof account === 'string' && !!account.match(emailReg) && account.length < 255) &&
          account.toLowerCase() != 'admin' &&
          account.toLowerCase() != 'foradar'
        ) {
          callback(new Error('请输入手机号或邮箱'))
        }
        callback()
      }
    }
    return {
      versionData: 'V1.14.1',
      show: true,
      count: '',
      timer: null,
      keycode: '',
      form: {
        account: '', // foradar1
        password: '', // 123456
        captcha: '',
        captcha_key: '',
        code: ''
        // checked: false
      },
      rules: {
        account: [
          { required: true, validator: checkAccount, trigger: 'blur' }
          // { type: 'email', message: '账号格式不正确', trigger: ['blur', 'change'] }
          // {required: true, message: '请输入用户名', trigger: 'change'}
        ],
        password: [{ required: true, message: '请输入密码', trigger: ['blur'] }],
        captcha: [{ required: true, message: '请输入验证码', trigger: 'change' }]
        // code: [
        //   {required: true, message: '请输入短信验证码', trigger: 'change'}
        // ],
      },
      shortMessage: false,
      shortText: '短信验证码登录'
    }
  },
  watch: {
    //  // 监听到端有返回信息
    //   socketTimestamp(old, newVal) {
    //     this.handleMessage(this.websocketMessage);
    //   }
  },
  mounted() {
    sessionStorage.clear()
    localStorage.clear()
    this.setCurrentCompany(0)
    this.getcaptcha()
    // this.getVersionData()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
  methods: {
    ...mapMutations(['setCurrentCompany']),
    getCurYear() {
      return new Date().getFullYear()
    },
    async getVersionData() {
      let res = await getVersion()
      this.versionData = res.data
    },
    keyDown(e) {
      if (e.keyCode == 13 || e.keyCode == 108) {
        this.onSubmit()
      }
    },
    getCodeTimer() {
      const TIME_COUNT = 60 // 60秒自动获取，测试阶段设置3秒
      if (!this.timer) {
        this.count = TIME_COUNT
        this.show = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.show = true
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    },
    async getcodeData() {
      if (!this.form.captcha) {
        this.$message.error('请先输入验证码')
        return
      }
      if (!this.form.account) {
        this.$message.error('请先输入账号')
        return
      }
      let obj = {
        account: this.form.account,
        captcha: this.form.captcha,
        captcha_key: this.form.captcha_key
      }
      let res = await getcode(obj).catch(() => {
        this.getcaptcha()
      })
      if (res.code == 0) {
        this.$message.success('发送成功！')
        if (process.env.NODE_ENV == 'production') {
          // 115环境短信验证码生效
          this.getCodeTimer() // 倒计时功能
        } else {
          this.form.code = res.data
        }
      }
    },
    async getcaptcha() {
      let res = await captcha()
      this.keycode = res.data.img
      this.form.captcha_key = res.data.key
    },
    async onSubmit() {
      this.$refs.loginform.validate(async (valid) => {
        if (valid) {
          if (this.shortMessage) {
            let objData = {
              account: this.form.account,
              password: this.sha1(this.form.password),
              captcha: this.form.captcha,
              captcha_key: this.form.captcha_key,
              code: this.form.code,
              is_golang_api: 1 // 代表golang
            }
            login(objData)
              .then(async (res) => {
                let token = 'Bearer' + ' ' + res.data.access_token
                localStorage.setItem('token', token)
                localStorage.setItem('websocketToken', res.data.access_token)
                localStorage.setItem('tokenTime', res.data.expires_in)
                this.$router.push('/home')
                this.noticeFunc()
              })
              .catch(() => {
                this.getcaptcha()
              })
          } else {
            let objData = {
              account: this.form.account,
              password: this.sha1(this.form.password),
              captcha: this.form.captcha,
              captcha_key: this.form.captcha_key,
              code: this.form.code,
              model: 'captcha',
              is_golang_api: 1
            }
            login(objData)
              .then(async (res) => {
                let token = 'Bearer' + ' ' + res.data.access_token
                localStorage.setItem('token', token)
                localStorage.setItem('websocketToken', res.data.access_token)
                localStorage.setItem('tokenTime', res.data.expires_in)
                this.$router.push('/home')
                this.noticeFunc()
              })
              .catch(() => {
                this.getcaptcha()
              })
          }
        }
      })
    },
    async noticeFunc() {
      let res = await latestPublicNotice()
      if (res.code == 0) {
        let beforeFiveTime = this.recentTime(-5, 'yyyy-MM-dd hh:mm:ss', res.data.up_at_start) // 升级前五天时间
        // 当前登录是否是升级开始时间前5天内，是则显示升级弹窗
        if (
          new Date().getTime() > new Date(beforeFiveTime).getTime() &&
          new Date().getTime() < new Date(res.data.up_at_start).getTime()
        ) {
          const h = this.$createElement
          let notify = this.$notify({
            title: '重要公告',
            type: 'warning',
            duration: 120000,
            customClass: 'noticeClass',
            offset: 44,
            message: h('p', {}, [
              h('p', {}, '尊敬的用户您好！'),
              h('span', {}, '因维护升级，系统将于'),
              h('u', {}, `${res.data.up_at_start}-${res.data.up_at_end}`),
              h('span', {}, '暂停服务，由此给您带来的不便敬请谅解。'),
              h('span', {}, '如有疑问，可邮件至'),
              h('u', {}, '<EMAIL>'),
              h('span', {}, '咨询，感谢您的支持。')
            ])
          })
        }
      }
    },
    recentTime(day, fmt, time) {
      //获取当前时间的毫秒值
      let now = (time ? new Date(time) : new Date()).getTime()
      // 获取前后n天时间
      let recent = new Date(now + day * 24 * 60 * 60 * 1000)

      // key：正则匹配表达式，value：对应的时间、日期
      let fmtObj = {
        'M+': recent.getMonth() + 1, //月份
        'd+': recent.getDate(), //日
        'h+': recent.getHours(), //时
        'm+': recent.getMinutes(), //分
        's+': recent.getSeconds() //秒
      }
      // 获取匹配年份替换
      if (/(y+)/.test(fmt)) {
        //RegExp.$1 匹配结果，替换成对应的长度。如：yyyy就替换成整个年份2021，yy就替换成后两位21，以此类推
        fmt = fmt.replace(RegExp.$1, (recent.getFullYear() + '').substr(4 - RegExp.$1.length))
      }
      for (let key in fmtObj) {
        if (new RegExp(`(${key})`).test(fmt)) {
          //日期，时、分、秒替换，判断fmt是否需要补0，如：yyyy-M-d h:m:s 不补0,yyyy-MM-dd hh:mm:ss 则自动补0
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length == 1
              ? fmtObj[key]
              : ('00' + fmtObj[key]).substr(('' + fmtObj[key]).length)
          )
        }
      }
      return fmt
    },

    wxLogin() {
      //微信登录
      if (process.env.NODE_ENV == 'production') {
        // 115环境
        window.location.href =
          'https://i.nosec.org/login?service=' +
          `${window.location.protocol}//${window.location.host}` +
          '/wxLogin'
      } else {
        window.location.href =
          'https://i.nosec.org/login?service=https://foradar.baimaohui.net/wxLogin'
      }
    },
    shortMessageChange() {
      if (this.shortMessage) {
        this.shortMessage = false
        this.shortText = '短信验证码登录'
      } else {
        this.shortMessage = true
        this.shortText = '图形验证登录'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.login {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  // background: url('../../assets/images/index.png') no-repeat center ;
  // background-size:cover;
  position: relative;
  justify-content: center;
  .left {
    position: absolute;
    top: 38px;
    left: 42px;
    // img {
    //   margin: 38px 0 0 42px;
    // }
  }
  .right {
    position: absolute;
    right: 10%;
    width: 440px;
    background: #fff;
    padding: 54px 60px;
    box-sizing: border-box;
    background: -webkit-linear-gradient(
      180deg,
      rgba(233, 241, 255, 0.72) 0%,
      rgba(255, 255, 255, 0.72) 100%
    );
    background: -o-linear-gradient(
      180deg,
      rgba(233, 241, 255, 0.72) 0%,
      rgba(255, 255, 255, 0.72) 100%
    );
    background: -moz-linear-gradient(
      180deg,
      rgba(233, 241, 255, 0.72) 0%,
      rgba(255, 255, 255, 0.72) 100%
    );
    background: linear-gradient(
      180deg,
      rgba(233, 241, 255, 0.72) 0%,
      rgba(255, 255, 255, 0.72) 100%
    );
    box-shadow: -4px 0px 16px 0px rgba(5, 28, 67, 0.08);
    border-radius: 4px;
    border: 2px solid #ffffff;
    backdrop-filter: blur(4px);
    p {
      color: #4d5159;
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 36px;
      position: relative;
    }
    .el-form {
      width: 100%;
      padding: 0 !important;
      .el-form-item {
        height: 44px;
        background: rgba(232, 238, 244, 0.76);
        border-radius: 2px;
        border: 1px solid #dce5f0;
        margin-bottom: 28px;
        /deep/.el-input .el-input__inner {
          height: 44px;
          line-height: 44px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 2px;
          border: 1px solid #d4e1f2;
        }
      }
      .loginBtn {
        width: 100%;
        height: 48px;
        margin-top: 44px;
        background: #2677ff;
        box-shadow: 0px 4px 12px 0px rgba(11, 113, 244, 0.4);
        border-radius: 4px;
      }
      .lastBtn {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        color: #37393c;
        & > span {
          cursor: pointer;
        }
      }
      .yzm {
        display: inline-block;
        // width: 100%;
        padding: 0px 9px;
        height: 42px;
        line-height: 42px;
        text-align: center;
        vertical-align: middle;
        font-weight: 400;
        color: #2677ff;
        cursor: pointer;
        position: absolute;
        top: 1px;
        right: 1px;
      }
      .yzm::before {
        content: '';
        display: block;
        position: absolute;
        top: 20%;
        left: -10px;
        width: 1px;
        height: 60%;
        background: #2677ff;
      }
      .imgcode {
        width: 96px;
        height: 42px;
        background: #a0c2e3;
        border-radius: 0px 1px 1px 0px;
        cursor: pointer;
        position: absolute;
        top: 1px;
        right: 1px;
      }
    }
    .code-input {
      /deep/ .el-input::after {
        content: '';
        display: inline-block;
        width: 1px;
        height: 24px;
        background: #dce5f0;
        position: absolute;
        top: 10px;
        right: 6px;
      }
    }
  }
}
.footerLogin {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}
.footerLogin {
  .wexinBox,
  .messageBox {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  div {
    img {
      width: 30px;
      margin-right: 5px;
    }
  }
}
.botShow {
  width: 100%;
  position: absolute;
  padding: 0;
  bottom: 24px;
  left: 0;
  line-height: 32px;
  font-size: 14px;
  font-weight: 400;
  color: #808ea5;
  text-align: center;
}
#video {
  position: fixed;
  object-fit: cover;
  height: 100%;
  width: 100%;
  bottom: 0;
  right: 0;
}
.yangText {
  position: absolute;
  bottom: 10px;
  left: 0px;
}
.yang {
  display: inline-block;
  margin-left: -8px;
  width: 128px;
  height: 18px;
  background: -webkit-linear-gradient(
    90deg,
    rgba(38, 119, 255, 0.48) 0%,
    rgba(38, 119, 255, 0) 100%
  );
  background: -o-linear-gradient(90deg, rgba(38, 119, 255, 0.48) 0%, rgba(38, 119, 255, 0) 100%);
  background: -moz-linear-gradient(90deg, rgba(38, 119, 255, 0.48) 0%, rgba(38, 119, 255, 0) 100%);
  background: linear-gradient(90deg, rgba(38, 119, 255, 0.48) 0%, rgba(38, 119, 255, 0) 100%);
  border-radius: 100px 0px 0px 100px;
}
/deep/.el-input__inner:hover {
  border: 1px solid #bdcce0 !important;
}
/deep/.el-input__inner:focus {
  border: 1px solid #2677ff !important;
}
</style>
