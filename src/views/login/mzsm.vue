<template>
  <el-container class="login">
    <div id="wordView" v-html="wordText" v-loading="previewLoading"></div>
  </el-container>
</template>

<script>
import mammoth from 'mammoth'
export default {
  data() {
    return {
      wordText: '',
      previewLoading: false
    }
  },
  async mounted() {
    this.previewLoading = true
    let url = 'downloadTemplate/FORadar-用户注册和服务协议.docx'
    const xhr = new XMLHttpRequest()
    xhr.open('get', url, true)
    xhr.responseType = 'arraybuffer'
    xhr.onload = () => {
      if (xhr.status == 200) {
        mammoth
          .convertToHtml({ arrayBuffer: new Uint8Array(xhr.response) })
          .then((resultObject) => {
            this.$nextTick(() => {
              this.wordText = resultObject.value
            })
          })
      }
    }
    xhr.send()
  }
}
</script>

<style lang="less" scoped>
.login {
  width: 100%;
  height: 100%;
  /deep/#wordView {
    h1 {
      text-align: center;
      line-height: 40px;
    }
    h3 {
      line-height: 40px;
    }
    p {
      line-height: 32px;
      text-indent: 20px;
    }
    ul li ol {
      padding-left: 30px !important;
    }
    li {
      line-height: 32px;
    }
    strong {
      line-height: 32px;
    }
  }

  .downloadFile {
    color: rgb(64, 158, 255);
    padding: 0 5px;
    cursor: pointer;
  }
  a {
    color: #409eff;
    text-decoration: underline;
  }
}
</style>
