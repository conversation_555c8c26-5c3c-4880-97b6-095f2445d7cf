<template>
  <div class="container">
    <div class="headerTitle">个人中心</div>
    <div class="home_header">
      <div class="filterTab">
        <div class="left">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-touxiang"></use>
          </svg>
          <div>
            <p>
              <span class="f-label" v-if="user.role == 3 && user.level == 0">测试</span>
              <b v-if="user.role == 3"
                >{{ company.name }}<el-divider direction="vertical"></el-divider
                ><i>{{ user.name }}</i></b
              >
              <b v-if="user.role == 2">{{ user.name }}</b>
              <span
                v-if="(accountUser.role == 2 && currentCompany == -1) || accountUser.role != 2"
                class="psd"
                @click="updatePsd"
                >修改密码</span
              >
              <span
                v-if="(accountUser.role == 2 && currentCompany == -1) || accountUser.role != 2"
                style="margin-left: 10px"
              >
                <el-button type="text" @click="bind" style="color: #2677ff" v-if="isBind"
                  >绑定微信</el-button
                >
                <el-button type="text" @click="noBind" style="color: #2677ff" v-else
                  >解除微信绑定</el-button
                >
              </span>
            </p>
            <p>
              <span><i class="el-icon-mobile-phone"></i>{{ user.mobile }}</span>
              <el-divider direction="vertical"></el-divider>
              <span><i class="el-icon-message"></i>{{ user.email }}</span>
            </p>
          </div>
        </div>
        <!-- 仅企业用户有编辑功能 -->
        <el-button
          v-if="accountUser.role == 3"
          class="normalBtnRe"
          type="primary"
          @click="$router.push({ path: '/personalCenterInfo', query: { cion: 1 } })"
          >编辑</el-button
        >
      </div>
      <template v-if="user.role == 2">
        <div class="company-info">
          <p class="title">基本信息</p>
          <div class="content" style="padding-top: 27px">
            管理企业：{{ user.safe_company.map((item) => item.name).join('、') }}
          </div>
          <div
            class="apiToken"
            v-if="(accountUser.role == 2 && currentCompany == -1) || accountUser.role != 2"
          >
            <span>API KEY：</span>
            <div class="text">
              <b>{{ apiToken ? apiToken : '---' }}</b
              ><i class="el-icon-refresh" @click="getApiTokenFn(true)"></i
              ><i class="el-icon-document-copy" @click="copyClick(apiToken)"></i>
            </div>
          </div>
          <div class="apiToken" v-if="userInfo.is_show_copy_data == 1">
            <span>数据同步KEY：</span>
            <div class="text">
              <b>{{ user.bear_token ? user.bear_token : '---' }}</b>
            </div>
          </div>
        </div>
        <!-- <div class="black-list">
          <blackList ref="blackList"/>
        </div> -->
      </template>
      <template v-if="user.role == 3">
        <div class="company-info">
          <p class="title">基本信息</p>
          <ul class="content">
            <li>
              <span>企业名称：</span>
              <b>{{ company.name }}</b>
            </li>
            <li>
              <span>企业所在地区：</span>
              <b>{{
                `${company.area_province_name}-${company.area_city_name}-${company.area_zone_name}`
              }}</b>
            </li>
            <li>
              <span>行业：</span>
              <b>{{ company.industry.name }}</b>
            </li>
            <li>
              <span>到期时间：</span>
              <b>{{ user.expires_at }}</b>
            </li>
          </ul>
          <!-- sass需要限制测试用户不展示以及调用接口 -->
          <div
            class="apiToken"
            v-if="
              (accountUser.role == 2 && currentCompany == -1) ||
              (accountUser.role != 2 && !(accountUser.role == 3 && accountUser.level == 0))
            "
          >
            <span>API KEY：</span>
            <div class="text">
              <b>{{ apiToken ? apiToken : '---' }}</b
              ><i class="el-icon-refresh" @click="getApiTokenFn(true)"></i
              ><i class="el-icon-document-copy" @click="copyClick(apiToken)"></i>
            </div>
          </div>
          <div class="apiToken" v-if="userInfo.is_show_copy_data == 1">
            <span>数据同步KEY：</span>
            <div class="text">
              <b>{{ user.bear_token ? user.bear_token : '---' }}</b>
            </div>
          </div>
        </div>
        <div class="company-info">
          <p class="title">授权信息</p>
          <div class="clearfloat">
            <div
              class="chart"
              :id="'chart' + index"
              v-for="(item, index) in pieData.length"
              :key="index"
            ></div>
          </div>
        </div>
        <!-- <div class="black-list">
          <blackList ref="blackList"/>
        </div> -->
      </template>
      <template v-if="user.role != 2 && user.role != 3">
        <div
          class="apiToken"
          v-if="
            (accountUser.role == 2 && currentCompany == -1) ||
            (accountUser.role != 2 && !(accountUser.role == 3 && accountUser.level == 0))
          "
        >
          <span>API KEY：</span>
          <div class="text">
            <b>{{ apiToken ? apiToken : '---' }}</b
            ><i class="el-icon-refresh" @click="getApiTokenFn(true)"></i
            ><i class="el-icon-document-copy" @click="copyClick(apiToken)"></i>
          </div>
        </div>
        <div class="apiToken" v-if="userInfo.is_show_copy_data == 1">
          <span>数据同步KEY：</span>
          <div class="text">
            <b>{{ user.bear_token ? user.bear_token : '---' }}</b>
          </div>
        </div>
        <!-- <div class="black-list">
          <blackList ref="blackList"/>
        </div> -->
      </template>
      <div class="black-list">
        <blackList ref="blackList" />
      </div>
      <el-dialog
        class="elDialogAdd"
        :close-on-click-modal="false"
        :visible.sync="resetPasswordVisible"
        width="34%"
      >
        <template slot="title"> 修改密码 </template>
        <el-form
          :model="resetForm"
          :rules="passwordRules"
          ref="resetForm"
          label-width="124px"
          class="demo-ruleForm"
        >
          <el-form-item label="输入旧密码" prop="old_password">
            <el-input
              v-model="resetForm.old_password"
              type="password"
              show-password
              placeholder="请输入用户密码"
            ></el-input>
          </el-form-item>
          <el-form-item label="输入新密码" prop="password">
            <el-input
              v-model="resetForm.password"
              type="password"
              show-password
              placeholder="请输入用户密码"
            ></el-input>
          </el-form-item>
          <el-form-item label="再次输入新密码" prop="confirm_password">
            <el-input
              v-model="resetForm.confirm_password"
              type="password"
              show-password
              placeholder="请再次输入用户密码"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button class="highBtnRe" @click="resetPasswordVisible = false">关闭</el-button>
          <el-button
            class="highBtn"
            @click="passwordInsertSave('resetForm')"
            :loading="otherLoading"
            >确定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import sha1 from '@/utils/sha1Encrypt'
import { mapGetters, mapState } from 'vuex'
import blackList from './blackList.vue'
import {
  weixiBind,
  weixiUnBind,
  weixiLogin,
  getApiToken,
  logout,
  personInfo,
  resetPwd
} from '@/api/apiConfig/person.js'

export default {
  components: { blackList },

  data() {
    var checkPassword22 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        let reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{13,}$/
        // let reg = /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{13,}$/ // 不包含.
        if (!reg.test(value)) {
          callback(new Error('密码正确格式：12位以上 数字、大小写字母、特殊符号'))
        }
        if (this.resetForm.confirm_password !== '') {
          this.$refs.resetForm.validateField('confirm_password')
        }
        callback()
      }
    }
    var checkPassword222 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.resetForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      blackList: [],
      resetPasswordVisible: false,
      otherLoading: false,
      userInfo: {},
      user: {
        expires_at: '-',
        name: '',
        email: '-',
        mobile: '-'
      },
      company: {
        name: '-',
        industry: { name: '-' },
        area_province_name: '-',
        area_city_name: '-',
        area_zone_name: '-'
      },
      resetForm: {
        old_password: '',
        password: '',
        confirm_password: ''
      },
      passwordRules: {
        old_password: [{ required: true, message: '请输入旧密码', trigger: 'change' }],
        password: [{ required: true, validator: checkPassword22, trigger: 'change' }],
        confirm_password: [{ required: true, validator: checkPassword222, trigger: 'change' }]
      },
      pieData: [],
      isBind: true, //是否绑定过微信
      apiToken: '',
      accountUser: {
        role: ''
      },
      userInfo: {}
    }
  },
  mounted() {
    let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    this.userInfo = userInfo
    if (userInfo) {
      this.accountUser = userInfo.user
    }
    if (this.accountUser.role == 2) {
      if (!this.currentCompany) return
      this.init()
    } else {
      this.init()
    }
  },
  methods: {
    async init() {
      let userMessage = await personInfo({
        detail: 1,
        operate_company_id: this.currentCompany
      })
      if (userMessage.data.user.wechat_login_id) {
        //判断是否绑定过微信
        this.isBind = false
      } else {
        this.isBind = true
      }
      this.userInfo = userMessage.data
      this.user = this.userInfo.user
      if (this.userInfo.company) {
        this.company = this.userInfo.company

        let pieData = [
          {
            title: '资产台账（数量）',
            used: this.company.used_ip_asset,
            total: this.company.limit_ip_asset,
            swith: true
          },
          /*{
          title: "资产扫描（数量）",
          used: this.company.used_ip_asset_scan,
          total: this.company.limit_ip_asset,
          swith: true
        },*/ {
            title: '云端推荐（次数）',
            used: this.company.used_cloud_recommend,
            total: this.company.limit_cloud_recommend,
            swith: true
          } /*,{
          title: "数字资产（次数）",
          used: this.company.used_new_asset,
          total: this.company.limit_new_asset,
          swith: this.company.new_asset_rate > 0
        }*/,
          {
            title: '漏洞扫描（次数）',
            used: this.company.used_poc_scan,
            total: this.company.limit_poc_scan,
            swith: this.company.limit_poc_scan > 0
          } /*,{
          title: "数据泄露（次数）",
          used: this.company.used_data_leak,
          total: this.company.limit_data_leak,
          swith: this.company.data_leak_rate > 0
        }*/,
          {
            title: '关键词（数量）',
            used: this.company.used_monitor_keyword,
            total: this.company.limit_monitor_keyword,
            swith: this.company.data_leak_rate > 0 || this.company.new_asset_rate > 0
          }
        ]

        this.pieData = pieData.filter((item) => {
          return item.swith == true
        })

        this.$nextTick(() => {
          for (let i = 0; i < this.pieData.length; i++) {
            this.pie(`chart${i}`, this.pieData[i])
          }
        })
      }
      if (this.$route.query.type == 'success') {
        let obj = {
          type: this.$route.query.type,
          open_id: this.$route.query.openid,
          time: this.$route.query.time,
          sign: this.$route.query.sign
        }
        weixiLogin(obj)
          .then((res) => {
            if (res.code == 0) {
              //已经绑定过微信，直接登陆
              if (res.data.type == '1') {
                this.$router.push('/personalCenter')
              } else {
                //未绑定过微信
                let obj = {
                  open_id: this.$route.query.openid,
                  mobile: this.user.mobile
                }
                weixiBind(obj).then(() => {
                  if (res.code == 0) {
                    this.$router.push('/personalCenter')
                    this.$message.success('绑定成功！')
                  }
                })
              }
            }
          })
          .catch(() => {
            //扫描失败
            this.$router.push('/personalCenter')
          })
      } else {
        this.$router.push('/personalCenter')
      }
      // this.getBlackListFn()
      if (this.user.role == 2) {
        this.$refs.blackList && this.$refs.blackList.getData()
      }
      let { accountUser } = this
      if (
        (accountUser.role == 2 && this.currentCompany == -1) ||
        (accountUser.role != 2 && !(accountUser.role == 3 && accountUser.level == 0))
      ) {
        this.getApiTokenFn()
      }
    },
    async getApiTokenFn(ref = false) {
      let res = await getApiToken({ ref, operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.apiToken = res.data.access_token.split('|')[1]
        if (ref) {
          this.$message.success('更新成功')
        }
      }
    },
    copyClick(text) {
      this.$copyText(text).then(
        (res) => {
          console.log('复制成功：', res)
          this.$message.success('复制成功')
        },
        (err) => {
          console.log('', err)
          this.$message.error('复制失败')
        }
      )
    },
    updatePsd() {
      this.resetForm = {
        old_password: '',
        password: '',
        confirm_password: ''
      }
      this.resetPasswordVisible = true
    },
    async passwordInsertSave(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          return false
        }
        let obj = {
          old_password: sha1(this.resetForm.old_password),
          password: sha1(this.resetForm.password),
          confirm_password: sha1(this.resetForm.confirm_password)
        }
        this.otherLoading = true
        resetPwd(obj)
          .then((res) => {
            this.otherLoading = false
            if (res.code != 0) {
              this.$message.error(data.message)
              return false
            }
            this.resetPasswordVisible = false
            this.$message.success('操作成功！')
            this.logoutFun()
          })
          .catch((error) => {
            this.otherLoading = false
          })
      })
    },
    async logoutFun() {
      let res = await logout()
      if (res.code == 0) {
        sessionStorage.clear()
        localStorage.clear()
        this.$router.replace('/login')
      }
    },
    pie(id, data) {
      let option = {
        color: ['#FFBC00', '#cbcdd0'],
        title: [
          {
            text: data.used + '/' + data.total,
            top: 'center',
            right: 'center',
            textStyle: {
              fontSize: '12px',
              fontWeight: 400,
              color: '#62666C'
            }
          },
          {
            text: data.title,
            left: 'center',
            bottom: '0',
            textStyle: {
              fontSize: '14px',
              fontWeight: 400,
              color: '#333'
            }
          }
        ],
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['50%', '60%'],
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: [
              { value: data.used, name: '已使用' },
              { value: data.total - data.used, name: '总量' }
            ]
          }
        ]
      }
      var myChart = this.$echarts.init(document.getElementById(id))
      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 绑定微信
    bind() {
      if (process.env.NODE_ENV == 'production') {
        // 115环境
        window.location.href =
          'https://i.nosec.org/login?service=' +
          `${window.location.protocol}//${window.location.host}` +
          '/personalCenter'
      } else {
        window.location.href =
          'https://i.nosec.org/login?service=https://foradar.baimaohui.net/personalCenter'
      }
    },
    // 解除绑定
    async noBind() {
      this.$confirm('此操作将解除绑定微信, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await weixiUnBind()
          if (res.code == 0) {
            this.$router.go(0)
            setTimeout(() => {
              this.$message.success('解绑成功！')
            }, 50)
          }
        })
        .catch(() => {})
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.accountUser.role == 2 && this.currentCompany) {
        this.init()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany'])
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  // height: 100%;
  background: #fff;
  /deep/.home_header {
    box-sizing: border-box;
    position: relative;
    // height: 100%;
    padding: 36px 28px;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 25px;
      .left {
        display: flex;
        align-items: center;
        svg {
          font-size: 78px;
          background: #c3d9ff;
          border-radius: 4px;
          margin-right: 16px;
        }
        & > div {
          p {
            line-height: 39px;
            b {
              font-weight: 500;
              font-size: 16px;
              color: #37393c;
            }
            .psd {
              color: #2677ff;
              margin-left: 12px;
              cursor: pointer;
            }
            .el-divider {
              margin: 0 16px;
            }
            i {
              margin-right: 3px;
            }
          }
        }
      }
    }
    .content {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 0 20px;
      li {
        width: 50%;
        padding-top: 27px;
        span {
          display: inline-block;
          //width: 100px;
          color: #62666c;
        }
        b {
          font-weight: 400;
          color: #37393c;
        }
      }
    }
    .company-info {
      border-top: 1px solid #e9ebef;
      padding-bottom: 27px;
      .title {
        padding-left: 5px;
        border-left: 5px solid #cbcdd0;
        margin-top: 27px;
        margin-left: 5px;
      }
      .chart {
        width: calc(100% / 7);
        height: 200px;
        float: left;
        box-sizing: border-box;
      }
    }
  }
  .f-label {
    font-size: 14px;
    color: #2677ff;
    background: rgba(38, 119, 255, 0.12);
    padding: 2px 8px;
    margin-right: 5px;
  }
}
.black-list {
  height: calc(100% - 229px);
}
.apiToken {
  display: flex;
  margin-left: 18px;
  margin-top: 10px;
  i {
    margin-left: 20px;
  }
}
.clearfloat:after {
  display: block;
  clear: both;
  content: '';
  visibility: hidden;
  height: 0;
}
　 .clearfloat {
  zoom: 1;
}
</style>
