<template>
  <div class="container">
    <div class="headerTitle">编辑信息</div>
    <div class="home_header">
      <div class="filterTab">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="企业名称" prop="company_name">
            <el-input v-model="ruleForm.company_name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="用户姓名" prop="name">
            <el-input v-model="ruleForm.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <span>{{ user.mobile }}</span>
            <!-- <el-input v-model="ruleForm.email" placeholder="请输入"></el-input> -->
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <span>{{ user.email }}</span>
            <!-- <el-input v-model="ruleForm.email" placeholder="请输入"></el-input> -->
          </el-form-item>
          <el-form-item label="地区" prop="diqu">
            <el-cascader v-model="ruleForm.diqu" :props="cascaderProps"></el-cascader>
          </el-form-item>
          <el-form-item label="行业" prop="industry_id">
            <el-select filterable v-model="ruleForm.industry_id" placeholder="请选择行业">
              <el-option
                :label="v.name"
                :value="v.id"
                v-for="v in industryArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="到期日期" prop="status">
            <span>{{ user.expires_at }}</span>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="content">
      <el-button class="normalBtnRe" type="primary" @click="$router.push('/personalCenter')"
        >取消</el-button
      >
      <el-button class="normalBtn" type="primary" @click="save">确定</el-button>
    </div>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
import { industry, area } from '@/api/apiConfig/api.js'
import { personInfo, UpdateMe } from '@/api/apiConfig/person.js'

export default {
  data() {
    return {
      userInfo: {},
      user: {},
      company: {},
      rules: {},
      ruleForm: {
        diqu: '',
        name: '',
        company_name: '',
        industry_id: ''
      },
      industryArr: [],
      cascaderProps: {
        lazy: true,
        lazyLoad: this.lazyLoad
      }
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    this.user = this.userInfo.user
    this.company = this.userInfo.company
    this.ruleForm = {
      diqu: [this.company.province_code, this.company.city_code, this.company.area_code],
      name: this.user.name,
      company_name: this.company.name,
      industry_id: this.company.industry_id
    }
  },
  async mounted() {
    this.industryData()
  },

  methods: {
    ...mapMutations(['changeCompany', 'setOwnerIdChange']),
    async lazyLoad(node, resolve) {
      let level = node.level
      let res
      if (!node.data) {
        res = await area('')
      } else {
        res = await area(node.data.value)
      }
      if (res.code == 0) {
        const nodes = Array.from(res.data).map((item) => ({
          value: item.adcode,
          label: item.name,
          leaf: level >= 2
        }))
        resolve(nodes)
      }
    },
    async industryData() {
      let res = await industry()
      if (res.code == 0) {
        this.industryArr = res.data
      }
    },
    async save() {
      let codeArr = ['province_code', 'city_code', 'area_code']
      this.ruleForm.diqu.forEach((v, index) => {
        this.ruleForm[codeArr[index]] = v
      })
      let obj = Object.assign({}, this.ruleForm)
      delete obj.diqu
      let res = await UpdateMe(obj)
      if (res.code == 0) {
        let userMessage = await personInfo()
        sessionStorage.setItem('userMessage', JSON.stringify(userMessage.data))
        this.setOwnerIdChange(userMessage.data.user.id) // 存储当前账号id
        this.$message.success('操作成功！')
        this.$router.push('/personalCenter')
        if (userMessage.data && userMessage.data.company) {
          this.changeCompany(userMessage.data) // 企业名称，用户名修改了，系统右上角显示需要更新
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 64px;
    line-height: 64px;
    text-align: center;
    background: #ffffff;
    box-shadow: 0px -2px 12px 0px rgba(0, 0, 0, 0.04);
    .normalBtn {
      margin-left: 20px;
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    padding: 36px 28px;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .demo-ruleForm {
        width: 40%;
      }
    }
  }
}
</style>
