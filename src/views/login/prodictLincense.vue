<template>
  <div class="container">
    <div class="headerTitle">
      产品激活
      <el-button type="primary" class="btn" :loading="downloadLogLoading" @click="downloadLogFunc"
        >日志下载</el-button
      >
    </div>
    <div class="home_header" v-loading="loadingIcon">
      <div class="localiHeader">
        <div>
          <div>
            <template v-if="env === 'logo'">
              <img
                class="icon"
                style="padding: 0 12px"
                src="../../assets/images/qmxc_logo.png"
                alt=""
              />
            </template>
            <template v-else>
              <svg class="icon" v-if="(!infoData||!infoData.system_logo) && !loadingIcon" aria-hidden="true">
                <use xlink:href="#icon-bianzu"></use>
              </svg>
              <div v-else-if="loadingIcon"></div>
              <img v-else style="width: 40px;height: 40px; padding: 0 12px" :src="getterPageIcon" alt="">
            </template>
            <!-- <span style="margin-left: 12px"> 互联网资产攻击面管理平台 </span> -->
            <span style="margin-left: 12px">{{pageTitle}}</span>
          </div>
        </div>
        <div class="pSpecial textSpecial"
          >设备序列号： <p class="specialDiv">{{ lincenseObj.licenseKey }}</p>
          <span
            class="showAll"
            @click="showLincenseAll(lincenseObj.licenseKey)"
            v-if="
              lincenseObj.licenseKey != '' &&
              lincenseObj.licenseKey != null &&
              lincenseObj.licenseKey
            "
            >点击复制</span
          >
        </div>
        <p v-if="!infoData||!infoData.product_code"
          >产品型号：<span>{{ lincenseObj.product_model }}</span></p
        >
        <p v-else
          >产品型号：<span>{{ productCode }}</span></p
        >
        <p
          >版本： <span>{{ lincenseObj.version_name }}</span></p
        >
        <p
          >版本号：<span>{{ lincenseObj.version }}</span></p
        >
        <p
          >设备ID：<span>{{ lincenseObj.device_id }}</span></p
        >
        <p
          >激活状态：<span>{{ lincenseObj.license_state ? '已激活' : '未激活' }}</span></p
        >
        <p
          >试用时间：<span>{{ lincenseObj.product_limit_date }}</span></p
        >
        <p
          >售后时间：<span>{{ lincenseObj.upgrade_limit_date }}</span></p
        >
        <p
          >资产上限：<span>{{ lincenseObj.max_asset_num }}</span></p
        >
      </div>
      <div class="messageText">
        <p>我该如何激活产品？</p>
        <div
          >请您将页面中的设备序列号，复制并发送给设备提供商，他会给您一份license文件，用来激活产品！点击立即激活按钮，在激活弹窗中输入或上传license文件，点击确定完成产品激活。</div
        >
      </div>
      <div class="activateBox">
        <el-button class="normalBtnRe" type="primary" id="scan_more_del" @click="goBack"
          >返回首页</el-button
        >
        <el-button class="normalBtn" type="primary" id="scan_add" @click="immediateActivation"
          >立即激活</el-button
        >
      </div>
      <el-dialog
        :visible.sync="dialogFormVisible"
        :close-on-click-modal="false"
        class="elDialogAdd"
        width="500px"
      >
        <template slot="title"> 立即激活 </template>
        <div class="dialog-body">
          <el-form
            :model="ruleFormAdd"
            style="padding: 0 !important"
            ref="ruleForm"
            :rules="ruleFormRules"
            label-width="80px"
            class="demo-ruleForm"
          >
            <el-form-item label="激活方式" prop="type">
              <el-select v-model="ruleFormAdd.type" @change="banningWayChange" placeholder="请选择">
                <el-option :key="0" :label="'输入产品密钥'" :value="'1'"></el-option>
                <el-option :key="2" :label="'上传文件'" :value="'2'"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="产品密钥" prop="license" v-if="ruleFormAdd.type == '1'">
              <el-input
                type="textarea"
                :rows="10"
                v-model="ruleFormAdd.license"
                class="placeholderIdBox"
                placeholder="请输入产品密钥"
              ></el-input>
            </el-form-item>
            <el-form-item prop="file" class="uploadFile" v-if="ruleFormAdd.type == '2'">
              <el-upload
                class="upload-demo"
                drag
                :action="uploadActionHd"
                :headers="uploadHeaders"
                accept=".txt"
                :before-upload="beforeIpUpload"
                :on-success="ipUploadSuccessHd"
                :on-remove="uploadRemoveHd"
                :on-error="ipUploadErrorHd"
                :limit="uploadMaxCount"
                :on-exceed="handleExceedHd"
                :file-list="fileList"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">支持上传txt 文件，且大小不超过1MB</div>
              </el-upload>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button class="highBtnRe" @click="dialogFormVisible = false">关闭</el-button>
          <el-button class="highBtn" @click="chainSave">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions, mapState, mapMutations } from 'vuex'
import { getLicenseInfo, setlicenseAssets, downloadLog } from '@/api/apiConfig/person.js'
import { getSystemInfos } from '@/api/apiConfig/setting.js'
export default {
  data() {
    return {
      downloadLogLoading: false,
      dialogFormVisible: false,
      uploadActionHd: `${this.uploadSrcIp}/assets/account/files?encode=0`,
      uploadHeaders: { Authorization: localStorage.getItem('token') },
      uploadMaxCount: 1,
      ruleFormAdd: {
        type: '1',
        license: '',
        file: ''
      },
      lincenseObj: {},
      fileList: [],
      ruleFormRules: {
        type: [{ required: true, message: '请选择上传类型', trigger: 'change' }],
        license: [{ required: true, message: '请填写license', trigger: 'blur' }],
        file: [{ required: true, message: '请上传文件', trigger: 'change' }]
      },
      infoData: {},
      loadingIcon: false
    }
  },
  computed: {
    ...mapState(['currentCompany','pageTitle','pageIcon','productCode']),
    ...mapGetters([
      'getterPageIcon',
    ])
  },
  methods: {
    ...mapMutations(['changeMenuId','setPageTitle','setPageIcon','setPageProductCode']),
    async setPageData() {
      this.loadingIcon = true
      let {data: systemInfo} = await getSystemInfos()
      this.infoData = systemInfo
      console.log('this.infoData',this.infoData)
      if(systemInfo && systemInfo.system_logo && systemInfo.system_name){
        let systemLogo = systemInfo.system_logo.includes('http')
                  ? '//images.weserv.nl/?url=' + systemInfo.system_logo
                  : this.showSrcIp + systemInfo.system_logo
        this.setPageTitle(systemInfo.system_name)
        this.setPageIcon(systemLogo)
        this.setPageProductCode(systemInfo.product_code)
      }else{
        this.setPageTitle('互联网资产攻击面管理平台')
        this.setPageIcon(this.showSrcIp + 'foradar.ico')
        this.setPageProductCode('FORADAR-F9100')

      }
      this.loadingIcon = false

    },
    async downloadLogFunc() {
      this.downloadLogLoading = true
      let res = await downloadLog().catch(() => {
        this.downloadLogLoading = false
      })
      this.download(this.showSrcIp + res.data)
      this.downloadLogLoading = false
    },
    goBack() {
      sessionStorage.setItem('menuId', '1-1')
      this.changeMenuId('1-1')
      this.$router.push('/index')
    },
    immediateActivation() {
      ;(this.ruleFormAdd = {
        type: '1',
        license: '',
        file: ''
      }),
        (this.dialogFormVisible = true)
    },
    ipUploadErrorHd(err, file, fileList) {
      this.$message.error(JSON.parse(err.message).message)
    },
    chainSave() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          let obj = {}
          if (this.ruleFormAdd.type == '1') {
            obj = {
              type: '1',
              license: this.ruleFormAdd.license,
              operate_company_id: this.currentCompany
            }
          } else {
            obj = {
              type: '2',
              file: this.ruleFormAdd.file,
              operate_company_id: this.currentCompany
            }
          }
          let res = await setlicenseAssets(obj)
          if (res.code == 0) {
            // this.$message.success(res.data).then(()=>{
            //   this.dialogFormVisible=false
            //   window.location.reload()
            // })
            this.$message.success(res.data)
            setTimeout(() => {
              this.dialogFormVisible = false
              window.location.reload()
            }, 1000)
          } else {
            this.$message.error(res.data)
          }
        }
      })
    },
    showLincenseAll(text) {
      this.$copyText(text).then(
        (res) => {
          console.log('复制成功：', res)
          this.$message.success('复制成功')
        },
        (err) => {
          console.log('', err)
          this.$message.error('复制失败')
        }
      )
    },
    banningWayChange() {},
    handleExceedHd() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    beforeIpUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 1
      if (!isLt1M) {
        this.$message.error('上传文件不能超过 1MB!')
      }
      return isLt1M
    },
    ipUploadSuccessHd(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.ruleFormAdd.file = response.data.url
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadRemoveHd(file, fileList) {
      this.ruleFormAdd.file = ''
    },
    async getLicense() {
      let res = await getLicenseInfo({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.lincenseObj = res.data
      }
    }
  },
  mounted() {
    this.getLicense()
    this.setPageData()
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .headerTitle {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .btn {
      // height: 30px;
      // line-height: 30px;
      margin-right: 50px;
      padding: 8px 20px;
    }
  }
  .downloadClass1 {
    position: absolute;
    top: -41.5px;
    right: 0px;
    height: 40px;
    line-height: 40px;
    color: #62666c;

    i {
      font-size: 14px;
      color: #2677ff;
      margin: 0 8px 0 16px;
    }
    .el-icon-close {
      position: absolute;
      right: 10px;
      top: 12px;
      font-size: 16px;
      color: #9fa6af;
      // font-weight: bold;
    }
    span {
      color: #2677ff;
    }
    .task {
      display: inline-block;
      max-width: 186px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #37393c;
    }
  }
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
    .uploadFile {
      /deep/.el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    // padding: 20px;
    box-sizing: border-box;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}
.messageText {
  width: 100%;
  padding: 32px;
  color: rgba(98, 102, 108, 1);
  box-sizing: border-box;
  p {
    color: rgba(55, 57, 60, 1);
    font-size: 16px;
  }
  div {
    margin-top: 10px;
  }
}
.localiHeader {
  width: 100%;
  // height: 27%;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  padding: 32px;
  div {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    font-size: 20px;
    font-weight: 600;
    color: rgba(55, 57, 60, 1);
    box-sizing: border-box;
    .icon {
      font-size: 33px;
    }
  }
  p {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    // line-height: 0px;
    color: rgba(98, 102, 108, 1);
    margin-top: 25px;
    span {
      color: rgba(55, 57, 60, 1);
    }
  }
  .textSpecial {
    font-size: 14px;
    font-weight: 400;
    align-items: none;
    letter-spacing: 0px;
    // line-height: 0px;
    color: rgba(98, 102, 108, 1);
    margin-top: 25px;
    display: flex;
    span {
      color: rgba(55, 57, 60, 1);
    }
    .specialDiv {
      margin-top: 0;
    }
  }
  .pSpecial {
    margin-top: 18px;
  }
}
.localiMain {
  width: 100%;
  height: 73%;
  box-sizing: border-box;
  padding: 32px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%),
    linear-gradient(90deg, rgba(240, 246, 255, 1) 0%, rgba(255, 255, 255, 1) 100%);
  .mainTitle {
    display: flex;
    width: 100%;
    height: 28px;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: rgba(55, 57, 60, 1);
  }
  .mainUploader {
    width: 100%;
    height: 50%;
    display: flex;
    justify-content: center;
    margin-top: 8%;
    div {
      cursor: pointer;
      width: 38.7%;
      height: 100%;
      border-radius: 4px;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
      // background-image: url('../../assets/images/localized/banner.png');
      // background-repeat: no-repeat;
      // background-size: 100%;
      position: relative;
      p:nth-child(2) {
        font-size: 14px;
        font-weight: 400;
        color: rgba(55, 57, 60, 1);
      }
      p {
        box-sizing: border-box;
        padding: 20px 0 0 20px;
        display: flex;
        align-items: center;
        img {
          width: 8px;
          height: 8px;
        }
        span {
          margin-left: 9px;
          font-size: 14px;
          font-weight: 500;
          color: rgba(55, 57, 60, 1);
        }
      }
      .statusImg {
        width: 115px;
        height: 122px;
        position: absolute;
        bottom: 0;
        right: 17px;
      }
    }
  }
  .uploadMessage {
    width: 238px;
    height: 28px;
    background: rgba(235, 238, 245, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 400;
    color: rgba(98, 102, 108, 1);
    margin-left: 4px;
  }
  .loaderSpecial {
    margin-left: 31px;
  }
}
.hostoryMain {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
}
.historyBox {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  background: rgba(255, 255, 255, 1);
  margin-top: 15px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
  p {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(55, 57, 60, 1);
  }
}
.specialVersion {
  font-size: 16px;
  font-weight: 500;
  color: rgba(55, 57, 60, 1);
  span {
    // margin-left: 9px;
    font-size: 16px;
    font-weight: 500;
    color: rgba(55, 57, 60, 1);
  }
}
.dialog-body1 {
  min-height: 300px;
  max-height: 500px;
}
.imgBox {
  width: 33px;
  height: 33px;
}
.activateBox {
  width: 100%;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.placeholderIdBox {
  background-color: transparent !important;
  /deep/.el-textarea__inner {
    background-color: transparent !important;
  }
  z-index: 1;
}
.specialDiv {
  width: 100px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.showAll {
  color: #2677ff !important;
  cursor: pointer;
}
</style>
