<template>
  <div>
    <div v-if="isBind" class="text">正在校验请稍后！</div>
    <div v-else class="fromBox">
      <p>绑<i style="padding: 0 10px"></i>定</p>
      <el-form ref="bindform" :model="form" :rules="rules" label-width="0">
        <el-form-item label="" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="" prop="captcha">
          <el-col :span="16">
            <el-input v-model="form.captcha" placeholder="请输入验证码"></el-input>
          </el-col>
          <el-col :span="8" style="background: #fff">
            <img
              class="imgcode"
              :src="keycode"
              @click="getcaptcha"
              width="100%"
              height="100%"
              alt=""
            />
          </el-col>
        </el-form-item>
        <el-form-item label="" prop="code">
          <el-col style="width: 62%" class="code-input">
            <el-input v-model="form.code" placeholder="请输入手机验证码"></el-input>
          </el-col>
          <el-col style="width: 38%; height: 100%; text-align: center">
            <span v-show="show" class="yzm" @click="getcodeData">获取短信验证码</span>
            <span v-show="!show" class="yzm">{{ count }}s后重新获取</span>
          </el-col>
        </el-form-item>
        <el-button class="loginBtn" type="primary" @click="onBind">绑定</el-button>
      </el-form>
      <div class="botShow"
        >Copyright 2023 华顺信安 All Rights Reserved | 京ICP备18024709号-3 | {{ versionData }}</div
      >
    </div>
  </div>
</template>

<script>
import { weixiLogin, weixiBind, captcha, getVersion, getcode } from '@/api/apiConfig/person.js'
export default {
  name: 'wxlogin',
  data() {
    return {
      isBind: true, //是否需要绑定
      form: {
        mobile: '', // foradar1
        captcha: '',
        captcha_key: '',
        code: ''
        // checked: false
      },
      rules: {
        mobile: [{ required: true, message: '请输入用户名', trigger: 'change' }]
      },
      keycode: '',
      show: true,
      count: '',
      versionData: ''
    }
  },
  created() {
    // 判断是否绑定账号
    if (this.$route.query.type == 'success') {
      let obj = {
        type: this.$route.query.type,
        open_id: this.$route.query.openid,
        time: this.$route.query.time,
        sign: this.$route.query.sign
      }
      weixiLogin(obj)
        .then((res) => {
          if (res.code == 0) {
            if (res.data.type == '1') {
              //已经绑定过微信，直接登陆
              let token = 'Bearer' + ' ' + res.data.token.access_token
              localStorage.setItem('token', token)
              localStorage.setItem('websocketToken', res.data.token.access_token)
              localStorage.setItem('tokenTime', res.data.token.expires_in)
              this.$router.push('/home')
            } else {
              //未绑定过微信
              this.isBind = false
            }
          }
        })
        .catch(() => {
          //超时
          this.$router.push('/login')
        })
    } else {
      //扫描失败
      this.$router.push('/login')
    }
  },
  mounted() {
    this.getcaptcha()
    this.getVersionData()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
  methods: {
    //短信验证码
    async getcodeData() {
      if (!this.form.captcha) {
        this.$message.error('请先输入验证码')
        return
      }
      let obj = {
        account: this.form.mobile,
        captcha: this.form.captcha,
        captcha_key: this.form.captcha_key
      }
      let res = await getcode(obj).catch(() => {
        this.getcaptcha()
      })
      if (res.code == 0) {
        this.$message.success('发送成功！')
        this.getCodeTimer() // 倒计时功能
        if (process.env.NODE_ENV == 'production') {
          // 115环境短信验证码生效
          this.getCodeTimer() // 倒计时功能
        } else {
          this.form.code = res.data
        }
      }
    },
    // 图形验证码
    async getcaptcha() {
      let res = await captcha()
      this.keycode = res.data.img
      this.form.captcha_key = res.data.key
    },
    async getVersionData() {
      let res = await getVersion()
      this.versionData = res.data
    },
    getCodeTimer() {
      const TIME_COUNT = 60 // 60秒自动获取，测试阶段设置3秒
      if (!this.timer) {
        this.count = TIME_COUNT
        this.show = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.show = true
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    },
    keyDown(e) {
      if (e.keyCode == 13 || e.keyCode == 108) {
        this.onBind()
      }
    },
    // 绑定
    onBind() {
      this.$refs.bindform.validate(async (valid) => {
        if (valid) {
          let obj = {
            open_id: this.$route.query.openid,
            mobile: this.form.mobile,
            captcha_key: this.form.captcha_key,
            captcha: this.form.captcha,
            code: String(this.form.code)
          }
          let res = await weixiBind(obj)
          if (res.code == 0) {
            let token = 'Bearer' + ' ' + res.data.access_token
            localStorage.setItem('token', token)
            localStorage.setItem('websocketToken', res.data.access_token)
            localStorage.setItem('tokenTime', res.data.expires_in)
            this.$router.push('/home')
          } else {
            this.$router.push('/login')
          }
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.text {
  font-size: 34px;
  font-weight: 600;
  text-align: center;
  padding-top: 200px;
}
.fromBox {
  width: 520px;
  // height: 350px;
  margin: auto;
  margin-top: 200px;
  background: #fff;
  padding: 20px;
  p {
    color: #4d5159;
    font-size: 34px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
  }
  .botShow {
    font-size: 14px;
    font-weight: 500;
    color: #999;
    text-align: center;
    margin-top: 20px;
  }
  .el-form {
    background: #fff;
    margin: 0 auto;
    .el-form-item {
      height: 44px;
      background: rgba(232, 238, 244, 0.76);
      border-radius: 2px;
      border: 1px solid #dce5f0;
      margin-bottom: 32px;
      /deep/.el-input .el-input__inner {
        border: 0;
        height: 44px;
        line-height: 44px;
        background: transparent;
      }
    }
    .loginBtn {
      width: 100%;
      height: 48px;
      margin-top: 52px;
      background: #2677ff;
      box-shadow: 0px 4px 12px 0px rgba(11, 113, 244, 0.4);
      border-radius: 4px;
    }
    .lastBtn {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      color: #37393c;
      & > span {
        cursor: pointer;
      }
    }
    .yzm {
      display: inline-block;
      width: 100%;
      text-align: center;
      vertical-align: middle;
      font-weight: 400;
      color: #2677ff;
      cursor: pointer;
    }
    .imgcode {
      width: 96px;
      height: 44px;
      background: #a0c2e3;
      border-radius: 0px 1px 1px 0px;
      cursor: pointer;
    }
  }
}
</style>
