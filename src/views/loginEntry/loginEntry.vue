<template>
  <div class="container">
    <div class="headerTitle" v-if="!($route.path == '/index')">
      <div>
        <span>登录入口</span>
        <span v-if="notifyFilterId">
          / {{ notifyFilterMsg }}
          <span style="color: #2677ff; cursor: pointer" @click="clearNotifyFilter">恢复默认</span>
        </span>
      </div>
      <div class="statisticnumBox">
        <span class="statisticnum"
          >待确认：<span class="num">{{ loginEntyCountData.default }}</span></span
        >
        <span class="statisticnum"
          >已确认：<span class="num">{{ loginEntyCountData.confirm }}</span></span
        >
        <span class="statisticnum"
          >已忽略：<span class="num">{{ loginEntyCountData.ingore }}</span></span
        >
      </div>
    </div>
    <div class="home_header">
      <div class="filterTab" v-if="!($route.path == '/index')">
        <div>
          <el-input
            v-model="formInline.keyword"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入关键字检索"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            <el-tooltip
              slot="prepend"
              class="item"
              effect="dark"
              content="支持检索字段：网站标题、URL"
              placement="top"
              :open-delay="100"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-input>
          <span @click="highCheckdialog = true" id="poc_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox class="checkboxAll" v-model="checkedAll" id="ip_all" @change="checkAllChange"
            >选择全部</el-checkbox
          >
          <el-button class="normalBtnRe" type="primary" id="ip_all" @click="remove">删除</el-button>
          <el-button class="normalBtn" type="primary" @click="columnChange('', 1, 'more')"
            >确认</el-button
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="exportInfo()"
            :loading="exportLoadingBtn"
            :disabled="exportLoadingBtn"
            >导出</el-button
          >
          <el-dropdown class="dropdownClass" trigger="click">
            <el-button
              class="normalBtn"
              type="primary"
              id="keyword_more_disable"
              style="margin-left: 10px"
            >
              导入
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="addDialogVisible = true">单个导入</el-dropdown-item>
              <el-dropdown-item @click.native="importVisible = true">批量文件导入</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            class="normalBtn"
            type="primary"
            id="ip_all"
            @click="columnChange('', 2, 'more')"
            >忽略</el-button
          >
        </div>
      </div>
      <hightFilter
        id="hightFilter"
        v-if="!($route.path == '/index')"
        :highTabShow="highTabShow"
        :highlist="highlist"
        pageIcon="loginEntry"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()" v-loading="loading">
        <div v-if="tableData.length == 0" class="emptyClass">
          <div>
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-kong"></use>
            </svg>
            <p>暂无数据</p>
          </div>
        </div>

        <div v-else class="cardBox" v-for="(item, index) in tableData" :key="index">
          <div class="cardHeader">
            <el-tooltip
              :open-delay="500"
              class="item"
              effect="dark"
              :content="item.title"
              placement="top"
            >
              <el-checkbox
                v-if="!($route.path == '/index')"
                class="checkboxAll"
                id="ip_all"
                :disabled="item.disabled"
                v-model="item.select"
                :checked="item.select"
                @change="
                  (val) => {
                    checkChange(val, item.id)
                  }
                "
                ><span>{{ item.title == null ? '未知' : item.title }}</span></el-checkbox
              >
              <span class="checkTitle" v-else>{{ item.title == null ? '未知' : item.title }}</span>
            </el-tooltip>
            <span class="editClass" v-if="!($route.path == '/index')" @click="editOne(item)"
              >编辑</span
            >
          </div>
          <div class="cardImg">
            <el-image
              class="imgCardBox"
              @click="clickevaluatePicture(item)"
              :preview-src-list="evaluatePictureList"
              :src="
                item.img_url && item.img_url.includes('http')
                  ? imgUrl + item.img_url
                  : showSrcIp + item.img_url
              "
              lazy
            >
              <div v-loading="currentIndex == index && picLoading" slot="error" class="image-slot">
                <!-- 给当前操作的截图加loading,其余的不可操作 -->
                <span>
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-kong"></use>
                  </svg>
                  <p
                    >暂无图片，<el-button
                      :disabled="picLoading"
                      @click="reGetPic(item.id, index)"
                      type="text"
                      >请重试</el-button
                    ></p
                  >
                </span>
              </div>
            </el-image>
          </div>
          <div class="cardUrl">
            <a :href="item.url" target="_blank" rel="noopener noreferrer" class="url">{{
              item.url
            }}</a>
            <!-- <el-link type="primary" :href="item.url"  target='_blank'>{{item.url}}</el-link> -->
          </div>
          <div class="cardStatus">
            <span>{{ item.updated_at }}</span>
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                {{ setStatus(item.status) }}<i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  @click.native="columnChange(item.id, 1, '')"
                  v-if="item.status !== 1"
                  >确认</el-dropdown-item
                >
                <el-dropdown-item
                  @click.native="columnChange(item.id, 2, '')"
                  v-if="item.status !== 2"
                  >忽略</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
      <el-pagination
        v-if="$route.path != '/index'"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
      <el-dialog
        class="elDialogAdd"
        :close-on-click-modal="false"
        :visible.sync="dialogFormVisibleInsert"
        width="550px"
      >
        <template slot="title"> 编辑 </template>
        <div class="dialog-body">
          <el-form
            :model="ruleForm"
            :rules="rules"
            style="padding: 0 !important"
            ref="ruleForm"
            label-width="60px"
            class="demo-ruleForm"
          >
            <el-form-item label="标题" prop="title">
              <el-input v-model="ruleForm.title" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="截图" prop="img">
              <el-upload
                class="upload-demo"
                drag
                :action="uploadSrcIp + '/assets/account/files'"
                :headers="uploadHeaders"
                accept=".png,.ico,.bmp,.jpg,.jpeg"
                :before-upload="beforeIpUpload"
                :on-success="uploadSuccess"
                :on-remove="uploadRemove"
                :on-error="uploadError"
                :limit="uploadMaxCount"
                :on-exceed="handleExceed"
                list-type="picture"
                :file-list="fileList"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将截图拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip"
                  >支持上传.png,.ico,.bmp,.jpg文件，且大小不超过3M</div
                >
              </el-upload>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button
            class="highBtnRe"
            @click="dialogFormVisibleInsert = false"
            id="keyword_add_cancel"
            >关闭</el-button
          >
          <el-button
            class="highBtn"
            @click="insertSave('ruleForm')"
            :loading="otherLoading"
            id="keyword_add_sure"
            >确定</el-button
          >
        </div>
      </el-dialog>
      <el-drawer
        v-if="!($route.path == '/index')"
        title="高级筛选"
        :visible.sync="highCheckdialog"
        direction="rtl"
        ref="drawer"
      >
        <div class="demo-drawer__content">
          <el-form :model="formInline" ref="drawerForm" label-width="110px">
            <el-form-item label="网站标题：" prop="title">
              <el-select
                filterable
                v-model="formInline.title"
                multiple
                collapse-tags
                placeholder="请选择"
                clearable
                @change="selectChange($event, 'title', condition.title, false, true)"
              >
                <el-option
                  v-for="item in condition.title"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="状态：" prop="status">
              <el-select
                filterable
                v-model="formInline.status"
                placeholder="请选择"
                @change="selectChange($event, 'status', statusArr, true, false)"
                clearable
              >
                <el-option
                  v-for="item in statusArr"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" prop="updated_at_range">
              <el-date-picker
                v-model="formInline.updated_at_range"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="demo-drawer__footer">
            <el-button
              class="highBtnRe"
              @click="resetForm('drawerForm')"
              id="number_filter_repossess"
              >重置</el-button
            >
            <el-button class="highBtn" @click="checkFuncList" id="number_filter_select"
              >筛选</el-button
            >
          </div>
        </div>
      </el-drawer>
    </div>
    <batchImport
      v-if="!($route.path == '/index')"
      title="批量文件导入"
      :btnLoading="importBtnLoading"
      :dialogVisible="importVisible"
      @save="importSave"
      @closeDialog="closeImportDialog"
      downloadForbidUrlName="访问地址导入模板"
      downloadForbidUrl="/downloadTemplate/访问地址导入模板.xlsx"
    />
    <prompt
      title="单个导入"
      placeholder="请输入需要导入的访问地址,支持输入多个,分号或换行分隔"
      :visible="addDialogVisible"
      @save="handleUrlImport"
      @close="addDialogVisible = false"
      :loading="addDialogBtnLoading"
      inputType="textarea"
      label="访问地址"
      :rows="6"
      v-if="!($route.path == '/index')"
    />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import hightFilter from '../../components/assets/highTab.vue'
import batchImport from '@/components/assets/batchImport'
import prompt from '@/components/assets/prompt'
import {
  uploadLoginUrl,
  loginEntyList,
  loginEntyStatus,
  delLoginEnty,
  editLoginEnty,
  regetScreenshot,
  loginEntyCount,
  uploadLoginExport
} from '@/api/apiConfig/api.js'

export default {
  components: {
    hightFilter,
    batchImport,
    prompt
  },
  data() {
    return {
      exportLoadingBtn: false,
      imgUrl: '',
      notifyFilterMsg: '',
      notifyFilterId: '',
      addDialogVisible: false,
      addDialogBtnLoading: false,
      importBtnLoading: false,
      importVisible: false,
      highTabShow: [
        {
          label: '网站标题',
          name: 'title',
          type: 'select'
        },
        {
          label: '状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '更新时间',
          name: 'updated_at_range',
          type: 'date'
        }
      ],
      condition: {},
      currentIndex: '', // 当前截图的数据index
      picLoading: false,
      uploadAction: '',
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      rules: {
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        img: [{ required: true, message: '请上传截图', trigger: 'change' }]
      },
      otherLoading: false,
      dialogFormVisibleInsert: false,
      checkedAll: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      evaluatePictureList: [],
      user: {
        role: ''
      },
      loading: false,
      highlist: null,
      tableData: [],
      checkArr: [],
      formInline: {
        title: [],
        keyword: '',
        status: '',
        updated_at_range: []
      },
      ruleForm: {
        id: '',
        title: '',
        img: ''
      },
      statusArr: [
        {
          name: '待处理',
          id: 0
        },
        {
          name: '已确认',
          id: 1
        },
        {
          name: '已忽略',
          id: 2
        }
      ],
      highCheckdialog: false,
      loginEntyCountData: {},
      fileList: []
    }
  },
  watch: {
    getterCompanyChange(val) {
      if (this.notifyFilterId) {
        this.clearNotifyFilter()
      }
    },
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.user.role == 2) {
        this.getLoginEntryList()
      }
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage', 'getterCompanyChange']),
    ...mapState(['currentCompany'])
  },
  methods: {
    clearNotifyFilter() {
      this.$router.replace({ path: '/loginEntry', query: {} })
    },
    async getLoginEntyCount() {
      let res = await loginEntyCount({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.loginEntyCountData = res.data
      }
    },
    closeImportDialog() {
      this.importVisible = false
    },
    async handleUrlImport(val) {
      if (!val) {
        this.$message.error('请输入需要导入的访问地址')
        return
      }
      let url = val
        ? val
            .split(/[；|;|\r\n]/)
            .filter((item) => {
              return item.trim()
            })
            .map((item) => {
              return item.trim()
            })
        : []
      let res = await uploadLoginUrl({ operate_company_id: this.currentCompany, url }).catch(() => {
        this.addDialogBtnLoading = false
      })
      if (res && res.code == 0) {
        this.getLoginEntryList()
        this.addDialogVisible = false
        this.addDialogBtnLoading = false
        this.$message({
          type: 'success',
          message: '导入成功'
        })
      }
    },
    async importSave(url) {
      this.importBtnLoading = true
      let res = await uploadLoginUrl({ operate_company_id: this.currentCompany, url }).catch(() => {
        this.importBtnLoading = false
      })
      if (res.code == 0) {
        this.importVisible = false
        this.importBtnLoading = false
        this.getLoginEntryList()
      }
    },
    // 重新获取截图
    async reGetPic(id, index) {
      this.currentIndex = index
      this.picLoading = true
      let res = await regetScreenshot({
        id: id,
        type: 1, // 1登录入口，2数据泄露
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.picLoading = false
      })
      if (res.code == 0) {
        this.picLoading = false
        this.tableData[index].img_url = res.data
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getLoginEntryList()
    },
    clickevaluatePicture(row) {
      let url =
        row.img_url && row.img_url.includes('http')
          ? this.imgUrl + row.img_url
          : this.showSrcIp + row.img_url
      var srclist = []
      srclist.push(url)
      this.evaluatePictureList = srclist // 赋值
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getLoginEntryList()
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        title: [],
        keyword: '',
        status: '',
        updated_at_range: []
      }
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    async editOne(row) {
      let url = ''
      if (row.img_url) {
        url = row.img_url.includes('http')
          ? this.imgUrl + row.img_url
          : this.showSrcIp + row.img_url
        this.fileList = [
          {
            name: '截图',
            url
          }
        ]
      } else {
        this.fileList = []
      }
      this.ruleForm.id = row.id
      this.ruleForm.title = row.title ? row.title : '未知'
      this.ruleForm.img = url
      this.uploadUrl = url
      this.dialogFormVisibleInsert = true
    },
    async insertSave(formName) {
      this.ruleForm.img = this.uploadUrl
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.ruleForm.operate_company_id = this.currentCompany
          let obj = { ...this.ruleForm }
          this.otherLoading = true
          let res = await editLoginEnty(obj).catch(() => {
            this.otherLoading = false
          })
          this.otherLoading = false
          if (res.code == 0) {
            this.dialogFormVisibleInsert = false
            this.getLoginEntryList()
            this.$message.success('操作成功！')
          }
        }
      })
    },
    // beforeIpUpload(file){
    //   let isLt1M = ''
    //   if (this.routePath == 'newAssets') {
    //     isLt1M = file.size / 1024 / 1024 < 20;
    //   } else {
    //     isLt1M = file.size / 1024 / 1024 < 45;
    //   }
    //   if (!isLt1M) {
    //     this.$message.error(`上传文件不能超过${this.routePath == 'newAssets' ? '20M' : '45MB'}!`);
    //   }
    //   return isLt1M;
    // },
    beforeIpUpload(file, limit = 3) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    },
    uploadSuccess(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功！')
        if (file.response && file.response.data && file.response.data.url) {
          this.ruleForm.img = file.response.data.url
          this.uploadUrl = file.response.data.url
          // this.$refs.ruleForm.resetFields('img') // 上传后再次表单校验，去掉提示
        }
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadError(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      if (myError.status == 401) {
        this.$router.replace('/login')
        sessionStorage.clear()
        localStorage.clear()
      } else {
        this.$message.error(myError.message)
      }
    },
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      if (res.length == 0) {
        this.ruleForm.img = ''
        this.uploadUrl = ''
      }
    },
    async columnChange(id, val, icon) {
      let obj = {}
      //  全部操作
      if (icon === 'more') {
        // 全部选择
        if (this.checkedAll) {
          obj = {
            id: [],
            operate_company_id: this.currentCompany,
            set_status: val,
            ...this.formInline
          }
        } else {
          if (this.checkArr.length == 0) {
            this.$message.error('请选择数据！')
            return
          }
          obj = {
            id: this.checkArr,
            operate_company_id: this.currentCompany,
            set_status: val,
            ...this.formInline
          }
        }
      } else {
        obj = {
          id: [id],
          operate_company_id: this.currentCompany,
          set_status: val,
          ...this.formInline
        }
      }
      let res = await loginEntyStatus(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.getLoginEntryList()
      }
    },

    async getLoginEntryList() {
      this.checkArr = []
      this.checkedAll = false
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.loading = true
      let res = await loginEntyList(this.formInline).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.tableData = res.data && res.data.items ? res.data.items : []
      this.tableData.map((v) => {
        this.$set(v, 'select', false)
        v.disabled = false
      })
      this.title = res.data.items.title
      this.condition = res.data.condition
      this.total = res.data.total
      if ((!this.total || this.total == 0) && this.notifyFilterId) {
        this.$message.error('该批新增登录入口已被删除')
      }
      this.getLoginEntyCount()
    },
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getLoginEntryList()
    },
    hightFilterIsShow(type) {
      let bol = ''
      if (type) {
        bol = 'tableWrap classTmp'
      }
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      if (this.$route.path == '/index') {
        bol = 'tableWrap tableIndex'
      }
      return bol
    },
    setStatus(status) {
      let str = ''
      switch (status) {
        case 0:
          str = '待处理'
          break
        case 1:
          str = '确认'
          break
        case 2:
          str = '忽略'
          break
        default:
      }
      return str
    },
    checkAllChange(val) {
      this.tableData.map((v) => {
        if (val) {
          v.select = true
          v.disabled = true
        } else {
          v.select = false
          v.disabled = false
        }
      })
    },
    checkChange(value, id) {
      this.tableData.map((v) => {
        if (id == v.id) {
          v.select = value
          // 选中
          if (v.select) {
            this.checkArr.push(id)
          } else {
            let dataTmp = this.checkArr
            let flag = this.checkArr.indexOf(id)
            dataTmp.splice(flag, 1)
            this.checkArr = dataTmp
          }
        }
      })
    },
    async exportInfo() {
      let obj = {}
      if (this.checkedAll) {
        obj = {
          id: [],
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else {
        if (this.checkArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
        obj = {
          id: this.checkArr,
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      }
      try {
        this.exportLoadingBtn = true
        let res = await uploadLoginExport(obj)
        console.log('checkarr', this.checkArr)

        if (res.code == 0) {
          this.download(this.showSrcIp + res.data.url)
          this.tableData.map((v) => {
            if (this.checkArr.indexOf(v.id) != -1) {
              v.select = false
            }
          })
          this.$message.success('导出成功')
          this.exportLoadingBtn = false
          this.checkedAll = false
          this.checkAllChange(false)
        }
      } catch (error) {
        this.exportLoadingBtn = false
      }
    },
    remove() {
      let obj = {}
      if (this.checkedAll) {
        obj = {
          id: [],
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else {
        if (this.checkArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
        obj = {
          id: this.checkArr,
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'poc_del_cancel',
        confirmButtonClass: 'poc_del_sure',
        customClass: 'poc_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await delLoginEnty(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.getLoginEntryList(this.rklEditId)
          }
        })
        .catch(() => {})
    }
  },
  created() {
    this.notifyFilterId = this.$route.query.notifyFilterId
    this.notifyFilterMsg = this.$route.query.notifyFilterMsg
    this.formInline.website_message_id = this.notifyFilterId || ''

    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.imgUrl = this.userInfo.is_local ? '//images.weserv.nl/?url=' : this.imgBaseUrl
    if (this.user.role == 2 && !this.currentCompany) return
    this.getLoginEntryList()
  },

  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .headerTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    .statisticnumBox {
      .statisticnum {
        margin-right: 12px;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
        background: #ffffff;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
        border-radius: 2px;
        padding: 4px 10px;
        border-left: 2px solid #2677ff;
        .num {
          font-weight: 500;
          color: #2677ff;
          margin-right: 0px;
        }
      }
    }
  }
}
/deep/.el-upload-list {
  min-height: 110px !important;
}
.tableIndex {
  height: 100% !important;
}
.tableWrap {
  height: calc(100% - 129px);
  // background: yellow;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  overflow: auto;
  .cardBox {
    width: 24%;
    // height: 48%;
    height: 300px;
    background: red;
    margin-left: 1%;
    border-radius: 4px;
    background: #ffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    padding: 12px 16px 16px 16px;
    box-sizing: border-box;
    margin-top: 10px;
    div {
      margin-top: 10px;
      box-sizing: border-box;
    }
  }
  .emptyClass {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    text-align: center;
    svg {
      display: inline-block;
      font-size: 120px;
    }
    p {
      line-height: 25px;
      color: #d1d5dd;
      span {
        margin-left: 4px;
        color: #2677ff;
        cursor: pointer;
      }
    }
  }
}
/deep/.home_header {
  position: relative;
  height: 100%;
}
.cardBox > div:nth-child(1) {
  margin-top: 0 !important;
}
.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  & > div {
    display: flex;
    align-items: center;
    .normalBtnRe {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .el-input {
      width: 240px;
      margin-right: 12px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      margin-left: 4px;
      cursor: pointer;
    }
  }
}
.checkboxAll {
  font-weight: bold;
}
.imgCardBox {
  width: 100%;
  height: 100%;
  /deep/.image-slot {
    height: 100%;
    width: 100%;
    color: #606266;
    background: #e9ebef;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    text-align: center;
    svg {
      display: inline-block;
      font-size: 60px;
    }
    p {
      width: 100%;
      line-height: 25px;
      color: #d1d5dd;
      span {
        margin-left: 4px;
        color: #2677ff;
      }
      .el-button {
        padding: 0;
      }
    }
  }
}
.cardImg {
  width: 100%;
  height: 70%;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 100%;
    height: 100%;
  }
  div {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.tableWrapFilter {
  height: calc(100% - 224px) !important;
}
.cardUrl {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #2677ff;
  a {
    color: #2677ff;
  }
}
.cardStatus {
  position: relative;
}
.cardStatus > span:nth-child(1) {
  color: rgb(166, 179, 197);
}
.cardStatus {
  .el-dropdown {
    position: absolute !important;
    right: 0;
    top: 0;
    margin-top: 0 !important;
  }
}

.conwrap/deep/ {
  background: none !important;
  & > span {
    display: inline-block;
    width: 28px;
    height: 36px;
    line-height: 36px;
    color: #62666c;
    background: none !important;
  }
}
.classTmp {
  display: flex;
  justify-content: center;
  align-items: center;
}
.cardHeader {
  display: flex;
  justify-content: space-between;
  .checkTitle {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  /deep/.el-checkbox {
    width: 80%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    .el-checkbox__label {
      display: inline !important;
    }
  }
  .editClass {
    display: block;
    width: 40px;
    text-align: right;
    color: #2677ff;
    font-size: 14px;
    cursor: pointer;
  }
}
.el-empty {
  width: 100%;
  height: calc(100% - 129px);
}
.index {
  margin-top: 30px;
}
</style>
