<template>
  <div class="myBox" v-loading="allLoading">
    <div class="boxTwo">
      <div class="eltableBox">
        <div class="groupClass">
          <p style="display: flex; justify-content: space-between">
            <span style="color: #37393c; font-size: 14px">场景选择</span>
          </p>
          <ul
            style="padding: 0px 8px; border: 0; height: calc(100% - 51px); overflow: auto"
            v-loading="groupLoading"
          >
            <li
              :class="currentGroupId == item.id ? 'activeMenuClass' : ''"
              @click="groupClick(item.id)"
              v-for="item in groupArr"
              :key="item.id"
              :index="item.id"
            >
              <el-tooltip
                class="item"
                effect="light"
                placement="top"
                :content="`${item.name}(${item.num})`"
              >
                <span>{{ `${item.name}(${item.num})` }}</span>
              </el-tooltip>
            </li>
          </ul>
        </div>
        <div class="clueWrap">
          <div style="display: flex; justify-content: space-between; margin: 20px 0">
            <div class="confirmBox">
              <el-radio-group v-model="tabActiveName" @change="handleClick(tabActiveName)">
                <el-radio-button
                  :label="item.name"
                  v-for="(item, index) in tabData.filter((item) => {
                    return (
                      !item.has_subdomain_tab || this.has_subdomain_tab == item.has_subdomain_tab
                    )
                  })"
                  :key="index"
                  >{{ item.label }}({{ item.count }})</el-radio-button
                >
              </el-radio-group>
            </div>
            <div
              style="width: 40%; display: flex; justify-content: space-between; align-items: center"
            >
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAllObj[checkedAllArr[tabActiveName]]"
                @change="checkAllChange"
                >选择全部</el-checkbox
              >
              <el-button
                class="normalBtnRe"
                style="margin-right: 20px"
                type="primary"
                @click="reserveSelect"
                :disabled="checkedAll"
                >一键反选</el-button
              >
              <el-input
                style="width: 75%"
                v-if="tabActiveName != 3"
                @keyup.enter.native="getClueData('tmp')"
                v-model="keywordObj[tabKeywordArr[tabActiveName]]"
                :placeholder="`请输入${getLabel()}检索`"
                clearable
                id="cloud_keycheck"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="getClueData('tmp')"
                ></el-button>
              </el-input>
            </div>
          </div>
          <div style="height: calc(100% - 140px)">
            <el-table
              border
              :data="tableData"
              row-key="id"
              :header-cell-style="{ background: '#F5F7FA', color: '#62666C' }"
              @selection-change="handleSelectionChange"
              @cell-mouse-enter="showTooltip"
              @cell-mouse-leave="hiddenTooltip"
              :ref="'eltable' + tabActiveName"
              height="99%"
              style="width: 100%"
            >
              <template slot="empty">
                <div class="emptyClass">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-kong"></use>
                  </svg>
                  <p>暂无数据</p>
                </div>
              </template>
              <el-table-column
                type="selection"
                align="center"
                :reserve-selection="true"
                :selectable="handleSelectable"
                width="55"
              >
              </el-table-column>
              <el-table-column
                v-for="item in tableHeader"
                :key="item.id"
                :prop="item.name"
                align="left"
                :min-width="item.minWidth"
                :fixed="item.fixed"
                :label="item.label"
              >
                <template slot="header">
                  <span v-if="item.name == 'content'">{{ getLabel() }}</span>
                  <span v-else>{{ item.label }}</span>
                </template>
                <template slot-scope="scope">
                  <span
                    v-if="tabActiveName == '3' && item.name == 'content' && scope.row[item.name]"
                    style="display: flex; align-items: center"
                  >
                    <el-image
                      class="imgwrap"
                      :src="
                        scope.row[item.name].includes('http')
                          ? scope.row[item.name]
                          : showSrcIp + scope.row[item.name]
                      "
                      alt=""
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                    <span>{{ scope.row.hash }}</span>
                  </span>
                  <span
                    v-else-if="
                      tabActiveName == '3' && item.name == 'content' && !scope.row[item.name]
                    "
                    >-</span
                  >
                  <span v-else>
                    <span v-if="item.name == 'clue_company_name'">
                      <span v-if="scope.row['type'] != 3">
                        <el-tooltip
                          class="item"
                          effect="light"
                          placement="top"
                          :open-delay="500"
                          v-if="scope.row[item.name]"
                        >
                          <div slot="content">
                            <span style="padding: 2px 0px; display: inline-block">{{
                              scope.row[item.name] ? scope.row[item.name] : '-'
                            }}</span>
                            <span
                              v-if="
                                scope.row['equity_percent'] && scope.row['equity_percent'] != '-'
                              "
                              class="kgBox"
                            >
                              <el-tag>
                                <span style="color: #37393c">
                                  控股比例：{{ scope.row['equity_percent'] }}%
                                </span>
                              </el-tag>
                            </span>
                          </div>
                          <span>
                            <span style="padding: 2px 0px; display: inline-block">{{
                              scope.row[item.name] ? scope.row[item.name] : '-'
                            }}</span>
                            <span
                              v-if="
                                scope.row['equity_percent'] && scope.row['equity_percent'] != '-'
                              "
                              class="kgBox"
                            >
                              <el-tag>
                                <span style="color: #37393c">
                                  控股比例：{{ scope.row['equity_percent'] }}%
                                </span>
                              </el-tag>
                            </span>
                          </span>
                        </el-tooltip>
                        <span v-else style="padding: 2px 0px; display: inline-block">-</span>
                      </span>
                      <span v-else style="padding: 2px 0px; display: inline-block">-</span>
                    </span>
                    <span v-else-if="item.name == 'content'">
                      <span v-if="scope.row[item.name] && scope.row[item.name].length > 0">
                        <el-tooltip class="item" effect="light" placement="top">
                          <div slot="content">
                            {{
                              $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '')
                            }}{{
                              scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                            }}
                          </div>
                          <span>{{
                            $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '-')
                          }}</span>
                        </el-tooltip>
                      </span>
                    </span>
                    <span v-else-if="item.name == 'chain_list'">
                      <span v-if="scope.row[item.name] && scope.row[item.name].length > 0">
                        <el-tooltip
                          class="item"
                          effect="light"
                          placement="top"
                          popper-class="chainClass"
                        >
                          <div slot="content" style="position: relative">
                            <el-tooltip
                              effect="light"
                              class="item"
                              placement="top"
                              content="一键复制"
                              v-if="scope.row[item.name] && scope.row[item.name].length != 0"
                              :open-delay="500"
                            >
                              <i
                                class="el-icon-document-copy"
                                @click="copyClick(scope.row[item.name])"
                                style="
                                  color: #2677ff;
                                  cursor: pointer;
                                  position: absolute;
                                  right: -6px;
                                  top: 0;
                                "
                              ></i>
                            </el-tooltip>
                            <span
                              v-for="(con, index) in getChains(scope.row[item.name])"
                              :key="index"
                            >
                              <span v-if="con.type && con.type == 3">
                                <el-image
                                  :src="
                                    con.content.includes('http')
                                      ? con.content
                                      : showSrcIp + con.content
                                  "
                                >
                                  <div slot="error" class="image-slot">
                                    <i class="el-icon-picture-outline"></i>
                                  </div>
                                </el-image>
                              </span>
                              <span v-else
                                >{{ $punyCode.toUnicode(con.content || con)
                                }}{{
                                  con.punycode_domain ? '(' + con.punycode_domain + ')' : ''
                                }}</span
                              >
                              <i
                                v-if="index < getChains(scope.row[item.name]).length - 1"
                                class="el-icon-right iconRight"
                              ></i>
                            </span>
                          </div>
                          <span style="display: flex !important; align-items: center">
                            {{ scope.row[item.name].length - 1 }}
                            <img
                              src="../../assets/images/chain.svg"
                              alt=""
                              style="width: 12px; margin-left: 5px; vertical-align: middle"
                            />
                          </span>
                        </el-tooltip>
                      </span>
                      <span v-else>-</span>
                    </span>
                    <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="pageSizeArr"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="total"
          >
          </el-pagination>
          <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
        </div>
      </div>
      <div class="footer">
        <el-select
          style="width: 110px"
          v-model="recommendTimeRange"
          default-first-option
          placeholder="请选择资产梳理范围"
        >
          <template #prefix>
            <span style="padding-left: 5px; height: 32px; line-height: 32px">
              <el-tooltip
                placement="top"
                :open-delay="500"
                content="资产梳理范围：支持配置从fofa获取数据的时间范围"
              >
                <i class="el-icon-info"></i>
              </el-tooltip>
            </span>
          </template>
          <el-option label="全部" :value="0"> </el-option>
          <el-option label="近一年" :value="1"> </el-option>
          <el-option label="近半年" :value="2"> </el-option>
        </el-select>
        <div class="bottom-check" v-if="user.role == 2">
          <el-checkbox v-model="is_need_hunter" :true-label="1" :false-label="0" :disabled="true"
            >hunter导入</el-checkbox
          >
          <el-tooltip
            placement="top"
            :open-delay="500"
            content="支持自动获取近一个月内的Hunter数据"
          >
            <i class="el-icon-info" style="padding-left: 5px"></i>
          </el-tooltip>
        </div>
        <div class="bottom-check" v-if="user.role == 2">
          <el-checkbox v-model="is_need_dnschecker" :true-label="1" :false-label="0"
            >全球DNS解析</el-checkbox
          >
          <el-tooltip placement="top" :open-delay="500" content="支持自动获取域名的实时DNS解析数据">
            <i class="el-icon-info" style="padding-left: 5px"></i>
          </el-tooltip>
        </div>
        <div class="bottom-check" v-if="user.role == 2 && !userInfo.is_local">
          <el-checkbox v-model="is_auto_expend_ip" :true-label="1" :false-label="0"
            >自动推导IP</el-checkbox
          >
          <el-tooltip placement="top" :open-delay="500" content="根据推荐拿到的资产，自动关联同C段相临近IP的资产数据">
            <i class="el-icon-info" style="padding-left: 5px"></i>
          </el-tooltip>
        </div>
        <el-button
          class="normalBtn"
          :loading="allLoading"
          type="primary"
          @click="cloudRecommendFun"
          id="unit_clue"
          >云端资产推荐</el-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import * as animationData from '../../assets/images/data.json'
import Lottie from 'vue-lottie/src/lottie.vue'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import clueTable from '../unit_surveying/taskSecondTable.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { cluesGroupList, tabNumClues, cluesList } from '@/api/apiConfig/clue.js'
import { recommend } from '@/api/apiConfig/recommend.js'
import { endDetectTask } from '@/api/apiConfig/surveying.js'

export default {
  components: { Lottie, clueTable, tableTooltip },
  props: ['taskInfoData'],
  data() {
    return {
      is_need_hunter: 0,
      is_need_dnschecker: 0,
      recommendTimeRange: 0,
      allLoading: false,
      onekey: true,
      clue: '初始线索',
      clueTitle: '依据企业名称获取单位资产初始线索',
      clueText: '成功获取企业初始线索',
      currentPercent: 0, //进度条
      defaultOptions: {
        //json动画
        animationData: animationData.default
      },
      currentGroupIds: '',
      groupLoading: false,
      groupArr: [],
      dialogFormVisibleInsert: false,
      tabActiveName: '0',
      againIsTrue: true, // 重新获取的按钮
      progressBar: true, //是否显示进度条
      tabTotal: 0,
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      tableData: [],
      task_id: '',
      group_id: '',
      loading: false,
      keywordObj: {
        domain_keyword: '',
        cert_keyword: '',
        icp_keyword: '',
        key_keyword: '',
        ip_keyword: ''
      },
      checkedAllObj: {
        domain_checked: false,
        cert_checked: false,
        icp_checked: false,
        icon_checked: false,
        key_checked: false,
        ip_checked: false,
        subdomain_checked: false
      },
      tabData: [
        {
          name: '0',
          label: '域名',
          count: ''
        },
        {
          name: '1',
          label: '证书',
          count: ''
        },
        {
          name: '2',
          label: 'ICP',
          count: ''
        },
        {
          name: '3',
          label: 'ICON',
          count: ''
        },
        {
          name: '4',
          label: '关键词',
          count: ''
        },
        {
          name: '5',
          label: '子域名',
          count: '',
          has_subdomain_tab: 1
        },
        {
          name: '6',
          label: 'IP段',
          count: ''
        }
      ],
      has_subdomain_tab: 0,
      tableHeader: [
        {
          label: '关键词',
          name: 'content'
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          minWidth: '150px'
        },
        {
          label: '来源',
          name: 'source_label'
        },
        {
          label: '添加时间',
          name: 'created_at'
        },
        {
          label: '线索链',
          name: 'chain_list'
        }
      ],
      checkedArr0: [],
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      formInline: {
        no_page: '', // 1 没有分页，不传值 有分页
        keyword: '',
        page: 1,
        per_page: 10,
        status: 1, // 已确认线索
        group_id: '' // 安服角色分组id
      },
      groupCurrentPage: 1,
      groupPageSize: 10,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      currentGroupId: '',
      checkedAll: false
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (this.userInfo) {
        this.user = this.userInfo.user
      }
      this.tableData = []
      sessionStorage.removeItem('scrollTop')
      this.getTaskResultData(true)
      // 延时解决获取sessionStorage.getItem('companyInfo')接口没有响应数据
      setTimeout(() => {
        if (this.currentCompany == -1) {
          // 安服账号本身,不用配置就有权限
          this.has_subdomain_tab = 1
        } else {
          // 安服操作的企业,要你根据用户管理的配置判断
          this.has_subdomain_tab = sessionStorage.getItem('companyInfo')
            ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.has_subdomain_tab
            : ''
        }
      }, 100)
    },
    getterRecommentData(val) {
      this.setRecomment()
    },
    taskInfoData(val) {
      // this.getTaskResultData()
    },
    // 通过哪个分类扩展，左侧就不显示哪个分类的导航
    accuracyIndex(val) {}
  },
  computed: {
    ...mapState(['currentCompany', 'recommentData']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage', 'getterRecommentData'])
  },
  created() {
    this.setRecomment() // 企业线索库跳转带过来的数据
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
      if (this.user.role == 2) {
        if (!this.currentCompany) {
          return
        }
        this.getTaskResultData()
      } else {
        this.getTaskResultData()
      }
    }
    // 子域名推荐权限
    if (this.user.role == 1) {
      // 超管账号有权限
      this.has_subdomain_tab = 1
    } else if (this.user.role == 2) {
      // 安服
      if (this.currentCompany) {
        if (this.currentCompany == -1) {
          // 安服账号本身,不用配置就有权限
          this.has_subdomain_tab = 1
        } else {
          // 安服操作的企业,要你根据用户管理的配置判断
          this.has_subdomain_tab = sessionStorage.getItem('companyInfo')
            ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.has_subdomain_tab
            : ''
        }
      }
    } else {
      // 企业账号
      this.has_subdomain_tab = this.user ? this.user.has_subdomain_tab : ''
    }
  },
  mounted() {
    sessionStorage.removeItem('scrollTop')
  },
  methods: {
    ...mapMutations(['recommentFlagChange', 'saveRecommentData', 'changeMenuId']),
    setRecomment() {
      let recommentData = this.recommentData ? this.recommentData : null
      if (recommentData) {
        this.keywordObj = recommentData.keywordObj
        this.checkedAllObj = recommentData.checkedAllObj
        this.currentGroupId = recommentData.group_id
        this.tabActiveName = recommentData.tabActiveName
        this.checkedArr0 = recommentData.checkedArr0
        this.checkedArr1 = recommentData.checkedArr1
        this.checkedArr2 = recommentData.checkedArr2
        this.checkedArr3 = recommentData.checkedArr3
        this.checkedArr4 = recommentData.checkedArr4
        this.checkedArr5 = recommentData.checkedArr5
        this.checkedArr6 = recommentData.checkedArr6
      }
    },
    // 反选
    reserveSelect() {
      this.tableData.forEach((item) => {
        item.flg = false
        this['checkedArr' + this.tabActiveName].forEach((el) => {
          if (el.id == item.id) {
            item.flg = true // 代表已经选中
          }
        })
        if (item.flg) {
          this.$refs['eltable' + this.tabActiveName].toggleRowSelection(item, false)
        } else {
          this.$refs['eltable' + this.tabActiveName].toggleRowSelection(item, true)
        }
      })
    },
    // 获取分组列表数据
    async getTaskResultData(isChangeCompany = false) {
      // // 调用之前清空选择项
      // this.checkedAll=false
      // this.$refs['eltable' + this.tabActiveName].clearSelection();
      let obj = {
        page: this.groupCurrentPage,
        per_page: 10,
        no_page: '1',
        operate_company_id: this.currentCompany
      }
      this.groupLoading = true
      let res = await cluesGroupList(obj)
        .catch(() => {
          this.groupLoading = false
        })
        .catch(() => {
          this.groupArr = []
        })
      this.groupLoading = false
      let arr = res.data
      arr.forEach((item) => {
        item['num'] = item.tab_num ? item.tab_num[1].count : 0 // 每个分组线索数量
      })
      this.groupArr = arr
      if (!this.currentGroupId || isChangeCompany) {
        // 当前分组id不存在
        this.currentGroupId = this.groupArr.length > 0 ? String(this.groupArr[0].id) : ''
      }
      if (!this.currentGroupId) {
        return
      }
      this.getClueData()
      this.getTabNum() // tab数量
    },
    handleClick(data) {
      //icp和证书时要有企业名称
      if (data == 0 || data == 1 || data == 2 || data == 6) {
        this.tableHeader = [
          {
            label: '关键词',
            name: 'content'
          },
          {
            label: '企业名称',
            name: 'clue_company_name',
            minWidth: '150px'
          },
          {
            label: '来源',
            name: 'source_label'
          },
          {
            label: '添加时间',
            name: 'created_at'
          },
          {
            label: '线索链',
            name: 'chain_list'
          }
        ]
      } else {
        this.tableHeader = [
          {
            label: '关键词',
            name: 'content'
          },
          {
            label: '来源',
            name: 'source_label'
          },
          {
            label: '添加时间',
            name: 'created_at'
          },
          {
            label: '线索链',
            name: 'chain_list'
          }
        ]
      }
      this.currentPage = 1
      this.getClueData()
      this.getTabNum()
    },
    getChains(row) {
      let arr = row.filter((item) => {
        return item.content
      })
      return arr
    },
    copyClick(tmp) {
      let data = []
      data.push(tmp)
      let text = ''
      data.map((item) => {
        this.getChains(item).map((v, i) => {
          if (v.type && v.type == 3) {
            if (i == item.length - 1 && v) {
              text += 'icon' + '\n'
            } else {
              text += 'icon' + '-->'
            }
          } else {
            if (i < item.length - 1 && v) {
              text += v.content + '-->'
            } else if (i == item.length - 1 && v) {
              text += v.content + '\n'
            } else {
              text += v.content
            }
          }
        })
      })
      this.$copyText(text).then(
        (res) => {
          this.$message.success('复制成功')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    groupClick(id) {
      this.currentGroupId = String(id)
      this.tableData = []
      this.checkedAllObj = {
        domain_checked: false,
        cert_checked: false,
        icp_checked: false,
        icon_checked: false,
        key_checked: false,
        ip_checked: false,
        subdomain_checked: false
      }
      this.keywordObj = {
        domain_keyword: '',
        cert_keyword: '',
        icp_keyword: '',
        key_keyword: '',
        ip_keyword: '',
        subdomain_keyword: ''
      }
      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []

      this.$nextTick(() => {
        this.$refs['eltable' + this.tabActiveName].clearSelection()
      })
      this.getClueData('tmp')
      this.getTabNum()
    },
    async getTabNum() {
      let obj = {
        group_id: this.currentGroupId,
        data: {
          status: 1, // 云端推荐只展示已确认的数据
          operate_company_id: this.currentCompany
        }
      }
      let res = await tabNumClues(obj)
      this.tabNumStatus = 0 // 获取总数，用于删除、导出等操作无数据提示
      if (res.data && res.data.clues_count) {
        this.tabData.forEach((item) => {
          res.data.clues_count.forEach((ch) => {
            if (item['name'] == ch['type']) {
              item.count = ch.count
            }
          })
        })
      }
    },
    async getClueData(tmp, flag) {
      // 忽略:2,确认:1,待确认：0
      // 搜索page为1
      if (tmp == 'tmp') {
        this.currentPage = 1
        this.formInline.page = 1
      } else {
        this.formInline.page = this.currentPage
      }
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.group_id = this.currentGroupId // 分组id
      this.formInline.no_page = ''
      this.formInline.keyword = this.keywordObj[this.tabKeywordArr[this.tabActiveName]]
      let obj = {
        type: this.tabActiveName,
        query: this.formInline,
        operate_company_id: this.currentCompany
      }
      this.loading = true
      let res = await cluesList(obj).catch(() => {
        this.loading = false
      })
      this.loading = false
      let arr = res.data && res.data.items ? res.data.items : []
      let arrayNew = []
      arr.map((item) => {
        arrayNew.push(Object.assign({}, item, { type: this.tabActiveName }))
      })
      this.tableData = arrayNew
      this.total = res.data && res.data.total ? res.data.total : 0
      // 全选操作；一种是直接在云端推荐操作，另一种是从企业线索库跳转过来的
      this.$nextTick(() => {
        if (this.checkedAllObj[this.checkedAllArr[this.tabActiveName]]) {
          this.tableData.forEach((row) => {
            this.$refs['eltable' + this.tabActiveName].toggleRowSelection(row, true)
          })
        } else {
          this['checkedArr' + this.tabActiveName].forEach((el) => {
            this.tableData.forEach((row) => {
              if (el.id == row.id) {
                this.$refs['eltable' + this.tabActiveName].toggleRowSelection(row, true)
              }
            })
          })
        }
      })
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    // 云端资产推荐
    async cloudRecommendFun() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要下发的数据！')
        return
      }
      this.allLoading = true
      let clueData = [
        {
          id: this.checkedAllObj.domain_checked
            ? []
            : this.checkedArr0.map((item) => {
                return item.id
              }),
          type: '0',
          is_all: this.checkedAllObj.domain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.cert_checked
            ? []
            : this.checkedArr1.map((item) => {
                return item.id
              }),
          type: '1',
          is_all: this.checkedAllObj.cert_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icp_checked
            ? []
            : this.checkedArr2.map((item) => {
                return item.id
              }),
          type: '2',
          is_all: this.checkedAllObj.icp_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.icon_checked
            ? []
            : this.checkedArr3.map((item) => {
                return item.id
              }),
          type: '3',
          is_all: this.checkedAllObj.icon_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.key_checked
            ? []
            : this.checkedArr4.map((item) => {
                return item.id
              }),
          type: '4',
          is_all: this.checkedAllObj.key_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.subdomain_checked
            ? []
            : this.checkedArr5.map((item) => {
                return item.id
              }),
          type: '5',
          is_all: this.checkedAllObj.subdomain_checked ? '1' : ''
        },
        {
          id: this.checkedAllObj.ip_checked
            ? []
            : this.checkedArr6.map((item) => {
                return item.id
              }),
          type: '6',
          is_all: this.checkedAllObj.ip_checked ? '1' : ''
        }
      ]
      let objData = {
        tab_status: 1,
        group_id: this.currentGroupId,
        fofa_range: this.recommendTimeRange,
        is_need_hunter: this.is_need_hunter,
        is_need_dnschecker: this.is_need_dnschecker,
        is_auto_expend_ip: this.is_auto_expend_ip,
        data: clueData,
        keyword: {
          domain_keyword: this.keywordObj.domain_keyword,
          cert_keyword: this.keywordObj.cert_keyword,
          icp_keyword: this.keywordObj.icp_keyword,
          key_keyword: this.keywordObj.key_keyword,
          key_keyword: this.keywordObj.key_keyword,
          ip_keyword: this.keywordObj.ip_keyword
        },
        operate_company_id: this.currentCompany
      }
      let res = await recommend(objData).catch(() => {
        this.allLoading = false
      })
      if (res.code == 0) {
        this.$nextTick(() => {
          this.$refs['eltable' + this.tabActiveName].clearSelection()
        })
        this.saveRecommentData('') // 下发推荐任务后清掉企业线索带过来的数据
        this.recommentFlagChange(res.data.flag) // 存储云端推荐flag
        this.$emit('son', '3', res.data.expend_id)
      }
      this.allLoading = false
    },
    getLabel() {
      let label = ''
      if (this.tabActiveName == '0') {
        label = '根域'
      } else if (this.tabActiveName == '1') {
        label = '证书'
      } else if (this.tabActiveName == '2') {
        label = 'ICP'
      } else if (this.tabActiveName == '3') {
        label = 'ICON'
      } else if (this.tabActiveName == '4') {
        label = '关键词'
      } else if (this.tabActiveName == '5') {
        label = '子域名'
      } else if (this.tabActiveName == '6') {
        label = 'IP段'
      }
      return label
    },
    checkAllChange() {
      if (this.checkedAllObj[this.checkedAllArr[this.tabActiveName]]) {
        this.tableData.forEach((row) => {
          this.$refs['eltable' + this.tabActiveName].toggleRowSelection(row, true)
        })
      } else {
        this.$refs['eltable' + this.tabActiveName].clearSelection()
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAllObj[this.checkedAllArr[this.tabActiveName]]
    },
    handleSelectionChange(val) {
      let arr = this.$refs['eltable' + this.tabActiveName].selection.filter((item) => {
        return item.type == this.tabActiveName
      })
      this['checkedArr' + this.tabActiveName] = arr // .selection获取当前选中的数据
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getClueData('', true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getClueData('', true)
    },
    // 结束流程
    async ending() {
      this.$confirm('确定结束流程?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_end_cancel',
        confirmButtonClass: 'unit_end_sure',
        customClass: 'unit_end',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            expend_id: this.task_id,
            operate_company_id: this.currentCompany
          }
          let res = await endDetectTask(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.$emit('son', '1')
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.unit_end>.el-message-box__btns')
        del.children[0].id = 'unit_end_cancel'
        del.children[1].id = 'unit_end_sure'
      }, 50)
    }
  },
  beforeDestroy() {
    clearInterval(this.setTimer)
    this.setTimer = null
    sessionStorage.removeItem('scrollTop')
    this.saveRecommentData('')
  }
}
</script>
<style lang="less" scoped>
.elDialogAdd1 {
  margin-top: 20vh !important;
}
/deep/.el-dialog__body {
  min-height: 110px !important;
}
.dialog-body > div > div {
  color: #62666c;
  margin-bottom: 16px;
}
.el-table .cell {
  overflow: hidden !important;
  white-space: nowrap !important;
}
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.suspected {
  color: #f2b824;
  background: rgba(248, 193, 54, 0.16);
  border-radius: 2px;
  border: 1px solid #f8c136;
  padding: 2px 6px;
}
/deep/.el-table .warning-row {
  background: #fdecec;
}
/deep/.el-icon-warning {
  color: #e94747;
  font-size: 16px;
  vertical-align: middle;
  margin-right: 2px;
}
.eltableBox {
  display: flex;
  padding: 0px 20px 0px 0px;
  height: 98.5% !important;
  margin-bottom: 10px;
  justify-content: space-between;
  .groupClass {
    width: 21%;
    height: 100%;
    background: #fff;
    border-right: 1px solid #e9ebef;
    p {
      padding: 16px;
      font-weight: 600;
    }
    .activeMenuClass {
      color: #2677ff;
      border-left: 4px solid #2677ff;
      background: #eff2f7;
    }
    ul {
      height: calc(100% - 53px);
      overflow: auto;
      li {
        padding: 10px 16px;
        line-height: 20px;
        cursor: pointer;
        color: #62666c;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        i {
          color: #62666c;
          margin-left: 6px;
          cursor: pointer;
        }
      }
    }
  }
  .clueWrap {
    width: 77%;
    height: 100%;
    background: #fff;
    padding: 0 20px;
    .confirmBox {
      // width: 50%;
      margin-left: 0 !important;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      height: 20px;
      .dropdownClass {
        margin-right: 10px;
      }
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input--suffix {
          i {
            line-height: 26px;
          }
        }
      }
      .el-tabs__header {
        height: 33px;
        border-bottom: 0;
      }
      .el-tabs__nav {
        padding-left: 0;
        height: 33px;
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #e4e7ed;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        text-align: center;
        background: rgba(38, 119, 255, 0.1);
        border-radius: 0;
        border: 1px solid #2677ff;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
        transition: none;
      }
    }
    .tableWrap {
      height: calc(100% - 354px);
    }
    .tableWrapTwo {
      height: calc(100% - 234px);
    }
    /deep/.el-table {
      border: 0;
      .el-table__body td.el-table__cell > div {
        padding: 12px;
      }
    }
  }
}
.myTable {
  width: calc(100% - 170px);
  padding: 0px !important;
  height: calc(100% - 12px) !important;
}
.myTableContent {
  min-height: calc(100% - 106px);
  height: calc(100% - 106px);
  overflow: auto;
  background-color: #fff;
}
.myTableHeader {
  display: flex;
  align-items: center;
  height: 52px;
  font-weight: 600;
  color: #62666c;
  font-size: 12px;
  margin-left: 3px;
  background: #f2f3f5;
  border: 1px solid #e4e8ef;
  .chooseBox {
    padding: 0px 14px 0px 10px;
    width: 55px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
  }
  .operationBox {
    padding: 0px 10px;
    width: 55px;
    box-sizing: border-box;
  }
  .shuBox {
    width: 10px;
  }
}
.tableHeaderBox {
  flex-grow: 1;
}
.tableHeaderBox > div {
  padding: 0px 10px;
  box-sizing: border-box;
}
.qiyeBox {
  padding: 0px 0px 0px 10px !important;
}
/deep/.el-table {
  border: none !important;
}
.eltable_domain {
  border-left: 4px solid #2677ff !important;
  border-radius: 4px 0px 0px 4px;
}
.eltable_ip {
  border-left: 4px solid #1059d5 !important;
}
/deep/.boxTwo1 {
  height: calc(100% - 156px) !important;
  padding-top: 2px !important;
}
/deep/.boxTwo {
  height: calc(100% - 63px) !important;
  padding-top: 2px !important;
}
.eltable_icp {
  border-left: 4px solid #05d4a7 !important;
}
.eltable_cert {
  border-left: 4px solid #13b7ff !important;
}
.eltable_icon {
  border-left: 4px solid #ec8f3c !important;
}
.tableLabel {
  padding: 0px 0px 0px 4px !important;
  margin-bottom: 10px !important;
  margin-top: 10px !important;
}
.zhanwu {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
.bottom-check {
  display: flex;
  align-items: center;
  color: #606266;
  height: 30px;
  padding: 0 10px;
  border-radius: 4px;
  margin-left: 10px;
  border: 1px solid #d1d5dd;
  font-weight: 400;
}
.el-icon-info {
  color: #c0c4cc;
}
</style>
