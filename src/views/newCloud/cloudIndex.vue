<template>
  <div v-if="!recommentIsShow" class="container">
    <div class="headerTitle">
      <span class="goback" @click="goBack"><i class="el-icon-arrow-left"></i>返回</span
      ><span class="spline">/</span>
      云端推荐任务
      <span class="headerShow"
        ><i class="el-icon-warning"></i
        >支持自定义线索进行资产的推荐，帮助用户快速、精准的获取资产数据，适用于不同场景资产盘点</span
      >
    </div>
    <div class="home_header">
      <div class="tabsWrap">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="推荐任务" name="first"> </el-tab-pane>
          <el-tab-pane label="任务简报" name="second"> </el-tab-pane>
        </el-tabs>
        <span @click="recommentIsShow = true" class="recommentViewClass"
          ><i class="el-icon-s-cooperation"></i>推荐资产库<i class="el-icon-right"></i
        ></span>
      </div>
      <div
        class="tab_content"
        :style="activeName == 'first' ? 'height: calc(100% - 64px);' : 'height: calc(100% - 44px);'"
      >
        <unitTask v-if="activeName == 'first'" :taskId="taskId" />
        <unitRecord v-if="activeName == 'second'" @son="sonData" />
      </div>
    </div>
  </div>
  <ipAssets v-else @closeLog="closeLog" />
</template>

<script>
import unitTask from './cloudTask.vue'
import unitRecord from '../unit_surveying/unitRecord.vue'
import ipAssets from '../assetsView/ipAssets.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
export default {
  components: { unitRecord, unitTask, ipAssets },
  data() {
    return {
      recommentIsShow: false,
      activeName: 'first',
      taskId: ''
    }
  },
  watch: {
    getterWebsocketMessage(msg) {}
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage'])
  },
  mounted() {
    if (this.$route.query.activeName) {
      this.activeName = this.$route.query.activeName
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goBack() {
      sessionStorage.setItem('menuId', '2-1')
      this.changeMenuId('2-1')
      this.$router.push('/assetsTanzhi')
    },
    // 关闭推荐资产库
    closeLog() {
      this.recommentIsShow = false
    },
    // 用于任务记录查看详情
    sonData(val) {
      this.activeName = 'first'
      this.taskId = val
    },
    handleClick(tab) {
      this.$router.push({
        query: {
          activeName: tab.name || 'first'
        }
      })
      this.taskId = ''
      // this.$router.push({path:"assetsCloud",activeName:{}})
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  // background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    & > .tabsWrap {
      position: relative;
      .el-tabs__nav {
        padding-left: 20px;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        // min-width: 60px;
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        // padding: 0;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
      }
    }
  }
}
/deep/.el-table {
  width: 99%;
  border: 0;
  .detail {
    padding: 0 0;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ebeef5;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    img {
      vertical-align: middle;
    }
    p {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    div {
      div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .el-table__body td.el-table__cell {
    padding: 0 !important;
  }
  /deep/.el-table__body .cell {
    padding: 0 !important;
  }
  .detail:last-child {
    border-bottom: 0;
  }
  .cell-other {
    padding: 0 0;
  }
  // 定义单元格文本超出不换行
  /deep/.cell-other {
    // width: 140px !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
  }
}
/deep/.tableWrap {
  padding: 0 20px;
}
/deep/.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  & > div {
    .el-input {
      width: 240px;
    }
    .el-select {
      width: 240px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      // margin-left: 16px;
      cursor: pointer;
    }
  }
}
</style>
