<template>
  <div class="Box">
    <stepBar
      style="margin: 20px 0 16px 0"
      :active.sync="taskStep"
      :taskStep="taskStep"
      :stepArr="stepArr"
      :width="stepWidth"
    />
    <div class="myContent" v-loading="loading">
      <taskFirst v-if="taskStep == '1'" @son="sonDataFirst" />
      <taskSecond v-if="taskStep == '3'" :taskInfoData="taskInfoData" @son="sonData" />
      <taskThird v-if="taskStep == '4'" :taskInfoData="taskInfoData" @son="sonData" />
      <taskFourth v-if="taskStep == '5'" :taskInfoData="taskInfoData" @son="sonData" />
    </div>
    <div class="yuchang tuSecond"></div>
    <div class="yuchang tuThird"></div>
    <div class="yuchang tuFourth"></div>
    <div class="yuchang tuFifth"></div>
  </div>
</template>
<script>
import stepBar from '@/components/stepBar.vue'
import taskFirst from './cloudFirst.vue'
import taskSecond from '../unit_surveying_V1.0/taskThird.vue'
import taskThird from '../unit_surveying_V1.0/taskFourth.vue'
import taskFourth from '../unit_surveying_V1.0/taskFive.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { detectTaskInfo } from '@/api/apiConfig/surveying.js'

export default {
  components: { stepBar, taskFirst, taskSecond, taskThird, taskFourth },
  props: ['taskId'],
  data() {
    return {
      loading: false,
      taskInfoData: {},
      taskStep: '1',
      stepWidth: '26%',
      user: {
        role: ''
      },
      stepArr: [
        {
          id: 1,
          title: '目标线索选择'
        },
        {
          id: 3,
          title: '云端资产推荐'
        },
        {
          id: 4,
          title: '资产可信度评估'
        },
        {
          id: 5,
          title: '关联任务自定义'
        }
      ],
      userInfo: ''
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getTaskInfo()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role != 2) {
      // props接受任务记录查看详情的id
      this.getTaskInfo(this.taskId ? this.taskId : '')
    } else {
      this.getTaskInfo(this.taskId ? this.taskId : '')
    }
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    change(val) {
      this.taskStep = val
    },
    sonDataFirst(val, taskId) {
      if (taskId) {
        // 第一步点击确定后拿任务记录id去获取任务详情
        this.getTaskInfo(taskId)
      } else {
        this.taskStep = val
      }
    },
    sonData(val, taskId) {
      //父子传参改变tab
      if (taskId) {
        // 第一步点击确定后拿任务记录id去获取任务详情
        this.taskStep = val
        this.getTaskInfo(taskId)
      } else {
        this.taskStep = val
      }
    },
    async getTaskInfo(taskId) {
      // 获取任务详情数据，任务记录id存在，查询对应的详情数据；不存在后端直接查询未完成的任务数据，有返回值则展示，没有返回值新建流程
      let id = taskId ? taskId : ''
      let obj = {
        taskId: id,
        data: {
          operate_company_id: this.currentCompany,
          expand_source: 1 // 1云端推荐，0单位资产测绘
        }
      }
      this.loading = true
      let res = await detectTaskInfo(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.taskInfoData = JSON.parse(JSON.stringify(res.data)) // 任务详情
        sessionStorage.setItem('taskInfoData', res.data)
        if (res.data) {
          if (res.data.step) {
            // 跳转步骤
            this.taskStep = res.data.step
          }
          if (res.data.expend_flags) {
            this.recommentFlagChange(res.data.expend_flags) // 存储云端推荐flag
          } else {
            this.recommentFlagChange('')
          }
        } else {
          // 返回数据为空，直接跳转到第一步即可
          this.taskStep = '1'
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.Box {
  height: 100%;
}
.tabBox {
  display: flex;
  align-items: center;
  margin: 20px 20px 16px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.tabBox > div {
  margin-right: 20px;
  height: 40px;
  width: 25%;
  display: flex;
  align-items: center;
}
.tabBox > div > span {
  display: flex;
  align-items: center;
}
.tabBox > div:last-child {
  margin-right: 0px;
}
.tabBaryuan {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  color: #2677ff;
  margin: 0px 8px 0px 16px;
}
.tabBargray {
  color: #ffffff;
  background: #b2bdd4;
}
.tabBaryuan1 {
  margin: 0px 8px 0px 16px;
  font-size: 24px;
  color: #2677ff;
}
.myContent {
  height: calc(100% - 56px);
}
/deep/.el-progress {
  position: relative;
  .el-progress-bar {
    padding-right: 0px !important;
  }
  .el-progress__text {
    font-size: 14px !important;
    color: #2677ff;
    position: absolute;
    bottom: 23px;
    right: 0px;
  }
}
.myBox {
  height: 100%;
}
/deep/.box {
  background-image: url('../../assets/images/bg.png');
  // background: linear-gradient(90deg, #F0F6FF 0%, #FFFFFF 100%);
  background-repeat: no-repeat;
  // width: 100%;
  background-size: 100% 100%;
  padding: 0 0 0 16px;
}
/deep/.progressBarBox {
  display: flex;
  align-items: center;
  margin: 0px 20px;
  color: #37393c;
  padding: 16px 0;
  .tu {
    width: 100px;
    height: 100px;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .progressBox {
    flex-grow: 1;
    margin-left: 22px;
  }
  .progressBox > div {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }
}
/deep/.tableLabel {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #37393c;
  justify-content: space-between;
  // margin-bottom: 16px;
}
/deep/.tableLabel > div {
  display: flex;
  align-items: center;
  & > p {
    margin-right: 16px;
  }
  .el-input {
    width: 240px;
  }
  & > span {
    font-weight: 400;
    color: #2677ff;
    line-height: 20px;
    margin-left: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}
/deep/.progressBoxFinish {
  display: flex;
  align-items: center;
  color: #37393c;
  padding: 0px 20px 16px 20px;
}
/deep/.progressBoxFinish > span {
  display: flex;
  align-items: center;
  margin-right: 16px;
  i {
    color: #10d595;
    margin-right: 5px;
    font-size: 20px;
  }
}
/deep/.progressContent {
  color: #62666c;
  display: flex;
  align-items: center;
  img {
    width: 24px;
    height: 20px;
    margin-right: 12px;
  }
}
/deep/.el-pagination {
  background: transparent !important;
  box-shadow: none !important;
}
/deep/.myTable {
  padding: 0px 20px;
  height: calc(100% - 177px);
}
/deep/.boxTwo {
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  padding-top: 22px;
  height: calc(100% - 213px);
}
/deep/.boxTwo1 {
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  // padding-top: 22px;
  height: calc(100% - 96px);
}
/deep/.footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  background: #ffffff;
  box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
}
/deep/.tu {
  width: 120px !important;
  height: 120px !important;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position-y: 0px;
}
/deep/.tuSecond {
  background-image: url('../../assets/images/dongtu1.png');
}
/deep/.tuThird {
  width: 180px !important;
  height: 180px !important;
  background-image: url('../../assets/images/dongtu2.png');
}
/deep/.tuFourth {
  background-image: url('../../assets/images/dongtu3.png');
}
/deep/.tuFifth {
  width: 180px !important;
  height: 180px !important;
  background-image: url('../../assets/images/dongtu4.png');
}
.yuchang {
  visibility: hidden;
  position: fixed;
}
</style>
