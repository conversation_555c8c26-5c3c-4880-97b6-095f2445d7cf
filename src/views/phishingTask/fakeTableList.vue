<template>
  <div class="con">
    <el-table
      border
      :data="tableData"
      row-key="id"
      :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
      @selection-change="handleSelectionChange"
      ref="eltable"
      height="100%"
      style="width: 100%"
    >
      <template slot="empty">
        <div class="emptyClass">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-kong"></use>
          </svg>
          <p>暂无数据</p>
        </div>
      </template>
      <el-table-column
        type="selection"
        align="center"
        :reserve-selection="true"
        :show-overflow-tooltip="true"
        :selectable="handleSelectable"
        width="55"
      >
      </el-table-column>
      <el-table-column
        v-for="(item, itemIndex) in tableHeaderIsShow"
        :key="itemIndex"
        align="left"
        :prop="item.name"
        :label="item.label"
        :fixed="item.fixed"
        :min-width="item.minWidth"
      >
        <template slot-scope="scope">
          <span
            class="blueLine"
            v-if="
              item.name == 'chain_list' &&
              scope.row['chain_list'] &&
              scope.row['chain_list'].length &&
              scope.row['chain_list'].length > 0
            "
          >
            <el-tooltip
              placement="top"
              :disabled="!scope.row['chain_list']"
              class="item"
              effect="light"
              popper-class="chainClass"
              :open-delay="500"
            >
              <div slot="content" style="position: relative">
                <el-tooltip
                  effect="light"
                  class="item"
                  placement="top"
                  content="一键复制"
                  v-if="scope.row['chain_list'] && scope.row['chain_list'].length != 0"
                  :open-delay="500"
                >
                  <i
                    class="el-icon-document-copy"
                    @click="copyClick(scope.row['chain_list'])"
                    style="color: #2677ff; cursor: pointer; position: absolute; right: -6px; top: 0"
                  ></i>
                </el-tooltip>

                <span v-for="(item, index) in scope.row['chain_list']" :key="index">
                  <el-image
                    v-if="item.type && item.type == 3"
                    :src="item.content.includes('http') ? item.content : showSrcIp + item.content"
                    alt=""
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                  <span v-else>{{ item.content }}</span>
                  <span
                    v-if="index < scope.row['chain_list'].length - 1 && item"
                    class="el-icon-right iconRight"
                  ></span>
                </span>
              </div>
              <span>
                <img
                  src="../../assets/images/chain.svg"
                  alt=""
                  style="width: 12px; vertical-align: middle"
                />
              </span>
            </el-tooltip>
          </span>

          <!-- icon -->
          <span v-else-if="item.name == 'icon_content'">
            <el-image
              v-if="scope.row['chain_list'] && scope.row['chain_list'][0].type == 3"
              :src="
                scope.row['chain_list'][0].content.includes('http')
                ? scope.row['chain_list'][0].content
                : showSrcIp + scope.row['chain_list'][0].content
              "
              lazy
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <span v-else>-</span>
          </span>
          <el-tooltip
            v-else-if="item.name == 'url'"
            :open-delay="500"
            class="item"
            effect="dark"
            :content="scope.row[item.name] ? String(scope.row[item.name]) : '-'"
            placement="top"
          >
            <a
              v-if="scope.row[item.name] && String(scope.row[item.name]).includes('http')"
              style="color: #409eff"
              :href="scope.row[item.name]"
              target="_blank"
            >
              <span class="urlClass">{{ scope.row[item.name] }}</span>
              <span v-if="scope.row.online_state" class="blueRadiusBorder">可访问</span>
            </a>
            <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
          </el-tooltip>
          <span
            v-else-if="item.name == 'title'"
            class="clickPointer"
            @click="noteBlackList(scope.row.title)"
          >
            <el-tooltip
              v-if="scope.row[item.name]"
              effect="light"
              class="item"
              placement="top"
              :content="scope.row[item.name]"
              :open-delay="500"
            >
              <span>{{ scope.row[item.name] }}</span>
            </el-tooltip>
            <span v-else>-</span>
          </span>
          <span v-else>
            <el-tooltip
              v-if="scope.row[item.name]"
              effect="light"
              class="item"
              placement="top"
              :content="scope.row[item.name]"
              :open-delay="500"
            >
              <span>{{ scope.row[item.name] }}</span>
            </el-tooltip>
            <span v-else>-</span>
          </span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="taskInfoData.step == 4 || taskInfoData.step == 5"
        align="left"
        prop="screenshot"
        label="截图"
        min-width="160"
      >
        <template slot-scope="scope">
          <template v-if="scope.row.online_state">
            <el-image
              class="imgCardBox"
              @click="clickevaluatePicture(scope.row)"
              :preview-src-list="evaluatePictureList"
              :src="
                scope.row.screenshot && scope.row.screenshot.includes('http')
                  ? scope.row.screenshot
                  : showSrcIp + scope.row.screenshot
              "
              lazy
            >
              <div
                v-loading="currentIndex == scope.$index && picLoading"
                slot="error"
                class="image-slot"
              >
                <span class="imgCardContainer">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-kong"></use>
                  </svg>
                  <p class="imgKongBtn"
                    >暂无图片,<el-button
                      :disabled="picLoading"
                      @click="reGetPic(scope.row.id, scope.$index)"
                      type="text"
                      >请重试</el-button
                    ></p
                  >
                </span>
              </div>
            </el-image>
          </template>
          <template v-else> - </template>
        </template>
      </el-table-column>
    </el-table>
    <noteBlack
      :selectTitle="currentTitle"
      :visible="noteDialogVisible"
      ref=""
      @close="noteDialogVisible = false"
    />
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import noteBlack from '@/components/assets/noteBlack.vue'
import { regetScreenshot } from '@/api/apiConfig/api.js'

export default {
  components: { noteBlack },
  props: [
    'tableData',
    'pageIcon',
    'checkedAll',
    'checkedOnline',
    'taskInfoData',
    'loading',
    'assetType',
    'isAccessChecked'
  ],
  data() {
    return {
      picLoading: false,
      currentIndex: '', // 当前截图的数据index
      evaluatePictureList: [],
      currentTitle: '',
      noteDialogVisible: false,
      checkedArr: [],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      user: {
        role: ''
      },
      tableHeader: [
        {
          label: 'IP地址',
          name: 'ip',
          icon: 'input',
          minWidth: '120',
          detailIs: 1,
          fixed: 'left'
        },
        {
          label: '仿冒目标',
          name: 'name',
          icon: 'input',
          minWidth: '120',
          detailIs: 1
        },
        {
          label: '端口',
          name: 'port',
          icon: 'select',
          minWidth: '70',
          detailIs: 2
        },
        {
          label: '协议',
          name: 'protocol',
          icon: 'select',
          minWidth: '70',
          detailIs: 2
        },
        // {
        // 	label: '根域',
        // 	name: 'domain',
        // 	icon: 'select',
        // 	minWidth: '100',
        // 	detailIs: 2
        // },
        {
          label: '子域名',
          name: 'domain',
          icon: 'select',
          minWidth: '100',
          detailIs: 2
        },
        // {
        // 	label: '域名备案企业',
        // 	name: 'icp_company',
        // 	icon: 'select',
        // 	minWidth: '120',
        // 	detailIs: 2
        // },
        {
          label: 'URL',
          name: 'url',
          icon: 'select',
          minWidth: '180',
          detailIs: 2
        },
        {
          label: '网站标题',
          name: 'title',
          icon: 'select',
          minWidth: '120',
          detailIs: 2
        },
        {
          label: 'ICON',
          name: 'icon_content',
          minWidth: '80',
          detailIs: 2
        },
        {
          label: '数据来源',
          name: 'fake_asset_from',
          minWidth: '80',
          detailIs: 2
        },
        {
          label: '首次发现时间',
          name: 'created_at',
          minWidth: '120',
          detailIs: 1
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: '120',
          detailIs: 1
        },
        {
          label: '证据链',
          name: 'chain_list',
          minWidth: '80',
          detailIs: 1
        }
        // {
        // 	label: '截图',
        // 	name: 'screenshot',
        // 	minWidth: '200',
        // 	detailIs: 1
        // },
      ]
    }
  },
  watch: {
    tableData(val) {
      this.$nextTick(() => {
        if (this.checkedAll[this.assetType]) {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
          })
        } else if (!this.checkedAll[this.assetType] && this.isAccessChecked) {
          this.tableData.forEach((row) => {
            if (row.online_state) {
              this.$refs.eltable.toggleRowSelection(row, true)
            } else {
              this.$refs.eltable.toggleRowSelection(row, false)
            }
          })
        } else {
        }
        this.doLayout(this, 'eltable')
      })
    },
    checkedAll: {
      handler(val) {
        this.checkAllFunc()
      },
      deep: true
    },
    checkedOnline(val) {
      this.checkAllOnlineFunc()
    },
    isAccessChecked(val) {
      if (val) {
        this.tableData.forEach((row) => {
          if (row.online_state) {
            this.$refs.eltable.toggleRowSelection(row, true)
          } else {
            this.$refs.eltable.toggleRowSelection(row, false)
          }
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    }
  },
  created(){
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange']),
    tableHeaderIsShow(path) {
      let arr = this.tableHeader
      if (arr && arr.length > 0) {
        arr[0].fixed = 'left'
      }
      if(this.user.role !== 2 || this.userInfo.is_local){
        let arrs = arr.filter(item=>{
          return item.name != 'fake_asset_from'
        })
        return arrs
      }
      return arr
    }
  },
  methods: {
    // 重新获取截图
    reGetPic(id, index) {
      this.currentIndex = index
      this.picLoading = true
      let res = regetScreenshot({
        task_id: this.taskInfoData.id || (this.$route.query && this.$route.query.id),
        ids: [id],
        type: 3, // 1登录入口，2数据泄露，3仿冒资产
        operate_company_id: this.currentCompany
      })
        .then(() => {
          this.picLoading = false
          if (res.code == 0) {
            this.tableData[index].screenshot = res.data
          }
        })
        .catch(() => {
          this.picLoading = false
        })
    },
    DataSource(data) {
      const source = {
        0: 'fofa',
        1: 'Hunter',
        2: 'quake'
      }
      return source[data]


    },
    clickevaluatePicture(row) {
      var srclist = []
      srclist.push(this.showSrcIp + row.screenshot)
      this.evaluatePictureList = srclist // 赋值
    },
    noteBlackList(title) {
      this.noteDialogVisible = true
      this.currentTitle = title
    },
    showimg(url) {
      let imgUrl = ''
      url = url.replace('/', '')
      url = url.replace('.', '')
      url = url.replace(' ', '')
      let result = false
      url = './' + url + '.png'
      let urlTmp = url
      urlTmp = urlTmp.replace('./', '').replace('.png', '')
      // 目录下所有文件名
      const files = require.context('../../assets/images/componentIcon/', false, /\.png$/)
      const filedata = files.keys()
      filedata.forEach((item) => {
        if (item === url) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp + '.png')
        } else if (item === url.toLowerCase()) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp.toLowerCase() + '.png')
        }
      })
      // 返回结果
      return {
        result,
        url: imgUrl
      }
    },
    copyClick(item) {
      let text = ''
      // data.map(item=>{
      this.getChains(item).map((v, i) => {
        if (v.type && v.type == 3) {
          if (i == item.length - 1 && v) {
            text += 'icon' + '\n'
          } else {
            text += 'icon' + '-->'
          }
        } else {
          if (i < item.length - 1 && v) {
            text += v.content + '-->'
          } else if (i == item.length - 1 && v) {
            text += v.content + '\n'
          } else {
            text += v.content
          }
        }
      })
      // })
      this.$copyText(text).then(
        (res) => {
          console.log('复制成功：', res)
          this.$message.success('复制成功')
        },
        (err) => {
          console.log('', err)
          this.$message.error('复制失败')
        }
      )
    },
    getExpandData(data, index) {
      let arr = []
      if (!this.tableData[index].isExpand) {
        arr = data && data.length > 0 ? data.slice(0, 3) : []
      } else {
        arr = data
      }
      this.doLayout(this, 'eltable')
      return arr
    },
    // 展开
    getExpand(index) {
      this.tableData[index].isExpand = true
      this.tableData[index].myPopoverFlag = false
    },
    // 收缩
    getExpands(index) {
      this.tableData[index].isExpand = false
      this.tableData[index].myPopoverFlag = true
    },
    getChains(row) {
      let arr = row.filter((item) => {
        return item.content
      })
      return arr
    },
    // 全选方法
    checkAllFunc() {
      this.$nextTick(() => {
        if (this.checkedAll[this.assetType]) {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
          })
        } else {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, false)
          })
        }
      })
    },
    // 在线全选方法
    checkAllOnlineFunc() {
      if (this.checkedOnline) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectionChange(val) {
      this.checkedArr = val
      this.$emit('handleSelectionChange', val)
    },
    handleSelectable(row, index) {
      let isSelectable = true

      isSelectable = this.checkedAll[this.assetType]
        ? !this.checkedAll[this.assetType]
        : !this.isAccessChecked
      return isSelectable
    },
    getTableGeo(item, data) {
      if (item) {
        if (item[data]) {
          return item[data]
        } else {
          return '-'
        }
      } else {
        return '-'
      }
    },
    getTableItem(item) {
      if (Array.isArray(item)) {
        if (item.length == 0) {
          return '-'
        }
      }
      if (item) {
        if (Array.isArray(item)) {
          return item.join(',')
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    getReason(item) {
      // 推荐理由默认展示两个，鼠标移入显示更多
      if (item) {
        let str = item.split(/;|；|\s+/).splice(0, 2)
        if (str.length > 2) {
          return str.join(';') + '…'
        } else {
          return str.join(';')
        }
      } else {
        return '-'
      }
    },
    getCompany(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length == '0') {
            return ''
          } else {
            return item.join(',')
          }
        } else {
          return String(item)
        }
      } else {
        return ''
      }
    },
    getCloudName(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length == '0') {
            return ''
          } else {
            return item.join(',')
          }
        } else {
          // 字符串的数组需要单独处理
          if (Array.isArray(JSON.parse(JSON.stringify(item)))) {
            return JSON.parse(JSON.stringify(item)).join(',')
          } else {
            return String(item)
          }
        }
      } else {
        return ''
      }
    }
  }
}
</script>
<style lang="less" scoped>
.con {
  width: 100%;
  height: 100%;
}
/deep/.el-table th.el-table__cell::after {
  display: none;
}
.urlClass {
  display: inline-block;
  max-width: 60%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.productImg {
  max-height: 16px;
  max-width: 16px;
  margin-right: 4px;
  vertical-align: middle;
}
.imgCardBox {
  height: 30px;
  // width: 100%;
  // height: 100%;
  /deep/.image-slot {
    height: 100%;
    width: 100%;
    color: #606266;
    background: #e9ebef;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    text-align: center;
    svg {
      display: inline-block;
      font-size: 25px;
    }

    p {
      width: 100%;
      line-height: 25px;
      color: #d1d5dd;
      span {
        margin-left: 4px;
        color: #2677ff;
      }
      .el-button {
        padding: 0;
      }
    }
  }
}
/deep/.el-image {
  position: relative;
  overflow: visible;
  display: block;
}
.imgCardContainer {
  position: relative;
  // position: absolute;
  // top: 0;
  // left: 0;
}
.imgKongBtn {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto 0;
  left: calc(100% + 10px);
  // z-index: 9999;
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  text-align: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
