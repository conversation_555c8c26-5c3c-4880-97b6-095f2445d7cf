<template>
  <el-dialog
    class="elDialogAdd"
    @open="open"
    :close-on-click-modal="false"
    @close="$emit('close:dialogRelateVisible')"
    :visible="dialogRelateVisible"
    width="880px"
  >
    <template slot="title"> 关联情报数据 </template>
    <!-- <div class="dialog-body"> -->
    <div class="filterTab">
      <!-- <div> -->
      <el-input
        v-model="formInline.url"
        placeholder="请输入仿冒url检索"
        @keyup.enter.native="checkFuncList"
      >
        <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
      </el-input>
      <el-input
        v-model="formInline.target"
        placeholder="请输入仿冒目标检索"
        @keyup.enter.native="checkFuncList"
      >
        <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
      </el-input>
      <el-select
        v-model="formInline.status"
        placeholder="请选择状态"
        @change="checkFuncList"
        clearable
        collapse-tags
        id="user_select"
      >
        <el-option
          :label="v.name"
          :value="v.value"
          v-for="v in statusArr"
          :key="v.value"
        ></el-option>
      </el-select>
      <!-- </div> -->
    </div>
    <div class="tableComponent">
      <tableList
        ref="tableList"
        @selectChange="selectChangeData"
        :tableHeader="tableHeader"
        :isSelectData="true"
        :tableData="tableData"
        @updateList="updateList"
        :total="total"
      >
        <template v-slot:judge="{ row, itemName }">
          <span v-if="itemName == 'status'">{{
            statusArrMap[row[itemName]] ? statusArrMap[row[itemName]] : '-'
          }}</span>
          <span v-else-if="itemName == 'url'">
            <a
              v-if="row[itemName] && String(row[itemName]).includes('http')"
              style="color: #409eff"
              :href="row[itemName]"
              target="_blank"
              >{{ row[itemName] }}</a
            >
            <span v-else>{{ row[itemName] || '-' }}</span>
          </span>
          <span v-else>{{ row[itemName] || '-' }}</span>
        </template>
      </tableList>
    </div>
    <!-- </div> -->
    <div slot="footer" class="dialog-footer">
      <el-button class="highBtnRe" @click="$emit('close:dialogRelateVisible')">关闭</el-button>
      <!-- <el-button class="highBtnRe" @click="resetData">重置</el-button> -->
      <el-button class="highBtn" type="primary" @click="confirmRelateData">
        确认({{ selectData.length }})
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import tableList from '../intelligenceCenterv1/table.vue'
import { fakeList } from '@/api/apiConfig/api.js'

export default {
  components: {
    tableList
  },
  props: {
    // userInfo: {
    //   type: Object,
    //   default: () => ({})
    // },
    sample_url: {
      type: String,
      default: ''
    },
    currentCompany: {
      type: [String, Number],
      default: ''
    },
    // pageType: {
    //   type: String,
    //   default: ''
    // },
    dialogRelateVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let statusArr = [
      { name: '在线', value: 1 },
      { name: '离线', value: 2 }
    ]
    return {
      selectData: [],
      statusArr,
      statusArrMap: {
        1: '在线',
        2: '离线'
      },
      formInline: {},
      addDialogFormVisible: false,
      addForm: {},

      searchParams: {
        page: 1,
        per_page: 10
      },
      total: 0,
      tableData: [],
      // 钓鱼仿冒
      tableHeader: [
        {
          label: '发现时间',
          name: 'found_at',
          fixed: 'left',
          minWidth: 100
        },
        {
          label: '钓鱼URL',
          name: 'url',
          minWidth: 100
        },
        {
          label: 'IP',
          name: 'ip',
          minWidth: 100
        },
        {
          label: 'IP所在地（国家）',
          name: 'country',
          minWidth: 100
        },
        {
          label: '仿冒目标',
          name: 'target',
          minWidth: 100
        },
        {
          label: '云服务商',
          name: 'cloud_name',
          minWidth: 100
        },
        {
          label: '状态',
          name: 'status',
          minWidth: 100
        }
      ],
      currentRow: {},
      currentRowCopy: {}
    }
  },

  // mounted() {
  //   if (this.user.role == 2 && !this.currentCompany) {
  //     return;
  //   }
  //   this.getData();
  // },
  // watch: {
  //   getterCurrentCompany(val) {
  //     if (this.user.role == 2) {
  //       this.getData();
  //     }
  //   }
  // },
  computed: {
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    open() {
      this.$nextTick(() => {
        this.resetData()
        this.getData()
      })
    },
    resetData() {
      this.$refs.tableList.$refs.eltable.clearSelection()
    },
    confirmRelateData() {
      if (this.selectData.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      if (this.selectData.length > 10) {
        this.$message.error('最多勾选10条数据')
      }
      this.$emit('close:dialogRelateVisible')
      this.$emit('confirmRelateData', this.selectData)
    },
    selectChangeData(val) {
      this.selectData = val.map((item) => item.url)
    },
    checkFuncList() {
      this.updateList({ page: 1, per_page: 10, ...this.formInline })
    },
    updateList(data) {
      let searchParam = JSON.parse(JSON.stringify(this.searchParams))
      this.searchParams = Object.assign(searchParam, data)
      this.getData()
    },
    async getData() {
      let res = await fakeList({
        ...this.searchParams,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.$emit('updatePageLoading', false)
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
      } else {
        this.$message.error(res.message)
      }
      this.$emit('updatePageLoading', false)
    }
  }
}
</script>

<style lang="less" scoped>
.tableList {
  height: 100%;
}
.elDialogAdd /deep/.el-dialog .el-dialog__body {
  height: 450px !important;
  padding-top: 0;
}

.tableComponent {
  height: calc(100% - 66px);
}
.filterTab > div {
  margin-right: 10px;
  &:last-child {
    margin-right: 0;
  }
}
</style>
