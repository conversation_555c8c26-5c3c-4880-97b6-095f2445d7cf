<template>
  <div class="keyWrap">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>主体词</span>
        <el-button class="ignoreClass" :loading="removeLoading" @click="removeMore" type="text"
          >忽略</el-button
        >
        <el-button class="addClass" :loading="btnLoading" @click="fucAdd('main')" type="text"
          >新增</el-button
        >
      </div>
      <el-table
        :data="legendData.main_keyword"
        style="width: 100%"
        height="100%"
        @selection-change="handleSelectionChangeMain"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="content" label="关键词" min-width="150"> </el-table-column>
        <el-table-column prop="source" label="来源" min-width="120">
          <template slot-scope="scope">
            <!-- <span>手动添加</span> -->
            <span v-if="scope.row.source == 1">循环线索</span>
            <span v-else-if="scope.row.source == 2">扩展线索</span>
            <span v-else>初始线索</span>
          </template>
        </el-table-column>
        <el-table-column prop="chain_list" label="线索源">
          <template slot-scope="scope">
            <el-tooltip
              v-if="getSourcetype(scope.row['chain_list'])"
              class="item"
              effect="light"
              placement="top"
              :open-delay="500"
            >
              <div slot="content"
                >{{
                  $punyCode.toUnicode(
                    getSource(scope.row['chain_list'], scope.row.parent_id, scope.row.source)
                  )
                }}{{ scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : '' }}</div
              >
              <span>{{
                $punyCode.toUnicode(
                  getSource(scope.row['chain_list'], scope.row.parent_id, scope.row.source)
                )
              }}</span>
            </el-tooltip>
            <span v-else
              ><img :src="getImg(scope.row['chain_list'])" alt="" style="width: 23px; height: 23px"
            /></span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>辅助词</span>
        <el-button class="ignoreClass" :loading="removeLoading" @click="fucRemove" type="text"
          >忽略</el-button
        >
        <el-button class="addClass" :loading="btnLoading" @click="fucAdd('node')" type="text"
          >新增</el-button
        >
        <el-button class="ignoreClass" @click="openMyWordDialog" type="text">我的词库</el-button>
      </div>
      <el-table
        :data="legendData.node_keyword"
        style="width: 100%"
        height="100%"
        @selection-change="handleSelectionChangeNode"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="content" label="关键词" min-width="150"> </el-table-column>
        <el-table-column prop="source" label="来源" min-width="120"> </el-table-column>
      </el-table>
    </el-card>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="45%"
    >
      <template slot="title"> 新建 </template>
      <div class="dialog-body">
        <el-form
          :model="contentForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="ruleForms"
          label-width="95px"
          class="demo-ruleForm"
        >
          <el-form-item
            class="clueItem"
            :label="addIcon == 'main' ? '主体词' : '辅助词'"
            prop="way"
          >
            <el-input
              type="textarea"
              :rows="2"
              v-model="contentForm.keyowrd"
              :placeholder="`请输入${addIcon == 'main' ? '主体词' : '辅助词'}，多个值请用分号或换行分隔，每个主体词不能超过200字符`"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-if="addIcon == 'node'" v-model="syncChecked">保存至我的词库</el-checkbox>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleInsert = false" id="cloud_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="insertSave" id="cloud_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <my-word
      :taskInfoData="taskInfoData"
      :currentCompany="currentCompany"
      :myWordList="myWordList"
      @syncList="syncList"
      @getMyWords="getMyWords"
      :myWordDialogVisible.sync="myWordDialogVisible"
      @close="
        () => {
          myWordDialogVisible = false
        }
      "
    />
  </div>
</template>
<script>
import myWord from './myWord.vue'
import { mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { passClueV1, insertCluesV1 } from '@/api/apiConfig/clue.js'
import { addMyWordList, getMyWordList, addfakeModelList } from '@/api/apiConfig/phishing.js'

export default {
  components: { tableTooltip, myWord },
  props: ['legendData', 'taskInfoData'],
  data() {
    return {
      syncChecked: false,
      myWordList: [],
      myWordDialogVisible: false,
      tableData: [],
      dialogFormVisibleInsert: false,
      btnLoading: false,
      removeLoading: false,
      contentForm: {
        keyowrd: ''
      },
      rules: {
        keyowrd: [{ required: true, message: '请输入关键词', trigger: 'blur' }]
      },
      addIcon: '',
      checkedMain: [],
      checkedNode: [],
      user: {
        role: ''
      }
    }
  },
  watch: {
    //
    async legendData(val) {
      this.legendData.main_keyword = this.legendData.main_keyword
        ? this.legendData.main_keyword
        : []
      this.legendData.node_keyword = this.legendData.node_keyword
        ? this.legendData.node_keyword
        : []
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  methods: {
    async getMyWords() {
      let { code, data } = await getMyWordList({ operate_company_id: this.currentCompany })
      if (code == 0) {
        this.myWordList = data
      }
    },
    openMyWordDialog() {
      this.getMyWords()
      this.myWordDialogVisible = true
    },
    handleSelectionChangeMain(val) {
      this.checkedMain = val
    },
    handleSelectionChangeNode(val) {
      this.checkedNode = val
    },
    removeMore() {
      if (this.checkedMain.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let clueData = [
        {
          id: this.checkedMain.map((item) => {
            return item.id
          }),
          type: '4',
          is_all: '',
          keyword: ''
        }
      ]
      this.$confirm('确定忽略数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      })
        .then(async () => {
          this.removeLoading = true
          let obj = {
            status: 2,
            tab_status: 1,
            group_id: this.taskInfoData.group_id,
            data: clueData,
            operate_company_id: this.currentCompany
          }
          let res = await passClueV1(obj).catch(() => {
            this.removeLoading = false
          })
          if (res.code == 0) {
            this.removeLoading = false
            this.$message.success('操作成功！')
            this.$emit('getfakeModelList')
          }
        })
        .catch(() => {})
    },
    fucAdd(icon) {
      this.addIcon = icon
      this.contentForm.keyowrd = ''
      this.syncChecked = false
      this.dialogFormVisibleInsert = true
    },
    async fucRemove() {
      if (this.checkedNode.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let removeAfter = []
      this.legendData.node_keyword.forEach((item) => {
        if (!this.checkedNode.includes(item)) {
          // 全量更新
          removeAfter.push(item)
        }
      })
      this.$confirm('确定忽略数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            fake_detect_task_id: this.taskInfoData.id,
            node_keyword: removeAfter,
            operate_company_id: this.currentCompany
          }
          this.removeLoading = true
          let res = await addfakeModelList(obj).catch(() => {
            this.removeLoading = false
          })
          if (res.code == 0) {
            this.removeLoading = false
            this.dialogFormVisibleInsert = false
            this.$message.success('操作成功！')
            this.$emit('getfakeModelList')
          }
        })
        .catch(() => {})
    },
    async syncList(node_keyword) {
      let obj = {
        fake_detect_task_id: this.taskInfoData.id,
        node_keyword: this.legendData.node_keyword.concat(node_keyword),
        operate_company_id: this.currentCompany
      }
      let res = await addfakeModelList(obj).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.myWordDialogVisible = false
        this.$message.success('操作成功！')
        this.$emit('getfakeModelList')
      }
    },
    async insertSave() {
      this.$refs.ruleForms.validate(async (valid) => {
        if (valid) {
          let actionFun = null
          let obj = null
          let node_keyword = []
          let keywordArr = this.contentForm.keyowrd
            .split(/[；|;|\r\n]/)
            .filter((item) => {
              return item.trim()
            })
            .map((item) => {
              return item.trim()
            })
          let isOverLength = false
          keywordArr.some((item, index) => {
            if (item.length > 200) {
              this.$message.error(`第${index + 1}个关键词超过200字符限制`)
              isOverLength = true
              return true
            }
          })
          if (isOverLength) {
            // 关键词字符限制
            return
          }
          if (this.addIcon == 'main') {
            // 主体词添加
            this.btnLoading = true
            obj = {
              group_id: this.taskInfoData.group_id,
              data: [
                {
                  type: 4,
                  content: keywordArr
                }
              ],
              is_auto_expend: 0,
              file: '',
              set_status: 1, // 添加自动为已确认线索
              operate_company_id: this.currentCompany
            }
            actionFun = insertCluesV1
          } else {
            // 辅助词添加
            keywordArr.forEach((item) => {
              node_keyword.push({
                content: item,
                source: '手动添加'
              })
            })
            this.btnLoading = true
            obj = {
              fake_detect_task_id: this.taskInfoData.id,
              node_keyword: this.legendData.node_keyword.concat(node_keyword),
              operate_company_id: this.currentCompany
            }
            actionFun = addfakeModelList
          }
          let res = await actionFun(obj).catch(() => {
            this.btnLoading = false
          })
          let res1 = null
          if (this.syncChecked) {
            obj.node_keyword = this.myWordList
              .map((item) => ({ content: item.keyword, source: item.source }))
              .concat(node_keyword)
            res1 = await addMyWordList(obj).catch(() => {
              this.btnLoading = false
            })
            if (res1 && res1.code == 0 && res.code == 0) {
              this.btnLoading = false
              this.dialogFormVisibleInsert = false
              this.$message.success('操作成功！')
              this.$emit('getfakeModelList')
            }
          } else {
            if (res.code == 0) {
              this.btnLoading = false
              this.dialogFormVisibleInsert = false
              this.$message.success('操作成功！')
              this.$emit('getfakeModelList')
            }
          }
        }
      })
    },
    getSourcetype(val) {
      //线索来源类型
      if (val.length != 0) {
        if (val.length >= 2) {
          if (val[val.length - 2].type != 3) {
            return true
          } else {
            return false
          }
        } else {
          if (val[0].type != 3) {
            return true
          } else {
            return false
          }
        }
      } else {
        return true
      }
    },
    getImg(val) {
      //线索图片
      if (val.length != 0) {
        if (val.length >= 2) {
          return val[val.length - 2].content.includes('http')
            ? val[val.length - 2].content
            : this.showSrcIp + val[val.length - 2].content
        } else {
          return val[0].content.includes('http') ? val[0].content : this.showSrcIp + val[0].content
        }
      }
    },
    getSource(val, parent_id, source) {
      //线索来源
      if (val.length != 0) {
        if (val.length >= 2) {
          return val[val.length - 2].content
        } else {
          return val[0].content
        }
      } else {
        // 初始线索：0、3、4、5，扩展线索：2
        if (parent_id == 0 && source == 0) {
          return '初始线索'
        } else if (parent_id == 0 && source == 2) {
          return '扩展线索'
        } else if (parent_id == 0 && source == 4) {
          return '初始线索'
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.keyWrap {
  width: 100%;
  height: 96%;
  margin: 1% 0;
  padding: 0 16px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  /deep/.box-card {
    width: 49.5%;
    .el-card__body {
      height: 83% !important;
    }
    .ignoreClass {
      float: right;
      padding: 3px 0;
    }
    .addClass {
      float: right;
      padding: 3px 0;
      margin-right: 20px;
    }
  }
}
.el-table th.el-table__cell::after {
  background-color: transparent;
}
</style>
