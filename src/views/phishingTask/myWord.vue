<template>
  <el-dialog
    class="elDialogAdd"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    hight="80%"
    width="45%"
    @close="closeDialog"
  >
    <template slot="title"> 我的词库 </template>
    <div class="dialog-body">
      <div class="btnGroup">
        <el-button class="ignoreClass" type="text" @click="deleteListItem">忽略</el-button>
        <el-button class="addClass" type="text" @click="isShowKeyWordAdd = true">新增</el-button>
      </div>
      <div class="keywordAdd" v-if="isShowKeyWordAdd">
        <el-input
          type="textarea"
          :rows="2"
          v-model="keyowrd"
          :placeholder="`请输入辅助词多个值请用分号或换行分隔，每个主体词不能超过200字符`"
        ></el-input>
        <div class="btnGroup">
          <el-button type="primary" class="highBtn" @click="myWordAdd">确认</el-button>
          <el-button type="primary" class="highBtnRe" @click="closeAddDiv">取消</el-button>
        </div>
      </div>
      <el-table
        :data="myWordList"
        style="width: 100%"
        :height="isShowKeyWordAdd ? 280 : 390"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="keyword" label="关键词" min-width="150"> </el-table-column>
        <el-table-column prop="created_at" label="创建时间" min-width="150"> </el-table-column>
        <el-table-column prop="source" label="来源" min-width="120"> </el-table-column>
      </el-table>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="syncBtn" @click="syncList">同步至辅助词列表</el-button>
      <el-button type="primary" class="highBtnRe" @click="closeDialog" id="cloud_cancel"
        >关闭</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { addMyWordList, delMyWordList } from '@/api/apiConfig/phishing.js'

export default {
  methods: {
    async deleteListItem() {
      if (this.checkoutArr.length == 0) return this.$message.error('请选择需要同步的数据')
      let obj = {
        id: this.checkoutArr.map((item) => item.id),
        operate_company_id: this.currentCompany
      }
      this.$confirm('确定忽略所勾选的数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await delMyWordList(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.$emit('getMyWords')
          }
        })
        .catch(() => {})
    },
    syncList() {
      if (this.checkoutArr.length == 0) return this.$message.error('请选择需要同步的数据')
      let list = this.checkoutArr.map((item) => ({ content: item.keyword, source: item.source }))
      this.$emit('syncList', list)
    },
    async myWordAdd() {
      let obj = null
      let node_keyword = []
      let keywordArr = this.keyowrd
        .split(/[；|;|\r\n]/)
        .filter((item) => {
          return item.trim()
        })
        .map((item) => {
          return item.trim()
        })
      let isOverLength = false
      keywordArr.some((item, index) => {
        if (item.length > 200) {
          this.$message.error(`第${index + 1}个关键词超过200字符限制`)
          isOverLength = true
          return true
        }
      })
      if (isOverLength) {
        // 关键词字符限制
        return
      }
      keywordArr.forEach((item) => {
        node_keyword.push({
          content: item,
          source: '我的词库'
        })
      })
      obj = {
        // fake_detect_task_id: this.taskInfoData.id,
        node_keyword: this.myWordList.concat(node_keyword),
        operate_company_id: this.currentCompany
      }
      this.btnLoading = true
      let { code } = await addMyWordList(obj)
      if (code == 0) {
        this.closeAddDiv()
        this.$emit('getMyWords')
        this.$message.success('添加成功')
      }
    },
    closeAddDiv() {
      this.keyowrd = ''
      this.isShowKeyWordAdd = false
    },
    handleSelectionChange(val) {
      this.checkoutArr = val
    },
    closeDialog() {
      if (this.keyowrd && this.isShowKeyWordAdd) {
        this.$confirm('关闭后所填写的数据将不会保存(可点击确认按钮保存辅助词), 是否继续?', '提示', {
          confirmButtonText: '确定关闭',
          cancelButtonText: '取消关闭',
          type: 'warning'
        })
          .then(() => {
            this.close()
            this.closeAddDiv()
          })
          .catch(() => {})
      } else {
        this.close()
      }
    },
    close() {
      this.$emit('close')
      this.isShowKeyWordAdd = false
    }
  },
  watch: {
    isShowKeyWordAdd(val) {
      // let className = val ? 'tableHight':'tableHight1'
      // document.getElementByClass('el-table__body-wrapper')[].setAttribute("class",className)
    }
  },
  props: {
    myWordDialogVisible: {
      type: Boolean,
      default: false
    },
    myWordList: {
      type: Array,
      default: () => []
    },
    taskInfoData: {
      type: Object,
      default: () => ({})
    },
    currentCompany: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      checkoutArr: [],
      isShowKeyWordAdd: false,
      keyowrd: '',
      btnLoading: false,
      legendData: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.myWordDialogVisible
      },
      set() {}
    }
  }
}
</script>

<style lang="less" scoped>
.keywordAdd {
  margin-bottom: 20px;
  .btnGroup {
    margin-top: 10px;
  }
}
// .dialog-body{
//   height: 800px !important;
// }
// .dialog-body{
//   box-sizing: border-box;
//   height: 300px;
// }
// /deep/.el-table__body-wrapper{
//   height: 70%;
// }
/deep/.el-table {
  margin-bottom: 20px;
}
// .tableHight{
//   height: 280px;
// }
// .tableHight1{
//   height: 380px;
// }
.syncBtn {
  height: 32px;
  width: 138px;
  // height: 32px;
  background: #2677ff;
  border-radius: 4px;
  border: 1px solid #2677ff;
  font-weight: 400;
  color: #fff;
  text-align: center;
  padding: 0;
}
.btnGroup {
  width: 100%;
  text-align: right;
}
</style>
