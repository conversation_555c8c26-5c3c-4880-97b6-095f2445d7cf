<template>
  <div class="myBox1" v-loading="changeCompanyLoading">
    <div class="content">
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="rules"
        label-width="125px"
        style="width: 100%"
      >
        <div class="module findModule">
          <div class="top">
            <div class="label">
              <div class="blueBlock"></div>
              线索发现设置
            </div>
            <div class="act" v-if="isShowFindModule" @click="unfoldOrCollapse('Find', false)"
              ><span>收起</span> <img :src="topArrow" alt=""
            /></div>
            <div class="act" v-else @click="unfoldOrCollapse('Find', true)"
              ><span>展开</span> <img :src="bottomArrow" alt="" />
            </div>
          </div>
          <div class="moduleBottom" v-if="isShowFindModule">
            <el-form-item label="" prop="companyName" class="companyDiv">
              <!-- is_real_customer == 1 代表在用户管理配置成客户，只能对本企业以及配置好的企业测绘 -->
              <span v-if="is_real_customer == 1" slot="label">
                企业名称：
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="只支持对本企业以及控股企业进行仿冒发现"
                  placement="top"
                >
                  <i
                    style="color: rgba(38, 119, 255, 0.2); font-size: 16px"
                    class="el-icon-question"
                  ></i>
                </el-tooltip>
              </span>
              <span v-else slot="label"> 企业名称： </span>
              <div class="select-button">
                <el-select
                  v-if="is_real_customer == 1"
                  v-model="addForm.companyName"
                  filterable
                  remote
                  clearable
                  @change="companyChange"
                  placeholder="请输入本企业名称或控股企业名称"
                  :remote-method="querySearch"
                  :loading="loading"
                >
                  <el-option
                    v-for="(item, index) in kehuCompanyResults"
                    :key="index"
                    :value="item.value"
                  >
                    {{ item.value }}
                  </el-option>
                </el-select>
                <el-select
                  v-else
                  v-model="addForm.companyName"
                  filterable
                  remote
                  clearable
                  @change="companyChange"
                  placeholder="请输入企业名称进行搜索"
                  :remote-method="remoteMethod"
                  :loading="loading"
                >
                  <el-option v-for="(item, index) in companyList" :key="index" :value="item.value">
                    {{ item.value }}
                  </el-option>
                </el-select>
                <el-button
                  :class="addForm.companyName ? 'companyBtn companyBlue' : 'companyBtn companyGray'"
                  @click.native="($event) => Inquire('')"
                  id="unit_sure"
                  >企业关系查询</el-button
                >
              </div>
            </el-form-item>
            <el-form-item class="treeWrap" v-if="treeIsTrue" label="">
              <p class="onekeyclass">
                <el-checkbox @change="onekeyCheck" v-model="isTreeChecked">全选</el-checkbox>
                <!-- 安服支持重新获取 -->
                <el-button v-if="user.role == 2" type="text" @click="Inquire(1)"
                  >重新获取</el-button
                >
              </p>
              <el-tree
                class="companyTree"
                ref="companyTree"
                :data="companyCascadeEquityList"
                show-checkbox
                node-key="company_name"
                :check-strictly="true"
                :default-expand-all="true"
                :default-checked-keys="checkedTree"
                :props="defaultProps"
              >
                <el-checkbox @change="onekeyCheck" v-model="isTreeChecked">全选</el-checkbox>
                <span class="custom-tree-node" slot-scope="{ data }">
                  <el-tooltip class="nodeLabel" effect="light" placement="top" :open-delay="500">
                    <div slot="content">{{ data.company_name }}</div>
                    <span>{{ data.company_name }}</span>
                  </el-tooltip>
                  <businessTagVue
                    v-if="data.reg_status"
                    :reg_status="data.reg_status"
                  ></businessTagVue>
                  <span
                    v-if="data.level != 0"
                    :class="parseFloat(data.rate) > 50 ? 'treeNum bigNum' : 'treeNum smallNum'"
                    >控股：<span class="num">{{ transferRate(data.rate) }}</span></span
                  >
                </span>
              </el-tree>
            </el-form-item>
            <el-form-item label="官网URL：" label-suffix="opop">
              <el-input
                type="textarea"
                :rows="5"
                v-model="addForm.official_url"
                placeholder="请输入官网URL,多个值请使用分号或换行分隔,示例：https://foradar.baimaohui.net"
              ></el-input>
            </el-form-item>
            <el-form-item class="drawTypeClass" label="" v-if="user.role == 2 && !userInfo.is_local">
              <template slot="label"
                >数据源：
              </template>
              <el-checkbox-group v-model="addForm.checkList">
                <el-checkbox disabled label="0" value="0">fofa</el-checkbox>
                <el-checkbox disabled label="1" value="1">Hunter</el-checkbox>
                <el-checkbox label="2" value="2">quake</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item class="drawTypeClass" label="">
              <template slot="label"
                >云厂商识别：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    启用后将识别目标企业的云厂商资产，并纳入资产管理。
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-radio-group v-model="addForm.has_cloud_assets">
                <el-radio :label="0" value="0">否</el-radio>
                <el-radio :label="1" value="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item class="drawTypeClass" label="线索获取：">
              <el-radio-group v-model="addForm.is_detect_scene" @change="drawTypeChange">
                <el-radio :label="0" value="0">新建线索</el-radio>
                <el-radio :label="1" value="1">使用已有线索</el-radio>
              </el-radio-group>
              <el-select
                v-loading="groupLoading"
                v-if="addForm.is_detect_scene == 1"
                v-model="addForm.scene_group_id"
                placeholder="请选择场景"
              >
                <el-option
                  v-for="item in groupArr"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  @click.native="groupOptionClick(item)"
                >
                </el-option>
              </el-select>
              <el-checkbox v-if="addForm.is_detect_scene == 1" v-model="addForm.expand_init_clues"
                >是否作为初始线索进行扩展</el-checkbox
              >
            </el-form-item>
            <el-form-item label="已知域名：">
              <el-input
                type="textarea"
                :rows="2"
                v-model="content.content0"
                placeholder="请输入域名，多个值请用分号或换行分隔，例如：fofa.info，每条线索不能超过200字符"
              ></el-input>
            </el-form-item>
            <el-form-item label="关键词：">
              <el-input
                type="textarea"
                :rows="2"
                v-model="content.content4"
                placeholder="请输入关键词，多个值请用分号或换行分隔，例如：北京华顺信安科技有限公司，每条线索不能超过200字符"
              ></el-input>
            </el-form-item>
            <el-form-item label="ICON：">
              <el-upload
                class="upload-demo"
                drag
                :before-upload="beforeIpUpload"
                :action="uploadSrcIp + '/assets/account/files'"
                :headers="uploadHeaders"
                :on-success="iconUploadSucc"
                :on-remove="iconUploadRemove"
                :on-error="uploadErr"
                list-type="picture"
                accept=".png,.ico,.bmp,.jpg,.jpeg"
                :file-list="fileListIcon"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将ICON拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip"
                  >支持上传.png,.ico,.bmp,.jpg,.jpeg文件，且大小不超过3M</div
                >
              </el-upload>
            </el-form-item>
          </div>
        </div>
        <div class="module modelModule">
          <div class="top">
            <div class="label">
              <div class="blueBlock"></div>
              模式设置
            </div>
            <div class="act" v-if="isShowModelModule" @click="unfoldOrCollapse('Model', false)"
              ><span>收起</span> <img :src="topArrow" alt=""
            /></div>
            <div class="act" v-else @click="unfoldOrCollapse('Model', true)"
              ><span>展开</span> <img :src="bottomArrow" alt=""
            /></div>
          </div>
          <div class="moduleBottom" v-if="isShowModelModule">
            <el-form-item label="模式设置：">
              <template slot="label">
                模式设置：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    选择钓鱼仿冒测绘流程模式，具体的模式区别如下:
                    <ul>
                      <li> 标准模式：根据提供的官方网站的特征以及线索进行钓鱼仿冒网站的发现； </li>
                      <li>
                        深度模式：根据提供的官方网站、已经确认的钓鱼仿冒样本目标以及线索特征进行钓鱼仿冒网站的发现，其中仿冒样本数据为必填。
                      </li>
                    </ul>
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-radio-group v-model="addForm.detect_type">
                <el-radio :label="1">标准模式</el-radio>
                <el-radio :label="2">深度模式</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              class="sampleUrlRequire"
              v-if="addForm.detect_type == 2"
              label="样本目标："
              label-suffix="opop"
              prop="sample_url"
            >
              <span class="relateBtn" @click="relateIntelligence">情报数据联动</span>
              <el-input
                type="textarea"
                :rows="5"
                v-model="addForm.sample_url"
                placeholder="请输入已经发现的钓鱼网站URL,作为反样本目标,多个值请使用分号或换行分隔,最多可以添加10个URL,示例：https://foradar.baimaohui.net"
              ></el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <div class="footer">
      <el-button class="normalBtn" type="primary" :loading="sureLoading" @click="sureGetClue"
        >开始任务</el-button
      >
    </div>
    <intelligence
      :sample_url="addForm.sample_url"
      @confirmRelateData="confirmRelateData"
      @close:dialogRelateVisible="() => (dialogRelateVisible = false)"
      :dialogRelateVisible.sync="dialogRelateVisible"
      :currentCompany="currentCompany"
    ></intelligence>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import intelligence from './intelligence.vue'
import { fakeSureCompany } from '@/api/apiConfig/phishing.js'
import { cluesGroupListNoPage } from '@/api/apiConfig/clue.js'
import { companyCascadeEquity } from '@/api/apiConfig/recommend.js'
import { getAssociate, kehuCompanyList } from '@/api/apiConfig/surveying.js'
import businessTagVue from '../../components/businessTag.vue'

export default {
  components: {
    intelligence,
    businessTagVue
  },
  data() {
    return {
      dialogRelateVisible: false,
      topArrow: require('@/assets/images/topArrow.png'),
      bottomArrow: require('@/assets/images/bottomArrow.png'),
      isShowFindModule: true,
      isShowModelModule: true,
      isShowParamsModule: true,
      isShowAssetsModule: true,
      isShowTaskModule: true,
      changeCompanyLoading: false,
      groupLoading: false,
      treeIsTrue: false,
      sureLoading: false,
      loading: false,
      isTreeChecked: false,
      companyList: [],
      companyCascadeEquityList: [], // 控股企业列表
      otherCompany: [],
      other_customer_company: [],
      checkedTree: [],
      groupArr: [],
      restaurants: [],
      addForm: {
        detect_type: 1,
        companyName: '',
        has_cloud_assets: 0,
        is_detect_scene: 0,
        expand_init_clues: false, // 是否拿场景分组中的线索作为初始线索进行扩展 0不扩展/1进行扩展
        scene_group_id: '', // 场景库线索分组选择
        sample_url: '',
        official_url: '',
        checkList:["0"]
      },
      groupId: '',
      checked: '',
      rules: {
        companyName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
        sample_url: [{ required: true, message: '请输入样本目标', trigger: ['blur', 'change'] }]
      },
      content: {
        content0: '',
        content1: '',
        content2: '',
        content3: [],
        content4: '',
        content5: '',
        content6: ''
      },

      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileListIcon: [],
      defaultProps: {
        children: 'children',
        label: 'company_name'
      },
      user: {
        role: ''
      },
      kehuCompanyResults: [],
      is_real_customer: 0,
      userInfo: ''
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.getGroupList()
      if (this.user.role == 2) {
        // 安服
        this.changeCompanyLoading = true
        this.addForm = {
          companyName: '',
          is_detect_scene: 0,
          has_cloud_assets: 0,
          expand_init_clues: false, // 是否拿场景分组中的线索作为初始线索进行扩展 0不扩展/1进行扩展
          scene_group_id: '', // 场景库线索分组选择
          detect_type: 1,
          checkList:["0"]
        }
        setTimeout(() => {
          this.is_real_customer = sessionStorage.getItem('companyInfo')
            ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.is_real_customer
            : ''
          if (this.is_real_customer == 1) {
            // 客户账号，需要控制只能操作自己及控股企业
            this.getSearchData() // 企业账号获取企业及控股企业列表
          } else {
            this.restaurants = []
          }
          this.changeCompanyLoading = false
        }, 1000)
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage', 'getterCurrentCompany'])
  },

  created() {

    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    if (this.user.role == 2) {
      // 安服
      if (!this.currentCompany) return
      this.is_real_customer = sessionStorage.getItem('companyInfo')
        ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.is_real_customer
        : ''
    } else {
      // 企业、超管
      this.is_real_customer = this.user ? this.user.is_real_customer : ''
    }
    if (this.is_real_customer == 1) {
      // 客户账号，需要控制只能操作自己及控股企业
      this.getSearchData() // 企业账号获取企业及控股企业列表
    } else {
      this.restaurants = []
    }
    this.getGroupList() // 获取已有场景列表
  },

  methods: {
    beforeIpUpload(file, limit = 3) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    },
    // 关联数据确认
    confirmRelateData(val) {
      let sample_url = JSON.parse(JSON.stringify(val))
      let sample_urlArr = this.addForm.sample_url
        ? JSON.parse(
            JSON.stringify(
              this.addForm.sample_url.split(/[；|;|\r\n]/).filter((item) => item.trim())
            )
          )
        : []
      sample_urlArr.push(...sample_url)
      this.$set(this.addForm, 'sample_url', Array.from(new Set(sample_urlArr)).join('\r\r'))
    },
    // 关联情报数据查询
    relateIntelligence() {
      this.dialogRelateVisible = true
    },
    unfoldOrCollapse(type, isShow) {
      this['isShow' + type + 'Module'] = isShow
    },
    transferRate(rate) {
      if (rate == '0.00%') {
        return '-'
      } else {
        return rate.replace('.00', '')
      }
    },
    companyChangeOther(val) {
      if (!val) {
        this.other_customer_company = []
      }
    },
    async remoteMethodOther(val) {
      if (val !== '') {
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        this.checkedTree = []
        this.other_customer_company = []
        let obj = {
          name: val,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })
            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.other_customer_company = a
          }
        }, 200)
      } else {
        this.other_customer_company = []
      }
    },
    async getSearchData() {
      // 用户管理设置为【客户】后可测绘的企业列表
      let res = await kehuCompanyList({ operate_company_id: this.currentCompany })
      let data = []
      if (res.data && res.data.length > 0) {
        res.data.map((v) => {
          data.push({ value: v })
        })
      }
      this.restaurants = data
      this.kehuCompanyResults = data
    },
    // 客户账号输入企业名称
    querySearch(queryString) {
      let restaurants = this.restaurants
      this.kehuCompanyResults = []
      // 调用 callback 返回建议列表的数据
      if (queryString !== '') {
        // 有输入值，自动关联企业
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        this.treeIsTrue = false
        this.checkedTree = []
        let obj = {
          name: queryString,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })
            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.kehuCompanyResults = [...a]
          }
        }, 200)
      } else {
        // 没有输入值，展示客户可以测绘的企业
        this.kehuCompanyResults = queryString
          ? restaurants.filter(this.createFilter(queryString))
          : restaurants
      }
    },
    // 非客户账号输入企业名称，自动关联
    async remoteMethod(val) {
      if (val !== '') {
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        this.treeIsTrue = false
        this.checkedTree = []
        this.companyList = []
        let obj = {
          name: val,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })
            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.companyList = a
          }
        }, 200)
      } else {
        this.companyList = []
      }
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    companyChange(val) {
      if (!val) {
        this.companyList = []
        this.kehuCompanyResults = [...this.restaurants] // 客户账号默认展示可测绘企业
        this.treeIsTrue = false
      }
    },
    groupOptionClick(item) {
      this.addForm.companyName = item.company_name
      this.addForm.scene_group_id = item.id
    },
    async drawTypeChange() {
      this.addForm.companyName = ''
      this.addForm.expand_init_clues = false // 是否拿场景分组中的线索作为初始线索进行扩展 0不扩展/1进行扩展
      this.addForm.scene_group_id = '' // 场景库线索分组选择
    },
    async getGroupList() {
      let obj = {
        per_page: 10,
        no_page: '1',
        is_detect_scene: 1, // 代表是单位资产测绘场景列表
        operate_company_id: this.currentCompany
      }
      this.groupLoading = true
      let res = await cluesGroupListNoPage(obj)
        .catch(() => {
          this.groupLoading = false
        })
        .catch(() => {
          this.groupArr = []
        })
      this.groupLoading = false
      let arr = res.data
      this.groupArr = arr
    },
    // 控股企业全选
    onekeyCheck() {
      if (this.isTreeChecked) {
        let arr = this.companyCascadeEquityList[0].children.map((item) => {
          return item.company_name
        })
        arr.push(this.addForm.companyName)
        this.$refs.companyTree.setCheckedKeys(arr)
      } else {
        this.$refs.companyTree.setCheckedKeys([])
      }
      this.$forceUpdate()
    },
    // 企业关系查询
    async Inquire(no_cache) {
      if (this.addForm.companyName != '') {
        this.companyCascadeEquityList = [
          {
            company_id: this.addForm.companyName,
            company_name: this.addForm.companyName,
            level: 0,
            children: []
          }
        ]
        let obj = {
          company_id: this.addForm.companyName,
          operate_company_id: this.currentCompany,
          no_cache: no_cache,
          not_need_icp: 1 //不推送备案信息
        }
        this.treeIsTrue = true
        let res = await companyCascadeEquity(obj).catch(() => {})
        if (res.code == 0) {
          this.companyCascadeEquityList[0].children = res.data
          setTimeout(() => {
            this.checkedTree = res.data
              .filter((item) => {
                return parseFloat(item.rate) >= 50
              })
              .map((item) => {
                return item.company_name
              })
            this.checkedTree.push(this.addForm.companyName)
          }, 100)
        }
      }
    },
    iconUploadSucc(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        let obj = response.data
        this.content.content3.push(obj.url)
      } else {
        this.$message.error(file.response.message)
      }
    },
    iconUploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data.url
      })
      if (res.length == 0) {
        this.content.content3 = ''
      } else {
        this.content.content3 = res
      }
    },
    uploadErr(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    async sureGetClue() {
      this.$refs.addFormRef.validate(async (valid) => {
        if (valid) {
          // 如果是客户账号，只能操作本企业及控股企业
          if (this.is_real_customer == 1) {
            if (
              this.restaurants
                .map((item) => {
                  return item.value
                })
                .indexOf(this.addForm.companyName) == -1
            ) {
              this.$message.error('暂无权限，请重新输入')
              return
            }
          }
          let clueData = []
          for (let i = 0; i <= 6; i++) {
            if (
              (i == 3 && this.content['content' + i].length > 0) ||
              (i != 3 && this.content['content' + i])
            ) {
              clueData.push({
                type: i,
                content:
                  i != 3
                    ? this.content['content' + i]
                        .split(/[；|;|\r\n]/)
                        .filter((item) => {
                          return item.trim()
                        })
                        .map((item) => {
                          return item.trim()
                        })
                    : this.content['content' + i]
              })
            }
          }
          if (this.addForm.official_url || this.addForm.sample_url) {
            let official_url = this.addForm.official_url
              ? this.addForm.official_url
                  .split(/[；|;|\r\n]/)
                  .filter((item) => {
                    return item.trim()
                  })
                  .map((item) => {
                    return item.trim()
                  })
              : []
            let sample_url = this.addForm.sample_url
              ? this.addForm.sample_url
                  .split(/[；|;|\r\n]/)
                  .filter((item) => {
                    return item.trim()
                  })
                  .map((item) => {
                    return item.trim()
                  })
              : []
            if (sample_url.length > 10) {
              this.$message.error('样本目标最多可以添加10条URL')
              return
            }
            clueData.push({
              type: 10,
              content: official_url.concat(sample_url)
            })
          }
          let company_list = []
          if (this.$refs.companyTree) {
            // 若企业关系查询数据存在，合并otherCompany其他测绘企业并去重
            company_list = [
              ...new Set(this.$refs.companyTree.getCheckedKeys().concat(this.otherCompany))
            ]
          } else {
            company_list = [...this.otherCompany]
          }
          console.log(this.addForm.checkList,'----------')
          let obj = {
            detect_type: this.addForm.detect_type,
            name: this.addForm.companyName,
            expand_init_clues: this.addForm.expand_init_clues ? 1 : 0,
            scene_group_id: this.addForm.scene_group_id,
            has_cloud_assets: this.addForm.has_cloud_assets,
            is_detect_scene: this.addForm.is_detect_scene,
            operate_company_id: this.currentCompany,
            company_list: company_list,
            data: clueData,
            data_source: this.addForm.checkList,
          }
          if(this.user.role == 2 && !this.userInfo.is_local){
            obj.is_quake = this.addForm.checkList.filter(String).includes('2') ? 1 : 0
            obj.is_hunter = this.addForm.checkList.filter(String).includes('1') ? 1 : 0
          }
          this.sureLoading = true
          let res = await fakeSureCompany(obj).catch(() => {
            this.sureLoading = false
          })
          if (res.code == 0) {
            sessionStorage.setItem('mappingTaskId', res.data.fake_detect_task_id)
            sessionStorage.setItem('mappingTaskGroupId', res.data.group_id)
            //子传父
            this.$emit('son', '2', res.data.fake_detect_task_id)
            this.sureLoading = false
          }
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.highBtn {
  width: 174px;
}

.footer {
  // position: fixed;
  // bottom: 0;
  // left: 220px;
  // width: calc(100% - 220px) !important;
  width: 100%;

  // right: 0;
  // width: 100%;
  .normalBtn {
    width: 254px;
  }
}

.myBox1 {
  position: relative;
  // height: calc(100% - 56px);
  height: 100%;
  width: 100%;

  .el-input__suffix-inner > i {
    margin-top: 0 !important;
  }
  .content /deep/.el-select {
    display: block;
  }
}

.content {
  width: calc(100% - 32px);
  height: 100%;

  // height: calc(100% - 40px);
  padding: 0 16px;
  overflow: auto;

  .module {
    width: 100%;
    margin-bottom: 20px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    background: #ffffff;
    border-radius: 4px;

    .top {
      height: 46px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e9ebef;

      // margin-bottom: 32px;
      // span {
      //   color: #a1b5d8;
      // }

      .label {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        height: 2px;
        font-size: 16px;
        font-weight: 600;
        line-height: 16px;
        // height: 100%;
        // padding-left: 16px;
        // border-left: 4px solid #2677FF;
        .blueBlock {
          display: inline-block;
          width: 4px;
          height: 16px;
          // height: 100%;
          border-radius: 0 2px 2px 0;
          margin-right: 12px;
          background-color: #2677ff;
        }
      }

      .act {
        // line-height: 46px;
        display: flex;
        align-items: center;
        color: #2677ff;
        &:hover {
          cursor: pointer;
        }

        img {
          margin-right: 16px;
          margin-left: 4px;
        }
      }
    }

    .moduleBottom {
      // margin-top: 32px;
      padding: 32px 0;

      .inputNumber {
        width: 100%;
      }

      /deep/.el-select {
        width: 100% !important;

        .el-input__icon {
          // height: 30px;
          margin-top: 0;
        }
      }

      /deep/.el-form-item:last-child {
        margin-bottom: 0;
      }
    }
  }

  .findModule {
    // background:  url('../../assets/images/taskFirst1.png') no-repeat top center;
    background-image: url(../../assets/images/taskFirst1.png);
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top center;
    background-color: #fff;
  }

  .modelModule,
  .paramsModule,
  .taskModule {
    /deep/.el-form-item {
      .el-form-item__content {
        // display: flex;
        // align-items: center;
        // flex-direction: row;
        // justify-content: start;

        // height: 32px;
        line-height: 32px;

        .el-checkbox-group {
          height: 100%;
        }

        .el-checkbox {
          // height: 100%;
          line-height: 32px;
        }
      }
    }
  }

  /deep/.drawTypeClass {
    margin: 20px 0 20px 0;

    .el-form-item__label {
      line-height: 42px !important;

      img {
        line-height: 32px;
      }
    }
  }

  .treeWrap {
    .onekeyclass {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25px;
      background: #fff;
    }
  }
}

.companyTree {
  height: 222px;
  overflow: auto;
  color: #37393c;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #d1d5dd;

  .custom-tree-node {
    display: flex;
    align-items: center;
    width: 100%;

    .nodeLabel {
      display: inline-block;
      max-width: 70%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.treeNum {
  display: inline-block;
  width: 83px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  margin-left: 16px;
  font-size: 12px;
  border-radius: 14px;
}

.bigNum {
  background: #f0f3f8;
  border: 1px solid #dce5f3;

  .num {
    color: #2677ff;
  }
}

.smallNum {
  background: #f0f3f8;
  border: 1px solid #dce5f3;

  .num {
    color: #ec8f3c;
  }
}

// .companyBtn{
//   height: 32px;
//   border: 1px solid rgb(209, 213, 221);
// }
.companyGray {
  color: #ccd2db;
  // background: #CCD2DB;
}

.companyBlue {
  color: #2677ff;
  border: 0;
  &:hover {
    color: #4389ff;
  }
  // background: #2677FF !important;
}

/deep/.el-input__suffix-inner > i {
  margin-top: 3px;
}

/deep/.el-input__inner {
  height: 32px;
}

/deep/.companyDiv {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
  .select-button {
    position: relative;
    width: 100%;
    height: 32px;
    .el-input__inner {
      &:hover {
        cursor: text;
      }
    }

    #unit_sure {
      position: absolute;
      top: 1px;
      right: 1px;
      height: 30px;
      margin-left: 0px !important;
      border-radius: 0px 4px 4px 0px;
      border: 0;
    }
  }
  .el-autocomplete {
    width: 83% !important;

    .el-input__inner {
      // border-radius: 4px 0px 0px 4px !important;
      border-radius: 4px !important;
      cursor: text;
      height: 32px;
    }

    .el-form-item__content .el-input__icon {
      line-height: 36px !important;
    }
  }
  .el-button {
    border: 0;
    padding: 0 15px !important;
  }
  .el-select {
    position: relative;
    .el-input__suffix {
      display: none;
    }
    .el-input__inner {
      padding-right: 125px;
    }
  }
}

/deep/.upload-demo {
  .el-upload-dragger {
    height: 134px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #eff2f7;
    img {
      width: 28px;
    }
  }

  .el-upload-list {
    // max-height: 200px !important;
    min-height: auto;
    // padding-bottom: 20px;
    // display: none;
  }
  .el-upload__text {
    color: #62666c;
  }

  .el-upload__tip {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: rgba(98, 102, 108, 0.7);

    .downloadBtn {
      color: #2677ff;

      &:hover {
        cursor: pointer;
      }
    }
  }
}

.errorTip {
  line-height: 20px;
  font-size: 12px;
  color: red;
}

/deep/ .el-form {
  padding: 0 !important;

  .el-form-item {
    width: 724px;
    margin-left: 40px;

    .el-form-item__label {
      display: flex;
      align-items: center;
      text-align: left;

      img {
        &:hover {
          cursor: pointer;
        }

        // margin-left: px;
      }

      &::before {
        display: none;
      }
    }

    &.is-required {
      position: relative;

      .el-form-item__content::after {
        position: absolute;
        right: -16px;
        top: 0;
        content: '*';
        font-size: 24px;
        // width: 10px;
        color: #f56c6c;
      }
    }
  }
}
/deep/.sampleUrlRequire {
  .relateBtn {
    color: #2677ff;
    cursor: pointer;
  }
  .is-required {
    .el-form-item__content::after {
      top: 30px !important;
    }
  }
}
/deep/.el-tooltip__popper {
  li::before {
    content: '';
    width: 6px;
    height: 6px;
    display: inline-block;
    border-radius: 50%;
    background: black;
    vertical-align: middle;
    margin-right: 14px;
  }
}
/deep/.el-input__suffix {
  right: 15px;
}

.btn {
  &:hover {
    color: #2677ff;
  }
}
</style>
