<template>
  <div class="taskBox">
    <taskHeader
      tuClassName="tuSecondPhishing"
      :isExpand="isHeaderEx"
      :companyName="taskInfoData.name"
      :isOuterProcess="progressBar"
      :progress="currentPercent"
      :currentStep="2"
    >
      <template #bottomText>
        <div class="progressContent" v-if="accuracyIndex == 2 && !progressBar">
          <img src="../../assets/images/taskSed.png" alt="" />
          <span>{{ stepInfoArr[accuracyIndex].clueText }}：</span>
          <span style="color: #2677ff">{{ legendDataTotal }}</span
          >条，
          <span
            >主体词：<span style="color: #2677ff">{{ main_keyword_num }}</span
            >条，</span
          >
          <span
            >辅助词：<span style="color: #2677ff">{{ node_keyword_num }}</span
            >条</span
          >
        </div>
        <div class="progressContent" v-else>
          <img src="../../assets/images/taskSed.png" alt="" />
          <span>{{ stepInfoArr[accuracyIndex].clueText }}：</span>
          <span style="color: #2677ff">{{ tabTotal }}</span
          >条，
          <span
            >域名：<span style="color: #2677ff">{{ domain_num }}</span
            >条，</span
          >
          <span
            >ICON：<span style="color: #2677ff">{{ icon_num }}</span
            >条</span
          >，
          <span
            >关键词：<span style="color: #2677ff">{{ keyword_num }}</span
            >条</span
          >
          ，
          <span
            >FID：<span style="color: #2677ff">{{ fid_num }}</span
            >条</span
          >
        </div>
      </template>
      <template #buttonDiv>
        <el-button class="headerBtn" type="text" v-if="accuracyIndex == 0" @click="againGetClueFun">
          <svg class="icon svg-icon icon-repeat" aria-hidden="true">
            <use xlink:href="#icon-repeat"></use>
          </svg>
          重新获取
        </el-button>
        <el-button class="headerBtn" @click="endTask" id="unit_end" type="text">
          <svg class="icon svg-icon icon-close" aria-hidden="true">
            <use xlink:href="#icon-close"></use>
          </svg>
          结束流程
        </el-button>
      </template>
    </taskHeader>
    <!-- <div class="box">
          <div class="grayBox">
              <div>
                <span class="unitCompany">当前企业：
                  <el-tooltip class="item" effect="dark" :content="taskInfoData.name" placement="top" :open-delay="500">
                    <span>{{taskInfoData.name}}</span>
                  </el-tooltip>
                </span>
                <span v-if="accuracyIndex == 2"><span>特征线索获取 > <span style="color: #2677FF;font-weight: 600;">{{stepInfoArr[accuracyIndex].tableText}}</span></span><img src="../../assets/images/triangle.png" alt="" class="graySan"><span class="graylabel">{{stepInfoArr[accuracyIndex].clueTitle}}</span></span>
                <span v-else><span>特征线索获取 > <span style="color: #2677FF;font-weight: 600;">{{stepInfoArr[accuracyIndex].clueSecondTitle}}</span></span><img src="../../assets/images/triangle.png" alt="" class="graySan"><span class="graylabel">{{stepInfoArr[accuracyIndex].clueTitle}}</span></span>
              </div>
              <div>
                  <el-button style="margin-right: 20px;" class="normalBtn" v-if="accuracyIndex == 0" type="primary" @click="againGetClueFun">重新获取</el-button>
                  <span style="color: #62666C;cursor: pointer;" @click="endTask" id="unit_end"><i class="el-icon-switch-button" style="margin-right:5px"></i>结束流程</span>
              </div>
          </div>
          <div v-if="progressBar" class="progressBarBox">
              <div class="tu tuSecond">

              </div>
              <div class="progressBox">
                  <div>正在获取线索</div>
                  <el-progress :stroke-width="14" :percentage="currentPercent" style="margin-bottom:20px"></el-progress>
                  <div class="progressContent">
                      <img src="../../assets/images/taskSed.png" alt="">
                      <span>{{stepInfoArr[accuracyIndex].clueText}}：</span>
                      <span style="color: #2677FF;">{{tabTotal}}</span>条，
                      <span>域名：<span style="color: #2677FF;">{{domain_num}}</span>条，</span>
                      <span>ICON：<span style="color: #2677FF;">{{icon_num}}</span>条</span>，
                       <span>关键词：<span style="color: #2677FF;">{{keyword_num}}</span>条</span>
                  </div>
              </div>
          </div>
          <div v-else>
              <div class="progressBoxFinish">
                  <span v-if="accuracyIndex == 2"><i class="el-icon-success"></i>关键词联想完成</span>
                  <span v-else><i class="el-icon-success"></i>获取线索完成</span>
                  <div v-if="accuracyIndex == 2" class="progressContent">
                      <img src="../../assets/images/taskSed.png" alt="">
                      <span>{{stepInfoArr[accuracyIndex].clueText}}：</span>
                      <span style="color: #2677FF;">{{legendDataTotal}}</span>条，
                      <span>主体词：<span style="color: #2677FF;">{{main_keyword_num}}</span>条，</span>
                      <span>辅助词：<span style="color: #2677FF;">{{node_keyword_num}}</span>条</span>
                  </div>
                  <div v-else class="progressContent">
                      <img src="../../assets/images/taskSed.png" alt="">
                      <span>{{stepInfoArr[accuracyIndex].clueText}}：</span>
                      <span style="color: #2677FF;">{{tabTotal}}</span>条，
                      <span>域名：<span style="color: #2677FF;">{{domain_num}}</span>条，</span>
                      <span>ICON：<span style="color: #2677FF;">{{icon_num}}</span>条</span>，
                      <span>关键词：<span style="color: #2677FF;">{{keyword_num}}</span>条</span>
                  </div>
              </div>
          </div>
      </div> -->
    <div class="myBox">
      <div :class="progressBar ? 'boxTwo' : 'boxTwo1'">
        <div
          class="eltableBox"
          v-if="
            accuracyIndex == 0 || accuracyIndex == 1 || accuracyIndex == 3 || accuracyIndex == 4
          "
        >
          <ul class="ulBox">
            <li class="ultitle">线索分类</li>
            <li
              v-for="(item, index) in tabList"
              :key="index"
              @click="changeTab(item.label)"
              class="clueList"
              :style="
                tabActive == item.label ? 'background: #EAEFF6;color:#2677FF;font-weight: 500;' : ''
              "
            >
              <span class="fangkuai" v-if="item.label == 0"></span>
              <span class="fangkuai fangkuai4" v-else-if="item.label == 4"></span>
              <span class="fangkuai fangkuai3" v-else-if="item.label == 3"></span>
              <span class="fangkuai fangkuai10" v-else-if="item.label == 10"></span>
              {{ getType(item.name) }}
            </li>
          </ul>
          <div class="myTable" v-loading="loading">
            <div class="tableLabel">
              <div>
                <div>{{ stepInfoArr[accuracyIndex].tableText }}</div>
                <div style="color: rgba(98, 102, 108, 0.7); margin-left: 8px; font-size: 12px">{{
                  tableName
                }}</div>
              </div>
              <div>
                <el-button class="normalBtn" type="primary" @click="unclaimChecked"
                  >一键操作疑似线索</el-button
                >
                <!-- 关键词联想那一步不要新增 -->
                <el-button
                  class="normalBtn"
                  v-if="accuracyIndex != 1"
                  :disabled="progressBar"
                  type="primary"
                  @click="insertClueShow"
                  id="unit_add"
                  >新增</el-button
                >
                <el-button
                  class="normalBtn"
                  :loading="removeLoading"
                  type="primary"
                  @click="removeFun"
                  id="unit_del"
                  >忽略</el-button
                >
                <el-button
                  class="normalBtnRe"
                  v-if="accuracyIndex == 4"
                  :loading="exportLoading"
                  type="primary"
                  @click="exportList"
                  id="unit_export"
                  >导出</el-button
                >
              </div>
            </div>
            <div
              v-if="
                accuracyIndex == 0 || accuracyIndex == 1 || accuracyIndex == 3 || accuracyIndex == 4
              "
              class="myTableContent"
              ref="myTableContent"
            >
              <clueTable
                ref="clueTable"
                v-loading="tableLoading"
                @scrollChangeTab="scrollChangeTab"
                :handleIsShow="true"
                :onekey="onekey"
                @checkedArr="checkedArrFun"
                :tableData="tableData"
                :tableHeader="tableHeader"
                :isPhish="{ accuracyIndex: accuracyIndex }"
              ></clueTable>
            </div>
          </div>
        </div>
        <keywordLegend
          :legendData="legendData"
          :taskInfoData="taskInfoData"
          @getfakeModelList="getfakeModelList"
          v-if="accuracyIndex == 2"
        />
        <div class="footer">
          <el-button
            class="normalBtn"
            type="primary"
            @click="clueExpanding"
            id="unit_clue"
            :disabled="progressBar"
            v-if="accuracyIndex == 0"
            >特征线索扩展</el-button
          >
          <!-- <el-button class="normalBtn" type="primary" @click="legendModelShow" id="unit_clue" :disabled="progressBar" v-if="accuracyIndex == 1">关键词联想</el-button> -->
          <el-button
            class="normalBtn"
            type="primary"
            @click="getKeyword"
            id="unit_clue"
            :disabled="progressBar"
            :loading="keyBtnLoading"
            v-if="accuracyIndex == 2"
            >获取关键词</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            @click="getAllCluesList"
            id="unit_clue"
            :disabled="progressBar"
            v-if="accuracyIndex == 3"
            >生成线索总表</el-button
          >
          <div v-if="domain_num == 0">
            <el-popover
            placement="top"
            trigger="hover"
            content="域名线索为空,无法进行仿冒数据发现。">
              <el-button
                slot="reference"
                class="normalBtn"
                type="primary"
                @click="gofakeDataFind"
                id="unit_clue"
                :disabled="domain_num == 0"
                v-show="accuracyIndex == 4"
                >仿冒数据发现</el-button
              >
            </el-popover>
          </div>
          <div v-else>
            <el-button
                slot="reference"
                class="normalBtn"
                type="primary"
                @click="gofakeDataFind"
                id="unit_clue"
                :disabled="btnLoading"
                v-show="accuracyIndex == 4"
                >仿冒数据发现</el-button
              >
          </div>
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="myReturn"
            id="unit_return"
            :disabled="progressBar"
            v-if="accuracyIndex > 0 && accuracyIndex < 5"
            >返回</el-button
          >
        </div>
      </div>
      <el-dialog
        class="elDialogAdd elDialogAdd1"
        :close-on-click-modal="false"
        :visible.sync="clueExpansion"
        width="480px"
      >
        <template slot="title"> 联想模式 </template>
        <div class="dialog-body">
          <div>
            <el-radio-group v-model="legendType">
              <!-- 1/2 系统自动模式/自定义模式 -->
              <el-radio :label="1" id="unit_clue_speed">系统自动模式</el-radio>
              <el-radio :label="2" id="unit_clue_depth">自定义模式</el-radio>
            </el-radio-group>
            <div v-if="legendType == 1">
              <p class="modelShow" style="margin-top: 10px">系统自动模式：系统自动进行关键词联想</p>
            </div>
            <div v-if="legendType == 2">
              <p class="modelShow" style="margin-top: 20px"
                >自定义模式：支持自定义联想策略，设置主体词和辅助词</p
              >
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button class="highBtnRe" @click="clueExpansion = false" id="unit_clue_sure"
            >关闭</el-button
          >
          <el-button
            class="highBtn"
            :loading="legendLoading"
            @click="legendModelSave"
            id="unit_clue_cancel"
            >确定</el-button
          >
        </div>
      </el-dialog>
      <!-- 新建线索 -->
      <clueDialog
        :dialogFormVisibleInsert="dialogFormVisibleInsert"
        :is_fake="{ status: accuracyIndex }"
        :currentGroupId="taskInfoData.group_id"
        :tabActiveName="tabActiveName"
        :company_name="taskInfoData.name"
        :ruleForm="clueRuleForm"
        @insertClueSave="insertClueSave"
      />
      <suspectedCluesOperation
        ref="suspectedCluesOperation"
        :tabList="tabList"
        @removeOne="removeOne"
        @removeMore="removeMore"
        :user="user"
        :tableHeader="tableHeader"
        :tableData="tableData"
        :dialogVisible="suspectedClueVisible"
        @closeDialog="closeSuspectDialog"
      />
    </div>
  </div>
</template>
<script>
import suspectedCluesOperation from '@/views/unit_surveying/suspectedCluesOperation'
import taskHeader from '../../views/unit_surveying/taskHeader.vue'
import * as animationData from '../../assets/images/data.json'
import Lottie from 'vue-lottie/src/lottie.vue'
import clueTable from './taskSecondTable.vue'
import keywordLegend from './keywordLegend.vue'
import clueDialog from '../cloudRecommend/clueInsertDialog.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { passClueV1, tabNumClues, exportCluesListV1 } from '@/api/apiConfig/clue.js'
import {
  fakeReturn,
  fakeAllClues,
  fakeMergeKeyword,
  fakeModelList,
  fakeModelSet,
  fakeExpanding,
  fakeGetCluelist,
  fakeAgainGetClue,
  fakeDataFind,
  fakeEndDetectTask
} from '@/api/apiConfig/phishing.js'

export default {
  components: { suspectedCluesOperation, Lottie, clueDialog, clueTable, keywordLegend, taskHeader },
  props: ['taskInfoData'],
  data() {
    return {
      filterDomainArr: [],
      suspectedTableData: [],
      suspectedClueVisible: false,
      isHeaderEx: true, //header  true 展开 false 收起
      keyBtnLoading: false,
      onekey: false,
      currentPercent: 0, //进度条
      defaultOptions: {
        //json动画
        animationData: animationData.default
      },
      dialogFormVisibleInsert: false,
      tabActiveName: '0',
      againIsTrue: true, // 重新获取的按钮
      clueRuleForm: {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      },
      progressBar: true, //是否显示进度条
      btnLoading: false,
      tabTotal: 0,
      total: 0,
      currentPage: 1,
      pageSize: 20,
      pageSizeArr: [10, 30, 50, 100],
      domain_num: 0,
      ip_num: 0,
      icp_num: 0,
      cert_num: 0,
      icon_num: 0,
      keyword_num: 0,
      fid_num: 0,
      tableName: '可手动忽略非本企业的线索内容',
      tabActive: '6', // 左侧导航默认选中IP段：6
      tabList: [
        {
          label: '0',
          name: '域名'
        },
        {
          label: '3',
          name: 'ICON'
        },
        {
          label: '4',
          name: '关键词'
        },
        {
          label: '10',
          name: 'FID'
        }
      ],
      tableLoading: false,
      legendData: [],
      legendDataTotal: 0,
      main_keyword_num: 0,
      node_keyword_num: 0,
      tableData: [],
      tableArr: [],
      tableHeader: [
        {
          label: '线索',
          name: 'content',
          fixed: 'left'
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          minWidth: '150px'
        }
      ],
      legendLoading: false,
      clueExpansion: false, //扩展线索框
      legendType: 1, // 关键词联想模式： 1/2 系统自动模式/自定义模式
      accuracyIndex: 0,
      stepInfoArr: [
        {
          clueSecondTitle: '初始线索',
          clueText: '成功获取企业初始线索',
          clueTitle: '依据企业名称获取单位资产初始线索',
          tableText: '初始线索'
        },
        {
          clueSecondTitle: '线索扩展',
          clueText: '成功获取特征线索',
          clueTitle: '通过域名线索扩展，扩展线索已自动补充至线索库',
          tableText: '特征线索'
        },
        {
          clueSecondTitle: '线索扩展',
          clueText: '成功获取特征线索',
          clueTitle: '通过域名线索扩展，扩展线索已自动补充至线索库',
          tableText: '特征线索'
        },
        {
          clueSecondTitle: '线索扩展',
          clueText: '成功获取特征线索',
          clueTitle: '通过域名线索扩展，扩展线索已自动补充至线索库',
          tableText: '特征线索'
        },
        // {
        //   clueSecondTitle: '关键词获取',
        //   clueText: '关键词',
        //   clueTitle: '支持自定义主体词和辅助词，进行关键词联想',
        //   tableText: '关键词自定义'
        // },
        // {
        //   clueSecondTitle: '关键词获取',
        //   clueText: '关键词获取',
        //   clueTitle: '通过基础关键词进行智能联想，联想后的线索已自动补充至线索库',
        //   tableText: '关键词联想'
        // },
        {
          clueSecondTitle: '已生成线索总表',
          clueText: '成功获取特征线索',
          clueTitle: '已生成线索总表，可进行仿冒数据发现',
          tableText: '线索总表'
        }
      ],
      task_id: '',
      group_id: '',
      user: {
        role: ''
      },
      userInfo: '',
      checkedArr0: [],
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      checkedArr10: [],
      checkedArr: [],
      tabActiveNameStatus: 1,
      setTimer: null,
      loading: false,
      removeLoading: false,
      exportLoading: false,
      headText: '线索扩展'
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.user.role == 2) {
        this.tableData = []
        this.tableArr = []
        sessionStorage.removeItem('scrollTop')
      }
    },
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    taskInfoData(val) {
      this.getInit()
    },
    // 通过哪个分类扩展，左侧就不用显示哪个分类的导航
    accuracyIndex(val) {
      if (val == 0) {
        this.tableHeader = [
          {
            label: '线索',
            name: 'content',
            width: '310px'
          },
          {
            label: '企业名称',
            name: 'clue_company_name',
            minWidth: '150px'
          }
        ]
      } else {
        this.tableHeader = [
          {
            label: '线索',
            name: 'content'
          },
          {
            label: '企业名称',
            name: 'clue_company_name',
            minWidth: '150px'
          },
          {
            label: '线索源',
            name: 'chain_list',
            fixed: 'left'
          }
        ]
      }
      if (val == 0) {
        //初始线索
        this.tabActiveNameStatus = 1
      } else if (val == 4) {
        // 生成总表后
        this.tabActiveNameStatus = 1
      } else {
        //扩展线索
        this.tabActiveNameStatus = 0
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2) {
      // 安服账号切换企业需要回到一级页面
      if (!this.currentCompany) return
      this.getInit()
    } else {
      this.getInit()
    }

    sessionStorage.removeItem('scrollTop')
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    // 滚动改变tab线索分类选中
    scrollChangeTab(type) {
      this.tabActive = String(type)
    },
    //切换线索分类
    changeTab(val) {
      var arr = this.tableData.map((item) => {
        return item.type
      })
      var num = arr.indexOf(Number(val))
      if (num != -1) {
        // 获取行高
        let dataList = document.getElementsByClassName('borderWrap')[0].clientHeight
        // 滚动距离
        document.getElementsByClassName('clueContent')[0].scrollTop = dataList * num
        setTimeout(() => {
          this.tabActive = val
        }, 100)
      }
    },
    getType(val) {
      //tabActive
      if (val == '域名') {
        return `域名(${this.domain_num})`
      } else if (val == 'ICON') {
        return `ICON(${this.icon_num})`
      } else if (val == 'FID') {
        return `FID(${this.fid_num})`
      } else {
        return `关键词(${this.keyword_num})`
      }
    },
    filterSuspectData() {
      this.suspectedTableData = this.tableData.filter((item) => item.is_highlight)
    },
    // 疑似资产一键选中
    unclaimChecked() {
      // this.onekey =  this.onekey ? false : true
      this.filterSuspectData()
      if (this.suspectedTableData.length == 0) {
        this.$message.warning('暂无疑似线索可操作')
        return
      }
      this.suspectedClueVisible = true
    },
    closeSuspectDialog() {
      this.suspectedClueVisible = false
    },
    insertClueShow() {
      this.clueRuleForm = {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: ''
      }
      this.dialogFormVisibleInsert = true
      if (this.tabActiveName == 3) {
        this.clueRuleForm.content = []
      } else {
        this.clueRuleForm.content = ''
      }
      if (this.tabActiveName == '3') {
        this.$set(this.clueRuleForm, 'way', 1)
      } else {
        this.$set(this.clueRuleForm, 'way', 0)
      }
      this.fileList = []
    },
    // 线索保存
    insertClueSave(isClose) {
      this.dialogFormVisibleInsert = false
      this.clueRuleForm = {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      }
      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []
      this.checkedArr10 = []
      if (!isClose) {
        // 关闭弹层不需要执行
        this.getTable(this.tabActiveNameStatus)
        this.getTab(this.tabActiveNameStatus)
      }
    },
    async exportList() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0 &&
        this.checkedArr10.length == 0
      ) {
        this.$message.error('请选择要导出的数据！')
        return
      }
      let clueData = [
        {
          id: this.checkedArr0,
          type: '0',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr1,
          type: '1',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr2,
          type: '2',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr3,
          type: '3',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr4,
          type: '4',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr5,
          type: '5',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr6,
          type: '6',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr10,
          type: '10',
          is_all: '',
          keyword: ''
        }
      ]
      let obj = {
        tab_status: this.tabActiveNameStatus,
        group_id: this.taskInfoData.group_id,
        data: clueData,
        keyword: {
          domain_keyword: '',
          cert_keyword: '',
          icp_keyword: '',
          key_keyword: '',
          ip_keyword: ''
        },
        operate_company_id: this.currentCompany
      }
      this.exportLoading = true
      let res = await exportCluesListV1(obj).catch(() => {
        this.exportLoading = false
      })
      if (res.code == 0) {
        this.exportLoading = false
        this.checkedArr0 = []
        this.checkedArr1 = []
        this.checkedArr2 = []
        this.checkedArr3 = []
        this.checkedArr4 = []
        this.checkedArr5 = []
        this.checkedArr6 = []
        this.checkedArr10 = []
        this.download(this.showSrcIp + res.data.url)
      }
    },
    async getInit() {
      if (this.taskInfoData) {
        this.currentPercent = parseFloat(this.taskInfoData.clue_progress)
        this.task_id = this.taskInfoData.id
        this.group_id = this.taskInfoData.group_id
        if (this.taskInfoData.step_detail == '200') {
          this.tableHeader = [
            {
              label: '线索',
              name: 'content',
              width: '310px'
            },
            {
              label: '企业名称',
              name: 'clue_company_name',
              minWidth: '150px'
            }
          ]
        } else {
          this.tableHeader = [
            {
              label: '线索',
              name: 'content'
            },
            {
              label: '企业名称',
              name: 'clue_company_name',
              minWidth: '150px'
            },
            {
              label: '线索源',
              name: 'chain_list',
              fixed: 'left'
            }
          ]
        }
        if (this.taskInfoData.step_detail == '200') {
          //公司名称获取初始线索
          this.tabActive = '0'
          this.accuracyIndex = 0
          this.tabActiveNameStatus = 1
          if (this.taskInfoData.step_status == '0') {
            this.progressBar = true
            this.againIsTrue = false // 重新获取原始线索的按钮是否显示
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTable(this.tabActiveNameStatus)
              this.getTab(this.tabActiveNameStatus)
            }, 20000) // 每10秒刷新一次
          } else {
            clearInterval(this.setTimer)
            this.setTimer = null
            this.progressBar = false
            this.againIsTrue = true // 重新获取原始线索的按钮是否显示
            this.getTable(this.tabActiveNameStatus)
            this.getTab(this.tabActiveNameStatus)
          }
        } else if (this.taskInfoData.step_detail == '201') {
          // 通过域名扩展完成
          this.againIsTrue = false // 重新获取原始线索的按钮是否显示
          this.tabActive = '0'
          this.accuracyIndex = 3
          this.tabActiveNameStatus = 0
          if (this.taskInfoData.step_status == '0') {
            this.progressBar = true
            this.getTable(this.tabActiveNameStatus)
            this.getTab(this.tabActiveNameStatus)
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTable(this.tabActiveNameStatus)
              this.getTab(this.tabActiveNameStatus)
            }, 20000) // 每10秒刷新一次
          } else {
            this.progressBar = false
            this.getTable(this.tabActiveNameStatus)
            this.getTab(this.tabActiveNameStatus)
          }
        } else if (this.taskInfoData.step_detail == '202') {
          // 关键词模式选择完毕
          this.againIsTrue = false // 重新获取原始线索的按钮是否显示
          this.accuracyIndex = 2
          this.tabActiveNameStatus = 0
          this.progressBar = false
          this.getfakeModelList()
        } else if (this.taskInfoData.step_detail == '204') {
          // 开始生成线索总表
          this.accuracyIndex = 3
          this.tabActiveNameStatus = 0
          this.progressBar = false
          this.getTable(this.tabActiveNameStatus)
          this.getTab(this.tabActiveNameStatus)
        } else {
          // 已经生成线索总表，开始仿冒数据发现
          this.accuracyIndex = 4
          this.tabActiveNameStatus = 1
          this.progressBar = false
          this.getTable(1)
          this.getTab(1)
        }
      }
    },
    // 返回
    async myReturn() {
      let obj = {
        fake_detect_task_id: this.task_id,
        operate_company_id: this.currentCompany
      }
      let res = await fakeReturn(obj)
      if (res.code == 0) {
        this.$emit('son', '2', this.task_id)
      }
    },
    async handleMessage(res, o) {
      //处理接收到的信息
      // 根据公司名称获取初始线索
      if (
        res.cmd == 'detect_assets_tip1_process' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        this.accuracyIndex = 0
        if (res.data.progress == 100) {
          // if (!this.progressBar) return
          this.currentPercent = 100
          this.progressBar = false
          this.againIsTrue = true // 重新获取原始线索的按钮是否显示
          this.getTable(1)
          this.getTab(1)
          clearInterval(this.setTimer)
          this.setTimer = null
        } else {
          this.progressBar = true
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
      // 完成ip扩展
      if (
        res.cmd == 'detect_assets_tip2_ip' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        this.accuracyIndex = 3
        if (res.data.progress == 100) {
          // if (!this.progressBar) return
          this.currentPercent = 100
          this.progressBar = false
          this.getTable(0)
          this.getTab(0)
          clearInterval(this.setTimer)
          this.setTimer = null
        } else {
          this.progressBar = true
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
      // 完成域名扩展
      if (
        res.cmd == 'detect_assets_tip2_domain' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        //根域扩展
        this.accuracyIndex = 3
        if (res.data.progress == 100) {
          // if (!this.progressBar) return
          this.currentPercent = 100
          setTimeout(() => {
            this.progressBar = false
            this.getTable(0)
            this.getTab(0)
          }, 3000)
          clearInterval(this.setTimer)
          this.setTimer = null
        } else {
          this.progressBar = true
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
    },
    // 统计根域、icp等
    async getTab(val) {
      let obj = {
        group_id: this.group_id,
        data: {
          status: val, // 线索总表和获取初始线索是1，其余为0
          operate_company_id: this.currentCompany,
          fake_detect_task_id: this.taskInfoData.id
        }
      }
      let res = await tabNumClues(obj)
      if (res.code == 0) {
        this.tabTotal = 0
        if (res.data.clues_count.length != 0) {
          res.data.clues_count.forEach((val) => {
            if (val.type == '0') {
              this.domain_num = val.count
            }
            if (val.type == '3') {
              this.icon_num = val.count
            }
            if (val.type == '4') {
              this.keyword_num = val.count
            }
            if (val.type == '10') {
              this.fid_num = val.count
            }
          })
          this.tabTotal = this.domain_num + this.icon_num + this.keyword_num + this.fid_num
        }
      }
    },
    // 获取所有线索列表
    async getTable(val) {
      this.tableArr = []
      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []
      this.checkedArr10 = []
      let obj = {
        keyword: '',
        page: this.currentPage,
        per_page: this.pageSize,
        status: val, // 线索总表和获取初始线索是1，其余为0
        group_id: this.group_id,
        fake_detect_task_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany,
        // type: this.tabActive,
        is_whole: 1
      }
      this.tableLoading = true
      let res = await fakeGetCluelist(obj).catch(() => {
        this.tableLoading = false
      })
      if (res.code == 0) {
        this.tableLoading = false
        this.tableData = this.tableArr
          .concat(res.data.domain)
          .concat(res.data.icon)
          .concat(res.data.title ? res.data.title : [])
          .concat(res.data.fid ? res.data.fid : [])
        this.onekey = false // 表格数据更新重置
        if (sessionStorage.getItem('scrollTop')) {
          setTimeout(() => {
            // 获取行高
            let dataList = document.getElementsByClassName('borderWrap')[0].clientHeight
            // 滚动距离
            document.getElementsByClassName('clueContent')[0].scrollTop =
              sessionStorage.getItem('scrollTop')
            let num = parseInt(sessionStorage.getItem('scrollTop') / dataList)
            let type = this.tableData[num].type //当前类型
            this.tabActive = type
          }, 500)
        } else {
          setTimeout(() => {
            if (document.getElementsByClassName('clueContent')) {
              document.getElementsByClassName('clueContent')[0].scrollTop = 0
            }
            if (this.tableData.length != 0) {
              this.tabActive = String(this.tableData[0].type)
            }
          }, 500)
        }
        this.loading = false
      }
    },
    checkedArrFun(arr) {
      // 所有选中的数据
      this.filterDomainArr = [] // 初始域名数组
      // arr.forEach(item => {
      //   if (this.accuracyIndex == 0) { // 初始线索列表直接过滤掉域名
      //     if (String(item.type) != 0 || item.is_highlight) {
      //       this.filterDomainArr.push(item.id)
      //     }
      //   } else { // 其他步骤列表，过滤掉初始域名
      //     if (item.type == 0 && this.getSource(item['chain_list'], item.parent_id, item.source) != '初始线索') {
      //       this.filterDomainArr.push(item.id)
      //     }
      //   }
      // })
      this.checkedArr0 = arr
        .filter((item) => {
          return String(item.type) == 0
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr3 = arr
        .filter((item) => {
          return String(item.type) == 3
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr4 = arr
        .filter((item) => {
          return String(item.type) == 4
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr10 = arr
        .filter((item) => {
          return String(item.type) == 10
        })
        .map((item) => {
          return item.id
        })
    },
    getSource(val, parent_id, source) {
      //线索来源
      if (val.length != 0) {
        if (val.length >= 2) {
          return val[val.length - 2].content
        } else {
          return val[0].content
        }
      } else {
        // 初始线索：0、3、4、5，扩展线索：2
        if (parent_id == 0 && source == 0) {
          return '初始线索'
        } else if (parent_id == 0 && source == 2) {
          return '扩展线索'
        } else if (parent_id == 0 && source == 4) {
          return '初始线索'
        }
      }
    },
    async removeFun() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr10.length == 0
      ) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      // else {
      //   if (this.checkedArr0.length > 0 && this.checkedArr3.length == 0 && this.checkedArr4.length == 0 && this.checkedArr10.length == 0) {
      //     if (this.filterDomainArr.length == 0) { // 可忽略列表为空，证明都是初始线索
      //       this.$message.error('初始域名非疑似线索不可忽略!')
      //       return
      //     }
      //   }
      // }
      let clueData = [
        {
          id: this.checkedArr0,
          type: '0',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr1,
          type: '1',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr2,
          type: '2',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr3,
          type: '3',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr4,
          type: '4',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr5,
          type: '5',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr6,
          type: '6',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr10,
          type: '10',
          is_all: '',
          keyword: ''
        }
      ]
      this.removeMore(clueData)
    },
    removeMore(clueData) {
      // this.$confirm(`${this.checkedArr0.length > 0 ? '确定忽略数据(初始域名非疑似线索不可忽略，已自动过滤)?' : '确定忽略数据?'}`, '提示', {
      this.$confirm('确定忽略数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      })
        .then(async () => {
          this.removeLoading = true
          let obj = {
            status: 2,
            tab_status: this.tabActiveNameStatus,
            group_id: this.group_id,
            data: clueData,
            operate_company_id: this.currentCompany
          }
          let res = await passClueV1(obj).catch(() => {
            this.removeLoading = false
          })
          if (res.code == 0) {
            this.removeLoading = false
            this.$message.success('操作成功！')
            this.checkedArr0 = []
            this.checkedArr1 = []
            this.checkedArr2 = []
            this.checkedArr3 = []
            this.checkedArr4 = []
            this.checkedArr5 = []
            this.checkedArr6 = []
            this.checkedArr10 = []
            this.getTable(this.tabActiveNameStatus)
            this.getTab(this.tabActiveNameStatus)
            this.$refs.suspectedCluesOperation &&
              this.$refs.suspectedCluesOperation.$refs.eltable.clearSelection()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.unit_del>.el-message-box__btns')
        del.children[0].id = 'unit_del_cancel'
        del.children[1].id = 'unit_del_sure'
      }, 50)
    },
    removeOne(id, type) {
      let data = [
        {
          id: [id],
          type: String(type)
        }
      ]
      this.$confirm('确定忽略数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            status: 2,
            tab_status: this.tabActiveNameStatus,
            group_id: this.group_id,
            data: data,
            operate_company_id: this.currentCompany
          }
          let res = await passClueV1(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            for (var i = 0; i < this.tableData.length; i++) {
              if (this.tableData[i].id == id) {
                this.tableData.splice(i, 1)
              }
            }
            this.getTable(this.tabActiveNameStatus)
            this.getTab(this.tabActiveNameStatus)
            // this.doLayout(this, 'eltable')// 解决表格错位
          }
        })
        .catch(() => {})
    },
    // 特征线索获取
    async clueExpanding() {
      // 没有获取到初始线索
      if (!this.progressBar && this.tabTotal == 0) {
        this.$message.warning(
          '未查询到企业资产初始线索信息，建议手动导入或者确认该企业是否存在初始线索'
        )
        return
      }
      let obj = {
        fake_detect_task_id: this.task_id,
        operate_company_id: this.currentCompany
      }
      this.headText = '正在扩展线索…'
      this.currentPercent = 0
      let res = await fakeExpanding(obj)
      if (res.code == 0) {
        this.accuracyIndex = 3 // 域名扩展线索
        this.tableLoading = true
        setTimeout(() => {
          this.$emit('getTaskInfo', this.task_id)
          this.getTable(this.tabActiveNameStatus)
          this.getTab(this.tabActiveNameStatus)
        }, 1000)
      }
    },
    // 关键词联想模式确定
    async legendModelSave() {
      this.legendLoading = true
      let res = await fakeModelSet({
        detect_type: this.legendType,
        fake_detect_task_id: this.task_id,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.legendLoading = false
      })
      if (res.code == 0) {
        this.legendLoading = false
        this.clueExpansion = false
        if (this.legendType == 1) {
          // 自动模式，直接获取组合关键词列表
          this.accuracyIndex = 3
          this.getTable(this.tabActiveNameStatus)
          this.getTab(this.tabActiveNameStatus)
        } else {
          // 自定义模式需要关键词联想
          this.accuracyIndex = 2
          this.getfakeModelList()
        }
      }
    },
    async getfakeModelList() {
      let res = await fakeModelList({
        fake_detect_task_id: this.task_id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.legendData = res.data
        this.main_keyword_num =
          this.legendData.main_keyword && this.legendData.main_keyword.length
            ? this.legendData.main_keyword.length
            : 0
        this.node_keyword_num =
          this.legendData.node_keyword && this.legendData.node_keyword.length
            ? this.legendData.node_keyword.length
            : 0
        this.legendDataTotal = this.main_keyword_num + this.node_keyword_num
      }
    },
    // 关键词联想点击
    async legendModelShow() {
      this.clueExpansion = true
    },
    // 获取关键词
    async getKeyword() {
      this.keyBtnLoading = true
      this.tableArr = []
      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []
      this.checkedArr10 = []
      this.domain_num = 0
      this.ip_num = 0
      this.icp_num = 0
      this.cert_num = 0
      this.icon_num = 0
      this.keyword_num = 0
      this.tableData = []
      let res = await fakeMergeKeyword({
        fake_detect_task_id: this.task_id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.getTable(this.tabActiveNameStatus)
        this.getTab(this.tabActiveNameStatus)
        this.accuracyIndex = 3
        this.keyBtnLoading = false
      }
    },
    // 生成线索总表
    async getAllCluesList() {
      let res = await fakeAllClues({
        fake_detect_task_id: this.task_id,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.accuracyIndex = 4
        this.getTable(1)
        this.getTab(1)
      }
    },
    // 仿冒数据发现
    async gofakeDataFind() {
      this.btnLoading = true
      let res = await fakeDataFind({
        task_id: this.task_id,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.btnLoading = false
        this.$emit('son', '3', this.task_id)
      }
    },
    // 重新获取原始线索
    async againGetClueFun() {
      this.$confirm('确定重新获取初始线索?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_end_cancel',
        confirmButtonClass: 'unit_end_sure',
        customClass: 'unit_end',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            fake_detect_task_id: this.task_id,
            operate_company_id: this.currentCompany
          }
          let res = await fakeAgainGetClue(obj)
          this.againIsTrue = false
          this.progressBar = true
          this.currentPercent = 0
          this.tabTotal = 0
          this.keyword_num = 0
          this.domain_num = 0
          this.icon_num = 0
          this.tableData = []
          this.$message.success('操作成功！')
        })
        .catch(() => {})
    },
    // 结束流程
    async endTask() {
      this.$confirm('确定结束流程?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_end_cancel',
        confirmButtonClass: 'unit_end_sure',
        customClass: 'unit_end',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            fake_detect_task_id: this.task_id,
            operate_company_id: this.currentCompany
          }
          let res = await fakeEndDetectTask(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.$emit('son', '1')
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.unit_end>.el-message-box__btns')
        del.children[0].id = 'unit_end_cancel'
        del.children[1].id = 'unit_end_sure'
      }, 50)
    }
  },
  beforeDestroy() {
    clearInterval(this.setTimer)
    this.setTimer = null
    sessionStorage.removeItem('scrollTop')
  }
}
</script>
<style lang="less" scoped>
.elDialogAdd1 {
  margin-top: 20vh !important;
}
.grayBox {
  height: 32px;
}
/deep/.el-dialog__body {
  min-height: 110px !important;
}
.dialog-body > div > div {
  color: #62666c;
  margin-bottom: 32px;
  .el-radio-group {
    font-size: 16px;
  }
  /deep/.el-radio {
    font-size: 16px;
    font-weight: bold;
    .el-radio__label {
      font-size: 16px;
    }
  }
}
.cardClass {
  height: 70%;
  margin-top: 2%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  img {
    display: block;
    width: 120px;
    height: 80px;
  }
  .cardWrap {
    height: 160px;
    width: 49%;
    margin-bottom: 15px;
    background: url('../../assets/images/fiveBg.png');
    background-size: cover;
    .el-card {
      position: relative;
      height: 100%;
      width: 100%;
      padding: 20px;
      box-sizing: border-box;
      background: transparent;
      .title {
        color: rgba(55, 57, 60, 1);
        font-weight: 500;
        font-size: 14px;
        margin-bottom: 12px;
      }
      .content {
        width: 68%;
        line-height: 24px;
        color: rgba(98, 102, 108, 1);
      }
      .imgBot {
        position: absolute;
        right: 0px;
        bottom: 4px;
      }
    }
  }
}
.el-radio-group {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.modelShow {
  color: #909399;
}
.el-table .cell {
  overflow: hidden !important;
  white-space: nowrap !important;
}
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.fourthBox {
  height: 100%;
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .fourthTitle {
    position: absolute;
    top: 20px;
    left: 0px;
  }
}
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.suspected {
  color: #f2b824;
  background: rgba(248, 193, 54, 0.16);
  border-radius: 2px;
  border: 1px solid #f8c136;
  padding: 2px 6px;
}
/deep/.el-icon-warning {
  color: #e94747;
  font-size: 16px;
  vertical-align: middle;
  margin-right: 2px;
}
.eltableBox {
  display: flex;
  padding: 0px 20px 0px 0px;
  // height: 100% !important;
  height: calc(100% - 56px);
  justify-content: space-between;
}
.ulBox {
  width: 160px;
  height: 100%;
  background: rgba(255, 255, 255, 0.36);
  border-right: 1px solid #e9ebef;
  box-sizing: border-box;
  padding-top: 5px;
  li {
    color: #62666c;
    cursor: pointer;
    height: 44px;
    background: rgba(234, 239, 246, 0);
    display: flex;
    align-items: center;
  }
  .ultitle {
    font-weight: 500;
    color: #37393c;
    padding-left: 16px;
  }
}
.fangkuai {
  width: 6px;
  height: 6px;
  background: #2677ff;
  box-shadow: 0px 0px 4px 0px rgba(38, 119, 255, 0.74);
  border-radius: 1px;
  margin: 0px 8px 0px 16px;
}
.fangkuai4 {
  background: #ec5f5c;
}
.fangkuai2 {
  background: #05d4a7;
}
.fangkuai1 {
  background: #13b7ff;
}
.fangkuai3 {
  background: #ec8f3c;
}
.fangkuai10 {
  background: #8a3cff;
}
.myTable {
  width: calc(100% - 170px);
  padding: 0px !important;
  height: calc(100% - 12px) !important;
}
.myTableContent {
  height: calc(100% - 46px);
  background-color: #fff;
}
/deep/.el-table {
  border: none !important;
}
.eltable_domain {
  border-left: 4px solid #2677ff !important;
  border-radius: 4px 0px 0px 4px;
}
.eltable_ip {
  border-left: 4px solid #1059d5 !important;
}
/deep/.boxTwo1 {
  height: 100% !important;
  // height: calc(100% - 154px) !important;
  padding-top: 2px !important;
}
/deep/.boxTwo {
  // height: calc(100% - 270px) !important;
  height: 100% !important;
  padding-top: 2px !important;
}
.eltable_icp {
  border-left: 4px solid #05d4a7 !important;
}
.eltable_cert {
  border-left: 4px solid #13b7ff !important;
}
.eltable_icon {
  border-left: 4px solid #ec8f3c !important;
}
.tableLabel {
  padding: 0px 0px 0px 4px !important;
  margin-bottom: 10px !important;
  margin-top: 10px !important;
}
.item {
      margin: 4px;
    }
</style>
