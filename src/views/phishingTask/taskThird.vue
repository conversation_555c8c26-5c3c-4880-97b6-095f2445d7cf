<template>
  <div class="taskBox">
    <taskHeader
      tuClassName="tuSecondPhishing"
      :companyName="taskInfoData.name"
      :isOuterProcess="isScan"
      :progress="currentPercent"
      :currentStep="taskInfoData.step"
    >
      <template #bottomText>
        <div v-if="!isScan" class="progressContent">
          <!-- <span><i class="el-icon-success"></i>数据存活性探测完成</span> -->
          <div class="progressContent">
            <img src="../../assets/images/taskSed.png" alt="" />
            <span>获取仿冒数据：</span>
            <span style="color: #2677ff">{{ totalTabNum }}</span
            >条
          </div>
        </div>
        <div v-else>
          {{ headText }}
        </div>
      </template>
      <template #buttonDiv>
        <el-button class="headerBtn" @click="endTask" id="unit_end" type="text">
          <svg class="icon svg-icon icon-close" aria-hidden="true">
            <use xlink:href="#icon-close"></use>
          </svg>
          结束流程
        </el-button>
      </template>
    </taskHeader>
    <div class="myBox" v-if="!isScan">
      <div class="boxTwo1" v-loading="loading">
        <div class="tableLabel">
          <div>
            <div style="margin-right: 10px">仿冒数据</div>
            <!-- <div class="confirmBox">
                <el-radio-group v-model="assetType" @change="changeTab(assetType, 'isTab', 'yesLoading')">
                  <el-radio-button :label="item.level" v-for="(item,index) in tabList" :key="index">{{item.name}}({{tabNum[item.icon] ? tabNum[item.icon] : 0}})</el-radio-button>
                </el-radio-group>
              </div> -->
            <el-input
              v-if="taskInfoData.step == 4"
              v-model="formInline.keyword"
              placeholder="请输入关键字检索"
              @keyup.enter.native="highCheck"
            >
              <el-button slot="append" icon="el-icon-search" @click="highCheck"></el-button>
              <el-tooltip
                slot="prepend"
                class="item"
                effect="dark"
                content="支持检索字段：IP地址、仿冒目标、域名、域名备案企业、URL、网站标题"
                placement="top"
                :open-delay="100"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-input>
            <span v-if="taskInfoData.step == 4" @click="highCheckClick"
              ><img
                src="../../assets/images/filter.png"
                alt=""
                style="width: 16px; vertical-align: middle; margin-right: 3px"
              />高级筛选</span
            >
          </div>
          <div>
            <el-checkbox
              class="checkboxAll"
              v-if="taskInfoData.step == 4 || taskInfoData.step == 5"
              v-model="checkedAllArr[assetType]"
              :disabled="isAccessChecked"
              @change="checkAllChange"
              id="keyword_all"
              >选择全部</el-checkbox
            >
            <el-checkbox
              class="checkboxAll"
              v-if="taskInfoData.step == 4 || taskInfoData.step == 5"
              v-model="isAccessChecked"
              :disabled="checkedAllArr[assetType]"
              @change="checkAllChange"
              id="keyword_all"
              >选择全部可访问资产</el-checkbox
            >
            <!-- <el-button class="normalBtn" type="primary" v-if="taskInfoData.step == 4" @click="syncThreatAssets(0)">入账威胁资产</el-button> -->
            <!-- <el-button class="normalBtn" type="primary" v-if="taskInfoData.step == 4" @click="oneKeySyncThreatAssets">可访问资产一键入账威胁</el-button> -->
            <el-button
              class="normalBtn"
              type="primary"
              v-if="taskInfoData.step == 4 || taskInfoData.step == 5"
              :loading="retryLoading"
              @click="reGetPic"
              >截图重试</el-button
            >
            <el-button
              class="normalBtnRe"
              type="primary"
              v-if="taskInfoData.step == 3"
              @click="uploadOtherPlate"
              >数据导入</el-button
            >
          </div>
        </div>
        <div class="myTable">
          <!-- 1-仿冒，2-域名混淆，3-其他 -->
          <tableList
            ref="tableList"
            :tableData="tableData"
            pageIcon="third"
            :assetType="assetType"
            :isAccessChecked="isAccessChecked"
            :taskInfoData="taskInfoData"
            :checkedAll="checkedAllArr"
            @handleSelectionChange="handleSelectionChange"
          />
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="pageSizeArr"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
        <div class="footer">
          <el-button
            class="normalBtn"
            type="primary"
            v-if="taskInfoData.step == 4 || taskInfoData.step == 5"
            @click="syncThreatAssets(0)"
            >入账威胁资产</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            v-if="
              taskInfoData.step == 4 &&
              taskInfoData.detect_type == 1 &&
              taskInfoData.step_detail != 402
            "
            :loading="scanBtnLoading"
            @click="syncAssetsScan"
            >入账威胁并深度探测</el-button
          >
          <!-- <el-button class="normalBtn" type="primary" v-if="taskInfoData.step == 4" @click="oneKeySyncThreatAssets">可访问资产一键入账威胁</el-button> -->
          <el-button
            class="normalBtn"
            type="primary"
            v-if="taskInfoData.step == 3"
            @click="goDetect"
            id="unit_predict"
            :disabled="totalTabNum == 0"
            >数据存活性探测</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            v-if="taskInfoData.step == 4 || taskInfoData.step == 5"
            @click="syncThreatAssets(1)"
            id="unit_predict"
            >完成</el-button
          >
        </div>
      </div>
      <highCheckDrawerScan
        :highCheckdialog="highCheckdialog"
        :selectArr="cluesList"
        :formInline="formInline"
        :stateData="stateData"
        @highCheck="highCheck"
        @highIsClose="highIsClose"
      />
      <el-dialog
        class="elDialogAdd"
        :close-on-click-modal="false"
        :visible.sync="dialogFormVisible"
        width="500px"
      >
        <template slot="title"> 数据导入 </template>
        <div class="dialog-body">
          <p class="downloadClass">
            <i class="el-icon-warning"></i>请点击下载
            <span @click="downloadOther('钓鱼仿冒三方数据导入模板.xlsx')"
              >钓鱼仿冒三方数据导入模板.xlsx</span
            >
          </p>
          <el-upload
            class="upload-demo"
            drag
            :action="uploadAction"
            :headers="uploadHeaders"
            accept=".xlsx"
            :before-upload="beforeIpUpload"
            :on-success="ipUploadSuccess"
            :on-remove="uploadRemove"
            :on-error="ipUploadError"
            :file-list="fileList"
            :on-exceed="handleExceedHd"
            :limit="1"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过20M</div>
          </el-upload>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button class="highBtnRe" @click="dialogFormVisible = false" id="unit_export_cancel"
            >关闭</el-button
          >
          <el-button
            :loading="otherLoading"
            class="highBtn"
            @click="otherDataSave"
            id="unit_export_save"
            >确定</el-button
          >
        </div>
      </el-dialog>
      <el-dialog
        title="提示"
        :close-on-click-modal="false"
        :visible.sync="dialogFormVisibleTip"
        width="500px"
      >
        <div style="margin-bottom: 20px">以下IP已经存在于台账中，点击复制去IP台账页面确认</div>
        <div style="cursor: pointer" v-html="IPList" @click="copy"></div>
        <div slot="footer" class="dialog-footer">
          <el-button class="highBtnRe" @click="dialogFormVisibleTip = false" id="keyword_add_cancel"
            >关闭</el-button
          >
        </div>
      </el-dialog>
    </div>
    <div v-else class="fourthBox1">
      <div class="loadingBox" v-if="taskInfoData.step == 4">
        <div class="dongtu6"></div>
      </div>
      <div class="loadingBox" v-else-if="taskInfoData.step == 5">
        <div class="dongtu7"></div>
      </div>
      <div class="loadingBox loadingBox2" v-else>
        <div class="loadingTu"></div>
      </div>
      <div v-if="taskInfoData.step == 4">正在进行数据存活性探测…</div>
      <div v-else-if="taskInfoData.step == 5">正在入账威胁资产，并进行深度探测…</div>
      <div v-else>正在进行仿冒数据发现…</div>
    </div>
  </div>
</template>
<script>
import taskLoading from '../../views/unit_surveying/taskLoading.vue'

import taskHeader from '../../views/unit_surveying/taskHeader.vue'
import * as animationData from '../../assets/images/data.json'
import Lottie from 'vue-lottie/src/lottie.vue'
import tableList from './fakeTableList.vue'
import highCheckDrawerScan from '../home_set/highCheck.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { regetScreenshot } from '@/api/apiConfig/api.js'
import {
  fakeDataSyncthreatDetect,
  fakeTabCount,
  fakeImportSave,
  fakeDataSyncthreat,
  fakeDataDetect,
  fakeDataFindList,
  fakeCondition,
  fakeTaskInfo,
  fakeEndDetectTask
} from '@/api/apiConfig/phishing.js'

export default {
  components: { Lottie, tableList, highCheckDrawerScan, taskHeader, taskLoading },
  props: ['taskInfoData', 'expand_source', 'expandType'],
  data() {
    return {
      isAccessChecked: false,
      isLoadingBgc: true,
      taskLoading: false,
      dialogFormVisibleTip: false,
      IPList: '',
      headText: '仿冒数据发现中',
      assetType: 1,
      highCheckdialog: false,
      cluesList: {},
      stateData: [
        { name: '可访问', value: 'true' },
        { name: '不可访问', value: 'false' }
      ],
      isScan: false,
      hunterLoading: false,
      loading: false,
      currentPercent: 0,
      defaultOptions: {
        //json动画
        animationData: animationData.default
      },
      formInline: {
        ip: '',
        name: [],
        keyword: '', // 123123,
        url: '',
        title: '', // title,
        protocol: [], // protocol,
        logo: [], // logo,
        port: [], // port,
        subdomain: '',
        domain: [], // domain
        icp_company: [],
        chain_type: '', // 证据链
        created_at: [],
        updated_at: []
      },
      tabNum: {
        domain_total: 0,
        fake_total: 0
      },
      totalTabNum: 0,
      tabList: [
        {
          level: 1,
          name: '钓鱼仿冒',
          icon: 'fake_total'
        },
        {
          level: 2,
          name: '域名混淆',
          icon: 'domain_total'
        }
      ],
      uploadPath: '',
      uploadAction: `${this.golangUploadSrcIp}/public/upload`,
      uploadHeaders: { Authorization: localStorage.getItem('token') },
      uploadMaxCount: 1,
      fileList: [],
      otherLoading: false,
      dialogFormVisible: false,
      hunterData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      tableData: [],
      checkedArr: [],
      checkedAll: false,
      checkedAllArr: [],
      setTimer: null,
      retryLoading: false,
      scanBtnLoading: false,
      userInfo: {
        user: ''
      },
      user: {}
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  watch: {
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    taskInfoData(val) {
      this.getInit()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage', 'getterRecommentFlag', 'getterCurrentCompany'])
  },
  beforeDestroy() {
    clearInterval(this.setTimer)
    this.setTimer = null
  },
  mounted() {
    for (let index = 0; index < this.tabList.length; index++) {
      const element = this.tabList[index].level
      this.checkedAllArr[element] = false
    }
    if (this.user.role == 2 && !this.currentCompany) return
    this.getInit()
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    handleExceedHd() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    // 重新获取截图
    reGetPic() {
      if (this.tableData.length == 0) {
        this.$message.error('暂无可重试数据！')
        return
      }
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      this.retryLoading = true
      regetScreenshot({
        task_id: this.taskInfoData.id,
        ids:
          this.checkedAll || this.isAccessChecked
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
        type: 3, // 1登录入口，2数据泄露，3仿冒资产
        operate_company_id: this.currentCompany
      })
        .then((res) => {
          this.retryLoading = false
          if (res.code == 0) {
            this.$message.success('截图重试成功')
            this.checkedAllArr[this.assetType] = false
            this.isAccessChecked = false
            this.$refs.tableList.$refs.eltable.clearSelection()
            this.getfakeAssets('yesLoading')
          }
        })
        .catch(() => {
          this.retryLoading = false
        })
    },
    async syncAssetsScan() {
      if (
        !this.isAccessChecked &&
        !this.checkedAllArr[this.assetType] &&
        this.checkedArr.length == 0
      ) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      this.scanBtnLoading = true
      let obj = {
        operate_company_id: this.currentCompany,
        task_id: this.taskInfoData.id,
        ids:
          this.checkedAllArr[this.assetType] || this.isAccessChecked
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
        asset_type: this.assetType,
        online_state: String(this.isAccessChecked), // 是否仅同步在线资产
        is_finished: false, // 是否完成任务
        ...this.formInline
      }
      let res = await fakeDataSyncthreatDetect(obj).catch(() => {
        this.scanBtnLoading = false
      })
      this.scanBtnLoading = false
      if (res.code == 0) {
        if (res.data.ip) {
          this.repeatIPNotify(res.data)
        } else {
          this.$message.success('操作成功！')
        }
        this.checkedAllArr[this.assetType] = false
        this.isAccessChecked = false
        this.$refs.tableList.$refs.eltable.clearSelection()
        this.$emit('son', '4', this.taskInfoData.id) // 执行第四步
      }
    },
    // 关键字检索
    highCheck() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.getfakeAssets('yesLoading')
    },
    highIsClose() {
      this.highCheckdialog = false
    },
    highCheckClick() {
      this.highCheckdialog = true
      this.getConditionList()
    },
    // 高级筛选条件
    async getConditionList() {
      let res = await fakeCondition({
        task_id: this.taskInfoData.id,
        asset_type: this.assetType, // 1-仿冒，2-域名混淆，3-其他
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.cluesList = res.data
      }
    },
    getInit() {
      // 300|0未完成推荐显示进度，300|1已完成推荐不显示进度
      if (this.taskInfoData) {
        if (this.taskInfoData.step_detail == 300) {
          this.currentPercent =
            this.taskInfoData.step_status == 0 ? this.taskInfoData.expend_progress / 1 : 100
          if (this.taskInfoData.step_status == 0) {
            // 正在发现
            this.headText = '正在进行仿冒数据发现…'
            this.isScan = true
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTaskInfo()
            }, 5000)
          } else {
            this.currentPercent = 100
            clearInterval(this.setTimer)
            this.setTimer = null
            this.headText = '仿冒数据发现'
            this.isScan = false
            this.getfakeAssets('yesLoading')
          }
        } else if (this.taskInfoData.step_detail == 301) {
          this.currentPercent =
            this.taskInfoData.step_status == 0 ? this.taskInfoData.expend_progress / 1 : 100
          if (this.taskInfoData.step_status == 0) {
            // 正在导入
            this.headText = '正在导入数据…'
            this.isScan = true
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTaskInfo()
            }, 5000)
          } else {
            // step_status：1/2  导入成功/导入失败
            this.headText = '仿冒数据发现'
            this.isScan = false
            this.getfakeAssets('yesLoading')
          }
        } else if (this.taskInfoData.step_detail == 400) {
          this.currentPercent =
            this.taskInfoData.step_status == 0 ? this.taskInfoData.progress / 1 : 100
          if (this.taskInfoData.step_status == 0) {
            // 存活性探测
            this.headText = '正在进行数据存活性探测…'
            this.isScan = true
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTaskInfo()
            }, 5000)
          } else {
            // step_status：1/2  导入成功/导入失败
            this.headText = '数据存活性检测'
            this.isScan = false
            this.getfakeAssets('yesLoading')
          }
        } else if (this.taskInfoData.step_detail == 401) {
          this.taskInfoData.step = 5
          this.currentPercent =
            this.taskInfoData.step_status == 0 ? this.taskInfoData.progress / 1 : 100
          if (this.taskInfoData.step_status == 0) {
            // 存活性探测
            this.headText = '正在入账威胁资产，并进行深度探测...'
            this.isScan = true
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTaskInfo()
            }, 5000)
          } else {
            // step_status：1/2  导入成功/导入失败
            this.headText = '深度探测'
            this.isScan = false
            this.getfakeAssets('yesLoading')
          }
        } else if (this.taskInfoData.step_detail == 402) {
          this.currentPercent =
            this.taskInfoData.step_status == 0 ? this.taskInfoData.progress / 1 : 100
          if (this.taskInfoData.step_status == 0) {
            // 存活性探测
            this.headText = '正在进行数据存活性探测…'
            this.isScan = true
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTaskInfo()
            }, 5000)
          } else {
            // step_status：1/2  导入成功/导入失败
            this.headText = '数据存活性检测'
            this.isScan = false
            this.getfakeAssets('yesLoading')
          }
          this.taskInfoData.step = 5
        } else {
          this.headText = '数据存活性检测'
          this.isScan = false
          this.getfakeAssets('yesLoading')
        }
      } else {
        this.isScan = false
        this.getfakeAssets('yesLoading')
      }
    },
    async getTaskInfo() {
      // 获取任务详情数据，任务记录id存在，查询对应的详情数据；不存在后端直接查询未完成的任务数据，有返回值则展示，没有返回值新建流程
      let id = this.taskInfoData.id ? this.taskInfoData.id : ''
      let obj = {
        taskId: id,
        data: {
          operate_company_id: this.currentCompany
        }
      }
      this.loading = true
      let res = await fakeTaskInfo(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        if (res.data.step_status == 1) {
          this.currentPercent = 100 // 仿冒数据发现的进度
          clearInterval(this.setTimer)
          this.setTimer = null
          this.isScan = false
          this.$emit('son', '3', this.taskInfoData.id)
        } else {
          if (
            this.taskInfoData.step_detail == 400 ||
            this.taskInfoData.step_detail == 401 ||
            this.taskInfoData.step_detail == 402
          ) {
            this.currentPercent = res.data.progress / 1 // 探活的进度
          } else {
            this.currentPercent = res.data.expend_progress / 1 // 仿冒数据发现的进度
          }
        }
      }
    },
    async endTask() {
      this.$confirm('确定结束流程?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            fake_detect_task_id: this.taskInfoData.id,
            operate_company_id: this.currentCompany
          }
          let res = await fakeEndDetectTask(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.$emit('son', '1')
          }
        })
        .catch(() => {})
    },
    handleMessage(res, o) {
      //处理接收到的信息
    },
    async getfakeAssets(isLoading) {
      // 获取仿冒数据列表
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        task_id: this.taskInfoData.id,
        asset_type: this.assetType, // 1-仿冒，2-域名混淆，3-其他
        operate_company_id: this.currentCompany,
        ...this.formInline
      }
      this.loading = isLoading == 'yesLoading' ? true : false
      let res = await fakeDataFindList(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.tableData = res.data.items ? res.data.items : []
        this.total = res.data.total ? res.data.total : 0
      } else {
        this.loading = false
      }
      console.log('tableData', this.tableData);
      
      // 获取tab页数量
      let res1 = await fakeTabCount({
        task_id: this.taskInfoData.id,
        asset_type: this.assetType, // 1-仿冒，2-域名混淆，3-其他
        operate_company_id: this.currentCompany
      })
      if (res1.code == 0) {
        this.tabNum.domain_total = res1.data && res1.data.domain_total ? res1.data.domain_total : 0
        this.tabNum.fake_total = res1.data && res1.data.fake_total ? res1.data.fake_total : 0
        this.totalTabNum = this.tabNum.domain_total + this.tabNum.fake_total
      }
    },
    // 切换类型
    async changeTab(val, isTab, isLoading) {
      if (isTab) {
        // tab切换要把分页重置
        this.currentPage = 1
      }
      this.getfakeAssets(isLoading)
    },
    // 全选
    checkAllChange(val, type) {
      this.checkedAll = val
    },
    // 一键入账威胁
    oneKeySyncThreatAssets() {
      if (this.tableData.length == 0) {
        this.$message.error('暂无可入账威胁数据！')
        return
      }
      this.$confirm('确定将该类型所有可访问数据直接入账到威胁资产?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'account_del_cancel',
        confirmButtonClass: 'account_del_sure',
        customClass: 'account_del',
        type: 'warning'
      }).then(async () => {
        let obj = {
          operate_company_id: this.currentCompany,
          task_id: this.taskInfoData.id / 1,
          ids: [],
          asset_type: this.assetType / 1,
          online_state: 'true', // 是否仅同步在线资产
          is_finished: false // 是否完成任务
        }
        this.loading = true
        let res = await fakeDataSyncthreat(obj).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          this.loading = false
          this.$refs.tableList.$refs.eltable.clearSelection()
          this.$message.success('操作成功！')
          this.getfakeAssets(true)
        } else {
          this.loading = false
        }
      })
    },
    // 入账威胁或者完成
    async syncThreatAssets(icon) {
      if (icon == 0) {
        // 非完成需要校验有数据
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要操作的数据！')
          return
        }
        let obj = {
          operate_company_id: this.currentCompany,
          task_id: this.taskInfoData.id,
          ids: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          asset_type: this.assetType,
          online_state: String(this.isAccessChecked), // 是否仅同步在线资产
          is_finished: false, // 是否完成任务
          ...this.formInline
        }
        this.loading = true
        let res = await fakeDataSyncthreat(obj).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          // this.$message.success('操作成功！')
          this.loading = false
          this.checkedAll = false
          if (res.data.ip) {
            // res.data.ip = ['12324354','3424326456','gdfgdfgdf']
            this.repeatIPNotify(res.data)
          } else {
            this.$message.success('操作成功！')
          }

          this.getfakeAssets(true)
          this.checkedAllArr[this.assetType] = false
          this.isAccessChecked = false
          this.$refs.tableList.$refs.eltable.clearSelection()

          this.$confirm('已成功提取到威胁资产，是否结束此任务？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.syncThreatAssets(1)
            })
            .catch(() => {})
        } else {
          this.loading = false
        }
      } else {
        let obj = {
          operate_company_id: this.currentCompany,
          task_id: this.taskInfoData.id,
          ids: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          asset_type: this.assetType,
          online_state: 'false', // 是否仅同步在线资产
          is_finished: true // 是否完成任务
        }
        let res = await fakeDataSyncthreat(obj)
        if (res.code == 0) {
          this.$message.success('操作成功！')
          this.$emit('son', '1')
          this.getfakeAssets(true)
        }
      }
    },
    repeatIPNotify(data) {
      this.IPList = data.ip.join('\n')
      const h = this.$createElement
      let htmlStr = []
      data.ip.forEach((ip) => {
        htmlStr.push(
          h(
            'p',
            {
              style: 'width: 250px;display: flex;justify-content: space-between;'
            },
            [
              h(
                'span',
                {
                  style: 'color: #409EFF;cursor: pointer;',
                  on: {
                    click: this.copy
                  }
                },
                ip
              )
            ]
          )
        )
      })
      this.$notify({
        title: '以下IP已经存在于台账中，点击复制去IP台账页面确认',
        message: h('div', {}, htmlStr),
        type: 'warning',
        duration: 10000,
        offset: 64,
        customClass: 'custom-class'
      })
    },
    // 数据导入
    uploadOtherPlate() {
      this.dialogFormVisible = true
      this.fileList = []
      this.uploadPath = ''
    },
    // 数据导入保存
    async otherDataSave() {
      if (!this.uploadPath) {
        this.$message.error('请上传文件')
        return
      }
      this.otherLoading = true
      let res = await fakeImportSave({
        file_path: this.uploadPath,
        task_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.otherLoading = false
      })
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.loading = true
        setTimeout(() => {
          // 解决导入后列表不能及时更新的问题
          this.dialogFormVisible = false
          this.otherLoading = false
          this.getfakeAssets('yesLoading')
        }, 1000)
      } else {
        this.otherLoading = false
      }
    },
    // 三方导入模板下载
    downloadOther(name) {
      window.location.href = `/downloadTemplate/${name}`
    },
    beforeIpUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过 20MB!')
      }
      return isLt1M
    },
    ipUploadSuccess(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.uploadPath =
          file.response && file.response.data && file.response.data.path
            ? file.response.data.path
            : ''
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadRemove(file, fileList) {
      this.uploadPath = ''
    },
    ipUploadError(err, file, fileList) {
      this.$message.error(JSON.parse(err.message).message)
      this.otherLoading = false
    },
    removeOne() {},
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getfakeAssets('yesLoading')
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getfakeAssets('yesLoading')
    },
    // 数据探活
    async goDetect() {
      this.loading = true
      let obj = {
        task_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      let res = await fakeDataDetect(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.taskInfoData.step = 4
        this.$emit('son', '4', this.taskInfoData.id) // 执行第四步
      }
    },
    copy(text) {
      const _that = this
      _that.$copyText(this.IPList).then(
        function (res) {
          console.log('复制成功：', res)
          _that.$message.success('复制成功')
        },
        function (err) {
          console.log('', err)
          _that.$message.error('复制失败')
        }
      )
    }
  }
}
</script>
<style lang="less" scoped>
.hunterClass {
  position: relative;
  padding: 0 0 0 10px;
  line-height: 30px;
  .el-icon-document-copy {
    position: absolute;
    top: 8px;
    left: -10px;
    color: #2677ff;
    cursor: pointer;
  }
}
.loadingBox {
  height: 260px;
}
.loadingTu {
  width: 400px !important;
  height: 260px !important;
  background-repeat: no-repeat;
  background-size: cover;
  background-position-y: 0;
  background-image: url('../../assets/images/loadingAnimation.png');
  animation: running2 8s steps(149) infinite;
  -webkit-animation: running2 8s steps(149) infinite;
}

.dongtu6 {
  width: 240px;
  height: 240px;
  background-image: url('../../assets/images/dongtu6.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position-y: 0;
  animation: running4 6s steps(149) infinite;
  -webkit-animation: running4 6s steps(149) infinite;
}
@keyframes running4 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -35760px;
  }
}
.dongtu7 {
  width: 440px;
  height: 260px;
  background-image: url('../../assets/images/dongtu7.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position-y: 0;
  animation: running2 6s steps(149) infinite;
  -webkit-animation: running2 6s steps(149) infinite;
}

@keyframes running2 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -38740px;
  }
}
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.fourthBox1 {
  height: 100%;
  // background: -webkit-linear-gradient(180deg, rgba(240,246,255,0.7) 0%, #FFFFFF 100%) !important;
  // background: -o-linear-gradient(180deg, rgba(240,246,255,0.7) 0%, #FFFFFF 100%) !important;
  // background: -moz-linear-gradient(180deg, rgba(240,246,255,0.7) 0%, #FFFFFF 100%) !important;
  // background: linear-gradient(180deg, rgba(240,246,255,0.7) 0%, #FFFFFF 100%) !important;

  background: url('../../assets/images/loadingBgc2.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .loadingBox {
    overflow: hidden;
  }
  .loadingBox1 {
    width: 240px;
    height: 240px;
  }
  .loadingBox2 {
    width: 400px !important;
    height: 260px !important;
    transform: translateX(-20px);
  }
  .fourthTitle {
    position: absolute;
    top: 20px;
    left: 0px;
  }
}
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.boxTwo {
  height: calc(100% - 208px) !important;
}
</style>
