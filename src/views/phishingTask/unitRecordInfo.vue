<template>
  <div class="container">
    <div class="headerTitle">
      <span>
        <span class="goback" @click="goback"><i class="el-icon-arrow-left"></i>返回上一层</span>
        <span class="spline">/</span>
        <span>任务记录</span>
      </span>
    </div>
    <div class="box">
      <ul class="recommendlLeft" style="width: 20%">
        <div class="recommendTitle">任务参数</div>
        <div class="recommendBox">
          <li v-for="item in recommendTitle" :key="item.name">
            <p class="rightTitle">{{ item.label }}：</p>
            <span class="rightContent">
              <span v-if="item.name == 'op'">{{ recommendData[item.name].name }}</span>
              <span class="clueClass" v-else-if="item.name == 'clue'">
                <span v-for="ch in recommendData[item.name]" :key="ch.id">
                  <span v-if="ch.type != 3 && ch.content">{{ ch.content }}</span>
                  <el-image
                    v-else
                    :src="ch.content.includes('http') ? ch.content : showSrcIp + ch.content"
                    class="myImg"
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </span>
              </span>
              <span v-else-if="item.name == 'use_seconds'">
                <span>{{ secondsFormat(recommendData[item.name]) }}</span>
              </span>
              <span v-else>
                <span>{{ recommendData[item.name] }}</span>
              </span>
            </span>
          </li>
        </div>
      </ul>
      <div class="home_header" style="width: 79%">
        <div>
          <div>
            <!-- <span class="taskResults">任务发现结果</span> -->
            <!-- <el-tabs v-model="assetType" @tab-click="handleClick" style="margin-left:105px;margin-right: 16px;">
              <el-tab-pane label="钓鱼仿冒" name="1">
              </el-tab-pane>
              <el-tab-pane label="域名混淆" name="2">
              </el-tab-pane>
            </el-tabs> -->
          </div>
        </div>
        <div style="height: 93%" v-loading="loading">
          <div class="filterWrap">
            <div>
              <el-input
                v-if="taskInfoData.step == 4"
                v-model="formInline.keyword"
                placeholder="请输入关键字检索"
                @keyup.enter.native="checkFuncList"
              >
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
                <el-tooltip
                  slot="prepend"
                  class="item"
                  effect="dark"
                  content="支持检索字段：IP地址、仿冒目标、域名、域名备案企业、URL、网站标题"
                  placement="top"
                  :open-delay="100"
                >
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </el-input>
              <span v-if="taskInfoData.step == 4" @click="highCheckClick"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <div>
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAllArr[assetType]"
                @change="checkAllChange"
                id="keyword_all"
                >选择全部</el-checkbox
              >
              <el-button class="normalBtn" type="primary" @click="syncThreatAssets(0)"
                >入账威胁资产</el-button
              >
              <el-button class="normalBtn" type="primary" @click="oneKeySyncThreatAssets"
                >可访问资产一键入账威胁</el-button
              >
            </div>
          </div>
          <!-- 高级筛选条件 -->
          <hightFilter
            id="hightFilter"
            :highTabShow="highTabShow"
            :highlist="highlist"
            :total="total"
            @highcheck="highCheck"
          ></hightFilter>
          <div class="bot">
            <tableList
              style="height: 100%"
              ref="tableList"
              :assetType="assetType"
              :tableData="tableData"
              pageIcon="third"
              :taskInfoData="taskInfoData"
              :checkedAll="checkedAllArr"
              @handleSelectionChange="handleSelectionChange"
            />
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="pageSizeArr"
              :page-size="pageSize"
              layout="total, prev, pager, next, sizes, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="提示"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleTip"
      width="500px"
    >
      <div style="margin-bottom: 20px">以下IP已经存在于台账中，点击复制去IP台账页面确认</div>
      <div style="cursor: pointer" v-html="IPList" @click="copy"></div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleTip = false" id="keyword_add_cancel"
          >关闭</el-button
        >
      </div>
    </el-dialog>
    <highCheckDrawerScan
      :highCheckdialog="highCheckdialog"
      :stateData="stateData"
      :selectArr="cluesList"
      :formInline="formInline"
      @highCheck="checkFuncList"
      @highIsClose="highIsClose"
    />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import highCheckDrawerScan from '../home_set/highCheck.vue'
import hightFilter from '../../components/assets/highTab.vue'
import tableList from './fakeTableList.vue'
import { recommandScanTaskDetail } from '@/api/apiConfig/asset.js'
import {
  fakeTabCount,
  fakeDataSyncthreat,
  fakeDataFindList,
  fakeCondition
} from '@/api/apiConfig/phishing.js'

export default {
  components: { tableList, highCheckDrawerScan, hightFilter },
  data() {
    return {
      IPList: '',
      dialogFormVisibleTip: false,
      highTabShow: [
        {
          label: 'IP地址',
          name: 'ip',
          type: 'input'
        },
        {
          label: '仿冒目标',
          name: 'name',
          type: 'select'
        },
        {
          label: '端口',
          name: 'port',
          type: 'select'
        },
        {
          label: '协议',
          name: 'protocol',
          type: 'select'
        },
        {
          label: '域名',
          name: 'domain',
          type: 'select'
        },
        {
          label: '子域名',
          name: 'subdomain',
          type: 'input'
        },
        {
          label: '域名备案企业',
          name: 'icp_company',
          type: 'select'
        },
        {
          label: 'URL',
          name: 'url',
          type: 'select'
        },
        {
          label: '网站标题',
          name: 'title',
          type: 'select'
        },
        {
          label: 'ICON',
          name: 'icon_content',
          type: 'select'
        },
        {
          label: '首次发现时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          type: 'date'
        },
        {
          label: '证据链',
          name: 'chain_list',
          type: 'select'
        }
      ],
      highlist: null,
      highCheckdialog: false,
      cluesList: {},
      stateData: [
        { name: '可访问', value: 'true' },
        { name: '不可访问', value: 'false' }
      ],
      loading: false,
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      tableData: [],
      checkedAll: false,
      checkedAll1: false,
      checkedAll2: false,
      assetType: '1',
      recommendData: {
        //扫描资产核对任务参数
        name: '',
        clue: '',
        start_at: '',
        end_at: '',
        use_seconds: '',
        op: ''
      },
      formInline: {
        ip: '',
        name: [],
        keyword: '', // 123123,
        url: '',
        title: '', // title,
        protocol: [], // protocol,
        logo: [], // logo,
        port: [], // port,
        subdomain: '',
        domain: [], // domain
        icp_company: [],
        chain_type: '', // 证据链
        created_at: [],
        updated_at: []
      },
      recommendTitle: [
        // {
        //   label: '任务参数',
        //   name: ''
        // },
        {
          label: '任务名称',
          name: 'name'
        },
        {
          label: '开始时间',
          name: 'start_at'
        },
        {
          label: '结束时间',
          name: 'end_at'
        },
        {
          label: '任务耗时',
          name: 'use_seconds'
        },
        {
          label: '发起人',
          name: 'op'
        },
        {
          label: '推荐线索',
          name: 'clue'
        }
      ],
      taskInfoData: {
        step: 4
      },
      tabNum: {
        domain_total: 0,
        fake_total: 0
      },
      checkedArr: [],
      checkedAllArr: ['', false, false]
    }
  },
  watch: {
    getterCurrentCompany(val) {
      // 安服账号刷新页面加载
      this.currentPage = 1
      // 切换账号去除全选
      // this.checkedAll = false
      this.checkedAllArr = [false, false]
      if (this.user.role == 2) {
        this.$router.push({
          path: '/phishingTask',
          query: {
            isrecordBack: 1
          }
        })
      }
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  async mounted() {
    this.getfakeAssets('yesLoading')
    this.getDetails()
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
  },
  methods: {
    goback() {
      this.$router.push({
        path: '/phishingTask',
        query: {
          isrecordBack: 1
        }
      })
    },
    // 关键字检索
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      // this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.getfakeAssets('yesLoading')
    },
    highCheck(val) {
      this.currentPage = 1
      this.formInline = Object.assign(this.highlist)
      this.getfakeAssets('yesLoading')
    },
    highIsClose() {
      this.highCheckdialog = false
    },
    highCheckClick() {
      this.highCheckdialog = true
      this.getConditionList()
    },
    // 高级筛选条件
    async getConditionList() {
      let res = await fakeCondition({
        task_id: this.$route.query.id / 1,
        asset_type: this.assetType, // 1-仿冒，2-域名混淆，3-其他
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.cluesList = res.data
      }
    },
    // 任务参数
    async getDetails() {
      let obj = {
        id: this.$route.query.flag,
        operate_company_id: this.currentCompany
      }
      let res = await recommandScanTaskDetail(obj).catch(() => {
        this.loading = false
      })
      this.recommendData = res.data
    },
    async getfakeAssets(isLoading) {
      // 获取仿冒数据列表
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        task_id: this.$route.query.id / 1,
        asset_type: this.assetType / 1, // 1-仿冒，2-域名混淆，3-其他
        operate_company_id: this.currentCompany,
        ...this.formInline
      }
      this.loading = isLoading == 'yesLoading' ? true : false
      let res = await fakeDataFindList(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.tableData = res.data.items ? res.data.items : []
        this.total = res.data.total ? res.data.total : 0
      } else {
        this.loading = false
      }
      let res1 = await fakeTabCount({
        task_id: this.$route.query.id,
        asset_type: this.assetType / 1, // 1-仿冒，2-域名混淆，3-其他
        operate_company_id: this.currentCompany
      })
      if (res1.code == 0) {
        this.tabNum.domain_total = res1.data && res1.data.domain_total ? res1.data.domain_total : 0
        this.tabNum.fake_total = res1.data && res1.data.fake_total ? res1.data.fake_total : 0
      }
    },
    handleClick() {
      this.checkedAll = false
      this.currentPage = 1
      this.getfakeAssets('yesLoading')
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getfakeAssets('yesLoading')
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getfakeAssets('yesLoading')
    },
    // 全选
    checkAllChange(val) {
      this.checkedAll = val
      // if (this.checkedAll) {
      //       this.tableData.forEach(row => {
      //         // this.$refs.tableList.checkAllOnlineFunc()
      //         // this.$refs.tableList.checkAllOnlineFunc()
      //       });
      //     } else {
      //       this.$refs.tableList.$refs.eltable.clearSelection();
      //     }
    },
    // 一键入账威胁
    oneKeySyncThreatAssets() {
      if (this.tableData.length == 0) {
        this.$message.error('暂无可入账威胁数据！')
        return
      }
      this.$confirm('确定将该类型所有可访问数据直接入账到威胁资产?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'account_del_cancel',
        confirmButtonClass: 'account_del_sure',
        customClass: 'account_del',
        type: 'warning'
      }).then(async () => {
        let obj = {
          operate_company_id: this.currentCompany,
          task_id: this.$route.query.id / 1,
          ids: [],
          asset_type: this.assetType / 1,
          online_state: 'true', // 是否仅同步在线资产
          is_finished: false // 是否完成任务
        }
        this.loading = true
        let res = await fakeDataSyncthreat(obj).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          this.loading = false
          this.$message.success('操作成功！')
          this.getfakeAssets(true)
        } else {
          this.loading = false
        }
      })
    },
    // 入账威胁或者完成
    async syncThreatAssets(icon) {
      if (icon == 0) {
        // 非完成需要校验有数据
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要操作的数据！')
          return
        }
        let obj = {
          operate_company_id: this.currentCompany,
          task_id: this.$route.query.id / 1,
          ids: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          asset_type: this.assetType / 1,
          online_state: 'false', // 是否仅同步在线资产
          is_finished: false, // 是否完成任务
          ...this.formInline
        }
        this.loading = true
        let res = await fakeDataSyncthreat(obj).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          this.loading = false
          if (res.data.ip) {
            this.dialogFormVisibleTip = true
            this.IPList = res.data.ip.join('\n')
          } else {
            this.checkedAllArr = ['', false, false]
            this.$refs.tableList.$refs.eltable.clearSelection()
            this.$message.success('操作成功！')
          }
          this.getfakeAssets(true)
        } else {
          this.loading = false
        }
      }
    },
    copy(text) {
      const _that = this
      _that.$copyText(this.IPList).then(
        function (res) {
          console.log('复制成功：', res)
          _that.$message.success('复制成功')
        },
        function (err) {
          console.log('', err)
          _that.$message.error('复制失败')
        }
      )
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  /deep/.home_header {
    width: 100%;
    height: 100%;
    background: #fff;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      padding: 0 16px;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
      background: #fff;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      // padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        width: 32.5%;
        height: 140px;
        padding: 36px 26px;
        background: #fff;
        p {
          b {
            display: block;
            font-size: 28px;
            font-weight: 500;
            color: #37393c;
            line-height: 40px;
          }
          span {
            color: #62666c;
            font-size: 16px;
          }
        }
        svg {
          font-size: 64px;
        }
      }
    }
    .bot {
      width: 100%;
      height: calc(100% - 110px);
      padding: 0 16px;
      box-sizing: border-box;
      background: #fff;
      .filterTab {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        & > div {
          .el-input {
            width: 240px;
          }
          .el-select {
            width: 240px;
          }
          & > span {
            font-weight: 400;
            color: #2677ff;
            line-height: 20px;
            // margin-left: 16px;
            cursor: pointer;
          }
        }
      }
      .tableWrap {
        height: calc(100% - 131px);
        padding: 0px 20px;
        .autoHeader {
          font-size: 16px;
          color: #62666c;
          box-shadow: 2px 0px 8px 0px rgba(0, 0, 0, 0.08);
          cursor: pointer;
        }
      }
      .el-table {
        width: 99%;
        border: 0;
        .detail {
          padding: 0 0;
          height: 40px;
          line-height: 40px;
          border-bottom: 1px solid #ebeef5;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          img {
            vertical-align: middle;
          }
          p {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          div {
            div {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
        /deep/.el-table__body td.el-table__cell {
          padding: 0 !important;
        }
        /deep/.el-table__body .cell {
          padding: 0 !important;
        }
        .detail:last-child {
          border-bottom: 0;
        }
        .cell-other {
          padding: 0 0;
        }
      }
    }
  }
  .filterWrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    margin: 16px 0;
    // .btnWrap {
    //   position:absolute;
    //   top: 5px;
    //   right: 16px;
    // }
  }
  .filterWrap > div {
    display: flex;
    align-items: center;
    & > p {
      margin-right: 16px;
    }
    .confirmBox {
      margin-right: 16px;
    }
    .el-input {
      width: 240px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      margin-left: 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }
  // 定义单元格文本超出不换行
  /deep/.cell-other {
    // width: 140px !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
  }
  .box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;
  }
  .recommendlLeft {
    background: #ffffff linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #f3f7ff 100%);
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    // position: relative;
    .recommendTitle {
      font-weight: 500;
      color: #37393c;
      margin: 12px 16px 0px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e9ebef;
    }
    .recommendBox {
      height: calc(100% - 44px);
      overflow: auto;
    }
    li {
      padding: 16px 16px 0px;
      color: #62666c;
      // display: flex;
      .rightTitle {
        width: 100%;
        padding-bottom: 4px;
        font-size: 14px;
        color: #111;
      }
      .rightContent {
        span {
          display: inline-block;
          // width: 100%;
          padding: 0;
        }
        .clueClass {
          // max-height: 400px;
          // overflow-y: auto;
          .el-icon-picture-outline {
            font-size: 16px;
          }
        }
      }
    }
  }
}
.clueClass > span {
  display: inline-block;
  color: #37393c;
  margin-bottom: 12px;
  margin-right: 10px;
  padding: 3px 12px !important;
  border-radius: 14px;
  border: 1px solid #d1d5dd;
}
.clueClass > span > .myImg {
  width: 16px;
  height: 16px;
  font-size: 16px;
}
.clueClass > span > .myImg > img {
  width: 100%;
  height: 100%;
}
// /deep/.el-tooltip {
//   display: inline !important;
// }
.myHeader {
  display: flex;
  align-items: center;
  i {
    margin-right: 5px;
  }
}
.headerTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.headerTitle > span:first-child {
  margin-right: 12px;
}
.statisticnum {
  margin-right: 12px;
  font-size: 14px;
  font-weight: 400;
  color: #62666c;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  padding: 4px 10px;
  border-left: 2px solid #2677ff;
  .num {
    font-weight: 500;
    color: #2677ff;
    margin-right: 0px;
  }
}
.statisticnumBox > .statisticnum:last-child {
  margin-right: 0px;
}
#account_filter {
  margin-left: 16px;
}
.taskResults {
  position: absolute;
  height: 44px;
  line-height: 44px;
  padding-left: 16px;
  top: 0;
  left: 0;
  z-index: 10;
  font-weight: 500;
  color: #37393c;
  background-color: #fff;
  border-bottom: 1px solid #e9ebef;
  box-sizing: border-box;
}
</style>
