<template>
  <div class="Box">
    <div :class="taskStep != '1' ? 'myContentShort' : 'myContent'" v-loading="loading">
      <taskFirst v-if="taskStep == '1'" @son="sonDataFirst" />
      <taskSecond
        v-if="taskStep == '2'"
        @getTaskInfo="getTaskInfo"
        :taskInfoData="taskInfoData"
        @son="sonData"
      />
      <taskThird
        v-if="taskStep == '3'"
        :taskInfoData="taskInfoData"
        :expandType="expandType"
        @son="sonData"
      />
      <taskFourth
        v-if="taskStep == '4'"
        :taskInfoData="taskInfoData"
        :expandType="expandType"
        @son="sonData"
      />
    </div>
  </div>
</template>
<script>
import stepBar from '@/components/stepBar.vue'
import taskFirst from './taskFirst.vue'
import taskSecond from './taskSecond.vue'
import taskThird from './taskThird.vue'
import taskFourth from './taskThird.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { fakeTaskInfo } from '@/api/apiConfig/phishing.js'

export default {
  components: { stepBar, taskFirst, taskSecond, taskThird, taskFourth },
  props: ['taskId'],
  provide() {
    return {
      getStepArr: () => this.stepArr
    }
  },
  data() {
    return {
      expandType: '',
      loading: false,
      stepWidth: '20%',
      taskInfoData: {},
      taskStep: '1',
      stepArr: [
        {
          id: 1,
          title: '输入企业名称'
        },
        {
          id: 2,
          title: '特征线索提取'
        },
        {
          id: 3,
          title: '仿冒数据发现'
        },
        {
          id: 4,
          title: '数据存活性检测'
        }
      ],
      user: {
        role: ''
      },
      userInfo: ''
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getTaskInfo()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2) {
      // 安服账号切换企业需要回到一级页面
      if (!this.currentCompany) return
      // props接受任务记录查看详情的id
      this.getTaskInfo(this.taskId ? this.taskId : '')
    } else {
      this.getTaskInfo(this.taskId ? this.taskId : '')
    }
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    change(val) {
      this.taskStep = val
    },
    sonData(val, taskId, expandType) {
      //父子传参改变tab
      this.expandType = expandType
      if (taskId) {
        // 第一步点击确定后拿任务记录id去获取任务详情
        this.taskStep = val
        this.getTaskInfo(taskId)
      } else {
        this.taskStep = val
      }
    },
    sonDataFirst(val, taskId) {
      if (taskId) {
        // 第一步点击确定后拿任务记录id去获取任务详情
        this.getTaskInfo(taskId)
      } else {
        this.taskStep = val
      }
    },
    async getTaskInfo(taskId) {
      // 获取任务详情数据，任务记录id存在，查询对应的详情数据；不存在后端直接查询未完成的任务数据，有返回值则展示，没有返回值新建流程
      let id = taskId ? taskId : ''
      let obj = {
        taskId: id,
        data: {
          operate_company_id: this.currentCompany
        }
      }
      this.loading = true
      let res = await fakeTaskInfo(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.taskInfoData = JSON.parse(JSON.stringify(res.data)) // 任务详情
        sessionStorage.setItem('taskInfoData', res.data)
        if (res.data) {
          if (res.data.step) {
            // 跳转步骤
            this.taskStep = res.data.step
          }
          if (res.data.expend_flags) {
            this.recommentFlagChange(res.data.expend_flags) // 存储云端推荐flag
          } else {
            this.recommentFlagChange('')
          }
          if (res.data.step_detail == 402) {
            // this.stepArr.push(
            //     {
            //         id: 5,
            //         title: '深度探测'
            //     },
            // )
            this.$set(this.stepArr, 4, {
              id: 5,
              title: '深度探测'
            })
          } else {
            this.stepArr = [
              {
                id: 1,
                title: '输入企业名称'
              },
              {
                id: 2,
                title: '特征线索提取'
              },
              {
                id: 3,
                title: '仿冒数据发现'
              },
              {
                id: 4,
                title: '数据存活性检测'
              }
            ]
          }
        } else {
          // 返回数据为空，直接跳转到第一步即可
          this.taskStep = '1'
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.Box {
  height: 100%;
}
.tabBox {
  display: flex;
  align-items: center;
  margin: 20px 20px 16px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.tabBox > div {
  margin-right: 20px;
  height: 40px;
  width: 25%;
  display: flex;
  align-items: center;
}
.tabBox > div > span {
  display: flex;
  align-items: center;
}
.tabBox > div:last-child {
  margin-right: 0px;
}
.tabBaryuan {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  color: #2677ff;
  margin: 0px 8px 0px 16px;
}
.tabBargray {
  color: #ffffff;
  background: #b2bdd4;
}
.tabBaryuan1 {
  margin: 0px 8px 0px 16px;
  font-size: 24px;
  color: #2677ff;
}
// .myContent {
//     height: calc(100% - 78px);
// }
/deep/.el-progress {
  position: relative;
  .el-progress-bar {
    padding-right: 0px !important;
  }
  .el-progress__text {
    font-size: 14px !important;
    color: #2677ff;
    position: absolute;
    bottom: 23px;
    right: 0px;
  }
}
.myBox {
  height: 100%;
}
/deep/.box {
  background-image: url('../../assets/images/bg.png');
  // background: linear-gradient(90deg, #F0F6FF 0%, #FFFFFF 100%);
  background-repeat: no-repeat;
  // width: 100%;
  background-size: 100% 100%;
  padding: 0 0 0 16px;
}
/deep/.progressBarBox {
  display: flex;
  align-items: center;
  margin: 0px 20px;
  color: #37393c;
  padding: 16px 0;
  .tu {
    width: 100px;
    height: 100px;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .progressBox {
    flex-grow: 1;
    margin-left: 22px;
  }
  .progressBox > div {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }
}
/deep/.tableLabel {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #37393c;
  justify-content: space-between;
  // margin-bottom: 16px;
}
/deep/.tableLabel > div {
  display: flex;
  align-items: center;
  & > p {
    margin-right: 16px;
  }
  .confirmBox {
    margin-right: 16px;
  }
  .el-input {
    width: 240px;
  }
  & > span {
    font-weight: 400;
    color: #2677ff;
    line-height: 20px;
    margin-left: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}
/deep/.progressBoxFinish {
  display: flex;
  align-items: center;
  color: #37393c;
  padding: 0px 20px 16px 20px;
}
/deep/.progressBoxFinish > span {
  display: flex;
  align-items: center;
  margin-right: 16px;
  i {
    color: #10d595;
    margin-right: 5px;
    font-size: 20px;
  }
}
/deep/.progressContent {
  color: #62666c;
  display: flex;
  align-items: center;
  img {
    width: 24px;
    height: 20px;
    margin-right: 12px;
  }
}
/deep/.el-pagination {
  background: transparent !important;
  box-shadow: none !important;
}
/deep/.myTable {
  padding: 0px 20px;
  height: calc(100% - 178px);
}
/deep/.boxTwo {
  background: #ffffff !important;
}
/deep/.boxTwo1 {
  height: 0;
  flex: 1;
  background: #ffffff !important;
}
/deep/.footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  background: #ffffff;
  box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
}
/deep/.tu {
  width: 120px !important;
  height: 120px !important;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position-y: 0px;
}
/deep/.tuSecond {
  background-image: url('../../assets/images/dongtu1.png');
}
/deep/.tuThird {
  width: 180px !important;
  height: 180px !important;
  background-image: url('../../assets/images/dongtu2.png');
}
/deep/.tuFourth {
  background-image: url('../../assets/images/dongtu3.png');
}
/deep/.tuFifth {
  width: 180px !important;
  height: 180px !important;
  background-image: url('../../assets/images/dongtu4.png');
}

.yuchang {
  visibility: hidden;
  position: fixed;
}
/deep/.tuSecondPhishing {
  background-image: url('../../assets/images/dongtu5.png');
}
/deep/.grayBox {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0 !important;
}
.myContent {
  height: calc(100% - 56px);
}
.myContentShort {
  box-sizing: border-box;
  position: relative;
  height: 100%;
  padding: 0 16px 16px;
  /deep/.taskBox {
    box-sizing: border-box;
    height: calc(100% - 56px);
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
    .fourthBox {
      position: relative;
      height: 0;
      flex: 1;
      overflow: auto;
      .el-loading-mask {
        opacity: 0.3;
        z-index: 1999 !important;
      }
      .loading {
        position: absolute;
        top: 0;
        width: 100%;
        height: 158px;
        padding-top: 32px;
        // background: linear-gradient(180deg, #FFFFFF 0%, rgba(255,255,255,0.00) 100%);
        color: rgba(38, 119, 255, 1);
        text-align: center;
        z-index: 998;
        &.scroll {
          background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
        }
      }
      .loadingItem {
        height: 100%;
        display: flex;
        justify-content: space-around;
        align-items: center;
        & > span {
          text-align: left;
          margin-right: 20px;
          overflow: hidden;
          white-space: nowrap;
          -o-text-overflow: ellipsis;
          text-overflow: ellipsis;
          &:last-child {
            margin-right: 0;
          }
          &.img {
            height: auto;
            vertical-align: middle;
          }
        }
      }
    }
    .myBox {
      height: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
