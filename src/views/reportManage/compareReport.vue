<template>
  <div class="container">
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入报告名称进行搜索"
            id="report_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckDialog = true" id="report_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="report_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="btnHandle('删除', '', 'isMore')"
            id="report_more_del"
            >删除</el-button
          >
        </div>
      </div>
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highList"
        :total="total"
        pageIcon="report"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableHeader"
            :key="index"
            :prop="item.name"
            align="left"
            :min-width="item.name == 'name' ? 150 : 120"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'create_status'">
                <span class="blueLine" v-if="scope.row.create_status == 0">生成中</span>
                <span class="greenLine" v-if="scope.row.create_status == 1">生成成功</span>
                <span class="redLine" v-if="scope.row.create_status == 2">生成失败</span>
              </span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="200">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="btnHandle('下载', scope.row.path)"
                v-if="scope.row.create_status == 1"
                id="report_download"
                >下载</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="btnHandle('删除', scope.row.id)"
                v-if="scope.row.create_status != 0"
                id="report_del"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckDialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="报告名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="生成状态：" prop="create_status">
            <el-select
              v-model="formInline.create_status"
              placeholder="请选择"
              @change="selectChange($event, 'create_status', createStatus, true, false)"
            >
              <el-option
                v-for="item in createStatus"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="生成时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="report_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="report_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { mapGetters, mapState } from 'vuex'
import { changeReportsList, deleteChangeReports } from '@/api/apiConfig/api.js'
export default {
  components: { hightFilter, tableTooltip },
  data() {
    return {
      createStatus: [
        {
          id: '0',
          name: '生成中'
        },
        {
          id: '1',
          name: '生成成功'
        },
        {
          id: '2',
          name: '生成失败'
        }
      ],
      loading: false,
      formInline: {
        keyword: '',
        page: 1,
        per_page: 10,
        operate_company_id: '',
        name: '',
        created_at_range: [],
        create_status: '',
        report_template_id: []
      },
      highCheckDialog: false,
      checkedArr: [],
      checkedAll: false,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      currentPage: 1,
      tableData: [],
      total: 0,
      highList: null,
      highTabShow: [
        {
          label: '报告名称',
          name: 'name',
          type: 'input'
        },
        {
          label: '生成状态',
          name: 'create_status',
          type: 'select'
        },
        {
          label: '生成时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      tableHeader: [
        {
          label: '报告名称',
          name: 'name'
        },
        {
          label: '生成时间',
          name: 'updated_at'
        },
        {
          label: '生成状态',
          name: 'create_status'
        }
      ],
      user: {
        level: '',
        role: ''
      },
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (this.user.role == 2 && !this.currentCompany) return
    this.getData()
  },
  watch: {
    getterCurrentCompany(msg) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    }
  },
  methods: {
    async btnHandle(icon, data, isMore) {
      let id = []
      if (isMore) {
        //批量操作
        id = this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            })
      } else {
        id = [data]
      }
      if (isMore && this.checkedArr.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at_range = this.formInline.created_at_range
        ? this.formInline.created_at_range
        : []
      let obj = {
        id: id,
        ...this.formInline
      }
      let res = null
      if (icon == '下载') {
        this.download(this.showSrcIp + data)
      } else if (icon == '删除') {
        this.$confirm('确定删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'report_del_cancel',
          confirmButtonClass: 'report_del_sure',
          customClass: 'report_del',
          type: 'warning'
        })
          .then(async () => {
            this.loading = true
            res = await deleteChangeReports(obj).catch(() => {
              this.loading = false
            })
            if (res.code == 0) {
              this.loading = false
              this.$refs.eltable.clearSelection()
              this.$message.success('操作成功！')
            }
            this.getData()
          })
          .catch(() => {
            this.loading = false
          })
        setTimeout(() => {
          var del = document.querySelector('.report_del>.el-message-box__btns')
          del.children[0].id = 'report_del_cancel'
          del.children[1].id = 'report_del_sure'
        }, 50)
      }
    },
    async getData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at_range = this.formInline.created_at_range
        ? this.formInline.created_at_range
        : []
      this.loading = true
      const res = await changeReportsList(this.formInline).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.tableData = res.data.items || []
      this.total = res.data ? res.data.total : 0
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    checkFuncList() {
      this.highCheckDialog = false
      this.currentPage = 1
      this.highList = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highList)
      this.checkFuncList()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
          margin-right: 10px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 6px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
      .pocName {
        cursor: pointer;
      }
      .btnClass {
        .el-button + .el-button {
          margin-left: 10px;
        }
      }
      .btnClass:nth-child(2) {
        margin-left: 10px;
      }
    }
    .el-table {
      border: 0;
    }
  }
  .word_dialog {
    /deep/ .el-dialog {
      height: 80%;
      .el-dialog__body {
        max-height: 86%;
      }
      #wordView {
        height: 100%;
        overflow: auto;
        position: relative;
        .bg {
          padding-bottom: 10px;
        }
        tbody {
          border: 1px solid #ddd !important;
          td {
            padding: 5px 10px;
            border: 1px solid #ddd !important;
          }
        }
        .samll-bg {
          position: absolute;
          left: 20px;
          top: 20px;
          width: 55%;
        }
        .title {
          top: 300px;
          font-size: 20px;
        }
        .report {
          top: 360px;
          font-size: 18px;
        }
        .company {
          top: 590px;
          font-size: 14px;
        }
        .date {
          top: 620px;
        }
        .title,
        .report,
        .company,
        .date {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          color: #fff;
        }
      }
    }
  }
  .name {
    cursor: pointer;
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
