<template>
  <div class="container">
    <div class="headerTitle">{{ headerTitle }}</div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="报告列表" name="first"> </el-tab-pane>
        <el-tab-pane label="资产变化报告列表" name="third"> </el-tab-pane>
        <el-tab-pane label="模板管理" name="second"> </el-tab-pane>
      </el-tabs>
      <div class="tab_content">
        <reportList v-if="activeName == 'first'" />
        <reportTemplate v-if="activeName == 'second'" />
        <compareReport v-if="activeName == 'third'" />
      </div>
    </div>
  </div>
</template>

<script>
import reportTemplate from './reportTemplate.vue' // 报告模板
import reportList from './reportList.vue' // 报告列表
import compareReport from './compareReport.vue' // 报告列表

export default {
  components: { reportList, reportTemplate, compareReport },

  data() {
    return {
      headerTitle: '',
      activeName: 'first'
    }
  },
  computed: {},
  watch: {
    $route(to, from) {}
  },
  created() {
    this.init()
  },
  async mounted() {
    if (this.$route.query && this.$route.query.flag == 1) {
      // 报告生成后提示
      this.activeName = 'first'
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // if (from.path == '/alreadyTask_viewlist') { // 从已完成任务返回
      //   vm.activeName = 'first'
      // } else {
      //   vm.activeName = 'first'
      //   sessionStorage.setItem('queryParam', '')
      // }
    })
  },
  beforeDestroy() {
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    init() {
      this.headerTitle = this.activeName == 'first' ? '报告列表' : '模板管理'
    },
    handleClick() {
      sessionStorage.setItem('activeTabName', this.activeName)
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      padding: 0 16px;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      // padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .tab_content {
      height: calc(100% - 44px);
    }
    .tab_content_tip {
      height: calc(100% - 101px);
    }
    .downloadClass {
      // width: 100%;
      height: 40px;
      line-height: 40px;
      margin: 16px 20px 0 20px;
      background: rgba(38, 119, 255, 0.18);
      border-radius: 2px;
      border: 1px solid rgba(38, 119, 255, 0.44);
      cursor: pointer;
      i {
        font-size: 14px;
        color: #2677ff;
        margin: 0 8px 0 16px;
      }
      span {
        color: #2677ff;
      }
    }
  }
}
</style>
