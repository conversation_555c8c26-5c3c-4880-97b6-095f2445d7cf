<template>
  <div class="container" v-loading="loading">
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入报告名称进行搜索"
            id="report_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="report_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="report_all"
            >选择全部</el-checkbox
          >
          <el-tooltip
            class="item"
            effect="light"
            placement="top"
            content="选择两个资产报告可以对比出新增减少的数据"
          >
            <el-button
              class="normalBtn"
              type="primary"
              :disabled="compareReportDisabled"
              @click="compareReport"
              id="report_add"
              >报告对比</el-button
            >
          </el-tooltip>
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="btnHandle('删除', '', 'isMore')"
            id="report_more_del"
            >删除</el-button
          >
          <el-button class="normalBtn" type="primary" @click="addReport" id="report_add"
            >生成报告</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="report"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableHeader"
            :key="index"
            :prop="item.name"
            align="left"
            :min-width="item.name == 'name' ? 150 : 120"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'reportsetting'">{{
                scope.row[item.name] ? scope.row[item.name][item.names] : '-'
              }}</span>
              <span v-else-if="item.name == 'create_status'">
                <span class="blueLine" v-if="scope.row.create_status == 0">生成中</span>
                <span class="greenLine" v-if="scope.row.create_status == 1">生成成功</span>
                <span class="redLine" v-if="scope.row.create_status == 2">生成失败</span>
              </span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="200">
            <template slot-scope="scope">
              <!-- <el-button type="text" size="small" @click="previewWord(scope.row)" v-if="scope.row['create_status'] == 1" id="report_review">预览</el-button> -->
              <el-button
                type="text"
                size="small"
                @click="btnHandle('下载', scope.row.path)"
                v-if="scope.row.create_status == 1"
                id="report_download"
                >下载</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="btnHandle('删除', scope.row.id)"
                v-if="scope.row.create_status != 0"
                id="report_del"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      :visible.sync="dialogWordVisible"
      :close-on-click-modal="false"
      class="elDialogAdd word_dialog"
      width="1000px"
    >
      <template slot="title">
        报告预览
        <span style="margin-left: 10px; cursor: pointer" @click="btnHandle('下载', record.path)"
          >下载</span
        >
      </template>
      <div id="wordView" ref="file" v-loading="previewLoading"></div>
    </el-dialog>
    <el-dialog
      :visible.sync="addReportVisible"
      :close-on-click-modal="false"
      class="elDialogAdd"
      width="500px"
      :rules="rules"
    >
      <template slot="title"> 生成报告 </template>
      <div class="dialog-body">
        <el-form
          :model="ruleFormAdd"
          style="padding: 0 !important"
          ref="ruleForm"
          :rules="ruleFormRules"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="报告名称" prop="name">
            <el-input v-model="ruleFormAdd.name" placeholder="请输入报告名称，不能包含/"></el-input>
          </el-form-item>
          <el-form-item label="报告模板" prop="myTemplate">
            <el-select v-model="ruleFormAdd.myTemplate" placeholder="请选择">
              <el-option
                v-for="item in myTemplates"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="addReportVisible = false" id="report_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          :loading="btnLoading"
          @click="generateReport"
          id="report_add_sure"
          >生成</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="报告名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="生成状态：" prop="create_status">
            <el-select
              v-model="formInline.create_status"
              placeholder="请选择"
              @change="selectChange($event, 'create_status', createStatus, true, false)"
            >
              <el-option
                v-for="item in createStatus"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报告模板：" prop="report_template_id">
            <el-select
              filterable
              clearable
              multiple
              collapse-tags
              v-model="formInline.report_template_id"
              @change="selectChange($event, 'report_template_id', template_name_arr, true, true)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in template_name_arr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="生成时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="report_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="report_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
    <prompt
      label="报告名称"
      title="生成报告"
      placeholder="请输入报告名称"
      :visible="newReportVisible"
      @save="onSubmitBtn"
      @close="newReportVisible = false"
      :loading="newReportLoading"
    />
  </div>
</template>

<script>
import prompt from '@/components/assets/prompt'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { mapGetters, mapState } from 'vuex'
import hightFilter from '../../components/assets/highTab.vue'
import {
  getReportsList,
  delReports,
  previewReport,
  getReportsTemplate,
  addReports,
  createChangeReports
} from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip, hightFilter, prompt },
  data() {
    return {
      newReportLoading: false,
      newReportVisible: false,
      highTabShow: [
        {
          label: '报告名称',
          name: 'name',
          type: 'input'
        },
        {
          label: '生成状态',
          name: 'create_status',
          type: 'select'
        },
        {
          label: '报告模板',
          name: 'report_template_id',
          type: 'select'
        },
        {
          label: '生成时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      template_name_arr: [],
      pdfUrl: '',
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      btnLoading: false,
      checkedArr: [],
      checkedAll: false,
      highCheckdialog: false,
      iframeUrl: '',
      formInline: {
        keyword: '',
        page: 1,
        per_page: 10,
        operate_company_id: '',
        name: '',
        created_at_range: [],
        create_status: '',
        report_template_id: []
      },
      addIsTrue: true,
      loading: false,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      tableData: [],
      currentPage: 1,
      tableHeader: [
        {
          label: '报告名称',
          name: 'name'
        },
        {
          label: '生成时间',
          name: 'updated_at'
        },
        {
          label: '生成状态',
          name: 'create_status'
        },
        {
          label: '报告模板',
          name: 'reportsetting',
          names: 'name'
        }
      ],
      user: {
        level: '',
        role: ''
      },
      record: null,
      dialogWordVisible: false,
      previewLoading: false,
      wordText: '',
      ruleFormAdd: {
        name: '',
        myTemplate: ''
      },
      ruleFormRules: {
        name: [
          { required: true, message: '请填写报告名称', trigger: 'change' },
          {
            message: '报告名称不能包含/',
            pattern: /^((?![/]).)*$/,
            trigger: 'blur'
          }
        ],
        myTemplate: [{ required: true, message: '请选择模板名称', trigger: 'change' }]
      },
      addReportVisible: false,
      myTemplates: [],
      rules: {
        name: [{ required: true, message: '请输入报告名称', trigger: 'blur' }]
      },
      createStatus: [
        {
          id: '0',
          name: '生成中'
        },
        {
          id: '1',
          name: '生成成功'
        },
        {
          id: '2',
          name: '生成失败'
        }
      ],
      reportTemplateList: [],
      lookPdfFile: false, //预览pdf
      pdfUrl: '' // pdf路径
    }
  },
  watch: {
    getterCurrentCompany(msg) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getReportData()
      }
    },
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage']),
    compareReportDisabled() {
      return this.checkedArr.length < 2
    }
  },
  async created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (this.user.role == 2) {
      if (this.currentCompany) {
        this.getReportData()
      }
    } else {
      this.getReportData()
    }
  },
  beforeDestroy() {
    this.highlist = null
  },
  methods: {
    compareReport() {
      if (this.checkedArr.length > 2) {
        this.$message.error('最多只能选择两个报告进行对比')
        return
      }

      this.newReportVisible = true
    },
    async onSubmitBtn(name) {
      if (!name) {
        return this.$message.error('请输入报告名称')
      }
      // this.formInline = Object.assign(this.highlist)
      // this.checkFuncList()
      // let last_report_id = this.checkedArr[0].id
      console.log(this.checkedArr)

      let last_report_id = Math.min(this.checkedArr[0].id, this.checkedArr[1].id)
      let now_report_id = Math.max(this.checkedArr[0].id, this.checkedArr[1].id)
      this.newReportLoading = true
      let res = await createChangeReports({
        name,
        last_report_id,
        now_report_id,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.newReportVisible = false
        this.newReportLoading = false
      })
      this.newReportVisible = false
      this.newReportLoading = false
      if (res.code == 0) {
        this.$message.success('对比报告生成成功')
        this.$refs.eltable && this.$refs.eltable.clearSelection()
      }
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getReportData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    handleMessage(res, o) {
      if (res.cmd == 'create_report_success') {
        // 报告生成推送
        this.getReportData()
      }
    },
    async getReportData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at_range = this.formInline.created_at_range
        ? this.formInline.created_at_range
        : []
      this.loading = true
      let res = null
      res = await getReportsList(this.formInline).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      if (res.code == 0) {
        this.loading = false
        this.tableData = res.data ? res.data.items : []
        this.template_name_arr = []
        if (res.data && res.data.condition) {
          for (let k in res.data.condition.report_template_id) {
            this.template_name_arr.push({
              id: k,
              name: res.data.condition.report_template_id[k]
            })
          }
        }
        this.total = res.data ? res.data.total : 0
      }
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    async btnHandle(icon, data, isMore) {
      let id = []
      if (isMore) {
        //批量操作
        id = this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            })
      } else {
        id = [data]
      }
      if (isMore && this.checkedArr.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at_range = this.formInline.created_at_range
        ? this.formInline.created_at_range
        : []
      let obj = {
        id: id,
        ...this.formInline
      }
      let res = null
      if (icon == '下载') {
        this.download(this.showSrcIp + data)
      } else if (icon == '删除') {
        this.$confirm('确定删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'report_del_cancel',
          confirmButtonClass: 'report_del_sure',
          customClass: 'report_del',
          type: 'warning'
        })
          .then(async () => {
            this.loading = true
            res = await delReports(obj).catch(() => {
              this.loading = false
            })
            if (res.code == 0) {
              this.loading = false
              this.$refs.eltable.clearSelection()
              this.$message.success('操作成功！')
            }
            this.getReportData()
          })
          .catch(() => {
            this.loading = false
          })
        setTimeout(() => {
          var del = document.querySelector('.report_del>.el-message-box__btns')
          del.children[0].id = 'report_del_cancel'
          del.children[1].id = 'report_del_sure'
        }, 50)
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getReportData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getReportData(true)
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    resetForm(ref) {
      this.$refs[ref].resetFields()
      this.formInline = {
        keyword: '',
        page: 1,
        per_page: 10,
        operate_company_id: '',
        name: '',
        created_at_range: [],
        create_status: '',
        report_template_id: []
      }
    },
    // 预览
    async previewWord(row) {
      this.wordText = ''
      this.record = row
      let url = row.preview.substring(7) // 地址只需要files/
      let params = {
        url,
        responseType: 'blob',
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
      let res = await previewReport(params).catch(() => {
        this.$message.error('文件不存在！')
      })
      if (res) {
        this.previewLoading = true
        // 直接转换成word
        this.dialogWordVisible = true
        if (document.getElementById('wordView')) {
          docx.renderAsync(res, document.getElementById('wordView')) // 渲染到页面预览
          setTimeout(() => {
            this.previewLoading = false
          }, 2000)
        }
      }
    },
    // 将返回的流数据转换为url
    getObjectURL(file) {
      let url = null
      if (window.createObjectURL != undefined) {
        // basic
        url = window.createObjectURL(file)
      } else if (window.webkitURL != undefined) {
        // webkit or chrome
        try {
          url = window.webkitURL.createObjectURL(file)
        } catch (error) {}
      } else if (window.URL != undefined) {
        // mozilla(firefox)
        try {
          url = window.URL.createObjectURL(file)
        } catch (error) {}
      }
      return url
    },
    // 生成报告
    async addReport() {
      this.ruleFormAdd.name = ''
      this.addReportVisible = true
      let obj = {
        operate_company_id: this.currentCompany,
        no_page: '1'
      }
      let res = await getReportsTemplate(obj)
      if (res.code == 0) {
        if (res.data.length != 0) {
          this.ruleFormAdd.myTemplate = res.data[0].id
          this.myTemplates = res.data
        } else {
          this.$message.error('请添加模板！')
        }
      }
    },
    async generateReport() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (!valid) return
        this.btnLoading = true
        let obj = {
          id: this.ruleFormAdd.myTemplate,
          name: this.ruleFormAdd.name,
          operate_company_id: this.currentCompany
        }
        let res = await addReports(obj).catch(() => {
          this.btnLoading = false
        })
        if (res.code == 0) {
          this.btnLoading = false
          this.addReportVisible = false
          this.getReportData()
          this.$message.success('操作成功！')
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
          margin-right: 10px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 6px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
      .pocName {
        cursor: pointer;
      }
      .btnClass {
        .el-button + .el-button {
          margin-left: 10px;
        }
      }
      .btnClass:nth-child(2) {
        margin-left: 10px;
      }
    }
    .el-table {
      border: 0;
    }
  }
  .word_dialog {
    /deep/ .el-dialog {
      height: 80%;
      .el-dialog__body {
        max-height: 86%;
      }
      #wordView {
        height: 100%;
        overflow: auto;
        position: relative;
        .bg {
          padding-bottom: 10px;
        }
        tbody {
          border: 1px solid #ddd !important;
          td {
            padding: 5px 10px;
            border: 1px solid #ddd !important;
          }
        }
        .samll-bg {
          position: absolute;
          left: 20px;
          top: 20px;
          width: 55%;
        }
        .title {
          top: 300px;
          font-size: 20px;
        }
        .report {
          top: 360px;
          font-size: 18px;
        }
        .company {
          top: 590px;
          font-size: 14px;
        }
        .date {
          top: 620px;
        }
        .title,
        .report,
        .company,
        .date {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          color: #fff;
        }
      }
    }
  }
  .name {
    cursor: pointer;
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
