<template>
  <div class="container" v-loading="loading">
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            @keyup.enter.native="getReportData"
            placeholder="请输入模板名称进行搜索"
            id="report_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="getReportData"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="report_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="report_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="btnHandle('删除', '', 'isMore')"
            id="report_more_del"
            >删除</el-button
          >
          <el-button class="normalBtn" type="primary" @click="insertShow" id="report_add"
            >新建模板</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="report_template"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableHeader"
            :key="index"
            :prop="item.name"
            align="left"
            :min-width="item.name == 'name' ? 150 : 120"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span class="blueRadiusBorder" v-if="item.name == 'type' && scope.row[item.name] == 1"
                >系统预置</span
              >
              <span
                class="greenRadiusBorder"
                v-else-if="item.name == 'type' && scope.row[item.name] == 2"
                >自定义</span
              >
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="200">
            <template slot-scope="scope">
              <!-- 单位测绘和云端推荐模板不可编辑（scope.row.detect_assets_tasks_id） -->
              <el-button
                type="text"
                :disabled="scope.row.detect_assets_tasks_id ? true : false"
                size="small"
                @click="editHandleTemplate(scope.row)"
                id="report_edit"
                >编辑</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="btnHandle('立即生成', scope.row.id)"
                id="report_immediately"
                >立即生成</el-button
              >
              <el-button
                type="text"
                size="small"
                v-if="scope.row['type'] == 2"
                @click="btnHandle('删除', scope.row.id)"
                id="report_del"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 新建模板 -->
    <el-dialog
      class="elDialogAdd"
      :title="addIsTrue ? '新建模板' : '编辑模板'"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="680px"
    >
      <div class="dialog-body" v-loading="dialoading">
        <el-form
          :model="ruleForm"
          style="padding: 0 !important"
          ref="ruleForm"
          :rules="ruleFormRules"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="模板名称" prop="name">
            <el-input v-model="ruleForm.name" placeholder="请输入模板名称，不能包含/"></el-input>
          </el-form-item>
          <el-form-item label="时间范围" prop="time_type">
            <el-radio-group v-model="ruleForm.time_type" size="medium">
              <el-radio-button label="1">全部</el-radio-button>
              <el-radio-button label="2">近365天</el-radio-button>
              <el-radio-button label="3">近180天</el-radio-button>
              <el-radio-button label="4">近90天</el-radio-button>
              <el-radio-button label="5">近30天</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="报告内容" prop="report_module">
            <div class="contentClass" v-for="item in reportCon" :key="item.id">
              <el-checkbox
                :indeterminate="item.isIndeterminate"
                v-model="item.report_module"
                @change="handleCheckAllChange($event, item)"
                >{{ item.name }}</el-checkbox
              >
              <el-checkbox-group
                v-model="item.checkedArr"
                @change="handleCheckedCitiesChange($event, item)"
              >
                <!-- :disabled="city.id == 1 || city.id == 2",台账和疑似资产默认选中不可操作 -->
                <el-checkbox
                  :disabled="city.id == 1 || city.id == 2 || city.id == 6 || ( city.id == 7 && handleData(item.checkedArr))"
                  v-for="city in item.children"
                  :label="city.id"
                  :key="city.id"
                  >{{ city.name }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
          </el-form-item>
          <el-form-item label="报告通知" prop="email">
            <el-input v-model="ruleForm.email" placeholder="请填写通知邮箱"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleInsert = false" id="report_add_cancel"
          >取消</el-button
        >
        <el-button
          class="highBtn"
          :loading="btnLoading"
          @click="handleTemplate"
          id="report_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="模板名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入模板名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="模板类型：" prop="type">
            <el-select
              filterable
              v-model="formInline.type"
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'type', typeArr, true, false)"
            >
              <el-option
                v-for="item in typeArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="report_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="report_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { mapGetters, mapState } from 'vuex'
import hightFilter from '../../components/assets/highTab.vue'
import {
  getReportsTemplate,
  addReportsTemplate,
  editReportsTemplate,
  infoReportsTemplate,
  addReports,
  deleteReportsTemplate,
  conditionTemplate
} from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip, hightFilter },
  data() {
    return {
      highTabShow: [
        {
          label: '模板名称',
          name: 'name',
          type: 'input'
        },
        {
          label: '模板类型',
          name: 'type',
          type: 'select'
        },
        {
          label: '创建时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      typeArr: [
        {
          id: 1,
          name: '系统预置'
        },
        {
          id: 2,
          name: '自定义'
        }
      ],
      dialoading: false,
      // 报告开启的模块,1/资产台账 2/疑似资产 3/数字资产 4/漏洞检测 5/数据泄露
      reportCon: [
        {
          id: 'a1',
          name: '资产测绘',
          report_module: false,
          isIndeterminate: false,
          checkedArr: [1, 2, 6],
          children: [
            {
              id: 1,
              name: '资产台账'
            },
            {
              id: 2,
              name: '疑似资产'
            },
            {
              id: 6,
              name: '威胁资产'
            },
            {
              id: 3,
              name: '数字资产'
            }
          ]
        },
        {
          id: 'b1',
          name: '资产威胁检测',
          report_module: false,
          isIndeterminate: true,
          checkedArr: [],
          children: [
            {
              id: 4,
              name: '漏洞检测'
            },
            {
              id: 5,
              name: '数据泄露检测'
            },
            {
              id: 7,
              name: '数据泄露报告显示截图'
            }
          ]
        }
      ],
      dialogFormVisibleInsert: false,
      btnLoading: false,
      checkedArr: [],
      checkedAll: false,
      highCheckdialog: false,
      iframeUrl: '',
      formInline: {
        keyword: '',
        page: 1,
        per_page: 10,
        name: '',
        type: '',
        operate_company_id: '',
        created_at_range: []
      },
      ruleForm: {
        name: '',
        time_type: 1, // 1、全部 2、近365天 3、近180天 4、近90天 5、30天
        email: ''
      },
      ruleFormRules: {
        name: [
          { required: true, message: '请填写模板名称', trigger: 'change' },
          {
            message: '模板名称不能包含/',
            pattern: /^((?![/]).)*$/,
            trigger: 'blur'
          }
        ]
      },
      addIsTrue: true,
      loading: false,
      currentTemplateId: '',
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      tableData: [],
      currentPage: 1,
      tableHeader: [
        {
          label: '模板名称',
          name: 'name'
        },
        {
          label: '模板类型',
          name: 'type'
        },
        {
          label: '创建时间',
          name: 'created_at'
        }
      ],
      userEmail: '',
      user: {
        level: '',
        role: ''
      },
      record: null,
      flagRu: true,
      flagRus: true,
      isflagRu: true,
      isflagRus: true
    }
  },
  watch: {
    getterCurrentCompany(msg) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getReportData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany']),
    handleData(){
      return (val)=> {
        if(!val.includes(5)){
          return true
        }else {
          return false
        }
      }
    }
  },
  async created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
    this.getReportData()
  },
  beforeDestroy() {
    this.highlist = null
  },
  methods: {
    // 模块勾选，默认全选
    handleCheckAllChange(val, data) {
      if (data.id == 'a1') {
        data.checkedArr = val
          ? data.children.map((item) => {
              return item.id
            })
          : [1, 2, 6]
        data.isIndeterminate = false
      } else {
        data.checkedArr = val
          ? data.children.map((item) => {
              return item.id
            })
          : []
        data.isIndeterminate = false
      }
    },
    handleCheckedCitiesChange(value, data) {
      console.log(value,data)

      let checkedCount = value.length
      console.log("value.includes(7)",value.includes(7))
      console.log("checkedCount != data.children.length",checkedCount != data.children.length)
      console.log("!value.includes(5)",!value.includes(5))
      if(value.includes(7) && checkedCount != data.children.length && !value.includes(5)){
        // 去除里面的7
        this.reportCon[1].checkedArr = value.filter((item) => item != 7)
      }else {

      }
      data.report_module = checkedCount == data.children.length
      data.isIndeterminate = checkedCount > 0 && checkedCount < data.children.length
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getReportData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    // 没有选中全部去掉勾选
    assetsChange(val, nameF, name, optionsArr) {
      if (val.length < optionsArr.length) {
        this.ruleForm[nameF][name] = false
      }
      this.$forceUpdate()
    },
    selectAll(checked, nameF, name, optionsArr) {
      if (checked) {
        this.ruleForm[nameF][name] = optionsArr
      } else {
        this.ruleForm[nameF][name] = []
      }
      this.$forceUpdate()
    },
    async handleTemplate() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (!valid) return
        let report_module = [] // 单独处理选中传对应id,report_module
        this.reportCon.forEach((item) => {
          report_module = report_module.concat(item.checkedArr)
        })
        if (report_module.length == 0) {
          this.$message.error('请选择报告模块!')
          return
        }
        let obj = {
          operate_company_id: this.currentCompany,
          report_module: report_module.includes(7) ? report_module.filter((item) => item != 7) : report_module,
          name: this.ruleForm.name,
          time_type: this.ruleForm.time_type,
          is_show_leak_image: report_module.includes(7) ? 1 : 0,
          email: this.ruleForm.email, // '<EMAIL>',
        }
        let res = null
        if (this.addIsTrue) {
          // 新增
          res = await addReportsTemplate(obj)
        } else {
          // 编辑
          res = await editReportsTemplate({ id: this.currentTemplateId, data: obj })
        }
        if (res.code == 0) {
          this.$message.success('操作成功！')
          this.dialogFormVisibleInsert = false
          this.getReportData()
          if (this.addIsTrue) {
            // 模板新增，需要拿到生成的模板id
            this.currentTemplateId = res.data // 当前模板id
          }
          this.$confirm('是否用此模板立即生成报告?', '提示', {
            confirmButtonText: '立即生成',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(async () => {
              let res1 = await addReports({
                id: this.currentTemplateId,
                name: '',
                operate_company_id: this.currentCompany
              })
              if (res1.code == 0) {
                this.$message.success('操作成功！')
              }
            })
            .catch(() => {})
        }
      })
    },
    async editHandleTemplate(row) {
      this.addIsTrue = false
      this.dialogFormVisibleInsert = true
      this.currentTemplateId = row.id
      this.ruleForm = {
        id: row.id,
        name: row.name,
        time_type: row.time_type, // 1、全部 2、近365天 3、近180天 4、近90天 5、30天
        email: row.email
      }
      // 回显选中的报告内容
      if (row.report_module.indexOf(6) == -1) {
        row.report_module.push(6) // 没有威胁资产的自动补充
      }
      row.report_module = [...new Set(row.report_module)] // 去重
      this.reportCon[0].checkedArr = row.report_module.filter((item) => {
        return item < 4 || item == 6
      }) // row.report_module是包含资产测绘和资产威胁检测的模板，需要过滤
      this.reportCon[0].report_module = this.reportCon[0].checkedArr.length == 4 // 是否选中资产测绘
      this.reportCon[0].isIndeterminate = this.reportCon[0].checkedArr.length != 4
      this.reportCon[1].checkedArr = row.report_module.filter((item) => {
        return item > 3 && item != 6
      })
      this.reportCon[1].report_module = this.reportCon[1].checkedArr.length == 2 // 是否选中资产威胁检测
      this.reportCon[1].isIndeterminate =
        this.reportCon[1].checkedArr.length != 0 && this.reportCon[1].checkedArr.length != 2
        if(row.is_show_leak_image){
          this.reportCon[1].checkedArr.push(7)
        }
      this.$forceUpdate()
    },
    async insertShow() {
      this.addIsTrue = true
      this.dialogFormVisibleInsert = true
      this.reportCon.forEach((item, index) => {
        item.report_module = false // 初始化，补选中
        if (index == 0) {
          item.checkedArr = [1, 2, 6] // 初始化，默认选中台账，疑似资产,威胁资产
          item.isIndeterminate = true // 初始化，取消半选
        } else {
          item.checkedArr = []
          item.isIndeterminate = false // 初始化，取消半选
        }
      })
      this.$forceUpdate()
      this.ruleForm = {
        name: '',
        time_type: 1, // 1、全部 2、近365天 3、近180天 4、近90天 5、30天
        email: ''
      }
      if (sessionStorage.getItem('companyInfo')) {
        // 默认展示账号邮箱
        let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
        this.userEmail = companyInfo.owner.email
        this.ruleForm.email = this.userEmail
      } else {
        this.ruleForm.email = this.user.email
      }
    },
    async getReportData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }

      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at_range = this.formInline.created_at_range
        ? this.formInline.created_at_range
        : []
      this.loading = true
      let res = null
      res = await getReportsTemplate(this.formInline).catch((err) => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.tableData = res.data ? res.data.items : []
      this.total = res.data ? res.data.total : 0
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    async btnHandle(icon, data, isMore) {
      let id = []
      if (isMore) {
        //批量操作
        id = this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            })
      } else {
        id = [data]
      }
      if (isMore && this.checkedArr.length == 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at_range = this.formInline.created_at_range
        ? this.formInline.created_at_range
        : []
      let obj = {
        id: id,
        ...this.formInline
      }
      let res = null
      if (icon == '立即生成') {
        this.loading = true
        res = await addReports({
          id: data, // 当前模板id
          name: '',
          operate_company_id: this.currentCompany
        }).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          this.loading = false
          this.$message.success('操作成功！')
        }
        this.getReportData()
      } else if (icon == '删除') {
        this.$confirm('确定删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'report_del_cancel',
          confirmButtonClass: 'report_del_sure',
          customClass: 'report_del',
          type: 'warning'
        })
          .then(async () => {
            this.loading = true
            res = await deleteReportsTemplate(obj).catch(() => {
              this.loading = false
            })
            if (res.code == 0) {
              this.loading = false
              this.$message.success('操作成功！')
            }
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getReportData()
          })
          .catch(() => {
            this.loading = false
          })
        setTimeout(() => {
          var del = document.querySelector('.report_del>.el-message-box__btns')
          del.children[0].id = 'report_del_cancel'
          del.children[1].id = 'report_del_sure'
        }, 50)
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getReportData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getReportData(true)
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    resetForm(ref) {
      this.$refs[ref].resetFields()
      this.formInline = {
        keyword: '',
        page: 1,
        per_page: 10,
        name: '',
        type: '',
        operate_company_id: '',
        created_at_range: []
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.dialog-body {
    .el-loading-spinner {
      top: 20% !important;
    }
    .contentClass {
      & > .el-checkbox {
        background: #fff;
      }
      .el-checkbox-group {
        .el-checkbox {
          min-width: 90px;
        }
      }
    }
    .is-active > span {
      margin-left: 0 !important;
    }
    .titleClass {
      line-height: 26px !important;
    }
    .filterClass {
      padding-left: 20px;
      margin-left: 10px;
      margin-bottom: 5px;
      line-height: 24px;
    }
    .el-checkbox-group {
      width: 94% !important;
      padding-left: 20px;
      margin-left: 10px;
    }
    .el-select {
      width: 94% !important;
      padding-left: 20px;
      margin-left: 10px;
    }
    .el-checkbox {
      margin-right: 10px;
      /* width:32px; */
      text-align: left;
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      border-radius: 0;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
          margin-right: 10px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 6px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
      .pocName {
        cursor: pointer;
      }
      .btnClass {
        .el-button + .el-button {
          margin-left: 10px;
        }
      }
      .btnClass:nth-child(2) {
        margin-left: 10px;
      }
    }
    .el-table {
      border: 0;
    }
  }
  .name {
    cursor: pointer;
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
