<template>
  <div
    class="wrapper"
    id="assetProfile"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
  </div>
</template>

<script>
import blue from '../../assets/images/screenImage/blue.png'
import blue2 from '../../assets/images/screenImage/blue2.png'
import green from '../../assets/images/screenImage/green.png'
import profile from '../../assets/images/screenImage/profile.png'
import purple from '../../assets/images/screenImage/purple.png'
import orange from '../../assets/images/screenImage/orange.png'
import { mapState, mapGetters } from 'vuex'
import { assetOverview } from '@/api/apiConfig/screen.js'

export default {
  data() {
    return {
      myCharts: null,
      dataList: {},
      // currentCompany: '',
      loading: true
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany']),
    ...mapState(['currentCompany'])
  },
  methods: {
    async getDataList() {
      let res = await assetOverview({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.dataList = this.setData(res.data)
        // 与柱状图一个数据源
        // 直接放进去
        this.$emit('setLeakageDistribution', res.data)
        this.chartsInit()
      }
    },
    chartsInit() {
      if (this.myCharts !== null) {
        this.myCharts.clear()
      }
      this.myCharts = this.$echarts.init(document.getElementById('assetProfile'))
      let data = this.dataList.data
      let links = this.dataList.links
      let options = {
        animation: true,
        animationEasing: 'bounceOut',
        //  animationDuration: 300000,
        series: [
          {
            type: 'graph',
            name: 'name',
            layout: 'force',
            force: {
              //元素之间的间距
              repulsion: 400
            },
            lineStyle: {
              // ========关系边的公用线条样式。
              normal: {
                color: 'rgba(255, 0, 255, 0.4)',
                width: '1', //线的粗细
                type: 'solid', // 线的类型 'solid'（实线）'dashed'（虚线）'dotted'（点线）
                curveness: 0.3, // 线条的曲线程度，从0到1
                opacity: 0 // 图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。默认0.5
              },
              emphasis: {
                // 高亮状态
              }
            },
            edgeLabel: {
              normal: {
                show: true,
                textStyle: {
                  fontSize: 20
                }
              }
            },
            roam: true,
            label: {
              normal: {
                show: true,
                color: '#fff'
              }
            },
            data: data,
            links: links
          }
        ]
      }
      this.myCharts.setOption(options)
      this.loading = false
      window.addEventListener('resize', () => {
        if (this.myCharts != null) {
          this.myCharts.resize()
        }
      })
    },
    // 处理接口数据
    setData(value) {
      let dataTmp = [
        {
          name: '域名',
          value: 0,
          label: 'domain_count',
          color: 'rgba(16,213,149,0.2)',
          children: [
            {
              name: '主域名',
              value: 0,
              label: 'domain_top_count'
            },
            {
              name: '子域名',
              value: 0,
              label: 'domain_sub_count'
            }
          ]
        },
        {
          name: '证书',
          value: 0,
          label: 'cert_count',
          color: 'rgba(19,183,255,0.2)'
        },
        {
          name: '数字资产',
          value: 0,
          label: 'sensitive_count',
          color: 'rgba(255,121,0,0.2)',
          children: [
            {
              name: '公众号',
              value: 0,
              label: 'sensitive_wechat_count'
            },
            {
              name: 'APP',
              value: 0,
              label: 'sensitive_app_count'
            },
            {
              name: '小程序',
              value: 0,
              label: 'sensitive_applets_count'
            }
          ]
        },
        {
          name: 'IP',
          value: 0,
          label: 'ip_count',
          color: 'rgba(38,119,255,0.2)',
          children: [
            {
              name: '影子资产',
              value: 0,
              label: 'ip_not_sure_count'
            },
            {
              name: '已知资产',
              value: 0,
              label: 'ip_sure_count'
            }
          ]
        },
        {
          name: '登录入口',
          value: 0,
          label: 'login_count',
          color: 'rgba(112,95,255,0.2)'
        }
      ]
      let data = []
      let links = []
      var obj = {
        name: '资产暴露面',
        symbol: 'image://' + profile,
        symbolSize: ['78', '78'],
        draggable: false,
        itemStyle: {
          normal: {
            color: 'rgba(38,119,255,0.2)'
          }
        },
        label: {
          normal: {
            show: true, // 是否显示标签。
            textStyle: {
              fontSize: 16,
              fontWeight: 500
            }
          }
        }
      }
      data.push(obj)
      dataTmp.map((v) => {
        let objTmp = {
          name: v.name,
          value: value[v.label],
          symbolSize: ['55', '55'],
          symbol: this.setSymbolImg(v.name),
          draggable: false,
          itemStyle: {
            normal: {
              color: v.color
            }
          },
          label: {
            normal: {
              show: true, // 是否显示标签。
              // position:'bottom',
              formatter: (params) => {
                return `{a0|${params.data.value}}\n{a1|${params.data.name}}`
              },
              rich: {
                a0: {
                  color: '#ffff',
                  with: 56,
                  align: 'center',
                  // marginTop:40
                  lineHeight: 70,
                  padding: [26, 0, 0, 0],
                  fontSize: 14
                },
                a1: {
                  padding: [10, 0, 0, 0]
                }
              }
            }
          }
        }
        data.push(objTmp)
        let link = {
          source: '资产暴露面',
          target: v.name
        }
        links.push(link)
        if (v.children) {
          v.children.map((item) => {
            let objTmp1 = {
              name: item.name,
              value: value[item.label],
              symbolSize: 40,
              draggable: false,
              itemStyle: {
                normal: {
                  color: v.color,
                  borderColor: v.color,
                  borderWidth: 5
                }
              },
              label: {
                normal: {
                  show: true, // 是否显示标签。
                  // position:'bottom',
                  formatter: (params) => {
                    return `{a0|${params.data.value}}\n{a1|${params.data.name}}`
                  },
                  rich: {
                    a0: {
                      color: '#ffff',
                      with: 40,
                      align: 'center',
                      // marginTop:40
                      lineHeight: 70,
                      padding: [22, 0, 0, 0],
                      fontSize: 14
                    },
                    a1: {
                      padding: [10, 0, 0, 0]
                    }
                  }
                }
              }
            }
            data.push(objTmp1)
            let link1 = {
              source: v.name,
              target: item.name
            }
            links.push(link1)
          })
        }
      })
      return { data, links }
    },
    setSymbolImg(name) {
      if (name == '登录入口') {
        return 'image://' + purple
      } else if (name == '域名') {
        return 'image://' + green
      } else if (name == 'IP') {
        return 'image://' + blue
      } else if (name == '证书') {
        return 'image://' + blue2
      } else if (name == '数字资产') {
        return 'image://' + orange
      }
    }
  },
  mounted() {
    // console.log(sessionStorage.getItem('currentCompany'))

    // this.currentCompany = JSON.parse(sessionStorage.getItem('currentCompany'))
    //   ? JSON.parse(sessionStorage.getItem('currentCompany')).id
    //   : ''
    this.getDataList()
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
}
</style>
