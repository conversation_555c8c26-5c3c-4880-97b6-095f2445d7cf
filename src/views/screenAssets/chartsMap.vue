<template>
  <div
    class="wrapper"
    id="chartsMap"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
  </div>
</template>

<script>
import { screenMapChart } from '@/api/apiConfig/screen.js'

export default {
  data() {
    return {
      myChart: null,
      mapZoomVal: 1.22,
      mapCenterVal: null,
      timer: null,
      loading: true,
      dataLength: 33,
      currentCompany: '',
      index: 0,
      dataList: [],
      dataListMap: [],
      geoCoordMap: {
        上海: [121.4648, 31.2891],
        山西: [112.549248, 37.857014],
        新疆: [84.738775, 40.791562],
        甘肃: [103.5901, 36.3043],
        内蒙古: [110.3467, 41.4899],
        北京: [116.4551, 40.2539],
        广西: [108.80244, 23.776873],
        江西: [115.278288, 27.937219],
        陕西: [108.4131, 34.8706],
        黑龙江: [128.569752, 47.108444],
        天津: [117.4219, 39.4189],
        安徽: [117.0123, 32.2121],
        四川: [101.848261, 30.646339],
        西藏: [88.786181, 30.963961],
        云南: [101.811467, 24.956574],
        湖南: [111.598829, 28.328652],
        青海: [96.8038, 36.2207],
        贵州: [106.6992, 26.7682],
        重庆: [107.293861, 29.719279],
        吉林: [125.8154, 43.6584],
        香港: [115.1215, 22.1235],
        澳门: [112.1211, 22.2111],
        台湾: [120.1111, 23.2435],
        湖北: [112.077158, 30.995665],
        河南: [113.475353, 34.233691],
        江苏: [119.546461, 32.223755],
        山东: [117.485964, 36.106822],
        福建: [118.001088, 26.224563],
        浙江: [119.693639, 29.397571],
        河北: [114.983931, 38.168723],
        广东: [113.144202, 22.825228],
        辽宁: [122.82118, 41.43163],
        海南: [109.611921, 19.093147],
        宁夏: [105.969256, 37.497501]
      }
    }
  },
  methods: {
    chartsInit() {
      let _this = this
      this.myChart = this.$echarts.init(document.getElementById('chartsMap'))
      // 默认series
      let series = [
        {
          type: 'map',
          map: 'china',
          zoom: this.mapZoomVal,
          center: this.mapCenterVal,
          //  left: '18.5%',
          //  top:'8.7%',
          //  align: 'right',
          label: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          //  animation: false,
          itemStyle: {
            color: 'transparent',
            normal: {
              borderColor: '#3CE1FF', //省市边界线
              shadowColor: 'none',
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              shadowBlur: 120,
              areaColor: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.8,
                colorStops: [
                  {
                    offset: 0.74,
                    color: 'rgba(0,50,129,0.74)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#0067C9' // 100% 处的颜色
                  }
                ],
                globalCoord: false // 缺省为 false
              }
            },
            emphasis: {
              areaColor: '#2677FF '
            }
          },
          data: this.setDatatList(this.dataList)
        }
      ]
      this.dataList.map((item, i) => {
        series.push({
          type: 'effectScatter',
          coordinateSystem: 'geo',
          name: item.name,
          clickable: false,
          //  rippleEffect:{
          //   color:'yellow'
          //  },
          //  zlevel: ,
          // zlevel: 2,
          rippleEffect: {
            color: _this.setCircleList(item.risks_num).color,
            //涟漪特效
            period: 4, //动画时间，值越小速度越快
            brushType: 'stroke', //波纹绘制方式 stroke, fill
            scale: 3 //波纹圆环最大限制，值越大波纹越大
          },
          label: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          symbol: 'circle',
          itemStyle: {
            color: _this.setCircleList(item.risks_num).color
            // borderWidth: 20,
            // borderColor: 'rgba(79, 141, 245, 0.05)',
          },
          symbolSize: function (val) {
            return _this.setCircleList(val[2], val[3]).symbol //圆环大小
          },
          data: [
            {
              name: item.name,
              value: this.geoCoordMap[item.name].concat([item.risks_num, item.asset_count])
            }
          ]
        })
      })
      let options = {
        geo: {
          map: 'china',
          show: false,
          //  roam: true, //是否允许缩放
          zoom: this.mapZoomVal,

          center: this.mapCenterVal,
          //  left: '18.5%',
          //  top:'8.7%',
          //  align: 'right',
          regions: [
            {
              name: '南海诸岛',
              value: 0,
              itemStyle: {
                normal: {
                  opacity: 0,
                  label: {
                    show: false
                  }
                }
              }
            }
          ]
        },
        series: series,

        tooltip: {
          // triggerOn:'click',
          trigger: 'item',
          backgroundColor: 'none',
          // borderColor: '#2ABEFF',
          // borderWidth: 1,
          padding: [0, 0, 0, 0],
          // textStyle: {
          //     color: '#E3EEFF',
          //     fontSize: 16
          // },
          // appendToBody: true,
          formatter: (params) => {
            if (params.data) {
              if (params.data.value_assets != 0) {
                if (params.data.value) {
                  params.data.value_assets = params.data.value[3]
                  params.data.value_risks = params.data.value[2]
                }
                let tip = ''
                tip += '<div class="tips">'
                tip += '<p class="tips-title">' + params.data.name + '</p>'
                tip +=
                  '<p class="tips-title1"><span>资产：</span>' + params.data.value_assets + '</p>'
                tip +=
                  '<p class="tips-title2"><span>风险：</span>' + params.data.value_risks + '</p>'
                tip += '</div>'
                return tip
              } else {
                return ''
              }
            }
          }
          // axisPointer:{
          //   animation:false
          // }
        }
      }
      this.myChart.setOption(options)
      this.loading = false
      window.addEventListener('resize', () => {
        if (this.myChart != null) {
          this.myChart.resize()
        }
      })
      this.myChart.on('mouseover', (e) => {
        for (let i = 0; i < this.dataLength; i++) {
          if (i != e.dataIndex) {
            this.myChart.dispatchAction({
              type: 'downplay',
              seriesIndex: 0,
              dataIndex: i
            })
          }
        }
        if (e.dataIndex != this.index) {
          this.index = e.dataIndex
          this.myChart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: this.index
          })
        }
        clearInterval(this.timer)
      })
      this.myChart.on('mouseout', (e) => {
        clearInterval(this.timer)
        this.index = e.dataIndex
        this.myChart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: e.dataIndex
        })
        clearInterval(this.timer)
        if (this.index + 1 > this.dataLength - 1) {
          this.index = 0
        } else {
          this.index += 1
        }
        this.setInterFun()
      })
      this.myChart.on('georoam', (params) => {
        var _option = this.myChart.getOption()
        this.mapZoomVal = _option.geo[0].zoom
        this.mapCenterVal = _option.geo[0].center

        if (params.zoom != null && params.zoom != undefined) {
          //捕捉到缩放时

          _option.series[0].zoom = _option.geo[0].zoom //下层geo的缩放等级跟着上层的geo一起改变

          _option.series[0].center = _option.geo[0].center //下层的geo的中心位置随着上层geo一起改变
        } else {
          _option.series[0].center = _option.geo[0].center
        }
        this.myChart.setOption(_option)
      })
    },
    setDatatList(data) {
      var res = []
      data.map((v) => {
        res.push({ name: v.name, value_risks: v.risks_num, value_assets: v.asset_count })
      })
      return res
    },
    setInterFun() {
      this.timer = setInterval(() => {
        this.myChart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: this.index
        })
        // 取消高亮指定的数据图形
        this.myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.index == 0 ? this.dataLength - 1 : this.index - 1
        })
        this.myChart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: this.index
        })
        this.index++
        if (this.index > this.dataLength - 1) {
          this.index = 0
        }
      }, 3000)
    },
    async getApiList() {
      let res = await screenMapChart({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.dataLength = res.data.length
        this.dataList = res.data
        this.chartsInit()
        this.setInterFun()
      }
      //  operate_company_id:this.currentCompany
    },
    setCircleList(val, valTmp) {
      let obj = {
        symbol: 0,
        color: ''
      }
      if (valTmp == 0) {
        obj = {
          symbol: 0,
          color: ''
        }
      } else {
        if (val >= 0 && val <= 25) {
          obj.symbol = 8
          obj.color = '#10D595'
        } else if (val >= 26 && val <= 50) {
          obj.symbol = 10
          obj.color = '#F8C136'
        } else if (val >= 51 && val <= 75) {
          obj.symbol = 12
          obj.color = '#FF7900'
        } else if (val >= 76 && val <= 100) {
          obj.symbol = 14
          obj.color = '#FF4646'
        }
      }

      return obj
    }
  },
  mounted() {
    this.currentCompany = JSON.parse(sessionStorage.getItem('currentCompany'))
      ? JSON.parse(sessionStorage.getItem('currentCompany')).id
      : ''
    this.getApiList()
    // this.chartsInit()
    // this.setInterFun()
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  position: relative;
  z-index: 1;
}
</style>
