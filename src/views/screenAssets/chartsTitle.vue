<template>
  <div class="wrapper">
    <div class="wrapperTitleBox" v-if="titleFlag == 'left'">
      <div class="diamondShape">
        <img src="../../assets/images/screenImage/diamondShape.png" alt="" />
        <span>{{ titleText }}</span>
      </div>
      <div class="titlebrake">
        <img src="../../assets/images/screenImage/titlebrake.png" alt="" />
      </div>
    </div>
    <div class="wrapperTitleBox wrapperTitleBoxCenter" v-if="titleFlag == 'leftSpecil'">
      <div class="diamondShape">
        <img src="../../assets/images/screenImage/diamondShape.png" alt="" />
        <span>{{ titleText }}</span>
      </div>
      <div class="titlebrake">
        <img src="../../assets/images/screenImage/titlebrake.png" alt="" />
      </div>
    </div>
    <div class="wrapperTitleBox wrapperTitleBox1" v-if="titleFlag == 'right'">
      <div class="titlebrake">
        <img src="../../assets/images/screenImage/titlebrake.png" alt="" />
      </div>
      <div class="diamondShape">
        <img src="../../assets/images/screenImage/diamondShape.png" alt="" />
        <span>{{ titleText }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['titleFlag', 'titleText']
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 60px;
  .wrapperTitleBox {
    width: 100%;
    height: 100%;
    background-image: url('../../assets/images/screenImage/titleImg.png');
    background-size: 100% 100%;
    // overflow: hidden;
    display: flex;
    justify-content: space-between;
    // align-items: center;
    box-sizing: border-box;
    // align-items: center;
    padding: 8px 24px;
    .titlebrake {
      width: 53px;
      height: 16px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .diamondShape {
      img {
        width: 11px;
        height: 14px;
        position: relative;
        top: -3px;
      }
      font-size: 14px;
      // font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #e3eeff;
      text-shadow: 0px 0px 2px rgba(38, 119, 255, 0.7);
      span {
        margin-left: 6px;
      }
    }
  }
  .wrapperTitleBoxCenter {
    background-image: url('../../assets/images/screenImage/centerTitleImg.png');
    background-size: 100% 100%;
    padding: 8px 24px;
  }
  .wrapperTitleBox1 {
    background-image: url('../../assets/images/screenImage/titleImg1.png');
    background-size: 100% 100%;
    padding: 8px 24px;
  }
}
</style>
