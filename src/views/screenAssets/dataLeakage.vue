<template>
  <div class="mainBox">
    <div
      class="wrapper"
      id="dataLeakage"
      v-loading="loading"
      element-loading-background="rgba(0, 0, 0, 0)"
      v-if="!nullFlag"
    >
    </div>
    <div class="wrapperSpecial" v-if="nullFlag">
      <img src="../../assets/images/screenImage/nullImg.png" alt="" />
      <span style="margin-top: 8px; color: #8ca3c2">暂无数据</span>
    </div>
  </div>
</template>

<script>
import { screenCuleMapData } from '@/api/apiConfig/screen.js'
import { cluesGroupList, getCuleMapDataChildren } from '@/api/apiConfig/clue.js'

export default {
  data() {
    return {
      tmpFlag: false,
      currentCompany: '',
      group_id: '',
      myChart: null,
      dataList: [],
      timer: null,
      dataLength: 0,
      index: 0,
      loading: true,
      nullFlag: false
    }
  },
  methods: {
    async getGroupList() {
      let obj = {
        page: 1,
        per_page: 10,
        no_page: 1,
        operate_company_id: this.currentCompany
      }
      let res = await cluesGroupList(obj)
      if (res.data.length != 0) {
        this.group_id = res.data[0].id
      }
      this.getClueMap()
    },
    async getClueMap() {
      let res = await screenCuleMapData({
        group_id: Number(this.group_id),
        data: { operate_company_id: this.currentCompany }
      })

      if (!res.data || res.data.length == 0) {
        this.nullFlag = true
        this.loading = false
      } else {
        this.dataList = this.setMapData(res.data)

        // this.dataLength = this.dataList.data.length
        this.setmyChart()
        // this.setInterFun()
      }
    },
    setInterFun() {
      this.timer = setInterval(() => {
        this.myChart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: this.index
        })
        // 取消高亮指定的数据图形
        this.myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.index == 0 ? this.dataLength - 1 : this.index - 1
        })
        this.myChart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: this.index
        })
        this.index++
        if (this.index > this.dataLength - 1) {
          this.index = 0
        }
      }, 3000)
    },
    setMapData(data) {
      let datanodesTmp = []
      let dataedgesTmp = []
      data.map((v, index) => {
        if (v.name != null && v.children.length > 0) {
          datanodesTmp.push({ id: v.name, category: '-1', symbolSize: 25, name: '单位' })
          v.children.map((item) => {
            // 是否是ip
            let obj = {}
            if (item.type == 5) {
              item.symbolSize = 15
            } else {
              item.symbolSize = 15
            }
            item.id = String(item.id) + index // id有重复造成报错，添加一个随机值index
            //  item.symbolSize=15
            item.category = String(item.type)
            datanodesTmp.push(item)
            if (item.is_ip == 1) {
              obj = { ...item }
              obj.id = obj.id + 'is_ip'
              obj.category = '-2'
              obj.name = 'IP'
              datanodesTmp.push(obj)
              // 先连接父级 然后链接子集
              dataedgesTmp.push({ source: v.name, target: String(obj.id) })
              dataedgesTmp.push({ source: obj.id, target: String(item.id) })
            } else {
              dataedgesTmp.push({ source: v.name, target: String(item.id) })
            }
          })
        }
      })
      return { nodes: datanodesTmp, edges: dataedgesTmp }
    },
    // 处理子集数据
    setMapDataChildren(data, parent) {
      data.map((v) => {
        // 是否是ip
        let obj = {}
        if (v.type == 5) {
          v.symbolSize = 20
        } else {
          v.symbolSize = 15
        }
        v.category = String(v.type)
        v.id = String(v.id)
        this.dataList.nodes.push(v)
        if (v.is_ip == 1) {
          obj = { ...v }
          obj.id = obj.id + 'is_ip'
          obj.category = '-2'
          obj.name = 'IP'
          this.dataList.nodes.push(obj)
          // 先连接父级 然后链接子集
          this.dataList.edges.push({ source: parent.id, target: String(obj.id) })
          this.dataList.edges.push({ source: obj.id, target: String(v.id) })
        } else {
          this.dataList.edges.push({ source: parent.id, target: String(v.id) })
        }
      })
      this.myChart.setOption({
        series: [
          {
            data: this.dataList.nodes,
            links: this.dataList.edges
          }
        ]
      })
      // this.loading = false
    },
    setmyChart() {
      if (this.myChart !== null) {
        this.myChart.clear()
      }
      let _this = this
      this.myChart = this.$echarts.init(document.getElementById('dataLeakage'))
      let data = this.dataList.data
      let links = this.dataList.links
      let options = {
        label: {
          normal: {
            show: true
          }
        },
        tooltip: {
          show: true,
          formatter: function (params) {
            if (params.data.category == '-1') {
              return params.data.id
            } else if (params.data.category == '0') {
              return '域名：' + params.data.content
            } else if (params.data.category == '1') {
              return '证书：' + params.data.content
            } else if (params.data.category == '2') {
              return 'ICP：' + params.data.content
            } else if (params.data.category == '3') {
              return '<img src=' + _this.showSrcIp + params.data.content + '></img/>'
            } else if (params.data.category == '4') {
              return '关键词：' + params.data.content
            } else if (params.data.category == '5') {
              return '子域名：' + params.data.content
            } else if (params.data.category == '6') {
              return 'IP段：' + params.data.content
            } else if (params.data.category == '-2') {
              return 'IP'
            }
          }
        },
        // 图表标题
        title: {
          show: false, //显示策略，默认值true,可选为：true（显示） | false（隐藏）
          text: '', //主标题文本，'\n'指定换行
          animationDuration: 3000,
          animationEasingUpdate: 'quinticInOut',
          itemGap: 10, // 主副标题纵向间隔，单位px，默认为10，
          textStyle: {
            fontSize: 50,
            fontWeight: 'bolder',
            color: '#fff' // 主标题文字颜色
          },
          subtextStyle: {
            color: '#aaa' // 副标题文字颜色
          }
        },
        animation: false,
        series: [
          {
            type: 'graph',
            name: 'name',
            layout: 'force',
            force: {
              //元素之间的间距
              repulsion: 200
            },
            lineStyle: {
              // ========关系边的公用线条样式。
              normal: {
                color: 'rgba(38, 119, 255, 0.6)',
                width: '1', //线的粗细
                type: 'solid', // 线的类型 'solid'（实线）'dashed'（虚线）'dotted'（点线）
                curveness: 0, // 线条的曲线程度，从0到1
                opacity: 1 // 图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。默认0.5
              },
              emphasis: {
                // 高亮状态
              }
            },
            roam: true,
            // focusNodeAdjacency: true,//鼠标悬停在节点上时，会隐藏和当前节点非直接连接的节点
            categories: [
              {
                //节点类别
                label: {
                  show: true
                }
              },
              {
                name: '-1',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#26C5FF' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#2677FF' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                },
                label: {
                  show: false
                }
              },
              {
                name: '0',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(38, 118, 255, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(38, 157, 255, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              },
              {
                name: '1',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(19, 183, 255, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(18, 227, 255, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              },
              {
                name: '2',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: ' rgba(16, 213, 149, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(16, 232, 210, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              },
              {
                name: '3',
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: 'rgba(255, 121, 0, 1)' // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: 'rgba(255, 191, 0, 1)' // 100% 处的颜色
                      }
                    ],
                    global: false // 缺省为 false
                  }
                }
              },
              {
                name: '4',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(255, 70, 70, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(255, 168, 168, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              },
              {
                name: '5',
                itemStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(39, 85, 153, 1)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(34, 111, 227, 1)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  }
                }
              }
            ],
            data: _this.dataList.nodes,
            links: _this.dataList.edges
            // lineStyle: {
            //   //关系连接线的样式设置
            //   normal: {
            //     opacity: 0.9, //关系连接线的不透明度为0.9
            //     width: 1, //关系连接线的宽度
            //     curveness: 0 //关系连接线的弯曲程度
            //   }
            // }
          }
        ]
      }
      this.myChart.setOption(options)
      this.loading = false
      this.myChart.on('click', (param) => {
        if (param.borderColor !== undefined) {
          return ''
        } else {
          if (param.data.category == -1) {
            _this.$message.error('单位数据已默认全部展开')
          } else if (param.data.is_show <= 0 || param.data.id == undefined) {
            _this.$message.error('此节点无子集数据')
          } else {
            // param.data.id.substr(0, param.data.id.length - 1) id随机拼接了一个数字，查询时要去掉
            _this.getChildrenData(param.data.id.substr(0, param.data.id.length - 1), param.data)
          }
        }
      })
      // this.myChart.on('mouseover', (e) => {
      //   for (let i = 0; i < this.dataLength; i++) {
      //     if (i != e.dataIndex) {
      //       this.myChart.dispatchAction({
      //         type: 'downplay',
      //         seriesIndex: 0,
      //         dataIndex: i
      //       })
      //     }
      //   }
      //   if (e.dataIndex != this.index) {
      //     this.index = e.dataIndex
      //     this.myChart.dispatchAction({
      //       type: 'highlight',
      //       seriesIndex: 0,
      //       dataIndex: this.index
      //     })
      //   }
      //   clearInterval(this.timer)
      // })
      // this.myChart.on('mouseout', (e) => {
      //   clearInterval(this.timer)
      //   this.index = e.dataIndex
      //   this.myChart.dispatchAction({
      //     type: 'highlight',
      //     seriesIndex: 0,
      //     dataIndex: e.dataIndex
      //   })
      //   clearInterval(this.timer)
      //   if (this.index + 1 > this.dataLength - 1) {
      //     this.index = 0
      //   } else {
      //     this.index += 1
      //   }
      //   this.setInterFun()
      // })
      window.addEventListener('resize', () => {
        if (this.myChart != null) {
          this.myChart.resize()
        }
      })
    },
    // 获取子集数据
    async getChildrenData(id, parent) {
      this.loading = true
      let res = await getCuleMapDataChildren({
        id,
        data: { group_id: this.groupId, operate_company_id: this.currentCompany }
      })
      if (res.data && res.data.length != 0) {
        let obj = res.data[0]
        this.dataList.nodes.map((v) => {
          if (v.id == String(obj.id)) {
            this.$message.error('当前节点已展开')
            this.tmpFlag = true
            this.loading = false
          }
        })
        if (!this.tmpFlag) {
          this.setMapDataChildren(res.data, parent)
        } else {
          this.loading = false
        }
      } else {
        this.loading = false
      }
    },
    // 处理子集数据
    setMapDataChildren(data, parent) {
      if (!data) {
        this.loading = false
      }
      data &&
        data.map((v) => {
          // 是否是ip
          let obj = {}
          if (v.type == 5) {
            v.symbolSize = 20
          } else {
            v.symbolSize = 15
          }
          v.category = String(v.type)
          v.id = String(v.id)
          this.dataList.nodes.push(v)
          if (v.is_ip == 1) {
            obj = { ...v }
            obj.id = obj.id + 'is_ip'
            obj.category = '-2'
            obj.name = 'IP'
            this.dataList.nodes.push(obj)
            // 先连接父级 然后链接子集
            this.dataList.edges.push({ source: parent.id, target: String(obj.id) })
            this.dataList.edges.push({ source: obj.id, target: String(v.id) })
          } else {
            this.dataList.edges.push({ source: parent.id, target: String(v.id) })
          }
        })
      this.myChart.setOption({
        series: [
          {
            data: this.dataList.nodes,
            links: this.dataList.edges
          }
        ]
      })
    }
  },

  mounted() {
    if (JSON.parse(sessionStorage.getItem('userMessage')).company == null) {
      this.currentCompany = JSON.parse(sessionStorage.getItem('currentCompany'))
        ? JSON.parse(sessionStorage.getItem('currentCompany')).id
        : ''
    } else {
      this.currentCompany = JSON.parse(sessionStorage.getItem('userMessage')).company.id
    }
    this.getGroupList()
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
}
.mainBox {
  width: 100%;
  height: 100%;
}
.wrapperSpecial {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #2677ff;
}
</style>
