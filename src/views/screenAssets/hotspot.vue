<template>
  <div class="mainBox">
    <div
      class="wrapper"
      id="hotspot"
      v-loading="loading"
      element-loading-background="rgba(0, 0, 0, 0)"
      v-if="!nullFlag"
    >
    </div>
    <div class="wrapperSpecial" v-if="nullFlag">
      <img src="../../assets/images/screenImage/nullImg.png" alt="" />
      <span style="margin-top: 8px; color: #8ca3c2">暂无数据</span>
    </div>
  </div>
</template>

<script>
import img1 from '../../assets/images/screenImage/hotImg1.png'
import img2 from '../../assets/images/screenImage/hotImg2.png'
import img3 from '../../assets/images/screenImage/hotImg3.png'
import { screenHotspotChart } from '@/api/apiConfig/screen.js'

export default {
  data() {
    return {
      myCharts: null,
      timer: null,
      index: 0,
      currentCompany: '',
      loading: false,
      nullFlag: false
    }
  },
  methods: {
    async setChartsList() {
      let data = []
      let res = await screenHotspotChart({ operate_company_id: this.currentCompany })
      if (res.data.length == 0) {
        this.loading = false
        this.nullFlag = true
        return
      } else {
        this.loading = true
        res.data.map((v) => {
          data.push(v)
        })
      }
      // 分割数组
      let dataTmp = []
      let dataTmp1 = []
      if (data.length > 0) {
        if (data.length <= 10) {
          dataTmp = data
        } else {
          dataTmp = data.slice(0, 10)
          dataTmp1 = data.slice(10, 20)
        }
      }

      if (this.myCharts == null) {
        this.chartsInit(this.setChartsStyle(dataTmp))
      }
      if (dataTmp.length > 0 && dataTmp1.length == 0) {
        this.chartsInit(this.setChartsStyle(dataTmp))
      } else if (dataTmp.length == 0) {
      } else {
        this.timer = setInterval(() => {
          if (this.index == 1) {
            this.chartsInit(this.setChartsStyle(dataTmp1))
          } else {
            this.chartsInit(this.setChartsStyle(dataTmp))
          }
        }, 5000)
      }
    },
    // 随机设置元素样式方
    setChartsStyle(data) {
      let len = data.length
      let canDraggable = true
      let dataTmp = []
      data.map((v, i) => {
        if (i <= 1) {
          dataTmp.push({
            name: v,
            value: 2,
            symbolSize: 0,
            draggable: canDraggable,
            symbol: 'image://' + img2,
            symbolSize: ['119', '24'],
            label: {
              fontSize: 12,
              color: '#E3EEFF'
            }
          })
        } else if (i >= 2 && i <= 5) {
          dataTmp.push({
            name: v,
            value: 2,
            symbolSize: 0,
            draggable: canDraggable,
            symbol: 'image://' + img3,
            symbolSize: ['119', '24'],
            label: {
              fontSize: 12,
              color: '#E3EEFF'
            }
          })
        } else {
          dataTmp.push({
            name: v,
            value: 2,
            symbolSize: 0,
            draggable: canDraggable,
            symbolSize: 0,
            label: {
              fontSize: 10,
              color: '#E3EEFF'
            }
          })
        }
      })
      let random = Math.floor(Math.random() * len)
      dataTmp[random] = {
        name: data[random],
        value: 2,
        symbolSize: 0,
        draggable: canDraggable,
        symbol: 'image://' + img1,
        symbolSize: ['135', '32'],
        label: {
          fontSize: 14,
          color: '#E3EEFF'
        }
      }
      return dataTmp
    },

    chartsInit(value) {
      if (this.myCharts !== null) {
        this.myCharts.clear()
      }
      this.myCharts = this.$echarts.init(document.getElementById('hotspot'))
      let options = {
        color: ['#37A2DA', '#32C5E9', '#67E0E3'],
        series: [
          {
            type: 'graph',
            layout: 'force',
            force: {
              repulsion: 50,
              edgeLength: 30
            },
            roam: 'scale',
            label: {
              show: true,
              color: 'auto',
              fontSize: 14
            },
            data: value
          }
        ]
      }
      this.myCharts.setOption(options)
      this.loading = false
      this.index = this.index == 1 ? 0 : 1
      window.addEventListener('resize', () => {
        if (this.myCharts != null) {
          this.myCharts.resize()
        }
      })
    }
  },
  mounted() {
    // this.chartsInit()
    this.currentCompany = JSON.parse(sessionStorage.getItem('currentCompany'))
      ? JSON.parse(sessionStorage.getItem('currentCompany')).id
      : ''
    this.setChartsList()
  },
  beforeDestroy() {
    clearInterval(this.timer)
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
}
.mainBox {
  width: 100%;
  height: 100%;
}
.wrapperSpecial {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #2677ff;
}
</style>
