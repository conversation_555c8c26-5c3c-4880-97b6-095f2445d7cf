<template>
  <div class="mainBox">
    <div
      class="wrapper"
      id="leakageDistribution"
      v-loading="loading"
      element-loading-background="rgba(0, 0, 0, 0)"
      v-if="!nullFlag"
    >
    </div>
    <div class="wrapperSpecial" v-if="nullFlag">
      <img src="../../assets/images/screenImage/nullImg.png" alt="" />
      <span style="margin-top: 8px; color: #8ca3c2">暂无数据</span>
    </div>
  </div>
</template>

<script>
export default {
  props: ['dataList'],
  data() {
    return {
      myCharts: null,
      loading: true,
      nullFlag: false
    }
  },
  watch: {
    dataList(val) {
      let numFlag = 0
      val.map((v) => {
        numFlag = numFlag + v.rate
      })
      if (Number(numFlag) == 0) {
        this.loading = false
        this.nullFlag = true
      } else {
        this.chartsInit(val)
      }
    }
  },
  computed: {},
  methods: {
    chartsInit(value) {
      if (this.myCharts !== null) {
        this.myCharts.clear()
      }
      this.myCharts = this.$echarts.init(document.getElementById('leakageDistribution'))
      var data = value
      var nameList = []
      var dataList = []
      data.forEach((item) => {
        nameList.push(item.name)
        if (item.name == '网盘') {
          dataList.push({
            value: parseInt(item.rate).toFixed(0),
            itemStyle: {
              normal: {
                //渐变色
                color: {
                  type: 'bar',
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(38,119,255,1)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(38,119,255,0.1)'
                    }
                  ]
                }
              }
            }
          })
        } else if (item.name == '文库') {
          dataList.push({
            value: parseInt(item.rate).toFixed(0),
            itemStyle: {
              normal: {
                //渐变色
                color: {
                  type: 'bar',
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(19,183,255,1)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(19,183,255,0.1)'
                    }
                  ]
                }
              }
            }
          })
        } else if (item.name == '代码仓库') {
          dataList.push({
            value: parseInt(item.rate).toFixed(0),
            itemStyle: {
              normal: {
                //渐变色
                color: {
                  type: 'bar',
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(112,95,255,1)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(112,95,255,0.1)'
                    }
                  ]
                }
              }
            }
          })
        }
      })
      var options = {
        grid: {
          left: '0%',
          right: '4%',
          bottom: '9%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          data: nameList,
          triggerEvent: true,
          axisTick: {
            show: false
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#70F5FE'
            }
          },
          axisLabel: {
            show: true,
            rotate: 0,
            interval: 0,
            textStyle: {
              padding: [8, 0, 0, 0],
              fontSize: 14,
              color: '#E3EEFF'
            }
          }
        },
        yAxis: {
          minInterval: 1,
          name: '',
          triggerEvent: true,
          nameTextStyle: {
            color: 'rgba(255,255,255,1)',
            fontSize: 16,
            padding: [0, 0, 10, -20]
          },
          label: {
            show: true,
            position: 'top',
            distance: 5,
            color: '#202d40',
            fontSize: 12
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#A6D9F6',
              type: 'dashed'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#E3EEFF',
              fontSize: 14
            }
          }
        },
        series: [
          {
            name: '',
            // barMinHeight: 5,
            type: 'pictorialBar',
            barCategoryGap: '40%',
            symbol: 'path://M0,10 L10,10 C9.5,10 5.5,5 5,0 C4.5,5 0.5,10 0,10 z',
            label: {
              show: true,
              position: 'top',
              // distance: 5,
              color: '#ffff',
              fontSize: 14
            },
            data: dataList,
            z: 10
          }
        ]
      }

      this.myCharts.setOption(options)
      this.loading = false
      window.addEventListener('resize', () => {
        if (this.myCharts != null) {
          this.myCharts.resize()
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.mainBox {
  width: 100%;
  height: 100%;
}
.wrapper {
  width: 100%;
  height: 100%;
}
.wrapperSpecial {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #2677ff;
}
</style>
