<template>
  <div
    class="wrapper"
    id="loopholeDistribution"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  ></div>
</template>

<script>
export default {
  props: ['loopholePieList'],
  data() {
    return {
      myChart: null,
      timer: null,
      dataLength: 0,
      index: 0,
      loading: true,
      colorList: [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(200, 17, 17, 0.44)' // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(200, 17, 17, 1)' // 100% 处的颜色
            }
          ],
          globalCoord: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 1,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(255, 70, 70, 1)' // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(255, 70, 70, 0.6)' // 100% 处的颜色
            }
          ],
          globalCoord: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 1,
          y: 0,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(255, 121, 0, 1)' // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(255, 121, 0, 0.6)' // 100% 处的颜色
            }
          ],
          globalCoord: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 1,
          y: 0,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(248, 193, 54, 0.6)' // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(248, 193, 54, 1)' // 100% 处的颜色
            }
          ],
          globalCoord: false // 缺省为 false
        }
      ]
    }
  },
  watch: {
    loopholePieList(val) {
      this.dataLength = 4
      this.chartsInit(val)
      this.setInterFun()
      window.addEventListener('resize', () => {
        if (this.myChart != null) {
          this.myChart.resize()
        }
      })
    }
  },
  computed: {},
  methods: {
    setInterFun() {
      this.timer = setInterval(() => {
        this.myChart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: this.index
        })
        // 取消高亮指定的数据图形
        this.myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.index == 0 ? this.dataLength - 1 : this.index - 1
        })
        this.myChart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: this.index
        })
        this.index++
        if (this.index > this.dataLength - 1) {
          this.index = 0
        }
      }, 3000)
    },
    chartsInit(val) {
      let _this = this
      this.myChart = this.$echarts.init(document.getElementById('loopholeDistribution'))
      let options = {
        grid: {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['40%', '65%'],
            minAngle: 18,
            avoidLabelOverlap: false,
            itemStyle: {
              color: function (params) {
                return _this.colorList[params.dataIndex]
              }
            },
            // roseType: 'area', // 玫瑰图
            labelLine: {
              show: true
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 13.5,
                fontWeight: 'bold'
              }
            },
            label: {
              normal: {
                show: false,
                position: 'center',
                formatter: '{value|{d}%}\n{value|{b}}\n{label|{c}}',
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 16,
                    textBorderWidth: 0,
                    color: '#ffff'
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 14,
                    textBorderWidth: 0,
                    color: '#ffff'
                  }
                }
              }
              // emphasis: {
              //     show: true,
              //     textStyle: {
              //         fontSize: '12',
              //     },
              // },
            },
            data: val
          }
        ]
      }
      this.myChart.setOption(options)
      this.loading = false
      this.myChart.on('mouseover', (e) => {
        for (let i = 0; i < this.dataLength; i++) {
          if (i != e.dataIndex) {
            this.myChart.dispatchAction({
              type: 'downplay',
              seriesIndex: 0,
              dataIndex: i
            })
          }
        }
        if (e.dataIndex != this.index) {
          this.index = e.dataIndex
          this.myChart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: this.index
          })
        }
        clearInterval(this.timer)
      })
      this.myChart.on('mouseout', (e) => {
        clearInterval(this.timer)
        this.index = e.dataIndex
        this.myChart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: e.dataIndex
        })
        clearInterval(this.timer)
        if (this.index + 1 > this.dataLength - 1) {
          this.index = 0
        } else {
          this.index += 1
        }
        this.setInterFun()
      })
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
}
</style>
