<template>
  <div class="mainBox">
    <div
      class="wrapper"
      v-loading="loading"
      element-loading-background="rgba(0, 0, 0, 0)"
      v-if="!nullFlag"
    >
      <div class="wrapperMain" v-for="(item, key) in loopDataList" :key="key">
        <div class="mainText">
          <el-tooltip class="item" effect="dark" :content="item.common_title" placement="top">
            <span>{{ item.common_title }}</span>
          </el-tooltip>
          <span>{{ item.common_title_count }}</span>
        </div>
        <div class="mainProgress">
          <el-progress
            :percentage="item.label"
            :color="item.color"
            :show-text="false"
          ></el-progress>
        </div>
      </div>
    </div>
    <div class="wrapperSpecial" v-if="nullFlag">
      <img src="../../assets/images/screenImage/nullImg.png" alt="" />
      <span style="margin-top: 8px; color: #8ca3c2">暂无数据</span>
    </div>
  </div>
</template>

<script>
export default {
  props: ['loopList'],
  data() {
    return {
      loopDataList: [],
      loading: true,
      nullFlag: false
    }
  },
  watch: {
    loopList(val) {
      if (val.length != 0) {
        this.setValueData(val)
      } else {
        this.nullFlag = true
      }
    }
  },
  computed: {},
  methods: {
    setValueData(value) {
      let colorArr = ['#2677FF', '#F8C136', '#FF7900', '#FF4646']
      let num = 0
      value.map((v) => {
        num += v['common_title_count']
      })
      if (value.length !== 0) {
        value.map((v, i) => {
          v.color = colorArr[v.item]
          v.label = (v['common_title_count'] / num) * 100
        })
      }
      this.loopDataList = value
      this.loading = false
    }
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0 16px 20px 16px;
  .wrapperMain {
    width: 100%;
    height: 20%;
  }
  /deep/ .el-progress-bar__outer {
    // 修改进度条背景色
    background-color: rgba(51, 116, 176, 0.32);
  }
}
.mainText {
  display: flex;
  justify-content: space-between;
}
.mainText > span:nth-child(1) {
  width: 70%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.mainProgress {
  margin-top: 4px;
}
.mainBox {
  width: 100%;
  height: 100%;
}
.wrapperSpecial {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #2677ff;
}
</style>
