<template>
  <div class="mainBox">
    <div
      class="wrapper"
      v-loading="loading"
      element-loading-background="rgba(0, 0, 0, 0)"
      v-if="!nullFlag"
    >
      <vue-seamless-scroll
        :data="dataList"
        class="list-main"
        id="screenListBox"
        :class-option="classOption"
        ref="seamlessScroll"
      >
        <div
          v-for="(item, index) in dataList"
          :class="'wrapperMain wrapperMain' + item.type"
          :key="index"
        >
          <div class="leftBox">
            <i :class="'el-icon-warning elIcon elIcon' + item.type"></i>
            <p :class="'p' + item.type">{{ setType(item.type) }}</p>
            <el-tooltip class="item" effect="dark" :content="item.content.content" placement="top">
              <div>{{ item.content.content }}</div>
            </el-tooltip>
          </div>
          <div class="centerBox">
            <span>{{ item.type == 2 ? '关联关键词：' : '关联资产：' }}</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                item.type == 2 ? item.content.realted_ip : setCenterText(item.content.realted_ip)
              "
              placement="top"
            >
              <div class="ipTextBox">{{
                item.type == 2 ? item.content.realted_ip : setCenterText(item.content.realted_ip)
              }}</div>
            </el-tooltip>
          </div>
          <div class="rightBox">
            <div>{{ item.created_at }}</div>
          </div>
        </div>
      </vue-seamless-scroll>
    </div>
    <div class="wrapperSpecial" v-if="nullFlag">
      <img src="../../assets/images/screenImage/nullImg.png" alt="" />
      <span style="margin-top: 8px; color: #8ca3c2">暂无数据</span>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from 'vue-seamless-scroll'
import { screenRisksChart } from '@/api/apiConfig/screen.js'

export default {
  components: { vueSeamlessScroll },
  data() {
    return {
      timer: null,
      limitMoveNumMax: 0,
      currentCompany: '',
      nullFlag: false,
      classOption: {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: this.limitMoveNumMax, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      },
      dataList: [],
      loading: true
    }
  },
  methods: {
    async getDataList() {
      let res = await screenRisksChart({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        if (res.data.length != 0) {
          this.dataList = res.data.reverse()
          this.limitMoveNumMax = this.dataList.length
          this.loading = false
        } else {
          this.loading = false
          this.nullFlag = true
        }
      }
    },
    setType(type) {
      let str = ''
      if (type == 1) {
        str = '漏洞风险'
      } else if (type == 2) {
        str = '数据泄露'
      } else if (type == 3) {
        str = '资产风险'
      } else if (type == 4) {
        str = '资产信息'
      }
      return str
    },
    setCenterText(data) {
      let str = ''
      if (data.length != 0) {
        data.map((v) => {
          str += v + '，'
        })
        return str.substr(0, str.length - 1)
      } else {
        return ''
      }
    }
  },
  mounted() {
    this.currentCompany = JSON.parse(sessionStorage.getItem('currentCompany'))
      ? JSON.parse(sessionStorage.getItem('currentCompany')).id
      : ''
    this.getDataList()
  }
}
</script>
<style lang="less" scoped>
.mainBox {
  width: 100%;
  height: 100%;
}
.wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  // padding: 0 12px 12px 12px;
  display: flex;
  flex-direction: column;
  margin-left: 2%;
  .wrapperMain {
    width: 96%;
    height: 44px;
    margin-top: 12px;
    display: flex;
    box-sizing: border-box;
    padding: 0 12px 0 12px;
    .leftBox {
      width: 50%;
      height: 100%;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      p {
        width: 64px;
        height: 28px;
        border-radius: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 12px;
        font-size: 12px;
      }
      div {
        width: 60%;
        margin-left: 8px;
        font-size: 12px;
        // font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .p1 {
        color: #ff4646;
        border: 1px solid #ff4646;
      }
      .p2 {
        color: #ff7900;
        border: 1px solid #ff7900;
      }
      .p3 {
        color: #f8c136;
        border: 1px solid #f8c136;
      }
      .p4 {
        color: #10d595;
        border: 1px solid #10d595;
      }
    }
    .centerBox {
      width: 35%;
      height: 100%;
      display: flex;
      align-items: center;
      font-size: 12px;
      span {
        color: #e3eeff;
      }
      .ipTextBox {
        width: 65%;
        height: 100%;
        // display: flex;
        // align-items: center;
        line-height: 44px;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; // 默认不换行；
      }
    }
    .rightBox {
      width: 15%;
      height: 100%;
      // display: flex;
      // align-items: center;
      // justify-content: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 44px;
      color: rgba(227, 238, 255, 0.6);
      // overflow:hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
      div {
        width: 100%;
        height: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .wrapperMain1 {
    background: linear-gradient(270deg, rgba(255, 70, 70, 0.2) 0%, rgba(255, 70, 70, 0.08) 100%);
    border-radius: 4px;
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(255, 70, 70, 0.6), rgba(255, 70, 70, 0.2)) 1 1;
    backdrop-filter: blur(4px);
  }
  .wrapperMain2 {
    background: linear-gradient(270deg, rgba(255, 121, 0, 0.2) 0%, rgba(255, 121, 0, 0.08) 100%);
    border-radius: 4px;
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(255, 121, 0, 0.6), rgba(255, 121, 0, 0.2)) 1 1;
    backdrop-filter: blur(4px);
  }
  .wrapperMain3 {
    background: linear-gradient(270deg, rgba(248, 193, 54, 0.2) 0%, rgba(248, 193, 54, 0.08) 100%);
    border-radius: 4px;
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(248, 193, 54, 0.6), rgba(248, 193, 54, 0.2)) 1 1;
    backdrop-filter: blur(4px);
  }
  .wrapperMain4 {
    border-radius: 4px;
    border: 1px solid;
    background: linear-gradient(270deg, rgba(16, 213, 149, 0.2) 0%, rgba(16, 213, 149, 0.08) 100%);
    border-image: linear-gradient(270deg, rgba(16, 213, 149, 0.6), rgba(16, 213, 149, 0.2)) 1 1;
    backdrop-filter: blur(4px);
  }
}
.list-main {
  height: 100%;
  overflow: hidden;
}
.elIcon {
  font-size: 22px;
}
.elIcon1 {
  color: #ff4646;
}
.elIcon2 {
  color: #ff7900;
}
.elIcon3 {
  color: #f8c136;
}
.elIcon4 {
  color: #10d595;
}
#screenListBox > div > div {
  display: flex;
  justify-content: center;
  flex-direction: column;
}
// #screenListBox .wrapperMain:nth-child(1){
//   margin-top: 0;
// }
.wrapperSpecial {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #2677ff;
}
</style>
