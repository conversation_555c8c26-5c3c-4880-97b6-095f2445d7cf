<template>
  <div class="warrp">
    <div class="header">
      <div class="headerImg">
        <img src="../../assets/images/screenImage/headerText.png" alt="" />
      </div>
      <titleTime :titleTimer="titleTimer"></titleTime>
    </div>
    <div class="main clearfix">
      <div class="left">
        <div class="left-top">
          <chartsTitle :titleFlag="'left'" :titleText="'资产概况'"></chartsTitle>
          <div class="chartsBoxMain" :loaing="assetProfileLoaing">
            <assetProfile @setLeakageDistribution="setLeakageDistribution"></assetProfile>
          </div>
        </div>
        <div class="left-bottom">
          <chartsTitle :titleFlag="'left'" :titleText="'线索图谱'"></chartsTitle>
          <div class="chartsBoxMain">
            <dataLeakage></dataLeakage>
          </div>
        </div>
      </div>
      <div class="center">
        <div class="center-top">
          <div class="chartsBoxMain1 lengendBoxPostion">
            <div class="lengendBox">
              <p>风险值图例</p>
              <div v-for="(item, key) in lenghedList" :key="key" class="circalMainBox">
                <div :style="'background:' + item.color" class="circalBox"></div>
                <span>{{ item.name }}</span>
              </div>
            </div>
            <chartsMap></chartsMap>
          </div>
        </div>
        <div class="center-bottom">
          <chartsTitle :titleFlag="'leftSpecil'" :titleText="'风险事件'"></chartsTitle>
          <div class="chartsBoxMain">
            <riskEvent></riskEvent>
          </div>
        </div>
      </div>
      <div class="right rightTmp">
        <div class="right-top">
          <chartsTitle :titleFlag="'right'" :titleText="'白帽子关注热点'"></chartsTitle>
          <div class="chartsBoxMain">
            <hotspot></hotspot>
          </div>
        </div>
        <div class="right-center"
          ><chartsTitle :titleFlag="'right'" :titleText="'漏洞等级分布'"></chartsTitle>
          <div class="chartsBoxMain">
            <loopholeDistribution :loopholePieList="loopholeDistrList"></loopholeDistribution>
          </div>
        </div>
        <div class="right-bottom"
          ><chartsTitle :titleFlag="'right'" :titleText="'数据泄露分布'"></chartsTitle>
          <div class="chartsBoxMain">
            <leakageDistribution :dataList="leakageDataList"></leakageDistribution>
          </div>
        </div>
      </div>
      <div class="right1 rightTmp">
        <div class="right-top"
          ><chartsTitle :titleFlag="'right'" :titleText="'资产来源分布'"></chartsTitle>
          <div class="chartsBoxMain">
            <vennCharts></vennCharts>
          </div>
        </div>
        <div class="right-center"
          ><chartsTitle :titleFlag="'right'" :titleText="'漏洞排行TOP5'"></chartsTitle>
          <div class="chartsBoxMain">
            <loopholeRank :loopList="loopholeRank"></loopholeRank>
          </div>
        </div>
        <div class="right-bottom"
          ><chartsTitle :titleFlag="'right'" :titleText="'威胁资产分布'"></chartsTitle>
          <div class="chartsBoxMain">
            <threatDistribution></threatDistribution>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import chartsTitle from './chartsTitle.vue'
import assetProfile from './assetProfile.vue'
import chartsMap from './chartsMap.vue'
import loopholeDistribution from './loopholeDistribution.vue'
import vennCharts from './vennCharts.vue'
import loopholeRank from './loopholeRank.vue'
import hotspot from './hotspot.vue'
import riskEvent from './riskEvent.vue'
import threatDistribution from './threatDistribution.vue'
import leakageDistribution from './leakageDistribution.vue'
import dataLeakage from './dataLeakage'
import titleTime from './titleTime.vue'
import { screenLoopholeChart } from '@/api/apiConfig/screen.js'
import { countPocTypesNum } from '@/api/apiConfig/poc.js'

export default {
  components: {
    chartsTitle,
    assetProfile,
    chartsMap,
    loopholeDistribution,
    vennCharts,
    titleTime,
    loopholeRank,
    hotspot,
    riskEvent,
    threatDistribution,
    leakageDistribution,
    dataLeakage
  },
  data() {
    return {
      titleTimer: 0,
      currentCompany: '',
      loopholeDistrList: [],
      loopholeRank: [],
      leakageDataList: [],
      assetProfileLoaing: true,
      lenghedList: [
        {
          color: '#10D595',
          name: '0-25'
        },
        {
          color: '#F8C136',
          name: '26-50'
        },
        {
          color: '#FF7900',
          name: '51-75'
        },
        {
          color: '#FF4646',
          name: '76-100'
        }
      ]
    }
  },
  methods: {
    async getLoopHoleList() {
      let res = await screenLoopholeChart({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        let data = res.data
        //  漏洞分布 第一版接口2和一现在不需要暂时逻辑先不动还进行两个接口调用传参
        //  this.loopholeDistrList=[
        //   {name:'严重',value:data.loophole_critical_count},
        //   {name:'高危',value:data.loophole_high_count},
        //   {name:'中危',value:data.loophole_medium_count},
        //   {name:'低危',value:data.loophole_low_count},
        //  ]
        // 漏洞排行
        this.loopholeRank = data.loophole_top5
      }
      let res1 = await countPocTypesNum({ operate_company_id: this.currentCompany }).catch(() => {
        this.loopholeDistrList = [
          { name: '严重', value: 0 },
          { name: '高危', value: 0 },
          { name: '中危', value: 0 },
          { name: '低危', value: 0 }
        ]
      })
      if (res.code == 0) {
        let data = res1.data
        this.loopholeDistrList = [
          { name: '严重', value: data.unrepaired.critical },
          { name: '高危', value: data.unrepaired.high },
          { name: '中危', value: data.unrepaired.medium },
          { name: '低危', value: data.unrepaired.low }
        ]
      }
    },
    setLeakageDistribution(value) {
      this.titleTimer = value.time
      let dataTmp = [
        {
          name: '网盘',
          rate: value.sensitive_wangpan_count
        },
        {
          name: '文库',
          rate: value.sensitive_wenku_count
        },
        {
          name: '代码仓库',
          rate: value.sensitive_github_count
        }
      ]
      this.leakageDataList = dataTmp
    }
  },
  mounted() {
    this.currentCompany = JSON.parse(sessionStorage.getItem('currentCompany'))
      ? JSON.parse(sessionStorage.getItem('currentCompany')).id
      : ''
    this.getLoopHoleList()
  }
}
</script>
<style lang="less" scoped>
.warrp {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  // font-family: PingFangSC-Medium, PingFang SC;
  color: #e3eeff;
  background: url('../../assets/images/screenImage/bannerImg.png') no-repeat;
  background-size: 100% 100%;
  // overflow: hidden;
}
.header {
  width: 100%;
  height: 94px;
  display: flex;
  justify-content: center;
  // align-items: center;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 12px;
  background: url('../../assets/images/screenImage/headerBanner.png') no-repeat;
  background-size: 100% 100%;
  .headerImg {
    width: 607px;
    height: 35px;
    img {
      width: 100%;
      // height: 100%;
      margin-top: 12px;
    }
  }
}
.main {
  flex: 1;
  display: flex;
  padding: 9px 20px 20px 20px;
  justify-content: space-between;
  .left {
    width: 20.8%;
    height: 100%;
    // float: left;
    position: relative;
    // background: red;
    .left-top {
      width: 100%;
      height: 57%;
      border-radius: 0px 40px 40px 0px;
      border-image-source: url(../../assets/images/screenB.png);
      border-image-slice: 40 40 40 40 fill;
      border-image-width: 40px 40px 40px 40px;
      border-image-repeat: no-repeat;
    }
    .left-bottom {
      width: 100%;
      height: 41.4%;
      position: absolute;
      border-image-source: url(../../assets/images/screenB.png);
      border-image-slice: 40 40 40 40 fill;
      border-image-width: 40px 40px 40px 40px;
      border-image-repeat: no-repeat;
      top: 58.7%;
    }
  }
  .center {
    // float: left;
    width: 44.1%;
    height: 100%;
    position: relative;
    box-sizing: border-box;
    margin-left: 16px;
    .center-top {
      width: 100%;
      height: 68%;
      position: absolute;
      top: 0;
    }
    .center-bottom {
      width: 100%;
      height: 32%;
      position: absolute;
      top: 68%;
      box-sizing: border-box;
      border-image-source: url(../../assets/images/screenB.png);
      border-image-slice: 40 40 40 40 fill;
      border-image-width: 40px 40px 40px 40px;
      border-image-repeat: no-repeat;
    }
  }
  .right {
    // float: left;
    width: 16.1%;
    height: 100%;
    position: relative;
    margin-left: 16px;
  }
  .rightTmp {
    .right-top {
      width: 100%;
      height: 32.2%;
      border-image-source: url(../../assets/images/screenR.png);
      border-image-slice: 40 40 40 40 fill;
      border-image-width: 40px 40px 40px 40px;
      border-image-repeat: no-repeat;
      position: absolute;
      top: 0;
    }
    .right-center {
      width: 100%;
      height: 32.2%;
      border-image-source: url(../../assets/images/screenR.png);
      border-image-slice: 40 40 40 40 fill;
      border-image-width: 40px 40px 40px 40px;
      border-image-repeat: no-repeat;
      position: absolute;
      top: 33.9%;
    }
    .right-bottom {
      width: 100%;
      height: 32.2%;
      border-image-source: url(../../assets/images/screenR.png);
      border-image-slice: 40 40 40 40 fill;
      border-image-width: 40px 40px 40px 40px;
      border-image-repeat: no-repeat;
      position: absolute;
      top: 67.8%;
    }
  }
  .right1 {
    // float: left;
    width: 16.1%;
    height: 100%;
    position: relative;
    margin-left: 16px;
  }
  .chartsBoxMain {
    width: 100%;
    height: calc(100% - 60px);
  }
  .chartsBoxMain1 {
    width: 100%;
    height: 100%;
  }
}
.clearfix::before,
.clearfix::after {
  content: '';
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}
.clearfix:after {
  clear: both;
}
.clearfix {
  *zoom: 1; /*IE/7/6*/
}
.lengendBoxPostion {
  position: relative;
}
.lengendBox {
  position: absolute;
  bottom: 28px;
  box-sizing: border-box;
  p {
    color: #e3eeff;
  }
}
.circalBox {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
.circalMainBox {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding-top: 10px;
  span {
    margin-left: 6px;
  }
}
</style>
