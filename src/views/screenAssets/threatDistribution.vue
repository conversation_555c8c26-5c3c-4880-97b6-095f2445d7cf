<template>
  <div
    class="wrapper"
    id="threatDistribution"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
  </div>
</template>

<script>
import { screenThreatenChart } from '@/api/apiConfig/screen.js'

export default {
  data() {
    return {
      myChart: null,
      timer: null,
      dataLength: 4,
      loading: true,
      index: 0,
      currentCompany: '',
      dataList: []
    }
  },
  methods: {
    setInterFun() {
      this.timer = setInterval(() => {
        this.myChart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: this.index
        })
        // 取消高亮指定的数据图形
        this.myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.index == 0 ? this.dataLength - 1 : this.index - 1
        })
        this.myChart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: this.index
        })
        this.index++
        if (this.index > this.dataLength - 1) {
          this.index = 0
        }
      }, 3000)
    },
    chartsInit() {
      if (this.myChart !== null) {
        this.myChart.clear()
      }
      this.myChart = this.$echarts.init(document.getElementById('threatDistribution'))
      let options = {
        grid: {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0
        },
        color: ['#13E7FF', '#13B7FF', '#2677FF', '#2643FF'],
        series: [
          {
            name: '漏斗图',
            type: 'funnel',
            min: 0,
            max: 100,
            top: '10%',
            left: '10%',
            // right:'10%',
            bottom: '10%',
            minSize: '30%',
            maxSize: '90%',
            sort: 'descending', // 'ascending', 'descending'
            gap: 0,
            data: this.dataList.sort(function (a, b) {
              return a.value - b.value
            }),
            label: {
              normal: {
                show: false,
                formatter: '{a0|{b}}\n{a1|{d}%}',
                rich: {
                  a0: {
                    color: '#ffff',
                    align: 'center',
                    textBorderWidth: 0,
                    fontSize: 14
                  },
                  a1: {
                    textBorderWidth: 0,
                    color: '#2677FF',
                    padding: [3, 0, 0, 0]
                  }
                }
              }
            },
            labelLine: {
              normal: {
                // show:false,
                lineStyle: {
                  type: 'dashed',
                  color: 'rgba(227,238,255,0.5)'
                }
              }
            },
            roseType: true,
            itemStyle: {
              normal: {
                borderWidth: 0,
                shadowBlur: 30,
                shadowOffsetX: 0,
                shadowOffsetY: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.myChart.setOption(options)
      this.loading = false
      this.myChart.on('mouseover', (e) => {
        for (let i = 0; i < this.dataLength; i++) {
          if (i != e.dataIndex) {
            this.myChart.dispatchAction({
              type: 'downplay',
              seriesIndex: 0,
              dataIndex: i
            })
          }
        }
        if (e.dataIndex != this.index) {
          this.index = e.dataIndex
          this.myChart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: this.index
          })
        }
        clearInterval(this.timer)
      })
      this.myChart.on('mouseout', (e) => {
        clearInterval(this.timer)
        this.index = e.dataIndex
        this.myChart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: e.dataIndex
        })
        clearInterval(this.timer)
        if (this.index + 1 > this.dataLength - 1) {
          this.index = 0
        } else {
          this.index += 1
        }
        this.setInterFun()
      })
      window.addEventListener('resize', () => {
        if (this.myChart != null) {
          this.myChart.resize()
        }
      })
    },
    async getDtatList() {
      let res = await screenThreatenChart({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.dataList = this.setValue(res.data)
        this.chartsInit()
        this.setInterFun()
      }
      //  operate_company_id:this.currentCompany
    },
    setValue(data) {
      // 钓鱼仿冒、ICP盗用、黄赌毒网站、其他类型
      let tmp = 0
      tmp =
        data.threaten_type_other_count +
        data.threaten_type_fm_icp_count +
        data.threaten_type_dy_count +
        data.threaten_type_hdd_count +
        data.threaten_type_fm_domain_count
      let dataTmp = [
        { value: ((data.threaten_type_other_count / tmp) * 100).toFixed(0), name: '其他类型' },
        { value: ((data.threaten_type_fm_icp_count / tmp) * 100).toFixed(0), name: 'ICP盗用' },
        { value: ((data.threaten_type_dy_count / tmp) * 100).toFixed(0), name: '钓鱼仿冒' },
        { value: ((data.threaten_type_hdd_count / tmp) * 100).toFixed(0), name: '黄赌毒网站' },
        { value: ((data.threaten_type_fm_domain_count / tmp) * 100).toFixed(0), name: '域名混淆' }
      ]
      return dataTmp
    }
  },
  mounted() {
    this.currentCompany = JSON.parse(sessionStorage.getItem('currentCompany'))
      ? JSON.parse(sessionStorage.getItem('currentCompany')).id
      : ''
    this.getDtatList()
    // this.chartsInit()
    // this.setInterFun()
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
}
</style>
