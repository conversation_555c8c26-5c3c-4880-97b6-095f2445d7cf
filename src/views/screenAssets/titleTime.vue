<template>
  <div class="wrapper">
    <span>{{ timeObj.time }}</span>
    <span>{{ timeObj.week }}</span>
    <span>{{ timeObj.timer }}</span>
  </div>
</template>

<script>
export default {
  props: ['titleTimer'],
  data() {
    return {
      timeObj: '',
      timeStr: 0,
      timer: null
    }
  },
  watch: {
    titleTimer(val) {
      this.timeStr = val * 1000
      if (this.timer != null) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        this.timeStr = this.timeStr + 1000
        this.timeObj = this.getDate(this.timeStr)
      }, 1000)
    }
  },
  computed: {},
  methods: {
    getDate(date) {
      var t = new Date(date)
      let arrtmp = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']
      var brrTmp = new Array('周日', '周一', '周二', '周三', '周四', '周五', '周六')
      return {
        time: t.getFullYear() + '年' + arrtmp[t.getMonth()] + '月' + t.getDate() + '日',
        week: brrTmp[t.getDay()],
        timer: t.toLocaleTimeString()
      }
    }
  },
  beforeDestroy() {
    if (this.timer != null) {
      clearInterval(this.timer)
    }
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  position: absolute;
  right: 20px;
  top: 15px;
}
span {
  margin-left: 12px;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
}
</style>
