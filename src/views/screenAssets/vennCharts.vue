<template>
  <div
    class="wrapper"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
    v-if="!loading"
  >
    <div class="veenImg">
      <img src="../../assets/images/screenImage/veen.png" alt="" />
    </div>
    <p v-for="(item, key) in dataList" :key="key" :class="'p' + key"
      >{{ item.name }}<span>{{ item.value }}</span></p
    >
    <span v-for="(item, key) in dataListTmp" :key="'a' + key" :class="'span' + key">{{
      item
    }}</span>
  </div>
</template>

<script>
import { screenAssetVennMap } from '@/api/apiConfig/screen.js'

export default {
  data() {
    return {
      currentCompany: '',
      loading: true,
      dataList: [
        {
          name: '安服扫描',
          value: ''
        },
        {
          name: '用户推荐',
          value: ''
        },
        {
          name: '用户扫描',
          value: ''
        },
        {
          name: '安服推荐',
          value: ''
        }
      ],
      dataListTmp: []
    }
  },
  methods: {
    async chartsInit() {
      let res = await screenAssetVennMap({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.dataList.map((v, i) => {
          v.value = res.data[0][i]
        })
        this.dataListTmp = res.data[1]
        this.loading = false
      }
    }
  },
  mounted() {
    this.currentCompany = JSON.parse(sessionStorage.getItem('currentCompany'))
      ? JSON.parse(sessionStorage.getItem('currentCompany')).id
      : ''
    this.chartsInit()
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  .veenImg {
    width: 60%;
    height: 80%;
    position: absolute;
    z-index: 9;
    top: 10%;
    img {
      width: 100%;
      height: 100%;
    }
  }
  p {
    span {
      color: #2677ff;
      margin-left: 2px;
    }
  }
  .p0 {
    position: absolute;
    top: 13%;
    left: 5%;
  }
  .p1 {
    position: absolute;
    top: 0%;
    left: 20%;
  }
  .p2 {
    position: absolute;
    top: 0%;
    right: 20%;
  }
  .p3 {
    position: absolute;
    top: 13%;
    right: 3%;
  }
  span {
    z-index: 99;
  }
  .span0 {
    position: absolute;
    top: 50%;
    left: 27%;
  }
  .span1 {
    position: absolute;
    top: 15%;
    left: 35%;
  }
  .span2 {
    position: absolute;
    top: 13%;
    right: 35%;
  }
  .span3 {
    position: absolute;
    top: 50%;
    right: 27%;
  }
  .span4 {
    position: absolute;
    top: 45%;
    left: 42%;
  }
  .span5 {
    position: absolute;
    top: 25%;
    left: 48%;
  }
  .span6 {
    position: absolute;
    top: 45%;
    right: 42%;
  }
  .span7 {
    position: absolute;
    top: 30%;
    right: 35%;
  }
  .span8 {
    position: absolute;
    top: 67.5%;
    left: 43.5%;
  }
  .span9 {
    position: absolute;
    bottom: 15%;
    left: 48%;
  }
  .span10 {
    position: absolute;
    top: 67.5%;
    left: 53.5%;
  }
  .span11 {
    position: absolute;
    top: 30%;
    left: 33%;
  }
  .span12 {
    position: absolute;
    top: 57%;
    left: 49%;
  }
  .span13 {
    position: absolute;
    top: 63%;
    right: 38%;
  }
  .span14 {
    position: absolute;
    top: 63%;
    left: 38%;
  }
}
</style>
