<template>
  <div class="container spatialRetrieval">
    <div class="headerTitle">空间检索</div>
    <div class="home_header" v-loading="loading">
      <div class="filterTab">
        <div>
          <el-input
            v-model="inputSearch"
            placeholder='domain="" || ip="" || host="" || title="" || header=""'
            @keyup.enter.native="search"
            id="keyword_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="search"></el-button>
            <el-tooltip
              slot="prepend"
              class="item"
              effect="dark"
              content='支持检索字段：domain="" || ip="" || host="" || title="" || header=""'
              placement="top"
              :open-delay="100"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-input>
          <span @click="$refs.querySyntax.open('', '')"
            ><svg class="icon svg-icon" aria-hidden="true">
              <use xlink:href="#icon-question"></use></svg
            >查询语法</span
          >
        </div>
      </div>
      <div class="content">
        <div class="left">
          <StatisticalList :list="statisticsListData" @selectTreeNode="selectTreeNode" />
        </div>
        <div class="right">
          <div class="right-top">
            <div class="right-top-left">
              <span class="one">搜索 </span>
              <span class="two">
                <span
                  style="
                    display: inline-block;
                    vertical-align: bottom;
                    max-width: calc(100% - 340px);
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    color: #333;
                  "
                  >{{ inputSearchIn }}</span
                >
                获得 <span>{{ total }}</span> 条匹配结果
              </span>
            </div>
          </div>
          <div class="table" v-if="list.length !== 0">
            <HsxaMetaDataList
              class="huashunxinan"
              :list="list"
              :multiple="false"
              :loading="loading"
              :pagination="false"
              :api-type="'Foeye'"
              @link-click="clickCard"
              :handleLinkType="'event'"
              @body-icon-click="bodyIconClick"
            >
              <!-- @ip-polymerization-icon-click="infoCheck" -->
              <!-- 处理ui库loading重复 -->
              <template slot="loading">
                <img src="" alt="" />
              </template>
              <template v-slot:list-left-top="slotProps">
                <div class="list-middle-left-ip" :class="{ urlJump: slotProps.item.ip }">
                  <span @click="clickCard(slotProps.item.ip, 'ip')"
                    >IP: {{ slotProps.item.ip }}</span
                  >
                </div>
                <div class="list-middle-left-iporhost" :title="slotProps.item.title">
                  网站标题:
                  {{ slotProps.item.title || '-' }}
                </div>
                <div
                  class="list-middle-left-belong"
                  :class="{ urlJump: slotProps.item.geoip.country_name }"
                  :title="slotProps.item.geoip.country_name"
                >
                  国家/地区：
                  <span @click="clickCard(slotProps.item, 'country')">{{
                    slotProps.item.geoip.country_name || '-'
                  }}</span>
                  <template v-if="slotProps.item.geoip.city_name">/</template>
                  <span @click="clickCard(slotProps.item, 'city')">{{
                    slotProps.item.geoip.city_name
                  }}</span>
                </div>
                <div
                  class="list-middle-left-ans"
                  :class="{ urlJump: slotProps.item.asn.as_number }"
                  :title="slotProps.item.asn.as_number || ''"
                >
                  ASN:
                  <span @click="clickCard(slotProps.item.asn.as_number, 'asn')">{{
                    slotProps.item.asn.as_number || '-'
                  }}</span>
                </div>
                <div
                  class="list-middle-left-org"
                  :class="{ urlJump: slotProps.item.asn.as_organization }"
                  :title="slotProps.item.asn.as_organization"
                >
                  组织：
                  <span @click="clickCard(slotProps.item.asn.as_organization, 'org')">{{
                    slotProps.item.asn.as_organization || '-'
                  }}</span>
                </div>
                <div class="list-middle-left-coordinate">
                  <span>
                    坐标：{{
                      (!slotProps.item.geoip.longitude && !slotProps.item.geoip.latitude) ||
                      (slotProps.item.geoip.longitude == 0 && slotProps.item.geoip.latitude == 0)
                        ? '-'
                        : `${slotProps.item.geoip.longitude},${slotProps.item.geoip.latitude}`
                    }}</span
                  >
                </div>
                <div
                  class="list-middle-left-server"
                  :class="{ urlJump: slotProps.item.server }"
                  :title="slotProps.item.server"
                >
                  <span @click="clickCard(slotProps.item.server, 'server')"
                    >Server: {{ slotProps.item.server || '-' }}</span
                  >
                </div>
                <div class="list-middle-left-date">
                  更新时间: {{ slotProps.item.last_update_time || '-' }}
                </div>
                <div class="list-middle-left-module">
                  <span
                    v-for="(u1, index1) in slotProps.item.product"
                    :key="index1"
                    style="flex-shrink: 0"
                  >
                    <img
                      v-if="showimg(u1).result"
                      :src="showimg(u1).result ? showimg(u1).url : ''"
                      :alt="u1"
                    />
                    <span>{{ u1 }}</span>
                  </span>
                </div>
              </template>

              <template v-slot:cert-title="slotProps">
                <span style="color: #333">证书: </span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="slotProps.item.certs.is_valid ? '有效证书' : '无效证书'"
                  placement="top"
                >
                  <span v-if="slotProps.item.certs">
                    <svg
                      v-if="!slotProps.item.certs.is_valid"
                      class="icon svg-icon validate-certificate"
                      aria-hidden="true"
                    >
                      <use xlink:href="#icon-certificate"></use>
                    </svg>
                  </span>
                </el-tooltip>
              </template>
              <template v-slot:ip-detail="slotProps">
                <span @click="infoCheck(slotProps.item)">
                  <i class="iconfont icon-yingyong" />
                </span>
              </template>
            </HsxaMetaDataList>
          </div>
          <div class="table" v-else>
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 30, 50, 100]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <WebsiteCodeModal ref="websiteCodeModal" />
    <QuerySyntax ref="querySyntax" />
  </div>
</template>

<script>
import countryData from '@/utils/country'
import { HsxaMetaDataList, HsxaMetaDataStatisticalList } from 'hsxa-ui'
import QuerySyntax from './querySyntax'
import StatisticalList from './statisticalList'
import WebsiteCodeModal from './websiteCodeModal'
import { mapGetters, mapState } from 'vuex'
import { assetsSearchList, statisticalList } from '@/api/apiConfig/api.js'

export default {
  components: {
    QuerySyntax,
    HsxaMetaDataList,
    HsxaMetaDataStatisticalList,
    WebsiteCodeModal,
    StatisticalList
  },
  data() {
    return {
      selectedTypeKeys: [],
      selectedKeys: [],
      inputSearchOuter: '',
      inputShowKeys: [],
      statisticsListData: [],
      loading: false,
      inputSearch: '',
      inputSearchIn: '',
      list: [],
      loading: false,
      pageSize: 10,
      currentPage: 1,
      total: 0,
      user: {
        role: ''
      }
    }
  },
  watch: {
    getterCurrentCompany(val) {
      let isGetCompany = sessionStorage.getItem('isGetCompany')
      if (this.user.role != 1) {
        if (Boolean(isGetCompany)) {
          this.getData()
        }
      } else {
        this.getData()
      }
    }
  },
  mounted() {
    if (sessionStorage.getItem('userMessage')) {
      this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (this.userInfo) {
        this.user = this.userInfo.user
      }
    }
    let isGetCompany = sessionStorage.getItem('isGetCompany')
    if (this.user.role == 2) {
      if (Boolean(isGetCompany)) {
        this.getData()
      }
    } else {
      this.getData()
    }
  },
  computed: {
    ...mapState(['currentCompany', 'currentCompanyId']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    search() {
      // this.inputSearch = ""
      this.currentPage = 1
      if (!this.inputSearch) {
        this.inputShowKeys = []
      }
      this.getData()
    },
    bodyIconClick(item) {
      this.$refs.websiteCodeModal.open(item.body, item.host)
    },
    infoCheck(item) {
      let id = item.user_id + '_' + item.ip
      window.open(
        `/alreadyTask_viewlist_ipinfo?id=${id}&fromIcon=0&preList=${this.$route.path}&userId=${item.user_id}`,
        '_blank'
      ) // fromIcon: 默认'0' 已完成任务列查看ip详情，传1
    },
    // 判断图片是否存在
    showimg(url) {
      let imgUrl = ''
      url = url.replace('/', '')
      url = url.replace('.', '')
      url = url.replace(' ', '')
      let result = false
      url = './' + url + '.png'
      let urlTmp = url
      urlTmp = urlTmp.replace('./', '').replace('.png', '')
      // 目录下所有文件名
      const files = require.context('../../assets/images/componentIcon/', false, /\.png$/)
      const filedata = files.keys()
      filedata.forEach((item) => {
        if (item === url) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp + '.png')
        } else if (item === url.toLowerCase()) {
          result = true
          imgUrl = require('../../assets/images/componentIcon/' + urlTmp.toLowerCase() + '.png')
        }
      })
      // 返回结果
      return {
        result,
        url: imgUrl
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    loadImg(moduleName) {
      const keys = Object.keys(this.modulesList)
      const lowerCases = keys.map((key) => key.toLowerCase())
      let index = NaN
      lowerCases.map((key, indexIn) => {
        if (key.indexOf(moduleName.toLowerCase()) > -1) {
          index = indexIn
        }
      })
      if (!isNaN(index)) {
        try {
          return require(`@/assets/images/ven/${this.modulesList[keys[index]]}.png`)
        } catch (e) {
          return ''
          // throw new Error(JSON.stringify(e))
        }
      }
    },
    clickCard(data, key) {
      if (!data) return
      let inputStr = ''
      if (key == 'country') {
        if (data.geoip.country_code2 === 'TW' || data.geoip.country_code2 === 'HK') {
          inputStr = `region="${data.geoip.country_code2}"`
        } else {
          inputStr = `country="${data.geoip.country_code2}"`
        }
      } else if (key == 'city') {
        if (data.geoip.country_code2 === 'TW' || data.geoip.country_code2 === 'HK') {
          return
        } else {
          inputStr = `city="${data.geoip.city_name}"`
        }
      } else if (key == 'protocol') {
        inputStr = `protocol="${data[key]}"`
      } else if (key == 'port') {
        inputStr = `port="${data[key]}"`
      } else if (key == 'ip' || key == 'asn' || key == 'org' || key == 'server') {
        inputStr = `${key}="${data}"`
      }
      // this.currentPage = 1;
      // this.inputSearch = inputStr;
      // this.getData();
      this.inputStr(inputStr)
    },
    inputStr(inputVal) {
      this.currentPage = 1
      this.inputSearch = inputVal
      // this.inputSearchOuter = inputVal;
      this.getData()
    },
    async getData() {
      this.loading = true
      if (
        this.inputSearch.includes('country = "TW"') ||
        this.inputSearch.includes("country = 'TW'") ||
        this.inputSearch.includes('country="TW"') ||
        this.inputSearch.includes("country='TW'") ||
        this.inputSearch.includes("country= 'TW'") ||
        this.inputSearch.includes('country= "TW"') ||
        this.inputSearch.includes("country ='TW'") ||
        this.inputSearch.includes('country ="TW"') ||
        this.inputSearch.includes('country="台湾"')
      ) {
        this.inputSearch = this.inputSearch.replace(/country="TW"/g, 'region="TW"')
        this.inputSearch = this.inputSearch.replace(/country='TW'/g, 'region="TW"')
      }
      if (
        this.inputSearch.includes('country = "HK"') ||
        this.inputSearch.includes("country = 'HK'") ||
        this.inputSearch.includes('country="HK"') ||
        this.inputSearch.includes("country='HK'") ||
        this.inputSearch.includes("country= 'HK'") ||
        this.inputSearch.includes('country= "HK"') ||
        this.inputSearch.includes("country ='HK'") ||
        this.inputSearch.includes('country ="HK"') ||
        this.inputSearch.includes('country="香港"') ||
        this.inputSearch.includes('country ="Hong Kong"')
      ) {
        this.inputSearch = this.inputSearch.replace(/country="HK"/g, 'region="HK"')
        this.inputSearch = this.inputSearch.replace(/country='HK'/g, 'region="HK"')
      }
      if (
        this.inputSearch.includes('country = "MO"') ||
        this.inputSearch.includes("country = 'MO'") ||
        this.inputSearch.includes('country="MO"') ||
        this.inputSearch.includes("country='MO'") ||
        this.inputSearch.includes("country= 'MO'") ||
        this.inputSearch.includes('country= "MO"') ||
        this.inputSearch.includes("country ='MO'") ||
        this.inputSearch.includes('country ="MO"') ||
        this.inputSearch.includes('country="澳门特别行政区"')
      ) {
        this.inputSearch = this.inputSearch.replace(/country="MO"/g, 'region="MO"')
        this.inputSearch = this.inputSearch.replace(/country='MO'/g, 'region="MO"')
      }
      let search = this.inputSearch ? window.btoa(window.encodeURIComponent(this.inputSearch)) : ''
      let res = await assetsSearchList({
        operate_company_id: this.currentCompany,
        search,
        page: this.currentPage,
        per_page: this.pageSize
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        let data = res.data.items
        this.list = data || []
        this.total = res.data.total || 0
      }
      this.getStaticalData()
      this.inputSearchIn = this.inputSearch
      this.loading = false
    },
    async getStaticalData() {
      this.statisticsListData = []

      let search = this.inputSearch ? window.btoa(window.encodeURIComponent(this.inputSearch)) : ''
      let res = await statisticalList({
        operate_company_id: this.currentCompany,
        search
      })
      if (res.code == 0) {
        const treeListData = (res.data.items || []).map((item) => {
          if (item.key == 'country') {
            ;(item.children || []).forEach((child) => {
              child.code = child.key
              child.key = countryData[child.title]
              child.title = child.code
            })
          }
          return item
        })

        this.statisticsListData = treeListData || []
        // this.treeListOrigin = this.treeList
      }
    },
    selectTreeNode(type, value) {
      let option = ''

      if (type === 'type') {
        option = value.name || ''
      } else {
        option = value.title || ''
      }
      let addString = ''
      if (!this.inputSearch.includes(type + `="${option}"`)) {
        if (type === 'server') {
          addString = type + `=="${option}"`
        } else {
          addString = type + `="${option}"`
        }
        let inputSearch = this.inputSearch
        this.inputSearch = (inputSearch ? inputSearch + '&&' : inputSearch) + addString
      }

      // 右侧点击和左侧条件一致时，将右侧历史清空
      if (addString.indexOf(this.inputSearchOuter) > -1) this.inputSearchOuter = ''
      this.currentPage = 1
      this.getData()
    }
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: calc(100% - 16px) !important;
  /deep/.home_header {
    position: relative;
    width: 100%;
    height: 100%;
    .content {
      display: flex;
      height: calc(100% - 74px);
      // flex-direction: column;
      .left {
        width: 234px;
        height: 100%;
        // padding:0 16px;
        margin-right: 16px;
        overflow: auto;

        .statistical-main {
          width: 100%;
          // height: 100%;
        }
        .hsxa-list-main {
          background: #fff;
        }
      }
      .right {
        width: calc(100% - 234px);
        height: 100%;
        background: #fff;
        .right-top {
          display: flex;
          justify-content: space-between;
          margin: 24px 0 26px;
          padding-left: 16px;
          .right-top-left {
            color: #333;
            font-weight: 400;
            width: calc(100% - 164px);
          }
        }
        .table {
          height: calc(100% - 135px);
          overflow: auto;
        }
        .el-pagination {
          margin-top: 20px;
        }
      }
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      margin-bottom: 16px;
      background: #fff;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }

      .el-input {
        width: 700px !important;
        .el-input__inner {
          width: 100%;
          height: 32px;
          line-height: 32px;
          background: #ffffff;
          border-radius: 4px;
        }
        .el-input-group--append {
          border: 1px solid #d1d5dd;
          border-radius: 4px;
          .el-input__inner {
            border: 0;
          }
        }
        .el-input-group__prepend {
          padding: 0 5px;
          border: 0;
          border-radius: none;
          .el-icon-question {
            font-size: 16px;
            color: rgb(177, 189, 209);
          }
        }
        .el-input-group__append {
          padding: 0 5px;
          background: #ffffff;
          border: 0;
          .el-button {
            padding: 0 !important;
          }
        }
      }
    }
  }
}
/deep/.hsxa-ui-component {
  a {
    color: #1059d5;
  }
  /deep/.hsxa-meta-data-list {
    padding: 0px;
    .hsxa-ui-component .el-checkbox-group > div {
      background-color: #d7dadf;
    }
  }
}

/deep/.hsxa-meta-data-list-main-left {
  div {
    line-height: 2;
  }
  .urlJump {
    span {
      color: #4285f4;
      cursor: pointer;
    }
  }
  .list-middle-left-iporhost {
    height: 28px;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-right: 1em;
  }
  .list-middle-left-module {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    cursor: default;
    > span {
      border: 1px solid #4285f4;
      background: rgba(66, 133, 244, 0.1);
      color: #4285f4;
      display: inline-flex;
      font-size: 12px;
      padding: 0 6px;
      border-radius: 10px;
      height: 20px;
      line-height: 20px;
      margin-right: 10px;
      margin-top: 5px;
      align-items: center;
      flex-shrink: 0;
      img {
        width: 12px;
        height: 12px;
        margin-right: 3px;
      }
    }
  }
  .list-middle-left-icon {
    padding-top: 4px;
    svg {
      width: 14px;
      height: 14px;
      fill: #333;
      cursor: pointer;
      margin-right: 14px;
      transition: all 0.3s;
      &:hover {
        fill: #4285f4;
      }
      &:focus {
        outline: none;
      }
    }
  }
}
/deep/.hsxa-meta-data-list {
  padding: 0px;
}

/deep/.el-collapse .el-collapse-item__header .el-collapse-item__arrow {
  color: #333;
}
/deep/.el-pagination {
  background-color: #fff;
}
/deep/.validate-certificate {
  fill: #ffbb00;
  color: #ff2600;
}
.emptyClass {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  // text-align: center;
  // vertical-align: middle;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
/deep/.hsxa-fid {
  display: none;
}
</style>
