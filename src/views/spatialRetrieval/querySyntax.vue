<template>
  <el-drawer title="查询语法" :visible.sync="drawer" :direction="direction">
    <!-- <template #title>
    {{ hostData }}  的网站正文
  </template> -->
    <div class="drawer-body">
      <div class="text-title">
        直接输入查询语句，将从标题，html内容，http头信息，url字段中搜索
      </div>
      <div class="text-top">
        <ul>
          <li>title="beijing" 从标题中搜索北京。例：标题中有北京的网站</li>
          <li>header="elastic" 从http头中搜索elastic。例：jboss服务器</li>
          <li>header="elastic" 从http头中搜索elastic。例：jboss服务器</li>
          <li> body="网络空间测绘" 从html正文中搜索网络空间测绘。例：正文包含Hacked by </li>
          <li> domain="qq.com" 搜索根域名带有qq.com的网站。例：根域名是qq.com的网站 </li>
          <li>
            host=".gov.cn" 从url中搜索.gov.cn,注意搜索要用host作为名称。例：政府网站, 教育网站
          </li>
          <li>port="443" 查找对应443端口的资产。例：查找对应443端口的资产</li>
          <li>
            ip="*******" 从ip中搜索包含*******的网站,注意搜索要用ip作为名称。例：查询IP为
            *************的 网站;
          </li>
          <li>
            如果想要查询网段，可以是：ip="*************/24"，例：查询IP为 *************的C网段资产
          </li>
          <li>
            protocol="https" 搜索制定协议类型(在开启端口扫描的情况下有效)。例：查询https协议资产
          </li>
          <li>city="Beijing" 搜索指定城市的资产。例：搜索指定城市的资产</li>
          <li> region="Zhejiang" 搜索指定行政区的资产。例：搜索指定行政区的资产 </li>
          <li> country="CN" 搜索指定国家(编码)的资产。例：搜索指定国家(编码)的资产 </li>
          <li>
            cert="google.com"
            搜索证书(https或者imaps等)中带有google.com的资产。例：搜索证书(https或者imaps等)中带有google.com的资产
          </li>
          <li>is_honeypot=true 查询蜜罐数据。例：查询蜜罐数据</li>
          <li>is_honeypot=false 排除蜜罐数据。例：排除蜜罐数据</li>
          <li>is_fraud=true 查询非法网站。例：查询非法网站</li>
          <li>is_fraud=false 排除非法网站。例：排除非法网站</li>
        </ul>
      </div>
      <div class="text-middle">
        <div class="text-title">高级搜索</div>
        <div class="text-middle-content"> 可以使用括号 和 && || !=等符号，如 </div>
        <div class="text-middle-content"> title="powered by" && title!=discuz </div>
        <div class="text-middle-content"> title="powered by" && body!=discuz </div>
        <div class="text-middle-content">
          ( body="content=\"WordPress" || (header="X-Pingback" && header="/xmlrpc.php" &&
          body="/wp-includes/") ) && host="gov.cn"
        </div>
        <div class="text-middle-content">
          新增==完全匹配的符号，可以加快搜索速度，比如查找qq.com所有host，可以是 domain=="qq.com"
        </div>
        <div class="text-middle-content"> 关于建站软件的搜索语法请参考：组件列表 </div>
      </div>
      <div class="text-bottom">
        <div class="text-title">注意事项</div>
        <div> * 如果查询表达式有多个与或关系，尽量在外面用（）包含起来 </div>
        <div> 剩下来，就是发挥你想象力的时候了 ；） </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      bodyData: '',
      hostData: ''
    }
  },
  methods: {
    open(body, host) {
      this.bodyData = body
      this.hostData = host
      this.drawer = true
    },
    handleClose(done) {}
  }
}
</script>

<style lang="less" scoped>
textarea {
  resize: none;
}
/deep/.el-drawer.rtl {
  width: 850px !important;
}
.drawer-body {
  width: 100%;
  // height: calc(100% - 55px);
  height: 100%;
  padding: 24px;
  line-height: 24px;
  .drawer-content {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    // height: calc(100% - 120px);
    padding: 4px 11px;
    border: 1px solid #d9d9d9;
    line-height: 2;
  }
  .text-top {
    margin-bottom: 20px;
    li:before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border: 1px solid #333;
      border-radius: 50%;
      margin-right: 5px;
    }
  }
  .text-middle {
    margin-bottom: 20px;
  }
  .text-title {
    // font-family: PingFangSC-Semibold,sans-serif;
    font-weight: 600;
  }
}
</style>
