<template>
  <div class="statistical-main">
    <HsxaMetaDataStatisticalList
      class="hsxa-meta-data-statistical-list"
      :multiple="false"
      :loading="loading"
      :list="list"
      :show-items="'all'"
      :empty="'hidden'"
      :border="false"
      :fold="true"
      :api-type="'Foeye'"
      :handle-link-type="'event'"
      @link-click="selectTreeNode"
      :sort="['fid']"
    >
      <template v-slot:title="slotProps">
        <div>
          <!-- <Expand v-if="!slotProps.item.showCountTable"/> -->
          <!-- <Fold v-if="slotProps.item.showCountTable"/> -->
          <span v-if="slotProps.item.showCountTable" class="svgIcon">
            <svg class="icon svg-icon" aria-hidden="true">
              <use xlink:href="#icon-expand"></use>
            </svg>
          </span>
          <span v-if="!slotProps.item.showCountTable" class="svgIcon">
            <svg class="icon svg-icon" aria-hidden="true">
              <use xlink:href="#icon-collapse"></use>
            </svg>
          </span>
          <span v-if="slotProps.item.key == 'fid'">
            <el-tooltip
              class="item"
              effect="dark"
              content="对资产进行智能“特征聚类分析”，生成唯一值“FID”，同一FID的资产，是同一种资产"
              placement="top"
            >
              <span
                >资产特征聚类
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-question"></use>
                </svg>
              </span>
            </el-tooltip>
          </span>
          <span v-else>{{ slotProps.item.title }}</span>
        </div>
      </template>
      <!-- 处理ui库loading重复 -->
      <template slot="loading">
        <img src="" alt="" />
      </template>
    </HsxaMetaDataStatisticalList>
  </div>
</template>

<script>
import { HsxaMetaDataList, HsxaMetaDataStatisticalList } from 'hsxa-ui'
export default {
  components: {
    HsxaMetaDataStatisticalList
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      apiType: 'Foeye',
      // list: {},
      countryList: [],
      countryChartData: [],
      showItems: ['fid', 'port', 'server'],
      empty: 'empty'
    }
  },
  methods: {
    selectTreeNode(type, value) {
      this.$emit('selectTreeNode', type, value)
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.hsxa-meta-data-statistical-list {
  min-width: 202px;
}

/deep/.hsxa-meta-data-statistical-list .hsxa-list-main {
  > div {
    padding: 10px 18px 10px 12px;
  }
  .hsxa-list-title {
    font-weight: 600;
  }
}
/deep/.hsxa-meta-data-statistical-list .hsxa-list-main-hidden {
  display: none;
}

/deep/.hsxa-meta-data-statistical-list .hsxa-long-title a:first-child {
  margin-left: 21px;
}
.svgIcon {
  margin-right: 6px;
}
</style>
