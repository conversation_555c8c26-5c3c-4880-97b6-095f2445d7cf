<template>
  <el-drawer :title="hostData + ' 的网站正文'" :visible.sync="drawer" :direction="direction">
    <!-- <template #title>
      {{ hostData }}  的网站正文
    </template> -->
    <div class="drawer-body">
      <textarea readonly="true" v-model="bodyData" class="drawer-content"></textarea>
    </div>
  </el-drawer>
</template>

<script>
export default {
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      bodyData: '',
      hostData: ''
    }
  },
  methods: {
    open(body, host) {
      this.bodyData = body
      this.hostData = host
      this.drawer = true
    },
    handleClose(done) {}
  }
}
</script>

<style lang="less" scoped>
textarea {
  resize: none;
}
/deep/.el-drawer.rtl {
  width: 850px !important;
}
.drawer-body {
  width: 100%;
  // height: calc(100% - 55px);
  height: 100%;
  padding: 20px;
  .drawer-content {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    // height: calc(100% - 120px);
    padding: 4px 11px;
    border: 1px solid #d9d9d9;
    line-height: 2;
  }
}
</style>
