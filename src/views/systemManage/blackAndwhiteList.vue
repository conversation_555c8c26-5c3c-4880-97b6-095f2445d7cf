<template>
  <div class="tab_content">
    <div class="filterTab">
      <div>
        <!-- <el-input v-model="formInline.blackkeyword" placeholder="请输入黑名单关键字检索" @keyup.enter.native="checkFuncList('black')" id="keyword_keycheck">
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList('black')"></el-button>
            </el-input>
                 <el-input v-model="formInline.whitekeyword" placeholder="请输入白名单关键字检索" @keyup.enter.native="checkFuncList('white')" id="keyword_keycheck" style="marginLeft:16px">
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList('white')"></el-button>
            </el-input> -->
      </div>
      <div>
        <el-button class="normalBtnRe" type="primary" id="ip_more_del" @click="remove()"
          >删除</el-button
        >
        <!-- <el-button class="normalBtnRe" type="primary"  id="ip_more_del" @click="remove('white')">删除白名单</el-button> -->
        <el-button class="normalBtn" type="primary" id="ip_all" @click="addEdit">新建</el-button>
      </div>
    </div>
    <div class="tableWrap">
      <div class="tableSeparate">
        <div class="separateTitle"> 黑名单 </div>
        <div>
          <el-input
            v-model="formInline.blackkeyword"
            placeholder="请输入黑名单关键字检索"
            @keyup.enter.native="checkFuncList('black')"
            id="keyword_keycheck"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="checkFuncList('black')"
            ></el-button>
          </el-input>
        </div>
        <div class="tableBox">
          <el-table
            :data="blackData"
            v-loading="blackLoading"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            @selection-change="
              (value) => {
                handleSelectionChange(value, 'black')
              }
            "
            @cell-mouse-enter="showTooltip"
            @cell-mouse-leave="hiddenTooltip"
            ref="blackTable"
            height="100%"
            style="width: 100%"
          >
            <el-table-column type="selection" align="center" :reserve-selection="true" width="55">
            </el-table-column>
            <el-table-column
              v-for="val in tableHeader"
              :key="val.name"
              :prop="val.name"
              align="left"
              min-width="120"
              :label="val.label"
            >
              <template slot-scope="scope">
                <span v-if="scope.row[val.name]">{{ scope.row[val.name] }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="
              (value) => {
                handleSizeChange(value, 'black')
              }
            "
            @current-change="
              (value) => {
                handleCurrentChange(value, 'black')
              }
            "
            :current-page="blackcurrentPage"
            :page-sizes="[10, 30, 50, 100]"
            :page-size="blackPageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="blacktotal"
          >
          </el-pagination>
        </div>
      </div>
      <div class="tableSeparate">
        <div class="separateTitle"> 白名单 </div>
        <div>
          <el-input
            v-model="formInline.whitekeyword"
            placeholder="请输入白名单关键字检索"
            @keyup.enter.native="checkFuncList('white')"
            id="keyword_keycheck"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="checkFuncList('white')"
            ></el-button>
          </el-input>
        </div>
        <div class="tableBox">
          <el-table
            :data="whiteData"
            v-loading="whiteLoading"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            @selection-change="
              (value) => {
                handleSelectionChange(value, 'white')
              }
            "
            @cell-mouse-enter="showTooltip"
            @cell-mouse-leave="hiddenTooltip"
            ref="whiteTable"
            height="100%"
            style="width: 100%"
          >
            <el-table-column type="selection" align="center" :reserve-selection="true" width="55">
            </el-table-column>
            <el-table-column
              v-for="val in tableHeader"
              :key="val.name"
              :prop="val.name"
              align="left"
              min-width="120"
              :label="val.label"
            >
              <template slot-scope="scope">
                <span v-if="scope.row[val.name]">{{ scope.row[val.name] }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
          <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
          <el-pagination
            @size-change="
              (value) => {
                handleSizeChange(value, 'white')
              }
            "
            @current-change="
              (value) => {
                handleCurrentChange(value, 'white')
              }
            "
            :current-page="whitecurrentPage"
            :page-sizes="[10, 30, 50, 100]"
            :page-size="whitePageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="whitetotal"
          >
          </el-pagination>
        </div>
        <el-dialog
          class="elDialogAdd"
          :close-on-click-modal="false"
          :visible.sync="dialogFormVisibleInsert"
          width="500px"
        >
          <template slot="title"> 新建 </template>
          <div class="dialog-body">
            <el-form
              :model="ruleForm"
              :rules="rules"
              style="padding: 0 !important"
              ref="ruleForm"
              label-width="80px"
              class="demo-ruleForm"
            >
              <el-form-item label="类型" prop="type">
                <el-radio-group v-model="ruleForm.type">
                  <el-radio-button label="black"> 黑名单 </el-radio-button>
                  <el-radio-button label="white">白名单</el-radio-button>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="关键词" prop="keywords">
                <el-input
                  type="textarea"
                  :rows="10"
                  v-model="ruleForm.keywords"
                  placeholder="请输入关键词，多个值请用分号或换行分隔"
                ></el-input>
              </el-form-item>
            </el-form>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button
              class="highBtnRe"
              @click="dialogFormVisibleInsert = false"
              id="keyword_add_cancel"
              >关闭</el-button
            >
            <el-button
              class="highBtn"
              :loading="otherLoading"
              id="keyword_add_sure"
              @click="addSure"
              >确定</el-button
            >
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { blackAndwhiteList, blackAndwhiteDel, blackAndwhiteAdd } from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip
  },
  data() {
    return {
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      rules: {
        keyword: [{ required: true, message: ' ', trigger: 'blur' }]
      },
      formInline: {
        blackkeyword: '',
        whitekeyword: ''
      },
      otherLoading: false,
      blackData: [],
      // operate_company_id: this.currentCompany,
      blackLoading: false,
      blackcurrentPage: 1,
      blackPageSize: 10,
      blacktotal: 0,
      whiteData: [],
      whiteLoading: false,
      whitecurrentPage: 1,
      whitePageSize: 10,
      whitetotal: 0,
      blackCheckArr: [],
      whiteCheckArr: [],
      ruleForm: {
        keywords: '',
        type: 'black'
      },
      dialogFormVisibleInsert: false,
      tableHeader: [
        {
          label: '关键词',
          name: 'keyword',
          id: 0
        },
        {
          label: '创建时间',
          name: 'created_at',
          id: 1
        }
      ]
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  methods: {
    checkFuncList(type) {
      this[type + 'currentPage '] = 1
      this.getList(type)
    },
    async addSure() {
      let obj = {
        type: this.ruleForm.type,
        keywords: this.ruleForm.keywords
          .split(/[；|;|\r\n]/)
          .filter((item) => {
            return item.trim()
          })
          .map((item) => {
            return item.trim()
          }),
        operate_company_id: this.currentCompany
      }
      let res = await blackAndwhiteAdd(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.dialogFormVisibleInsert = false
        this.getList(this.ruleForm.type)
      }
    },
    addEdit() {
      this.dialogFormVisibleInsert = true
      this.ruleForm = {
        keywords: '',
        type: 'black'
      }
    },
    remove() {
      //   blackAndwhiteDel()
      if (this.blackCheckArr.length == 0 && this.whiteCheckArr.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      let obj = {
        black_ids: this.blackCheckArr.map((item) => {
          return item.id
        }),
        white_ids: this.whiteCheckArr.map((item) => {
          return item.id
        }),
        operate_company_id: this.currentCompany,
        keyword: ''
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_info_del_cancel',
        confirmButtonClass: 'cloud_info_del_sure',
        customClass: 'cloud_info_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await blackAndwhiteDel(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.getList()
          }
        })
        .catch(() => {})
    },
    handleSelectionChange(value, type) {
      if (type == 'black') {
        this.blackCheckArr = value
      } else {
        this.whiteCheckArr = value
      }
    },
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSizeChange(value, type) {
      if (type == 'black') {
        this.blackPageSize = value
      } else {
        this.whitePageSize = value
      }
      this.getList(type)
    },
    handleCurrentChange(value, type) {
      if (type == 'black') {
        this.blackcurrentPage = value
      } else {
        this.whitecurrentPage = value
      }
      this.getList(type)
    },
    async getList(type) {
      let obj = {}
      obj.operate_company_id = this.currentCompany
      // 区分黑名单白名单
      if (type) {
        this[type + 'Loading'] = true
        obj.type = type
        obj.page = this[type + 'currentPage']
        obj.per_page = this[type + 'PageSize']
        obj.keyword = this.formInline[type + 'keyword']
        let res = await blackAndwhiteList(obj).catch(() => {
          this[type + 'Loading'] = false
          this[type + 'Data'] = []
          this[type + 'total'] = 0
        })
        this[type + 'Loading'] = false
        this[type + 'Data'] = res.data && res.data.items ? res.data.items : []
        this[type + 'total'] = res.data.total
        this.$nextTick(() => {
          this.$refs[type + 'Table'].clearSelection()
        })
      } else {
        obj.page = 1
        obj.per_page = 10
        obj.type = 'black'
        let res = await blackAndwhiteList(obj).catch(() => {
          this.blackLoading = false
          this.blackData = []
          this.blacktotal = 0
        })
        this.blackLoading = false
        this.blackData = res.data && res.data.items ? res.data.items : []
        this.blacktotal = res.data.total
        obj.type = 'white'
        let res1 = await blackAndwhiteList(obj).catch(() => {
          this.whiteLoading = false
          this.whiteData = []
          this.whitetotal = 0
        })
        this.whiteLoading = false
        this.whiteData = res1.data && res1.data.items ? res1.data.items : []
        this.whitetotal = res1.data.total
        this.$nextTick(() => {
          this.$refs.blackTable.clearSelection()
          this.$refs.whiteTable.clearSelection()
        })
      }
    }
  },
  created() {
    this.getList()
  },
  beforeDestroy() {
    sessionStorage.setItem('activeTabName', '')
  }
}
</script>
<style lang="less" scoped>
/deep/.home_header {
  position: relative;
  height: 100%;
}
.tableWrap {
  height: calc(100% - 129px);
  // padding: 0px 20px;
  .tableSeparate:nth-child(2) {
    margin-left: 2%;
    float: right;
  }
}
.tableSeparate {
  width: 49%;
  height: 100%;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  padding: 12px 16px 16px 16px;
  box-sizing: border-box;
  float: left;
  .el-input {
    width: 240px;
    margin-right: 12px;
  }
}
.separateTitle {
  padding-bottom: 12px;
  color: #37393c;
  font-weight: 500;
  border-bottom: 1px solid #e9ebef;
  margin-bottom: 12px;
}
.tableMain {
  height: calc(100%-40px);
}
.tableBox {
  margin-top: 10px;
  height: 80%;
}
.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  & > div {
    display: flex;
    align-items: center;
    .normalBtnRe {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .el-input {
      width: 240px;
      margin-right: 12px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      margin-left: 4px;
      cursor: pointer;
    }
  }
}
dialog-body {
  height: 100%;
  display: flex;
  justify-content: space-between;
  .el-form {
    /deep/ .el-form-item__content {
      p,
      span {
        color: #606266 !important;
        font-size: 14px !important;
      }
      p span {
        color: #606266 !important;
        font-size: 14px !important;
      }
    }
  }
}
/deep/.elDialogAdd {
  .is-active > span {
    margin-left: 0 !important;
  }
  .el-radio-button__inner {
    padding: 8px 20px !important;
  }
}
</style>
