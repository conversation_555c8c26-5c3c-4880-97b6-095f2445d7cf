<template>
  <div class="container">
    <div class="headerTitle">线索黑名单管理</div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入线索内容检索"
            @keyup.enter.native="checkFuncList"
            id="keyword_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="keyword_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="keyword_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="remove('more')"
            id="keyword_more_del"
            >删除</el-button
          >
          <el-button class="normalBtn" type="primary" @click="addEdit()" id="keyword_add"
            >新建线索</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="blackClue"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            min-width="120"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'type'">{{ getTypeName(scope.row[item.name]) }}</span>
              <span v-else-if="item.name == 'name'">
                <span v-if="scope.row.type == 3">
                  <el-image
                    class="imgwrap"
                    :src="
                      scope.row[item.name].includes('http')
                        ? scope.row[item.name]
                        : showSrcIp + scope.row[item.name]
                    "
                    alt=""
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                  {{ scope.row.hash }}
                </span>
                <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
              </span>
              <span v-else-if="item.name == 'userName'">{{ scope.row.user.name }}</span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="150">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="remove('one', scope.row.id)"
                id="keyword_del"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="500px"
      v-if="dialogFormVisibleInsert"
    >
      <template slot="title"> 新建 </template>
      <div class="dialog-body">
        <el-form
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="ruleForm"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="线索类型" prop="type">
            <el-select v-model="ruleForm.type" @change="wayChange" placeholder="请选择线索类型">
              <el-option
                v-for="item in typeArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="ruleForm.type != 3" label="线索内容" prop="name">
            <el-input
              type="textarea"
              :rows="10"
              v-model="ruleForm.name"
              :placeholder="
                ruleForm.type == 1
                  ? '请输入线索内容，单条线索CN和O只能输入一种，多个值请用分号或换行分隔，每条线索不能超过200字符'
                  : '请输入线索内容，多个值请用分号或换行分隔，每条线索不能超过200字符'
              "
            ></el-input>
          </el-form-item>
          <el-form-item v-if="ruleForm.type == 3" label="线索内容" prop="name">
            <el-upload
              class="upload-demo"
              drag
              :action="uploadSrcIp + '/assets/account/files'"
              :headers="uploadHeaders"
              :before-upload="beforeUpload"
              :on-success="iconUploadSucc"
              :on-remove="iconUploadRemove"
              :on-error="uploadErr"
              :multiple="true"
              list-type="picture"
              accept=".png,.ico,.bmp,.jpg,.jpeg"
              :file-list="fileList"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将ICON拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip"
                >支持上传.png,.ico,.bmp,.jpg,.jpeg文件，且大小不超过3M</div
              >
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="otherLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="线索类型：" prop="type">
            <el-select
              filterable
              v-model="formInline.type"
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'type', typeArr, true, false)"
            >
              <el-option
                v-for="item in typeArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="线索内容：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入线索内容检索"></el-input>
          </el-form-item>
          <el-form-item label="添加时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { blackClueIndex, addBlackClue, delBlackClue } from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      highTabShow: [
        {
          label: '线索类型',
          name: 'type',
          type: 'select'
        },
        {
          label: '线索内容',
          name: 'name',
          type: 'input'
        },
        {
          label: '添加时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      typeArr: [
        {
          id: '0',
          name: '根域'
        },
        {
          id: '1',
          name: '证书'
        },
        {
          id: '2',
          name: 'ICP'
        },
        {
          id: '3',
          name: 'ICON'
        },
        {
          id: '4',
          name: '关键词'
        },
        {
          id: '5',
          name: '子域名'
        }
      ],
      formInline: {
        page: 1,
        per_page: 10,
        keyword: '',
        type: '',
        name: '',
        created_at_range: [],
        operate_company_id: ''
      },
      checkedAll: false,
      highCheckdialog: false,
      tableHeader: [
        {
          label: '线索类型',
          name: 'type'
        },
        {
          label: '线索内容',
          name: 'name'
        },
        {
          label: '标记人',
          name: 'userName'
        },
        {
          label: '添加时间',
          name: 'created_at'
        }
      ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      dialogFormVisibleInsert: false,
      ruleForm: {
        type: '0',
        name: '',
        files: []
      },
      rules: {
        name: [{ required: true, message: ' ', trigger: 'blur' }]
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      user: {
        role: ''
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    getTypeName(status) {
      let str = ''
      switch (status) {
        case 0:
          str = '根域'
          break
        case 1:
          str = '证书'
          break
        case 2:
          str = 'ICP'
          break
        case 3:
          str = 'ICON'
          break
        case 4:
          str = '关键词'
          break
        case 5:
          str = '子域名'
          break
        default:
      }
      return str
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    getData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at_range = this.formInline.created_at_range
        ? this.formInline.created_at_range
        : []
      blackClueIndex(this.formInline)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async remove(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'keyword_del_cancel',
        confirmButtonClass: 'keyword_del_sure',
        customClass: 'keyword_del',
        type: 'warning'
      })
        .then(async () => {
          this.formInline.page = this.currentPage
          this.formInline.per_page = this.pageSize
          if (this.checkedAll) {
            this.formInline.id = icon == 'one' ? [id] : []
          } else {
            this.formInline.id =
              icon == 'one'
                ? [id]
                : this.checkedArr.map((item) => {
                    return item.id
                  })
          }
          let obj = Object.assign({}, this.formInline)
          let res = await delBlackClue(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.checkedAll = false
            this.$refs.eltable.clearSelection()
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.keyword_del>.el-message-box__btns')
        del.children[0].id = 'keyword_del_cancel'
        del.children[1].id = 'keyword_del_sure'
      }, 50)
    },
    wayChange(val) {
      this.ruleForm.name = ''
      this.fileList = []
      this.ruleForm.files = []
    },
    iconUploadSucc(response, file, fileList) {
      if (file.response.code == 0) {
        this.ruleForm.files.push(response.data.url)
      } else {
        this.$message.error(file.response.message)
      }
    },
    iconUploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data.url
      })
      if (res.length == 0) {
        this.ruleForm.files = []
      } else {
        this.ruleForm.files = res
      }
    },
    uploadErr(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    beforeUpload(file, limit = 3) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    },
    async insertSave(formName) {
      if (this.ruleForm.type != 3 && !this.ruleForm.name) {
        this.$message.error('线索内容为必填')
        return
      }
      if (this.ruleForm.type == 3 && this.ruleForm.files.length == 0) {
        this.$message.error('线索内容为必填')
        return
      }
      if (this.ruleForm.type == 1) {
        // 校验证书线索单条不能同时存在CN=和O=，因为资产列表中CN和O拆开了
        let regName = true
        this.ruleForm.name
          .toLowerCase()
          .split(/[；|;|\r\n]/)
          .filter((item) => {
            return item.trim()
          })
          .map((item) => {
            return item.trim()
          })
          .some((item, index) => {
            if (item.indexOf('cn=') > -1 && item.indexOf('o=') > -1) {
              this.$message.error(`第${index + 1}条线索同时包含CN和O，请重新输入`)
              regName = false
              return true
            }
          })
        if (!regName) {
          return
        }
      }
      let obj = {
        files: this.ruleForm.files,
        type: this.ruleForm.type,
        name: this.ruleForm.name
          .split(/[；|;|\r\n]/)
          .filter((item) => {
            return item.trim()
          })
          .map((item) => {
            return item.trim()
          }),
        operate_company_id: this.currentCompany
      }
      this.otherLoading = true
      let res = await addBlackClue(obj).catch(() => {
        this.otherLoading = false
      })
      if (res.code == 0) {
        this.otherLoading = false
        this.dialogFormVisibleInsert = false
        this.getData()
        this.$message.success('操作成功！')
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        page: 1,
        per_page: 10,
        keyword: '',
        type: '',
        name: '',
        created_at_range: [],
        operate_company_id: ''
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    addEdit(row) {
      this.dialogFormVisibleInsert = true
      this.ruleForm = {
        type: '0',
        name: '',
        files: []
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}
</style>
