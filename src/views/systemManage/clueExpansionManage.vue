<template>
  <div class="container">
    <div class="headerTitle">线索扩展任务管理</div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入线索内容检索"
            @keyup.enter.native="checkFuncList"
            id="keyword_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="keyword_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div></div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="exportlog"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            min-width="120"
            :label="item.label"
          >
            <template slot-scope="scope">
              <!-- 线索类型 -->
              <span v-if="item.name == 'type'">{{ getTypeName(scope.row[item.name]) || '-' }}</span>
              <span v-else-if="item.name == 'process'"
                >{{ formatProgress(scope.row[item.name]) }}
              </span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="150">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleShowDetail(scope.row)"
                >查看详情</el-button
              >
              <el-button type="text" size="small" @click="handleRefresh(scope.row)"
                >强制刷新</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item
            v-for="item in tableHeaderIsShow"
            :key="item.id"
            :label="item.label"
            :prop="item.name"
          >
            <!-- <el-input
              v-if="item.name == 'process'"
              v-model="formInline[item.name]"
              placeholder="请输入"
              clearable
            ></el-input> -->
            <el-select
              v-if="item.name == 'process'"
              clearable
              filterable
              v-model="formInline[item.name]"
              @change="selectChange($event, 'process', processArr, true, false)"
            >
              <el-option
                v-for="item in processArr"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-select
              v-else-if="item.name == 'type'"
              clearable
              filterable
              v-model="formInline[item.name]"
              @change="selectChange($event, 'type', typeArr, true, false)"
            >
              <el-option
                v-for="item in typeArr"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-date-picker
              v-if="item.icon == 'date'"
              v-model="formInline[item.name]"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
    <!-- 详情弹窗 -->
    <el-dialog
      v-if="showDetail"
      title="详情"
      :close-on-click-modal="false"
      :visible.sync="showDetail"
      width="80%"
    >
      <div class="dialog-body">
        <ul class="recordClass">
          <li v-for="(con, index) in recordsTableHeader" :key="index">
            <div
              v-if="
                getRecords(currentDetail.items)['arr' + con.type] &&
                getRecords(currentDetail.items)['arr' + con.type].length > 0
              "
            >
              <p>
                {{ con.label }}：{{ getRecords(currentDetail.items)['arr' + con.type].length }}
              </p>
              <div
                class="recordItem"
                v-for="ch in getRecords(currentDetail.items)['arr' + con.type]"
                :key="ch.id"
              >
                <span v-if="ch.type && ch.type == 3">
                  <el-image
                    :src="ch.content.includes('http') ? ch.content : showSrcIp + ch.content"
                    alt=""
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                  {{ ch.hash }}
                </span>
                <span v-else>
                  {{ $punyCode.toUnicode(ch.content || ch) }}
                  {{ ch.punycode_domain ? '(' + ch.punycode_domain + ')' : '' }}
                </span>
                <el-tooltip class="item" effect="dark" placement="top">
                  <div class="recordItemTipContent" slot="content">
                    <el-descriptions>
                      <el-descriptions-item
                        v-for="desc in certTipData()"
                        :key="desc.key"
                        :label="desc.name"
                        >{{ formatRecordTipText(desc.key, ch[desc.key]) }}</el-descriptions-item
                      >
                    </el-descriptions>
                  </div>
                  <i v-if="ch.type == 1" class="recordItemTip el-icon-info"></i>
                </el-tooltip>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="showDetail = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  clueExpandTaskList,
  clueExpandTaskResult,
  clueExpandTaskIcp,
  clueExpandTaskDomain,
  clueExpandTaskKeyword,
  clueExpandTaskIp,
  clueExpandTaskCert,
  clueExpandTaskIcon,
  clueExpandTaskCompany,
  clueExpandTaskSubdomain
} from '@/api/apiConfig/api.js'

export default {
  name: 'ClueExpansionManage',
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      processArr: [
        { name: '已完成', value: '100' },
        { name: '未完成', value: '-1' }
      ],
      highTabShow: [
        {
          label: '关键字',
          name: 'keyword',
          type: 'input'
        },
        {
          label: '线索类型',
          name: 'type',
          type: 'select'
        },
        {
          label: '扩展状态',
          name: 'process',
          type: 'select'
        },
        {
          label: '开始时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '结束时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      tableHeaderIsShow: [
        {
          id: '4',
          name: 'type',
          icon: 'select',
          label: '线索类型'
        },
        {
          id: '1',
          name: 'process',
          icon: 'input',
          label: '扩展状态'
        },
        {
          id: '2',
          name: 'created_at',
          icon: 'date',
          label: '开始时间'
        },
        {
          id: '3',
          name: 'updated_at',
          icon: 'date',
          label: '结束时间'
        }
      ],
      typeArr: [
        {
          name: '证书',
          value: 1
        },
        {
          name: 'ICP',
          value: 2
        },
        {
          name: 'LOGO',
          value: 3
        },
        {
          name: '关键词',
          value: 4
        },
        {
          name: '域名',
          value: 5
        },
        {
          name: 'IP',
          value: 6
        },
        {
          name: '子域名',
          value: 7
        },
        {
          name: '企业名称',
          value: 8
        }
      ],
      formInline: {
        keyword: '',
        type: '',
        process: '',
        created_at: [],
        updated_at: []
      },
      recordsTableHeader: [
        {
          name: '6',
          label: 'IP段',
          type: '6'
        },
        {
          name: 'content',
          label: '根域',
          type: '5'
        },
        {
          name: 'content',
          label: '证书',
          type: '1'
        },
        {
          name: 'content',
          label: 'ICP',
          type: '2'
        },
        {
          name: '3',
          label: 'LOGO',
          type: '3'
        },
        {
          name: '4',
          label: '关键词',
          type: '4'
        },
        {
          name: '7',
          label: '子域名',
          type: '7'
        },
        {
          name: '8',
          label: '企业名称',
          type: '8'
        }
      ],
      highCheckdialog: false,
      tableHeader: [
        {
          label: '基础线索',
          name: 'keyword',
          minWidth: '100'
        },
        {
          label: '线索类型',
          name: 'type',
          minWidth: '50'
        },
        {
          label: '扩展状态',
          name: 'process',
          minWidth: '50'
        },
        {
          label: '开始时间',
          name: 'created_at',
          minWidth: '80'
        },
        {
          label: '结束时间',
          name: 'updated_at',
          minWidth: '80'
        }
      ],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      loading: false,
      currentDetail: {},
      showDetail: false,
      user: {
        role: ''
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
  },
  computed: {
    formatRecordTipText() {
      return (key, value) => {
        const certValidMap = {
          0: '否',
          1: '是'
        }
        if (key === 'cert_valid') return certValidMap[value]
        return value
      }
    }
  },
  watch: {
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  methods: {
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    getTypeName(type) {
      let str = ''
      switch (type) {
        case 1:
          str = '证书'
          break
        case 2:
          str = 'ICP'
          break
        case 3:
          str = 'LOGO'
          break
        case 4:
          str = '关键词'
          break
        case 5:
          str = '域名'
          break
        case 6:
          str = 'IP'
          break
        case 7:
          str = '子域名'
          break
        case 8:
          str = '企业名称'
          break
        default:
      }
      return str
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    getData(tmp) {
      this.loading = true
      this.formInline.created_at = this.formInline.created_at ? this.formInline.created_at : []
      let created_at = JSON.parse(JSON.stringify(this.formInline.created_at))
      if (created_at.length != 0) {
        created_at[0] = created_at[0] + ' ' + '00:00:00'
        created_at[1] = created_at[1] + ' ' + '23:59:59'
      }
      this.formInline.updated_at = this.formInline.updated_at ? this.formInline.updated_at : []
      let updated_at = JSON.parse(JSON.stringify(this.formInline.updated_at))
      if (updated_at.length != 0) {
        updated_at[0] = updated_at[0] + ' ' + '00:00:00'
        updated_at[1] = updated_at[1] + ' ' + '23:59:59'
      }

      const params = {
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline,
        created_at,
        updated_at
      }
      clueExpandTaskList(params)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items ? res.data.items : []
          this.total = res.data.total ? res.data.total : 0
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async getTaskDetail(id) {
      const params = {
        task_id: id
      }
      const res = await clueExpandTaskResult(params).catch((err) => {
        console.log(err)
      })
      if (res.code == 0) {
        this.currentDetail = res.data
      }
    },
    // 查看详情
    async handleShowDetail(row) {
      await this.getTaskDetail(row.id)
      this.showDetail = true
    },
    // 强制刷新
    handleRefresh(row) {
      const apiMap = {
        1: clueExpandTaskCert,
        2: clueExpandTaskIcp,
        3: clueExpandTaskIcon,
        4: clueExpandTaskKeyword,
        5: clueExpandTaskDomain,
        6: clueExpandTaskIp,
        7: clueExpandTaskSubdomain,
        8: clueExpandTaskCompany
      }
      const params = {
        force: true,
        // keyword: row.type != 3 ? row.keyword : '',
        keyword: row.keyword,
        hash: row.type == 3 ? Number(row.keyword) : null,
        source: row.source,
        company_name: row.company_name
      }
      apiMap[row.type](params).then((res) => {
        if (res.code == 0) {
          this.getData()
          this.$message.success('操作成功！')
        }
      })
    },
    getRecords(clue) {
      let obj = {
        arr0: [],
        arr1: [],
        arr2: [],
        arr3: [],
        arr4: [],
        arr5: [],
        arr6: [],
        arr7: [],
        arr8: []
      }
      clue.forEach((item) => {
        if (item.type || item.type == 0) {
          if (item && item.content) {
            const tempData = {
              id: item.id,
              type: item.type,
              content: item.content,
              hash: item.hash,
              punycode_domain: item.punycode_domain,
              cert_valid: item.cert_valid,
              source: item.source
            }
            obj['arr' + item.type].push(tempData)
          }
        }
      })
      return obj
    },
    certTipData() {
      const list = [
        {
          name: '证书有效',
          key: 'cert_valid'
        },
        {
          name: '来源',
          key: 'source'
        }
      ]
      return list
    },
    formatProgress(value) {
      if (value == 100) return `已完成(${value}%)`
      return `${value}%`
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
      .clueClass {
        display: flex;
        align-items: center;
      }
      .clueNumClass {
        display: block;
        padding: 1px 5px;
        margin-right: 10px;
        color: #2677ff;
        border: 1px solid #ebeef5;
      }
      .detail {
        width: 100%;
        display: block;
        margin-bottom: 5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
.dialog-body {
  max-height: 500px;
  overflow: auto;
}
.recordClass {
  li {
    padding-bottom: 10px;
    p {
      line-height: 24px;
      font-weight: bold;
      color: #2677ff;
    }
    .recordItem {
      position: relative;
      display: inline-block;
      width: 19%;
      min-width: 120px;
      text-overflow: inherit;
      padding: 2px 15px 2px 2px;
      margin: 1px 1px;
      border: 1px solid #ebeef5;
      background: rgb(245, 247, 250);
      span {
        display: inline-block;
        width: 100%;
        white-space: pre-wrap;
      }
      .recordItemTip {
        position: absolute;
        top: 50%;
        right: 2px;
        transform: translateY(-50%);
        color: #acb4c0;
        cursor: pointer;
      }
    }
  }
  span {
    display: inline-block;
    line-height: 24px;
    .el-image {
      vertical-align: middle;
    }
  }
}
</style>
