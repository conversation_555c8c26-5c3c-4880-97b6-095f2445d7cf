<template>
  <div class="container">
    <div class="headerTitle">定时任务</div>
    <div class="home_header">
      <div class="filterTab">
        <div></div>
        <div>
          <!-- <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="poc_all"
            >选择全部</el-checkbox
          >
          <el-button class="normalBtn" type="primary" @click="removeOne('more')"
            >删除</el-button
          > -->
          <el-button class="normalBtn" type="primary" @click="addEdit()">新建</el-button>
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highlist="highlist"
        :total="total"
        pageIcon="blackClue"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <!-- <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column> -->
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            min-width="120"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'status'">
                <span class="greenLine" v-if="scope.row[item.name] == 1">启用</span>
                <span class="redLine" v-else>禁用</span>
              </span>
              <span v-else-if="item.name == 'type'">
                <span class="greenLine" v-if="scope.row[item.name] == 1">系统任务</span>
                <span class="redLine" v-else>其他任务</span>
              </span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="150">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="removeOne('one', scope.row.id)"
                >删除</el-button
              >
              <el-button type="text" size="small" @click="addEdit(scope.row)">编辑</el-button>
              <el-button type="text" size="small" @click="handleToHistory(scope.row.id)"
                >执行记录</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="550px"
      v-if="dialogFormVisibleInsert"
    >
      <template slot="title">新建</template>
      <div class="dialog-body">
        <el-form
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="ruleForm"
          label-width="90px"
          class="demo-ruleForm"
        >
          <el-form-item label="任务名称" prop="name">
            <el-input v-model="ruleForm.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="调用方法" prop="method">
            <el-input v-model="ruleForm.method" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="任务类型" prop="type">
            <el-select v-model="ruleForm.type" placeholder="请选择" clearable>
              <el-option label="系统任务" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="ruleForm.status"
              active-text="开启"
              inactive-text="禁用"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </el-form-item>
          <el-form-item label="定时参数" prop="spec">
            <el-input v-model="ruleForm.spec" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="任务参数" prop="params">
            <el-input v-model="ruleForm.params" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="otherLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px"> </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { cronTaskList, addCronTask, delCronTask, editCronTask } from '@/api/apiConfig/api.js'

export default {
  name: 'CronTask',
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      checkedAll: false, // 是否全选
      checkedArr: [],
      formInline: {
        page: 1,
        per_page: 10
      },
      highCheckdialog: false,
      tableHeader: [
        {
          label: '任务名称',
          name: 'name'
        },
        {
          label: '状态',
          name: 'status'
        },
        {
          label: '任务类型',
          name: 'type'
        },
        {
          label: '定时参数',
          name: 'spec'
        },
        {
          label: '调用方法',
          name: 'method'
        },
        {
          label: '创建时间',
          name: 'created_at'
        },
        {
          label: '更新时间',
          name: 'updated_at'
        }
      ],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      dialogFormVisibleInsert: false,
      rules: {
        name: [{ required: true, message: '请输入', trigger: 'blur' }],
        method: [{ required: true, message: '请输入', trigger: 'blur' }],
        type: [{ required: true, message: '请选择', trigger: 'change' }],
        status: [{ required: true, message: '请选择', trigger: 'change' }],
        // params: [{ required: true, message: "请输入", trigger: "blur" }],
        spec: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      ruleForm: {
        name: '',
        method: '',
        type: 1,
        status: 1,
        params: '',
        spec: ''
      },
      loading: false,
      otherLoading: false,
      user: {
        role: ''
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    getData(tmp) {
      if (!tmp) {
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      cronTaskList(this.formInline)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    insertSave(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.otherLoading = true
          let params = { ...this.ruleForm }
          params.user_id = this.user.id
          let res = null
          if (params.id) {
            res = await editCronTask(params).catch(() => {
              this.otherLoading = false
            })
          } else {
            res = await addCronTask(params).catch(() => {
              this.otherLoading = false
            })
          }
          this.otherLoading = false
          if (res.code == 0) {
            this.dialogFormVisibleInsert = false
            this.getData()
            this.$message.success('操作成功！')
          }
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    addEdit(row) {
      this.dialogFormVisibleInsert = true
      if (row) {
        this.ruleForm = { ...row }
      } else {
        this.ruleForm = {
          method: '',
          type: 1,
          name: '',
          status: 1,
          params: '',
          spec: ''
        }
      }
    },
    // 删除
    removeOne(icon, id) {
      if (icon == 'more' && !this.checkedArr.length) {
        return this.$message.error('请选择要删除的数据！')
      }
      this.$confirm('确定删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const params = {
            id: [id]
          }
          if (icon == 'more') {
            params.id = this.checkedAll
              ? []
              : this.checkedArr.map((item) => {
                  return item.id
                })
            Object.assign(params, this.formInline)
          }
          let res = await delCronTask(params)
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.currentPage = this.updateCurrenPage(
              this.total,
              [1],
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getData()
          }
        })
        .catch(() => {})
    },
    // 查看任务执行记录
    handleToHistory(id) {
      this.$router.push({
        path: '/cronTaskHistory',
        query: {
          id
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}
</style>
