<template>
  <div class="container">
    <div class="headerTitle">
      <span class="goback" @click="$router.go(-1)"
        ><i class="el-icon-arrow-left"></i>返回上一层</span
      >
      <span class="spline">/</span>
      <span>任务执行记录 </span>
    </div>
    <div class="filterTab">
      <div>
        <span @click="highIsShow = true" id="cloud_info_filter" style="width: 80px"
          ><img
            src="../../assets/images/filter.png"
            alt=""
            style="width: 16px; vertical-align: middle; margin-right: 3px"
          />高级筛选</span
        >
      </div>
      <div>
        <el-button id="poc_del" class="normalBtnRe" type="primary" @click="handleClearHistory"
          >清除任务执行记录</el-button
        >
      </div>
    </div>
    <!-- 高级筛选条件 -->
    <hightFilter
      id="hightFilter"
      :highlist="highlist"
      :total="total"
      pageIcon="cron"
      @highcheck="highCheck"
    ></hightFilter>
    <div :class="hightFilterIsShow()">
      <el-table
        border
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
        @selection-change="handleSelectionChange"
        @cell-mouse-enter="showTooltip"
        @cell-mouse-leave="hiddenTooltip"
        ref="eltable"
        height="100%"
        style="width: 100%"
      >
        <!-- <el-table-column
          type="selection"
          align="center"
          :reserve-selection="true"
          :selectable="handleSelectable"
          width="55"
        >
        </el-table-column> -->
        <el-table-column
          v-for="item in tableHeader"
          :key="item.id"
          :prop="item.name"
          align="left"
          :min-width="item.minWidth"
          :label="item.label"
        >
          <template slot-scope="scope">
            <span v-if="item.name == 'status'">
              <span class="greenLine" v-if="scope.row[item.name] == 1">成功</span>
              <span class="redLine" v-else>失败</span>
            </span>
            <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="pageSizeArr"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
    >
    </el-pagination>
    <el-drawer title="高级筛选" :visible.sync="highIsShow" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item
            v-for="item in tableHeaderIsShow"
            :key="item.id"
            :label="item.label"
            :prop="item.name"
          >
            <el-select
              v-if="item.icon == 'select'"
              clearable
              filterable
              v-model="formInline[item.name]"
              @change="selectChange($event, 'status', statusData, true, false)"
            >
              <el-option
                v-for="item in statusData"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-date-picker
              v-if="item.icon == 'date'"
              v-model="formInline[item.name]"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="cloud_info_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="cloud_info_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { mapGetters, mapState } from 'vuex'
import { cronTaskHistoryList, delCronTaskHistory } from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip, hightFilter },
  data() {
    return {
      highlist: null,
      highIsShow: false,
      statusData: [
        {
          value: 0,
          name: '失败'
        },
        {
          value: 1,
          name: '成功'
        }
      ],
      tableHeaderIsShow: [
        {
          id: '1',
          name: 'status',
          icon: 'select',
          label: '执行状态'
        },
        {
          id: '2',
          name: 'start_at',
          icon: 'date',
          label: '开始时间'
        },
        {
          id: '3',
          name: 'end_at',
          icon: 'date',
          label: '结束时间'
        }
      ],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      loading: false,
      formInline: {
        status: '',
        start_at: [],
        end_at: []
      },
      checkedAll: false,
      tableData: [],
      checkedArr: [],
      currentPage: 1,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      tableHeader: [
        {
          label: '执行状态',
          name: 'status',
          minWidth: '50'
        },
        {
          label: '开始时间',
          name: 'start_at',
          minWidth: '80'
        },
        {
          label: '结束时间',
          name: 'end_at',
          minWidth: '80'
        },
        {
          label: '执行结果',
          name: 'result'
        }
      ],
      user: {
        role: ''
      }
    }
  },
  watch: {
    getterCompanyChange(val) {
      this.$router.go(-1) // 安服账号切换企业需要回到前一级页面
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    },
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      if (val) {
        this.getData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.getData()
  },

  methods: {
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    resetForm(drawerForm) {
      this.$nextTick(() => {
        this.$refs.drawerForm.resetFields()
      })
    },
    async getData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }

      this.loading = true
      this.highIsShow = false
      const content = {
        id: this.$route.query.id,
        data: {
          page: this.currentPage,
          per_page: this.pageSize,
          ...this.formInline
        }
      }
      const res = await cronTaskHistoryList(content).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
      })
      this.loading = false
      this.tableData = res.data.items
      this.total = res.data.total
      this.$nextTick(() => {
        this.$refs.eltable.clearSelection()
      })
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    // 清除任务执行记录
    handleClearHistory() {
      this.$confirm('确定清除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const params = {
            id: this.$route.query.id
          }
          let res = await delCronTaskHistory(params)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.getData()
          }
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  .dialog-body {
    & > .el-form {
      width: 100%;
    }
    img {
      display: block;
      margin: 0 auto;
      margin-bottom: 30px;
    }
    .el-form-item {
      margin-bottom: 20px !important;
    }
  }
  .filterTab {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #fff;
    & > div {
      .el-input {
        width: 240px;
        margin-right: 0;
      }
      & > span {
        font-weight: 400;
        color: #2677ff;
        line-height: 20px;
        margin-left: 16px;
        cursor: pointer;
      }
    }
  }
  & > .el-form {
    position: relative;
    // height: 244px;
    overflow: hidden;
    background: #eff2f7;
    border-radius: 4px;
    margin: 0 20px 28px;
    padding: 20px;
    .divider {
      position: absolute;
      bottom: 12px;
      width: 100%;
      text-align: center;
      color: #2677ff;
      font-size: 14px;
      i {
        margin-left: 4px;
      }
    }
    .el-form-item {
      margin-bottom: 0px !important;
    }
    .el-form-item__content {
      & > div {
        display: flex;
      }
    }
    .el-checkbox-group {
      margin-left: 20px;
    }
  }
  .tableWrapFilter {
    height: calc(100% - 175px) !important;
  }
  .tableWrap {
    height: calc(100% - 129px);
  }
  .el-table {
    width: 99%;
    border: 0;
    .companyClass {
      padding: 0 2px;
      height: 40px;
      line-height: 40px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .clueClass {
      display: flex;
      align-items: center;
    }
    .clueNumClass {
      display: block;
      padding: 1px 5px;
      margin-right: 10px;
      color: #2677ff;
      border: 1px solid #ebeef5;
    }
    .detail {
      width: 100%;
      display: block;
      margin-bottom: 5px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
