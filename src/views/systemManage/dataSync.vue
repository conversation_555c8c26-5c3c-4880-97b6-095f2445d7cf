<template>
  <div class="container">
    <div class="headerTitle">数据同步</div>
    <div class="home_header">
      <div class="filterTab">
        <div></div>
        <div>
          <el-button class="normalBtn" type="primary" @click="syncCopy()" id="user_add"
            >同步线上数据</el-button
          >
        </div>
      </div>
      <div class="tableWrap">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <template v-if="item.name == 'status' && scope.row[item.name]">
                {{ statusMap[Number(scope.row[item.name])] }}
              </template>
              <template v-else-if="item.name == 'result_json' && scope.row[item.name]">
                <el-tooltip class="item" effect="dark" placement="top">
                  <div slot="content" class="result">
                    <div class="title">获取到数据如下：</div>
                    <div class="line">{{
                      `IP资产 ${scope.row.result_json.table_ip} 个，域名资产 ${scope.row.result_json.domain} 个，证书资产 ${scope.row.result_json.cert_num} 个，登录入口资产 ${scope.row.result_json.login_page} 个，业务系统 ${scope.row.result_json.bussiness_system} 个，URL（api）资产 ${scope.row.result_json.url_api} 个;`
                    }}</div>
                    <div class="line">{{
                      `疑似资产 ${scope.row.result_json.unsure_ip} 个，忽略资产 ${scope.row.result_json.ingore_ip} 个，数字资产 ${scope.row.result_json.app} 个;`
                    }}</div>
                    <div class="line">{{
                      `漏洞跟踪 ${scope.row.result_json.poc} 个，数据泄漏 ${scope.row.result_json.data_leak} 个，事件告警 ${scope.row.result_json.warn_event} 个，威胁资产 ${scope.row.result_json.threaten_ip} 个。`
                    }}</div>
                  </div>
                  <span>
                    {{
                      `IP资产 ${scope.row.result_json.table_ip} 个，域名资产 ${scope.row.result_json.domain} 个，证书资产 ${scope.row.result_json.cert_num} 个，登录入口资产 ${scope.row.result_json.login_page} 个，业务系统 ${scope.row.result_json.bussiness_system} 个，URL（api）资产 ${scope.row.result_json.url_api} 个;
                      疑似资产 ${scope.row.result_json.unsure_ip} 个，忽略资产 ${scope.row.result_json.ingore_ip} 个，数字资产 ${scope.row.result_json.app} 个;
                      漏洞跟踪 ${scope.row.result_json.poc} 个，数据泄漏 ${scope.row.result_json.data_leak} 个，事件告警 ${scope.row.result_json.warn_event} 个，威胁资产 ${scope.row.result_json.threaten_ip} 个。
                      `
                    }}
                  </span>
                </el-tooltip>
              </template>
              <template v-else-if="!scope.row[item.name]">
                <span>-</span>
              </template>
              <template v-else>
                <el-tooltip class="item" effect="dark" placement="top">
                  <div slot="content">
                    {{ scope.row[item.name] }}
                  </div>
                  <span>{{ scope.row[item.name] }}</span>
                </el-tooltip>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <prompt
      title="同步数据"
      placeholder="请输入需要同步数据账号的key"
      :visible="dialogVisible"
      @save="saveKey"
      @close="dialogVisible = false"
      :loading="dialogBtnLoading"
      :inputValueBack="bearKey"
      label="同步数据key"
      labelWidth="120"
      width="700px"
    >
      <template #tip>
        <div class="diaTip">
          <i class="el-icon-warning"></i> 此操作将把SAAS上数据同步至本地化，确定继续吗
        </div>
      </template>
      <template #labelIcon>
        <el-tooltip
          class="item"
          effect="dark"
          content="SAAS平台目标账号的API KEY如果有刷新，需要将此处编辑为最新的API KEY"
          placement="top"
        >
          <i class="el-icon-question diaQues"></i>
        </el-tooltip>
      </template>
    </prompt>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import prompt from '@/components/assets/prompt'
import { syncCopyAssets, syncCopyList } from '@/api/apiConfig/api.js'
import { personInfo } from '@/api/apiConfig/person.js'

export default {
  components: {
    prompt
  },
  data() {
    return {
      bearKey: '',
      pageSize: 10,
      currentPage: 1,
      dialogVisible: false,
      dialogBtnLoading: false,
      tableData: [],
      loading: false,
      total: 0,
      tableHeader: [
        {
          label: '同步任务名称',
          name: 'task_name',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '拉取时间',
          name: 'created_at',
          minWidth: 120
        },
        {
          label: '结束时间',
          name: 'updated_at',
          minWidth: 120
        },
        {
          label: '任务状态',
          name: 'status',
          minWidth: 120
        },
        {
          label: '任务结果',
          name: 'result_json',
          minWidth: 120
        }
      ],
      userInfo: {},
      user: {
        role: ''
      },
      statusMap: {
        0: '进行中',
        1: '成功',
        2: '失败'
      }
    }
  },
  watch: {
    dialogVisible() {}
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  created() {
    let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (userInfo) {
      this.user = userInfo.user
    }
    this.getData()
  },
  methods: {
    getData() {
      syncCopyList({ page: this.currentPage, per_page: this.pageSize }).then((res) => {
        this.loading = false
        this.tableData = res.data.items || []
        this.total = res.data.total || 0
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    saveKey(val) {
      if (!val) {
        this.$message.error('请填写key值')
        return
      }
      this.syncCopyAction(val)
    },
    async syncCopy() {
      let userMessage = await personInfo({ detail: 1, operate_company_id: this.currentCompany })
      if (userMessage) {
        this.bearKey = userMessage.data.user.bear_token
        this.dialogVisible = true
      }
      // 从账号信息接口判断是否设置过 同步数据key
      // if(!this.user.bear_token){
      //   this.dialogVisible = true
      // } else {
      //   this.syncCopyAction()
      // }
    },
    syncCopyAction(val) {
      // this.$confirm('此操作将把sass上数据同步本地化，确定吗', '提示', {
      //   confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     cancelButtonClass: 'cloud_info_del_cancel',
      //     confirmButtonClass: 'cloud_info_del_sure',
      //     customClass:'cloud_info_del',
      //     type: 'warning'
      //   }).then(() => {
      this.dialogBtnLoading = true
      syncCopyAssets({ bear_token: val }).then((res) => {
        if (res.code == '0') {
          this.dialogVisible = false
          this.$message({
            type: 'success',
            message: '同步成功!'
          })
          this.dialogBtnLoading = false
          this.currentPage = 1
          this.getData()
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
      }
    }
  }
}

.tableWrap {
  height: calc(100% - 129px);
  padding: 0px 20px;
}
.result {
  .title {
    margin-bottom: 10px;
  }

  .line {
    height: 24px;
    line-height: 24px;
  }
}
.diaTip {
  margin: 10px 0;
  font-size: 16px;
  color: #3c3c3c;
}

.el-icon-warning {
  color: #e6a23c;
}
.diaQues {
  margin-left: 8px;
}
</style>
