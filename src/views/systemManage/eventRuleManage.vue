<template>
  <div class="container" v-loading="loading">
    <div class="headerTitle">事件规则管理</div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane :label="user.role == 1 ? '规则库' : '内置规则'" name="first">
          <div class="filterTab">
            <div>
              <el-input
                v-model="formInline.keyword"
                @keyup.enter.native="checkFuncList"
                placeholder="请输入事件名称进行搜索"
                id="poc_keycheck1"
              >
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
              </el-input>
              <el-select
                style="margin-left: 10px"
                filterable
                v-model="formInline.event_type"
                placeholder="请选风险类型"
                collapse-tags
                clearable
                @change="checkFuncList"
              >
                <el-option
                  v-for="item in eventTypeList"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
              <span
                @click="highCheckdialog = true"
                id="poc_filter"
                style="width: 80px"
                v-if="activeName == 'first'"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <div>
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAll"
                @change="checkAllChange"
                id="poc_all"
                >选择全部</el-checkbox
              >
              <el-button
                v-if="user.role != 1"
                class="normalBtn"
                type="primary"
                @click="updatePortStatus(1)"
                id="poc_open"
                >启用</el-button
              >
              <el-button
                v-if="user.role != 1"
                class="normalBtnRe"
                type="primary"
                @click="updatePortStatus(0)"
                id="poc_disable"
                >禁用</el-button
              >
              <el-button
                v-if="(user.role == 2 && currentCompany == -1) || user.role == 1"
                class="normalBtn"
                type="primary"
                @click="removeOne('more')"
                id="poc_del"
                >删除</el-button
              >
              <el-button
                v-if="
                  !this.userInfo.is_local &&
                  ((user.role == 2 && currentCompany == -1) || user.role == 1)
                "
                class="normalBtn"
                type="primary"
                @click="insertShow"
                id="poc_add"
                >添加规则</el-button
              >
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="user.role == 1 ? '规则审核' : '自定义规则'" name="second">
          <!-- 自定义规则提示说明 -->
          <p class="downloadClass" v-if="user.role != 1">
            <i class="el-icon-warning myGray"></i
            >提供风险规则公开收集入口，支持用户将关注的风险规则提交至系统，系统收录后将会同步至内置规则库，后续支持利用该规则进行资产的风险识别。
          </p>
          <div class="filterTab">
            <div>
              <el-input
                v-model="formInline.keyword"
                @keyup.enter.native="checkGroupFuncList"
                placeholder="请输入事件名称进行搜索"
                id="poc_keycheck"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="checkGroupFuncList"
                ></el-button>
              </el-input>
              <span
                @click="highCheckdialog = true"
                id="poc_filter"
                style="width: 80px"
                v-if="activeName == 'first'"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <div>
              <el-checkbox
                v-if="user.role != 1"
                class="checkboxAll"
                v-model="checkedAll"
                @change="checkAllChange"
                id="poc_all"
                >选择全部</el-checkbox
              >
              <el-select
                filterable
                v-model="formInline.pass_status"
                placeholder="请选择处理状态"
                multiple
                collapse-tags
                clearable
                @change="pass_status_Change"
              >
                <el-option
                  v-for="item in passStatusList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
              <el-button
                v-if="user.role != 1"
                class="normalBtn"
                type="primary"
                @click="removeOne('more')"
                id="poc_del"
                >删除</el-button
              >
              <el-button
                v-if="user.role != 1"
                class="normalBtn"
                type="primary"
                @click="insertShow"
                id="poc_add"
                >添加规则</el-button
              >
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="poc"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          ref="eltable"
          height="100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableHeaderIsShow"
            :key="index"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span
                v-if="item.name == 'status'"
                :class="scope.row[item.name] == 1 ? 'greenLine' : 'redLine'"
              >
                {{ scope.row[item.name] == 1 ? '启用' : '禁用' }}
              </span>
              <span
                v-else-if="item.name == 'name'"
                style="color: #2677ff; cursor: pointer"
                @click="openDetailDialog(scope.row)"
                >{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span
              >
              <el-tooltip
                v-else-if="item.name == 'pass_status' && scope.row[item.name] == 2"
                class="item"
                effect="dark"
                popper-class="chainClass"
                placement="top"
              >
                <span slot="content">
                  <span>
                    <span>驳回原因：</span><br />
                    <span>{{ scope.row.refuse_reason }}</span>
                  </span>
                </span>
                <span :class="getPassClass(scope.row[item.name])">
                  <i
                    v-if="item.name == 'pass_status' && scope.row[item.name] == 2"
                    class="el-icon-question"
                    style="cursor: pointer"
                  ></i>
                  {{ getPassStatus(scope.row[item.name]) }}
                </span>
              </el-tooltip>

              <span
                v-else-if="item.name == 'pass_status' && scope.row[item.name] != 2"
                :class="getPassClass(scope.row[item.name])"
                >{{ getPassStatus(scope.row[item.name]) }}</span
              >
              <!-- <el-tooltip v-else-if="item.name=='name' && user.role == 1 && activeName == 'first'" class="item" effect="dark" popper-class="chainClass" placement="top">
                <span slot="content">
                  <span>
                    <span>规则内容</span>
                    <span>{{scope.row.content}}</span>
                  </span><br>
                  <span>
                    <span>规则优先级</span>
                    <span>{{scope.row.priority}}</span>
                  </span><br>
                  <span>
                    <span>规则标签</span>
                    <span>{{scope.row.tag}}</span>
                  </span><br>
                </span>
                <span>
                  {{scope.row[item.name]}}
                </span>
              </el-tooltip> -->
              <span v-else-if="item.name == 'user'">{{
                scope.row[item.name] ? scope.row[item.name].name : '-'
              }}</span>
              <span
                v-else-if="item.name == 'tag'"
                :class="scope.row[item.name] ? riskLevelArrClassMap[scope.row[item.name]] : ''"
                >{{ scope.row[item.name] ? tagListMap[scope.row[item.name]] : '-' }}</span
              >

              <el-tooltip v-else class="item" effect="dark" placement="top">
                <p
                  slot="content"
                  style="white-space: pre-line"
                  v-html="scope.row[item.name] ? scope.row[item.name] : '-'"
                ></p>
                <p
                  class="ellipsis-10"
                  style="white-space: pre-line"
                  v-html="scope.row[item.name] ? scope.row[item.name] : '-'"
                ></p>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" min-width="130">
            <template slot-scope="scope">
              <span>
                <el-button
                  v-if="
                    (user.role != 1 && activeName == 'second' && scope.row.pass_status !== 1) ||
                    (activeName == 'first' &&
                      (user.role == 1 ||
                        (user.role == 2 &&
                          currentCompany == -1 &&
                          (!scope.row.user_id || scope.row.user_id == user.id))))
                  "
                  type="text"
                  size="small"
                  @click="editOne(scope.row)"
                  id="poc_edit"
                  >修改</el-button
                >
                <el-button
                  v-if="
                    (activeName == 'first' &&
                      (user.role == 1 ||
                        (user.role == 2 &&
                          currentCompany == -1 &&
                          (!scope.row.user_id || scope.row.user_id == user.id)))) ||
                    (user.role != 1 && activeName == 'second')
                  "
                  type="text"
                  size="small"
                  @click="removeOne('one', scope.row.id)"
                  id="poc_del"
                  >删除</el-button
                >
                <el-button
                  v-if="activeName == 'first' && scope.row.status == 1 && user.role != 1"
                  type="text"
                  size="small"
                  @click="updatePortStatus(0, scope.row.id)"
                  id="poc_del"
                  >禁用</el-button
                >
                <el-button
                  v-else-if="activeName == 'first' && scope.row.status != 1 && user.role != 1"
                  type="text"
                  size="small"
                  @click="updatePortStatus(1, scope.row.id)"
                  id="poc_del"
                  >启用</el-button
                >
                <el-button
                  v-if="user.role == 1 && activeName == 'second' && scope.row.pass_status == 0"
                  type="text"
                  size="small"
                  @click="passOrRefuce(1, scope.row.id)"
                  id="poc_del"
                  >通过</el-button
                >
                <el-button
                  v-if="user.role == 1 && activeName == 'second' && scope.row.pass_status == 0"
                  type="text"
                  size="small"
                  @click="passOrRefuce(2, scope.row.id)"
                  id="poc_del"
                  >驳回</el-button
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 规则库以及内置规则 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleAdd"
      width="650px"
      @close="closeDialog('dialogFormVisibleFenzu')"
    >
      <template slot="title">
        {{ !addIsTrue ? '修改规则' : '添加规则' }}
      </template>
      <div class="dialog-body">
        <el-form
          v-loading="diaLoading"
          :model="ruleForm"
          style="padding: 0 !important"
          :rules="rulesGroupFirst"
          ref="ruleFormGroup"
          label-width="85px"
          class="demo-ruleForm"
        >
          <el-form-item label="风险类型" prop="event_type">
            <!-- <el-input v-model="ruleForm.event_type" placeholder="请选择风险类型"></el-input> -->
            <el-select
              :disabled="Boolean(ruleForm.id)"
              v-model="ruleForm.event_type"
              allow-create
              filterable
            >
              <el-option
                v-for="(item, index) in eventTypeList"
                :key="index"
                :label="item"
                :value="item"
                >{{ item }}(已有)</el-option
              >
            </el-select>
          </el-form-item>
          <el-form-item label="事件名称" prop="name">
            <el-input
              :disabled="Boolean(ruleForm.id)"
              v-model="ruleForm.name"
              placeholder="请输入事件名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="风险等级" prop="tag">
            <el-select v-model="ruleForm.tag">
              <el-option
                v-for="item in tagList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="风险指纹" prop="content">
            <el-input
              type="textarea"
              :rows="3"
              v-model="ruleForm.content"
              placeholder="响应的代码片段"
            ></el-input>
          </el-form-item>
          <el-form-item label="风险介绍" prop="event_desc">
            <el-input
              type="textarea"
              :rows="3"
              v-model="ruleForm.event_desc"
              placeholder="对风险规则的简单解释"
            ></el-input>
          </el-form-item>
          <el-form-item label="事件危害" prop="event_impact">
            <el-input
              type="textarea"
              :rows="3"
              v-model="ruleForm.event_impact"
              placeholder="对该风险带来的危害进行简述"
            ></el-input>
          </el-form-item>
          <el-form-item label="解决方案" prop="event_solution">
            <el-input
              type="textarea"
              :rows="3"
              v-model="ruleForm.event_solution"
              placeholder="采取什么措施可以避免此类风险带来危害"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="closeDialog('dialogFormVisibleAdd')"
          id="poc_add_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="insertSave" id="poc_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 自定义规则 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleFenzu"
      width="650px"
    >
      <template slot="title">
        {{ !addIsTrue ? '修改规则' : '添加规则' }}
      </template>
      <div class="dialog-body">
        <div v-if="ruleForm.refuse_reason" class="reasonClass">
          <span>驳回原因</span>
          <p>{{ ruleForm.refuse_reason }}</p>
        </div>
        <el-form
          v-loading="diaLoading"
          :model="ruleForm"
          style="padding: 0 !important"
          :rules="rulesGroup"
          ref="ruleFormGroup"
          label-width="85px"
          class="demo-ruleForm"
        >
          <el-form-item label="事件名称" prop="name">
            <el-input
              v-model="ruleForm.name"
              placeholder="针对风险规则的简称，例如：内部业务应用暴露"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="user.role !== 1" label="处理方式" prop="deal_way">
            <el-input
              type="textarea"
              :rows="2"
              v-model="ruleForm.deal_way"
              placeholder="对该规则需要识别的指纹内容进行简述，例如：针对内部业务应用暴露，认为企业网盘、ERP、人力资源管理系统等为内部应用，不应该暴露在互联网"
            ></el-input>
          </el-form-item>
          <el-form-item label="事件描述" prop="event_desc">
            <el-input
              type="textarea"
              :rows="2"
              v-model="ruleForm.event_desc"
              placeholder="对风险规则的简单解释"
            ></el-input>
          </el-form-item>
          <el-form-item label="事件危害" prop="event_impact">
            <el-input
              type="textarea"
              :rows="2"
              v-model="ruleForm.event_impact"
              placeholder="对该风险带来的危害进行简述"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="user.role !== 1" label="解决方案" prop="event_recommendation">
            <el-input
              type="textarea"
              :rows="2"
              v-model="ruleForm.event_recommendation"
              placeholder="采取什么措施可以避免此类风险带来危害"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="user.role == 1" label="解决方案" prop="event_solution">
            <el-input
              type="textarea"
              :rows="2"
              v-model="ruleForm.event_solution"
              placeholder="采取什么措施可以避免此类风险带来危害"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="user.role == 1" label="规则内容" prop="content">
            <el-input
              type="textarea"
              :rows="2"
              v-model="ruleForm.content"
              placeholder="规则实现的代码片段"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="user.role == 1" label="规则优先级" prop="priority">
            <el-input
              type="number"
              v-model.number="ruleForm.priority"
              placeholder="规则生效的优先级，支持填写1-100"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="user.role == 1" label="规则标签" prop="tag">
            <el-select v-model="ruleForm.tag">
              <el-option label="资产扫描" :value="1"></el-option>
              <el-option label="资产推荐" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="closeDialog('dialogFormVisibleFenzu')"
          id="poc_add_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="insertSave" id="poc_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleRefuse"
      width="650px"
    >
      <template slot="title"> 驳回原因 </template>
      <div class="dialog-body">
        <el-form
          v-loading="diaLoading"
          :model="refuseForm"
          style="padding: 0 !important"
          :rules="rulesRefuse"
          ref="refuseForm"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="驳回原因" prop="refuse_reason">
            <el-input
              type="textarea"
              :rows="10"
              v-model="refuseForm.refuse_reason"
              placeholder="请输入驳回原因"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleRefuse = false" id="poc_add_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="refuseSave" id="poc_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="状态：" prop="status">
            <el-select
              v-model="formInline.status"
              placeholder="请选择状态"
              clearable
              @change="selectChange($event, 'status', statusArr, true, false)"
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="更新时间：" prop="updated_at">
            <el-date-picker
              v-model="formInline.updated_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="poc_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="poc_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
    <el-dialog
      class="elDialogAdd detail"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInfo"
      width="850px"
    >
      <template slot="title"> 风险详情 </template>
      <div class="dialog-body">
        <el-form
          ref="ruleForm"
          label-width="70px"
          class="demo-ruleForm"
          style="padding: 0 !important"
        >
          <el-form-item label="事件名称">
            <p>{{ ruleForm.name }}</p>
          </el-form-item>
          <el-form-item label="风险类型">
            <p>{{ ruleForm.event_type }}</p>
          </el-form-item>
          <el-form-item label="风险等级" prop="port">
            <span :class="ruleForm.tag ? riskLevelArrClassMap[ruleForm.tag] : ''">{{
              ruleForm.tag ? tagListMap[ruleForm.tag] : '-'
            }}</span>
          </el-form-item>
          <el-form-item label="风险介绍">
            <p>{{ ruleForm.event_desc }}</p>
          </el-form-item>
          <el-form-item
            label="风险指纹"
            v-if="user && ((user.role == 2 && currentCompany == -1) || user.role == 1)"
          >
            <p>{{ ruleForm.content }}</p>
          </el-form-item>
          <el-form-item label="事件危害">
            <p v-html="ruleForm.event_impact"></p>
          </el-form-item>
          <el-form-item label="解决方案">
            <p v-html="ruleForm.event_solution"></p>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleInfo = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import hightFilter from '../../components/assets/highTab.vue'
import {
  categoryList,
  customRuleList,
  passCustomRule,
  addCustomRule,
  delCustomRule,
  eventRuleListV1,
  addEventRuleList,
  editEventRuleList,
  delAdminRuleV1,
  updateEventRuleStatus
} from '@/api/apiConfig/api.js'

export default {
  components: {
    hightFilter
  },

  data() {
    return {
      dialogFormVisibleInfo: false,
      riskLevelArrClassMap: {
        4: 'yellowRadiusBorder',
        3: 'originRadiusBorder',
        2: 'redRadiusBorder',
        1: 'deepRedRadiusBorder'
      },
      eventTypeList: [],
      dialogFormVisibleAdd: false,
      tagListMap: {
        4: '低危',
        3: '中危',
        2: '高危',
        1: '严重'
      },
      tagList: [
        { label: '低危', value: 4 },
        { label: '中危', value: 3 },
        { label: '高危', value: 2 },
        { label: '严重', value: 1 }
      ],
      highTabShow: [
        {
          label: '状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      editDictName: '',
      currentDictId: '',
      currentDictName: '',
      currentDictType: '',
      isAddDict: false, // 是否是新建字典
      addDictForm: {
        name: ''
      },
      dictList: [],
      highlist: null,
      btnLoading: false,
      checkedAll: false, // 是否全选
      checkedArr: [],
      passStatusList: [
        {
          id: 0,
          name: '待审核'
        },
        {
          id: 1,
          name: '审核通过'
        },
        {
          id: 2,
          name: '审核驳回'
        }
      ],
      formInline: {
        keyword: '',
        pass_status: '',
        status: '',
        deal_way: '',
        event_desc: '',
        event_impact: '',
        event_recommendation: '',
        updated_at: []
        // page: 1,
        // per_page: 10
      },
      refuseForm: {
        id: '',
        refuse_reason: '',
        set_status: ''
      },
      ruleForm: {
        name: '',
        deal_way: '',
        event_desc: '',
        event_impact: '',
        event_recommendation: ''
      },
      addIsTrue: true,
      loading: false,
      diaLoading: false,
      rulesRefuse: {
        refuse_reason: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }]
      },
      rulesGroupFirst: {
        event_type: [{ required: true, message: '请输入风险类型', trigger: 'blur' }],
        name: [{ required: true, message: '请输入事件名称', trigger: 'blur' }],
        tag: [{ required: true, message: '请选择风险等级', trigger: 'blur' }],
        content: [{ required: true, message: '请输入风险指纹', trigger: 'blur' }],
        event_desc: [{ required: true, message: '请输入风险介绍', trigger: 'blur' }],
        event_impact: [{ required: true, message: '请选择事件危害', trigger: 'blur' }],
        event_solution: [{ required: true, message: '请选择解决方案', trigger: 'blur' }]
      },
      rulesGroup: {
        event_desc: [{ required: true, message: '请输入事件描述', trigger: 'blur' }],
        name: [{ required: true, message: '请输入事件名称', trigger: 'blur' }],
        deal_way: [{ required: true, message: '请输入处理方式', trigger: 'blur' }],
        content: [{ required: true, message: '请输入规则内容', trigger: 'blur' }],
        priority: [
          { pattern: /^([0-9][0-9]{0,1}|100)$/, message: '要求填写1-100的数', trigger: 'blur' }
        ],
        tag: [{ required: true, message: '请选择规则标签', trigger: 'blur' }]
      },
      rklData: '',
      rklEditId: '',
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      rules: {
        port: [{ required: true, message: '请输入活动名称', trigger: 'change' }],
        pro: [{ required: true, message: '请输入活动名称', trigger: 'change' }]
      },
      pocTypeArr: [],
      pocGroupsNoPageArr: [],
      vulTypeList: [],
      pocList: [],
      pocListCopy: [],
      statusArr: [
        {
          name: '启用',
          id: '1'
        },
        {
          name: '禁用',
          id: '0'
        }
      ],
      statusArrV1: [
        {
          name: '启用',
          id: 1
        },
        {
          name: '禁用',
          id: 2
        }
      ],
      dialogFormVisibleRefuse: false,
      dialogFormVisibleRkl: false,
      highCheckdialog: false,
      dialogFormVisibleFenzu: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      activeName: 'first',
      tableData: [],
      currentPage: 1,
      tableHeader: [
        {
          label: '事件名称',
          name: 'name',
          minWidth: 110,
          fixed: 'left',
          path: ['user_set', 'manager_rules']
        },
        {
          label: '风险类型',
          name: 'event_type',
          minWidth: 110,
          path: ['user_set', 'manager_rules']
        },
        {
          label: '风险等级',
          name: 'tag',
          minWidth: 110,
          path: ['user_set', 'manager_rules']
        },
        {
          label: '风险介绍',
          name: 'event_desc',
          minWidth: 110,
          path: ['user_set', 'manager_rules']
        },
        {
          label: '风险指纹',
          name: 'content',
          minWidth: 120,
          path: ['user_set', 'manager_rules']
        },
        // {
        //   label: '事件危害',
        //   name: 'event_impact',
        //   minWidth: 110,
        //   path: ['user_set', 'manager_rules']
        // },
        // {
        //   label: '解决方案',
        //   name: 'event_solution',
        //   minWidth: 110,
        //   path: ['user_set', 'manager_rules']
        // },
        {
          label: '状态',
          name: 'status',
          minWidth: 110,
          path: ['user_set', 'manager_rules']
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: 110,
          path: ['user_set', 'manager_rules']
        },
        {
          label: '事件名称',
          name: 'name',
          fixed: 'left',
          minWidth: 110,
          path: ['user_auto', 'manager_sh']
        },
        {
          label: '事件描述',
          name: 'event_desc',
          minWidth: 150,
          path: ['user_auto', 'manager_sh']
        },
        {
          label: '事件危害',
          name: 'event_impact',
          minWidth: 180,
          path: ['user_auto', 'manager_sh']
        },
        {
          label: '解决方案',
          name: 'event_recommendation',
          path: ['user_auto'],
          minWidth: 180
        },

        {
          label: '处理方式',
          name: 'deal_way',
          path: ['user_auto', 'manager_sh'],
          minWidth: 100
        },
        {
          label: '添加时间',
          name: 'created_at',
          path: ['user_auto', 'manager_sh'],
          minWidth: 120
        },

        {
          label: '添加用户',
          name: 'user',
          path: ['manager_sh']
        },
        {
          label: '状态', // 启用禁用
          name: 'status',
          path: [],
          minWidth: 80
        },
        {
          label: '处理状态',
          name: 'pass_status',
          path: ['user_auto', 'manager_sh'],
          minWidth: 100
        }
      ],
      user: {
        role: ''
      },
      isdis: true //是否禁用编辑弱口令
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user && this.user.role == 2) {
        if (this.activeName == 'first') {
          this.getEventRuleData()
          this.getCategoryList()
        } else {
          this.getCustomRuleListData()
        }
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany']),
    tableHeaderIsShow(path) {
      // 'user_set', 内置规则 'user_auto',自定义规则 'manager_rules',规则库 'manager_sh'规则审核
      let arr = []
      if (this.user && this.user.role == 1) {
        // 超管
        if (this.activeName == 'first') {
          arr = this.tableHeader.filter((item) => {
            return !item.path || item.path.indexOf('manager_rules') != -1
          })
        } else {
          arr = this.tableHeader.filter((item) => {
            return !item.path || item.path.indexOf('manager_sh') != -1
          })
        }
      } else {
        if (this.activeName == 'first') {
          arr = this.tableHeader.filter((item) => {
            return !item.path || item.path.indexOf('user_set') != -1
          })
        } else {
          arr = this.tableHeader.filter((item) => {
            return !item.path || item.path.indexOf('user_auto') != -1
          })
        }
      }
      if (
        this.user &&
        ((this.user.role == 2 && this.currentCompany !== -1) || this.user.role == 3)
      ) {
        arr = arr.filter((item) => {
          return item.name !== 'content'
        })
      }
      if (arr && arr.length > 0) {
        arr[0].fixed = 'left'
      }
      return arr
    }
  },
  async created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (sessionStorage.getItem('activeTabName')) {
      this.activeName = sessionStorage.getItem('activeTabName')
    }
    if (this.user.role == 2 && !this.currentCompany) return
    if (this.activeName == 'first') {
      this.getEventRuleData()
      this.getCategoryList()
    } else {
      this.getCustomRuleListData()
    }
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    openDetailDialog(row) {
      this.dialogFormVisibleInfo = true
      this.ruleForm = JSON.parse(JSON.stringify(row))
    },
    async getCategoryList() {
      let res = await categoryList({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.eventTypeList = res.data.event_type
      }
    },
    getPassStatus(val) {
      // '审核状态 0/待审核 1/审核通过 2/审核驳回'
      let str = ''
      switch (val) {
        case 0:
          str = '待审核'
          break
        case 1:
          str = '审核通过'
          break
        case 2:
          str = '审核驳回'
          break
        default:
      }
      return str
    },
    getPassClass(val) {
      let classN = ''
      switch (val) {
        case 0:
          classN = 'grayLine'
          break
        case 1:
          classN = 'greenLine'
          break
        case 2:
          classN = 'redLine'
          break
        default:
      }
      return classN
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      if (this.user.role != 1 && this.activeName == 'second') {
        bol = 'tableWrapTips'
      }
      return bol
    },
    handleChangeLeft(value, direction) {
      this.$nextTick(() => {
        this.$refs.reserve.addToRight() //直接执行到右事件
      })
    },
    handleChangeRight(value, direction) {
      this.$nextTick(() => {
        this.$refs.reserve.addToLeft() //直接执行到右事件
      })
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选查询
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      if (this.activeName == 'first') {
        this.getEventRuleData()
      } else {
        this.getCustomRuleListData()
      }
    },
    pass_status_Change(val) {
      this.formInline.status = this.formInline.pass_status
      this.currentPage = 1
      this.getCustomRuleListData()
    },
    // 选中项中文名称-用于高级筛选标签
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkGroupFuncList() {
      this.currentPage = 1
      this.getCustomRuleListData()
    },
    async getEventRuleData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.tableData = []
      this.highCheckdialog = false
      this.formInline.operate_company_id = this.currentCompany
      this.loading = true
      let res
      if (this.activeName == 'first') {
        res = await eventRuleListV1({
          ...this.formInline,
          is_order: 1,
          page: this.currentPage,
          per_page: this.pageSize
        }).catch(() => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
      }

      this.loading = false
      this.tableData = res.data.items
      this.total = res.data.total
      // 全选操作
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      }
    },
    async getCustomRuleListData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.tableData = []
      this.formInline.operate_company_id = this.currentCompany
      this.loading = true
      customRuleList({ ...this.formInline, page: this.currentPage, per_page: this.pageSize })
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items || []
          this.total = res.data.total
        })
        .catch(() => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    async updatePortStatus(val, id) {
      let ids = []
      if (id) {
        ids = [id]
      } else {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
        ids = this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            })
      }

      let res = await updateEventRuleStatus({
        ids,
        set_status: val,
        operate_company_id: this.currentCompany,
        ...this.formInline,
        type: 1
      })
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.$nextTick(() => {
          this.$refs.eltable.clearSelection()
        })
        this.getEventRuleData()
      }
    },
    async editOne(row) {
      this.addIsTrue = false

      this.ruleForm = {
        refuse_reason: row.refuse_reason,
        id: row.id,
        name: row.name,
        deal_way: row.deal_way,
        event_desc: row.event_desc,
        event_impact: row.event_impact,
        event_recommendation: row.event_recommendation,
        event_solution: row.event_solution,
        content: row.content,
        priority: row.priority,
        tag: row.tag,
        event_type: row.event_type
      }
      if (this.activeName == 'first') {
        this.getCategoryList()
        this.dialogFormVisibleAdd = true
      } else {
        this.dialogFormVisibleFenzu = true
      }
    },
    async refuseSave() {
      this.$refs['refuseForm'].validate(async (valid) => {
        if (valid) {
          this.btnLoading = true
          let res = await passCustomRule(this.refuseForm).catch(() => {
            this.btnLoading = false
          })
          if (res.code == 0) {
            this.dialogFormVisibleRefuse = false
            this.btnLoading = false
            this.$message.success('操作成功！')
            this.getCustomRuleListData()
          }
        } else {
          return false
        }
      })
    },
    // 通过，驳回
    async passOrRefuce(set_status, id) {
      if (set_status == 1) {
        // 通过
        this.btnLoading = true
        let res = await passCustomRule({ id: [id], set_status }).catch(() => {
          this.btnLoading = false
        })
        if (res.code == 0) {
          this.btnLoading = false
          this.$message.success('操作成功！')
          this.getCustomRuleListData()
        }
      } else {
        this.refuseForm = {
          refuse_reason: '',
          set_status: set_status,
          id: [id]
        }
        this.dialogFormVisibleRefuse = true
      }
    },
    async removeOne(icon, val) {
      let obj
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
        obj = {
          id: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      } else {
        obj = {
          id: [val],
          operate_company_id: this.currentCompany
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'poc_del_cancel',
        confirmButtonClass: 'poc_del_sure',
        customClass: 'poc_del',
        type: 'warning'
      })
        .then(async () => {
          if (this.activeName == 'first') {
            // 第一个tab页面的删除
            let ids = obj.id
            let keyword = this.formInline.keyword
            delAdminRuleV1({ ...obj, ids, keyword }).then((res) => {
              if (res.code == 0) {
                this.$message.success('删除成功！')
                this.currentPage = this.updateCurrenPage(
                  this.total,
                  [1],
                  this.currentPage,
                  this.pageSize
                ) // 更新页码
                this.getEventRuleData()
              }
            })
          } else {
            // 第二个tab页面的删除
            let res = await delCustomRule(obj)
            if (res.code == 0) {
              this.$message.success('删除成功！')
              this.currentPage = this.updateCurrenPage(
                this.total,
                [1],
                this.currentPage,
                this.pageSize
              ) // 更新页码
              this.getCustomRuleListData()
            }
          }
          ruleForm
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.poc_del>.el-message-box__btns')
        del.children[0].id = 'poc_del_cancel'
        del.children[1].id = 'poc_del_sure'
      }, 50)
    },
    handleClick() {
      this.highlist = null
      sessionStorage.setItem('activeTabName', this.activeName)
      this.$nextTick(() => {
        this.$refs.eltable.clearSelection()
      })
      this.currentPage = 1
      this.checkedAll = false
      this.formInline.status = ''
      this.formInline.pass_status = ''
      this.formInline.keyword = ''
      if (this.activeName == 'first') {
        this.getEventRuleData()
      } else {
        this.getCustomRuleListData()
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      if (this.activeName == 'first') {
        this.getEventRuleData(true)
      } else {
        this.getCustomRuleListData(true)
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      if (this.activeName == 'first') {
        this.getEventRuleData(true)
      } else {
        this.getCustomRuleListData(true)
      }
    },
    closeDialog(dialogType) {
      this[dialogType] = false
      this.$refs.ruleFormGroup.resetFields()
    },
    async insertShow() {
      this.addIsTrue = true
      this.ruleForm = {
        name: '',
        deal_way: '',
        event_desc: '',
        event_impact: '',
        event_recommendation: ''
      }
      if (this.activeName == 'first') {
        this.getCategoryList()
        this.dialogFormVisibleAdd = true
      } else {
        this.dialogFormVisibleFenzu = true
      }
    },
    // closeDialog
    async insertSave() {
      this.ruleForm.operate_company_id = this.currentCompany
      // this.ruleForm.priority = this.ruleForm.priority
      this.$refs['ruleFormGroup'].validate(async (valid) => {
        if (valid) {
          let res = null
          this.btnLoading = true
          if (this.activeName == 'first' && this.addIsTrue) {
            this.ruleForm.status = 1
            res = await addEventRuleList(this.ruleForm).catch(() => {
              this.btnLoading = false
            })
          } else if (this.activeName == 'first' && !this.addIsTrue) {
            this.ruleForm.status = 1
            res = await editEventRuleList(this.ruleForm).catch(() => {
              this.btnLoading = false
            })
          } else {
            res = await addCustomRule(this.ruleForm).catch(() => {
              this.btnLoading = false
            }) // 自定义规则新增 自定义规则修改
          }
          this.btnLoading = false
          if (res.code == 0) {
            if (this.activeName == 'first') {
              this.getCategoryList()
              this.getEventRuleData()
            } else {
              this.getCustomRuleListData()
            }
            this.$message.success('操作成功！')
            this.dialogFormVisibleFenzu = false
            this.dialogFormVisibleAdd = false
          }
        } else {
          return false
        }
      })
    },
    resetForm() {
      this.formInline = {
        rank: '',
        port: ''
      }
    },
    newAdd() {
      this.isAddDict = true
      this.dictList.forEach((item) => {
        item.isEditDict = false
      })
      this.addDictForm.name = ''
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    height: 100%;
    .reasonClass {
      max-height: 88px;
      padding: 12px 16px;
      margin-bottom: 24px;
      overflow: auto;
      background: #eff2f7;
      border-radius: 2px;
      border: 1px solid #c9cfdb;
      span {
        display: inline-block;
        line-height: 20px;
        color: #62666c;
      }
      p {
        color: #37393c;
        line-height: 20px;
      }
    }
    .el-form {
      /deep/ .el-form-item__content {
        p,
        span {
          color: #606266 !important;
          font-size: 14px !important;
        }
        p span {
          color: #606266 !important;
          font-size: 14px !important;
        }
      }
    }
    ::v-deep .left {
      flex: 1;
      width: 46%;
      padding-right: 1%;
      & > p {
        text-align: right;
        .el-icon-plus {
          margin-bottom: 10px;
        }
      }
      .addClass {
        margin-bottom: 10px;
        .el-input-group__append {
          padding: 0 24px !important;
        }
        .el-input-group__append .el-button {
          padding: 0 10px !important;
        }
      }
      .editClass {
        .el-input-group__append {
          padding: 0 10px !important;
        }
        .el-input-group__append .el-button {
          padding: 0 10px !important;
        }
      }
      .handlebtnWrap {
        width: 16%;
        display: flex;
        justify-content: space-between;
      }
      .iconClass {
        color: #2677ff;
        font-size: 14px;
        cursor: pointer;
      }
      ul {
        height: 75%;
        overflow: auto;
        li {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 10px;
          cursor: pointer;
          border-left: 4px solid transparent;
          .el-checkbox {
            margin-right: 10px;
          }
        }
        .dictname {
          display: inline-block;
          width: 60%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .activeClass {
          color: #2677ff;
          border-left: 4px solid #2677ff;
          background: #eff2f7;
        }
      }
    }
    .right {
      width: 52%;
      padding-left: 1%;
      border-left: 1px solid #e9ebef;
      .rklClass {
        position: relative;
        /deep/.el-textarea {
          background: #ffffff;
          border-radius: 0;
          border: 1px solid #d1d5dd;
          border-bottom: 0;
          .el-textarea__inner {
            height: 100%;
            border: 0 !important;
          }
          .el-textarea__inner:hover {
            border: 0;
          }
        }
        .rkl_footer {
          // position: absolute;
          // top: 0;
          // left: 0;
          text-align: right;
          padding: 5px;
          background: #fff;
          border: 1px solid #d1d5dd;
          .highBtn {
            width: 50px;
            height: 22px;
          }
        }
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      /* width: 60px; */
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      /* width: 60px; */
      height: 44px;
      text-align: center;
      line-height: 44px;
      /* padding: 0; */
      padding-left: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .downloadClass {
      position: relative;
      height: 40px;
      line-height: 40px;
      margin: 16px 20px 0 20px;
      background: #f0f3f8;
      color: #62666c;
      border-radius: 4px;
      // border: 1px solid rgba(38, 119, 255, 0.44);
      cursor: pointer;
      i {
        font-size: 14px;
        color: #2677ff;
        margin: 0 8px 0 16px;
      }
      .el-icon-close {
        position: absolute;
        right: 10px;
        top: 12px;
        font-size: 16px;
        color: #9fa6af;
        &:hover,
        &:focus {
          background-color: transparent;
          color: rgba(159, 166, 175, 1) !important;
        }
      }
      span {
        color: #2677ff;
      }
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 224px) !important;
    }
    .tableWrap {
      height: calc(100% - 172px);
      padding: 0px 20px;
      .pocName {
        cursor: pointer;
      }
    }
    .tableWrapTips {
      height: calc(100% - 230px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
    .mystatus {
      display: inline-block;
      padding: 1px 14px;
      border-radius: 14px;
    }
    .yzstatus {
      background: rgba(255, 70, 70, 0.08);
      border: 1px solid #ff4646;
      color: #ff4646 !important;
    }
    .gwstatus {
      background: rgba(255, 121, 0, 0.08);
      border: 1px solid #ff7900;
      color: #ff7900 !important;
    }
    .zwstatus {
      background: rgba(248, 193, 54, 0.08);
      border: 1px solid #f8c136;
      color: #f8c136 !important;
    }
    .dwstatus {
      background: rgba(38, 119, 255, 0.08);
      border: 1px solid #2677ff;
      color: #2677ff !important;
    }
  }
}
.ellipsis-10 {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 15;
  -webkit-box-orient: vertical;
}
.detail {
  /deep/ .el-form-item__label {
    font-weight: 600;
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
