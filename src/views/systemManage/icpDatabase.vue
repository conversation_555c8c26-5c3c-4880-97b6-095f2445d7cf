<template>
  <div class="container">
    <div class="headerTitle">ICP备案总库</div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="网站" name="domain_0"> </el-tab-pane>
        <el-tab-pane label="APP" name="app_1"> </el-tab-pane>
        <el-tab-pane label="小程序" name="app_2"> </el-tab-pane>
        <el-tab-pane label="快应用" name="app_3"> </el-tab-pane>
      </el-tabs>
      <div class="content">
        <div class="filterTab">
          <div>
            <el-input
              v-model="formInline.keyword"
              placeholder="请输入主办单位、备案号进行检索"
              @keyup.enter.native="checkFuncList"
              id="user_keycheck"
            >
              <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            </el-input>
            <span @click="highCheckDialog = true" id="user_filter" style="width: 80px"
              ><img
                src="../../assets/images/filter.png"
                alt=""
                style="width: 16px; vertical-align: middle; margin-right: 3px"
              />高级筛选</span
            >
          </div>
          <div>
            <el-button
              class="normalBtnRe"
              style="margin-left: 10px"
              type="primary"
              id="user_export"
              @click="forceUpdate('more')"
              >强制刷新</el-button
            >
            <el-button class="normalBtn" type="primary" id="user_add" @click="addNewIcp"
              >新建备案</el-button
            >
          </div>
        </div>
        <hightFilter
          id="hightFilter"
          :highTabShow="highTabShow"
          :highlist="highlist"
          :total="total"
          pageIcon="user"
          @highcheck="highCheck"
        ></hightFilter>
        <div :class="hightFilterIsShow()">
          <el-table
            border
            :data="tableData"
            v-loading="loading"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            @selection-change="handleSelectionChange"
            @cell-mouse-enter="showTooltip"
            @cell-mouse-leave="hiddenTooltip"
            ref="eltable"
            height="100%"
            style="width: 100%"
          >
            <el-table-column type="selection" align="center" :reserve-selection="true" width="55">
            </el-table-column>
            <el-table-column
              v-for="item in tableHeader"
              :key="item.id"
              :prop="item.name"
              align="left"
              :min-width="item.minWidth"
              :fixed="item.fixed"
              :label="item.label"
            >
              <template slot-scope="{ row }">
                <span v-if="item.name == 'company_type' && row.company_type">{{
                  typeArrMap[row.company_type]
                }}</span>
                <span v-else-if="item.name == 'source' && row.source">{{
                  sourceArrMap[row.source]
                }}</span>
                <span v-else-if="item.name == 'status' && row.status">{{
                  statusArrMap[row.status]
                }}</span>
                <span v-else>{{ row[item.name] ? row[item.name] : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="left" width="200">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="editList(scope.row)">编辑</el-button>
                <el-button type="text" size="small" @click="forceUpdate('one', index, scope.row)"
                  >强制刷新</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckDialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="单位性质：" prop="name">
            <el-select
              v-model="formInline.company_type"
              placeholder="请选择单位性质"
              multiple
              collapse-tags
              @change="selectChange($event, 'company_type', typeArr, true, true)"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in typeArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备案状态：" prop="name">
            <el-select
              v-model="formInline.status"
              placeholder="请选择备案状态"
              @change="selectChange($event, 'status', statusArr, true, false)"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in statusArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据来源：" prop="name">
            <el-select
              v-model="formInline.source"
              placeholder="请选择数据来源"
              multiple
              collapse-tags
              @change="selectChange($event, 'source', sourceArr, true, true)"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in sourceArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="审核日期：" prop="record_time">
            <el-date-picker
              v-model="formInline.record_time"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="user_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="user_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleAdd"
      width="660px"
      v-if="dialogFormVisibleAdd"
    >
      <template slot="title">
        {{ addForm.id != '' ? '编辑备案' : '新建备案' }}
      </template>
      <div class="dialog-body">
        <el-form
          :model="addForm"
          :rules="addRules"
          ref="ruleForm"
          label-width="120px"
          class="demo-ruleForm add-dialog"
        >
          <el-form-item :label="tabType == 'domain' ? '域名：' : '名称：'" prop="name">
            <el-input v-model="addForm.name" placeholder="请输入域名"></el-input>
          </el-form-item>
          <el-form-item label="主办单位：" prop="name">
            <el-input v-model="addForm.company_name" placeholder="请输入主办单位"></el-input>
          </el-form-item>
          <el-form-item label="单位性质：" prop="type">
            <!-- <el-input v-model="addForm.type" placeholder="请输入单位性质"></el-input> -->
            <el-select v-model="addForm.company_type" placeholder="请选择单位性质">
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in typeArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备案号：" prop="icp">
            <el-input v-model="addForm.icp" placeholder="请输入备案号"></el-input>
          </el-form-item>
          <el-form-item label="备案状态：" prop="status">
            <el-select v-model="addForm.status" placeholder="请选择备案状态">
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in statusArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="审核日期：" prop="record_time">
            <el-date-picker
              v-model="addForm.record_time"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="date"
              placeholder="选择日期"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
            <!-- <el-input v-model="addForm.record_time" placeholder="请输入审核日期"></el-input> -->
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleAdd = false" id="user_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="addSureLoading"
          id="user_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  icpDomainList,
  icpAppList,
  icpDomainAdd,
  icpAppAdd,
  icpDomainUpdate,
  icpAppUpdate
} from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      addSureLoading: false,
      pickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() < new Date().getTime() - 86400000;
        // },
      },
      addRules: {
        icp: [{ required: true, message: '请填写', trigger: 'blur' }]
      },
      addForm: {},
      highTabShow: [
        {
          label: '单位性质',
          name: 'company_type',
          type: 'select'
        },
        {
          label: '备案状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '数据来源',
          name: 'source',
          type: 'select'
        },
        {
          label: '审核日期',
          name: 'record_time',
          type: 'select'
        }
      ],
      highlist: [],
      formInline: {},
      highCheckDialog: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      loading: false,
      activeName: 'domain_0',
      tableHeader: [],
      tableHeader1: [
        {
          label: '域名',
          name: 'name',
          minWidth: 120
        },
        {
          label: '主办单位',
          name: 'company_name',
          minWidth: 120
        },
        {
          label: '单位性质',
          name: 'company_type',
          minWidth: 120
        },
        {
          label: '备案号',
          name: 'icp',
          minWidth: 120
        },
        {
          label: '备案状态',
          name: 'status',
          minWidth: 120
        },
        {
          label: '数据来源',
          name: 'source',
          minWidth: 120
        },
        {
          label: '审核日期',
          name: 'record_time',
          minWidth: 120
        }
      ],
      tableHeader2: [
        {
          label: '名称',
          name: 'name',
          minWidth: 120
        },
        {
          label: '主办单位',
          name: 'company_name',
          minWidth: 120
        },
        {
          label: '单位性质',
          name: 'company_type',
          minWidth: 120
        },
        {
          label: '备案号',
          name: 'icp',
          minWidth: 120
        },
        {
          label: '备案状态',
          name: 'status',
          minWidth: 120
        },
        {
          label: '数据来源',
          name: 'source',
          minWidth: 120
        },
        {
          label: '审核日期',
          name: 'record_time',
          minWidth: 120
        }
      ],
      statusArr: [
        {
          name: '正常',
          value: 1
        },
        {
          name: '注销',
          value: 2
        }
      ],
      statusArrMap: {
        1: '正常',
        2: '注销'
      },
      typeArr: [
        {
          name: '企业',
          value: 1
        },
        {
          name: '个人',
          value: 2
        },
        {
          name: '政府机关',
          value: 3
        },
        {
          name: '事业单位',
          value: 4
        },
        {
          name: '社会团体',
          value: 5
        },
        {
          name: '国防机构',
          value: 6
        },
        {
          name: '民办非企业单位',
          value: 7
        },
        {
          name: '基金会',
          value: 8
        },
        {
          name: '律师执业机构',
          value: 9
        },
        {
          name: '外国在华文化中心',
          value: 10
        },
        {
          name: '群众性团体组织',
          value: 11
        },
        {
          name: '司法鉴定机构',
          value: 12
        },
        {
          name: '宗教团体',
          value: 13
        },
        {
          name: '境外机构',
          value: 14
        },
        {
          name: '医疗机构',
          value: 15
        },
        {
          name: '公证机构',
          value: 16
        }
      ],
      typeArrMap: {
        1: '企业',
        2: '个人',
        3: '政府机关',
        4: '事业单位',
        5: '社会团体',
        6: '国防机构',
        7: '民办非企业单位',
        8: '基金会',
        9: '律师执业机构',
        10: '外国在华文化中心',
        11: '群众性团体组织',
        12: '司法鉴定机构',
        13: '宗教团体',
        14: '境外机构',
        15: '医疗机构',
        16: '公证机构'
      },
      sourceArr: [
        {
          name: '工信部',
          value: 'miit'
        },
        {
          name: '导入',
          value: 'import'
        },
        {
          name: '查子域',
          value: 'chinaz_api'
        }
      ],
      sourceArrMap: {
        miit: '工信部',
        import: '导入',
        chinaz_api: '查子域'
      },
      checkedArr: [],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      tabTypeVal: '0',
      tabType: 'domain',
      dialogFormVisibleAdd: false
    }
  },
  mounted() {
    this.tableHeader = this.tableHeader1
    this.getData()
  },
  watch: {
    activeName(val) {
      this.tabType = val.split('_')[0]
      this.tabTypeVal = val.split('_')[1]
      this.formInline = {}
      this.highlist = []
      this.currentPage = 1
      this.pageSize = 10
      this.getData()
      if (this.tabType != 'domain') {
        this.tableHeader = this.tableHeader2
      } else {
        this.tableHeader = this.tableHeader1
      }
    }
  },
  methods: {
    async forceUpdate(type, index, row) {
      let params = {}
      if (type == 'more') {
        params.ids = this.checkedArr.map((item) => item.id)
        if (params.ids.length == 0) {
          this.$message.error('请选择操作数据')
          return
        }
      } else if (type == 'one') {
        params = JSON.parse(JSON.stringify(row))
        params.ids = [params.id]
      }
      params.force = 1
      let funcName = null
      if (this.tabType == 'domain') {
        funcName = icpDomainUpdate
      } else {
        params.type = this.tabTypeVal
        funcName = icpAppUpdate
      }
      let res = await funcName(params)
      if (res.code == 0) {
        this.$message.success('刷新成功')
        this.getData()
      }
    },
    editList(row) {
      this.addFormCopy = {}
      this.addForm = JSON.parse(JSON.stringify(row))
      this.dialogFormVisibleAdd = true
    },
    addNewIcp() {
      this.addForm = {}
      this.dialogFormVisibleAdd = true
    },
    insertSave(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (!valid) {
          return false
        }
        let params = JSON.parse(JSON.stringify(this.addForm))
        let funcName = null

        if (params.id) {
          if (this.tabType == 'domain') {
            funcName = icpDomainUpdate
          } else {
            params.type = this.tabTypeVal
            funcName = icpAppUpdate
          }
        } else {
          if (this.tabType == 'domain') {
            funcName = icpDomainAdd
          } else {
            params.type = this.tabTypeVal
            funcName = icpAppAdd
          }
          // params.record_time = params.record_time +" 00:00:00"
        }
        if (!this.addForm.record_time) {
          // params.record_time = params.record_time +" 00:00:00"
        }
        params.ids = [params.id]
        let res = await funcName(params)
        if (res.code == 0) {
          this.currentPage = 1
          this.dialogFormVisibleAdd = false
          if (params.id) {
            this.$message.success('编辑成功')
          } else {
            this.$message.success('新建成功')
          }
          this.getData()
        }
      })
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    resetForm(ref) {
      this.formInline = {
        type: [],
        status: [],
        source: [],
        record_time: []
      }
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkFuncList() {
      this.highCheckDialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    async getData() {
      this.loading = true
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        parent_id: -1,
        ...this.formInline
      }
      let funcName = null
      if (this.tabType == 'domain') {
        funcName = icpDomainList
      } else {
        obj.type = this.tabTypeVal
        funcName = icpAppList
      }
      let res = await funcName(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.tableData = res.data.items || []
        this.total = res.data.total || 0
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    handleClick() {
      this.tableData = []
      this.checkedArr = []
      if (this.$refs.eltable) {
        this.$refs.eltable.clearSelection()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      // min-width: 60px;
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      padding: 0 16px;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .tab_content {
      height: calc(100% - 44px);
    }
    .tab_content_tip {
      height: calc(100% - 101px);
    }
    .content {
      // height: 100%;
      height: calc(100% - 44px);
      .filterTab {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        & > div {
          display: flex;
          align-items: center;
          .normalBtnRe {
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .el-input {
            width: 240px;
          }
          & > span {
            font-weight: 400;
            color: #2677ff;
            line-height: 20px;
            margin-left: 20px;
            cursor: pointer;
          }
        }
      }
    }

    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}
</style>
