<template>
  <div class="container">
    <div class="headerTitle">网口管理</div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <!-- <el-input v-model="formInline.keyword" placeholder="请输入关键字检索" @keyup.enter.native="checkFuncList" id="keyword_keycheck">
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            </el-input> -->
          <!-- <span @click="highCheckdialog = true" id="keyword_filter" style="width:80px"><img src="../../assets/images/filter.png" alt="" style="width:16px;vertical-align: middle;margin-right:3px">高级筛选</span> -->
        </div>
        <div>
          <el-button class="normalBtn" type="primary" @click="addEdit('add')" id="keyword_add"
            >新建网口</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highlist="highlist"
        :total="total"
        pageIcon="keyword"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            min-width="120"
            :label="item.label"
          >
            <template slot-scope="scope">
              <el-tooltip
                v-if="scope.row[item.name]"
                class="item"
                effect="dark"
                placement="top"
                :open-delay="500"
              >
                <span slot="content">{{ scope.row[item.name] }}</span>
                <span>{{ scope.row[item.name] }}</span>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="150">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="addEdit('edit', scope.row)"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <!-- <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total">
        </el-pagination> -->
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="500px"
    >
      <template slot="title">
        {{ editId ? '编辑' : '新建' }}
      </template>
      <div class="dialog-body">
        <el-form
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item v-if="editId" label="网卡名称" prop="name">
            <el-input disabled v-model="ruleForm.name" placeholder=""></el-input>
          </el-form-item>
          <el-form-item label="IP地址" prop="ip">
            <el-input v-model="ruleForm.ip" placeholder="请输入IP地址"></el-input>
          </el-form-item>
          <el-form-item label="子网掩码" prop="netmask">
            <el-input v-model="ruleForm.netmask" placeholder="请输入子网掩码"></el-input>
          </el-form-item>
          <el-form-item label="网关IP" prop="gateway">
            <el-input v-model="ruleForm.gateway" placeholder="请输入网关IP"></el-input>
          </el-form-item>
          <el-form-item label="DNS" prop="dns">
            <el-input v-model="ruleForm.dns" placeholder="请输入DNS"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="comment">
            <el-input v-model="ruleForm.comment" placeholder="请输入DNS"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="otherLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="100px">
          <el-form-item label="IP地址" prop="ip">
            <el-input v-model="formInline.ip" placeholder="请输入IP地址"></el-input>
          </el-form-item>
          <el-form-item label="子网掩码" prop="netmask">
            <el-input v-model="formInline.netmask" placeholder="请输入子网掩码"></el-input>
          </el-form-item>
          <el-form-item label="网关IP" prop="gateway">
            <el-input v-model="formInline.gateway" placeholder="请输入网关IP"></el-input>
          </el-form-item>
          <el-form-item label="DNS" prop="dns">
            <el-input v-model="formInline.dns" placeholder="请输入DNS"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="comment">
            <el-input v-model="formInline.comment" placeholder="请输入备注"></el-input>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import blackAndwhiteList from './blackAndwhiteList.vue'
import { networkList, editNetworkList, addNetworkList } from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip,
    hightFilter,
    blackAndwhiteList
  },
  data() {
    return {
      activeName: 'first',
      updateType: 0,
      updateTypeDialog: false,
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      formInline: {
        page: 1,
        per_page: 10,
        keyword: '',
        ip: '',
        netmask: '',
        gateway: '',
        dns: '',
        comment: '',
        operate_company_id: ''
      },
      checkedAll: false,
      highCheckdialog: false,
      tableHeader: [
        {
          label: '网卡名称',
          name: 'name'
        },
        {
          label: 'IP地址',
          name: 'ip'
        },
        {
          label: '子网掩码',
          name: 'netmask'
        },
        {
          label: '网关IP',
          name: 'gateway'
        },
        {
          label: 'DNS',
          name: 'dns'
        },
        {
          label: '备注',
          name: 'comment'
        }
      ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      user: {
        role: ''
      },
      dialogFormVisibleInsert: false,
      editId: '',
      ruleForm: {
        ip: '',
        netmask: '',
        gateway: '',
        dns: '',
        comment: ''
      },
      rules: {
        ip: [
          { required: true, message: '请输入IP地址', trigger: 'blur' },
          {
            required: true,
            message: '请输入正确的IP地址,格式127.0.0.1',
            pattern:
              /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
            trigger: 'blur'
          }
        ],
        netmask: [{ required: true, message: '请输入子网掩码', trigger: 'blur' }],
        gateway: [{ required: true, message: '请输入网关IP', trigger: 'blur' }],
        dns: [{ required: true, message: '请输入DNS', trigger: 'blur' }]
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      user: {
        role: ''
      }
    }
  },
  created() {
    this.activeName = 'first'
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (sessionStorage.getItem('userMessage')) {
      this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (this.userInfo) {
        this.user = this.userInfo.user
      }
    }
    if (this.user.role != 2) {
      this.getData()
    } else if (this.currentCompany) {
      this.getData()
    }
  },
  watch: {
    getterCurrentCompany(val) {
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    getData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at_range = this.formInline.created_at_range
        ? this.formInline.created_at_range
        : []
      let obj = {
        page: this.formInline.page,
        per_page: this.formInline.per_page,
        keyword: this.formInline.keyword,
        type: this.formInline.type,
        status: this.formInline.status,
        created_at_range: this.formInline.created_at_range,
        operate_company_id: this.currentCompany
      }
      networkList(obj)
        .then((res) => {
          this.loading = false
          this.tableData = res.data
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch((error) => {
          this.tableData = []
          this.loading = false
        })
    },
    async insertSave(formName) {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          let obj = { ...this.ruleForm }
          this.otherLoading = true
          let res = null
          if (this.editId) {
            res = await editNetworkList(obj).catch(() => {
              this.otherLoading = false
            })
          } else {
            res = await addNetworkList(obj).catch(() => {
              this.otherLoading = false
            })
          }
          if (res.code == 0) {
            this.otherLoading = false
            this.dialogFormVisibleInsert = false
            this.$message.success('操作成功！')
            this.getData()
          }
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    addEdit(icon, row) {
      if (icon == 'add') {
        let nameArr = this.tableData.map((item) => {
          return item.name
        })
        // 列表的网卡名称有eth0不可新建
        if (nameArr.indexOf('eth0') != -1) {
          this.$message.error('eth0网卡名称已存在不可新建！')
          return
        }
      }
      this.dialogFormVisibleInsert = true
      if (icon == 'edit') {
        this.editId = row.id
        this.ruleForm = {
          id: row.id,
          ip: row.ip,
          name: row.name,
          netmask: row.netmask,
          gateway: row.gateway,
          dns: row.dns,
          comment: row.comment
        }
      } else {
        this.ruleForm = {
          ip: '',
          netmask: '',
          gateway: '',
          dns: '',
          comment: ''
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    & > .tabsWrap {
      .el-tabs__nav {
        padding-left: 20px;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        // min-width: 60px;
        padding: 0 16px;
        height: 53px;
        text-align: center;
        line-height: 53px;
        // font-weight: bold;
        color: #267ee5;
        background: #fff;
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        // padding: 0;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
      }
    }
    // .el-tabs__nav {
    //     padding-left: 20px;
    // }
    // .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
    //     width: 60px;
    //     height: 44px;
    //     text-align: center;
    //     line-height: 44px;
    //     font-weight: bold;
    //     color: #2677FF;
    //     background: rgba(38, 119, 255, 0.08);
    // }
    // .el-tabs__active-bar {
    //     left: 4px;
    //     width: 100%;
    //     background: #2677FF;
    // }
    // .el-tabs__header {
    //     margin: 0;
    // }
    // .el-tabs__nav-wrap::after {
    //     height: 1px;
    //     background-color: #E9EBEF;
    // }
    // .el-tabs__item {
    //     width: 60px;
    //     height: 44px;
    //     text-align: center;
    //     line-height: 44px ;
    //     padding: 0;
    //     font-size: 14px;
    //     font-weight: 400;
    //     color: #62666C;
    // }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tab_content {
      height: calc(100% - 44px);
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}
</style>
