<template>
  <div class="container">
    <div class="headerTitle">IP段管理</div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.ip"
            placeholder="请输入IP段进行搜索"
            @keyup.enter.native="Advanced"
            id="ip_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="Advanced"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="ip_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox class="checkboxAll" v-model="checkedAll" @change="checkAllChange" id="ip_all"
            >选择全部</el-checkbox
          >
          <el-button class="normalBtnRe" type="primary" @click="remove()" id="ip_more_del"
            >删除</el-button
          >
          <el-button class="normalBtn" type="primary" @click="addEdit()" id="ip_all"
            >新建IP段</el-button
          >
        </div>
      </div>
      <div class="tableWrap">
        <el-table
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            min-width="120"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'type'">{{
                ipArr.find((v) => v.value == scope.row[item.name]).name
              }}</span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="left" width="150">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="remove(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="34%"
      v-if="dialogFormVisibleInsert"
    >
      <template slot="title"> 新建IP段 </template>
      <el-form
        :model="ruleForm"
        :rules="rules"
        style="padding: 0 !important"
        ref="ruleForm"
        label-width="80px"
        class="demo-ruleForm"
      >
        <!-- <el-form-item label="IP类型" prop="type">
                  <el-select v-model="ruleForm.type" placeholder="请选择IP类型">
                    <el-option :label="item.name" :value="item.value" v-for="item in ipArr" :key="item.value"></el-option>
                  </el-select>
                </el-form-item> -->
        <el-form-item label="IP段" prop="ip">
          <el-input
            type="textarea"
            v-model="ruleForm.ip"
            placeholder="IP段支持IPV4/IPV6格式如下：
***********-***********或者CIDR格式的**********/24
单个ip为CIDR格式的，单个提交最多1000个ip，格式如下:
***********/32或者ad80:0000:0000:0000:abaa:0000:00c2:0002/128
多个ip段/ip填写请换行
如果填写的ip段或者ip地址有重叠，那么需要重新填写"
          ></el-input>
        </el-form-item>
        <el-form-item label="企业名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入企业名称"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleInsert = false" id="ip_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="otherLoading"
          id="ip_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="企业名称：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入企业名称"></el-input>
          </el-form-item>
          <el-form-item label="添加时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="ip_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="Advanced" id="ip_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { ipList, addIp, delIp } from '@/api/apiConfig/api.js'

const defaultRuleForm = {
  // type: 1,
  ip: '',
  name: ''
}
export default {
  components: {
    tableTooltip
  },
  name: 'ipManage',
  data() {
    return {
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      formInline: {
        ip: '',
        name: '',
        created_at_range: ''
      },
      checkedAll: false,
      highCheckdialog: false,
      tableHeader: [
        {
          label: 'IP段',
          name: 'ip'
        },
        // {
        //     label: 'IP类型',
        //     name: 'type'
        // },
        {
          label: '企业名称',
          name: 'name'
        },
        {
          label: '添加时间',
          name: 'created_at'
        }
      ],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      dialogFormVisibleInsert: false,
      ruleForm: Object.assign({}, defaultRuleForm),
      rules: {
        ip: [{ required: true, message: '请输入IP段', trigger: 'blur' }],
        name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }]
      },
      loading: false,
      otherLoading: false,
      ipArr: [
        {
          name: 'IPv4',
          value: 1
        },
        {
          name: 'IPv6',
          value: 2
        }
      ],
      user: {
        role: ''
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    Advanced() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.getData()
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    getData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.loading = true
      let params = {
        page: this.currentPage,
        per_page: this.pageSize,
        operate_company_id: this.currentCompany,
        ...this.formInline
      }
      ipList(params)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async remove(id) {
      if (this.checkedAll && this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'ip_del_cancel',
        confirmButtonClass: 'ip_del_sure',
        customClass: 'ip_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            ids: this.checkedAll
              ? []
              : id
                ? [id]
                : this.checkedArr.map((item) => {
                    return item.id
                  }),
            operate_company_id: this.currentCompany,
            ...this.formInline
          }
          let res = await delIp(obj)
          if (res.code == 0) {
            this.$message.success('删除成功！')
            if (this.checkedAll) {
              this.checkedAll = false
              this.$refs.eltable.clearSelection()
              this.currentPage = this.updateCurrenPage(
                this.total,
                this.checkedArr,
                this.currentPage,
                this.pageSize
              ) // 更新页码
            } else {
              this.currentPage = this.updateCurrenPage(
                this.total,
                [1],
                this.currentPage,
                this.pageSize
              ) // 更新页码
            }
            this.getData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.ip_del>.el-message-box__btns')
        del.children[0].id = 'ip_del_cancel'
        del.children[1].id = 'ip_del_sure'
      }, 50)
    },
    insertSave(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (!valid) {
          return false
        }
        let params = Object.assign({}, this.ruleForm)
        params.operate_company_id = this.currentCompany || ''
        let ipList = params.ip.split(/,|，|\s+/).filter((item) => {
          return item
        })
        params.ip = ipList
        let res = await addIp(params)
        this.dialogFormVisibleInsert = false
        if (res.code == 0) {
          this.getData()
          this.$message.success('操作成功！')
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        ip: '',
        name: '',
        created_at_range: ''
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    addEdit(row) {
      this.dialogFormVisibleInsert = true
      this.ruleForm = Object.assign({}, row ? row : defaultRuleForm)
    }
  }
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left {
      height: 100%;
      display: flex;
      /deep/.el-textarea {
        width: 300px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 20px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
          margin-right: 12px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 4px;
          cursor: pointer;
        }
      }
    }
    .tableWrap {
      height: calc(100% - 129px);
    }
    .el-table {
      border: 0;
    }
  }
  /deep/.el-form {
    .el-textarea textarea {
      min-height: 270px !important;
    }
  }
}
</style>
