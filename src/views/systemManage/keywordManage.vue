<template>
  <div class="container">
    <div class="headerTitle">关键词管理</div>
    <div class="home_header">
      <div class="tabsWrap" v-if="user.role == 1">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="关键词管理" name="first"> </el-tab-pane>
          <el-tab-pane label="黑白名单" name="second"> </el-tab-pane>
        </el-tabs>
      </div>
      <div v-if="activeName == 'first'" class="tab_content">
        <div class="filterTab">
          <div>
            <el-input
              v-model="formInline.keyword"
              placeholder="请输入关键词检索"
              @keyup.enter.native="checkFuncList"
              id="keyword_keycheck"
            >
              <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
            </el-input>
            <span
              v-if="userIsOpen"
              @click="highCheckdialog = true"
              id="keyword_filter"
              style="width: 80px"
              ><img
                src="../../assets/images/filter.png"
                alt=""
                style="width: 16px; vertical-align: middle; margin-right: 3px"
              />高级筛选</span
            >
          </div>
          <div>
            <el-checkbox
              class="checkboxAll"
              v-model="checkedAll"
              @change="checkAllChange"
              id="keyword_all"
              >选择全部</el-checkbox
            >
            <el-button
              :disabled="!userIsOpen"
              class="normalBtnRe"
              type="primary"
              @click="remove('more')"
              id="keyword_more_del"
              >删除</el-button
            >
            <el-button
              :disabled="!userIsOpen"
              class="normalBtnRe"
              type="primary"
              @click="updateStatusSave('more', 0)"
              id="keyword_more_disable"
              >禁用</el-button
            >
            <el-button
              :disabled="!userIsOpen"
              class="normalBtn"
              type="primary"
              @click="updateStatusSave('more', 1)"
              id="keyword_more_open"
              >启用</el-button
            >
            <el-button
              :disabled="!userIsOpen"
              class="normalBtn"
              type="primary"
              @click="addEdit()"
              id="keyword_add"
              >新建关键词</el-button
            >
            <!-- <el-button
              :disabled="!userIsOpen"
              class="normalBtn"
              type="primary"
              @click="asyncDataClick('more')"
              id="keyword_sync"
              v-if="user.role == 2"
              >一键同步数据</el-button
            > -->
          </div>
        </div>
        <!-- 高级筛选条件 -->
        <hightFilter
          id="hightFilter"
          :highTabShow="highTabShow"
          :highlist="highlist"
          :total="total"
          @highcheck="highCheck"
        ></hightFilter>
        <div :class="hightFilterIsShow()">
          <el-table
            border
            :data="tableData"
            v-loading="loading"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            @selection-change="handleSelectionChange"
            @cell-mouse-enter="showTooltip"
            @cell-mouse-leave="hiddenTooltip"
            ref="eltable"
            height="100%"
            style="width: 100%"
          >
            <template slot="empty">
              <div class="emptyClass">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-kong"></use>
                </svg>
                <p>暂无数据</p>
              </div>
            </template>
            <el-table-column
              type="selection"
              align="center"
              :reserve-selection="true"
              :selectable="handleSelectable"
              width="55"
            >
            </el-table-column>
            <el-table-column
              v-for="item in tableHeader"
              :key="item.id"
              :prop="item.name"
              align="left"
              min-width="120"
              :label="item.label"
            >
              <template slot-scope="scope">
                <span class="greenLine" v-if="item.name == 'status' && scope.row[item.name] == 1"
                  >启用</span
                >
                <span class="redLine" v-else-if="item.name == 'status' && scope.row[item.name] == 0"
                  >禁用</span
                >
                <span v-else-if="item.name == 'type'">{{ getType(scope.row[item.name]) }}</span>
                <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" align="left" label="操作" width="230">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  v-if="scope.row['status'] == 1"
                  @click="updateStatusSave('one', 0, scope.row.id)"
                  id="keyword_disable"
                  >禁用</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  v-if="scope.row['status'] == 0"
                  @click="updateStatusSave('one', 1, scope.row.id)"
                  id="keyword_open"
                  >启用</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  @click="remove('one', scope.row.id)"
                  id="keyword_del"
                  >删除</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  @click="updateTypeClick(scope.row)"
                  id="keyword_del"
                  >更改类型</el-button
                >
                <!-- 关键词类型是数字资产/全部并且是超管/安服账号有此操作：同步此关键词的公众号和app数据  从七麦同步过来数据 插件已经不使用 按钮注释20250218记-->
                <!-- <el-button
                  type="text"
                  size="small"
                  v-if="
                    (user.role == 1 || user.role == 2) &&
                    (scope.row.type == 0 || scope.row.type == 1)
                  "
                  @click="asyncDataClick('one', scope.row)"
                  >同步数据</el-button
                > -->
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="支持立即同步最新数据泄漏"
                  placement="top"
                >
                  <el-button
                    type="text"
                    size="small"
                    :loading="updateKeywordCrawlerLoading && clickId == scope.row.id"
                    v-if="
                      scope.row.type == 0 ||
                      scope.row.type == 2 ||
                      (scope.row.type == 1 && user.role == 2)
                    "
                    :disabled="scope.row.status == 0"
                    @click="handleUpdateAssets(scope.row.id)"
                    id="keyword_del"
                    >强制更新</el-button
                  >
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <blackAndwhiteList v-if="activeName == 'second'"></blackAndwhiteList>
    </div>
    <el-dialog
      :visible.sync="updateTypeDialog"
      :close-on-click-modal="false"
      class="updateType"
      width="500px"
    >
      <template slot="title"> 更改类型 </template>
      <el-select style="width: 100%" v-model="updateType" placeholder="请选择">
        <el-option label="全部" :value="0"></el-option>
        <el-option label="数字资产" :value="1"></el-option>
        <el-option label="数据泄露" :value="2"></el-option>
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="updateTypeDialog = false" id="keyword_update_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="updateTypeFun"
          :loading="otherLoading"
          id="keyword_update_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="500px"
      v-if="dialogFormVisibleInsert"
    >
      <template slot="title"> 新建关键词 </template>
      <div class="dialog-body">
        <el-form
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="关键词类型" prop="type">
            <el-select v-model="ruleForm.type" placeholder="请选择">
              <el-option label="全部" :value="0"></el-option>
              <el-option label="数字资产" :value="1"></el-option>
              <el-option label="数据泄露" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="添加方式" prop="way">
            <el-select v-model="ruleForm.way" @change="wayChange" placeholder="请选择方式">
              <el-option label="手动输入" :value="0"></el-option>
              <el-option label="从文件导入" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="ruleForm.way == 0" label="关键词" prop="name">
            <el-input
              type="textarea"
              :rows="10"
              v-model="ruleForm.name"
              placeholder="请输入关键词，多个值请用分号或换行分隔"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="ruleForm.way == 1" label="关键词" prop="name">
            <p class="downloadClass" @click="downloadcluesExcel">
              <i class="el-icon-warning"></i>请点击下载
              <span>关键词模板</span>
            </p>
            <el-upload
              class="upload-demo"
              drag
              :action="uploadAction"
              :headers="uploadHeaders"
              :before-upload="beforeUpload"
              :on-success="uploadSucc"
              :on-remove="uploadRemove"
              :on-error="uploadErr"
              :limit="uploadMaxCount"
              :on-exceed="handleExceed"
              accept=".xlsx"
              :file-list="fileList"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">支持上传xlsx 文件，且大小不超过20M</div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="otherLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="100px">
          <el-form-item label="状态：" prop="status">
            <el-select
              filterable
              v-model="formInline.status"
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'status', statusArr, true, false)"
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关键词类型：" prop="type">
            <el-select
              v-model="formInline.type"
              placeholder="请选择"
              @change="selectChange($event, 'type', typeArr, true, false)"
            >
              <el-option label="全部" :value="0"></el-option>
              <el-option label="数字资产" :value="1"></el-option>
              <el-option label="数据泄露" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="添加时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import blackAndwhiteList from './blackAndwhiteList.vue'
import {
  monitorKeywordIndex,
  updateKeywordStatus,
  addSensitiveKeyword,
  delSensitiveKeyword,
  updateTypeSensitiveKeyword,
  keywordCopy,
  updateKeywordCrawler
} from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip,
    hightFilter,
    blackAndwhiteList
  },
  data() {
    return {
      clickId: '',
      updateKeywordCrawlerLoading: false,
      activeName: 'first',
      updateType: 0,
      updateTypeDialog: false,
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      formInline: {
        page: 1,
        per_page: 10,
        keyword: '',
        type: '',
        status: '',
        created_at_range: [],
        operate_company_id: ''
      },
      checkedAll: false,
      highCheckdialog: false,
      highTabShow: [
        {
          label: '状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '关键词类型',
          name: 'type',
          type: 'select'
        },
        {
          label: '添加时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      tableHeader: [
        {
          label: '关键词',
          name: 'name'
        },
        {
          label: '关键词类型',
          name: 'type'
        },
        {
          label: '状态',
          name: 'status'
        },
        {
          label: '添加时间',
          name: 'created_at'
        }
      ],
      typeArr: [
        {
          name: '全部',
          id: 0
        },
        {
          name: '数字资产',
          id: 1
        },
        {
          name: '数据泄露',
          id: 2
        }
      ],
      statusArr: [
        // 状态 0/1 禁用/启用
        {
          name: '禁用',
          id: 0
        },
        {
          name: '启用',
          id: 1
        }
      ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      user: {
        role: ''
      },
      uploadAction: '',
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      dialogFormVisibleInsert: false,
      ruleForm: {
        way: 0,
        type: 0,
        name: ''
      },
      rules: {
        name: [{ required: true, message: ' ', trigger: 'blur' }],
        type: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      user: {
        role: ''
      },
      userIsOpen: true // 权限控制
    }
  },
  created() {
    this.activeName = 'first'
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (sessionStorage.getItem('companyInfo')) {
      let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
      // 数据泄露和数字资产都没权限，关键词管理也不可操作
      this.userIsOpen =
        companyInfo.limit_new_asset == 0 && companyInfo.limit_data_leak == 0 ? false : true
    } else {
      this.userIsOpen = true
    }
    if (sessionStorage.getItem('userMessage')) {
      this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (this.userInfo) {
        this.user = this.userInfo.user
      }
    }
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getData()
    } else {
      this.getData()
    }
  },
  watch: {
    getterCurrentCompany(val) {
      setTimeout(() => {
        // 权限控制
        if (sessionStorage.getItem('companyInfo')) {
          let companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
          // 数据泄露和数字资产都没权限，关键词管理也不可操作
          this.userIsOpen =
            companyInfo.limit_new_asset == 0 && companyInfo.limit_data_leak == 0 ? false : true
        } else {
          this.userIsOpen = true
        }
      }, 1000)
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    async handleUpdateAssets(id) {
      this.clickId = id
      this.updateKeywordCrawlerLoading = true
      // 对启用的关键词进行更新
      let res = await updateKeywordCrawler({ id, operate_company_id: this.currentCompany })
      if (res.code == 0) {
        this.updateKeywordCrawlerLoading = false
        this.$message.success('更新成功')
        this.clickId = ''
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    handleClick() {
      if (this.activeName == 'first') {
        this.headerTitle = '关键词管理'
      } else if (this.activeName == 'second') {
        this.headerTitle = '黑白名单'
      }
      sessionStorage.setItem('activeTabName', this.activeName)
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    getType(type) {
      let str = ''
      switch (type) {
        case 0:
          str = '全部'
          break
        case 1:
          str = '数字资产'
          break
        case 2:
          str = '数据泄露'
          break
        default:
      }
      return str
    },
    getStatus(status) {
      let str = ''
      switch (status) {
        case 0:
          str = '禁用'
          break
        case 1:
          str = '启用'
          break
        default:
      }
      return str
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    hightFilterIsShow() {
      let bol = ''
      if (this.user.role == 1) {
        if (
          document.getElementById('hightFilter') &&
          document.getElementById('hightFilter').offsetHeight > 0
        ) {
          bol = 'tableWrapManage tableWrapFilterManage'
        } else {
          bol = 'tableWrapManage'
        }
      } else {
        if (
          document.getElementById('hightFilter') &&
          document.getElementById('hightFilter').offsetHeight > 0
        ) {
          bol = 'tableWrap tableWrapFilter'
        } else {
          bol = 'tableWrap'
        }
      }

      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    uploadSucc(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        this.ruleForm.name = response.data
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadErr(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    uploadRemove(file, fileList) {},
    handleExceed() {
      this.$message.error(`最多上传${this.uploadMaxCount}个文件`)
    },
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过20MB!')
      }
      return isLt1M
    },
    getData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.created_at_range = this.formInline.created_at_range
        ? this.formInline.created_at_range
        : []
      let obj = {
        page: this.formInline.page,
        per_page: this.formInline.per_page,
        keyword: this.formInline.keyword,
        type: this.formInline.type,
        status: this.formInline.status,
        created_at_range: this.formInline.created_at_range,
        operate_company_id: this.currentCompany
      }
      monitorKeywordIndex(obj)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    // 禁用启用
    async updateStatusSave(icon, status, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      if (this.checkedAll) {
        this.formInline.id = icon == 'one' ? [id] : []
      } else {
        this.formInline.id =
          icon == 'one'
            ? [id]
            : this.checkedArr.map((item) => {
                return item.id
              })
      }
      let obj = Object.assign({}, this.formInline)
      obj.set_status = status
      let res = await updateKeywordStatus(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.$nextTick(() => {
          this.$refs.eltable.clearSelection()
        })
        this.getData()
      }
    },
    // 同步数据
    async asyncDataClick(icon, row) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      if (this.checkedAll) {
        this.formInline.id = icon == 'one' ? [row.id] : []
      } else {
        this.formInline.id =
          icon == 'one'
            ? [row.id]
            : this.checkedArr.map((item) => {
                return item.id
              })
      }
      let obj = Object.assign({}, this.formInline)
      let res = await keywordCopy({
        ...obj,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.$message.success(`${res.data}`)
        this.getData()
      }
    },
    // 更改类型
    updateTypeClick(row) {
      this.updateTypeDialog = true
      this.updateType = row.type
      this.formInline.id = row.id
    },
    async updateTypeFun(row) {
      let obj = {
        id: this.formInline.id,
        data: {
          type: this.updateType,
          operate_company_id: this.currentCompany
        }
      }
      let res = await updateTypeSensitiveKeyword(obj)
      this.updateTypeDialog = false
      this.$message.success('操作成功！')
      this.getData()
    },
    async remove(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'keyword_del_cancel',
        confirmButtonClass: 'keyword_del_sure',
        customClass: 'keyword_del',
        type: 'warning'
      })
        .then(async () => {
          this.formInline.page = this.currentPage
          this.formInline.per_page = this.pageSize
          if (this.checkedAll) {
            this.formInline.id = icon == 'one' ? [id] : []
          } else {
            this.formInline.id =
              icon == 'one'
                ? [id]
                : this.checkedArr.map((item) => {
                    return item.id
                  })
          }
          let obj = Object.assign({}, this.formInline)
          let res = await delSensitiveKeyword(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.checkedAll = false
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.$refs.eltable.clearSelection()
            this.getData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.keyword_del>.el-message-box__btns')
        del.children[0].id = 'keyword_del_cancel'
        del.children[1].id = 'keyword_del_sure'
      }, 50)
    },
    wayChange(val) {
      this.ruleForm.name = ''
    },
    downloadcluesExcel() {
      window.location.href = '/downloadTemplate/关键词管理模板.xlsx'
    },
    async insertSave(formName) {
      if (!this.ruleForm.name) {
        this.$message.error('关键词为必填')
        return
      }
      let obj = {
        type: this.ruleForm.type,
        name: this.ruleForm.name
          .split(/[；|;|\r\n]/)
          .filter((item) => {
            return item.trim()
          })
          .map((item) => {
            return item.trim()
          }),
        operate_company_id: this.currentCompany
      }
      this.otherLoading = true
      let res = await addSensitiveKeyword(obj).catch(() => {
        this.otherLoading = false
      })
      this.otherLoading = false
      this.dialogFormVisibleInsert = false
      if (res.code == 0) {
        if (res.data) {
          this.$message({
            duration: 5000,
            type: 'success',
            showClose: true,
            message: `${res.data.same_data > 0 ? `${res.data.same_data}个关键词已存在` : ''}${res.data.same_data > 0 && res.data.extra_data > 0 ? '；' : ''}${res.data.extra_data > 0 ? `成功更新${res.data.extra_data}个关键词类型` : ''}${(res.data.same_data > 0 || res.data.extra_data > 0) && res.data.count > 0 ? '；' : ''}${res.data.count > 0 ? `成功新增${res.data.count}个` : ''}`
          })
        }
        this.getData()
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        page: 1,
        per_page: 10,
        keyword: '',
        type: '',
        status: '',
        created_at_range: [],
        operate_company_id: ''
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    addEdit(row) {
      this.dialogFormVisibleInsert = true
      this.uploadAction = `${this.uploadSrcIp}/keyword/upload`
      this.ruleForm = {
        way: 0,
        type: 0,
        name: ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    & > .tabsWrap {
      .el-tabs__nav {
        padding-left: 20px;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        min-width: 60px;
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        // padding: 0;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
      }
    }
    // .el-tabs__nav {
    //     padding-left: 20px;
    // }
    // .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
    //     width: 60px;
    //     height: 44px;
    //     text-align: center;
    //     line-height: 44px;
    //     font-weight: bold;
    //     color: #2677FF;
    //     background: rgba(38, 119, 255, 0.08);
    // }
    // .el-tabs__active-bar {
    //     left: 4px;
    //     width: 100%;
    //     background: #2677FF;
    // }
    // .el-tabs__header {
    //     margin: 0;
    // }
    // .el-tabs__nav-wrap::after {
    //     height: 1px;
    //     background-color: #E9EBEF;
    // }
    // .el-tabs__item {
    //     width: 60px;
    //     height: 44px;
    //     text-align: center;
    //     line-height: 44px ;
    //     padding: 0;
    //     font-size: 14px;
    //     font-weight: 400;
    //     color: #62666C;
    // }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tab_content {
      height: calc(100% - 44px);
    }
    .tableWrapFilter {
      height: calc(100% - 135px) !important;
    }
    .tableWrap {
      height: calc(100% - 83px);
      padding: 0px 20px;
    }
    .tableWrapManage {
      height: calc(100% - 126px);
      padding: 0px 20px;
    }
    .tableWrapFilterManage {
      height: calc(100% - 180px);
    }
    .el-table {
      border: 0;
    }
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
