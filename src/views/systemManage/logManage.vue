<template>
  <div class="container" v-loading="loading">
    <div class="headerTitle">{{ activeName == 'first' ? '登录日志' : '操作日志' }}</div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="登录日志" name="first">
          <div class="filterTab">
            <div style="display: flex; align-items: center">
              <el-input
                v-model="formInline.keyword"
                @keyup.enter.native="checkFuncList"
                placeholder="请输入操作结果关键字进行搜索"
                id="audit_keycheck"
              >
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
              </el-input>
              <span
                @click="highCheckdialog = true"
                id="audit_filter"
                style="width: 80px; display: inline-block"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <div>
              <el-button class="normalBtnRe" type="primary" @click="exportList" id="audit_export"
                >导出</el-button
              >
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="操作日志" name="second">
          <div class="filterTab">
            <div style="display: flex; align-items: center">
              <el-input
                v-model="formInline.keyword"
                @keyup.enter.native="checkFuncList"
                placeholder="请输入操作内容关键字进行搜索"
                id="audit_keycheck"
              >
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
              </el-input>
              <span
                @click="highCheckdialog = true"
                id="audit_filter"
                style="width: 80px; display: inline-block"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <el-button class="normalBtnRe" type="primary" @click="exportList" id="audit_export"
              >导出</el-button
            >
          </div>
        </el-tab-pane>
      </el-tabs>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="log"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
        >
          <el-table-column
            v-for="(item, index) in tableHeader[activeName]"
            :key="index"
            :prop="item.name"
            align="left"
            :min-width="item.name == 'name' ? 250 : 120"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'role'">{{ transferUser(scope.row[item.name]) }}</span>
              <span v-else-if="item.name == 'model'">{{
                transferModel(scope.row[item.name])
              }}</span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="用户名：" prop="username">
            <el-input v-model="formInline.username" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="登录IP：" prop="ip">
            <el-input v-model="formInline.ip" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item v-if="activeName == 'second'" label="操作模块：" prop="model">
            <el-select
              filterable
              v-model="formInline.model"
              @change="selectChange($event, 'model', modelArr, true, false)"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in modelArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户类型：" prop="role">
            <el-select
              filterable
              v-model="formInline.role"
              @change="selectChange($event, 'role', userArr, true, false)"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in userArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="操作时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="audit_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="audit_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { logsIndex, exportLogs } from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip, hightFilter },

  data() {
    return {
      highTabShow: [
        {
          label: '用户名',
          name: 'username',
          type: 'input'
        },
        {
          label: '登录IP',
          name: 'ip',
          type: 'input'
        },
        {
          label: '操作模块',
          name: 'model',
          type: 'select'
        },
        {
          label: '用户类型',
          name: 'role',
          type: 'select'
        },
        {
          label: '操作时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      formInline: {
        type: '', // "日志类型 默认 0/登录日志 1/操作日志",
        model: '', // "操作模块 1/资产发现  2/资产认领 3/已知资产 4/漏洞管理 5/报告管理 6/系统管理",
        created_at_range: [],
        role: '', // "用户类型: 1/2/3 超级管理员/安服人员/企业租户",
        keyword: '', // "关键词",
        ip: '',
        username: ''
      },
      addIsTrue: true,
      loading: false,
      diaLoading: false,
      rulesGroup: {},
      rklData: '',
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      rules: {
        port: [{ required: true, message: '请输入活动名称', trigger: 'change' }],
        pro: [{ required: true, message: '请输入活动名称', trigger: 'change' }]
      },
      pocTypeArr: [],
      pocGroupsNoPageArr: [],
      vulTypeList: [],
      pocList: [],
      modelArr: [
        {
          name: '资产发现',
          id: 1
        },
        {
          name: '资产认领',
          id: 2
        },
        {
          name: '已知资产',
          id: 3
        },
        {
          name: '漏洞管理',
          id: 4
        },
        {
          name: '报告管理',
          id: 5
        },
        {
          name: '系统管理',
          id: 6
        }
      ],
      userArr: [
        {
          name: '超级管理员',
          id: 1
        },
        {
          name: '安服人员',
          id: 2
        },
        {
          name: '企业租户',
          id: 3
        },
        {
          name: '售后人员',
          id: 4
        }
      ],
      ruleForm: {
        port: '',
        pro: '',
        region: ''
      },
      highCheckdialog: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      activeName: 'first',
      tableData: [],
      currentPage: 1,
      tableHeader: {
        first: [
          {
            label: '用户姓名',
            name: 'username',
            fixed: 'left'
          },
          {
            label: '用户类型',
            name: 'role'
          },
          {
            label: '登录IP',
            name: 'ip'
          },
          {
            label: '操作结果',
            name: 'content'
          },
          {
            label: '操作时间',
            name: 'updated_at'
          }
        ],
        second: [
          {
            label: '用户姓名',
            name: 'username',
            fixed: 'left'
          },
          {
            label: '用户类型',
            name: 'role'
          },
          {
            label: '操作IP',
            name: 'ip'
          },
          {
            label: '操作模块',
            name: 'model'
          },
          {
            label: '操作内容',
            name: 'content'
          },
          {
            label: '操作时间',
            name: 'updated_at'
          }
        ]
      }
    }
  },
  async mounted() {
    if (sessionStorage.getItem('activeTabName')) {
      this.activeName = sessionStorage.getItem('activeTabName')
    }
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
    this.getlogsIndexData()
  },
  beforeDestroy() {
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getlogsIndexData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    transferUser(row) {
      let str = ''
      switch (row) {
        case 1:
          str = '超级管理员'
          break
        case 2:
          str = '安服人员'
          break
        case 3:
          str = '企业租户'
          break
        default:
      }
      return str
    },
    transferModel(row) {
      let str = ''
      switch (row) {
        case 1:
          str = '资产发现'
          break
        case 2:
          str = '资产认领'
          break
        case 3:
          str = '已知资产'
          break
        case 4:
          str = '漏洞管理'
          break
        case 5:
          str = '报告管理'
          break
        case 6:
          str = '系统管理'
          break
        case 7:
          str = '个人中心'
          break
        default:
      }
      return str
    },
    async getlogsIndexData() {
      this.highCheckdialog = false
      this.loading = true
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        type: this.activeName == 'first' ? 0 : 1, // "日志类型 默认 0/登录日志 1/操作日志",
        model: this.formInline.model, // "操作模块 1/资产发现  2/资产认领 3/已知资产 4/漏洞管理 5/报告管理 6/系统管理",
        created_at_range: this.formInline.created_at_range,
        role: this.formInline.role, // "用户类型: 1/2/3 超级管理员/安服人员/企业租户",
        keyword: this.formInline.keyword, // "关键词",
        ip: this.formInline.ip,
        username: this.formInline.username
      }
      logsIndex(obj)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
        })
        .catch(() => {
          this.loading = false
        })
    },
    async exportList() {
      let obj = {
        type: this.activeName == 'first' ? 0 : 1, // "日志类型 默认 0/登录日志 1/操作日志",
        model: this.formInline.model, // "操作模块 1/资产发现  2/资产认领 3/已知资产 4/漏洞管理 5/报告管理 6/系统管理",
        created_at_range: this.formInline.created_at_range,
        role: this.formInline.role, // "用户类型: 1/2/3 超级管理员/安服人员/企业租户",
        keyword: this.formInline.keyword, // "关键词",
        ip: this.formInline.ip,
        username: this.formInline.username
      }
      let res = await exportLogs(obj)
      if (res.code == 0) {
        this.download(this.showSrcIp + res.data.url)
      }
    },
    handleClick() {
      sessionStorage.setItem('activeTabName', this.activeName)
      this.currentPage = 1

      this.resetForm()
      this.getlogsIndexData()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getlogsIndexData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getlogsIndexData()
    },
    resetForm() {
      this.formInline = {
        type: '', // "日志类型 默认 0/登录日志 1/操作日志",
        model: '', // "操作模块 1/资产发现  2/资产认领 3/已知资产 4/漏洞管理 5/报告管理 6/系统管理",
        created_at_range: [],
        role: '', // "用户类型: 1/2/3 超级管理员/安服人员/企业租户",
        keyword: '', // "关键词",
        ip: '',
        username: ''
      }
      this.highlist = {}
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left {
      height: 100%;
      display: flex;
      /deep/.el-textarea {
        width: 300px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      padding: 0 16px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 223px) !important;
    }
    .tableWrap {
      height: calc(100% - 169px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
    .el-table .el-table__header th:first-child,
    .el-table .el-table__body td:first-child {
      padding-left: 10px;
    }
  }
}
/deep/.el-table th.el-table__cell:first-child:after {
  display: block;
}
</style>
