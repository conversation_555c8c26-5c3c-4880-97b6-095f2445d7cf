<template>
  <div class="container">
    <div class="headerTitle">
      <span>微服务请求记录</span>
    </div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
            id="user_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="user_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span
                v-if="
                  item.name == 'company_name' &&
                  scope.row.clientinfo &&
                  scope.row.clientinfo[item.name]
                "
              >
                {{ scope.row.clientinfo && scope.row.clientinfo[item.name] }}
              </span>
              <span v-else-if="item.name == 'client_id'">
                <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                  <template slot="content">
                    账号创建时间：{{ scope.row.clientinfo && scope.row.clientinfo.created_at }}
                  </template>
                  <span>{{ scope.row[item.name] || '-' }}</span>
                </el-tooltip>
              </span>
              <el-tooltip
                v-else
                class="item"
                effect="dark"
                placement="top"
                :open-delay="500"
                :content="String(scope.row[item.name])"
                :disabled="!scope.row[item.name]"
              >
                <span>{{ scope.row[item.name] || '-' }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="120px">
          <el-form-item label="客户端的id：" prop="client_id">
            <el-input v-model="formInline.client_id" placeholder="请输入客户端的id"></el-input>
          </el-form-item>
          <el-form-item label="请求的api路径：" prop="api_path">
            <el-input v-model="formInline.api_path" placeholder="请输入请求的api路径"></el-input>
          </el-form-item>
          <el-form-item label="请求方法：" prop="method">
            <el-input v-model="formInline.method" placeholder="请输入请求方法"></el-input>
          </el-form-item>
          <el-form-item label="请求的参数：" prop="param">
            <el-input v-model="formInline.param" placeholder="请输入请求的参数"></el-input>
          </el-form-item>
          <el-form-item label="创建时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="user_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="user_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import hightFilter from '../../components/assets/highTab.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { microServeList } from '@/api/apiConfig/api.js'

export default {
  components: {
    hightFilter
  },
  data() {
    return {
      highCheckdialog: false,
      total: 0,
      pageSize: 10,
      currentPage: 1,
      formInline: {
        keyword: ''
      },
      tableData: [],
      loading: false,
      tableHeader: [
        {
          label: '索引ID',
          name: 'id',
          fixed: 'left',
          minWidth: 50
        },
        {
          label: '客户端的id',
          name: 'client_id',
          minWidth: 60
        },
        {
          label: '请求的api路径',
          name: 'api_path',
          minWidth: 120
        },
        {
          label: '请求方法',
          name: 'method',
          minWidth: 50
        },
        {
          label: '请求的参数',
          name: 'param',
          minWidth: 120
        },
        {
          label: '企业名称',
          name: 'company_name',
          minWidth: 100
        },
        // {
        //   label: '账号创建时间',
        //   name: 'client_created_at',
        //   minWidth: 120
        // },
        {
          label: 'API请求时间',
          name: 'created_at',
          minWidth: 100
        }
      ],
      highlist: null,
      highTabShow: [
        {
          label: '索引ID',
          name: 'id',
          type: 'input'
        },
        {
          label: '客户端的id',
          name: 'client_id',
          type: 'input'
        },
        {
          label: '请求的api路径',
          name: 'api_path',
          type: 'input'
        },
        {
          label: '请求方法',
          name: 'method',
          type: 'input'
        },
        {
          label: '请求的参数',
          name: 'param',
          type: 'input'
        },
        {
          label: '创建时间',
          name: 'created_at',
          type: 'input'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          type: 'input'
        }
      ]
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  mounted() {
    this.getData()
  },
  methods: {
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    async getData() {
      this.loading = true
      let res = await microServeList({
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total || 0
      }
      this.loading = false
    },
    resetForm(ref) {
      this.$refs[ref].resetFields()
      this.formInline = {
        keyword: '',
        client_id: '',
        api_path: '',
        method: '',
        param: '',
        status: '',
        created_at_range: [],
        company: ''
      }
    }
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}
</style>
