<template>
  <div class="containerInfo">
    <div class="headerTitle">
      <span>公告管理</span>
    </div>
    <div class="contentWrap">
      <div class="leftTab">
        <p class="infoTitle">新增升级公告</p>
        <el-form ref="form" :model="form" label-width="80px">
          <el-form-item label="升级时间">
            <el-date-picker
              v-model="form.upTime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="升级内容">
            <el-input
              type="textarea"
              :rows="20"
              v-model="form.notice"
              placeholder="请输入升级内容"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="footer">
          <el-button class="normalBtn" type="primary" :loading="sureLoading" @click="onSubmit"
            >立即创建</el-button
          >
        </div>
      </div>
      <div class="rightWrap">
        <p class="infoTitle">公告记录</p>
        <div class="tableWrap">
          <el-table
            border
            :data="tableData"
            v-loading="loading"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            :row-class-name="tableRowClassName"
            @selection-change="handleSelectionChange"
            @cell-mouse-enter="showTooltip"
            @cell-mouse-leave="hiddenTooltip"
            ref="eltable"
            height="100%"
            style="width: 100%"
          >
            <el-table-column
              v-for="item in tableHeader"
              :key="item.id"
              :prop="item.name"
              align="left"
              min-width="120"
              :label="item.label"
            >
              <template slot-scope="scope">
                <p v-if="item.name == 'up_at_start'">
                  <span>{{ scope.row['up_at_start'] }}</span
                  >至<br />
                  <span>{{ scope.row['up_at_end'] }}</span>
                </p>
                <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" align="left" label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  :disabled="
                    new Date().getTime() >
                    new Date(scope.row.up_at_end.replace(/-/g, '/')).getTime()
                  "
                  type="text"
                  size="small"
                  @click="editClick(scope.row)"
                  id="keyword_del"
                  >编辑</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="500px"
    >
      <template slot="title"> 编辑 </template>
      <div class="dialog-body">
        <el-form
          :model="editform"
          :rules="rules"
          style="padding: 0 !important"
          ref="editform"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="升级时间" prop="upTime">
            <el-date-picker
              v-model="editform.upTime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="升级内容" prop="notice">
            <el-input
              type="textarea"
              :rows="10"
              v-model="editform.notice"
              placeholder="请填写升级内容"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('editform')"
          :loading="editLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { mapGetters, mapState } from 'vuex'
import { publicNoticeList, addPublicNotice, editPublicNotice } from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip },

  data() {
    return {
      tableData: [],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      sureLoading: false,
      editLoading: false,
      loading: false,
      dialogFormVisibleInsert: false,
      form: {
        upTime: [],
        notice: ''
      },
      editform: {
        upTime: [],
        notice: ''
      },
      rules: {
        upTime: [{ required: true, message: '请选择升级时间' }],
        notice: [{ required: true, message: '请填写升级内容' }]
      },
      tableHeader: [
        {
          label: '创建时间',
          name: 'created_at',
          minWidth: 80
        },
        {
          label: '升级时间',
          name: 'up_at_start',
          minWidth: 80
        },
        {
          label: '升级内容',
          name: 'notice',
          minWidth: 250
        }
      ],
      currentPage: 1,
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      user: null
    }
  },
  watch: {
    getterCurrentCompany() {
      this.currentPage = 1
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      if (this.user.role == 2) {
        if (!this.currentCompany) return
        this.getTaskResultData()
      } else {
        this.getTaskResultData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getTaskResultData()
    } else {
      this.getTaskResultData()
    }
  },

  methods: {
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    tableRowClassName({ row, rowIndex }) {
      if (new Date().getTime() > new Date(row.up_at_end.replace(/-/g, '/')).getTime()) {
        // 已过升级时间
        return 'passedRow'
      } else {
        return ''
      }
    },
    async onSubmit() {
      this.sureLoading = true
      let obj = {
        notice: this.form.notice,
        up_at_start: this.form.upTime[0],
        up_at_end: this.form.upTime[1]
      }
      let res = await addPublicNotice(obj).catch(() => {
        this.sureLoading = false
      })
      if (res.code == 0) {
        this.sureLoading = false
        this.$message.success('操作成功！')
        this.form = {
          upTime: [],
          notice: ''
        }
        this.getTaskResultData()
      }
    },
    getTagsName(tags) {
      let tagname = ''
      switch (tags) {
        case 0:
          tagname = '用户-扫描'
          break
        case 1:
          tagname = '安服-扫描'
          break
        case 2:
          tagname = '用户-推荐'
          break
        case 3:
          tagname = '安服-推荐'
          break
        case 4:
          tagname = '安服-导入'
          break
      }
      return tagname
    },
    // 编辑
    editClick(row) {
      this.dialogFormVisibleInsert = true
      this.editform = {
        id: row.id,
        notice: row.notice,
        upTime: [row.up_at_start, row.up_at_end]
      }
    },
    async insertSave() {
      this.editLoading = true
      let obj = {
        id: this.editform.id,
        notice: this.editform.notice,
        up_at_start: this.editform.upTime[0],
        up_at_end: this.editform.upTime[1]
      }
      let res = await editPublicNotice(obj).catch(() => {
        this.editLoading = false
      })
      if (res.code == 0) {
        this.editLoading = false
        this.dialogFormVisibleInsert = false
        this.$message.success('操作成功！')
        this.editform = {
          upTime: [],
          notice: ''
        }
        this.getTaskResultData()
      }
    },
    async getTaskResultData() {
      let obj = {
        current_page: this.currentPage,
        per_page: this.pageSize
      }
      this.loading = true
      let res = await publicNoticeList(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.tableData = res.data.items ? res.data.items : []
        this.total = res.data.total ? res.data.total : 0
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getTaskResultData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTaskResultData()
    },
    resetForm() {
      this.formInline = {
        rank: '',
        port: ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.containerInfo {
  position: relative;
  width: 100%;
  height: 100%;
  .contentWrap {
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    height: 100%;
    .leftTab {
      width: 40%;
      height: 100%;
      margin-right: 16px;
      background: #fff;
      .el-form {
        height: calc(100% - 111px);
        padding: 20px 16px !important;
        box-sizing: border-box;
        .el-date-editor .el-range-separator {
          line-height: 26px;
        }
      }
      .footer {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        background: #ffffff;
        box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
      }
    }
    .rightWrap {
      width: 60%;
      background: #fff;
      /deep/.tableWrap {
        height: 86%;
        padding: 20px 16px;
        box-sizing: border-box;
      }
    }
    /deep/.el-table {
      border: 0;
      .passedRow {
        background: rgba(233, 235, 239, 1);
      }
    }
  }
}
</style>
