<template>
  <div class="containerWrap">
    <div class="menuWrap">
      <div class="left">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-bianzu"></use>
        </svg>
        <span>互联网资产攻击面管理平台</span>
      </div>
      <img height="52" src="../../assets/images/notice_top.png" alt="" />
    </div>
    <div class="contentTop">
      <span><img src="../../assets/images/notice_lb.png" alt="" /> 重要公告</span>
      <img class="noticeBall" src="../../assets/images/notice_ball.png" alt="" />
    </div>
    <div class="content">
      <p style="margin-bottom: 12px">尊敬的用户您好!</p>
      <p class="indentClass"
        >为了给您带来更好的体验，FORadar互联网资产攻击面管理平台正在进行服务升级，期间系统将暂停服务，由此给您带来的不便敬请谅解。</p
      >
      <p class="indentClass"
        >如有疑问，可邮件至<span><EMAIL></span>咨询，感谢您的支持。</p
      >
      <p style="margin: 30px 0 12px 0"
        >升级内容({{ noticeContent.up_at_start }}至{{ noticeContent.up_at_end }})：</p
      >
      <div class="noticeContentClass">
        <!-- <p>升级时间：{{noticeContent.up_at_start}}-{{noticeContent.up_at_end}}</p> -->
        <p style="white-space: pre-line" v-html="noticeContent.notice"></p>
      </div>
    </div>
    <img class="noticeBot" src="../../assets/images/notice_bot.png" alt="" />
  </div>
</template>

<script>
import { latestPublicNotice } from '@/api/apiConfig/api.js'

export default {
  data() {
    return {
      noticeContent: ''
    }
  },
  async created() {
    this.getNotice()
  },
  methods: {
    async getNotice() {
      let res = await latestPublicNotice()
      this.noticeContent = res.data
    }
  }
}
</script>

<style lang="less" scoped>
.containerWrap {
  position: relative;
  width: 100%;
  height: 100%;
  background: url(../../assets/images/notice_bg.png) no-repeat;
  background-size: 100% 100%;
  .content {
    width: 748px;
    height: 452px;
    margin: -78px auto 0;
    padding: 28px 24px;
    box-sizing: border-box;
    opacity: 1;
    border-radius: 4px;
    background: linear-gradient(270deg, rgba(38, 119, 255, 0.1) 0%, rgba(38, 119, 255, 0.04) 100%);
    border: 1px solid rgba(175, 198, 237, 1);
    backdrop-filter: blur(2px);
    p {
      font-size: 18px;
      line-height: 36px;
    }
    .indentClass {
      text-indent: 24px;
      span {
        color: #2677ff;
      }
    }
    .noticeContentClass {
      max-height: 160px;
      overflow: auto;
      line-height: 36px;
    }
  }
  .contentTop {
    margin: 178px auto 0;
    width: 748px;
    display: flex;
    justify-content: space-between;
    font-size: 24px;
    font-weight: 500;
    color: #2677ff;
    line-height: 26px;
    .noticeBall {
      margin-top: -95px;
    }
  }
  .noticeBot {
    position: absolute;
    bottom: 0;
    right: 0;
  }
  ::v-deep .menuWrap {
    // position: absolute;
    width: 100%;
    height: 52px;
    line-height: 52px;
    font-size: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(270deg, #ffffff 0%, #f0f6ff 100%) !important;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08) !important;
    z-index: 99;
    img {
      cursor: pointer;
    }
    .left {
      span {
        font-weight: 800;
        color: #37393c;
      }
      svg {
        font-size: 28px;
        margin: 0 12px 0 11px;
        vertical-align: middle;
      }
    }
  }
}
</style>
