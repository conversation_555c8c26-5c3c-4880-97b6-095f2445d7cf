<template>
  <div class="container" v-loading="loading">
    <div class="headerTitle">漏洞审核</div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane :label="`未审核(${wait_audit})`" name="0"> </el-tab-pane>
        <el-tab-pane :label="`审核驳回(${has_audit_no})`" name="2"> </el-tab-pane>
        <el-tab-pane :label="`审核通过(${has_audit_yes})`" name="1"> </el-tab-pane>
      </el-tabs>
      <div class="tableWrap">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
        >
          <el-table-column
            v-for="(item, index) in tableHeaderIsShow"
            :key="index"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'poc_scan_type'"
                >{{ getPocTypeName(scope.row[item.name]) }}
                <el-popover
                  placement="top"
                  width="315"
                  style="padding-right: 0px !important; padding-left: 0px !important"
                  popper-class="rulePopover"
                >
                  <i
                    slot="reference"
                    v-if="scope.row[item.name] == 1 || scope.row[item.name] == 2"
                    style="color: #2677ff; cursor: pointer"
                    class="el-icon-view"
                  ></i>
                  <div class="myruleItemBox">
                    <span class="myruleItem" v-for="(v, i) in scope.row['poc']" :key="i">{{
                      v.name
                    }}</span>
                  </div>
                </el-popover>
              </span>
              <span v-else-if="item.name == 'op'">{{
                scope.row[item.name] ? scope.row[item.name].name : '-'
              }}</span>
              <span v-else-if="item.name == 'company'">{{
                scope.row[item.name] ? scope.row[item.name].name : '-'
              }}</span>
              <span v-else-if="item.name == 'task_type'">{{
                scope.row['type'] == 1 ? '周期任务' : '立即执行'
              }}</span>
              <div v-else-if="item.name == 'ips'" class="ruleItemBox">
                <span
                  class="el-icon-document-copy"
                  @click="
                    copyFun(
                      scope.row[item.name].map((item) => {
                        return item.ip
                      })
                    )
                  "
                  style="color: #2677ff; cursor: pointer"
                  >复制</span
                >
                <span
                  v-if="activeName == 0"
                  class="el-icon-c-scale-to-original"
                  @click="checkPoc(scope.row)"
                  style="color: #2677ff; cursor: pointer"
                  >核查</span
                >
                <span
                  class="ruleItem"
                  v-for="ch in get_ips(scope.row[item.name], 'filter')"
                  :key="ch"
                  >{{ ch }}</span
                >
                <el-popover
                  placement="top"
                  width="315"
                  style="padding-right: 0px !important; padding-left: 0px !important"
                  popper-class="rulePopover"
                >
                  <div class="myruleItemBox">
                    <span
                      class="myruleItem"
                      v-for="(v, i) in get_ips(scope.row[item.name])"
                      :key="i"
                      >{{ v }}</span
                    >
                  </div>
                  <div
                    slot="reference"
                    v-show="get_ips(scope.row[item.name]).length > 2"
                    class="ruleItemNum"
                    >共{{ get_ips(scope.row[item.name]).length }}条</div
                  >
                </el-popover>
              </div>
              <span v-else>{{
                scope.row[item.name] && scope.row[item.name].length != 0
                  ? scope.row[item.name]
                  : '-'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="activeName == '0'"
            fixed="right"
            align="left"
            label="操作"
            width="100"
          >
            <template slot-scope="scope">
              <span>
                <el-button type="text" size="small" @click="goAudit(scope.row, 1)">通过</el-button>
                <el-button type="text" size="small" @click="goAudit(scope.row, 2)">驳回</el-button>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="650px"
    >
      <template slot="title"> 驳回原因 </template>
      <div class="dialog-body">
        <el-form
          ref="ruleForm"
          label-width="0"
          class="demo-ruleForm"
          style="width: 100%; padding: 0 !important"
        >
          <el-form-item label="">
            <el-input
              type="textarea"
              :rows="12"
              v-model="reason"
              placeholder="请填写驳回原因"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisible = false">关闭</el-button>
        <el-button class="highBtn" @click="saveAudit">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleCheck"
      width="550px"
    >
      <template slot="title"> 扫描目标核查 </template>
      <div class="dialog-body">
        <el-form
          ref="ruleForm"
          label-width="70px"
          class="demo-ruleForm"
          style="width: 100%; padding: 0 !important"
        >
          <el-form-item label="扫描目标">
            <div class="ruleItemBox">
              <span
                class="ruleItem"
                v-for="ch in get_ips(this.currentRow.ips, 'filter')"
                :key="ch"
                >{{ ch }}</span
              >
              <el-popover
                placement="top"
                width="315"
                style="padding-right: 0px !important; padding-left: 0px !important"
                popper-class="rulePopover"
              >
                <div class="myruleItemBox">
                  <span
                    class="myruleItem"
                    v-for="(v, i) in get_ips(this.currentRow.ips)"
                    :key="i"
                    >{{ v }}</span
                  >
                </div>
                <div
                  slot="reference"
                  v-show="get_ips(this.currentRow.ips).length > 2"
                  class="ruleItemNum"
                  >共{{ get_ips(this.currentRow.ips).length }}条</div
                >
              </el-popover>
            </div>
          </el-form-item>
          <!-- <div style="display:flex;justify-content: space-between">
          </div> -->
          <el-form-item label="授权目标">
            <el-input
              v-model="userIps"
              type="textarea"
              :rows="5"
              placeholder="请填写授权IP，换行分隔"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="checkIsShow" label="核查结果">
            <div v-if="checkResult.length > 0">
              <el-button type="text" size="small" @click="copyFun(checkResult)"> 复制</el-button>
              <el-button type="text" size="small" @click="goAfterCheckAudit"> 驳回</el-button>
              <p>以下扫描目标不在授权目标中</p>
              <ul class="checkList">
                <li v-for="(item, index) in checkResult" :key="index">{{ item }}</li>
              </ul>
            </div>
            <div v-else> 扫描目标均已授权 </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleCheck = false">关闭</el-button>
        <el-button class="highBtn" @click="saveCheck">核查</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { pocAuditList, pocAudit, pocAuditCheck } from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip
  },

  data() {
    return {
      editDictName: '',
      currentDictId: '',
      currentDictName: '',
      currentDictType: '',
      reason: '',
      isAddDict: false, // 是否是新建字典
      addDictForm: {
        name: ''
      },
      dictList: [],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      btnLoading: false,
      checkedAll: false, // 是否全选
      checkedArr: [],
      formInline: {
        keyword: '',
        level: [],
        vul_type: [],
        status: '',
        poc_group_id: [],
        page: 1,
        per_page: 10
      },
      formInlineGroup: {
        page: 1,
        per_page: 10,
        keyword: ''
      },
      ruleGroupForm: {
        pocs: [],
        name: ''
      },
      addIsTrue: true,
      loading: false,
      diaLoading: false,
      rklData: '',
      rklEditId: '',
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      pocTypeArr: [],
      pocGroupsNoPageArr: [],
      vulTypeList: [],
      pocList: [],
      pocListCopy: [],
      ruleForm: {
        port: '',
        pro: '',
        region: ''
      },
      dialogFormVisibleRkl: false,
      dialogFormVisible: false,
      highCheckdialog: false,
      dialogFormVisibleCheck: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      activeName: '0',
      tableData: [],
      currentPage: 1,
      tableHeader: [
        {
          label: '任务名称',
          name: 'name',
          fixed: 'left',
          minWidth: '180',
          path: ['0', '1', '2']
        },
        {
          label: '扫描目标',
          name: 'ips',
          minWidth: '250',
          path: ['0', '1', '2']
        },
        {
          label: 'POC范围',
          name: 'poc_scan_type',
          minWidth: '120',
          path: ['0', '1', '2']
        },
        {
          label: '任务计划',
          name: 'task_type',
          minWidth: '80',
          path: ['0', '1', '2']
        },
        {
          label: '创建人',
          name: 'op',
          minWidth: '80',
          path: ['0', '1', '2']
        },
        {
          label: '企业名称',
          name: 'company',
          minWidth: '120',
          path: ['0', '1', '2']
        },
        {
          label: '创建时间',
          name: 'created_at',
          minWidth: '120',
          path: ['0', '1', '2']
        },
        {
          label: '审核通过时间',
          name: 'audit_time',
          minWidth: '120',
          path: ['1']
        },
        {
          label: '审核驳回时间',
          name: 'audit_time',
          minWidth: '120',
          path: ['2']
        },
        {
          label: '驳回原因',
          name: 'reason',
          path: ['2']
        }
      ],
      wait_audit: 0,
      has_audit_no: 0,
      has_audit_yes: 0,
      user: {
        role: ''
      },
      checkResult: '',
      currentRow: {
        ips: []
      },
      userIps: '',
      checkIsShow: false,
      auditObj: {
        id: '',
        type: '',
        is_audit: '',
        reason: ''
      }
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user && this.user.role == 2) {
        this.getPocListData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany']),
    tableHeaderIsShow(path) {
      let arr = []
      arr = this.tableHeader.filter((item) => {
        return item.path.indexOf(this.activeName) != -1
      })
      return arr
    }
  },
  async created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (sessionStorage.getItem('activeTabName')) {
      this.activeName = sessionStorage.getItem('activeTabName')
    }
    if (this.user.role == 2 && !this.currentCompany) return
    this.getPocListData()
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    async filterMethod(query, item) {
      return item.name.indexOf(query) > -1
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    getPocTypeName(val) {
      let name = ''
      switch (val) {
        case 0:
          name = '全部PoC'
          break
        case 1:
          name = '指定PoC范围'
          break
        case 2:
          name = '指定PoC分组'
          break
        case 3:
          name = '暴力破解'
          break
        default:
          name = '-'
      }
      return name
    },
    get_ips(val, filter) {
      let arr = []
      let newArr = val.map((item) => {
        return item.ip
      })
      if (newArr) {
        if (filter) {
          arr = newArr.slice(0, 2)
        } else {
          arr = newArr
        }
      } else {
        arr = []
      }
      return arr
    },
    async getPocListData() {
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.is_audit = this.activeName
      this.loading = true
      pocAuditList(this.formInline)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
          this.wait_audit = res.data && res.data.wait_audit ? res.data.wait_audit : 0
          this.has_audit_no = res.data && res.data.has_audit_no ? res.data.has_audit_no : 0
          this.has_audit_yes = res.data && res.data.has_audit_yes ? res.data.has_audit_yes : 0
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch(() => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    checkPoc(row) {
      this.currentRow = row
      this.checkIsShow = false
      this.checkResult = []
      this.userIps = ''
      this.dialogFormVisibleCheck = true
    },
    async saveCheck() {
      let obj = {
        id: this.currentRow.id,
        ips: this.userIps.split(/[\r\n]/)
      }
      let res = await pocAuditCheck(obj)
      if (res.code == 0) {
        this.checkResult = res.data
        this.checkIsShow = true
        // this.dialogFormVisibleCheck = false
      }
    },
    // 核查后驳回
    async goAfterCheckAudit() {
      this.auditObj = {
        id: this.currentRow.id,
        type: this.currentRow.type, // 是否为周期任务 0否 1是
        is_audit: 2, // 0待审核1审核成功2审核驳回
        reason: this.checkResult.join('，')
      }
      let res = await pocAudit(this.auditObj)
      if (res.code == 0) {
        this.dialogFormVisible = false
        this.$message.success('驳回成功！')
        this.getPocListData()
      }
    },
    async goAudit(row, icon) {
      if (icon == 1) {
        // 通过
        this.auditObj = {
          id: row.id,
          type: row.type, // 是否为周期任务 0否 1是
          is_audit: icon, // 0待审核1审核成功2审核驳回
          reason: ''
        }
        let res = await pocAudit(this.auditObj)
        if (res.code == 0) {
          this.$message.success('审核通过！')
          this.getPocListData()
        }
      } else {
        // 驳回2
        this.auditObj = {
          id: row.id,
          type: row.type, // 是否为周期任务 0否 1是;type=1为周期任务
          is_audit: icon, // 0待审核1审核成功2审核驳回
          reason: ''
        }
        this.dialogFormVisible = true
      }
    },
    async saveAudit() {
      this.auditObj.reason = this.reason
      let res = await pocAudit(this.auditObj)
      if (res.code == 0) {
        this.dialogFormVisible = false
        this.$message.success('操作成功！')
        this.getPocListData()
      }
    },
    handleClick() {
      sessionStorage.setItem('activeTabName', this.activeName)
      this.currentPage = 1
      this.checkedAll = false
      this.getPocListData()
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getPocListData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPocListData(true)
    },
    resetForm() {
      this.formInline = {
        rank: '',
        port: ''
      }
    },
    newAdd() {
      this.isAddDict = true
      this.dictList.forEach((item) => {
        item.isEditDict = false
      })
      this.addDictForm.name = ''
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;
    .el-form {
      /deep/ .el-form-item__content {
        p,
        span {
          color: #606266 !important;
          font-size: 14px !important;
        }
        p span {
          color: #606266 !important;
          font-size: 14px !important;
        }
        .el-button--text {
          span {
            color: #2677ff !important;
          }
        }
        .checkList {
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          padding: 5px 15px;
          line-height: 1.5;
          max-height: 200px;
          overflow: auto;
        }
      }
      .ruleItemBox {
        display: flex;
        flex-wrap: wrap !important;
        padding: 0 12px !important;
        .el-icon-document-copy {
          display: inline-block;
          line-height: 32px;
          margin-right: 5px;
        }
        .el-icon-c-scale-to-original {
          display: inline-block;
          line-height: 32px;
          margin-right: 5px;
        }
      }
      .ruleItem,
      .ruleItemNum {
        line-height: 16px;
        padding: 2px 10px;
        margin: 5px 8px 5px 0px;
        background: #ffffff;
        border-radius: 14px;
        border: 1px solid #d1d5dd;
        white-space: pre-wrap;
      }
      .ruleItemNum {
        display: inline-block;
        background: #f0f3f8;
        border: 1px solid #dce5f3;
        cursor: pointer;
      }
    }
    ::v-deep .left {
      flex: 1;
      width: 46%;
      padding-right: 1%;
      & > p {
        text-align: right;
        .el-icon-plus {
          margin-bottom: 10px;
        }
      }
      .addClass {
        margin-bottom: 10px;
        .el-input-group__append {
          padding: 0 24px !important;
        }
        .el-input-group__append .el-button {
          padding: 0 10px !important;
        }
      }
      .editClass {
        .el-input-group__append {
          padding: 0 10px !important;
        }
        .el-input-group__append .el-button {
          padding: 0 10px !important;
        }
      }
      .handlebtnWrap {
        width: 16%;
        display: flex;
        justify-content: space-between;
      }
      .iconClass {
        color: #2677ff;
        font-size: 14px;
        cursor: pointer;
      }
      ul {
        height: 75%;
        overflow: auto;
        li {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 10px;
          cursor: pointer;
          border-left: 4px solid transparent;
          .el-checkbox {
            margin-right: 10px;
          }
        }
        .dictname {
          display: inline-block;
          width: 60%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .activeClass {
          color: #2677ff;
          border-left: 4px solid #2677ff;
          background: #eff2f7;
        }
      }
    }
    .right {
      width: 52%;
      padding-left: 1%;
      border-left: 1px solid #e9ebef;
      .rklClass {
        position: relative;
        /deep/.el-textarea {
          background: #ffffff;
          border-radius: 0;
          border: 1px solid #d1d5dd;
          border-bottom: 0;
          .el-textarea__inner {
            height: 100%;
            border: 0 !important;
          }
          .el-textarea__inner:hover {
            border: 0;
          }
        }
        .rkl_footer {
          // position: absolute;
          // top: 0;
          // left: 0;
          text-align: right;
          padding: 5px;
          background: #fff;
          border: 1px solid #d1d5dd;
          .highBtn {
            width: 50px;
            height: 22px;
          }
        }
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 120px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 120px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 224px) !important;
    }
    .tableWrap {
      height: calc(100% - 137px);
      padding: 16px 20px;
      .ruleItemBox {
        display: flex;
        flex-wrap: wrap !important;
        padding: 0 12px !important;
        .el-icon-document-copy {
          display: inline-block;
          line-height: 32px;
          margin-right: 10px;
        }
        .el-icon-c-scale-to-original {
          display: inline-block;
          line-height: 32px;
          margin-right: 10px;
        }
      }
      .ruleItem,
      .ruleItemNum {
        line-height: 16px;
        padding: 2px 10px;
        margin: 5px 8px 5px 0px;
        background: #ffffff;
        border-radius: 14px;
        border: 1px solid #d1d5dd;
        white-space: pre-wrap;
      }
      .ruleItemNum {
        display: inline-block;
        background: #f0f3f8;
        border: 1px solid #dce5f3;
        cursor: pointer;
      }
      .pocName {
        cursor: pointer;
      }
    }
    .el-table {
      border: 0;
    }
    .mystatus {
      display: inline-block;
      // background-color: #fff;
      padding: 1px 14px;
      border-radius: 14px;
    }
    .yzstatus {
      background: rgba(255, 70, 70, 0.08);
      border: 1px solid #ff4646;
      color: #ff4646 !important;
    }
    .gwstatus {
      background: rgba(255, 121, 0, 0.08);
      border: 1px solid #ff7900;
      color: #ff7900 !important;
    }
    .zwstatus {
      background: rgba(248, 193, 54, 0.08);
      border: 1px solid #f8c136;
      color: #f8c136 !important;
    }
    .dwstatus {
      background: rgba(38, 119, 255, 0.08);
      border: 1px solid #2677ff;
      color: #2677ff !important;
    }
  }
  .disabled {
    pointer-events: none;
    cursor: default;
  }
}
</style>
