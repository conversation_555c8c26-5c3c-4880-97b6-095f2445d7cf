<template>
  <div class="container" v-loading="loading">
    <div class="header">
      <div v-show="!showCustomPoc" class="headerTitle"
        >{{
          activeName == 'first' ? 'PoC管理' : activeName == 'second' ? 'PoC分组管理' : '自定义POC'
        }}
      </div>
      <div v-if="activeName == 'third'" class="numWrap">
        <el-button
          :disabled="UpdatePocRiskBtnLoading"
          :loading="UpdatePocRiskBtnLoading"
          class="normalBtn"
          type="primary"
          @click="goUpdatePocRiskIp(true)"
          >更新风险资产IP</el-button
        >
        <span class="waitNum" v-if="last_rist_time"
          >风险资产更新时间：<span class="num">{{ last_rist_time }}</span></span
        >
      </div>
    </div>

    <div class="home_header" v-show="!showCustomPoc">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="PoC" name="first">
          <div class="filterTab">
            <div>
              <el-input
                v-model="formInline.keyword"
                @keyup.enter.native="checkFuncList"
                placeholder="请输入关键字进行搜索"
                id="poc_keycheck"
              >
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
                <el-tooltip
                  slot="prepend"
                  class="item"
                  effect="dark"
                  content="支持检索字段：风险名称、CVE编号、CNNVD、CVSS、CNVD"
                  placement="top"
                  :open-delay="100"
                >
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </el-input>
              <span @click="highCheckdialog = true" id="poc_filter" style="width: 80px"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <div>
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAll"
                @change="checkAllChange"
                id="poc_all"
                >选择全部</el-checkbox
              >
              <el-button
                class="normalBtnRe"
                type="primary"
                @click="exportRA"
                v-if="user.role == 2 || user.role == 1"
                :loading="exportBtnLoading"
                >导出风险资产</el-button
              >
              <el-button
                class="normalBtnRe"
                type="primary"
                @click="updatePortStatus(1)"
                id="poc_disable"
                >禁用</el-button
              >
              <el-button class="normalBtn" type="primary" @click="updatePortStatus(0)" id="poc_open"
                >启用</el-button
              >
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="分组" name="second">
          <div class="filterTab">
            <div>
              <el-input
                v-model="formInlineGroup.keyword"
                @keyup.enter.native="checkGroupFuncList"
                placeholder="请输入分组名称进行搜索"
                id="poc_keycheck"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="checkGroupFuncList"
                ></el-button>
              </el-input>
            </div>
            <div>
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAll"
                @change="checkAllChange"
                id="poc_all"
                >选择全部</el-checkbox
              >
              <el-button class="normalBtn" type="primary" @click="removeOne('more')" id="poc_del"
                >删除</el-button
              >
              <el-button class="normalBtn" type="primary" @click="insertShow" id="poc_add"
                >新建分组</el-button
              >
            </div>
          </div>
        </el-tab-pane>
        <!-- 自定义poc -->
        <!-- v-if="userInfo.is_show_custom_poc === true && userInfo.is_local === true" -->

        <el-tab-pane label="自定义" name="third">
          <div class="filterTab">
            <div>
              <el-input
                v-model="formCustomPoc.keyword"
                @keyup.enter.native="checkCustomFuncList"
                placeholder="请输入关键字进行搜索"
                id="poc_keycheck"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="checkCustomFuncList"
                ></el-button>
              </el-input>
              <span @click="pocCustomFilter" id="poc_filter" style="width: 80px"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <div>
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAll"
                @change="checkAllChange"
                id="poc_all"
                >选择全部</el-checkbox
              >
              <el-button
                class="normalBtn transParent"
                type="primary"
                @click="batchOnline(0)"
                id="poc_del"
                >下线</el-button
              >
              <el-button
                class="normalBtn transParent"
                type="primary"
                @click="batchOnline(1)"
                id="poc_del"
                >发布</el-button
              >
              <el-button class="normalBtn" type="primary" @click="handleScan" id="poc_add"
                >执行扫描</el-button
              >
              <el-button class="normalBtn" type="primary" @click="addPoc" id="poc_add"
                >新建Poc</el-button
              >
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="poc"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="(item, index) in filteredTableHeader"
            :key="index"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span
                class="pocName"
                v-if="activeName == 'first' && item.name == 'name'"
                @click="clickName(scope.row.id)"
              >
                <span class="ellipsis">{{ scope.row[item.name] }}</span>
              </span>
              <span
                v-else-if="activeName == 'first' && item.name == 'threat_num'"
                @click="click_threat_num(scope.row.id)"
                style="color: #2677ff; cursor: pointer"
                :class="scope.row[item.name] == 0 ? 'disabled' : ''"
                >{{ scope.row[item.name] }}</span
              >
              <p v-else-if="item.name == 'level'">
                <span v-if="scope.row[item.name] == 3" class="deepRedRadiusBorder">严重</span>
                <span v-if="scope.row[item.name] == 2" class="redRadiusBorder">高危</span>
                <span v-if="scope.row[item.name] == 1" class="originRadiusBorder">中危</span>
                <span v-if="scope.row[item.name] == 0" class="yellowRadiusBorder">低危</span>
              </p>
              <span class="greenLine" v-else-if="item.name == 'status' && scope.row[item.name] == 0"
                >启用</span
              >
              <span class="redLine" v-else-if="item.name == 'status' && scope.row[item.name] == 1"
                >禁用</span
              >

              <span
                class="greenLine"
                v-else-if="
                  item.name == 'state' &&
                  (scope.row[item.name] == 0 || scope.row[item.name] == undefined)
                "
                >已发布</span
              ><span class="redLine" v-else-if="item.name == 'state' && scope.row[item.name] == 1"
                >未发布</span
              >
              <span v-else-if="item.name == 'tags'">{{
                (scope.row[item.name] && scope.row[item.name].join('/')) || '-'
              }}</span>
              <span v-else-if="item.name == 'groupName'">{{
                scope.row[item.name] &&
                scope.row[item.name].length &&
                scope.row[item.name].length > 0
                  ? scope.row[item.name].join(',')
                  : '-'
              }}</span>
              <span v-else-if="item.name == 'group_name'">
                {{
                  scope.row[item.name] && scope.row[item.name].length > 0
                    ? scope.row[item.name].join('；')
                    : '-'
                }}
              </span>
              <span v-else-if="item.name == 'poc_from'">
                {{ scope.row.from == 1 ? '系统poc' : '自定义poc' }}
              </span>
              <span v-else-if="item.name == 'has_exp'">
                {{ scope.row[item.name] == 1 ? '支持' : '不支持' }}
              </span>
              <span v-else-if="item.name == 'updated_at'">{{
                transferTime(scope.row[item.name])
              }}</span>
              <span v-else>{{
                scope.row[item.name] && scope.row[item.name].length != 0
                  ? scope.row[item.name]
                  : '-'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="100">
            <template slot-scope="scope">
              <span>
                <el-button
                  v-if="
                    (activeName == 'first' && scope.row['vul_type'] == '弱口令') ||
                    activeName == 'second' ||
                    activeName == 'third'
                  "
                  type="text"
                  size="small"
                  @click="editOne(scope.row.id, scope.row.name)"
                  id="poc_edit"
                  >编辑</el-button
                >
                <el-button
                  v-if="activeName == 'second' || activeName == 'third'"
                  type="text"
                  size="small"
                  @click="removeOne('one', scope.row.id)"
                  id="poc_del"
                  >删除</el-button
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInfo"
      width="850px"
    >
      <template slot="title"> 漏洞详情 </template>
      <div class="dialog-body">
        <el-form
          v-loading="infoLoading"
          ref="ruleForm"
          label-width="70px"
          class="demo-ruleForm"
          style="padding: 0 !important"
        >
          <el-form-item label="漏洞名称">
            <p>{{ ruleForm.name }}</p>
          </el-form-item>
          <el-form-item label="漏洞等级" prop="port">
            <span v-if="ruleForm.level == 3" class="deepRedRadiusBorder">严重</span>
            <span v-if="ruleForm.level == 2" class="redRadiusBorder">高危</span>
            <span v-if="ruleForm.level == 1" class="originRadiusBorder">中危</span>
            <span v-if="ruleForm.level == 0" class="yellowRadiusBorder">低危</span>
          </el-form-item>
          <el-form-item label="CVE编号">
            <p>{{ ruleForm.cve_ids }}</p>
          </el-form-item>
          <el-form-item label="漏洞描述">
            <p v-html="ruleForm.description"></p>
          </el-form-item>
          <el-form-item label="漏洞危害">
            <p v-html="ruleForm.impact"></p>
          </el-form-item>
          <el-form-item label="解决方案">
            <p v-html="ruleForm.recommendation"></p>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleInfo = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleRkl"
      width="700px"
    >
      <template slot="title"> 编辑弱口令 </template>
      <div class="dialog-body">
        <div class="left">
          <p>
            <i class="iconClass el-icon-plus" @click="newAdd" id="poc_dict_add">新增</i>
          </p>
          <el-input
            class="addClass"
            v-if="isAddDict"
            v-model="addDictForm.name"
            :rows="9"
            placeholder="请输入字典名称"
          >
            <el-button
              slot="append"
              icon="el-icon-circle-check"
              @click="saveDictName('')"
            ></el-button>
            <!-- 新增保存 -->
            <el-button
              slot="append"
              icon="el-icon-circle-close"
              @click="isAddDict = false"
            ></el-button>
            <!-- 取消新增 -->
          </el-input>
          <ul>
            <li
              :class="item.id == currentDictId ? 'activeClass' : ''"
              v-for="(item, index) in dictList"
              :key="item.id"
              @click.prevent="dictClick(item)"
            >
              <el-tooltip
                :content="item.is_require_attack == '0' ? '关闭暴力破解' : '开启暴力破解'"
                placement="top"
              >
                <el-switch
                  @change="changeAttack($event, item.id)"
                  v-model="item.is_require_attack"
                  active-color="#2677FF"
                  inactive-color="#ddd"
                  :disabled="item.dict_type == 1"
                  :active-value="1"
                  :inactive-value="0"
                >
                </el-switch>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                :content="item.name"
                :open-delay="500"
              >
                <span class="dictname" v-if="!item.isEditDict">{{ item.name }}</span>
                <el-input
                  class="editClass"
                  v-if="item.isEditDict"
                  @click.stop.native
                  v-model="addDictForm.name"
                  :rows="9"
                  placeholder="请输入字典名称"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-circle-check"
                    @click="saveDictName(item.id, index)"
                  ></el-button>
                  <!-- 新增保存 -->
                  <el-button
                    slot="append"
                    icon="el-icon-circle-close"
                    @click="item.isEditDict = false"
                  ></el-button>
                  <!-- 取消新增 -->
                </el-input>
              </el-tooltip>
              <div class="handlebtnWrap" v-if="!item.isEditDict">
                <span
                  v-if="item.dict_type == 2"
                  class="el-icon-delete"
                  @click.stop="delDictItem(item.id)"
                ></span>
                <span
                  v-if="item.dict_type == 2"
                  class="el-icon-edit"
                  @click.stop="editDictItem(item, index)"
                ></span>
              </div>
            </li>
          </ul>
        </div>
        <div class="right">
          <div class="rklClass">
            <el-input
              v-if="activeName == 'first'"
              type="textarea"
              v-model="rklData"
              :rows="9"
              placeholder="请输入弱口令,换行分隔"
              :disabled="isdis"
            >
            </el-input>
            <div class="rkl_footer" v-if="currentDictType == 2">
              <span class="placeholderClass">保存字典弱口令</span>
              <el-button
                class="highBtn"
                :loading="btnLoading"
                @click="insertRklSave"
                id="poc_edit_sure"
                >保存</el-button
              >
            </div>
          </div>
          <p v-if="rklPocName != 'SNMP 弱口令漏洞'" class="placeholderClass">
            弱口令参考格式（账号密码一行一个，账号与密码以第一个冒号分隔，示例root:root代表，账号是root，密码是root，冒号要求英文冒号）<br />
            root:root<br />
            root:2015<br />
          </p>
          <p v-if="rklPocName == 'SNMP 弱口令漏洞'" class="placeholderClass">
            弱口令参考格式（仅需要输入密码，一行一个）<br />
            root<br />
            123456<br />
          </p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleRkl = false" id="poc_edit_cancel"
          >关闭</el-button
        >
        <!-- <el-button class="highBtn" :loading="btnLoading" @click="insertRklSave" id="poc_edit_sure">确定</el-button> -->
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleFenzu"
      width="650px"
    >
      <template slot="title">
        {{ !addIsTrue ? '编辑分组' : '新建分组' }}
      </template>
      <div class="dialog-body">
        <el-form
          v-loading="diaLoading"
          :model="ruleGroupForm"
          style="padding: 0 !important"
          :rules="rulesGroup"
          ref="ruleFormGroup"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="分组名称" prop="name">
            <el-input v-model="ruleGroupForm.name" placeholder="请输入分组名称"></el-input>
          </el-form-item>
          <el-form-item label="PoC" prop="pocs">
            <new-transfer
              :titles="['未选择', '已选择']"
              filterable
              :filter-node-method="filterMethod"
              filter-placeholder="请输入查询条件"
              v-model="ruleGroupForm.pocs"
              :props="transferPropGroup"
              ref="reserve"
              @left-check-change="handleChangeLeft"
              @right-check-change="handleChangeRight"
              :data="pocList"
            >
              <span slot-scope="{ option }">
                <span :title="option.name">{{ option.name }}</span>
              </span>
            </new-transfer>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleFenzu = false" id="poc_add_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="insertSave" id="poc_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="104px">
          <el-form-item label="漏洞等级：" prop="level">
            <el-select
              filterable
              v-model="formInline.level"
              placeholder="请选择漏洞等级"
              multiple
              collapse-tags
              clearable
              @change="selectChange($event, 'level', levelArr, true, true)"
            >
              <el-option
                v-for="item in levelArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="漏洞类型：" prop="vul_type">
            <el-select
              filterable
              v-model="formInline.vul_type"
              placeholder="请选择漏洞类型"
              multiple
              collapse-tags
              clearable
              @change="selectChange($event, 'vul_type', vulTypeList, false, true)"
            >
              <el-option
                v-for="item in vulTypeList"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="PoC分组：" prop="poc_group_id">
            <el-select
              filterable
              v-model="formInline.poc_group_id"
              placeholder="请选择PoC分组"
              multiple
              collapse-tags
              clearable
              @change="selectChange($event, 'poc_group_id', pocGroupsNoPageArr, true, true)"
            >
              <el-option
                v-for="item in pocGroupsNoPageArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：" prop="status">
            <el-select
              v-model="formInline.status"
              placeholder="请选择状态"
              clearable
              @change="selectChange($event, 'status', statusArr, true, false)"
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否支持exp验证：" prop="status">
            <el-select
              v-model="formInline.has_exp"
              placeholder="请选择状态"
              clearable
              @change="selectChange($event, 'has_exp', expArr, true, false)"
            >
              <el-option
                v-for="item in expArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="更新时间：" prop="disclosure_date">
            <el-date-picker
              v-model="formInline.disclosure_date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="漏洞标签：" prop="tags">
            <el-select
              filterable
              v-model="formInline.tags"
              placeholder="请选择漏洞标签"
              clearable
              @change="selectChange($event, 'tags', tagsList, false, false)"
            >
              <el-option
                v-for="item in tagsList"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            style="padding: 0"
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="poc_filter_repossess"
            >重置</el-button
          >
          <el-button
            style="padding: 0"
            class="highBtn"
            @click="checkFuncList"
            id="poc_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>

    <!-- 自定义Poc高级筛选 -->
    <el-drawer
      title="高级筛选"
      :visible.sync="highCheckCustomPocdialog"
      direction="rtl"
      ref="drawer"
    >
      <div class="demo-drawer__content">
        <el-form :model="formCustomPoc" ref="drawerForm" label-width="104px">
          <el-form-item label="漏洞等级：" prop="level">
            <el-select
              filterable
              v-model="formCustomPoc.level"
              placeholder="请选择漏洞等级"
              multiple
              collapse-tags
              clearable
              @change="selectChange($event, 'level', levelArr, true, true)"
            >
              <el-option
                v-for="item in levelArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="漏洞类型：" prop="vul_type">
            <el-select
              filterable
              v-model="formCustomPoc.vul_type"
              placeholder="请选择漏洞类型"
              multiple
              collapse-tags
              clearable
              @change="selectChange($event, 'vul_type', pocCustomTypeList, false, true)"
            >
              <el-option
                v-for="item in pocCustomTypeList"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：" prop="status">
            <el-select
              v-model="formCustomPoc.status"
              placeholder="请选择状态"
              clearable
              @change="selectChange($event, 'status', pocCustomStatus, true, false)"
            >
              <el-option
                v-for="item in pocCustomStatus"
                :key="item.key"
                :label="item.title"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            style="padding: 0"
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="poc_filter_repossess"
            >重置</el-button
          >
          <el-button
            style="padding: 0"
            class="highBtn"
            @click="searchCustomFuncList"
            id="poc_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>

    <pocCustomManage
      :pocCustomId="pocCustomId"
      :ruleScanForm="ruleScanForm"
      v-show="showCustomPoc"
      ref="pocCustomManage"
      @close="closePocCustom"
    />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import newTransfer from '../../components/transfer/src/main'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import PocCustomManage from '../../components/pocCustom/PocCustomManage.vue'
import { addVulnerability } from '@/api/apiConfig/api.js'
import {
  delPocGroup,
  updatePocGroup,
  infoPocGroup,
  addPocGroup,
  getPocGroupListPage,
  getPocCustomList,
  delPocCustom,
  getPocGroupListNoPage,
  exportRiskAsset,
  saveRequireAttack,
  delRkl,
  addEditRkl,
  getRklInfo,
  infoPoc,
  pocTypeList,
  getPocListNoPage,
  getPocList,
  pocStatusUpdate,
  releasePocCustom,
  updateRiskAsset
} from '@/api/apiConfig/poc.js'

export default {
  components: {
    newTransfer,
    tableTooltip,
    hightFilter,
    PocCustomManage
  },
  data() {
    return {
      tagsList: [],
      exportBtnLoading: false,
      pocCustomStatus: [
        {
          title: '已发布',
          value: 2,
          key: 2
        },
        {
          title: '未发布',
          value: 1,
          key: 1
        }
      ],
      highTabShow: [
        {
          label: '是否支持exp验证',
          name: 'has_exp',
          type: 'select'
        },

        {
          label: '漏洞等级',
          name: 'level',
          type: 'select'
        },
        {
          label: '漏洞类型',
          name: 'vul_type',
          type: 'select'
        },
        {
          label: 'PoC分组',
          name: 'poc_group_id',
          type: 'select'
        },
        {
          label: '状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '更新时间',
          name: 'disclosure_date',
          type: 'date'
        },
        {
          label: '漏洞标签',
          name: 'tags',
          type: 'select'
        }
      ],
      editDictName: '',
      currentDictId: '',
      currentDictName: '',
      currentDictType: '',
      pocCustomId: null,
      isAddDict: false, // 是否是新建字典
      addDictForm: {
        name: ''
      },
      dictList: [],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      btnLoading: false,
      UpdatePocRiskBtnLoading: false,
      checkedAll: false, // 是否全选
      checkedArr: [],
      ruleScanForm: {
        name: '',
        bandwidth: '1000',
        type: '6',
        ip_type: 1,
        ips: [],
        poc_ids: [],
        protocol_concurrency: 0,
        poc_scan_type: 1,
        scan_engine: '',
        scan_type: 1,
        task_type: 2,
        type: 6,
        scan_range: ''
      },
      formInline: {
        keyword: '',
        level: [],
        vul_type: [],
        status: '',
        poc_group_id: [],
        disclosure_date: [],
        page: 1,
        per_page: 10
      },
      formInlineGroup: {
        page: 1,
        per_page: 10,
        keyword: ''
      },
      formCustomPoc: {
        keyword: '',
        level: [],
        publish: [],
        vul_type: [],
        status: '',
        poc_group_id: [],
        disclosure_date: [],
        page: 1,
        per_page: 10
      },
      ruleGroupForm: {
        pocs: [],
        name: ''
      },
      addIsTrue: true,
      loading: false,
      diaLoading: false,
      rulesGroup: {
        name: [{ required: true, message: '请输入poc分组名称', trigger: 'change' }],
        pocs: [{ required: true, message: '请选择poc', trigger: 'change' }]
      },
      rklPocName: '',
      rklData: '',
      rklEditId: '',
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      transferPropGroup: {
        key: 'id',
        label: 'name'
      },
      rules: {
        port: [{ required: true, message: '请输入活动名称', trigger: 'change' }],
        pro: [{ required: true, message: '请输入活动名称', trigger: 'change' }]
      },
      pocTypeArr: [],
      pocGroupsNoPageArr: [],
      vulTypeList: [],
      pocCustomTypeList: [],
      pocList: [],
      pocListCopy: [],
      statusArr: [
        {
          name: '启用',
          id: 0
        },
        {
          name: '禁用',
          id: 1
        }
      ],
      levelArr: [
        {
          name: '低危',
          id: 0
        },
        {
          name: '中危',
          id: 1
        },
        {
          name: '高危',
          id: 2
        },
        {
          name: '严重',
          id: 3
        }
      ],
      expArr: [
        {
          name: '支持',
          id: 1
        },
        {
          name: '不支持',
          id: 0
        }
      ],
      ruleForm: {
        port: '',
        pro: '',
        region: ''
      },
      last_rist_time: '',
      dialogFormVisibleRkl: false,
      infoLoading: false,
      dialogFormVisibleInfo: false,
      highCheckdialog: false,
      highCheckCustomPocdialog: false,
      showCustomPoc: false, // 新建poc页面是否显示
      dialogFormVisibleFenzu: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      activeName: 'first',
      tableData: [],
      currentPage: 1,
      tableHeader: {
        first: [
          {
            label: '风险名称',
            name: 'name',
            fixed: 'left',
            minWidth: 250
          },
          {
            label: '级别',
            name: 'level',
            minWidth: 100
          },
          {
            label: '漏洞类型',
            name: 'vul_type',
            minWidth: 120
          },
          {
            label: '标签',
            name: 'tags',
            minWidth: 120
          },
          {
            label: 'CVE编号',
            name: 'cve_ids',
            minWidth: 120
          },
          {
            label: 'CNNVD',
            name: 'cnnvd',
            minWidth: 120
          },

          {
            label: 'CNVD',
            name: 'cnvd',
            minWidth: 120
          },
          {
            label: 'CVSS',
            name: 'cvss_score',
            minWidth: 120
          },
          {
            label: '风险资产数',
            name: 'threat_num',
            minWidth: 100
          },
          {
            label: 'poc来源',
            name: 'poc_from',
            minWidth: 100
          },
          {
            label: '状态',
            name: 'status',
            minWidth: 90
          },
          {
            label: '当前poc是否支持exp验证',
            name: 'has_exp',
            minWidth: 150
          },
          {
            label: '分组名称',
            name: 'group_name'
          },
          {
            label: '更新时间',
            name: 'disclosure_date'
          }
        ],
        second: [
          {
            label: 'PoC分组名称',
            name: 'name',
            fixed: 'left'
          },
          {
            label: 'PoC数量',
            name: 'poc_count'
          },
          {
            label: '风险资产数',
            name: 'threat_num'
          }
        ],
        third: [
          {
            label: '风险名称',
            name: 'name',
            fixed: 'left',
            minWidth: 250
          },
          {
            label: '级别',
            name: 'level',
            minWidth: 100
          },
          {
            label: '漏洞类型',
            name: 'vul_type',
            minWidth: 120
          },

          {
            label: 'CVE编号',
            name: 'cve',
            minWidth: 120
          },
          {
            label: '风险资产数',
            name: 'risk_count',
            minWidth: 100
          },
          {
            label: '状态',
            name: 'state',
            minWidth: 90
          },
          {
            label: '发现日期',
            name: 'disclosure_date',
            minWidth: 120
          },
          {
            label: '更新时间',
            name: 'update_time'
          }
        ]
      },
      user: {
        role: ''
      },
      isdis: true //是否禁用编辑弱口令
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user && this.user.role == 2) {
        if (this.activeName == 'first') {
          this.getPocListData()
        } else if (this.activeName == 'second') {
          this.getPocGroupListData()
        } else {
          this.getPocCustomListData()
        }
      }
    },
    getterWebsocketMessage(msg) {},
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    filteredTableHeader() {
      return this.tableHeader[this.activeName].filter(
        (item) => this.user.role === 1 || item.name !== 'poc_from'
      )
    },
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  async created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    // 获取POC分组
    let groupres = await getPocGroupListNoPage()
    this.pocGroupsNoPageArr = groupres.data
    // 获取POC类型
    let vulTypeList = await pocTypeList()
    this.vulTypeList = Object.values(vulTypeList.data)
  },
  async mounted() {
    if (sessionStorage.getItem('activeTabName')) {
      this.activeName = sessionStorage.getItem('activeTabName')
    }
    if (this.user.role == 2 && !this.currentCompany) return
    if (this.activeName == 'first') {
      this.getPocListData()
    } else if (this.activeName == 'second') {
      this.getPocGroupListData()
    } else {
      this.getPocCustomListData()
    }
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    async exportRA() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      // this.formInline.status = val
      this.exportBtnLoading = true
      let res = await exportRiskAsset({
        id: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            }),
        operate_company_id: this.currentCompany,
        ...this.formInline
      })
      if (res.code == 0) {
        this.download(this.showSrcIp + res.data.url)
        this.$message.success('导出成功！')
      }
      this.exportBtnLoading = false
    },
    // 自定义poc筛选
    async pocCustomFilter() {
      this.highCheckCustomPocdialog = true
    },
    async filterMethod(query, item) {
      return item.name.indexOf(query) > -1
    },

    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleChangeLeft(value, direction) {
      this.$nextTick(() => {
        this.$refs.reserve.addToRight() //直接执行到右事件
      })
    },
    handleChangeRight(value, direction) {
      this.$nextTick(() => {
        this.$refs.reserve.addToLeft() //直接执行到右事件
      })
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // poc高级筛选查询
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getPocListData()
    },
    //自定义poc高级查询
    searchCustomFuncList() {
      this.formCustomPoc.publish = []
      if (this.formCustomPoc.status) {
        this.formCustomPoc.publish.push(this.formCustomPoc.status)
      }

      this.highCheckCustomPocdialog = false
      this.currentPage = 1
      this.getPocCustomListData()
    },
    // 选中项中文名称-用于高级筛选标签
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkGroupFuncList() {
      this.currentPage = 1
      this.getPocGroupListData()
    },
    //自定义poc分组
    checkCustomFuncList() {
      this.currentPage = 1
      this.getPocCustomListData()
    },
    async getPocListData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.highCheckdialog = false
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.loading = true
      getPocList(this.formInline)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
          this.tagsList = res.data.condition.tags
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch(() => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async getPocGroupListData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.formInlineGroup.page = this.currentPage
      this.formInlineGroup.size = this.pageSize
      this.formInlineGroup.operate_company_id = this.currentCompany
      this.loading = true
      getPocGroupListPage(this.formInlineGroup)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
        })
        .catch(() => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    //获取自定义poc表格
    async getPocCustomListData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.formCustomPoc.page = this.currentPage
      this.formCustomPoc.size = this.pageSize
      this.formCustomPoc.operate_company_id = this.currentCompany
      this.loading = true
      this.goUpdatePocRiskIp(false)

      getPocCustomList(this.formCustomPoc)
        .then((res) => {
          console.log('getPocCustomList', res)

          this.pocCustomTypeList = [
            ...new Set(res.data.items.flatMap((item) => item.vul_type).filter((v) => v != ''))
          ]
          res.data.items.forEach((item) => {
            if (!item.level) {
              item.level = 0
            }
            if (item.cve) {
              item.cve = item.cve.join(',')
            }
            if (item.vul_type) {
              item.vul_type = item.vul_type.join(',')
            }
          })
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
        })
        .catch(() => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    async updatePortStatus(val) {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      this.formInline.status = val
      let res = await pocStatusUpdate({
        id: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            }),
        operate_company_id: this.currentCompany,
        ...this.formInline
      })
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.$nextTick(() => {
          this.$refs.eltable.clearSelection()
        })
        this.formInline.status = ''
        this.getPocListData()
      }
    },
    // 点击漏洞名称显示详情
    async clickName(val) {
      this.dialogFormVisibleInfo = true
      this.infoLoading = true
      let res = await infoPoc(val).catch(() => {
        this.infoLoading = false
      })
      this.infoLoading = false
      this.ruleForm = res.data
    },
    // 点击风险资产数量跳转到ip详情页
    async click_threat_num(id) {
      this.$router.push({ path: '/riskAssets', query: { poc_id: id } })
    },
    // 开关
    async changeAttack(val, id) {
      let obj = {
        id: id,
        data: {
          is_require_attack: val,
          operate_company_id: this.currentCompany
        }
      }
      let res = await saveRequireAttack(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
      }
    },
    // 字典列表点击切换
    dictClick(data) {
      if (data.dict_type == 1) {
        this.isdis = true
      } else {
        this.isdis = false
      }
      this.currentDictId = data.id
      this.currentDictName = data.name
      this.currentDictType = data.dict_type
      this.rklData = ''
      this.dictList.forEach((item) => {
        item.isEditDict = false
      }) // isEditDict为编辑字典名称辅助项
      data.dictory_data.forEach((item, index) => {
        let data1 = item.username
          ? item.username + ':' + item.password + '\n'
          : item.password + '\n'
        let data2 = item.username ? item.username + ':' + item.password : item.password
        if (index < data.dictory_data.length - 1) {
          this.rklData += data1
        } else {
          this.rklData += data2
        }
      })
    },
    //更新风险资产ip
    async goUpdatePocRiskIp(isClick) {
      this.UpdatePocRiskBtnLoading = isClick
      const backendTime = new Date(this.last_rist_time.replace(/-/g, '/')).getTime()
      const currentTime = new Date().getTime()
      const oneHour = 60 * 60 * 1000
      if (currentTime - backendTime < oneHour && isClick) {
        this.$message.success('操作成功')
        this.UpdatePocRiskBtnLoading = false
        return // 提前退出，不执行后续逻辑
      }
      let res = await updateRiskAsset({
        operate_company_id: this.currentCompany,
        click: isClick
      }).catch((error) => {
        this.$message.error(error.message || '操作失败')
        this.UpdatePocRiskBtnLoading = false
      })
      console.log('updateTime', res)

      this.UpdatePocRiskBtnLoading = false
      this.last_rist_time = res.data.update_time || ''
      if (isClick) {
        this.$message.success('操作成功！')
      }
    },
    // 保存字段名称
    async saveDictName(dictId, index) {
      let obj = {
        pocId: this.rklEditId,
        dictId: dictId, // 存在未编辑，不存在为新增
        data: {
          name: this.addDictForm.name,
          data: dictId ? this.rklData : '' // dictId存在编辑，不存在是新增弱口令数据为空
        }
      }
      let res = await addEditRkl(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.addDictForm.name = ''
        if (index) {
          this.$set(this.dictList[index], 'isEditDict', false)
        }
        this.isAddDict = false
        this.getDictRklData(this.rklEditId) // 字典名称保存后请求字典列表接口
      }
    },
    // 删除字典
    async delDictItem(id) {
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'poc_del_cancel',
        confirmButtonClass: 'poc_del_sure',
        customClass: 'poc_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await delRkl(id)
          if (res.code == 0) {
            if (id == this.currentDictId) {
              // 删除的id正好是当前选中字典，需要清空
              this.currentDictId = ''
              this.isdis = true
            }
            this.$message.success('操作成功！')
            this.getDictRklData(this.rklEditId)
          }
        })
        .catch(() => {})
    },
    // 编辑字典
    editDictItem(data, i) {
      this.isAddDict = false
      // data.isEditDict = true
      this.addDictForm.name = data.name
      if (data.dict_type == 1) {
        this.isdis = true
      } else {
        this.isdis = false
      }
      this.currentDictId = data.id
      this.currentDictName = data.name
      this.currentDictType = data.dict_type
      this.rklData = ''
      this.dictList.forEach((item, index) => {
        if (index == i) {
          item.isEditDict = true
        } else {
          item.isEditDict = false
        }
      }) // isEditDict为编辑字典名称辅助项
      data.dictory_data.forEach((item, index) => {
        let data1 = item.username
          ? item.username + ':' + item.password + '\n'
          : item.password + '\n'
        let data2 = item.username ? item.username + ':' + item.password : item.password
        if (index < data.dictory_data.length - 1) {
          this.rklData += data1
        } else {
          this.rklData += data2
        }
      })
    },
    // 保存弱口令编辑
    async insertRklSave() {
      // let str = this.rklData.trim()
      // let arr = []
      // str.split("\n").forEach(item => {
      //   arr.push({
      //     username: item.split(':')[0] ? item.split(':')[0] : '',
      //     password: item.split(':')[1] ? item.split(':')[1] : ''
      //   })
      // })
      this.btnLoading = true
      let obj = {
        pocId: this.rklEditId,
        dictId: this.currentDictId, // 存在未编辑，不存在为新增
        data: {
          name: this.currentDictName,
          data: this.currentDictId ? this.rklData : '' // dictId存在编辑，不存在是新增弱口令数据为空
        }
      }
      let res = await addEditRkl(obj).catch(() => {
        this.btnLoading = false
      })
      if (res.code == 0) {
        this.btnLoading = false
        this.$message.success('操作成功！')
        this.addDictForm.name = ''
        this.isAddDict = false
        this.isEditDict = false
        this.getDictRklData(this.rklEditId) // 字典名称保存后请求字典列表接口
      }
    },

    async editOne(val, name) {
      if (this.activeName == 'third') {
        this.pocCustomId = val
        this.$refs.pocCustomManage.$refs.Vulnerability.init(val, this.currentCompany)
        this.showCustomPoc = true
        return
      }
      this.addIsTrue = false
      if (this.activeName == 'first') {
        this.dialogFormVisibleRkl = true
        this.rklData = ''
        this.rklPocName = name
        this.rklEditId = val
        this.getDictRklData(val)
        this.currentDictId = '' //开启弱口令弹框默认选中第一个
        this.isdis = true
      } else {
        this.dialogFormVisibleFenzu = true
        this.diaLoading = true
        // 获取POC列表
        let pocList = await getPocListNoPage({
          keyword: '',
          operate_company_id: this.currentCompany
        })
        if (pocList && pocList.code == 0) {
          this.diaLoading = false
        } else {
          this.diaLoading = false
        }
        this.pocList = pocList.data
        this.pocListCopy = pocList.data
        // 获取详情
        let res = await infoPocGroup({ id: val, operate_company_id: this.currentCompany })
        this.ruleGroupForm = {
          id: res.data.id,
          pocs: res.data.group_pocs,
          name: res.data.name
        }
      }
    },
    async getDictRklData(val) {
      // 获取弱口令详情
      let res = await getRklInfo(val)
      if (res.code == 0) {
        res.data.forEach((item) => {
          item.isEditDict = false
        }) // isEditDict为编辑字典名称辅助项
        this.dictList = res.data
        this.rklData = ''
        if (res.data && res.data.length > 0) {
          if (!this.currentDictId) {
            this.currentDictId = res.data[0].id // 当前选中的字典id
            this.currentDictName = res.data[0].name
            this.currentDictType = res.data[0].dict_type
            res.data[0].dictory_data.forEach((item, index) => {
              let data1 = item.username
                ? item.username + ':' + item.password + '\n'
                : item.password + '\n'
              let data2 = item.username ? item.username + ':' + item.password : item.password
              if (index < res.data[0].dictory_data.length - 1) {
                this.rklData += data1
              } else {
                this.rklData += data2
              }
            })
          } else {
            let dictory_data = res.data.filter((item) => {
              return item.id == this.currentDictId
            })[0].dictory_data
            dictory_data.forEach((item, index) => {
              let data1 = item.username
                ? item.username + ':' + item.password + '\n'
                : item.password + '\n'
              let data2 = item.username ? item.username + ':' + item.password : item.password
              if (index < dictory_data.length - 1) {
                this.rklData += data1
              } else {
                this.rklData += data2
              }
            })
          }
        }
      }
    },
    async removeOne(icon, val) {
      let obj
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
        obj = {
          id: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          operate_company_id: this.currentCompany,
          ...this.formInlineGroup
        }
      } else {
        obj = { ids: [val], operate_company_id: this.currentCompany }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'poc_del_cancel',
        confirmButtonClass: 'poc_del_sure',
        customClass: 'poc_del',
        type: 'warning'
      })
        .then(async () => {
          if (this.activeName == 'second') {
            let res = await delPocGroup(obj)
            if (res.code == 0) {
              this.$message.success('删除成功！')
              this.currentPage = this.updateCurrenPage(
                this.total,
                [1],
                this.currentPage,
                this.pageSize
              ) // 更新页码
              this.getPocGroupListData()
            }
          } else {
            let res = await delPocCustom(obj)
            console.log('delCustom', res)
            if (res.code == 0) {
              this.$message.success('删除成功！')
              this.currentPage = this.updateCurrenPage(
                this.total,
                [1],
                this.currentPage,
                this.pageSize
              ) // 更新页码
              this.getPocCustomListData()
            }
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.poc_del>.el-message-box__btns')
        del.children[0].id = 'poc_del_cancel'
        del.children[1].id = 'poc_del_sure'
      }, 50)
    },
    addPoc() {
      this.pocCustomId = null
      this.showCustomPoc = true
      this.$refs.pocCustomManage.$refs.Vulnerability.init(null)
    },
    // 下线/发布
    batchOnline(state) {
      if (!this.checkedArr.length && !this.checkedAll) {
        this.$message.warning('请选择PoC！')
      } else {
        const that = this
        this.$confirm(state === 1 ? '确定发布所选PoC吗？' : '确定下线所选PoC吗', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let query = {
              // ...that.PocCustomList.filterOptions,
              ids: that.checkedArr.map((item) => {
                return item.id
              }),
              publish: [state],
              operate_company_id: this.currentCompany
            }

            // delete query.class
            releasePocCustom(query)
              .then((res) => {
                that.$message.success('操作成功')
                this.getPocCustomListData()
                that.checkedArr = []
                that.checkedAll = false
              })
              .catch((err) => {
                console.log(err, '0---------')
              })
          })
          .catch(() => {})
      }
    },
    //执行扫描
    handleScan() {
      let ScanPremise = this.checkedArr.map((item) => ({
        risk_count: item.risk_count,
        save_and_publish: item.save_and_publish
      }))
      const shouldProceed = ScanPremise.some(
        (item) => item.risk_count !== 0 && item.save_and_publish === true
      )
      if (!this.checkedArr.length && !this.checkedAll) {
        this.$message.warning('请选择PoC！')
      } else if (!shouldProceed) {
        this.$message.error('没有发布的poc或风险资产数为0,无法执行扫描')
      } else {
        this.ruleScanForm.poc_ids = this.checkedArr.map((item) => {
          return item.poc_id
        })
        this.ruleScanForm.name = `漏洞扫描任务-${this.transferTime(new Date()).replace(/:/g, '')}`
        this.ruleScanForm.operate_company_id = this.currentCompany
        this.ruleScanForm.scan_range = 4
        let obj = Object.assign({}, this.ruleScanForm)
        console.log('漏洞扫描参数', obj)

        this.$confirm(
          '<div>确定扫描所选PoC吗？</div><div style="font-size: 12px; color: #999; margin-top: 4px;">没有发布的poc或风险资产数为0,无法执行扫描</div>',
          '提示',
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          return new Promise((resolve, reject) => {
            addVulnerability(obj)
              .then((res) => {
                console.log('漏洞扫描', res)
                that.checkedArr = []
                that.checkedAll = false

                if (res.code === 0) {
                  this.$message.success(res.data.message || '执行成功')
                  resolve(res)
                }
              })
              .catch((e) => {
                console.log(e)
                reject(e)
              })
              .finally(() => {
                this.checkedArr = []
                this.checkedAll = false
                this.$refs.eltable.clearSelection()
              })
          })
        })
      }
    },
    handleClick() {
      this.highlist = null
      sessionStorage.setItem('activeTabName', this.activeName)
      this.$nextTick(() => {
        this.$refs.eltable.clearSelection()
      })
      this.currentPage = 1
      this.checkedAll = false
      if (this.activeName == 'first') {
        this.getPocListData()
      } else if (this.activeName == 'second') {
        this.getPocGroupListData()
      } else {
        this.formCustomPoc.publish = []
        this.formCustomPoc.vul_type = []
        this.getPocCustomListData()
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      if (this.activeName == 'first') {
        // 参数用于只切换分页时页面不进行选择数据清除
        this.getPocListData(true)
      } else if (this.activeName == 'second') {
        this.getPocGroupListData(true)
      } else {
        this.getPocCustomListData(true)
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      if (this.activeName == 'first') {
        this.getPocListData(true)
      } else if (this.activeName == 'second') {
        this.getPocGroupListData(true)
      } else {
        this.getPocCustomListData(true)
      }
    },
    async insertShow() {
      this.addIsTrue = true
      this.dialogFormVisibleFenzu = true
      this.ruleGroupForm = {
        pocs: [],
        name: ''
      }
      this.$nextTick(() => {
        this.$refs['ruleFormGroup'].clearValidate()
      })
      this.diaLoading = true
      // 获取POC列表
      let pocList = await getPocListNoPage({
        keyword: '',
        operate_company_id: this.currentCompany
      })
      if (pocList && pocList.code == 0) {
        this.diaLoading = false
      } else {
        this.diaLoading = false
      }
      this.pocList = pocList.data
      this.pocListCopy = pocList.data
    },
    async insertSave() {
      this.ruleGroupForm.operate_company_id = this.currentCompany
      this.$refs['ruleFormGroup'].validate(async (valid) => {
        if (valid) {
          let res = null
          this.btnLoading = true
          // 分组内容二次确认
          let pocName = ''
          let pocIds = this.pocList.map((item) => {
            return item.id
          })
          this.ruleGroupForm.pocs.forEach((item) => {
            if (pocIds.indexOf(item) != -1) {
              pocName += this.pocList[pocIds.indexOf(item)].name + '<br/>'
            }
          })
          this.$alert(pocName, '确定按下列poc分组？', {
            showCancelButton: true,
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            beforeClose: async (action, instance, done) => {
              if (action === 'confirm') {
                if (this.addIsTrue) {
                  res = await addPocGroup(this.ruleGroupForm).catch(() => {
                    this.btnLoading = false
                  })
                } else {
                  res = await updatePocGroup(this.ruleGroupForm).catch(() => {
                    this.btnLoading = false
                  })
                }
                done()
                this.btnLoading = false
                if (res.code == 0) {
                  this.getPocGroupListData()
                  this.$message.success('操作成功！')
                  this.dialogFormVisibleFenzu = false
                  this.$nextTick(() => {
                    this.$refs.reserve.clearQuery('left')
                    this.$refs.reserve.clearQuery('right')
                  })
                }
              } else {
                done()
                this.btnLoading = false
              }
            }
          })
        } else {
          return false
        }
      })
    },
    resetForm() {
      ;(this.formInline = {
        rank: '',
        port: ''
      }),
        (this.formCustomPoc = {
          level: [],
          vul_type: [],
          status: ''
        })
    },
    newAdd() {
      this.isAddDict = true
      this.dictList.forEach((item) => {
        item.isEditDict = false
      })
      this.addDictForm.name = ''
    },
    handleSubmit() {
      this.showCustomPoc = true
    },
    closePocCustom() {
      this.$refs.pocCustomManage.$refs.Vulnerability.reset()
      this.showCustomPoc = false
      this.getPocCustomListData()
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;
    .el-form {
      .el-transfer {
        height: 402px !important;
        overflow: auto;
      }
      .el-transfer-panel {
        height: 100% !important;
      }
      /deep/ .el-form-item__content {
        p {
          color: #606266 !important;
          font-size: 14px !important;
        }
      }
    }
    ::v-deep .left {
      flex: 1;
      width: 46%;
      padding-right: 1%;
      & > p {
        text-align: right;
        .el-icon-plus {
          margin-bottom: 10px;
        }
      }
      .addClass {
        margin-bottom: 10px;
        .el-input-group__append {
          padding: 0 24px !important;
        }
        .el-input-group__append .el-button {
          padding: 0 10px !important;
        }
      }
      .editClass {
        .el-input-group__append {
          padding: 0 10px !important;
        }
        .el-input-group__append .el-button {
          padding: 0 10px !important;
        }
      }
      .handlebtnWrap {
        width: 16%;
        display: flex;
        justify-content: space-between;
      }
      .iconClass {
        color: #2677ff;
        font-size: 14px;
        cursor: pointer;
      }
      ul {
        height: 75%;
        overflow: auto;
        li {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 10px;
          cursor: pointer;
          border-left: 4px solid transparent;
          .el-checkbox {
            margin-right: 10px;
          }
        }
        .dictname {
          display: inline-block;
          width: 60%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .activeClass {
          color: #2677ff;
          border-left: 4px solid #2677ff;
          background: #eff2f7;
        }
      }
    }
    .right {
      width: 52%;
      padding-left: 1%;
      border-left: 1px solid #e9ebef;
      .rklClass {
        position: relative;
        /deep/.el-textarea {
          background: #ffffff;
          border-radius: 0;
          border: 1px solid #d1d5dd;
          border-bottom: 0;
          .el-textarea__inner {
            height: 100%;
            border: 0 !important;
          }
          .el-textarea__inner:hover {
            border: 0;
          }
        }
        .rkl_footer {
          // position: absolute;
          // top: 0;
          // left: 0;
          text-align: right;
          padding: 5px;
          background: #fff;
          border: 1px solid #d1d5dd;
          .highBtn {
            width: 50px;
            height: 22px;
          }
        }
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 72px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 224px) !important;
    }
    .tableWrap {
      height: calc(100% - 172px);
      padding: 0px 20px;
      .pocName {
        cursor: pointer;
        color: #2677ff;
        display: flex;
        .ellipsis {
          width: 0;
          flex: 1;
        }
        .tag {
          width: 48px;
          color: #62666c;
        }
      }
    }
    .el-table {
      border: 0;
    }
  }
  .disabled {
    pointer-events: none;
    cursor: default;
  }
}

.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
.radioStyle {
  display: block;
  height: 42px;
  line-height: 42px;
  margin-left: 20px;
}
.drawer-bottom-button {
  margin-left: 20px;
}
.transParent {
  min-width: 63px !important;
  color: #4285f4;
  background-color: #fff !important;
}
</style>
