<template>
  <div class="container" v-loading="loading">
    <div class="headerTitle">{{ activeName == 'first' ? '端口管理' : '端口分组管理' }}</div>
    <div class="home_header">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="端口" name="first">
          <div class="filterTab">
            <div>
              <el-input
                v-model="formInline.keyword"
                @keyup.enter.native="checkFuncList"
                placeholder="请输入端口号进行搜索"
                id="port_keycheck"
              >
                <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
              </el-input>
              <span @click="highCheckdialog = true" id="port_filter" style="width: 80px"
                ><img
                  src="../../assets/images/filter.png"
                  alt=""
                  style="width: 16px; vertical-align: middle; margin-right: 3px"
                />高级筛选</span
              >
            </div>
            <div>
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAll"
                @change="checkAllChange"
                id="port_all"
                >选择全部</el-checkbox
              >
              <el-button
                class="normalBtnRe"
                type="primary"
                @click="updatePortStatus(1)"
                id="port_disable"
                >禁用</el-button
              >
              <el-button
                class="normalBtn"
                type="primary"
                @click="updatePortStatus(0)"
                id="port_open"
                >启用</el-button
              >
              <el-button class="normalBtn" type="primary" @click="insertShow" id="port_add"
                >新建端口</el-button
              >
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="分组" name="second">
          <div class="filterTab">
            <div>
              <el-input
                v-model="formInlineGroup.keyword"
                @keyup.enter.native="checkGroupFuncList"
                placeholder="请输入分组名称进行搜索"
                id="port_keycheck"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="checkGroupFuncList"
                ></el-button>
              </el-input>
            </div>
            <div>
              <el-checkbox
                class="checkboxAll"
                v-model="checkedAll"
                @change="checkAllChange"
                id="port_all"
                >选择全部</el-checkbox
              >
              <el-button class="normalBtn" type="primary" @click="removeOne('more')" id="port_del"
                >删除
                <el-tooltip
                  class="itemTooltip"
                  effect="dark"
                  content="勾选的内置端口后台会自动过滤，不会被删除"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="color: #b1bdd1; margin: 1px 0px 0px 4px; font-size: 16px"
                  ></i>
                </el-tooltip>
              </el-button>
              <el-button class="normalBtn" type="primary" @click="insertShow" id="port_add"
                >新建分组</el-button
              >
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader[activeName]"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span class="greenLine" v-if="item.name == 'status' && scope.row[item.name] == '0'"
                >启用</span
              >
              <span class="redLine" v-else-if="item.name == 'status' && scope.row[item.name] != '0'"
                >禁用</span
              >
              <span
                class="blueRadiusBorder"
                v-else-if="item.name == 'source' && scope.row[item.name] == 0"
                >系统预置</span
              >
              <span
                class="greenRadiusBorder"
                v-else-if="item.name == 'source' && scope.row[item.name] == 1"
                >自定义</span
              >
              <span v-else-if="item.name == 'portlist'">{{
                scope.row[item.name] && scope.row[item.name].length > 0
                  ? scope.row[item.name].join('；')
                  : '-'
              }}</span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="left" fixed="right" width="120">
            <template slot-scope="scope">
              <!-- 系统预置：支持编辑、不支持删除 -->
              <span>
                <el-button
                  :disabled="activeName == 'second' && scope.row['port'] == '0-65535'"
                  type="text"
                  size="small"
                  @click="editOne(scope.row.id)"
                  id="port_edit"
                  >编辑</el-button
                >
                <el-button
                  v-if="activeName == 'first' && scope.row['source'] && scope.row['source'] == 1"
                  type="text"
                  size="small"
                  @click="removeOne(scope.row.id)"
                  id="port_del"
                  >删除</el-button
                >
                <el-button
                  v-if="activeName == 'second' && scope.row['can_del'] == 1"
                  type="text"
                  size="small"
                  @click="removeOne(scope.row.id)"
                  id="port_del"
                  >删除</el-button
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="560px"
    >
      <template slot="title">
        {{ !addIsTrue ? '编辑端口' : '新建端口' }}
      </template>
      <div class="dialog-body">
        <!-- <div class="left"> -->
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          style="padding: 0 !important"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="端口号" prop="port">
            <el-input-number
              v-if="!addIsTrue"
              v-model="ruleForm.port"
              :min="0"
              :max="65535"
            ></el-input-number>
            <!-- 或端口号范围;端口号范围支持格式：80-89 -->
            <el-input
              v-if="addIsTrue"
              type="textarea"
              :rows="3"
              v-model="ruleForm.port"
              placeholder="请输入端口号,多个值请使用分号或换行分隔，单次最多可以添加500个端口号
"
            ></el-input>
          </el-form-item>
          <el-form-item label="分组" prop="groups">
            <el-select
              filterable
              multiple
              collapse-tags
              v-model="ruleForm.groups"
              placeholder="请选择分组"
            >
              <el-option
                v-for="item in portGroupsNoPageArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="协议" prop="protocols">
            <new-transfer
              :titles="['未选择', '已选择']"
              filterable
              :filter-method="filterMethod"
              filter-placeholder="请输入关键字"
              v-model="ruleForm.protocols"
              :props="transferProp"
              ref="reserve"
              @left-check-change="handleChangeLeft"
              @right-check-change="handleChangeRight"
              :data="transferData"
            >
            </new-transfer>
            <span>最多可添加5个协议</span>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisible = false" id="port_add_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="insertSave" id="port_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleFenzu"
      width="560px"
    >
      <template slot="title">
        {{ !addIsTrue ? '编辑分组' : '新建分组' }}
      </template>
      <div class="dialog-body">
        <el-form
          :model="ruleGroupForm"
          :rules="rulesGroup"
          ref="ruleFormGroup"
          style="padding: 0 !important"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="分组名称" prop="name">
            <el-input v-model="ruleGroupForm.name" placeholder="请输入分组名称"></el-input>
          </el-form-item>
          <el-form-item label="端口" prop="">
            <new-transfer
              :titles="['未选择', '已选择']"
              style="height: 350px"
              filterable
              :filter-method="filterMethodGroup"
              filter-placeholder="请输入关键字"
              v-model="ruleGroupForm.ports"
              :props="transferPropGroup"
              ref="reserve"
              @left-check-change="handleChangeLeft"
              @right-check-change="handleChangeRight"
              :data="group_portlist"
            >
            </new-transfer>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleFenzu = false" id="port_add_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" :loading="btnLoading" @click="inserGrouptSave" id="port_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="协议：" prop="rank">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.protocol_id"
              @change="selectChange($event, 'protocol_id', transferData, true, true)"
              placeholder="请选择协议名称"
            >
              <el-option
                v-for="item in transferData"
                :key="item.id"
                :label="item.protocol"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="分组：" prop="port_group_id">
            <el-select
              filterable
              multiple
              collapse-tags
              clearable
              v-model="formInline.port_group_id"
              @change="selectChange($event, 'port_group_id', portGroupsNoPageArr, true, true)"
              placeholder="请选择端口分组"
            >
              <el-option
                v-for="item in portGroupsNoPageArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="添加方式：" prop="source">
            <el-select
              filterable
              clearable
              v-model="formInline.source"
              @change="selectChange($event, 'source', sourceArr, true, false)"
              placeholder="请选择添加方式"
            >
              <el-option
                v-for="item in sourceArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：" prop="status">
            <el-select
              filterable
              clearable
              v-model="formInline.status"
              @change="selectChange($event, 'status', statusArr, true, false)"
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in statusArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="添加时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="port_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="port_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import newTransfer from '../../components/transfer/src/main'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  getPortGroups,
  infoPortGroups,
  editPortGroups,
  delPortGroups,
  addPortGroups,
  portGroupsPage,
  portGroupsNoPage,
  portUpdateStatus,
  editPort,
  infoPort,
  delPort,
  addPort,
  getPortProList,
  getPortList
} from '@/api/apiConfig/port.js'

export default {
  components: {
    newTransfer,
    tableTooltip,
    hightFilter
  },

  data() {
    return {
      highTabShow: [
        {
          label: '协议',
          name: 'protocol_id',
          type: 'select'
        },
        {
          label: '分组',
          name: 'port_group_id',
          type: 'select'
        },
        {
          label: '添加方式',
          name: 'source',
          type: 'select'
        },
        {
          label: '状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '添加时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      btnLoading: false,
      checkedAll: false, // 是否全选
      formInline: {
        keyword: '',
        protocol_id: [],
        port_group_id: [],
        page: 1,
        size: 10
      },
      sourceArr: [
        {
          id: 0,
          name: '系统预置'
        },
        {
          id: 1,
          name: '自定义'
        }
      ],
      statusArr: [
        {
          id: '1',
          name: '禁用'
        },
        {
          id: '0',
          name: '启用'
        }
      ],
      formInlineGroup: {
        keyword: '',
        page: 1,
        size: 10
      },
      pageSizeArr: [10, 30, 50, 100],
      pageSize: 10,
      total: 0,
      currentPage: 1,
      transferData: [],
      group_portlist: [],
      transferProp: {
        key: 'id',
        label: 'protocol'
      },
      transferPropGroup: {
        key: 'id',
        label: 'port'
      },
      loading: false,
      filterMethod(query, item) {
        return item.protocol.indexOf(query) > -1
      },
      filterMethodGroup(query, item) {
        return item.port.indexOf(query) > -1
      },
      rules: {
        port: [
          { required: true, message: '请输入端口', trigger: 'change' },
          {
            required: true,
            message: '请输入正确的端口格式',
            pattern: /^[0-9,\-_ \.；;\r\n]+$/u,
            trigger: 'blur'
          }
        ],
        protocols: [{ required: true, message: '请选择协议', trigger: 'change' }],
        groups: [{ required: true, message: '请选择分组', trigger: 'change' }]
      },
      rulesGroup: {
        name: [{ required: true, message: '请输入端口分组名称', trigger: 'change' }],
        ports: [{ required: true, message: '请选择端口', trigger: 'change' }]
      },
      ruleForm: {
        port: '',
        protocols: [],
        groups: ''
      },
      ruleGroupForm: {
        ports: [],
        name: ''
      },
      portGroupsNoPageArr: [],
      checkedArr: [],
      highCheckdialog: false,
      dialogFormVisible: false,
      dialogFormVisibleFenzu: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      activeName: 'first',
      addIsTrue: true, // 判断新增或编辑
      tableData: [],
      tableHeader: {
        first: [
          {
            label: '端口号',
            name: 'port',
            fixed: 'left',
            minWidth: 70
          },
          {
            label: '协议',
            name: 'protocols',
            minWidth: 200
          },
          {
            label: '分组',
            name: 'groups',
            minWidth: 180
          },
          {
            label: '添加方式',
            name: 'source', //: "添加方式 0系统预置 1自定义添加",
            minWidth: 80
          },
          {
            label: '状态',
            name: 'status', // 0正常 1禁用
            minWidth: 80
          },
          {
            label: '添加时间',
            name: 'created_at'
          }
        ],
        second: [
          {
            label: '端口分组名称',
            name: 'port',
            fixed: 'left',
            minWidth: 120
          },
          {
            label: '包含端口数量',
            name: 'count_ports'
          },
          {
            label: '包含端口',
            name: 'portlist',
            minWidth: 300
          }
        ]
      },
      user: null
    }
  },
  watch: {
    async getterCurrentCompany(val) {
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      this.currentPage = 1
      if (this.user && this.user.role == 2) {
        if (this.activeName == 'first') {
          this.getPortListData()
          // 获取端口分组
          let groupres = await portGroupsNoPage({ operate_company_id: this.currentCompany }).catch(
            () => {
              this.portGroupsNoPageArr = []
            }
          )
          this.portGroupsNoPageArr = groupres.data.filter((item) => {
            return item.name != '0-65535'
          })
        } else {
          this.getPortGroupListData()
          // 获取新建端口分组的端口列表
          let group_portlist = await getPortGroups({
            operate_company_id: this.currentCompany
          }).catch(() => {
            this.group_portlist = []
          })
          let arr = []
          group_portlist.data.forEach((item) => {
            arr.push({
              id: item.id,
              port: String(item.port)
            })
          })
          this.group_portlist = arr
        }
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  async created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    // 获取端口分组
    let groupres = await portGroupsNoPage({ operate_company_id: this.currentCompany })
    this.portGroupsNoPageArr = groupres.data.filter((item) => {
      return item.name != '0-65535'
    })
    // 获取协议列表
    let prores = await getPortProList()
    this.transferData = prores.data
    // 获取新建端口分组的端口列表
    let group_portlist = await getPortGroups({ operate_company_id: this.currentCompany })
    let arr = []
    group_portlist.data.forEach((item) => {
      arr.push({
        id: item.id,
        port: String(item.port)
      })
    })
    this.group_portlist = arr
  },
  async mounted() {
    if (sessionStorage.getItem('activeTabName')) {
      this.activeName = sessionStorage.getItem('activeTabName')
      if (this.activeName == 'first') {
        this.getPortListData()
        // 获取端口分组
        let groupres = await portGroupsNoPage({ operate_company_id: this.currentCompany })
        this.portGroupsNoPageArr = groupres.data.filter((item) => {
          return item.name != '0-65535'
        })
      } else {
        this.getPortGroupListData()
        // 获取新建端口分组的端口列表
        let group_portlist = await getPortGroups({ operate_company_id: this.currentCompany })
        let arr = []
        group_portlist.data.forEach((item) => {
          arr.push({
            id: item.id,
            port: String(item.port)
          })
        })
        this.group_portlist = arr
      }
    } else {
      this.getPortListData()
    }
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
  },
  beforeDestroy() {
    this.highlist = null // 清空高级筛选标签内容
    sessionStorage.setItem('activeTabName', '')
  },
  methods: {
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleChangeLeft(value, direction) {
      this.$nextTick(() => {
        this.$refs.reserve.addToRight() //直接执行到右事件
      })
    },
    handleChangeRight(value, direction) {
      this.$nextTick(() => {
        this.$refs.reserve.addToLeft() //直接执行到右事件
      })
    },
    checkGroupFuncList() {
      this.currentPage = 1
      this.getPortGroupListData()
    },
    async getPortListData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }

      this.formInline.page = this.currentPage
      this.formInline.size = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.loading = true
      getPortList(this.formInline)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    async getPortGroupListData(tmp) {
      // 调用之前清空选择项
      if (!tmp) {
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }

      this.formInlineGroup.page = this.currentPage
      this.formInlineGroup.size = this.pageSize
      this.formInlineGroup.operate_company_id = this.currentCompany
      this.loading = true
      portGroupsPage(this.formInlineGroup)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
        })
        .catch(() => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async editOne(val) {
      this.addIsTrue = false
      if (this.activeName == 'first') {
        this.dialogFormVisible = true
        // 获取详情
        let res = await infoPort({ id: val, operate_company_id: this.currentCompany })
        this.ruleForm = {
          id: res.data.id,
          port: res.data.port,
          protocols: res.data.protocols.map((item) => {
            return item.id
          }),
          groups: res.data.groups
        }
      } else {
        this.dialogFormVisibleFenzu = true
        // 获取详情
        let res = await infoPortGroups({ id: val, operate_company_id: this.currentCompany })
        this.ruleGroupForm = {
          id: res.data.id,
          ports: res.data.ports.map((item) => {
            return item.key
          }),
          name: res.data.name
        }
      }
    },
    async removeOne(val) {
      let obj
      // 删除分组
      if (this.activeName !== 'first') {
        if (val == 'more') {
          if (this.checkedArr.length == 0) {
            this.$message.error('请选择要删除的数据！')
            return
          }
          obj = {
            id: this.checkedAll
              ? []
              : this.checkedArr.map((item) => {
                  return item.id
                }),
            operate_company_id: this.currentCompany,
            ...this.formInlineGroup
          }
        } else {
          obj = { id: [val], operate_company_id: this.currentCompany }
        }
      }

      this.$confirm('确定删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'port_del_cancel',
        confirmButtonClass: 'port_del_sure',
        customClass: 'port_del',
        type: 'warning'
      })
        .then(async () => {
          if (this.activeName == 'first') {
            let res = await delPort({ ids: [val], operate_company_id: this.currentCompany })
            if (res.code == 0) {
              this.$message.success('删除成功！')
              this.$nextTick(() => {
                this.$refs.eltable.clearSelection()
              })
              this.currentPage = this.updateCurrenPage(
                this.total,
                [1],
                this.currentPage,
                this.pageSize
              ) // 更新页码
              this.getPortListData()
            }
          } else {
            let res = await delPortGroups(obj)
            if (res.code == 0) {
              this.$message.success('删除成功！')
              this.$nextTick(() => {
                this.$refs.eltable.clearSelection()
              })
              this.currentPage = this.updateCurrenPage(
                this.total,
                [1],
                this.currentPage,
                this.pageSize
              ) // 更新页码
              this.getPortGroupListData()
            }
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.port_del>.el-message-box__btns')
        del.children[0].id = 'port_del_cancel'
        del.children[1].id = 'port_del_sure'
      }, 50)
    },
    async updatePortStatus(val) {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据！')
        return
      }
      let res = await portUpdateStatus({
        ids: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            }),
        status: val,
        operate_company_id: this.currentCompany,
        ...this.formInline
      })
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.$nextTick(() => {
          this.$refs.eltable.clearSelection()
        })
        this.getPortListData()
      }
    },
    async handleClick(val) {
      this.highlist = null // 清空高级筛选标签内容
      this.formInline = {
        keyword: '',
        protocol_id: [],
        port_group_id: [],
        page: 1,
        size: 10
      }
      sessionStorage.setItem('activeTabName', this.activeName)
      this.$nextTick(() => {
        this.$refs.eltable.clearSelection()
      })
      this.currentPage = 1
      this.checkedAll = false
      if (this.activeName == 'first') {
        this.getPortListData()
        // 获取端口分组
        let groupres = await portGroupsNoPage({ operate_company_id: this.currentCompany })
        this.portGroupsNoPageArr = groupres.data.filter((item) => {
          return item.name != '0-65535'
        })
      } else {
        this.getPortGroupListData()
        // 获取新建端口分组的端口列表
        let group_portlist = await getPortGroups({ operate_company_id: this.currentCompany })
        let arr = []
        group_portlist.data.forEach((item) => {
          arr.push({
            id: item.id,
            port: String(item.port)
          })
        })
        this.group_portlist = arr
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      if (this.activeName == 'first') {
        this.getPortListData(true)
      } else {
        this.getPortGroupListData(true)
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      if (this.activeName == 'first') {
        this.getPortListData(true)
      } else {
        this.getPortGroupListData(true)
      }
    },
    checkFuncList() {
      this.currentPage = 1
      this.highCheckdialog = false
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow() // 每查询一次就改变高度
      this.getPortListData()
    },
    highCheck(val) {
      if (val) {
        this.formInline = val
        this.checkFuncList()
      }
    },
    // 选中项中文名称-用于高级筛选标签
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id) {
                this.formInline['ch_' + name].push(ar.name || ar.protocol)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    async insertShow() {
      this.addIsTrue = true
      if (this.activeName == 'first') {
        this.ruleForm = {
          port: '',
          protocols: [],
          groups: ''
        }
        this.$forceUpdate()
        this.$nextTick(() => {
          this.$refs['ruleForm'].clearValidate()
          this.$refs.reserve.clearQuery('left')
          this.$refs.reserve.clearQuery('right')
        })
        // 默认显示全部常用端口
        this.portGroupsNoPageArr.forEach((item) => {
          if (item.name == '全部常用端口') {
            this.ruleForm.groups = [item.id]
          }
        })
        this.dialogFormVisible = true
      } else {
        this.ruleGroupForm = {
          ports: [],
          name: ''
        }
        this.$nextTick(() => {
          this.$refs['ruleFormGroup'].clearValidate()
        })
        this.dialogFormVisibleFenzu = true
      }
    },
    async insertSave() {
      this.$refs['ruleForm'].validate(async (valid) => {
        if (valid) {
          if (this.ruleForm.protocols.length > 5) {
            this.$message.error('最多可添加5个协议！')
            return
          }
          // 端口范围判断
          let ports = []
          if (this.addIsTrue) {
            ports = this.ruleForm.port
              .split(/[；|;|\r\n]/)
              .filter((item) => {
                return item.trim()
              })
              .map((item) => {
                return item.trim()
              })
          } else {
            ports = [this.ruleForm.port]
          }
          let portsIsFalse = []
          ports.some((item) => {
            if (1 <= 1 * item && 1 * item <= 65535) {
            } else {
              portsIsFalse.push(1)
            }
          })
          if (portsIsFalse.indexOf(1) != -1) {
            this.$message.error('请填写正确的端口号格式；端口范围0-65535，多个用逗号隔开')
            return
          }
          let obj = {
            port_groups: this.ruleForm.groups,
            protocols: this.ruleForm.protocols,
            ports: ports, // 新增支持多个，只能编辑一个端口
            operate_company_id: this.currentCompany
          }
          this.btnLoading = true
          let res = null
          if (this.ruleForm.id) {
            res = await editPort({ id: this.ruleForm.id, data: obj }).catch(() => {
              this.btnLoading = false
            })
          } else {
            res = await addPort(obj).catch(() => {
              this.btnLoading = false
            })
          }
          if (res.code == 0) {
            this.btnLoading = false
            this.getPortListData()
            this.$message.success('操作成功！')
            this.dialogFormVisible = false
            this.$nextTick(() => {
              this.$refs.reserve.clearQuery('left')
              this.$refs.reserve.clearQuery('right')
            })
          }
        } else {
          return false
        }
      })
    },
    async inserGrouptSave() {
      this.$refs['ruleFormGroup'].validate(async (valid) => {
        if (valid) {
          let obj = {
            name: this.ruleGroupForm.name,
            ports: this.ruleGroupForm.ports,
            operate_company_id: this.currentCompany
          }
          let res = null
          this.btnLoading = true
          if (this.ruleGroupForm.id) {
            res = await editPortGroups({ id: this.ruleGroupForm.id, data: obj }).catch(() => {
              this.btnLoading = false
            })
          } else {
            res = await addPortGroups(obj).catch(() => {
              this.btnLoading = false
            })
          }
          this.btnLoading = false
          if (res.code == 0) {
            this.getPortGroupListData()
            this.$message.success('操作成功！')
            this.dialogFormVisibleFenzu = false
            this.$nextTick(() => {
              this.$refs.reserve.clearQuery('left')
              this.$refs.reserve.clearQuery('right')
            })
          }
        } else {
          return false
        }
      })
    },
    resetForm() {
      this.formInline = {
        keyword: '',
        protocol_id: [],
        port_group_id: [],
        page: 1,
        size: 10
      }
      this.getPortListData()
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    height: 100%;
    .el-form {
      .el-transfer {
        height: 299px !important;
        overflow: auto;
      }
      .el-transfer-panel {
        height: 100% !important;
        overflow: auto;
      }
    }
    .left {
      height: 100%;
      display: flex;
      /deep/.el-textarea {
        width: 300px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }

    .right {
      width: 50%;
      margin-bottom: 24px;
      margin-left: 20px;
      border-left: 1px solid #e9ebef;
      /deep/.el-textarea {
        width: 300px;
        height: 472px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 224px) !important;
    }
    .tableWrap {
      height: calc(100% - 172px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}

.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
