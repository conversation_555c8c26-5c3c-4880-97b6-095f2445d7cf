<template>
  <div class="container">
    <div class="headerTitle">规则列表</div>
    <div class="home_header">
      <div class="filterTab">
        <div></div>
        <div>
          <el-button
            class="normalBtn"
            type="primary"
            @click="
              () => {
                dialogFormVisible = true
                dialogTitle = '新增规则'
              }
            "
            >新建</el-button
          >
        </div>
      </div>
      <div class="tableWrap">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          ref="eltable"
          height="100%"
          style="width: 100%"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
        >
          <el-table-column align="center" prop="id" label="ID"> </el-table-column>
          <el-table-column align="center" prop="second_category_name" label="分类">
          </el-table-column>
          <el-table-column align="center" prop="product" label="规则名称"> </el-table-column>
          <el-table-column align="center" prop="rule" label="规则内容"> </el-table-column>
          <el-table-column align="center" label="相关操作">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="editOne(scope.row)" id="user_edit"
                >编辑</el-button
              >
              <el-button type="text" size="small" @click="delOne(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 添加以及删除等相关操作弹窗 -->
    <el-dialog
      class="elDialogAdd"
      :visible.sync="dialogFormVisible"
      @close="closeOperation"
      :close-on-click-modal="false"
    >
      <template slot="title">
        {{ dialogTitle }}
      </template>
      <div class="dialog-body">
        <el-form :model="ruleForm" :rules="rules" ref="submitForm" label-width="100px">
          <el-form-item class="treeWrap" label="分类" prop="second_category_name">
            <div v-if="!selectBtn" class="treeWrap">
              <el-popover style="width: 100%" placement="bottom-start" trigger="focus">
                <el-input
                  :disabled="selectBtn"
                  readonly
                  style="font-size: 16px; font-weight: bold; color: #8c939d"
                  placeholder="请选择分类"
                  v-model="ruleForm.second_category_name"
                  slot="reference"
                ></el-input>
                <div style="width: 300px; height: 200px; overflow: auto">
                  <el-tree
                    :data="data"
                    ref="tree"
                    :props="defaultProps"
                    node-key="id"
                    @node-click="handleNodeClick"
                  >
                  </el-tree>
                </div>
              </el-popover>
            </div>
            <div v-else>
              <el-input
                disabled
                v-model="ruleForm.second_category_name"
                style="width: 100%; font-size: 16px; font-weight: bold"
                placeholder="请选择分类"
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item label="规则名称" prop="product">
            <el-input v-model="ruleForm.product" placeholder="请填写规则名称"></el-input>
          </el-form-item>
          <el-form-item label="规则内容" prop="rule">
            <el-input
              type="textarea"
              v-model="ruleForm.rule"
              placeholder='header="Set-Cookie: phpMyAdmin=" || title="phpMyAdmin || body="pma_password"'
            ></el-input>
          </el-form-item>
          <el-form-item label="厂商名称" prop="company">
            <el-input v-model="ruleForm.company" placeholder="请填写厂商名称"></el-input>
          </el-form-item>
          <el-form-item label="应用网站" prop="producturl">
            <el-input v-model="ruleForm.producturl" placeholder="请填写应用网站"></el-input>
          </el-form-item>
          <el-form-item label="选择分层" prop="level_code">
            <el-select v-model="ruleForm.level_code" placeholder="请选择分层">
              <el-option
                :label="item.label"
                :value="item.id"
                v-for="item in ruleTitle"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtn" @click="closeOperation">取消</el-button>
        <el-button :loading="btnLoading" class="highBtn" @click="ruleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
  </div>
</template>

<script>
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import { ruleList, ruleType, ruleAdd, ruleEdit, ruleDel } from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip },
  data() {
    return {
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      dialogTitle: '',
      rules: {
        second_category_name: [
          { required: true, message: '请选择分类', trigger: ['blur', 'change'] }
        ],
        product: [{ required: true, message: '请填写规则名称', trigger: 'blur' }],
        rule: [{ required: true, message: '请填写规则内容', trigger: 'blur' }],
        company: [{ required: true, message: '请填写厂商名称', trigger: 'blur' }],
        producturl: [
          { required: true, message: '请填写应用网站', trigger: 'blur' },
          { pattern: /^http[s]{0,1}:\/\/([\w.]+\/?)\S*/, message: '网站格式请以http或者https开头' }
        ],
        level_code: [{ required: true, message: '请选择分层', trigger: 'blur' }]
      },
      selectBtn: false,
      btnLoading: false,
      ruleForm: {
        second_category_name: '',
        second_category_id: '',
        product: '',
        rule: '',
        company: '',
        producturl: '',
        level_code: ''
      },
      tableData: [
        {
          second_category_id: '111',
          product: '121245',
          ruleName: '4545',
          rule: '1212'
        }
      ],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogFormVisible: false,
      data: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      ruleTitle: [
        {
          id: 5,
          label: '业务层',
          name: 'rule5'
        },
        {
          id: 4,
          label: '支撑层',
          name: 'rule4'
        },
        {
          id: 3,
          label: '服务层',
          name: 'rule3'
        },
        {
          id: 2,
          label: '系统层',
          name: 'rule2'
        },
        {
          id: 1,
          label: '硬件层',
          name: 'rule1'
        }
      ]
    }
  },
  mounted() {
    this.getData()
    this.getType()
  },
  methods: {
    closeOperation() {
      this.ruleForm = {
        id: '',
        second_category_name: '',
        second_category_id: '',
        product: '',
        rule: '',
        company: '',
        producturl: '',
        level_code: ''
      }
      this.dialogFormVisible = false
      this.$refs.submitForm.resetFields()
    },
    delOne(row) {
      this.$confirm('此操作不可恢复，是否继续', '提示', {
        showCancelButton: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            let res = await ruleDel({ id: row.id + '' })
            done()
            if (res.code == 0) {
              this.getData()
              this.$message.success('操作成功！')
            }
          } else {
            done()
          }
        }
      })
      //   .then(() => {
      //   let res = await ruleDel(this.ruleGroupForm)

      // }).catch()
    },
    editOne(row) {
      this.dialogTitle = '编辑规则'
      // this.ruleForm = JSON.parse(JSON.stringify(row))
      this.ruleForm.id = row.id
      this.ruleForm.second_category_name = row.second_category_name
      this.ruleForm.second_category_id = row.second_category_id
      this.ruleForm.product = row.product
      this.ruleForm.rule = row.rule
      this.ruleForm.company = row.company
      this.ruleForm.producturl = row.producturl
      this.ruleForm.level_code = row.level_code
      this.dialogFormVisible = true
    },
    handleNodeClick(data) {
      if (data.children) return
      this.ruleForm.second_category_id = data.id
      this.ruleForm.second_category_name = data.title
    },
    async getType() {
      let res = await ruleType()
      if (res.code == 0) {
        this.data = res.data
      }
    },
    async getData() {
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize
      }
      this.loading = true
      let res = await ruleList(obj).catch(() => {
        this.tableData = []
        this.total = 0
        this.loading = false
      })
      this.loading = false
      this.tableData = res.data.items
      this.total = res.data.total
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    ruleSubmit() {
      this.$refs.submitForm.validate(async (valid) => {
        if (valid) {
          const { second_category_id, level_code, id } = this.ruleForm
          let funcName = this.ruleForm.id ? ruleEdit : ruleAdd
          let obj = {
            ...this.ruleForm,
            second_category_id: second_category_id + '',
            level_code: level_code + '',
            id: id + ''
          }
          let res = await funcName(obj)
          if (res.code == 0) {
            this.$message.success('操作成功')
            this.dialogFormVisible = false
            this.getData()
          }
        } else {
          return false
        }
      })
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    height: 100%;
    .el-form {
      /deep/ .el-form-item__content {
        p,
        span {
          color: #606266 !important;
          font-size: 14px !important;
        }
        p span {
          color: #606266 !important;
          font-size: 14px !important;
        }
      }
    }
  }
}

/deep/.el-tree-node > .el-tree-node__content {
  height: 36px;
}
.tableWrap {
  margin-top: 20px;
  height: calc(100% - 129px);
  padding: 0px 20px;
}
/deep/.home_header {
  padding-top: 10px;
  position: relative;
  height: 100%;
}
</style>
