<template>
  <div class="container">
    <div class="headerTitle">售后菜单管理</div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="keyword_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-button class="normalBtn" type="primary" @click="addEdit()">新建</el-button>
        </div>
      </div>
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="user"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'status'">
                <span class="greenLine" v-if="scope.row[item.name] == 1">启用</span>
                <span class="redLine" v-else-if="scope.row[item.name] == 0">禁用</span>
              </span>
              <span v-else-if="item.name == 'scope'">
                <span>{{ getScopeName(scope.row.scope) }}</span>
              </span>
              <span v-else-if="item.name == 'account_type'">
                <span>{{ accountTypeMap[scope.row.account_type] }}</span>
              </span>
              <span v-else-if="item.name == 'local_arch' && scope.row.local_arch">
                <span>{{ publicMapData[scope.row.local_arch] }}</span>
              </span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="150">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="editOne(scope.row)" id="keyword_del"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="650px"
      v-if="dialogFormVisibleInsert"
    >
      <template slot="title">
        {{ ruleForm.id ? '编辑' : '新建' }}
      </template>
      <div class="dialog-body">
        <el-form
          :model="ruleForm"
          :rules="rules"
          style="padding: 0 !important"
          ref="ruleForm"
          label-width="170px"
          class="demo-ruleForm"
        >
          <el-form-item label="用户类型" prop="account_type">
            <!-- <el-radio-group v-model="ruleForm.account_type" @change="account_type_change">
                <el-radio :label="0">默认用户</el-radio>
                <el-radio :label="1">中航金网用户</el-radio>
              </el-radio-group> -->
            <el-select v-model="ruleForm.account_type" placeholder="请选择日期" class="type">
              <el-option
                v-for="item in accountTypeData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- 新建不需要 -->
          <el-form-item v-if="!ruleForm.id" label="SAAS_MICRO_CLIENT" prop="client_id">
            <el-input v-model="ruleForm.client_id" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="客户端状态" prop="status">
            <el-switch
              v-model="ruleForm.status"
              active-text="开启"
              inactive-text="禁用"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </el-form-item>
          <el-form-item label="scope" prop="scope">
            <el-select
              filterable
              multiple
              collapse-tags
              v-model="ruleForm.scope"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(v, k, index) in scopeArr"
                :key="index"
                :label="v"
                :value="k"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="SAAS_MICRO_SECRET" prop="secret">
            <el-input v-model="ruleForm.secret" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="企业名称" prop="company_name">
            <el-input v-model="ruleForm.company_name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="FR点" prop="api_quota_remaining">
            <el-input v-model="ruleForm.api_quota_remaining" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="过期时间" prop="expired_at">
            <el-date-picker
              v-model="ruleForm.expired_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="设备ID" prop="desc">
            <el-input v-model="ruleForm.desc" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="服务器架构" prop="local_arch">
            <el-select v-model="ruleForm.local_arch" placeholder="请选择" clearable>
              <el-option label="amd架构" :value="1"></el-option>
              <el-option label="arm架构" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="limitClass"
            v-if="ruleForm.account_type == 1"
            label="单次查询企业个数限制"
            prop="per_cname_limit"
          >
            <el-input-number
              v-model="ruleForm.per_cname_limit"
              :min="0"
              label="请输入"
            ></el-input-number>
          </el-form-item>
          <el-form-item
            class="limitClass"
            v-if="ruleForm.account_type == 1"
            label="单日企业资产数限制"
            prop="day_assets_limit"
          >
            <el-input-number
              v-model="ruleForm.day_assets_limit"
              :min="0"
              label="请输入"
            ></el-input-number>
          </el-form-item>
          <el-form-item
            class="limitClass"
            v-if="ruleForm.account_type == 1"
            label="月企业查询次数限制"
            prop="mon_detect_limit"
          >
            <el-input-number
              v-model="ruleForm.mon_detect_limit"
              :min="0"
              label="请输入"
            ></el-input-number>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="otherLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="客户端状态：" prop="status">
            <el-select
              v-model="formInline.status"
              clearable
              placeholder="请选择客户端状态"
              @change="selectChange($event, 'status', statusArr, true, false)"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in statusArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户类型：" prop="account_type">
            <el-select
              v-model="formInline.account_type"
              clearable
              placeholder="请选择用户类型"
              collapse-tags
              @change="selectChange($event, 'account_type', accountTypeArr, true, false)"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in accountTypeArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="scope：" prop="scope">
            <el-select
              v-model="formInline.scope"
              clearable
              placeholder="请选择scope"
              collapse-tags
              @change="selectChange($event, 'scope', scopeFilterArr, true, false)"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in scopeFilterArr"
                :key="v.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="更新时间：" prop="updated_at_range">
            <el-date-picker
              v-model="formInline.updated_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="过期时间：" prop="expired_at_range">
            <el-date-picker
              v-model="formInline.expired_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  golang_client_account,
  add_golang_client_account,
  scope_golang_client_account
} from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      highlist: null,
      highTabShow: [
        {
          label: '客户端状态',
          name: 'status',
          type: 'select'
        },

        {
          label: '用户类型',
          name: 'account_type',
          type: 'select'
        },
        {
          label: 'scope',
          name: 'scope',
          type: 'select'
        },
        {
          label: '创建时间',
          name: 'created_at_range',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at_range',
          type: 'date'
        },
        {
          label: '过期时间',
          name: 'expired_at_range',
          type: 'date'
        }
      ],
      scopeFilterArr: [],
      accountTypeArr: [
        {
          name: '默认用户',
          value: 0
        },
        {
          name: '中航金网账户',
          value: 1
        },
        {
          name: 'foradar本地化',
          value: 2
        },
        {
          name: 'fd01本地化账号',
          value: 3
        }
      ],
      statusArr: [
        {
          name: '禁用',
          value: 0
        },
        {
          name: '启用',
          value: 1
        }
      ],
      publicMapData: {
        0: '-',
        1: 'amd架构',
        2: 'arm架构'
      },
      accountTypeData: [
        { value: 0, label: '默认用户' },
        { value: 1, label: '中航金网账户' },
        { value: 2, label: 'foradar本地化' },
        { value: 3, label: 'fd01本地化账号' }
      ],
      accountTypeMap: {
        0: '默认用户',
        1: '中航金网账户',
        2: 'foradar本地化',
        3: 'fd01本地化账号'
        // 4:'Goby'
      },
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      scopeArr: [],
      typeArr: [
        {
          id: '0',
          name: '根域'
        },
        {
          id: '1',
          name: '证书'
        },
        {
          id: '2',
          name: 'ICP'
        },
        {
          id: '3',
          name: 'ICON'
        },
        {
          id: '4',
          name: '关键词'
        }
      ],
      formInline: {
        page: 1,
        per_page: 10,
        keyword: '',
        operate_company_id: ''
      },
      highCheckdialog: false,
      tableHeader: [
        {
          label: 'SAAS_MICRO_CLIENT',
          name: 'client_id',
          minWidth: '150'
        },
        {
          label: 'SAAS_MICRO_SECRET',
          name: 'secret',
          minWidth: '150'
        },
        {
          label: '客户端状态',
          name: 'status',
          minWidth: '120'
        },
        {
          label: '用户类型',
          name: 'account_type'
        },
        {
          label: '设备ID',
          name: 'desc'
        },
        {
          label: '服务器架构',
          name: 'local_arch'
        },
        {
          label: '企业名称',
          name: 'company_name',
          minWidth: '120'
        },
        {
          label: 'FR点',
          name: 'api_quota_remaining',
          minWidth: '120'
        },
        {
          label: 'scope',
          name: 'scope',
          minWidth: '120'
        },
        {
          label: '过期时间',
          name: 'expired_at',
          minWidth: '150'
        },
        {
          label: 'IP白名单',
          name: 'ip_whitelist'
        },
        {
          label: '创建时间',
          name: 'created_at',
          minWidth: '150'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          minWidth: '150'
        }
      ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileList: [],
      dialogFormVisibleInsert: false,
      rules: {
        account_type: [{ required: true, message: '请输入', trigger: 'blur' }],
        client_id: [{ required: true, message: '请输入', trigger: 'blur' }],
        scope: [{ required: true, message: '请输入', trigger: 'blur' }],
        secret: [{ required: true, message: '请输入', trigger: 'blur' }],
        company_name: [{ required: true, message: '请输入', trigger: 'blur' }],
        expired_at: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      ruleForm: {
        account_type: 0,
        client_id: '',
        status: 0,
        scope: [],
        secret: '',
        company_name: '',
        expired_at: '',
        ip_whitelist: '*',
        mon_detect_limit: 0,
        per_cname_limit: 0,
        day_assets_limit: 0,
        desc: '',
        local_arch: 1,
        api_quota_remaining: 0
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      user: {
        role: ''
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getCname()
    this.getData()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    account_type_change(val) {
      if (val == 0) {
        this.ruleForm.mon_detect_limit = 0
        this.ruleForm.per_cname_limit = 0
        this.ruleForm.day_assets_limit = 0
      }
    },
    getScopeName(scope) {
      let str = ''
      let relScopeArr = scope.split('|')
      relScopeArr.forEach((item, index) => {
        for (let k in this.scopeArr) {
          if (k == item) {
            if (index == 0) {
              str += this.scopeArr[k]
            } else {
              str += ' | ' + this.scopeArr[k]
            }
          }
        }
      })
      return str
    },
    async getCname() {
      let res = await scope_golang_client_account()
      if (res.code == 0) {
        this.scopeArr = res.data
        this.scopeFilterArr = []
        Object.keys(res.data).forEach((key) => {
          this.scopeFilterArr.push({ value: key, name: res.data[key] })
        })
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    //生成32位随机数
    getNum() {
      var chars = [
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'j',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z'
      ]
      var nums = ''
      for (var i = 0; i < 10; i++) {
        var id = parseInt(Math.random() * 61)
        nums += chars[id]
      }
      return nums
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    getData(tmp) {
      if (!tmp) {
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      golang_client_account(this.formInline)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async insertSave(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let obj = { ...this.ruleForm }
          obj.scope = obj.scope.join('|')
          this.otherLoading = true
          let res = await add_golang_client_account(obj).catch(() => {
            this.otherLoading = false
          })
          this.otherLoading = false
          if (res.code == 0) {
            this.dialogFormVisibleInsert = false
            this.getData()
            this.$message.success('操作成功！')
          }
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        page: 1,
        per_page: 10,
        keyword: '',
        operate_company_id: ''
      }
    },
    editOne(row) {
      for (let i in this.ruleForm) {
        this.ruleForm[i] = row[i]
      }
      this.ruleForm.id = row.id
      this.ruleForm.local_arch = row.local_arch == 0 ? '' : row.local_arch
      this.ruleForm.status = row.status / 1
      this.ruleForm.scope = row.scope.split('|')
      this.dialogFormVisibleInsert = true
    },
    addEdit(row) {
      this.dialogFormVisibleInsert = true
      this.ruleForm = {
        account_type: 0,
        client_id: this.getNum(),
        status: 0,
        scope: [],
        secret: this.getNum(),
        company_name: '',
        expired_at: '',
        ip_whitelist: '*',
        mon_detect_limit: 0,
        per_cname_limit: 0,
        day_assets_limit: 0,
        desc: '',
        local_arch: 1,
        api_quota_remaining: 5000000
      }
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.limitClass {
      .el-form-item__label {
        line-height: 22px !important;
      }
    }
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}
</style>
