<template>
  <div class="container">
    <div class="headerTitle"
      >配置管理
      <el-button class="normalBtn" size="mini" type="primary" @click="addOne" id="port_add"
        >新增</el-button
      >
    </div>
    <div class="home_header">
      <div :class="hightFilterIsShow()">
        <el-descriptions v-for="(v, k, index) in tableData" :key="index">
          <template slot="title">
            {{ k }}
            <el-button
              class="normalBtn"
              size="mini"
              type="primary"
              @click="editOne(k, k == 'fofa_account_list' ? v[0] : v)"
              id="port_add"
              >更新</el-button
            >
          </template>
          <el-descriptions-item
            v-for="(ch_v, ch_k, ch_i) in k == 'fofa_account_list' ? v[0] : v"
            :key="ch_i"
            :label="ch_k"
          >
            <el-tooltip
              :open-delay="500"
              class="item"
              :content="Array.isArray(ch_v) ? ch_v.join(',') : String(ch_v)"
              effect="dark"
              placement="top"
            >
              <span>{{ Array.isArray(ch_v) ? ch_v.join(',') : ch_v }}</span>
            </el-tooltip>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleAdd"
      width="550px"
    >
      <template slot="title">新增</template>
      <div class="dialog-body">
        <el-form
          :model="addRuleForm"
          :rules="addrules"
          style="padding: 0 !important"
          ref="addruleForm"
          label-width="90px"
          class="demo-ruleForm"
        >
          <el-form-item label="标题" prop="key">
            <el-input
              v-model="addRuleForm.key"
              placeholder="请输入，不可输入consul, redis, mysql, rabbitmq, elastic"
            ></el-input>
          </el-form-item>
          <el-form-item label="内容" prop="val">
            <el-input
              type="textarea"
              :rows="4"
              v-model="addRuleForm.val"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleAdd = false" id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="addSave('addruleForm')"
          :loading="otherLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleInsert"
      width="550px"
    >
      <template slot="title">更新</template>
      <div class="dialog-body">
        <el-form
          :model="ruleForm"
          style="padding: 0 !important"
          ref="ruleForm"
          label-width="140px"
          class="demo-ruleForm"
        >
          <el-form-item v-for="(v, k, index) in ruleForm" :key="index" :label="k" prop="k">
            <el-input
              v-if="Array.isArray(ruleFormOld[k])"
              type="textarea"
              :rows="3"
              v-model="ruleForm[k]"
              placeholder="请输入"
            ></el-input>
            <el-switch
              v-else-if="typeof ruleFormOld[k] === 'boolean'"
              v-model="ruleForm[k]"
              active-color="#13ce66"
              inactive-color="#ff4949"
            >
            </el-switch>
            <el-input v-else v-model="ruleForm[k]" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
        <el-divider>新增字段</el-divider>
        <el-form style="padding: 0 !important" class="demo-form-inline">
          <el-form-item v-for="(item, index) in addWord" :key="index" prop="k">
            <el-col :span="2">
              <span>
                <i
                  v-if="index == addWord.length - 1"
                  class="el-icon-circle-plus-outline"
                  @click="addWordFun"
                ></i>
                <i
                  v-if="(addWord.length == 1 && index != 0) || addWord.length > 1"
                  class="el-icon-remove-outline"
                  @click="removeWordFun(index)"
                ></i>
              </span>
              <span></span>
            </el-col>
            <el-col :span="10">
              <el-input v-model="item.key" placeholder="请输入字段名称"></el-input>
            </el-col>
            <el-col style="text-align: center" class="line" :span="1">-</el-col>
            <el-col :span="11">
              <el-input v-model="item.val" placeholder="请输入字段值"></el-input>
            </el-col>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleInsert = false"
          id="keyword_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="otherLoading"
          id="keyword_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { configList, updateConfig } from '@/api/apiConfig/api.js'

export default {
  name: 'CronTask',
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      filterKeyword: ['consul', 'redis', 'mysql', 'rabbitmq', 'elastic'],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      checkedAll: false, // 是否全选
      checkedArr: [],
      formInline: {
        page: 1,
        per_page: 10
      },
      addRuleForm: {
        key: '',
        val: ''
      },
      highCheckdialog: false,
      addWord: [
        {
          key: '',
          val: ''
        }
      ],
      addruleForm: {},
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      dialogFormVisibleAdd: false,
      dialogFormVisibleInsert: false,
      addrules: {
        key: [{ required: true, message: '请输入', trigger: 'blur' }],
        val: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      ruleFormOld: {},
      ruleForm: {},
      loading: false,
      otherLoading: false,
      user: {
        role: ''
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    addWordFun() {
      this.addWord.push({
        keys: '',
        val: ''
      })
    },
    removeWordFun(index) {
      this.addWord.splice(index, 1)
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(true)
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    getData(tmp) {
      if (!tmp) {
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      configList(this.formInline)
        .then((res) => {
          // this.filterKeyword
          this.loading = false
          // 过滤掉key在this.filterKeyword数组中的数据
          for (let i in res.data) {
            if (this.filterKeyword.includes(i)) {
              delete res.data[i]
            }
          }
          this.tableData = res.data
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    addOne() {
      this.addRuleForm = {
        key: '',
        val: ''
      }
      this.dialogFormVisibleAdd = true
    },
    addSave(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (this.filterKeyword.includes(this.addRuleForm.key)) {
            this.$message.error('标题不能是consul, redis, mysql, rabbitmq, elastic')
            return
          }
          let params = {
            key: this.addRuleForm.key,
            val: this.addRuleForm.val
          }
          let res = null
          this.otherLoading = true
          res = await updateConfig(params).catch(() => {
            this.otherLoading = false
          })
          this.otherLoading = false
          if (res.code == 0) {
            this.dialogFormVisibleAdd = false
            this.getData()
            this.$message.success('操作成功！')
          }
        }
      })
    },
    insertSave(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (this.addWord.length > 0) {
            this.addWord.forEach((item) => {
              if (item.key) {
                this.ruleForm[item.key] = item.val
              }
            })
          }
          let params = {
            key: this.editKey,
            val: JSON.stringify(this.ruleForm)
          }
          let res = null
          res = await updateConfig(params).catch(() => {
            this.otherLoading = false
          })
          this.otherLoading = false
          if (res.code == 0) {
            this.dialogFormVisibleInsert = false
            this.getData()
            this.$message.success('操作成功！')
          }
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    editOne(editKey, row) {
      this.dialogFormVisibleInsert = true
      this.ruleForm = {}
      if (row) {
        this.editKey = editKey
        this.ruleFormOld = { ...row }
        let obj = { ...row }
        for (let i in obj) {
          if (Array.isArray(obj[i])) {
            obj[i] = obj[i].join(',')
          }
        }
        this.ruleForm = { ...obj }
      }
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.elDialogAdd .el-dialog .el-form .el-form-item .el-form-item__label {
  line-height: 22px !important;
}
.elDialogAdd {
  .el-divider__text {
    color: #2677ff;
  }
  i {
    // color: #2677ff;
    font-weight: bold;
    cursor: pointer;
  }
  /deep/.upload-demo {
    width: 100%;
    .el-upload {
      width: 100%;
    }
    .el-upload-dragger {
      width: 100%;
    }
    .downloadText {
      margin-left: 5px;
      color: #4285f4;
      cursor: pointer;
    }
    .el-upload-list {
      height: 50px;
      overflow-y: auto;
    }
  }
}
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .headerTitle {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      // padding: 0px 20px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%),
        linear-gradient(90deg, #f0f6ff 0%, #ffffff 100%);
      .el-descriptions {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #ebeef5;
        background-color: #fff;
        color: #303133;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 12px;
      }
      .el-descriptions-item__cell {
        max-width: 32%;
      }
      .el-descriptions__title {
        width: 100%;
        display: flex;
        justify-content: space-between;
        .normalBtn {
          min-width: 50px;
          height: 24px;
        }
      }
      .el-descriptions-item__container {
        width: 90%;
        .el-descriptions-item__content {
          display: inline-block;
          width: 95%;
          overflow: hidden !important;
          white-space: nowrap !important;
          text-overflow: ellipsis !important;
        }
      }
    }
    .el-table {
      border: 0;
    }
  }
}
</style>
