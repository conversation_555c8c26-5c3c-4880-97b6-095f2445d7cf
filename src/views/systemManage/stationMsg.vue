<template>
  <div class="container">
    <div class="headerTitle">
      <div>站内信</div>
      <div>
        <el-button type="text" id="user_add" :disabled="setNotifyDisable" @click="setNotify"
          >通知策略配置</el-button
        >
      </div>
    </div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入任务名称检索"
            @keyup.enter.native="checkFuncList"
            id="user_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="user_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="user_all"
            >选择全部</el-checkbox
          >
          <el-button class="normalBtn" type="primary" id="user_add" @click="delMsg()"
            >删除</el-button
          >
        </div>
      </div>
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="user"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="{ row }">
              <span v-if="item.name == 'msg_subtype' && row[item.name]">{{
                msgSubTypeMap[row.msg_subtype].name
              }}</span>
              <span v-else>
                {{ row[item.name] ? row[item.name] : '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="150">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="small"
                id="msg_detail"
                v-if="row.msg_status == 1"
                @click="jumpPage(row)"
                >查看详情</el-button
              >
              <el-button type="text" size="small" id="msg_detail" @click="delMsg(row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 新建模板 -->
    <el-dialog
      class="elDialogAdd"
      title="通知策略配置"
      :close-on-click-modal="false"
      :visible.sync="configDialogVisible"
      width="680px"
    >
      <div class="dialog-body" v-loading="configLoading">
        <el-form
          :model="ruleForm"
          style="padding: 0 !important"
          ref="ruleForm"
          :rules="ruleFormRules"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="通知内容" prop="report_module">
            <div class="contentClass">
              <el-checkbox
                v-model="assetCheckNotifyAll"
                :indeterminate="assetIsIndeterminate"
                @change="($event) => handleCheckAllChange($event, 'asset')"
                >资产台账变化</el-checkbox
              >
              <el-checkbox-group
                :key="1"
                v-model="ruleForm.asset_change_json"
                @change="($event) => handleCheckedSingleChange($event, 'asset')"
              >
                <el-checkbox
                  v-for="(item, id, index) in assetMsgSubTypeMap"
                  :label="id"
                  :key="index"
                  >{{ item.name }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
            <div class="contentClass">
              <el-checkbox
                v-model="riskCheckNotifyAll"
                :indeterminate="riskIsIndeterminate"
                @change="($event) => handleCheckAllChange($event, 'risk')"
                >风险变化</el-checkbox
              >
              <el-checkbox-group
                :key="2"
                v-model="ruleForm.risk_change_json"
                @change="($event) => handleCheckedSingleChange($event, 'risk')"
              >
                <el-checkbox
                  v-for="(item, id, index) in riskMsgSubTypeMap"
                  :label="id"
                  :key="index + '1'"
                  >{{ item.name }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
          </el-form-item>
          <el-form-item label="通知类型">
            <div class="msgType">
              <el-checkbox
                v-model="ruleForm.is_website_msg"
                :true-label="1"
                :false-label="2"
                disabled
                >站内信</el-checkbox
              >
              <el-checkbox v-model="ruleForm.is_email" :true-label="1" :false-label="2"
                >邮箱</el-checkbox
              >
              <el-input
                v-if="ruleForm.is_email == 1"
                v-model="ruleForm.email"
                placeholder="请填写通知邮箱"
              ></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="configDialogVisible = false" id="report_add_cancel"
          >取消</el-button
        >
        <el-button
          class="highBtn"
          :loading="setNotifyLoading"
          @click="handleMsg"
          id="report_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="消息类型：" prop="msg_type">
            <el-select
              filterable
              v-model="formInline.msg_subtype"
              placeholder="请选择"
              clearable
              @change="selectChange($event, 'msg_subtype', msgSubTypeData, true, false)"
            >
              <el-option
                v-for="item in msgSubTypeData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="report_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="report_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import hightFilter from '../../components/assets/highTab.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { webMsgList, webMsgNotify, setWebMsgNotify, delWebMsgNotify } from '@/api/apiConfig/api.js'

export default {
  components: { hightFilter },
  data() {
    return {
      setNotifyLoading: false,
      setNotifyDisable: false,
      highCheckdialog: false,
      assetIsIndeterminate: true,
      riskIsIndeterminate: true,
      btnLoading: false,
      ruleFormRules: {
        name: [
          { required: true, message: '请填写模板名称', trigger: 'change' },
          {
            message: '模板名称不能包含/',
            pattern: /^((?![/]).)*$/,
            trigger: 'blur'
          }
        ]
      },
      configDialogVisible: false,
      configLoading: false,
      ruleForm: {
        asset_change_json: [],
        risk_change_json: [],
        is_website_msg: 1,
        is_email: 2,
        email: ''
      },
      // 消息类型 顺序不可改变 追加只能在最后添加
      riskMsgSubTypeMap: {
        18: { name: '漏洞发现', path: '/repairLeakScan', menuId: '3-1-2' },
        22: {
          name: '热点漏洞',
          path: '/assetsLedger',
          menuId: '1-3-1',
          notifyFilterActTab: 'first'
        },
        23: {
          name: '风险情报',
          path: '/assetsLedger',
          menuId: '1-3-1',
          notifyFilterActTab: 'second'
        }
      },
      assetMsgSubTypeMap: {
        1: { name: 'IP资产', path: '/assetsLedger', menuId: '1-3-1', notifyFilterActTab: 'second' },
        2: { name: '登录入口', path: '/loginEntry', menuId: '1-3-2' },
        3: { name: '域名资产', path: '/domainAsset', menuId: '1-3-3' },
        4: { name: '证书资产', path: '/certAsset', menuId: '1-3-4' },
        5: { name: '业务系统', path: '/businessSystem', menuId: '1-3-5' },
        6: { name: 'URL(API)资产', path: '/urlAsset', menuId: '1-3-6' },
        7: { name: '疑似资产', path: '/unclaimCloud', menuId: '1-4' },
        8: { name: '数字资产', path: '/newAssets', menuId: '1-7' }
      },
      riskMsgSubTypeData: [
        { id: 18, name: 'IP资产', path: '/assetsLedger', menuId: '1-3-1' },
        { id: 22, name: '登录入口', path: '/loginEntry', menuId: '1-3-2' },
        { id: 23, name: '域名资产', path: '/domainAsset', menuId: '1-3-3' }
      ],
      msgSubTypeData: [
        { id: 1, name: 'IP资产', path: '/assetsLedger', menuId: '1-3-1' },
        { id: 2, name: '登录入口', path: '/loginEntry', menuId: '1-3-2' },
        { id: 3, name: '域名资产', path: '/domainAsset', menuId: '1-3-3' },
        { id: 4, name: '证书资产', path: '/certAsset', menuId: '1-3-4' },
        { id: 5, name: '业务系统', path: '/businessSystem', menuId: '1-3-5' },
        { id: 6, name: 'URL(API)资产', path: '/urlAsset', menuId: '1-3-6' },
        { id: 7, name: '疑似资产', path: '/unclaimCloud', menuId: '1-4' },
        { id: 8, name: '数字资产', path: '/newAssets', menuId: '1-7' }
      ],
      loading: false,
      tableData: [],
      tableHeader: [
        {
          label: '消息内容',
          name: 'msg_content',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '消息类型',
          name: 'msg_subtype',
          minWidth: 120
        },
        {
          label: '创建时间',
          name: 'created_at',
          minWidth: 120
        }
      ],
      checkedAll: false,
      formInline: {},
      total: 0,
      highlist: null,
      highTabShow: [
        {
          label: '消息类型',
          name: 'msg_subtype',
          type: 'select'
        },
        {
          label: '创建时间',
          name: 'created_at',
          type: 'date'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      formInline: {},
      checkedArr: [],
      assetCheckNotifyAll: false,
      riskCheckNotifyAll: false,
      user: {
        role: ''
      }
    }
  },
  mounted() {
    if (sessionStorage.getItem('userMessage')) {
      let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
      if (userInfo) {
        this.user = userInfo.user
      }
    }
    if (this.user.role == 2 && !this.currentCompany) return
    this.getDataList()
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany']),
    msgSubTypeMap(val) {
      let newVal = Object.assign(val, { ...this.riskMsgSubTypeMap, ...this.assetMsgSubTypeMap })
      return newVal
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.user.role == 2) {
        this.getDataList()
      }
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    resetForm(ref) {
      this.$refs[ref].resetFields()
      this.formInline = {
        keyword: '',
        page: 1,
        per_page: 10,
        name: '',
        type: '',
        operate_company_id: '',
        created_at_range: []
      }
    },
    jumpPage(row) {
      let typeObj = this.msgSubTypeMap[row.msg_subtype]
      let path = typeObj.path
      let menuId = typeObj.menuId
      this.changeMenuId(menuId)
      sessionStorage.setItem('menuId', menuId)
      this.$router.push({
        path,
        query: {
          notifyFilterId: row.id,
          notifyFilterMsg: row.msg_content,
          notifyFilterTab: row.app_type || typeObj.notifyFilterActTab,
          notifyFilterActTab: typeObj.notifyFilterActTab
        }
      })
    },
    async getNotifyData() {
      let res = await webMsgNotify({ operate_company_id: this.currentCompany }).catch(() => {
        this.setNotifyDisable = false
      })
      if (res.code == 0) {
        this.ruleForm.id = res.data.id
        this.ruleForm.is_email = res.data.is_email
        this.ruleForm.email = res.data.email
        this.ruleForm.is_website_msg = res.data.is_website_msg || 1
        if (res.data.asset_change_json) {
          this.ruleForm.asset_change_json = JSON.parse(res.data.asset_change_json)
          this.handleCheckedSingleChange(this.ruleForm.asset_change_json, 'asset')
        } else {
          this.ruleForm.asset_change_json = []
          this.assetCheckNotifyAll = true
          this.handleCheckAllChange(true, 'asset')
        }
        if (res.data.risk_found_json) {
          this.ruleForm.risk_change_json = JSON.parse(res.data.risk_found_json)
          this.handleCheckedSingleChange(this.ruleForm.risk_change_json, 'risk')
        } else {
          this.ruleForm.risk_change_json = []
          this.riskCheckNotifyAll = true
          this.handleCheckAllChange(true, 'risk')
        }
      }
      this.configDialogVisible = true
      this.setNotifyDisable = false
    },
    setNotify() {
      this.setNotifyDisable = true
      this.getNotifyData()
    },
    handleCheckAllChange(val, type) {
      let msgSubTypeMap = this[type + 'MsgSubTypeMap'] || {}
      this.ruleForm[type + '_change_json'] = val ? Object.keys(msgSubTypeMap || {}) : []
      this[type + 'IsIndeterminate'] = false
    },
    handleCheckedSingleChange(value, type) {
      let checkedCount = value.length
      let msgSubTypeMap = this[type + 'MsgSubTypeMap'] || {}
      this[type + 'CheckNotifyAll'] = checkedCount === Object.keys(msgSubTypeMap).length
      this[type + 'IsIndeterminate'] =
        checkedCount > 0 && checkedCount < Object.keys(msgSubTypeMap).length
    },
    // 通知策略配置
    async handleMsg() {
      if (
        this.ruleForm.asset_change_json.length == 0 &&
        this.ruleForm.risk_change_json.length == 0
      ) {
        this.$message.error('请至少选择一项通知内容')
        return
      }
      // if (this.ruleForm.asset_change_json.length == 0) {
      //   this.$message.error('请至少选择一项通知内容')
      //   return
      // }
      this.setNotifyLoading = true
      let risk_found_json = JSON.stringify(this.ruleForm.risk_change_json)
      // delete this.ruleForm.risk_change_json
      let obj = {
        ...this.ruleForm,
        asset_change_json: JSON.stringify(this.ruleForm.asset_change_json),
        risk_found_json,
        operate_company_id: this.currentCompany
      }
      let res = await setWebMsgNotify({ ...obj }).catch(() => {
        this.setNotifyLoading = false
      })
      if (res.code == 0) {
        this.configDialogVisible = false
        this.$message.success('配置成功')
      }
      this.setNotifyLoading = false
    },
    async getDataList() {
      this.loading = true
      let res = await webMsgList({
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total || 0
      }
      this.loading = false
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    async delMsg(row) {
      let ids = []
      if (row && row.id) {
        // 删除单条
        ids = [row.id]
      } else {
        if (!this.checkedAll) {
          if (this.checkedArr.length == 0) {
            this.$message.error('请选择需要操作的数据')
            return
          }
          ids = this.checkedArr.map((item) => item.id)
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_info_del_cancel',
        confirmButtonClass: 'cloud_info_del_sure',
        customClass: 'cloud_info_del',
        type: 'warning'
      }).then(async () => {
        let obj = {
          ids,
          ...this.formInline,
          page: this.currentPage,
          per_page: this.pageSize,
          operate_company_id: this.currentCompany
        }
        let res = await delWebMsgNotify(obj)
        if (res.code == 0) {
          this.$message.success('删除成功')
          this.getDataList()
          this.checkedAll = false
          this.$refs.eltable.clearSelection()
        }
      })
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    // 筛选显示条目
    highCheck() {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getDataList()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.headerTitle {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
  /deep/ .el-input__icon {
    line-height: 32px;
  }
}
/deep/.dialog-body {
  .el-loading-spinner {
    top: 20% !important;
  }
  .contentClass {
    & > .el-checkbox {
      background: #fff;
    }
    .el-checkbox-group {
      .el-checkbox {
        width: 117px;
      }
    }
  }
  .is-active > span {
    margin-left: 0 !important;
  }
  .titleClass {
    line-height: 26px !important;
  }
  .filterClass {
    padding-left: 20px;
    margin-left: 10px;
    margin-bottom: 5px;
    line-height: 24px;
  }
  .el-checkbox-group {
    width: 94% !important;
    padding-left: 20px;
    margin-left: 10px;
  }
  .el-select {
    width: 94% !important;
    padding-left: 20px;
    margin-left: 10px;
  }
  .el-checkbox {
    margin-right: 10px;
    /* width:32px; */
    text-align: left;
  }
  .msgType {
    display: flex;
  }
}
/deep/ .el-button--text {
  color: #2677ff;
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
