<template>
  <div class="container">
    <div class="headerTitle">扫描任务</div>
    <div class="tableContainer">
      <el-table
        border
        :data="tableData"
        row-key="id"
        :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
        @selection-change="handleSelectionChange"
        @cell-mouse-enter="showTooltip"
        @cell-mouse-leave="hiddenTooltip"
        ref="eltable"
        height="100%"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableHeader"
          :key="item.id"
          :prop="item.name"
          align="left"
          :min-width="item.minWidth"
          :fixed="item.fixed"
          :label="item.label"
        >
          <template slot-scope="scope">
            <span v-if="item.name == 'userName'">
              {{ scope.row.user.name }}
            </span>
            <span v-else-if="item.name == 'status'">
              {{ getTableStatus(scope.row.status) }}
            </span>
            <span v-else-if="item.name == 'progress'">
              <el-progress :percentage="Number(scope.row.progress)" :show-text="true"></el-progress>
            </span>
            <span v-else>
              <span v-if="scope.row[item.name]">{{ scope.row[item.name] }}</span>
              <span v-else>-</span>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { scanTasking } from '@/api/apiConfig/api.js'

export default {
  data() {
    return {
      tableHeader: [
        {
          label: '任务名称',
          name: 'name',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '任务ID',
          name: 'id',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '节点',
          name: 'node',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '进度',
          name: 'progress',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '状态',
          name: 'status',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '创建时间',
          name: 'created_at',
          minWidth: 100
        },
        {
          label: '任务开始时间',
          name: 'start_at',
          minWidth: 100
        },
        {
          label: '任务结束时间',
          name: 'end_at',
          minWidth: 100
        },
        {
          label: '用户',
          name: 'userName',
          minWidth: 100
        }
      ],
      tableData: [],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      checkedArr: false
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getTableStatus(status) {
      let statusLabel = ''
      if (status == 0) {
        statusLabel = '等待扫描'
      } else if (status == 1) {
        statusLabel = '扫描中'
      } else if (status == 2) {
        statusLabel = '扫描完成'
      } else if (status == 3) {
        statusLabel = '扫描失败'
      } else if (status == 4) {
        statusLabel = '暂停扫描'
      }
      return statusLabel
    },
    async getData() {
      let res = await scanTasking()
      if (res.code == 0) {
        this.tableData = res.data
      }
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
}
.tableContainer {
  height: 100%;
}
</style>
