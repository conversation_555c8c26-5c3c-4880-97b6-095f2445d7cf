<template>
  <div class="containerWrap">
    <div class="menuWrap">
      <div class="left">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-bianzu"></use>
        </svg>
        <span>互联网资产攻击面管理平台</span>
      </div>
      <!-- <img height="52" src="../../assets/images/notice_top.png" alt=""> -->
    </div>
    <div class="uploadBotBox">
      <img src="../../assets/images/uploadBot.png" alt="" class="uploadBot" />
      <div class="uploadBotText">升级中...</div>
    </div>
    <img class="noticeBot" src="../../assets/images/notice_bot.png" alt="" />
  </div>
</template>

<script>
import { getUpdateInfo } from '@/api/apiConfig/api.js'
import { logout } from '@/api/apiConfig/person.js'

export default {
  data() {
    return {
      timer: null
    }
  },
  created() {
    if (this.timer != null) {
      clearInterval(this.timer)
    } else {
      this.timer = setInterval(() => {
        this.getUploadInfo()
      }, 3000)
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    async getUploadInfo() {
      let res = await getUpdateInfo()
      if (res.code == 0) {
        // 执行中
        if (res.data.status == 1) {
          return ''
        }
        // 失败
        else if (res.data.status == 2) {
          this.$message.error(res.data.message)
          this.$alert(res.data.message, {
            confirmButtonText: '确定',
            callback: (action) => {
              this.setlogout()
            }
          })
          // this.setlogout()
        } else if (res.data.status == 0) {
          return ''
        }
        // 成功
        else {
          this.$message.success('升级成功')
          this.setlogout()
        }
      }
    },
    async setlogout() {
      let res = await logout()
      if (res.code == 0) {
        sessionStorage.clear()
        localStorage.clear()
        this.$router.replace('/login')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.containerWrap {
  position: relative;
  width: 100%;
  height: 100%;
  background: url(../../assets/images/notice_bg.png) no-repeat;
  background-size: 100% 100%;
  .content {
    width: 748px;
    height: 452px;
    margin: -78px auto 0;
    padding: 28px 24px;
    box-sizing: border-box;
    opacity: 1;
    border-radius: 4px;
    background: linear-gradient(270deg, rgba(38, 119, 255, 0.1) 0%, rgba(38, 119, 255, 0.04) 100%);
    border: 1px solid rgba(175, 198, 237, 1);
    backdrop-filter: blur(2px);
    p {
      font-size: 18px;
      line-height: 36px;
    }
    .indentClass {
      text-indent: 24px;
      span {
        color: #2677ff;
      }
    }
    .noticeContentClass {
      max-height: 160px;
      overflow: auto;
      line-height: 36px;
    }
  }
  .contentTop {
    margin: 178px auto 0;
    width: 748px;
    display: flex;
    justify-content: space-between;
    font-size: 24px;
    font-weight: 500;
    color: #2677ff;
    line-height: 26px;
    .noticeBall {
      margin-top: -95px;
    }
  }
  .noticeBot {
    position: absolute;
    bottom: 0;
    right: 0;
  }
  ::v-deep .menuWrap {
    // position: absolute;
    width: 100%;
    height: 52px;
    line-height: 52px;
    font-size: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(270deg, #ffffff 0%, #f0f6ff 100%) !important;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08) !important;
    z-index: 99;
    img {
      cursor: pointer;
    }
    .left {
      span {
        font-weight: 800;
        color: #37393c;
      }
      svg {
        font-size: 28px;
        margin: 0 12px 0 11px;
        vertical-align: middle;
      }
    }
  }
}
.uploadBotBox {
  position: absolute;
  bottom: 40%;
  right: 42%;
}
.uploadBot {
  width: 280px;
  height: 280px;
}
.uploadBotText {
  width: 100%;
  display: flex;
  justify-content: center;
  font-size: 24px;
  font-weight: 400;
  color: rgba(98, 102, 108, 1);
}
</style>
