<template>
  <div class="container">
    <div class="headerTitle">用户管理</div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
            id="user_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <el-select
            v-model="formInline.role"
            placeholder="请选择用户类型"
            @change="checkFuncList"
            multiple
            collapse-tags
            v-if="userMessage.user.role == 1"
            id="user_select"
          >
            <el-option
              :label="v.name"
              :value="v.value"
              v-for="v in roleArr"
              :key="v.name"
            ></el-option>
          </el-select>
          <span @click="highCheckdialog = true" id="user_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="user_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            style="margin-left: 10px"
            type="primary"
            @click="exportExcel"
            id="user_export"
            >导出</el-button
          >
          <el-button class="normalBtn" type="primary" @click="addFun()" id="user_add"
            >新建用户</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="user"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="checkedAll"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span class="greenLine" v-if="item.name == 'status' && scope.row[item.name] == 1"
                >启用</span
              >
              <span class="redLine" v-else-if="item.name == 'status' && scope.row[item.name] == 0"
                >禁用</span
              >
              <span v-else-if="item.name == 'role'">{{
                roleArr.find((v) => v.value == scope.row[item.name]).name
              }}</span>
              <span v-else-if="item.name == 'name'">
                <el-tooltip
                  :open-delay="500"
                  class="item"
                  popper-class="chainClass"
                  effect="dark"
                  placement="top"
                >
                  <div slot="content" class="userInfo">
                    <!-- <p>使用时间：{{scope.row.login_ip ? scope.row.login_ip : '-'}}</p> -->
                    <p>登录地址：{{ scope.row.login_ip ? scope.row.login_ip : '-' }}</p>
                    <!-- <p>在线时长：</p> -->
                    <p>ip台账数量：{{ scope.row.ip_sure_num ? scope.row.ip_sure_num : '-' }}</p>
                    <p
                      >忽略资产数量：{{
                        scope.row.ip_ingore_num ? scope.row.ip_ingore_num : '-'
                      }}</p
                    >
                    <p
                      >疑似资产数量：{{
                        scope.row.ip_default_num ? scope.row.ip_default_num : '-'
                      }}</p
                    >
                    <p
                      >威胁资产数量：{{
                        scope.row.ip_threaten_num ? scope.row.ip_threaten_num : '-'
                      }}</p
                    >
                    <p
                      >单位资产测绘任务数量：{{
                        scope.row.detect_task_num ? scope.row.detect_task_num : '-'
                      }}</p
                    >
                    <p
                      >云端推荐任务数量：{{
                        scope.row.recommend_task_num ? scope.row.recommend_task_num : '-'
                      }}</p
                    >
                    <p
                      >资产扫描任务数量：{{
                        scope.row.scan_task_num ? scope.row.scan_task_num : '-'
                      }}</p
                    >
                    <p
                      >域名发现任务数量：{{
                        scope.row.domain_task_num ? scope.row.domain_task_num : '-'
                      }}</p
                    >
                  </div>
                  <span
                    ><span class="f-label" v-if="scope.row.level == 0 && scope.row.role == 3"
                      >测试</span
                    >{{ scope.row[item.name] }}</span
                  >
                </el-tooltip>
              </span>
              <span v-else-if="item.name == 'is_test' || item.name == 'is_real_customer'">
                <span v-if="scope.row[item.name] == 0">否</span>
                <span v-else-if="scope.row[item.name] == 1">是</span>
                <span v-else>否</span>
              </span>
              <span v-else-if="item.name == 'has_subdomain_tab'">
                <span v-if="scope.row.role == 3">
                  <span v-if="scope.row[item.name] == 0">无</span>
                  <span v-else-if="scope.row[item.name] == 1">有</span>
                </span>
                <span v-else>
                  <span>有</span>
                </span>
              </span>
              <span v-else-if="item.name == 'detect_company_name'">
                {{
                  scope.row[item.name] && JSON.parse(scope.row[item.name]).length > 0
                    ? JSON.parse(scope.row[item.name]).join('，')
                    : '-'
                }}
              </span>
              <span v-else-if="item.name == 'companyName'">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="tooltipContent(scope.row, item.name)"
                  placement="top"
                  :open-delay="500"
                >
                  <span class="one-line">{{
                    scope.row['company'] && scope.row['company'].name
                  }}</span>
                </el-tooltip>
              </span>
              <span v-else-if="item.name == 'secret_key'">
                <span @mouseenter="setShow(scope.$index)" @mouseleave="setHide(scope.$index)">
                  <i
                    @click="copyFun(scope.row['secret_key'])"
                    v-if="scope.row['secret_key'] && scope.row['copyIsShow']"
                    class="el-icon-document-copy globalCopy"
                  ></i>
                  {{ scope.row['secret_key'] }}</span
                >
              </span>
              <span v-else-if="item.name == 'controled_safe_users'">
                {{
                  scope.row['controled_safe_users']
                    ? scope.row['controled_safe_users'].join(', ')
                    : '-'
                }}
              </span>
              <span v-else>{{ scope.row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="230">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="addFun(scope.row)"
                v-if="scope.row.role != 1"
                id="user_edit"
                >编辑</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="resetPasswordShow(scope.row)"
                id="user_repossess"
                >重置密码</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="removeOne(scope.row.id)"
                v-if="scope.row.role != 1"
                id="user_del"
                >删除</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="limit(scope.row)"
                v-if="scope.row.role == 3"
                id="user_authorize"
                >查看授权</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="limit(scope.row)"
                v-if="scope.row.role == 2"
                id="user_manage"
                >查看管理企业</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleFenzu"
      width="660px"
      v-if="dialogFormVisibleFenzu"
    >
      <template slot="title">
        {{ dialogRow != '' ? '编辑用户' : '新建用户' }}
      </template>
      <div class="dialog-body">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          class="demo-ruleForm add-dialog"
        >
          <el-form-item label="用户名" prop="name">
            <el-input v-model="ruleForm.name" placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item label="手机" prop="mobile">
            <el-input v-model="ruleForm.mobile" placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item label="邮箱地址" prop="email">
            <!-- 超管不校验邮箱 -->
            <el-input
              readonly
              onfocus="this.removeAttribute('readonly')"
              @input="$forceUpdate()"
              v-model="ruleForm.email"
              clearable
              autocomplete="off"
              placeholder="请输入邮箱地址"
            ></el-input>
          </el-form-item>
          <el-form-item label="scope" prop="scope">
            <el-select
              filterable
              multiple
              collapse-tags
              v-model="ruleForm.scopes"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(v, k, index) in scopeArr"
                :key="index"
                :label="v"
                :value="k"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="ruleForm.status"
              active-text="开启"
              inactive-text="关闭"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </el-form-item>
          <el-form-item label="测试人员" prop="is_test">
            <el-radio v-model="ruleForm.is_test" :label="1">是</el-radio>
            <el-radio v-model="ruleForm.is_test" :label="0">否</el-radio>
          </el-form-item>
          <el-form-item v-if="ruleForm.role == '3'" label="客户" prop="is_real_customer">
            <el-radio v-model="ruleForm.is_real_customer" :label="1">是</el-radio>
            <el-radio v-model="ruleForm.is_real_customer" :label="0">否</el-radio>
          </el-form-item>
          <el-form-item v-if="ruleForm.role == '3'" label="子域名权限" prop="has_subdomain_tab">
            <el-radio v-model="ruleForm.has_subdomain_tab" :label="1">有</el-radio>
            <el-radio v-model="ruleForm.has_subdomain_tab" :label="0">无</el-radio>
          </el-form-item>
          <el-form-item
            v-if="ruleForm.role == '3'"
            label="客户可测绘企业"
            prop="detect_company_name"
          >
            <!-- <el-select
              v-model="ruleForm.detect_company_name"
              filterable
              remote
              clearable
              multiple
              :collapse-tags="true"
              @change="companyChange"
              placeholder="请输入企业名称进行搜索"
              :remote-method="remoteMethod"
              :loading="loading">
              <el-option
                  v-for="(item,index) in real_customer_company"
                  :key="index"
                  :value="item.value">
                  {{item.value}}
              </el-option>
            </el-select> -->
            <el-input
              type="textarea"
              :rows="5"
              v-model="detect_company_name"
              placeholder="请输入其他测绘企业,多个值请使用分号或换行分隔"
            ></el-input>
          </el-form-item>
          <el-form-item label="用户优先级" prop="priority">
            <el-select v-model="ruleForm.priority" placeholder="请选择用户优先级">
              <el-option :label="v" :value="v" v-for="v in priorityArr" :key="v"></el-option>
            </el-select>
          </el-form-item>
          <template v-if="dialogRow == ''">
            <!-- readonly
              onfocus="this.removeAttribute('readonly')"
              @input="$forceUpdate()" -->
            <el-form-item label="用户密码" prop="password">
              <el-input
                v-model="ruleForm.password"
                type="password"
                clearable
                show-password
                autocomplete="password"
                placeholder="请输入用户密码"
              ></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="checkPassword">
              <el-input
                v-model="ruleForm.checkPassword"
                type="password"
                clearable
                show-password
                autocomplete="off"
                placeholder="请再次输入用户密码"
              ></el-input>
            </el-form-item>
          </template>
          <el-form-item
            label="用户类型"
            prop="role"
            v-if="userMessage.user.role == 1 && dialogRow == ''"
          >
            <el-select v-model="ruleForm.role" placeholder="请选择用户类型" @change="changeRole">
              <el-option label="安服人员" :value="2"></el-option>
              <el-option label="企业租户" :value="3"></el-option>
              <el-option label="售后人员" :value="4"></el-option>
            </el-select>
          </el-form-item>
          <template v-if="ruleForm.role == '2'">
            <el-form-item label="管理企业" prop="safe_company_id">
              <el-select
                filterable
                v-model="ruleForm.safe_company_id"
                placeholder="请选择管理的企业"
                multiple
                collapse-tags
              >
                <el-option
                  :label="v.name"
                  :value="v.id"
                  v-for="v in companyArr"
                  :key="v.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
          <template v-if="ruleForm.role == '3'">
            <el-form-item label="企业名称" prop="company.name">
              <el-input v-model="ruleForm.company.name" placeholder="请输入企业名称"></el-input>
            </el-form-item>
            <el-form-item label="企业所在地区" prop="company.area_code_all">
              <el-cascader
                v-model="ruleForm.company.area_code_all"
                :props="cascaderProps"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="企业简介" prop="company.introduce">
              <el-input
                v-model="ruleForm.company.introduce"
                placeholder="请输入企业简介"
              ></el-input>
            </el-form-item>
            <el-form-item label="行业" prop="company.industry_id">
              <el-select filterable v-model="ruleForm.company.industry_id" placeholder="请选择行业">
                <el-option
                  :label="v.name"
                  :value="v.id"
                  v-for="v in industryArr"
                  :key="v.name"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="到期日期" prop="expires_at">
              <el-date-picker
                v-model="ruleForm.expires_at"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="用户属性" prop="level">
              <el-select v-model="ruleForm.level" placeholder="请选择用户属性">
                <el-option
                  :label="v.name"
                  :value="v.value"
                  v-for="v in levelArr"
                  :key="v.name"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="IP资产" prop="company.limit_ip_asset">
              <el-select v-model="ruleForm.company.limit_ip_asset" placeholder="请选择IP资产">
                <el-option
                  :label="v.name"
                  :value="v.value"
                  v-for="v in ipAssetsArr"
                  :key="v.name"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="自定义"
              prop="company.limit_ip_asset_val"
              v-if="ruleForm.company.limit_ip_asset === -1"
            >
              <el-input
                v-model="ruleForm.company.limit_ip_asset_val"
                type="number"
                placeholder="请输入IP资产次数"
              ></el-input>
            </el-form-item>
            <el-form-item label="云端推荐" prop="company.limit_cloud_recommend">
              <el-input
                v-model="ruleForm.company.limit_cloud_recommend"
                type="number"
                placeholder="请输入云端推荐次数"
              ></el-input>
            </el-form-item>
            <template v-if="userMessage.user.role == 1 && dialogRow == ''">
              <el-form
              :model="ruleForm"
              :rules="rules"
              ref="ruleForm"
              label-width="290px"
              >
                <el-form-item prop="need_asset_mapping"  label="是否需要预先进行智能模式单位资产测绘" >
                  <el-radio v-model="ruleForm.need_asset_mapping" :label="1">是</el-radio>
                  <el-radio v-model="ruleForm.need_asset_mapping" :label="2">否</el-radio>
                </el-form-item>
               </el-form>
                <template v-if="ruleForm.need_asset_mapping == 1">
                  <el-form
                  label-width="120px"
                  :model="ruleForm"
                  :rules="rules"
                  ref="ruleForm"
                  >
                    <el-form-item prop="target_company_name" label="目标企业名称">
                      <el-select
                    v-model="ruleForm.target_company_name"
                    filterable
                    remote
                    clearable
                    @change="companyChange"
                    placeholder="请输入企业名称进行搜索"
                    :remote-method="remoteMethod"
                  >
                    <el-option v-for="(item, index) in real_customer_company" :key="index" :value="item.value">
                      {{ item.value }}
                    </el-option>
                  </el-select>
                    </el-form-item>
                    <el-form-item label="其他测绘企业">
                      <el-input
                        v-model="ruleForm.other_mapping_companies"
                        type="textarea"
                        :rows="2"
                        placeholder="请输入其他测绘企业,多个值请使用分号分隔"
                      ></el-input>
                    </el-form-item>
                    <el-form-item label="控股比例：">
                      <template slot="label">
                        控股比例：
                        <el-tooltip 
                        placement="top"
                        >
                          <span slot="content" style="line-height: 24px;">
                            控股比例的设置对上述（其他测绘企业）生效，如无需测绘（其他测绘企业）下的控股企业，<br/>保持默认配置0%即可（该配置项只对上方的其他测绘企业的控股比例范围生效）。
                          </span>
                          <img style="width: 15px; height: 15px;padding-bottom: 9px;" src="@/assets/images/description.png" alt="" />
                        </el-tooltip>
                      </template>
                      <el-input v-model="ruleForm.percent" type="input" placeholder="请输入控股比例">
                        <span slot="suffix">%</span>
                      </el-input>
                    </el-form-item>
                  </el-form>
                </template>
            </template>
            <el-form-item label="数字资产" prop="company.new_asset_rate_s">
              <el-switch
                v-model="ruleForm.company.new_asset_rate_s"
                active-text="开启"
                inactive-text="关闭"
                :active-value="1"
                :inactive-value="0"
              >
              </el-switch>
            </el-form-item>
            <el-form-item label="漏洞管理" prop="show_poc">
              <el-switch
                v-model="ruleForm.show_poc"
                active-text="开启"
                inactive-text="关闭"
                :active-value="1"
                :inactive-value="0"
              >
              </el-switch>
            </el-form-item>
            <template v-if="ruleForm.show_poc === 1">
              <el-form-item label="扫描次数" prop="company.limit_poc_scan">
                <el-input
                  v-model="ruleForm.company.limit_poc_scan"
                  type="number"
                  placeholder="请输入漏洞管理推荐次数"
                ></el-input>
              </el-form-item>
            </template>
            <el-form-item label="数据泄露" prop="company.data_leak_rate_s">
              <el-switch
                v-model="ruleForm.company.data_leak_rate_s"
                active-text="开启"
                inactive-text="关闭"
                :active-value="1"
                :inactive-value="0"
              >
              </el-switch>
            </el-form-item>
            <el-form-item
              label="关键词"
              prop="company.limit_monitor_keyword"
              v-if="
                ruleForm.company.new_asset_rate_s === 1 || ruleForm.company.data_leak_rate_s === 1
              "
            >
              <el-input
                v-model="ruleForm.company.limit_monitor_keyword"
                type="number"
                placeholder="请输入关键词数量"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="黑IP封禁" prop="black_ip_switch">
              <el-switch v-model="ruleForm.black_ip_switch"
                        active-text="开启"
                        inactive-text="关闭"
                        :active-value="1"
                        :inactive-value="0">
              </el-switch>
            </el-form-item> -->
          </template>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleFenzu = false" id="user_add_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="insertSave('ruleForm')"
          :loading="otherLoading"
          id="user_add_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="用户名：" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item label="手机：" prop="mobile">
            <el-input v-model="formInline.mobile" placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item label="邮箱地址：" prop="email">
            <el-input v-model="formInline.email" placeholder="请输入邮箱地址"></el-input>
          </el-form-item>
          <el-form-item label="用户类型：" prop="role" v-if="userMessage.user.role == 1">
            <el-select
              v-model="formInline.role"
              placeholder="请选择用户类型"
              multiple
              collapse-tags
              @change="selectChange($event, 'role', roleArr, true, true)"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in roleArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：" prop="status">
            <el-select
              v-model="formInline.status"
              clearable
              placeholder="请选择用户状态"
              @change="selectChange($event, 'status', statusArr, true, false)"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in statusArr"
                :key="v.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="企业名称："
            prop="company"
            v-if="userMessage.user.role == 1 && formInline.role.includes(3)"
          >
            <el-input v-model="formInline.company" placeholder="请输入企业名称"></el-input>
          </el-form-item>
          <el-form-item label="创建时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="到期时间：" prop="expires_at">
            <el-date-picker
              v-model="formInline.expires_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="user_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="user_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="resetPasswordVisible"
      width="560px"
    >
      <template slot="title"> 重置密码 </template>
      <el-form
        :model="passwordRuleForm"
        :rules="passwordRules"
        ref="passwordRuleForm"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-form-item label="输入密码" prop="password">
          <el-input
            v-model="passwordRuleForm.password"
            placeholder="请输入用户密码"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item label="再次输入密码" prop="checkPassword">
          <el-input
            v-model="passwordRuleForm.checkPassword"
            placeholder="请再次输入用户密码"
            show-password
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="resetPasswordVisible = false"
          id="user_repossess_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="passwordInsertSave('passwordRuleForm')"
          :loading="otherLoading"
          id="user_repossess_sure"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 查看授权 -->
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="queryVisible"
      width="560px"
      v-if="queryVisible"
    >
      <template slot="title">
        {{ ruleForm.title }}
      </template>
      <el-form
        v-model="ruleForm"
        ref="ruleForm"
        :label-width="ruleForm.leabelWidth + 'px'"
        label-position="left"
        class="demo-ruleForm empower-form"
      >
        <template v-if="ruleForm.role == 3">
          <div class="item">
            <el-form-item label="用户属性">
              {{ ruleForm.level_label }}
            </el-form-item>
            <el-form-item label="IP资产">
              {{ ruleForm.company.limit_ip_asset }}
            </el-form-item>
            <el-form-item label="云端推荐">
              {{ ruleForm.company.limit_cloud_recommend }}
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="漏洞管理">
              <p>{{ ruleForm.show_poc > 0 ? '开启' : '关闭' }}</p>
            </el-form-item>
            <el-form-item label="次数" v-if="ruleForm.show_poc > 0">
              {{ ruleForm.company.limit_poc_scan }}
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="数字资产">
              <p>{{ ruleForm.company.new_asset_rate > 0 ? '开启' : '关闭' }}</p>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="数据泄露">
              <p>{{ ruleForm.company.data_leak_rate > 0 ? '开启' : '关闭' }}</p>
            </el-form-item>
          </div>
          <div class="item" v-if="ruleForm.company.limit_monitor_keyword > 0">
            <el-form-item label="关键词">
              {{ ruleForm.company.limit_monitor_keyword }}
            </el-form-item>
          </div>
          <!-- <div class="item">
            <el-form-item label="黑IP封禁">
              <p>{{ ruleForm.black_ip_switch > 0 ? '开启' : '关闭' }}</p>
            </el-form-item>
          </div> -->
        </template>
        <template v-if="ruleForm.role == 2">
          <el-form-item v-for="v in ruleForm.safe_company" :key="v.id">
            {{ v.name }}
          </el-form-item>
        </template>
      </el-form>
    </el-dialog>
    <batchValidate
      :dialogVisible="validDialogVisible"
      @confirm="validateConfirm"
      @copyText="copyText"
      :list="errorList"
      @close="validDialogVisible = false"
    >
      <span slot="title">客户可测绘企业校验错误企业名称</span>
      <span slot="tip">以下为输入错误的企业名称,可以重新复制到输入框中做修改</span>
      <span slot="confirmBtn">移除并复制错误企业名称</span>
    </batchValidate>
  </div>
</template>

<script>
import batchValidate from '@/components/batchValidate.vue'
import sha1 from '@/utils/sha1Encrypt'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import { scope_golang_client_account, area, industry, companyList } from '@/api/apiConfig/api.js'
import {
  resetPassword,
  exportUsers,
  editUser,
  delUser,
  addUser,
  users
} from '@/api/apiConfig/user.js'
import { companyBatchVerify, getAssociate, kehuCompanyList } from '@/api/apiConfig/surveying.js'

let roleArr = [
  {
    name: '超级管理员',
    value: 1
  },
  {
    name: '安服人员',
    value: 2
  },
  {
    name: '企业租户',
    value: 3
  },
  {
    name: '售后人员',
    value: 4
  }
]
let levelArr = [
  {
    name: '测试用户',
    value: 0
  },
  {
    name: '正式用户',
    value: 100
  } /*,{
  name: 'VIP',
  value: 200
}*/
]
let statusArr = [
  {
    name: '禁用',
    value: 0
  },
  {
    name: '启用',
    value: 1
  }
]
let ipAssetsArr = [
  {
    name: 100,
    value: 100
  },
  {
    name: 1000,
    value: 1000
  },
  {
    name: 10000,
    value: 10000
  },
  {
    name: '自定义',
    value: -1
  }
]
let timeArr = [
  {
    name: '每月一次',
    value: 1
  },
  {
    name: '每两个月一次',
    value: 2
  },
  {
    name: '每三个月一次',
    value: 3
  }
]
// const usersForm = {

// }
const defaultRuleForm = {
  name: '',
  mobile: '',
  email: '',
  status: 1,
  is_test: 0, // 0否1是，默认0
  is_real_customer: 0, // 0否1是，默认0
  has_subdomain_tab: 0, // 0否1是，默认0
  detect_company_name: null,
  priority: 0, // 用户优先级
  password: '',
  checkPassword: '',
  role: 2,
  level: 0,
  expires_at: '',
  safe_company_id: [],
  show_poc: 0,
  black_ip_switch: 0,
  company: {
    name: '',
    area_code_all: [],
    introduce: '',
    industry_id: '',
    limit_ip_asset: 100,
    limit_ip_asset_val: '',
    limit_cloud_recommend: '',
    new_asset_rate_s: 0,
    new_asset_rate: 1,
    limit_new_asset: '10000',
    limit_poc_scan: '',
    data_leak_rate_s: 0,
    data_leak_rate: 1,
    limit_data_leak: '10000',
    limit_monitor_keyword: '',
    black_ip_switch: 0
  },
  need_asset_mapping: 2,
  target_company_name: '',
  other_mapping_companies: '',
  percent:'0'
}
let id = 0
export default {
  components: { tableTooltip, hightFilter, batchValidate },

  data() {
    var checkEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入邮箱'))
      } else {
        let reg = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.]){1,2}[A-Za-z\d]{2,5}$/g
        if (!reg.test(value)) {
          callback(new Error('邮箱格式不正确'))
        }
        callback()
      }
    }
    var checkMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入手机号'))
      } else {
        let reg = /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/
        let regHongkongPhone = /^(5|6|8|9)\d{7}$/
        if (!reg.test(value) && !regHongkongPhone.test(value)) {
          callback(new Error('手机号格式不正确'))
        }
        callback()
      }
    }
    var checkPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        // let reg = /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{13,}$/
        let reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{13,}$/
        if (!reg.test(value)) {
          callback(new Error('密码正确格式：12位以上 数字、大小写字母、特殊符号'))
        }
        if (this.ruleForm.checkPassword !== '') {
          this.$refs.ruleForm.validateField('checkPassword')
        }
        callback()
      }
    }
    var checkPassword2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.ruleForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    var checkPassword22 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        // let reg = /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{13,}$/
        let reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{13,}$/
        if (!reg.test(value)) {
          callback(new Error('密码正确格式：12位以上 数字、大小写字母、特殊符号'))
        }
        if (this.passwordRuleForm.checkPassword !== '') {
          this.$refs.passwordRuleForm.validateField('checkPassword')
        }
        callback()
      }
    }
    var checkPassword222 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.passwordRuleForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      companysList: [],
      restaurants: [],
      kehuCompanyResults: [],
      detect_company_name: '',
      correctCompanyList: [],
      textarea: '',
      correctList: [],
      errorList: [],
      validDialogVisible: false,
      scopeArr: {},
      highTabShow: [
        {
          label: '用户名',
          name: 'name',
          type: 'input'
        },
        {
          label: '手机',
          name: 'mobile',
          type: 'input'
        },
        {
          label: '邮箱地址',
          name: 'email',
          type: 'input'
        },
        {
          label: '用户类型',
          name: 'role',
          type: 'select'
        },
        {
          label: '状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '企业名称',
          name: 'company',
          type: 'input'
        },
        {
          label: '创建时间',
          name: 'created_at_range',
          type: 'date'
        },
        {
          label: '到期时间',
          name: 'expires_at',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      checkedAll: false,
      formInline: {
        keyword: '',
        name: '',
        mobile: '',
        email: '',
        role: [],
        status: '',
        created_at_range: [],
        company: ''
      },
      data: [],
      filterMethod(query, item) {
        return item.pinyin.indexOf(query) > -1
      },
      rules: {
        name: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        mobile: [{ required: true, validator: checkMobile, trigger: 'change' }],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '邮箱格式不正确', trigger: ['blur', 'change'] }
        ],
        password: [{ required: true, validator: checkPassword, trigger: 'blur' }],
        checkPassword: [{ required: true, validator: checkPassword2, trigger: 'blur' }],
        level: [{ required: true, message: '请选择用户属性', trigger: 'change' }],
        expires_at: [{ required: true, message: '请输入到期日期时间', trigger: 'change' }],
        priority: [{ required: true, message: '请输入用户优先级', trigger: 'change' }],
        company: {
          name: [{ required: true, message: '请输入企业名称', trigger: 'change' }],
          area_code_all: [{ required: true, message: '请选择企业所在地区', trigger: 'change' }],
          industry_id: [{ required: true, message: '请选择行业', trigger: 'change' }],
          limit_ip_asset_val: [{ required: true, message: '请输入IP资产数量', trigger: 'blur' }],
          limit_cloud_recommend: [
            { required: true, message: '请输入云端推荐次数', trigger: 'blur' }
          ],
          limit_new_asset: [{ required: true, message: '请输入数字资产推荐次数', trigger: 'blur' }],
          limit_poc_scan: [{ required: true, message: '请输入漏洞管理推荐次数', trigger: 'blur' }],
          limit_data_leak: [{ required: true, message: '请输入数据泄露推荐次数', trigger: 'blur' }],
          limit_monitor_keyword: [{ required: true, message: '请输入关键词数量', trigger: 'blur' }]
        },
        target_company_name: [{ required: true, message: '请输入企业名称', trigger: 'change' }],
        need_asset_mapping: [{ required: true }],
      },
      ruleForm: JSON.parse(JSON.stringify(defaultRuleForm)),
      dialogFormVisibleFenzu: false,
      proList: [],
      portList: [],
      tableIsShow: true,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      priorityArr: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      tableHeader: [
        {
          label: '用户名',
          name: 'name',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '手机',
          name: 'mobile',
          minWidth: 100
        },
        {
          label: '邮箱',
          name: 'email',
          minWidth: 100
        },
        {
          label: '用户类型',
          name: 'role',
          minWidth: 100
        },
        {
          label: '测试人员',
          name: 'is_test',
          minWidth: 70
        },
        {
          label: '相关安服账号',
          name: 'controled_safe_users',
          minWidth: 90
        },
        {
          label: '客户',
          name: 'is_real_customer',
          minWidth: 50
        },
        {
          label: '客户可测绘企业',
          name: 'detect_company_name',
          minWidth: 110
        },
        {
          label: '子域名权限',
          name: 'has_subdomain_tab',
          minWidth: 90
        },
        {
          label: '企业名称',
          name: 'companyName',
          minWidth: 120
        },
        {
          label: '状态',
          name: 'status',
          minWidth: 70
        },
        {
          label: 'secret_key',
          name: 'secret_key'
        },
        {
          label: '创建时间',
          name: 'created_at',
          minWidth: 140
        },
        {
          label: '到期时间',
          name: 'expires_at',
          minWidth: 140
        }
      ],
      loading: false,
      otherLoading: false,
      roleArr,
      levelArr,
      statusArr,
      ipAssetsArr,
      timeArr,
      industryArr: [],
      highCheckdialog: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < new Date().getTime() - 86400000
        }
      },
      resetPasswordVisible: false,
      cascaderProps: {
        lazy: true,
        lazyLoad: this.lazyLoad
      },
      dialogRow: '',
      userMessage: '',
      checkedArr: [],
      passwordRuleForm: {
        password: '',
        checkPassword: ''
      },
      passwordRules: {
        password: [{ required: true, validator: checkPassword22, trigger: 'change' }],
        checkPassword: [{ required: true, validator: checkPassword222, trigger: 'change' }]
      },
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      companyArr: [],
      queryVisible: false,
      real_customer_company: []
    }
  },
  created() {
    this.userMessage = JSON.parse(sessionStorage.getItem('userMessage'))
    //this.userMessage.user.role = 3
    if (this.userMessage.user.role == 3) {
      //企业租户
      this.tableHeader.splice(3, 1)
    }
  },
  async mounted() {
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳至'
    this.getCname()
    this.userData()
    this.industryData()
    this.companyData()
  },

  methods: {
    changeRole() {
      this.$refs['ruleForm'].clearValidate()
    },
    companyOtherValidate(val) {
      return new Promise(async (resolve, reject) => {
        if (val.length == 0) return
        // this.sureLoading = true
        let res = await companyBatchVerify({
          name: val,
          operate_company_id: this.currentCompany
        }).catch((error) => {
          this.sureLoading = false
          this.otherLoading = false
          reject()
        })
        if (res.code == 0 && res.data && res.data.error_list.length > 0) {
          // 代表存在错误企业名称
          this.errorList = res.data.error_list || []
          this.correctCompanyList = res.data.correct_list || []
          this.otherLoading = false
          resolve(true)
        } else {
          this.correctCompanyList = res.data.correct_list || []
          // this.otherLoading = false
          resolve(false)
        }
        // this.sureLoading = false
      })
    },
    validateConfirm() {
      this.detect_company_name = JSON.parse(JSON.stringify(this.correctCompanyList.join('\r')))
      let copyErrorList = this.errorList
      this.$copyText(copyErrorList.join('\r')).then(
        (res) => {
          this.otherCompanyValidVisible = false
          this.$message.success('复制成功')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
      this.validDialogVisible = false
    },
    copyText(val) {
      this.textarea = this.correctList.join('\r')
      this.$copyText(val.join('\r')).then(
        (res) => {
          this.$message.success('复制成功')
          this.validDialogVisible = false
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    async getCname() {
      let res = await scope_golang_client_account()
      if (res.code == 0) {
        let filterArr = ['*', 'safe', 'sale', 'tenant']
        for (let key in res.data) {
          if (!filterArr.includes(key)) {
            this.scopeArr[key] = res.data[key]
          }
        }
      }
    },
    companyChange(val) {
      if (!val) {
        this.real_customer_company = []
      }
    },
    async remoteMethod(val) {
      if (val !== '') {
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        this.checkedTree = []
        this.real_customer_company = []
        let obj = {
          name: val,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })
            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.real_customer_company = a
          }
        }, 200)
      } else {
        this.real_customer_company = []
      }
    },
    setShow(index) {
      this.tableData[index].copyIsShow = true
    },
    setHide(index) {
      this.tableData[index].copyIsShow = false
    },
    copyFun(val) {
      let that = this
      this.$copyText(val).then(
        function (e) {
          that.$message.success('已复制到剪贴板！')
        },
        function (e) {}
      )
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },

    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.userData(true)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.userData(true)
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      if (!this.formInline.role.includes(3)) {
        this.formInline.company = ''
      }
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.userData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    }, 
    querySearch(queryString) {
      let restaurants = this.restaurants
      this.kehuCompanyResults = []
      // 调用 callback 返回建议列表的数据
      if (queryString !== '') {
        // 有输入值，自动关联企业
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        let obj = {
          name: queryString,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })
            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.kehuCompanyResults = [...a]
          }
        }, 200)
      } else {
        // 没有输入值，展示客户可以测绘的企业
        this.kehuCompanyResults = queryString
          ? restaurants.filter(this.createFilter(queryString))
          : restaurants
      }
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    async getSearchData() {
      let res = await kehuCompanyList({ operate_company_id: this.currentCompany })
      let data = []
      if (res.data && res.data.length > 0) {
        res.data.map((v) => {
          data.push({ value: v })
        })
      }
      this.restaurants = data
      this.kehuCompanyResults = data
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    async insertSave(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (!valid) {
          return false
        }
        let params = JSON.parse(JSON.stringify(this.ruleForm))
        if (params.role != '3') {
          params.detect_company_name = [] // 超管、安服不用配置可测绘企业，直接置空，后端后数组校验
          delete params.company
          delete params.expires_at
        } else {
          // 企业账号
          let codeArr = ['province_code', 'city_code', 'area_code']
          params.company.area_code_all.forEach((v, index) => {
            params.company[codeArr[index]] = v
          })

          //字段值处理
          let company = params.company
          if (company.limit_ip_asset_val <= 0 && company.limit_ip_asset == -1) {
            this.$message.error('IP资产数量必须大于0')
            return
          }
          if (
            (company.new_asset_rate_s > 0 || company.data_leak_rate_s > 0) &&
            company.limit_monitor_keyword <= 0
          ) {
            this.$message.error('关键词数量必须大于0')
            return
          } else {
            company.limit_monitor_keyword =
              company.limit_monitor_keyword == '' ? 0 : company.limit_monitor_keyword
          }
          if (company.limit_ip_asset == -1) {
            company.limit_ip_asset = company.limit_ip_asset_val
          }
          if (company.new_asset_rate_s == 0) {
            company.new_asset_rate = 0
            company.limit_new_asset = 0
          } else {
            company.limit_new_asset = '10000'
          }
          if (params.show_poc === 0) {
            company.limit_poc_scan = 0
          }
          if (company.data_leak_rate_s == 0) {
            company.data_leak_rate = 0
            company.limit_data_leak = 0
          } else {
            company.limit_data_leak = '10000'
          }
          // 客户可测绘企业批量校验弹窗
          let detect_company_name = this.detect_company_name
          if (this.detect_company_name) {
            let otherCompany = detect_company_name
              ? detect_company_name.split(/[；|;|\r\n]/).filter((item) => {
                  return item.trim()
                })
              : []
            let validateRes = await this.companyOtherValidate(otherCompany).catch((error) => {
              console.log(error)
            })
            if (validateRes) {
              this.validDialogVisible = true
              return
            }
          } else {
            this.correctCompanyList = []
            this.errorList = []
          }
          params.detect_company_name = JSON.parse(JSON.stringify(this.detect_company_name))
            .split(/[；|;|\r\n]/)
            .filter((item) => {
              return item.trim()
            })
        }
        //密码
        if (this.dialogRow == '') {
          params.password = sha1(params.password)
          delete params.checkPassword
        }
        this.otherLoading = true

        if(params.need_asset_mapping == 1 && params.other_mapping_companies){
            params.other_mapping_companies = params.other_mapping_companies.split(/[；|;|\n]/).filter((item) => {
              return item.trim()
            })
            console.log(params.other_mapping_companies)
            let validateRes = await this.companyOtherValidate(params.other_mapping_companies).catch((error) => {
              console.log(error)
            })
            if (validateRes) {
              this.validDialogVisible = true
              return
            }
          }else{
            params.other_mapping_companies = []
            console.log(params.other_mapping_companies)
          }
        let res
        if (this.dialogRow == '') {
          if (params.company) {
            params.company.black_ip_switch = params.black_ip_switch
          }
          console.log(params)
          res = await addUser(params).catch(() => (this.otherLoading = false))
        } else {
          if (params.company) {
            params.company.black_ip_switch = params.black_ip_switch
          }
          res = await editUser({ id: this.dialogRow, query: params }).catch(
            () => (this.otherLoading = false)
          )
        }
        this.otherLoading = false
        if (res.code == 0) {
          this.dialogFormVisibleFenzu = false
          this.$message.success('操作成功！')
          this.userData()
          if (params.role == 3) {
            this.companyData()
          }
        }
      })
    },
    resetForm(ref) {
      this.$refs[ref].resetFields()
      this.formInline = {
        keyword: '',
        name: '',
        mobile: '',
        email: '',
        role: [],
        status: '',
        created_at_range: [],
        company: ''
      }
    },
    async removeOne(id) {
      this.$confirm('确定删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'user_del_cancel',
        confirmButtonClass: 'user_del_sure',
        customClass: 'user_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await delUser({ id: [id] })
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.currentPage = this.updateCurrenPage(
              this.total,
              [1],
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.userData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.user_del>.el-message-box__btns')
        del.children[0].id = 'user_del_cancel'
        del.children[1].id = 'user_del_sure'
      }, 50)
    },
    userData(tmp) {
      if (!tmp) {
        // 调用之前清空选择项
        this.checkedAll = false
        if (this.$refs.eltable != undefined) {
          this.$refs.eltable.clearSelection()
        }
      }

      this.loading = true
      this.formInline.created_at_range = this.formInline.created_at_range
        ? this.formInline.created_at_range
        : []
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline
      }
      users(obj)
        .then((res) => {
          this.loading = false
          if (res.code != 0) {
            this.$message.error(res.message)
            return
          }
          res.data.items.forEach((item) => {
            item.copyIsShow = false
          })
          this.tableData = res.data.items
          this.total = res.data.total
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch((error) => {
          this.loading = false
        })
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    addFun(record) {
      this.detect_company_name = ''
      this.ruleForm.need_asset_mapping = 2
      if (record) {
        // 编辑
        let r = JSON.parse(JSON.stringify(record))
        if (r.role == 3) {
          // 企业账号
          let company = r.company
          company.area_code_all = [company.province_code, company.city_code, company.area_code]

          let ipAssetsValArr = this.ipAssetsArr.map((item) => {
            return item.value
          })
          if (!ipAssetsValArr.includes(r.company.limit_ip_asset)) {
            r.company.limit_ip_asset_val = r.company.limit_ip_asset
            r.company.limit_ip_asset = -1
          }
          if (r.company.new_asset_rate > 0) {
            r.company.new_asset_rate_s = 1
          } else {
            r.company.new_asset_rate = 1
            r.company.limit_new_asset = ''
          }
          if (r.company.limit_poc_scan / 1 > 0) {
            r.show_poc = 1
          } else {
            r.show_poc = 0
            r.company.limit_poc_scan = ''
          }
          if (r.company.data_leak_rate > 0) {
            r.company.data_leak_rate_s = 1
          } else {
            r.company.data_leak_rate = 1
            r.company.limit_data_leak = ''
          }
        } else if (record.role == 2) {
          // 安服账号
          r.safe_company_id = r.safe_company.map((v) => {
            return v.id
          })
        }
        this.ruleForm = r
        this.dialogRow = r.id

        if (r.detect_company_name) {
          // 可测绘企业回显
          this.real_customer_company = []
          let real_customer_company = r.detect_company_name
            ? JSON.parse(r.detect_company_name).join('\r')
            : []
          this.detect_company_name = real_customer_company
          // real_customer_company.forEach(item => {
          //   this.real_customer_company.push({value: item})
          // })
        }
        this.$forceUpdate()
      } else {
        // 新增
        this.ruleForm = JSON.parse(JSON.stringify(defaultRuleForm))
        this.dialogRow = ''
      }
      this.dialogFormVisibleFenzu = true
      this.$nextTick(() => {
        this.$refs['ruleForm'].resetFields()
      })
    },
    async industryData() {
      let res = await industry()
      if (res.code == 0) {
        this.industryArr = res.data
      }
    },
    async companyData() {
      let res = await companyList()
      if (res.code == 0) {
        this.companyArr = res.data
      }
    },
    async lazyLoad(node, resolve) {
      let level = node.level
      let res
      if (!node.data) {
        res = await area('')
      } else {
        res = await area(node.data.value)
      }
      if (res.code == 0) {
        const nodes = Array.from(res.data).map((item) => ({
          value: item.adcode,
          label: item.name,
          leaf: level >= 2
        }))
        resolve(nodes)
      }
    },
    async exportExcel() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      let params = {
        ...this.formInline,
        id: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            })
      }
      let res = await exportUsers(params)
      if (res.code == 0) {
        this.download(this.showSrcIp + res.data.url)
        this.checkedAll = false
        this.$refs.eltable.clearSelection()
      }
    },
    resetPasswordShow(record) {
      this.passwordRuleForm = {
        password: '',
        checkPassword: ''
      }
      this.dialogRow = record.id
      this.$nextTick(() => {
        this.$refs.passwordRuleForm.resetFields()
      })
      this.resetPasswordVisible = true
    },
    async passwordInsertSave(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          return false
        }
        let obj = {
          id: this.dialogRow,
          password: sha1(this.passwordRuleForm.password)
        }
        resetPassword(obj)
          .then((res) => {
            this.otherLoading = false
            if (res.code != 0) {
              this.$message.error(data.message)
              return false
            }
            this.resetPasswordVisible = false
            this.$message.success('操作成功！')
          })
          .catch((error) => {
            this.otherLoading = false
          })
      })
    },
    tooltipContent(record, name) {
      if (record.role == 3) {
        let company = record.company
        if (company) {
          let common = `行业：${
            this.industryArr.length > 0 &&
            this.industryArr.find((item) => item.id == company.industry_id).name
          }，到期日期：${record.expires_at.substring(0, 10)}`
          if (name == 'name') {
            return `企业名称：${company && company.name ? company.name : ''}，${common}`
          }
          if (name == 'companyName') {
            return `地区：${record.province}-${record.city}-${record.area}，${common}`
          }
        }
      } else {
        return record[name]
      }
    },
    limit(record) {
      let r = JSON.parse(JSON.stringify(record))
      let title = ''
      let leabelWidth = 120
      if (record.role == 3) {
        let { company } = r
        r.level_label = this.levelArr.find((v) => v.value == r.level).name
        if (company.new_asset_rate > 0) {
          let obj = this.timeArr.find((v) => v.value == company.new_asset_rate)
          if (obj != undefined) {
            company.new_asset_rate_text = obj.name
          }
        }
        if (company.limit_poc_scan > 0) {
          r.show_poc = 1
        }
        if (company.data_leak_rate > 0) {
          let obj = this.timeArr.find((v) => v.value == company.data_leak_rate)
          if (obj != undefined) {
            company.data_leak_rate_text = obj.name
          }
        }
        title = '查看授权'
      }
      if (record.role == 2) {
        title = '查看管理企业'
        leabelWidth = 0
      }
      r.title = title
      r.leabelWidth = leabelWidth
      this.ruleForm = r
      this.queryVisible = true
    }
  },
  watch: {
    tableData(val) {
      this.doLayout(this, 'eltable')
    }
  },
  computed: {}
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left {
      height: 100%;
      display: flex;
      /deep/.el-textarea {
        width: 300px;
        height: 368px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d1d5dd;
        .el-textarea__inner {
          height: 100%;
          border: 0 !important;
        }
        .el-textarea__inner:hover {
          border: 0;
        }
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
  /deep/ .el-input__icon {
    line-height: 32px;
  }
  .add-dialog {
    width: 100%;
    padding: 0 !important;
    height: 500px;
    overflow-y: auto;
    /deep/ .el-form-item {
      width: 98% !important;
    }
  }
  .empower-form {
    padding-bottom: 28px !important;
    /deep/ .el-form-item {
      width: 50% !important;
      float: left;
      margin: 5px 0 !important;
    }
    .item {
      overflow: hidden;
      border-top: 1px solid #e9ebef;
      &:first-child {
        border-color: transparent;
      }
    }
  }
}
.elDialogAdd {
  /deep/.el-cascader {
    width: 100%;
  }
}
.one-line {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.f-label {
  color: #2677ff;
  background: rgba(38, 119, 255, 0.12);
  padding: 1px 4px;
  margin-right: 8px;
  transform: scale(0.8);
  display: inline-block;
}
/deep/#user_select {
  margin-left: 10px;
}
</style>
