<template>
  <div class="container">
    <div class="headerTitle">威胁词库</div>
    <div class="content">
      <div class="left">
        <div class="headerTitle title1">关键词审核</div>
        <div class="filterTab">
          <div>
            <el-input
              v-model="formInline.search"
              placeholder="请输入关键字检索"
              @keyup.enter.native="checkFuncList('left', 'Left')"
              id="user_keycheck"
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="checkFuncList('left', 'Left')"
              ></el-button>
            </el-input>
            <el-select
              v-model="formInline.status"
              placeholder="请选择审核状态"
              @change="changeStatus($event, 'status', statusArr, true, true, 'left', 'Left')"
              multiple
              clearable
              collapse-tags
              id="user_select"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in statusArr"
                :key="v.value"
              ></el-option>
            </el-select>
            <span @click="highCheckdialog = true" id="user_filter" style="width: 80px"
              ><img
                src="../../assets/images/filter.png"
                alt=""
                style="width: 16px; vertical-align: middle; margin-right: 3px"
              />高级筛选</span
            >
          </div>
          <div>
            <el-checkbox
              class="checkboxAll"
              id="user_all"
              v-model="leftCheckAll"
              @change="() => checkAllChange('left')"
              >选择全部</el-checkbox
            >
            <el-dropdown class="dropdownClass" trigger="click">
              <el-button class="normalBtn" type="primary" id="account_deal"
                >审核<i class="el-icon-arrow-down el-icon--right"></i
              ></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  @click.native="auditConfirm('', '1', 'more')"
                  id="account_deal_place"
                  >通过</el-dropdown-item
                >
                <el-dropdown-item
                  @click.native="auditConfirm('', '2', 'more')"
                  id="account_deal_place"
                  >驳回</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <hightFilter
          id="hightFilter"
          :highTabShow="highTabShow"
          :highlist="highlist"
          :total="leftTotal"
          pageIcon="user"
          @highcheck="highCheck"
        ></hightFilter>
        <div :class="hightFilterIsShow()">
          <el-table
            border
            :data="leftTableData"
            v-loading="loading"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            @selection-change="(val) => handleSelectionChange(val, 'left')"
            @cell-mouse-enter="showTooltip"
            @cell-mouse-leave="hiddenTooltip"
            ref="leftEltable"
            height="100%"
            style="width: 100%"
          >
            <el-table-column
              type="selection"
              align="center"
              :reserve-selection="true"
              :selectable="() => handleSelectable('left')"
              width="55"
            >
            </el-table-column>
            <el-table-column prop="keyword" label="关键词"> </el-table-column>
            <el-table-column prop="type_name" label="类型"> </el-table-column>
            <el-table-column prop="created_at" label="创建时间"> </el-table-column>
            <el-table-column prop="username" label="创建用户"> </el-table-column>
            <el-table-column prop="status" label="审核状态">
              <template slot-scope="{ row }">
                {{ statusArrMap[row.status] }}
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="150">
              <template slot-scope="{ row }" v-if="row.status == 0">
                <el-button type="text" @click="edit(row.id)">编辑</el-button>
                <el-button type="text" @click="auditConfirm(row.id, '1')">通过</el-button>
                <el-button type="text" @click="auditConfirm(row.id, '2')">驳回</el-button>
              </template>
            </el-table-column>
          </el-table>
          <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
        </div>
        <div class="pagination">
          <el-pagination
            @size-change="(val) => handleSizeChange(val, 'Left', 'left')"
            @current-change="(val) => handleCurrentChange(val, 'Left', 'left')"
            :current-page="leftCurrentPage"
            :page-sizes="[10, 30, 50, 100]"
            :page-size="leftPageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="leftTotal"
          >
          </el-pagination>
        </div>
      </div>
      <div class="middle-line"></div>
      <div class="right">
        <div class="headerTitle title1">系统词库</div>
        <div class="filterTab">
          <div>
            <el-input
              v-model="keywordSearch"
              placeholder="请输入关键字检索"
              @keyup.enter.native="checkFuncList('right', 'Right')"
              id="user_keycheck"
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="checkFuncList('right', 'Right')"
              ></el-button>
            </el-input>
          </div>
          <div>
            <el-checkbox
              class="checkboxAll"
              id="user_all"
              v-model="rightCheckAll"
              @change="() => checkAllChange('right')"
              >选择全部</el-checkbox
            >
            <!-- <el-button class="normalBtnRe" style="margin-left: 10px;" type="primary" id="user_del" @click="delKeyword('','more')">删除</el-button> -->
            <el-dropdown class="dropdownClass" trigger="click">
              <el-button class="normalBtn" type="primary" id="account_deal"
                >更新状态<i class="el-icon-arrow-down el-icon--right"></i
              ></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="editUseStatus(1)" id="account_deal_place"
                  >启用</el-dropdown-item
                >
                <el-dropdown-item @click.native="editUseStatus(2)" id="account_deal_place"
                  >禁用</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
            <el-dropdown class="dropdownClass" trigger="click">
              <el-button class="normalBtn" type="primary" id="account_deal"
                >新建<i class="el-icon-arrow-down el-icon--right"></i
              ></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="addKeyword()" id="account_deal_place"
                  >新建关键词</el-dropdown-item
                >
                <el-dropdown-item @click.native="addType()" id="account_deal_place"
                  >新建类型</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="table">
          <el-table
            border
            :data="rightTableData"
            v-loading="loading"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            @selection-change="(val) => handleSelectionChange(val, 'right')"
            @cell-mouse-enter="showTooltip"
            @cell-mouse-leave="hiddenTooltip"
            ref="rightEltable"
            height="100%"
            style="width: 100%"
          >
            <el-table-column
              type="selection"
              align="center"
              :reserve-selection="true"
              :selectable="() => handleSelectable('right')"
              width="55"
            >
            </el-table-column>
            <el-table-column prop="keyword" label="关键词"> </el-table-column>
            <el-table-column prop="type_name" label="类型"> </el-table-column>
            <el-table-column prop="enable" label="状态">
              <template slot-scope="{ row }">
                {{ row.enable == 1 ? '启用' : '禁用' }}
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间"> </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="{ row }">
                <!-- <el-button type="text" @click="delKeyword(row.id)">删除</el-button> -->
                <el-button type="text" v-if="row.enable == 2" @click="editUseStatus(1, row.id)"
                  >启用</el-button
                >
                <el-button type="text" v-if="row.enable == 1" @click="editUseStatus(2, row.id)"
                  >禁用</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
        </div>
        <div class="pagination">
          <el-pagination
            @size-change="(val) => handleSizeChange(val, 'Right', 'right')"
            @current-change="(val) => handleCurrentChange(val, 'Right', 'right')"
            :current-page="rightCurrentPage"
            :page-sizes="[10, 30, 50, 100]"
            :page-size="rightPageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="rightTotal"
            :pager-count="5"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="关键词：" prop="keyword">
            <el-input v-model="formInline.keyword" placeholder="请输入关键词"></el-input>
          </el-form-item>
          <el-form-item label="类型：" prop="type_id">
            <el-select
              v-model="formInline.type_id"
              placeholder="请选择类型"
              multiple
              collapse-tags
              @change="selectChange($event, 'type_id', typeSelectList, true, true)"
            >
              <el-option
                :label="v.type_name"
                :value="v.id"
                v-for="v in typeSelectList"
                :key="v.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态：" prop="status">
            <el-select
              v-model="formInline.status"
              placeholder="请选择审核状态"
              multiple
              clearable
              collapse-tags
              @change="selectChange($event, 'status', statusArr, true, true)"
            >
              <el-option
                :label="v.name"
                :value="v.value"
                v-for="v in statusArr"
                :key="v.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建用户：" prop="user_id">
            <!-- <el-input v-model="formInline.user_id" placeholder="请选择创建用户"></el-input> -->
            <el-select
              v-model="formInline.user_id"
              placeholder="请选择创建用户"
              filterable
              multiple
              collapse-tags
              @change="selectChange($event, 'user_id', userList, true, true)"
            >
              <el-option
                :label="v.name"
                :value="v.id"
                v-for="v in userList"
                :key="v.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间：" prop="created_at">
            <el-date-picker
              v-model="formInline.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm('drawerForm')" id="user_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList('left', 'Left')" id="user_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="editDialogVisible"
      width="400px"
    >
      <template slot="title">
        <span>编辑</span>
      </template>
      <div class="dialog-body edit">
        <el-form
          :model="editForm"
          :rules="editRules"
          style="padding: 0 !important"
          ref="addRuleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="分类" prop="name">
            <!-- <el-input v-model="auditForm.name" placeholder="请选择分类"></el-input> -->
            <el-select v-model="editForm.typeId" placeholder="请选择威胁类型">
              <el-option
                v-for="item in typeSelectList"
                :label="item.type_name"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="editDialogVisible = false" id="scan_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" @click="editConfirm" id="scan_sure" :loading="editBtnLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="newKeywordVisible"
      width="400px"
    >
      <template slot="title">
        <span>新增关键词</span>
      </template>
      <div class="dialog-body">
        <el-form
          :model="addKeywordForm"
          :rules="addKeywordRules"
          style="padding: 0 !important"
          ref="addRuleForm"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="威胁类型" prop="name">
            <!-- <el-input v-model="auditForm.name" placeholder="请选择分类"></el-input> -->
            <el-select v-model="addKeywordForm.typeId" placeholder="请选择威胁类型">
              <el-option
                v-for="item in typeSelectList"
                :label="item.type_name"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关键词" prop="name">
            <el-input
              type="textarea"
              :rows="6"
              v-model="addKeywordForm.newKeyword"
              style="font-size: 16px; font-weight: bold; color: #8c939d"
              placeholder="请输入需要新增的关键词，可以输入多个，多个关键词使用分号或换行分隔"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="newKeywordVisible = false" id="scan_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="addKeywordConfirm"
          id="scan_sure"
          :loading="newKeywordLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="newTypeVisible"
      width="400px"
    >
      <template slot="title">
        <span>新建类型</span>
      </template>
      <div class="dialog-body">
        <el-form
          :model="addTypeForm"
          :rules="addTypeRules"
          style="padding: 0 !important"
          ref="addRuleForm"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="已有类型" prop="name">
            <el-input
              readonly
              type="textarea"
              :rows="6"
              v-model="typeSelectListString"
              style="font-size: 16px; font-weight: bold; color: #8c939d"
              placeholder="暂无"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="输入类型" prop="name">
            <el-input
              type="textarea"
              :rows="6"
              v-model="addTypeForm.newType"
              style="font-size: 16px; font-weight: bold; color: #8c939d"
              placeholder="请输入需要新增的类型，可以输入多个，多个类型使用分号或换行分隔"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="newTypeVisible = false" id="scan_cancel"
          >关闭</el-button
        >
        <el-button
          class="highBtn"
          @click="addTypeConfirm"
          id="scan_sure"
          :loading="newKeywordLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import hightFilter from '../../components/assets/highTab.vue'

import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'

import {
  pgdStatusUpdate,
  pgdAuditList,
  pgdAuditAction,
  pgdSystemList,
  pgdSystemAdd,
  pgdSystemDel,
  pgdTypeAdd,
  pgdTypeList,
  pgdUserList
} from '@/api/apiConfig/api.js'

export default {
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      keywordSearch: '',
      typeSelectListString: '',
      addTypeRules: {},
      addTypeForm: {
        newType: ''
      },
      newTypeVisible: false,
      newTypeLoading: false,
      newKeyword: '',
      addKeywordRules: {},
      addKeywordForm: {
        newKeyword: '',
        typeId: ''
      },
      newKeywordVisible: false,
      newKeywordLoading: false,
      leftCheckAll: false,
      leftCheckedArr: [],
      rightCheckAll: false,
      rightCheckedArr: [],
      highlist: null,
      highTabShow: [
        {
          label: '关键词',
          name: 'keyword',
          type: 'input'
        },
        {
          label: '类型',
          name: 'type_id',
          type: 'select'
        },
        {
          label: '处理状态',
          name: 'status',
          type: 'select'
        },
        {
          label: '创建用户',
          name: 'user_id',
          type: 'select'
        },
        {
          label: '创建时间',
          name: 'created_at',
          type: 'date'
        }
      ],
      editDialogVisible: false,
      checkedAll: false,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      loading: false,
      leftTableData: [],
      leftTotal: 0,
      leftCurrentPage: 1,
      leftPageSize: 10,
      rightTableData: [],
      rightTotal: 0,
      rightCurrentPage: 1,
      rightPageSize: 10,
      highCheckdialog: false,
      formInline: {
        // status:null
      },
      statusArr: [
        {
          name: '待审核',
          value: '0'
        },
        {
          name: '审核通过',
          value: '1'
        },
        {
          name: '审核驳回',
          value: '2'
        }
      ],
      statusArrMap: {
        0: '待审核',
        1: '审核通过',
        2: '审核驳回',
        3: '禁用'
      },
      editBtnLoading: false,
      editRules: {},
      editForm: {},
      currentSelectId: '',
      currentActionStatus: '',
      typeSelectList: [],
      userList: []
    }
  },
  mounted() {
    this.getLeftData()
    this.getRightData()
    this.getTypeList()
    this.getUserList()
  },
  methods: {
    async editUseStatus(enable, id) {
      let ids = []
      if (!id) {
        if (!this.rightCheckAll) {
          ids = this.rightCheckedArr.map((item) => item.id)
          if (ids.length == 0) {
            this.$message.error('请选择需要操作的数据')
            return
          }
        }
      } else {
        ids = [id]
        if (ids.length == 0) {
          this.$message.error('请选择需要操作的数据')
          return
        }
      }

      this.$confirm(
        `确定${enable == 1 ? '启用' : '禁用'}${id ? '该条' : '所选'}关键词吗 ? `,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'cloud_info_del_cancel',
          confirmButtonClass: 'cloud_info_del_sure',
          customClass: 'cloud_info_del',
          type: 'warning'
        }
      ).then(async () => {
        let obj = {
          ids,
          enable,
          search: this.keywordSearch
        }
        let res = await pgdStatusUpdate(obj)
        if (res.code == 0) {
          this.$message.success('操作成功')
          this.rightCurrentPage = 1
          this.rightPageSize = 10
          this.getRightData()
          this.rightCheckAll = false
          this.$refs.rightEltable.clearSelection()
        }
      })
    },
    changeStatus(val, name, arr, isCh, isMul, type, getType) {
      this.selectChange(val, name, arr, isCh, isMul)
      this.checkFuncList(type, getType)
    },
    async getUserList() {
      let res = await pgdUserList({ get_all: 1, page: 1, per_page: 1 })
      if (res.code == 0) {
        this.userList = res.data.items
      }
    },
    // 删除关键词
    delKeyword(id, type = 'one') {
      let ids = []
      if (type == 'more') {
        if (!this.rightCheckAll) {
          ids = this.rightCheckedArr.map((item) => item.id)
          if (ids.length == 0) {
            this.$message.error('请选择需要删除的数据')
            return
          }
        }
      } else {
        ids = [id]
        if (ids.length == 0) {
          this.$message.error('请选择需要删除的数据')
          return
        }
      }
      // return
      this.$confirm('确定删除该条关键词吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_info_del_cancel',
        confirmButtonClass: 'cloud_info_del_sure',
        customClass: 'cloud_info_del',
        type: 'warning'
      }).then(async () => {
        let obj = {
          ids,
          search: this.keywordSearch
        }
        let res = await pgdSystemDel(obj)
        if (res.code == 0) {
          this.$message.success('已删除')
          this.rightCurrentPage = 1
          this.rightPageSize = 10
          this.getRightData()
          this.rightCheckAll = false
          this.$refs.rightEltable.clearSelection()
        }
      })
    },
    // 新建关键词
    addKeyword() {
      this.addKeywordForm = {
        typeId: '',
        newKeyword: ''
      }
      this.newKeywordVisible = true
    },
    async addKeywordConfirm() {
      let { typeId, newKeyword } = this.addKeywordForm
      if (!typeId) {
        this.$message.error('请选择归属分类')
        return
      }
      if (!newKeyword) {
        this.$message.error('请输入需要新增的关键词')
        return
      }
      let keyword = newKeyword
        ? newKeyword
            .split(/[；|;|\r\n]/)
            .filter((item) => {
              return item.trim()
            })
            .map((item) => {
              return item.trim()
            })
        : []
      if (keyword && keyword.length == 0) {
        this.$message.error('请输入需要新增的类型')
        return
      }
      let res = await pgdSystemAdd({
        operate_company_id: this.currentCompany,
        keyword,
        type_id: typeId
      }).catch(() => {
        this.newKeywordLoading = false
      })
      if (res && res.code == 0) {
        this.newKeywordVisible = false
        this.newKeywordLoading = false
        this.$message({
          type: 'success',
          message: '新建成功'
        })
        this.getRightData()
      }
    },
    addType() {
      this.addTypeForm = {
        newType: ''
      }
      this.newTypeVisible = true
    },
    // 新建类型
    async addTypeConfirm() {
      let { newType } = this.addTypeForm
      if (!newType) {
        this.$message.error('请输入需要新增的类型')
        return
      }
      let names = newType
        ? newType
            .split(/[；|;|\r\n]/)
            .filter((item) => {
              return item.trim()
            })
            .map((item) => {
              return item.trim()
            })
        : []
      if (names && names.length == 0) {
        this.$message.error('请输入需要新增的类型')
        return
      }
      let res = await pgdTypeAdd({ operate_company_id: this.currentCompany, names }).catch(() => {
        this.newTypeLoading = false
      })
      if (res && res.code == 0) {
        this.newTypeVisible = false
        this.newTypeLoading = false
        this.$message({
          type: 'success',
          message: '新建成功'
        })
        this.getTypeList()
      }
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList('left', 'Left')
    },
    checkAllChange(type) {
      if (this[type + 'CheckAll']) {
        this[type + 'TableData'].forEach((row) => {
          this.$refs[type + 'Eltable'].toggleRowSelection(row, true)
        })
      } else {
        this.$refs[type + 'Eltable'].clearSelection()
      }
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name || ar.type_name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    resetForm(ref) {
      this.formInline = {
        keyword: '',
        type_id: '',
        status: '',
        user_id: '',
        created_at: []
      }
    },
    async getTypeList() {
      let res = await pgdTypeList()
      if (res.code == 0) {
        this.typeSelectList = res.data.items || []
        this.typeSelectListString = res.data.items
          ? res.data.items.map((item) => item.type_name).join('\n')
          : ''
      }
    },
    handleSelectable(type) {
      return !this[type + 'CheckAll']
    },
    edit(id, status) {
      this.editDialogVisible = true
      this.currentSelectId = id
      this.currentActionStatus = status
    },
    async editConfirm() {
      let obj = {
        ids: [this.currentSelectId],
        is_audit: false,
        audit_type_id: this.editForm.typeId
      }

      let res = await pgdAuditAction(obj)
      if (res.code == 0) {
        this.$message.success('编辑成功')
        this.editDialogVisible = false
        this.leftCurrentPage = 1
        this.leftPageSize = 10
        this.getLeftData()
      }
    },
    async auditConfirm(id, status, type = 'one') {
      let ids = []
      if (type == 'more') {
        if (!this.leftCheckAll) {
          if (this.leftCheckedArr.length == 0) {
            this.$message.error('请选择需要审核的数据')
            return
          }
          ids = this.leftCheckedArr.map((item) => item.id)
        }
      } else {
        ids = [id]
        if (ids.length == 0) {
          this.$message.error('请选择需要审核的数据')
          return
        }
      }
      this.$confirm(`确定审核${status == 1 ? '通过' : '驳回'}该条信息?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_info_del_cancel',
        confirmButtonClass: 'cloud_info_del_sure',
        customClass: 'cloud_info_del',
        type: 'warning'
      }).then(async () => {
        let obj = {
          ...this.formInline,
          ids,
          is_audit: true,
          status
        }

        let res = await pgdAuditAction(obj)
        if (res.code == 0) {
          this.$message.success(`已审核${status == 1 ? '通过' : '驳回'}`)
          this.leftCurrentPage = 1
          this.leftPageSize = 10
          this.getLeftData()
          this.rightCurrentPage = 1
          this.rightPageSize = 10
          this.getRightData()

          this.rightCheckAll = false
          this.leftCheckAll = false
          this.$refs.leftEltable.clearSelection()
          this.$refs.rightEltable.clearSelection()
        }
      })
    },
    async getLeftData() {
      let obj = {
        page: this.leftCurrentPage,
        per_page: this.leftPageSize,
        ...this.formInline
      }
      let res = await pgdAuditList(obj)
      if (res.code == 0) {
        this.leftTableData = (res.data && res.data.items) || []
        this.leftTotal = (res.data && res.data.total) || 0
        if (this['leftCheckAll']) {
          this['leftTableData'].forEach((row) => {
            this.$refs['leftEltable'].toggleRowSelection(row, true)
          })
        }
      }
    },
    async getRightData() {
      let obj = {
        page: this.rightCurrentPage,
        per_page: this.rightPageSize,
        search: this.keywordSearch
      }
      let res = await pgdSystemList(obj)
      if (res.code == 0) {
        this.rightTableData = (res.data && res.data.items) || []
        this.rightTotal = (res.data && res.data.total) || 0
        if (this['rightCheckAll']) {
          this['rightTableData'].forEach((row) => {
            this.$refs['rightEltable'].toggleRowSelection(row, true)
          })
        }
      }
    },
    checkFuncList(type, getType) {
      this.highCheckdialog = false
      this[type + 'CurrentPage'] = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      // this.getLeftData()
      this['get' + getType + 'Data']()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'table tableFilter'
      } else {
        bol = 'table'
      }
      return bol
    },
    handleSizeChange(val, type1, type2) {
      this[type2 + 'PageSize'] = val
      let funcName = 'get' + type1 + 'Data'
      this[funcName]()
    },
    handleCurrentChange(val, type1, type2) {
      this[type2 + 'CurrentPage'] = val
      let funcName = 'get' + type1 + 'Data'
      this[funcName]()
    },
    handleSelectionChange(val, type) {
      this[type + 'CheckedArr'] = val
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    }
  }
}
</script>

<style lang="less" scoped>
.checkboxAll {
  margin-right: 5px !important;
}
.container {
  display: inline-block;
  position: relative;
  min-width: 1154px;
  width: 100%;
  height: 100%;
  background: #fff;
  .content {
    width: 100%;
    height: 100%;
    display: flex;
  }
  .left {
    box-sizing: border-box;
    position: relative;
    width: 60%;
    height: 100%;
    padding: 45px 16px 16px;
  }
  .middle-line {
    box-sizing: border-box;
    width: 0;
    height: calc(100% - 40px);
    // margin: 20px 20px 0 20px;
    margin: 20px 0;
    border-left: 1px solid #333;
  }
  .right {
    box-sizing: border-box;
    position: relative;
    width: 40%;
    height: 100%;
    padding: 45px 16px 16px;
  }
  .headerTitle.title1 {
    top: 16px;
    left: 16px;
    // position: none;
  }
  .filterTab {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 16px 0;
    & > div {
      display: flex;
      // flex-direction: row;
      justify-content: space-between;
      align-items: center;
      /deep/.el-input {
        width: 200px;
        .el-input__inner {
          // height: 30px;
          // line-height: 30px;
          border-color: #d1d5dd;
        }
        &:first-child {
          margin-right: 6px;
        }
      }
      /deep/.el-select {
        width: 160px;
        margin-right: 6px;
        .el-input {
          width: 100%;
        }
        .el-input__inner {
          height: 34px;
          line-height: 34px;
          border-color: #d1d5dd;
        }
      }
      & > span {
        font-weight: 400;
        color: #2677ff;
        line-height: 20px;
        &:hover {
          cursor: pointer;
        }
      }
      & > b {
        color: #2677ff;
        margin: 0 16px 0 4px;
      }
      .normalBtnRe {
        margin-right: 0;
      }
    }
  }
  .table {
    height: calc(100% - 100px);
  }
  .tableFilter {
    height: calc(100% - 142px);
  }
}
/deep/.el-button {
  // span{
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  // }
}
.pagination {
  min-width: 100%;
  overflow: auto;
}
/deep/.el-pagination {
  .el-select .el-input {
    min-width: 84px;
    max-width: 90px;
    width: auto !important;
  }
  .el-pagination__jump {
    margin-left: 0;
  }
}
.elDialogAdd {
  /deep/.el-dialog__body {
    min-height: 80px;
  }
}
// .dialog-body{
//   &.edit{
//     min-height: 200px;
//   }
// }
</style>
