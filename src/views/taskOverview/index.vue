<template>
  <div class="mainContainer">
    <div class="headerTitle"> 任务概览 </div>
    <div class="left">
      <taskMap @changeType="changeType" />
    </div>
    <div class="right" v-loading="loading">
      <el-tooltip effect="dark" content="可点击跳转查看详情" placement="top-start">
        <div class="mainTitle" @click="$router.push(taskInfoMap[type].path)">
          <span>{{ taskInfoMap[type].name }}</span>
          <img class="rightArrow" src="../../assets/images/arrowRight.png" alt="" />
        </div>
      </el-tooltip>
      <div class="intro">
        {{ taskInfoMap[type].intro }}
      </div>
      <div class="content">
        <div class="title">近期任务记录</div>
        <template v-if="taskInfo.id">
          <div class="module">
            <div class="subTitle">
              <svg class="icon svg-icon serial" aria-hidden="true">
                <use xlink:href="#icon-diamondNum"></use>
              </svg>
              任务参数
            </div>
            <div class="subContent taskParams">
              <div class="name">
                <el-tooltip effect="dark" :content="taskInfo.name" placement="top">
                  <span>{{ taskInfo.name }}</span>
                </el-tooltip>
              </div>
              <div class="item">
                <span class="label">开始时间：</span>
                <span class="value">{{ taskInfo.created_at }}</span>
              </div>
              <div class="item" v-if="type == 'activeUnitArr' || type == 'activePhishingArr'">
                <span class="label">任务模式：</span>
                <span class="value">{{ taskInfo.mode }}</span>
              </div>
              <div class="item">
                <span class="label">任务进度：</span>
                <span class="value" v-if="isFinish">已完成</span>
                <span class="value" v-else-if="type == 'activeScanArr'"> {{ currentStep }}</span>
                <span class="value progress" v-else
                  >{{ taskInfo.step }}/{{ currentStepArr.length }} {{ currentStep }}</span
                >
              </div>
            </div>
          </div>
          <div class="module result">
            <div class="subTitle">
              <svg class="icon svg-icon serial" aria-hidden="true">
                <use xlink:href="#icon-diamondNum"></use>
              </svg>
              任务结果
            </div>
            <!-- 单位资产测绘结果 -->
            <div class="subContent taskRes" v-if="type == 'activeUnitArr'">
              <div class="tip">输出结果如下：</div>
              <div class="res-content">
                <p class="res"
                  >1. 本次任务共收集到
                  <span class="num">{{ taskInfo.clue_icp_num || 0 }}</span>
                  条线索，包含手动添加以及自动收集；</p
                >
                <p class="res"
                  >2. 关联发现资产
                  <span class="num">{{ taskInfo.all_ip_num || 0 }}</span>
                  个，经过信任度评估，最终入到台帐IP
                  <span class="num">{{ taskInfo.konwn_table_num || 0 }}</span> 个，入到疑似的IP
                  <span class="num">{{ taskInfo.unknown_table_num || 0 }}</span> 个，入威胁的IP
                  <span class="num">{{ taskInfo.threat_table_num || 0 }}</span> 个；</p
                >
                <p class="res"
                  >3. 通过本次资产测绘，共发现域名资产
                  <span class="num">{{
                    (taskInfo.report_info && taskInfo.report_info.domain_num) || 0
                  }}</span>
                  个，登录入口资产
                  <span class="num">{{
                    (taskInfo.report_info && taskInfo.report_info.login_page_num) || 0
                  }}</span>
                  个，证书资产<span class="num">{{
                    (taskInfo.report_info && taskInfo.report_info.cert_num) || 0
                  }}</span>
                  个。</p
                >
              </div>
            </div>
            <!-- 云端推荐结果 -->
            <div class="subContent taskRes" v-if="type == 'activeCloudArr'">
              <div class="tip">输出结果如下：</div>
              <p class="res"
                >1. 关联发现资产
                <span class="num">{{ taskInfo.all_ip_num || 0 }}</span>
                个，经过信任度评估，最终入到台帐IP
                <span class="num">{{ taskInfo.konwn_table_num || 0 }}</span> 个，入到疑似的IP
                <span class="num">{{ taskInfo.unknown_table_num || 0 }}</span> 个，入威胁的IP
                <span class="num">{{ taskInfo.threat_table_num || 0 }}</span> 个；</p
              >
              <p class="res"
                >2. 通过本次云端推荐，共发现域名资产
                <span class="num">{{ (taskInfo && taskInfo.domain_num) || 0 }}</span>
                个，登录入口资产
                <span class="num">{{ (taskInfo && taskInfo.login_page_num) || 0 }}</span>
                个，证书资产<span class="num">{{ (taskInfo && taskInfo.cert_num) || 0 }}</span>
                个。</p
              >
            </div>
            <!-- 资产扫描结果 -->
            <div class="subContent taskRes" v-if="type == 'activeScanArr'">
              <div class="tip">输出结果如下：</div>
              <p class="res">1. 任务扫描耗时 {{ secondsFormat(taskInfo.use_seconds) }}；</p>
              <p class="res"
                >2. 关联存活IP <span class="num">{{ taskInfo.asset_num || 0 }}</span> 个，组件
                <span class="num">{{ taskInfo.rule_num || 0 }}</span> 个，端口
                <span class="num">{{ taskInfo.port_num || 0 }}</span> 个，涉及厂商
                <span class="num">{{ taskInfo.product_num || 0 }}</span> 个。</p
              >
            </div>
            <!-- 钓鱼仿冒结果 -->
            <div class="subContent taskRes" v-if="type == 'activePhishingArr'">
              <div class="tip">输出结果如下：</div>
              <p class="res"
                >1. 本次任务共收集到
                <span class="num">{{ taskInfo.clue_list_num || 0 }}</span>
                条线索，包含手动添加以及自动收集；</p
              >
              <p class="res"
                >2. 关联发现钓鱼仿冒资产
                <span class="num">{{ taskInfo.fake_num || 0 }}</span>
                个，经过资产存活性探测，可访问资产
                <span class="num">{{ taskInfo.online_url_num || 0 }}</span> 个，已失效资产
                <span class="num">{{ taskInfo.offline_url_num || 0 }}</span> 个。</p
              >
            </div>
          </div>
        </template>
        <template v-else>
          <div class="emptyModule">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p
                >近期暂无任务<span @click="$router.push(taskInfoMap[type].path)"
                  >，去创建任务</span
                ></p
              >
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { unitTask, recommendTask, assetsScanTask, fakeDetectTask } from '@/api/apiConfig/api.js'
import taskMap from './taskMap.vue'
export default {
  data() {
    return {
      loading: false,
      type: 'activeUnitArr',
      taskInfo: {},
      currentStepArr: [],
      currentStep: '',
      taskInfoMap: {
        activeUnitArr: {
          name: '单位资产测绘任务',
          intro:
            '通过企业名称一键式梳理互联网暴露面资产，依托多种模式的自动化线索扩展和资产识别算法，快速、高效。',
          stepArr: ['输入企业名称', '资产线索获取', '云端资产推荐', '资产可信度评估'],
          path: '/unitIndex',
          icon: require('../../assets/images/functionImg.png')
        },
        activeCloudArr: {
          name: '云端资产推荐',
          intro:
            '支持自定义线索进行影子资产的发现，帮助用户快速、精准的获取资产数据，适用于不同场景资产盘点。',
          stepArr: ['目标线索选择', '云端资产推荐', '资产可信度评估', '关联任务自定义'],
          path: '/assetsCloud',
          img: require('../../assets/images/assetsCloud.png')
        },
        activeScanArr: {
          name: '资产扫描任务',
          intro:
            '支持针对目标IP、IP段、域名进行深度探测，发现其开放的端口、协议、组件、地理位置等信息。',
          path: '/assetsScan',
          img: require('../../assets/images/assetsScan.png')
        },
        activePhishingArr: {
          name: '钓鱼仿冒发现任务',
          intro:
            '通过企业名称自动化发现钓鱼仿冒网站，结合线索扩展算法以及独创的FID识别技术实现数据的高效率获取。',
          stepArr: ['输入企业名称', '特征线索获取', '仿冒数据发现', '数据存活性检测'],
          path: '/phishingTask'
        }
      },
      isFinish: false,
      user: {}
    }
  },
  components: { taskMap },
  mounted() {
    let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (userInfo) {
      this.user = userInfo.user
    }
    if (this.user.role == 2 && !this.currentCompany) return
    this.getTaskInfo()
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getTaskInfo()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    async getTaskInfo() {
      this.loading = true
      let res
      if (this.type == 'activeUnitArr') {
        res = await unitTask({ operate_company_id: this.currentCompany }).catch(() => {
          this.loading = false
        })
        this.taskInfo = res.data || {}
        if (this.taskInfo.step == 5) {
          this.isFinish = true
        }
        if (this.taskInfo.is_intellect_mode == 1) {
          this.taskInfo.mode = '智能模式'
        } else if (this.taskInfo.detect_type == 1) {
          this.taskInfo.mode = '标准模式'
        } else if (this.taskInfo.detect_type == 2) {
          this.taskInfo.mode = '专家模式'
        }
        this.currentStepArr = this.taskInfoMap[this.type].stepArr
        this.currentStep = this.currentStepArr[this.taskInfo.step - 1]
      } else if (this.type == 'activeCloudArr') {
        res = await recommendTask({ operate_company_id: this.currentCompany }).catch(() => {
          this.loading = false
        })
        let data = res.data || {}
        this.taskInfo = res.data || {}

        if (this.taskInfo.step == 5) {
          this.isFinish = true
        }
        if (data && (data.step == 3 || data.step == 4)) {
          this.taskInfo.step = data.step - 1
        }
        this.currentStepArr = this.taskInfoMap[this.type].stepArr
        this.currentStep = this.currentStepArr[this.taskInfo.step - 1]
      } else if (this.type == 'activeScanArr') {
        res = await assetsScanTask({ operate_company_id: this.currentCompany }).catch(() => {
          this.loading = false
        })
        this.taskInfo = res.data || {}
        let status = this.taskInfo.status
        let currentStep
        if (
          this.taskInfo.task_type == 1 ||
          (this.taskInfo.task_type != 1 && this.taskInfo.is_audit == 1)
        ) {
          if (status == 0) {
            currentStep = '等待扫描'
          } else if (status == 1) {
            currentStep = '扫描中'
          } else if (status == 2) {
            currentStep = '扫描完成'
          } else if (status == 3) {
            currentStep = '扫描失败'
          } else if (status == 4) {
            currentStep = '暂停扫描'
          }
        } else if (this.taskInfo.is_audit != 1) {
          currentStep = '审核驳回'
        }
        this.currentStep = currentStep
      } else if (this.type == 'activePhishingArr') {
        res = await fakeDetectTask({ operate_company_id: this.currentCompany }).catch(() => {
          this.loading = false
        })
        this.taskInfo = res.data || {}
        this.currentStepArr = this.taskInfoMap[this.type].stepArr
        this.currentStep = this.currentStepArr[this.taskInfo.step - 1]
        if (this.taskInfo.detect_type == 2) {
          this.taskInfo.mode = '深度模式'
        } else {
          this.taskInfo.mode = '标准模式'
        }
        if (this.taskInfo.step_detail == 400) {
          this.$set(this.taskInfoMap.activePhishingArr, 'stepArr', [
            '输入企业名称',
            '特征线索获取',
            '仿冒数据发现',
            '数据存活性检测'
          ])
          this.currentStepArr = ['输入企业名称', '特征线索获取', '仿冒数据发现', '数据存活性检测']
          if (this.taskInfo.status == 2 && this.taskInfo.step == 4) {
            this.isFinish = true
          }
        } else {
          if (this.taskInfo.step == 4) {
            this.taskInfo.step = 5
            this.$set(this.taskInfoMap.activePhishingArr, 'stepArr', [
              '输入企业名称',
              '特征线索获取',
              '仿冒数据发现',
              '数据存活性检测',
              '深度探测'
            ])
            this.currentStepArr = [
              '输入企业名称',
              '特征线索获取',
              '仿冒数据发现',
              '数据存活性检测',
              '深度探测'
            ]
            this.currentStep = '深度探测'
          }
          if (this.taskInfo.status == 2 && this.taskInfo.step == 5) {
            this.isFinish = true
          }
        }
      }
      this.loading = false
    },
    changeType(type) {
      this.type = type
      this.isFinish = false
      this.currentStepArr = []
      this.currentStep = 0
      this.taskInfo = {}
      this.getTaskInfo()
    },
    setScale() {
      let designWidth = 1000 //设计稿的宽度，根据实际项目调整
      let designHeight = 600 //设计稿的高度，根据实际项目调整
      let scale =
        this.$refs.container.clientHeight / this.$refs.container.clientWidth >
        designWidth / designHeight
          ? this.$refs.container.clientWidth / designWidth
          : this.$refs.container.clientHeight / designHeight
      document.getElementById('mapContainer').style.transformOrigin = '0 0'
      document.getElementById('mapContainer').style.transform = `scale(${scale})`
    }
  }
}
</script>

<style lang="less" scoped>
// /deep/.mapContainer{
//   transform-origin: 0 0;
// }
.mainContainer {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  .left {
    width: 0;
    flex: 1;
    position: relative;
  }
  .right {
    width: 332px;
    height: 100%;
    background-color: #fff;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
    .mainTitle {
      display: flex;
      align-items: center;
      position: relative;
      height: 47px;
      line-height: 47px;
      padding-left: 10px;
      border-bottom: 1px solid #e9ebef;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        margin: auto 0;
        width: 4px;
        height: 16px;
        border-radius: 0 2px 2px 0;
        background-color: #2677ff;
      }
    }
    .intro {
      text-indent: 20px;
      padding: 16px;
      line-height: 20px;
      background: linear-gradient(180deg, #ffffff 0%, #eef5ff 100%);
    }
    .content {
      height: calc(100% - 171px);
      padding: 16px;
      .title {
        color: #37393c;
        font-weight: 600;
        font-size: 14px;
      }
      .module {
        position: relative;
        margin-top: 16px;
        .subTitle {
          display: flex;
          align-items: center;
          .serial {
            margin-right: 8px;
          }
        }
        .subContent {
          margin-top: 16px;
          border-radius: 4px;
          background-color: #f5f8fc;
          &.taskParams {
            height: 161px;
            .item {
              margin-top: 12px;
              margin-left: 16px;
              margin-right: 16px;
              &:first-child {
                margin-top: 16px;
              }
              .label {
                color: #62666c;
              }
              .value {
                color: #37393c;
                &.progress {
                  display: inline-block;
                  height: 24px;
                  padding: 0 8px;
                  line-height: 24px;
                  border-radius: 2px;
                  color: rgba(38, 119, 255, 1);
                  background: rgba(38, 119, 255, 0.12);
                }
              }
            }
          }
          .name {
            height: 44px;

            padding: 0 16px;
            border-bottom: 1px solid #e3e5ea;
            font-weight: 600;
            line-height: 44px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          &.taskRes {
            padding: 12px 16px;
            height: calc(100% - 65px);
            .tip {
              margin-bottom: 8px;
              color: #62666c;
            }
            .res-content {
              height: calc(100% - 25px);
              overflow: auto;
            }
            .res {
              color: #37393c;
              padding-left: 16px;
              text-indent: -16px;
              line-height: 24px;
              .num {
                color: rgba(38, 119, 255, 1);
              }
            }
          }
        }
      }
    }
  }
}
// .container{
//   height: 100%;
//   width: 100%;
// }
.rightArrow {
  // font-size: 10px;
  // width: 10px;
  height: 16px;
  vertical-align: middle;
  margin-left: 10px;
}
.serial {
  width: 8px;
  height: 8px;
  vertical-align: middle;
  color: #2677ff;
}

.result {
  height: calc(100% - 243px);
}
.emptyModule {
  width: 100%;
  height: 100%;
  position: relative;
}
.emptyClass {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(8px);
  svg {
    display: inline-block;
    font-size: 90px;
  }
  p {
    margin-top: 12px;
    color: #62666c;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
