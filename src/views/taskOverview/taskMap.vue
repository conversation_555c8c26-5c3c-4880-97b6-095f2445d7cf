<template>
  <div ref="container" class="container">
    <div id="mapContainer" @mousedown="down" ref="mapContainer" class="mapContainer">
      <!-- 主要模块 -->
      <div class="mainTask unitTask" @click="handleClick('activeUnitArr')">
        <img
          class="task-icon"
          :src="
            require(
              '../../assets/images/taskView/unit' +
                (type == 'activeUnitArr' ? '' : '-unactive') +
                '.png'
            )
          "
          alt=""
        />
        <template v-if="type == 'activeUnitArr'">
          <img class="logo logo1" src="../../assets/images/taskView/unit-logo1.png" alt="" />
          <img class="logo logo2" src="../../assets/images/taskView/unit-logo2.png" alt="" />
          <img class="logo logo3" src="../../assets/images/taskView/unit-logo3.png" alt="" />
          <img class="logo logo4" src="../../assets/images/taskView/unit-logo4.png" alt="" />
        </template>
        <div class="label task-label" :class="{ active: type == 'activeUnitArr' }">
          <span class="top-img">
            <img src="../../assets/images/functionImg.png" alt="" />
          </span>
          <span> 单位资产测绘 </span>
        </div>
      </div>
      <div class="mainTask cloud" @click="handleClick('activeCloudArr')">
        <img
          class="task-icon"
          :src="
            require(
              '../../assets/images/taskView/cloud' +
                (type == 'activeCloudArr' ? '' : '-unactive') +
                '.png'
            )
          "
          alt=""
        />
        <div class="label task-label" :class="{ active: type == 'activeCloudArr' }">
          <span class="top-img">
            <img src="../../assets/images/assetsCloud.png" alt="" />
          </span>
          <span> 云端资产推荐 </span>
        </div>
      </div>
      <div class="mainTask assetScan" @click="handleClick('activeScanArr')">
        <img
          class="task-icon"
          :src="
            require(
              '../../assets/images/taskView/assetScan' +
                (type == 'activeScanArr' ? '' : '-unactive') +
                '.png'
            )
          "
          alt=""
        />
        <div class="label task-label" :class="{ active: type == 'activeScanArr' }">
          <span class="top-img">
            <img src="../../assets/images/assetsScan.png" alt="" />
          </span>
          <span> 资产扫描任务 </span>
        </div>
      </div>
      <div class="mainTask phishing" @click="handleClick('activePhishingArr')">
        <img
          class="task-icon"
          :src="
            require(
              '../../assets/images/taskView/phishing' +
                (type == 'activePhishingArr' ? '' : '-unactive') +
                '.png'
            )
          "
          alt=""
        />
        <div class="label task-label" :class="{ active: type == 'activePhishingArr' }">
          <span class="top-img">
            <img src="../../assets/images/unit/relateTask4.png" alt="" />
          </span>
          <span> 钓鱼仿冒发现 </span>
        </div>
      </div>
      <div class="mainTask risk">
        <img
          class="task-icon"
          :src="
            require(
              `../../assets/images/taskView/risk${type == 'activeUnitArr' || type == 'activeCloudArr' ? '' : '-unactive'}.png`
            )
          "
          alt=""
        />
        <div class="label" v-if="type == 'activeUnitArr' || type == 'activeCloudArr'">风险告警</div>
        <div v-else class="label hover">风险告警</div>
      </div>

      <!-- 涉及模块 -->
      <div
        class="stepTask"
        :class="item.className"
        v-for="(item, index) in stepArr"
        :key="'9' + String(index)"
      >
        <img
          :src="
            require(
              `../../assets/images/taskView/step${activeArrStep.includes(index) ? '' : '-unactive'}.png`
            )
          "
          alt=""
        />
        <div v-if="activeArrStep.includes(index)" class="label">{{ item.label }}</div>
        <div v-else class="label hover">{{ item.label }}</div>
      </div>

      <!-- 轨道线云端资产推荐 -->
      <div v-for="index in 25" :key="index + type">
        <div
          :style="activeArrLine.includes(index) ? 'background-color:#2677FF' : ''"
          class="line"
          :class="'line' + index"
        >
          <!-- 单位资产测绘 -->
          <template v-if="type == 'activeUnitArr' || type == 'activeCloudArr'">
            <img
              v-if="activeArrLine.includes(index)"
              class="arrow"
              :class="'arrow' + index"
              src="../../assets/images/taskView/cicle.png"
              alt=""
            />
            <img
              v-if="index == 22"
              class="arrow arrow22-2"
              src="../../assets/images/taskView/cicle.png"
              alt=""
            />
          </template>
          <template v-if="activeArrLine.includes(index) && type == 'activeScanArr'">
            <img
              v-if="index != 5"
              class="arrow"
              :class="'arrow' + index + '-scan'"
              src="../../assets/images/taskView/cicle.png"
              alt=""
            />
          </template>
          <template v-if="activeArrLine.includes(index) && type == 'activePhishingArr'">
            <img
              class="arrow"
              :class="'arrow' + index + '-phishing'"
              src="../../assets/images/taskView/cicle.png"
              alt=""
            />
          </template>
        </div>
      </div>
      <!-- 池子背景图 -->
      <div class="blankBgc blankBgc1"> </div>
      <img
        class="blankBgc-img blankBgc1-img"
        src="../../assets/images/taskView/blankBgc1.png"
        alt=""
      />

      <div class="blankBgc blankBgc2"> </div>
      <img
        class="blankBgc-img blankBgc2-img"
        src="../../assets/images/taskView/blankBgc2.png"
        alt=""
      />

      <div class="blankBgc blankBgc3"> </div>
      <img
        class="blankBgc-img blankBgc3-img"
        src="../../assets/images/taskView/blankBgc2.png"
        alt=""
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dragging: false,
      boxX: '',
      boxY: '',
      mouseX: '',
      mouseY: '',
      offsetX: '',
      offsetY: '',
      container: null,
      taskMap: null,

      containerDiv: null,
      mapContainerDiv: null,
      type: 'activeUnitArr',
      activeArrLine: [],
      activeUnitArrLine: [1, 5, 10, 13, 14, 15, 2, 3, 6, 9, 8, 11, 16, 17, 18, 19, 22, 23, 24, 25],
      activeCloudArrLine: [4, 5, 10, 13, 14, 15, 2, 3, 6, 9, 8, 11, 16, 17, 18, 19, 22, 23, 24, 25],
      activeScanArrLine: [5, 7, 22],
      activePhishingArrLine: [5, 6, 20, 12, 21, 23],
      activeArrStep: [],
      activeUnitArrStep: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      activeCloudArrStep: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      activeScanArrStep: [0, 3],
      activePhishingArrStep: [0, 5],
      stepArr: [
        {
          className: 'step1-1',
          label: '线索'
        },
        {
          className: 'step1-2',
          label: '云端资产推荐'
        },
        {
          className: 'step1-3',
          label: '资产可信度评估'
        },
        {
          className: 'step2-1',
          label: '资产台账'
        },
        {
          className: 'step2-2',
          label: '疑似资产'
        },
        {
          className: 'step2-3',
          label: '威胁资产'
        },
        {
          className: 'step3-1',
          label: '已知资产'
        },
        {
          className: 'step3-2',
          label: '域名资产'
        },
        {
          className: 'step4-1',
          label: '影子资产'
        },
        {
          className: 'step4-2',
          label: '业务系统'
        }
      ]
    }
  },
  mounted() {
    this.containerDiv = this.$refs.container
    this.mapContainerDiv = this.$refs.mapContainer
    const _that = this
    _that.setScale()
    window.onresize = function () {
      _that.setScale()
    }
    document.onmousemove = this.move
    document.onmouseup = this.up
    this.activeArrLine = this.activeUnitArrLine
    this.activeArrStep = this.activeUnitArrStep
  },
  methods: {
    // 鼠标按下
    down(e) {
      this.dragging = true

      // 获取元素所在的坐标
      this.boxX = this.mapContainerDiv.offsetLeft
      this.boxY = this.mapContainerDiv.offsetTop

      // 获取鼠标所在的坐标
      this.mouseX = parseInt(this.getMouseXY(e).x)
      this.mouseY = parseInt(this.getMouseXY(e).y)

      // 鼠标相对元素左和上边缘的坐标
      this.offsetX = this.mouseX - this.boxX
      this.offsetY = this.mouseY - this.boxY
    },
    // 鼠标移动
    move(e) {
      if (this.dragging) {
        // 获取移动后的元素的坐标
        var x = this.getMouseXY(e).x - this.offsetX
        var y = this.getMouseXY(e).y - this.offsetY
        // 此处就是父元素的宽度减去子元素宽度
        var width = -this.mapContainerDiv.offsetWidth
        var height = 0
        // min方法保证不会超过右边界，max保证不会超过左边界
        x = Math.max(Math.min(0, x), width)
        y = Math.max(Math.min(0, y), height)

        // 给元素及时定位
        this.mapContainerDiv.style.left = x + 'px'
        this.mapContainerDiv.style.top = y + 'px'
      }
    },
    // 鼠标释放
    up(e) {
      this.dragging = false
    },
    // 获取鼠标位置
    getMouseXY(e) {
      var x = 0,
        y = 0
      e = e || window.event

      if (e.pageX) {
        x = e.pageX
        y = e.pageY
      } else {
        x = e.clientX + document.body.scrollLeft - document.body.clientLeft
        y = e.clientY + document.body.scrollTop - document.body.clientTop
      }

      return {
        x: x,
        y: y
      }
    },
    setScale() {
      let designWidth = 1000 //设计稿的宽度，根据实际项目调整
      let designHeight = 600 //设计稿的高度，根据实际项目调整
      let scale =
        this.$refs.container.clientWidth / this.$refs.container.clientHeight >
        designWidth / designHeight
          ? this.$refs.container.clientWidth / designWidth
          : this.$refs.container.clientHeight / designHeight
      document.getElementById('mapContainer').style.transformOrigin = '0 0'
      document.getElementById('mapContainer').style.transform = `scale(${scale})`
    },
    handleClick(type) {
      this.$forceUpdate()
      this.type = type
      this.$emit('changeType', type)
      this.activeArrLine = this[type + 'Line']
      this.activeArrStep = this[type + 'Step']
    },
    drawLine(obj1, obj2) {
      // 起点坐标
      var x1 = obj1.left
      var y1 = obj1.top

      // // 终点坐标
      var x2 = obj2.left
      var y2 = obj2.top

      // 计算连接线长度
      var length = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1))

      // 计算连接线旋转弧度值
      var rad = Math.atan2(y2 - y1, x2 - x1)

      // 连接线未旋转前，起点坐标计算
      var top = (y1 + y2) / 2
      var left = (x1 + x2) / 2 - length / 2
      // 创建连接线 dom 节点，并设置样式
      var line = document.createElement('div')
      var style =
        'position: absolute;z-index: 699; background-color: #2677FF; height: 1px; top:' +
        top +
        'px; left:' +
        left +
        'px; width: ' +
        length +
        'px; transform: rotate(' +
        rad +
        'rad);'
      line.setAttribute('style', style)
      line.setAttribute('class', 'highlight')
      this.$refs.mapContainer.appendChild(line)
    }
  }
}
</script>

<style lang="less" scoped>
img {
  user-select: none;
}

.top-img {
  height: 18px;
  margin-right: 4px;
  img {
    height: 100%;
    vertical-align: middle;
  }
}
.container {
  z-index: 2000;
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: absolute;
  cursor: move;
  // box-shadow: inset 0px 0px 120px 0px rgba(0, 84, 225, 0.12);
}
img {
  -webkit-user-drag: none;
}
.mapContainer {
  transform-origin: 0 0;
  width: 1000px;
  height: 600px;
  position: absolute;
  // background-color: radial-gradient(46% 37% at 50% 50%, #DBE5F6 0%, rgba(219, 233, 255, 0.1) 100%);
  background: radial-gradient(50% 50% at 50% 50%, #ecf0fc 0%, #f4f7ff 100%);
  box-shadow: inset 0px 0px 120px 0px rgba(0, 84, 225, 0.12);
  border: 2px solid #fff;
  user-select: none;
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // width: 1489px;
    // height: 1582px;
    // background: -webkit-linear-gradient(top, transparent 29px, #D8D8D8 0), -webkit-linear-gradient(right, transparent 29px, #D8D8D8 0);
    // , -webkit-linear-gradient(right, transparent 29px, #D8D8D8 0)
    // -webkit-linear-gradient(top, transparent 29px, #D8D8D8 0),
    // background: -webkit-linear-gradient(top, transparent 29px, #D8D8D8 0), -webkit-linear-gradient(left, transparent 29px, #D8D8D8 0),radial-gradient(46% 37% at 50% 50%, #DBE5F6 0%, rgba(219, 233, 255, 0.1) 100%);;
    // radial-gradient(46% 37% at 50% 50%, #DBE5F6 0%, rgba(219, 233, 255, 0.1) 100%);
    // background-size: 30px 30px;
    // background-position: center center;
    background: url('../../assets/images/taskView/bgc.png') no-repeat 100% 100%;
    background-size: 100% 100%;
    // transform: rotate(-30deg) skew(30deg);
  }
  .mainTask {
    width: 80px;
    position: absolute;
    z-index: 1099;

    .task-icon {
      width: 80px;
    }

    .label.hover {
      display: none;
    }
    &:hover {
      .label.hover {
        display: block;
      }
    }
    &:not(:last-child):hover {
      cursor: pointer;
    }
  }
  .unitTask {
    top: 208px;
    left: 70px;
    transform: translateY(-100%);
  }
  .cloud {
    top: 291px;
    left: 44px;
  }
  .assetScan {
    top: 344px;
    left: 184px;
  }
  .phishing {
    top: 62px;
    left: 669px;
  }
  .risk {
    top: 447px;
    left: 821px;
    &:hover {
      cursor: default !important;
    }
  }
  .stepTask {
    z-index: 1099;
    // width: 60px;
    position: absolute;
    img {
      width: 60px;
    }
    .label.hover {
      display: none;
    }
    &:hover {
      .label.hover {
        display: block;
      }
    }
  }
  .step1-1 {
    top: 205px;
    left: 214px;
  }
  .step1-2 {
    top: 151px;
    left: 309px;
  }
  .step1-3 {
    top: 100px;
    left: 399px;
  }
  .step2-1 {
    top: 278px;
    left: 348px;
  }
  .step2-2 {
    top: 225px;
    left: 440px;
  }
  .step2-3 {
    top: 174px;
    left: 530px;
  }
  .step3-1 {
    top: 368px;
    left: 506px;
  }
  .step3-2 {
    top: 264px;
    left: 688px;
  }
  .step4-1 {
    top: 427px;
    left: 612px;
  }
  .step4-2 {
    top: 323px;
    left: 792px;
  }
  .line {
    z-index: 99;
    position: absolute;
    height: 1px;
    background-color: #cbd7eb;
    // transform-origin:0 0;
    // transform: rotate(-45deg);
    &.active {
      background-color: #2677ff;
    }
    .arrow {
      position: absolute;
      top: -11px;
      left: 0;
      opacity: 0;
    }
  }
  .line1 {
    width: 150px;
    top: 222px;
    left: 107px;
    transform: rotate(30deg);
  }

  .line2 {
    width: 210px;
    top: 207px;
    left: 232px;
    transform: rotate(-30deg);
  }
  .line3 {
    width: 90px;
    top: 208px;
    left: 474px;
    transform: rotate(30deg);
  }
  .line4 {
    width: 175px;
    top: 303px;
    left: 84px;
    transform: rotate(-30deg);
  }
  .line5 {
    width: 60px;
    top: 275px;
    left: 244px;
    transform: rotate(-150deg);
  }
  .line22 {
    width: 90px;
    top: 312px;
    left: 293px;
    transform: rotate(30deg);
  }
  .line6 {
    width: 209px;
    top: 238px;
    left: 285px;
    transform: rotate(150deg);
  }
  // .line23 {
  //   width: 107px;
  //   top: 212px;
  //   left: 382px;
  //   transform: rotate(150deg);
  // }
  .line20 {
    width: 173px;
    top: 142px;
    left: 470px;
    transform: rotate(-30deg);
  }
  .line7 {
    width: 175px;
    top: 378px;
    left: 213px;
    transform: rotate(-30deg);
  }
  .line21 {
    width: 175px;
    top: 187px;
    left: 549px;
    transform: rotate(150deg);
  }

  .line8 {
    width: 60px;
    top: 170px;
    left: 424px;
    transform: rotate(30deg);
  }
  .line9 {
    width: 90px;
    top: 260px;
    left: 384px;
    transform: rotate(30deg);
  }
  .line10 {
    width: 90px;
    top: 357px;
    left: 372px;
    transform: rotate(-150deg);
  }
  .line24 {
    width: 90px;
    top: 401px;
    left: 449px;
    transform: rotate(30deg);
  }
  .line11 {
    width: 102px;
    top: 398px;
    left: 529px;
    transform: rotate(-30deg);
  }
  .line25 {
    width: 102px;
    top: 347px;
    left: 617px;
    transform: rotate(-30deg);
  }
  .line12 {
    width: 90px;
    top: 121px;
    left: 625px;
    transform: rotate(30deg);
  }
  .line13 {
    width: 103px;
    top: 405px;
    left: 359px;
    transform: rotate(150deg);
  }
  .line14 {
    width: 212px;
    top: 483px;
    left: 352px;
    transform: rotate(30deg);
  }
  .line15 {
    width: 102px;
    top: 510px;
    left: 543px;
    transform: rotate(-30deg);
  }
  .line16 {
    width: 120px;
    top: 402px;
    left: 617px;
    transform: rotate(30deg);
  }
  .line17 {
    width: 107px;
    top: 406px;
    left: 722px;
    transform: rotate(-30deg);
  }
  .line18 {
    width: 150px;
    top: 416px;
    left: 812px;
    transform: rotate(30deg);
  }
  .line19 {
    width: 102px;
    top: 479px;
    left: 857px;
    transform: rotate(150deg);
  }
  .blankBgc {
    position: absolute;
    z-index: 98;
    background-color: #fff;
    // box-shadow: 0 1px 1px 0 #ddd,
    //  0 2px 2px 0 #ddd,
    //   0 3px 3px 0 #ddd,
    // 0 4px 4px 0 #ddd;
  }
  // 单位资产测绘-线索
  .arrow1 {
    animation: line1 23s linear infinite forwards;
  }
  @keyframes line1 {
    0% {
      transform: translate(0, 0);
      opacity: 1;
    }

    10.87% {
      opacity: 1;
      transform: translate(150px, 0);
    }
    10.88% {
      opacity: 1;
      transform: translate(150px, 0) rotateY(180deg);
    }
    21.74% {
      opacity: 1;
      transform: translate(0, 0) rotateY(180deg);
    }

    /* 关键点 */
    41.67%,
    100% {
      // transform: translate(150px, 0);
      opacity: 0;
    }
  }
  .arrow4 {
    animation: line4 23s linear infinite forwards;
  }
  @keyframes line4 {
    0% {
      transform: translate(0, 0);
      opacity: 1;
    }

    10.87% {
      opacity: 1;
      transform: translate(150px, 0);
    }

    10.88% {
      opacity: 0;
      transform: translate(150px, 0);
    }

    /* 关键点 */
    41.67%,
    100% {
      // transform: translate(150px, 0);
      opacity: 0;
    }
  }

  // 线索-资产可信度评估
  .arrow2 {
    animation: line2 23s linear infinite forwards;
  }
  @keyframes line2 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }

    10.87% {
      transform: translate(0, 0);
      opacity: 1;
    }
    23.91% {
      transform: translate(210px, 0);
      opacity: 1;
    }

    /* 关键点 */
    41.67%,
    100% {
      // transform: translate(150px, 0);
      opacity: 0;
    }
  }
  // 资产可信度评估-威胁资产
  .arrow8 {
    animation: line8-1 23s linear infinite forwards;
  }
  @keyframes line8-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }

    23.91% {
      transform: translate(0, 0);
      opacity: 1;
    }
    34.78% {
      transform: translate(150px, 0);
      opacity: 1;
    }

    /* 关键点 */
    34.80%,
    100% {
      // transform: translate(150px, 0);
      opacity: 0;
    }
  }
  // 威胁资产-资产台账
  .arrow6 {
    animation: line6-1 23s linear infinite forwards;
  }
  @keyframes line6-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }

    28.25% {
      opacity: 0;
    }
    28.26% {
      transform: translate(0, 0);
      opacity: 1;
    }

    41.29% {
      transform: translate(196px, 0);
      opacity: 1;
    }

    /* 关键点 */
    41.31%,
    100% {
      // transform: translate(150px, 0);
      opacity: 0;
    }
  }
  // 威胁资产-资产台账
  .arrow9 {
    animation: line9-1 23s linear infinite forwards;
  }
  @keyframes line9-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }
    34.77% {
      opacity: 0;
    }
    34.78% {
      transform: translate(0, 0);
      opacity: 1;
    }

    41.30% {
      transform: translate(90px, 0);
      opacity: 1;
    }

    /* 关键点 */
    41.31%,
    100% {
      // transform: translate(150px, 0);
      opacity: 0;
    }
  }
  // 资产台账-已知资产
  .arrow22 {
    animation: line22-1 23s linear infinite forwards;
  }
  @keyframes line22-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }

    41.19% {
      transform: translate(0, 0);
      opacity: 0;
    }
    41.20% {
      transform: translate(0, 0);
      opacity: 1;
    }

    60.97% {
      transform: translate(270px, 0);
      opacity: 1;
    }

    /* 关键点 */
    61%,
    100% {
      // transform: translate(150px, 0);
      opacity: 0;
    }
  }
  // 资产台账/已知资产 -- 影子资产 step1
  .arrow13 {
    animation: line13-1 23s linear infinite forwards;
  }
  @keyframes line13-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }

    54.33% {
      opacity: 0;
    }
    54.34% {
      transform: translate(0, 0);
      opacity: 1;
    }

    60.87% {
      transform: translate(89px, 0);
      opacity: 1;
    }

    /* 关键点 */
    60.90%,
    100% {
      // transform: translate(150px, 0);
      opacity: 0;
    }
  }
  // 资产台账 - 线索
  .arrow22-2 {
    top: 0;
    right: 0;
    left: auto !important;
    // opacity: 1;
    animation: line22-2 23s linear infinite forwards;
  }
  @keyframes line22-2 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }

    47.83% {
      opacity: 0;
    }
    47.84% {
      transform: translate(0, 0) rotateY(180deg);
      opacity: 1;
    }

    58.70% {
      transform: translate(-136px, 0) rotateY(180deg);
      opacity: 1;
    }

    /* 关键点 */
    58.71%,
    100% {
      transform: rotateY(180deg);
      opacity: 0;
    }
  }
  // 资产台账/已知资产 -- 影子资产 step2
  .arrow14 {
    animation: line14-1 23s linear infinite forwards;
  }
  @keyframes line14-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }
    60.85% {
      opacity: 0;
    }
    60.87% {
      transform: translate(0, 0);
      opacity: 1;
    }

    76.05% {
      transform: translate(198px, 0);
      opacity: 1;
    }
    /* 关键点 */
    76.06%,
    100% {
      // transform: translate(150px, 0);
      opacity: 0;
    }
  }
  // 资产台账/已知资产 -- 影子资产 step2
  .arrow15 {
    animation: line15-1 23s linear infinite forwards;
  }
  @keyframes line15-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }
    76.00% {
      opacity: 0;
    }

    76.08% {
      transform: translate(0, 0);
      opacity: 1;
    }
    82.61% {
      transform: translate(102px, 0);
      opacity: 1;
    }
    /* 关键点 */
    82.62%,
    100% {
      opacity: 0;
    }
  }
  // 已知资产-域名资产
  .arrow11 {
    animation: line11-1 23s linear infinite forwards;
  }
  @keyframes line11-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }
    60.95% {
      opacity: 0;
    }
    60.97% {
      transform: translate(0, 0);
      opacity: 1;
    }
    73.91% {
      transform: translate(204px, 0);
      opacity: 1;
    }

    /* 关键点 */
    73.92%,
    100% {
      opacity: 0;
    }
  }
  // 已知资产/域名资产 - 业务系统施step1
  .arrow16 {
    animation: line16-1 23s linear infinite forwards;
  }
  @keyframes line16-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }
    67.38% {
      opacity: 0;
    }
    67.39% {
      transform: translate(0, 0);
      opacity: 1;
    }
    76.09% {
      transform: translate(106px, 0);
      opacity: 1;
    }
    // 82.61% {
    //   transform: translate(120px, 0);
    //   opacity: 1;
    // }
    /* 关键点 */
    76.10%,
    100% {
      opacity: 0;
    }
  }
  // 已知资产/域名资产 - 业务系统施step2
  .arrow17 {
    animation: line17-1 23s linear infinite forwards;
  }
  @keyframes line17-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }
    76.00% {
      opacity: 0;
    }
    76.09% {
      transform: translate(0, 0);
      opacity: 1;
    }
    82.61% {
      transform: translate(106px, 0);
      opacity: 1;
    }
    /* 关键点 */
    82.62%,
    100% {
      opacity: 0;
    }
  }

  // 业务系统-风险系统 step1
  .arrow18 {
    animation: line18-1 23s linear infinite forwards;
  }
  @keyframes line18-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }
    82.60% {
      opacity: 0;
    }
    82.61% {
      transform: translate(0, 0);
      opacity: 1;
    }
    93.48% {
      transform: translate(136px, 0);
      opacity: 1;
    }
    /* 关键点 */
    93.49%,
    100% {
      opacity: 0;
    }
  }
  // 业务系统-风险系统 step2
  .arrow19 {
    animation: line19-1 23s linear infinite forwards;
  }
  @keyframes line19-1 {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }

    93.47% {
      opacity: 0;
    }

    93.48% {
      transform: translate(0, 0);
      opacity: 1;
    }

    99.98% {
      transform: translate(102px, 0);
      opacity: 1;
    }
    /* 关键点 */
    99.99%,
    100% {
      opacity: 0;
    }
  }

  // 资产扫描
  // 资产扫描-资产台账
  .arrow7-scan {
    animation: line7 5s linear infinite forwards;
  }
  @keyframes line7 {
    0% {
      transform: translate(0, 0);
      opacity: 1;
    }

    50% {
      opacity: 1;
      transform: translate(150px, 0);
    }

    50.01% {
      opacity: 0;
      transform: translate(150px, 0);
    }

    /* 关键点 */
    100% {
      // transform: translate(150px, 0);
      opacity: 0;
    }
  }
  // 资产台账-线索
  .arrow22-scan {
    top: 0;
    right: 0;
    left: auto !important;
    // opacity: 1;
    animation: line22-scan 5s linear infinite forwards;
  }
  @keyframes line22-scan {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }

    49.99% {
      opacity: 0;
    }
    50% {
      transform: translate(0, 0) rotateY(180deg);
      opacity: 1;
    }

    99.99% {
      transform: translate(-136px, 0) rotateY(180deg);
      opacity: 1;
    }

    /* 关键点 */
    100% {
      transform: rotateY(180deg);
      opacity: 0;
    }
  }

  // 钓鱼仿冒
  // 线索 - 钓鱼仿冒 step1
  .arrow5-phishing {
    left: auto !important;
    right: 0;
    animation: line5-phishing 11s linear infinite forwards;
  }
  @keyframes line5-phishing {
    0% {
      transform: translate(0, 0) rotateY(180deg);
      opacity: 1;
    }

    9.51% {
      opacity: 1;
      transform: translate(-46px, 0) rotateY(180deg);
    }

    9.52% {
      opacity: 0;
      transform: translate(-46px, 0) rotateY(180deg);
    }

    /* 关键点 */
    100% {
      transform: translate(0, 0) rotateY(180deg);
      opacity: 0;
    }
  }
  // 线索 - 钓鱼仿冒 step2
  .arrow6-phishing {
    left: auto !important;
    right: 0;
    animation: line6-phishing 11s linear infinite forwards;
  }
  @keyframes line6-phishing {
    0% {
      transform: translate(0, 0) rotateY(180deg);
      opacity: 0;
    }

    9.52% {
      opacity: 0;
      transform: translate(0, 0) rotateY(180deg);
    }

    9.53% {
      opacity: 1;
      transform: translate(0, 0) rotateY(180deg);
    }
    61.9% {
      opacity: 1;
      transform: translate(-369px, 0) rotateY(180deg);
    }

    /* 关键点 */
    62%,
    100% {
      transform: translate(0, 0) rotateY(180deg);
      opacity: 0;
    }
  }
  // 线索 - 钓鱼仿冒 step3
  .arrow12-phishing {
    // left: auto !important;
    // right: 0;
    // opacity: 1 !important;
    animation: line12-phishing 11s linear infinite forwards;
  }
  @keyframes line12-phishing {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }

    61.89% {
      opacity: 0;
      transform: translate(0, 0);
    }

    61.9% {
      opacity: 1;
      transform: translate(0, 0);
    }
    76.19% {
      opacity: 1;
      transform: translate(76px, 0);
    }

    /* 关键点 */
    76.2%,
    100% {
      transform: translate(0, 0);
      opacity: 0;
    }
  }
  // 钓鱼仿冒 - 威胁资产
  .arrow21-phishing {
    animation: line21-phishing 11s linear infinite forwards;
  }
  @keyframes line21-phishing {
    0% {
      transform: translate(0, 0);
      opacity: 0;
    }

    76.18% {
      opacity: 0;
      transform: translate(0, 0);
    }

    76.19% {
      opacity: 1;
      transform: translate(0, 0);
    }
    99.98% {
      opacity: 1;
      transform: translate(175px, 0);
    }

    /* 关键点 */
    99.99%,
    100% {
      transform: translate(0, 0);
      opacity: 0;
    }
  }
  .blankBgc1 {
    top: 231px;
    left: 295px;
    width: 350px;
    height: 102px;
    transform: rotate(-30deg) skew(30deg);
  }
  .blankBgc2 {
    top: 395px;
    left: 467px;
    width: 240px;
    height: 120px;
    transform: rotate(30deg) skew(-30deg);
  }
  .blankBgc3 {
    top: 291px;
    left: 650px;
    width: 240px;
    height: 120px;
    transform: rotate(30deg) skew(-30deg);
  }
  .blankBgc-img {
    position: absolute;
    z-index: 799;
  }
  .blankBgc1-img {
    top: 163px;
    left: 263px;
    // width: 416px;
    height: 240px;
  }
  .blankBgc2-img {
    top: 357px;
    left: 419px;
    height: 195px;
  }
  .blankBgc3-img {
    top: 254px;
    left: 604px;
    height: 195px;
  }
  .label {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 8px;
    white-space: nowrap;
    color: rgba(98, 102, 108, 0.7);
    font-size: 12px;
    border-radius: 2px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.7);
    z-index: 2099;
    backdrop-filter: blur(4px);

    &.active {
      color: #2677ff;
      border: 1px solid #2677ff;
      background: linear-gradient(180deg, #f4f8ff 0%, #e1ecff 100%);
    }
  }
  .task-label {
    display: flex;
    padding: 6px 8px;
    backdrop-filter: none;
    color: #62666c;
    font-size: 14px;
    border: 1px solid #d8e1f3;
  }
}

.logo {
  position: absolute;
  width: 16px !important;
  height: 16px;

  &.logo1 {
    left: 1px;
    top: 41px;
    animation: leftTbB 3.2s linear infinite;
  }
  &.logo2 {
    left: 10px;
    top: 10px;
    animation: leftTbA 2.2s infinite;
  }
  &.logo3 {
    left: 57px;
    top: 10px;
    animation: leftTbB 3.2s linear infinite;
  }
  &.logo4 {
    left: 65px;
    top: 41px;
    animation: leftTbA 2.2s infinite;
  }
}
/* 单位资产测绘等icon 浮动 */
@keyframes leftTbA {
  35% {
    transform: translateY(7px);
  }

  80% {
    transform: translateY(-3px);
  }
}
/* 单位资产测绘等icon 浮动 */
@keyframes leftTbB {
  20% {
    transform: translateY(7px);
  }

  50% {
    transform: translateY(-3px);
  }
}
</style>
