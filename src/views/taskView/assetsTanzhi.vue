<template>
  <div class="container">
    <div class="headerTitle">资产探知</div>
    <div class="myBox">
      <div :class="`myBoxLeftBox${index}`" v-for="(item, index) in topData" :key="index">
        <div class="topContent">
          <div class="topContentLeft">
            <div class="topContentTitle"><i></i>{{ item.title }}</div>
            <div class="topContentNum">{{ item.num }}</div>
          </div>
          <div class="topContentRight">
            <img :src="item.icon" alt="" />
          </div>
        </div>
      </div>
      <div class="myBoxLeftBox4">
        <taskView :titleIsShow="1" />
      </div>
    </div>
    <p
      style="
        line-height: 20px;
        border-left: 4px solid rgba(38, 119, 255, 1);
        padding-left: 8px;
        margin-top: 28px;
      "
      >任务入口</p
    >
    <div class="taskWrap">
      <div class="taskClass" v-for="(item, index) in taskArr" :key="index">
        <p class="mainTilte">{{ item.title }}</p>
        <div class="taskItem">
          <ul v-for="(ch, chi) in item['list']" :key="chi">
            <template v-if="!ch.permissions || ch.permissions.includes(user.role)">
              <li class="titleClass" @click.stop.prevent="$router.push(ch.path)"
                ><i></i>{{ ch.name }}</li
              >
              <li class="descClass">{{ ch.desc }}</li>
              <li class="botClass">
                <p class="btnClass" @click.stop.prevent="$router.push(ch.path)">查看任务</p>
                <img :src="ch.imgUrl" alt="" />
              </li>
            </template>
          </ul>
          <ul class="emptyClass" v-if="item.id == 2">
            更多用户场景正在补充…
          </ul>
          <ul class="emptyClass" v-if="item.id == 3">
            更多风险监测任务正在补充…
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import taskView from '../assetsViewIndex/taskViewRuning.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { taskViewStatusCount } from '@/api/apiConfig/api.js'

export default {
  components: { taskView },
  data() {
    return {
      user: {
        role: ''
      },
      topData: [
        {
          title: '任务数量总计',
          num: 0,
          icon: require('../../assets/images/sure.png')
        },
        {
          title: '正在执行任务数量',
          num: 0,
          icon: require('../../assets/images/digitalAssets.png')
        },
        {
          title: '等待中任务数量',
          num: 0,
          icon: require('../../assets/images/unsure.png')
        }
      ],
      taskArr: [
        {
          id: 1,
          title: '资产测绘任务',
          list: [
            {
              name: '单位资产测绘',
              desc: '通过企业名称一键式梳理互联网暴露面资产，依托多种模式的自动化线索扩展和资产识别算法，快速、高效。',
              imgUrl: require('../../assets/images/zctz_dw.png'),
              path: '/unitIndex'
            },
            {
              name: '云端资产推荐',
              desc: '支持自定义线索进行影子资产的发现，帮助用户快速、精准的获取资产数据，适用于不同场景资产盘点。',
              imgUrl: require('../../assets/images/zctz_yd.png'),
              path: '/assetsCloud'
            },
            {
              name: '资产扫描任务',
              desc: '支持针对目标IP、IP段、域名进行深度探测，发现其开放的端口、协议、组件、地理位置等信息。',
              imgUrl: require('../../assets/images/zctz_zcsm.png'),
              path: '/assetsScan'
            },
            {
              name: '域名发现任务',
              desc: '支持对目标域名实施域名枚举以及验证，帮助用户发现更多的域名资产信息。',
              imgUrl: require('../../assets/images/zctz_ymfx.png'),
              path: '/domainTask'
            }
          ]
        },
        {
          id: 2,
          title: '场景分析任务',
          list: [
            {
              name: '资产核查任务',
              desc: '快速盘点用户输入资产是否已纳入系统进行管理，支持对资产台账以及推荐资产库的全量数据进行核查研判。',
              imgUrl: require('../../assets/images/zctz_zchc.png'),
              path: '/checkTask'
            },
            {
              name: '资产状态检测',
              desc: '快速判断用户导入资产的存活状态并输出结果。',
              imgUrl: require('../../assets/images/zctz_ztjc.png'),
              path: '/statusTask'
            },
            {
              name: '集团资产梳理',
              desc: '支持针对大型集团企业进行资产的快速盘点，帮助梳理各个子公司的资产,适用于利用企业架构梳理资产的场景。',
              imgUrl: require('../../assets/images/zctz_ztjc.png'),
              path: '/groupAssets',
              permissions: [2]
            }
            // {
            //   name: '资产变化跟踪',
            //   desc: '支持快速对比两部分资产数据的变化情况，包括IP、端口、组件、协议的变化跟踪。',
            //   imgUrl: require('../../assets/images/zctz_bhgz.png'),
            //   path: '/unitIndex'
            // }
          ]
        },
        {
          id: 3,
          title: '风险监测任务',
          list: [
            {
              name: '漏洞扫描任务',
              desc: '针对客户资产进行高价值PoC检测，发现存在的漏洞和安全弱点，并及时提修复建议和措施。',
              imgUrl: require('../../assets/images/zctz_ldsm.png'),
              path: '/leakScan'
            },
            {
              name: '钓鱼仿冒发现',
              desc: '通过企业名称自动化发现钓鱼仿冒网站，结合线索扩展算法以及独创的FID识别技术实现数据的高效率获取。',
              imgUrl: require('../../assets/images/zctz_ldsm.png'),
              path: '/phishingTask'
            },
                    {
              name: '接口风险识别',
              desc: '通过URL辅助发现未公开接口、敏感数据传输路径等风险点，提升资产可视化和安全监测的能力。',
              imgUrl: require('../../assets/images/zctz_ldsm.png'),
              path: '/tApiRisk',
              permissions: [1,2]

            }
          ]
        }
      ]
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getStatusCount()
      }
    },
    getterWebsocketMessage(msg) {}
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.getStatusCount()
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    async getStatusCount() {
      let statusRes = await taskViewStatusCount({ operate_company_id: this.currentCompany })
      if (statusRes.code == 0) {
        this.topData[0].num = statusRes.data.total ? statusRes.data.total : 0
        this.topData[1].num = statusRes.data.doing ? statusRes.data.doing : 0
        this.topData[2].num = statusRes.data.wait ? statusRes.data.wait : 0
      }
    }
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
}
.taskWrap {
  .taskClass {
    margin-top: 12px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
  }
  .mainTilte {
    padding: 12px 16px;
    color: #37393c;
    font-weight: 500;
    border-bottom: 1px solid #e9ebef;
    margin-bottom: 12px;
  }
  .taskItem {
    display: flex;
    padding: 16px;
  }
  .descClass {
    text-indent: 24px;
    line-height: 24px;
    color: rgba(55, 57, 60, 1);
  }
  .botClass {
    position: absolute;
    width: 86%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    box-sizing: border-box;
    bottom: 21px;
    .btnClass {
      font-size: 14px;
      color: #fff;
      border-radius: 4px;
      background: linear-gradient(90deg, rgba(38, 190, 255, 1) 0%, rgba(38, 118, 255, 1) 100%);
      padding: 6px 12px 6px 12px;
      cursor: pointer;
    }
  }
  .titleClass {
    padding-bottom: 12px;
    color: rgba(55, 57, 60, 1);
    font-weight: 600;
    margin-bottom: 12px;
    cursor: pointer;
    i {
      display: inline-block;
      vertical-align: middle;
      width: 6px;
      height: 6px;
      margin-right: 9px;
      border-radius: 1px;
      transform: rotate(135deg);
      background: rgba(38, 119, 255, 1);
    }
  }
  ul {
    position: relative;
    width: 24%;
    height: 285px;
    margin-right: 1%;
    padding: 21px 20px;
    box-sizing: border-box;
    background: url('../../assets/images/zctz_bg.png') no-repeat;
    background-size: cover;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
  }
  .emptyClass {
    line-height: 240px;
    text-align: center;
    color: rgba(172, 180, 192, 1);
    border-radius: 4px;
    background: linear-gradient(90deg, rgba(247, 251, 255, 1) 0%, rgba(237, 244, 250, 1) 100%);
    border: 2px solid rgba(255, 255, 255, 1);
    box-shadow:
      0px 2px 4px rgba(0, 0, 0, 0.08),
      inset 0px 0px 6px rgba(0, 27, 59, 0.08);
  }
}
.footerClass {
  width: 100%;
  height: calc(100% - 292px);
}
.myBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 12px;
}
.myBox > div {
  width: 24.5%;
  height: 112px;
  padding: 22px 20px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  box-sizing: border-box;
  i {
    display: inline-block;
    vertical-align: middle;
    width: 6px;
    height: 6px;
    margin-right: 5px;
    border-radius: 1px;
    transform: rotate(135deg);
  }
}
.myBoxLeftBox0 {
  // border-left: 4px solid rgba(38, 119, 255, 1) !important;
  i {
    background: rgba(38, 119, 255, 1);
  }
}
.myBoxLeftBox1 {
  // border-left: 4px solid rgba(16, 213, 149, 1) !important;
  i {
    background: rgba(16, 213, 149, 1);
  }
}
.myBoxLeftBox2 {
  // border-left: 4px solid #F8C136 !important;
  i {
    background: #f8c136;
  }
}
.myBoxLeftBox4 {
  height: 195px;
  padding: 0 !important;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
}
.topContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .topContentTitle {
    font-weight: 500;
    color: #62666c;
    font-size: 14px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
  }
  .topContentNum {
    font-size: 32px;
    font-weight: 500;
    color: #37393c;
  }
  .topContentRight {
    width: 50px;
    height: 50px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
