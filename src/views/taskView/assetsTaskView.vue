<template>
  <div class="container">
    <div class="headerTitle">任务概览</div>
    <!-- <div class="myBox">
          <div :class="`myBoxLeftBox${index}`" v-for="(item, index) in topData" :key="index">
              <div class="topContent">
                  <div class="topContentLeft">
                      <div class="topContentTitle">{{item.title}}</div>
                      <div class="topContentNum">{{item.num}}</div>
                  </div>
                  <div class="topContentRight">
                      <img :src="item.icon" alt="">
                  </div>
              </div>
          </div>
          <div class="myBoxLeftBox4">
            <taskView :titleIsShow="1" />
          </div>
        </div> -->
    <div class="con">
      <div class="left1">
        <div class="headerEcharts">任务分布雷达图</div>
        <radarChart
          class="chart_one"
          key="radar"
          :radarTrendData="radarData"
          taskIcon="radar"
        ></radarChart>
      </div>
      <div class="right1">
        <!-- <div class="headerEcharts">任务汇总</div> -->
        <div v-loading="loadingQs" id="chart_two">
          <el-tabs v-model="taskTab" tab-position="left" @tab-click="taskTabClick">
            <el-tab-pane name="1" label="单位资产测绘">
              <taskTable class="tabContentClass" :pageIcon="1" />
            </el-tab-pane>
            <el-tab-pane name="2" label="云端资产推荐">
              <taskTable class="tabContentClass" :pageIcon="2" />
            </el-tab-pane>
            <el-tab-pane name="3" label="资产扫描任务">
              <taskTable class="tabContentClass" :pageIcon="3" />
            </el-tab-pane>
            <el-tab-pane name="4" label="资产核查任务">
              <taskTable class="tabContentClass" :pageIcon="4" />
            </el-tab-pane>
            <el-tab-pane name="5" label="资产状态检测">
              <taskTable class="tabContentClass" :pageIcon="5" />
            </el-tab-pane>
            <el-tab-pane name="6" label="域名发现任务">
              <taskTable class="tabContentClass" :pageIcon="6" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <div class="con">
      <div class="leftsum">
        <div class="sumWrap">
          <el-tabs v-model="taskTabBot1" tab-position="top" @tab-click="changeTab">
            <el-tab-pane name="1" label="单位资产测绘"> </el-tab-pane>
            <el-tab-pane class="" name="2" label="云端资产推荐"> </el-tab-pane>
            <el-tab-pane class="" name="3" label="资产扫描任务"> </el-tab-pane>
          </el-tabs>
          <div v-if="taskTabBot1 == 1" class="taskItemclass">
            <taskItemLeft
              :total="parseNumber(detect_asset_task.total)"
              :percentage="(parseNumber(detect_asset_task.total) / sum).toFixed(2) * 100"
            />
            <taskDetect :itemData="detect_asset_task" />
          </div>
          <div v-if="taskTabBot1 == 2" class="taskItemclass">
            <taskItemLeft
              :total="parseNumber(recommend_record.total)"
              :percentage="(parseNumber(recommend_record.total) / sum) * 100"
            />
            <taskRecomment :itemData="recommentData" :taskIcon="taskIcon" />
          </div>
          <div v-if="taskTabBot1 == 3" class="taskItemclass">
            <taskItemLeft
              :total="parseNumber(scan_task.total)"
              class="left"
              :percentage="(parseNumber(scan_task.total) / sum) * 100"
            />
            <taskScan :itemData="scan_task" />
          </div>
        </div>
        <div class="sumWrap">
          <el-tabs v-model="taskTabBot2" tab-position="top" @tab-click="changeTab">
            <el-tab-pane name="4" label="资产核查任务"> </el-tab-pane>
            <el-tab-pane name="5" label="资产状态检测"> </el-tab-pane>
            <el-tab-pane name="6" label="域名发现任务"> </el-tab-pane>
          </el-tabs>
          <div v-if="taskTabBot2 == 4" class="taskItemclass">
            <taskItemLeft
              :total="parseNumber(asset_audit.total)"
              :percentage="(parseNumber(asset_audit.total) / sum) * 100"
            />
            <taskAudit :itemData="asset_audit" />
          </div>
          <div v-if="taskTabBot2 == 5" class="taskItemclass">
            <taskItemLeft
              :total="parseNumber(asset_status.total)"
              :percentage="(parseNumber(asset_status.total) / sum) * 100"
            />
            <taskStatus :itemData="asset_status" />
          </div>
          <div v-if="taskTabBot2 == 6" class="taskItemclass">
            <taskItemLeft
              :total="parseNumber(domain_find.total)"
              :percentage="(parseNumber(domain_find.total) / sum) * 100"
            />
            <taskDomain :itemData="domain_find" />
          </div>
        </div>
      </div>
      <div class="right1">
        <div class="headerEcharts">任务变化趋势统计图</div>
        <el-select v-model="timeRange" @change="timeChange" clearable placeholder="请选择">
          <el-option label="全部" :value="0"></el-option>
          <el-option label="近7天" :value="1"></el-option>
          <el-option label="近30天" :value="2"></el-option>
          <el-option label="近90天" :value="3"></el-option>
        </el-select>
        <taskTrendEchart
          class="chart_four"
          key="taskHis"
          :radarTrendData="trendData"
          taskIcon="taskHis"
        ></taskTrendEchart>
      </div>
    </div>
  </div>
</template>
<script>
import taskItemLeft from './taskItem_left.vue'
import taskDetect from './taskDetect.vue'
import taskView from '../assetsViewIndex/taskViewRuning.vue'
import taskRecomment from './taskRecomment.vue'
import taskScan from './taskScan.vue'
import taskAudit from './taskAudit.vue'
import taskStatus from './taskStatus.vue'
import taskDomain from './taskDomain.vue'

import taskTable from './assetsTaskViewtable.vue'
import radarChart from './radarChart.vue'
import taskTrendEchart from './taskTrendEchart.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { taskViewStatusCount, taskViewTypeCount } from '@/api/apiConfig/api.js'

export default {
  components: {
    taskView,
    taskItemLeft,
    taskDetect,
    taskRecomment,
    taskScan,
    taskAudit,
    taskStatus,
    taskDomain,
    taskTable,
    radarChart,
    taskTrendEchart
  },
  data() {
    return {
      user: {
        role: ''
      },
      timeRange: 0,
      recommentData: [],
      taskIcon: '',
      taskLoading: false,
      taskTab: '1',
      taskTabBot1: '1',
      taskTabBot2: '4',
      tableHeader: [
        [
          {
            label: '企业名称',
            name: 'name',
            minWidth: 120
          },
          {
            label: '线索数量',
            name: 'clues_count',
            minWidth: 60
          },
          {
            label: '进度',
            name: 'step',
            minWidth: 120
          },
          {
            label: '台账资产',
            name: 'sure_ip_num',
            minWidth: 60
          },
          {
            label: '疑似资产',
            name: 'unsure_ip_num',
            minWidth: 60
          },
          {
            label: '威胁资产',
            name: 'threaten_ip_num',
            minWidth: 60
          },
          {
            label: '关联任务',
            name: 'relate_task'
          },
          {
            label: '发起人',
            name: 'username'
          },
          {
            label: '发起时间',
            name: 'created_at',
            minWidth: 120
          }
        ]
      ],
      loadingLd: false,
      loadingQs: false,
      loadingSz: false,
      loadingLd: false,
      topData: [
        {
          title: '任务数量总计',
          num: 0,
          icon: require('../../assets/images/sure.png')
        },
        {
          title: '正在执行任务数量',
          num: 0,
          icon: require('../../assets/images/digitalAssets.png')
        },
        {
          title: '等待中任务数量',
          num: 0,
          icon: require('../../assets/images/unsure.png')
        }
      ],
      radarData: {
        asset_audit: null,
        asset_status: null,
        detect_asset_task: null,
        domain_find: null,
        recommend_record: null,
        scan_task: null
      }, // 雷达图数据
      trendData: {
        asset_audit: null,
        asset_status: null,
        detect_asset_task: null,
        domain_find: null,
        recommend_record: null,
        scan_task: null
      }, // 趋势图数据
      myOverview: '', //资产概览-数量统计
      taskInfoData: '',
      currentPercent: 0,
      isProgress: false,
      istaskInfoData: true,
      text: '输入企业名称',
      isNew: false, //是否有新增线索
      group_id: 0,
      clouesNum: 0,
      sum: 0,
      echartData: [],
      asset_audit: { included_assets: 0, total: 0, not_included_assets: 0 },
      asset_status: { online_assets: 0, total: 0, offline_assets: 0 },
      detect_asset_task: { total: 0 },
      domain_find: { enum_type: 0, total: 0, verify_type: 0 },
      recommend_record: { total: 0 },
      scan_task: { ip: 0, total: 0, component: 0 }
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getStatusCount()
        this.getTypeCount(this.timeRange)
      }
    },
    getterWebsocketMessage(msg) {}
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.getStatusCount()
    this.getTypeCount(this.timeRange)
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    async timeChange(count_type) {
      this.loadingLd = true
      let taskTypeRes = await taskViewTypeCount({
        count_type: count_type, // 统计类型，有值时对任务应统计趋势，1周，2月，3季
        scan_task: 1, // 0-否 1-是,1才会返回对应数据
        asset_audit: 1, // 0-否 1-是,1才会返回对应数据
        domain_find: 1, // 0-否 1-是,1才会返回对应数据
        asset_status: 1, // 0-否 1-是,1才会返回对应数据
        recommend_record: 1, // 0-否 1-是,1才会返回对应数据
        detect_asset: 1, // 0-否 1-是,1才会返回对应数据
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loadingLd = false
      })
      if (taskTypeRes.code == 0) {
        this.loadingLd = false
        // 用于渲染雷达图和资产趋势变化
        this.trendData = taskTypeRes.data
      }
    },
    changeTab(val) {
      let index = val.name / 1
      let arr = ['detect', 'recomment', 'scan', 'audit', 'status']
      this.taskIcon = arr[index]
    },
    async getStatusCount() {
      let statusRes = await taskViewStatusCount({ operate_company_id: this.currentCompany })
      if (statusRes.code == 0) {
        this.topData[0].num = statusRes.data.total ? statusRes.data.total : 0
        this.topData[1].num = statusRes.data.doing ? statusRes.data.doing : 0
        this.topData[2].num = statusRes.data.wait ? statusRes.data.wait : 0
      }
    },
    async getTypeCount(count_type) {
      this.loadingLd = true
      let taskTypeRes = await taskViewTypeCount({
        count_type: count_type, // 统计类型，有值时对任务应统计趋势，1周，2月，3季
        scan_task: 1, // 0-否 1-是,1才会返回对应数据
        asset_audit: 1, // 0-否 1-是,1才会返回对应数据
        domain_find: 1, // 0-否 1-是,1才会返回对应数据
        asset_status: 1, // 0-否 1-是,1才会返回对应数据
        recommend_record: 1, // 0-否 1-是,1才会返回对应数据
        detect_asset: 1, // 0-否 1-是,1才会返回对应数据
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loadingLd = false
      })
      if (taskTypeRes.code == 0) {
        this.loadingLd = false
        this.asset_audit = taskTypeRes.data.asset_audit
        this.asset_status = taskTypeRes.data.asset_status
        this.detect_asset_task = taskTypeRes.data.detect_asset_task
        this.domain_find = taskTypeRes.data.domain_find
        this.recommend_record = taskTypeRes.data.recommend_record
        this.scan_task = taskTypeRes.data.scan_task
        // 用于渲染雷达图和资产趋势变化
        this.radarData = taskTypeRes.data
        this.trendData = taskTypeRes.data
        // 任务数量总和
        this.sum =
          this.parseNumber(this.asset_audit.total) +
          this.parseNumber(this.asset_status.total) +
          this.parseNumber(this.detect_asset_task.total) +
          this.parseNumber(this.domain_find.total) +
          this.parseNumber(this.recommend_record.total) +
          this.parseNumber(this.scan_task.total)
        // 云端推荐
        let arr = [
          this.recommend_record.account_assets,
          this.recommend_record.suspected_assets,
          this.recommend_record.threat_assets
        ]
        this.recommentData = arr
        // this.pie(this.optionItemArr.taskHistory, 'four');
      }
    },
    parseNumber(num) {
      if (num) {
        return num
      } else {
        return 0
      }
    },
    taskTabClick(val) {}
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
}
.footerClass {
  width: 100%;
  height: calc(100% - 292px);
}
.myBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 12px;
}
.myBox > div {
  width: 24.5%;
  height: 112px;
  padding: 22px 20px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  box-sizing: border-box;
}
.myBoxLeftBox0 {
  border-left: 4px solid rgba(38, 119, 255, 1) !important;
}
.myBoxLeftBox1 {
  border-left: 4px solid rgba(16, 213, 149, 1) !important;
}
.myBoxLeftBox2 {
  border-left: 4px solid #f8c136 !important;
}
.myBoxLeftBox4 {
  height: 195px;
  padding: 0 !important;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
}
.topContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .topContentTitle {
    font-weight: 500;
    color: #62666c;
    font-size: 14px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
  }
  .topContentNum {
    font-size: 32px;
    font-weight: 500;
    color: #37393c;
  }
  .topContentRight {
    width: 50px;
    height: 50px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.con {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  /deep/.el-tabs--left {
    height: 100%;
    .el-tabs__nav-wrap.is-left {
      background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(240, 246, 255, 1) 100%);
    }
    .el-tabs__nav-wrap::after {
      background: rgba(233, 235, 239, 1);
    }
    .el-tabs__active-bar {
      padding: 0;
      background: #2677ff;
    }
    .el-tabs__content {
      height: 100%;
      & > div {
        height: 100%;
        .tabContentClass {
          height: 100%;
        }
      }
    }
  }
  /deep/.el-tabs__nav.is-top {
    .el-tabs__item {
      padding: 0 8px;
    }
    .el-tabs__item.is-top.is-active {
      background: transparent;
      padding: 0 8px;
      color: #2677ff;
    }
    .el-tabs__active-bar {
      padding: 0 8px;
      left: -4px;
      background: #2677ff;
    }
  }
  .left1 {
    width: 33%;
    height: 390px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    padding: 12px 16px 16px 16px;
    box-sizing: border-box;
    margin-bottom: 12px;
    margin-right: 12px;
  }
  .leftsum {
    width: 33%;
    margin-right: 12px;
    box-sizing: border-box;
  }
  /deep/.sumWrap {
    // width: 100%;
    height: 189px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    padding: 0 16px 16px 16px;
    box-sizing: border-box;
    margin-bottom: 12px;
    /deep/.el-tabs__content {
      padding: 16px 0;
    }

    .taskItem {
      height: 100%;
    }
    .taskItemclass {
      width: 100%;
      height: 100%;
      padding: 16px 0;
      display: flex;
      justify-content: space-between;
      .left {
        width: 112px;
        height: 112px;
        text-align: center;
        padding: 16px 0;
        margin-right: 16px;
        box-sizing: border-box;
        border-radius: 2px;
        background: rgba(245, 248, 252, 1);
        p {
          font-size: 14px;
          color: rgba(55, 57, 60, 1);
          margin-top: 12px;
        }
      }
      .right {
        width: 100%;
        .barClass {
          padding: 5px 0;
          box-sizing: border-box;
          .numClass {
            margin-top: 8px;
          }
          .point {
            display: inline-block;
            width: 6px;
            height: 6px;
            margin-right: 5px;
            opacity: 1;
            border-radius: 1px;
            transform: rotate(135deg);
            background: rgba(127, 148, 229, 1);
          }
          span {
            color: rgba(98, 102, 108, 1);
            i {
              color: rgba(55, 57, 60, 1);
            }
          }
          ul {
            width: 100%;
            display: flex;
            justify-content: space-between;
            color: rgba(98, 102, 108, 1);
            .ulLeft {
              height: 20px;
              text-align: center;
              opacity: 1;
              color: #fff;
              background: rgba(38, 119, 255, 1);
              margin: 8px 0 10px;
            }
            .ulRight {
              height: 20px;
              text-align: center;
              opacity: 1;
              color: #fff;
              background: rgba(209, 213, 221, 1);
              margin: 8px 0 10px;
            }
          }
        }
      }
    }
  }
  .right1 {
    width: 67%;
    flex-grow: 1;
    height: 390px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    padding: 12px 16px 16px 16px;
    box-sizing: border-box;
  }
}
.headerEcharts {
  padding-bottom: 12px;
  color: #37393c;
  font-weight: 500;
  border-bottom: 1px solid #e9ebef;
  margin-bottom: 12px;
}
.chart_one,
#chart_thr {
  height: calc(100% - 56px);
}
#chart_two {
  height: 100%;
}
.chart_four {
  height: calc(100% - 77px);
}
/deep/.el-progress-bar__outer {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(1px);
}
/deep/.el-progress-bar {
  position: relative;
  padding-right: 0px;
}
/deep/.el-progress__text {
  position: absolute;
  top: 25px;
  right: 0px;
  font-weight: 500;
  color: #ffffff;
}
/deep/.el-progress-bar__inner {
  background-color: #268bff;
}
.redBox {
  color: #2677ff;
  margin-left: 5px;
  font-weight: 600;
}
</style>
