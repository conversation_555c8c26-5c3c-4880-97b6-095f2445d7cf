<template>
  <div class="container">
    <p class="checkMore"><span @click="checkMoreData">查看更多</span></p>
    <el-table
      :data="tableData"
      v-loading="loading"
      row-key="id"
      :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
      ref="eltable"
      height="91%"
      style="width: 100%"
    >
      <!-- 只有单位资产测绘才有企业名称 -->
      <el-table-column
        v-for="item in tableHeader.filter((item) => {
          return item.path.indexOf(pageIcon) > -1
        })"
        :key="item.id"
        :prop="item.name"
        align="left"
        :min-width="item.minWidth"
        :label="item.label"
      >
        <template slot-scope="scope">
          <!-- 扫描 -->
          <span v-if="pageIcon == 3 && item.name == 'status'">{{
            getTableStatus(scope.row[item.name])
          }}</span>
          <span v-else-if="pageIcon == 3 && item.name == 'use_seconds'">{{
            secondsFormat(scope.row[item.name])
          }}</span>
          <!-- 域名 -->
          <span v-else-if="pageIcon == 6 && item.name == 'status'">{{
            getDomainStatus(scope.row[item.name])
          }}</span>
          <span v-else-if="pageIcon == 6 && item.name == 'modify'">{{
            scope.row[item.name] == '0' ? '枚举模式' : '验证模式'
          }}</span>
          <!-- 核查，检测 -->
          <span v-else-if="(pageIcon == 4 || pageIcon == 5) && item.name == 'status'">{{
            getState(scope.row[item.name])
          }}</span>
          <!-- 单位，云端 -->
          <span v-else-if="(pageIcon == 1 || pageIcon == 2) && item.name == 'step'">{{
            getStep(scope.row)
          }}</span>
          <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import { getAuditTask, getDetectTask } from '@/api/apiConfig/api.js'
import { getAllTaskList } from '@/api/apiConfig/discovery.js'
import { detectTaskList } from '@/api/apiConfig/surveying.js'
import { domainAssetTaskList } from '@/api/apiConfig/domain.js'

export default {
  props: ['pageIcon'],
  data() {
    return {
      tableHeader: [
        // path:[1,2,3,4,5,6]，六种任务类型
        {
          label: '企业名称',
          name: 'name',
          minWidth: 100,
          path: [1, 2]
        },
        {
          label: '线索数量',
          name: 'clues_count',
          minWidth: 60,
          path: [1, 2]
        },
        {
          label: '进度',
          name: 'step',
          minWidth: 120,
          path: [1, 2]
        },
        {
          label: '发起人',
          name: 'username',
          path: [1, 2]
        },
        {
          label: '任务名称',
          name: 'name',
          fixed: 'left',
          minWidth: '200',
          path: [3, 6]
        },
        {
          label: '任务状态',
          name: 'status',
          minWidth: '90',
          path: [3, 6]
        },
        {
          label: '任务耗时',
          name: 'use_seconds',
          minWidth: '60',
          path: [3]
        },
        {
          label: '任务模式',
          name: 'modify',
          minWidtth: '70',
          path: [6]
        },
        {
          label: '爆破层级',
          name: 'level',
          minWidtth: '90',
          path: [6]
        },
        {
          label: '导入资产',
          name: 'import_assets',
          minWidth: 120,
          path: [4, 5]
        },
        {
          label: '进度',
          name: 'status',
          minWidth: 120,
          path: [4, 5]
        },
        {
          label: '未纳入管理资产',
          name: 'not_included_assets',
          minWidth: 120,
          path: [4]
        },
        {
          label: '已纳入管理资产',
          name: 'included_assets',
          minWidth: 120,
          path: [4]
        },
        {
          label: '在线资产',
          name: 'online_assets',
          minWidth: 120,
          path: [5]
        },
        {
          label: '离线资产',
          name: 'unonline_assets',
          minWidth: 120,
          path: [5]
        }
      ],
      loading: false,
      tableData: []
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getTableData()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getTableData()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    },
    getterWebsocketMessage(msg) {}
  },
  computed: {
    ...mapState(['currentCompany', 'recommentFlag']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    getStep(row) {
      let str = ''
      if (row.step == 2) {
        if (row.step_detail == 200) {
          str = row.step_status == 1 ? '获取基础线索已完成' : '获取基础线索未完成'
        } else if (row.step_detail == 201) {
          str = row.step_status == 1 ? '域名扩展已完成' : '正在进行域名扩展'
        } else if (row.step_detail == 202) {
          str = row.step_status == 1 ? 'ICP扩展已完成' : '正在进行ICP扩展'
        } else if (row.step_detail == 203) {
          str = row.step_status == 1 ? '证书扩展已完成' : '正在进行证书扩展'
        } else if (row.step_detail == 204) {
          str = row.step_status == 1 ? '扩展线索已完成' : '扩展线索未完成'
        } else if (row.step_detail == 205) {
          str = row.step_status == 1 ? '线索总表已生成' : '线索总表未生成'
        } else if (row.step_detail == 206) {
          str = row.step_status == 1 ? 'IP段扩展已完成' : '正在进行IP段扩展'
        }
      } else if (row.step == 3) {
        if (row.step_detail == 300) {
          str = row.step_status == 1 ? '云端推荐已完成' : '云端推荐未完成'
        } else if (row.step_detail == 301) {
          str = row.step_status == 1 ? '三方导入数据融合已完成' : '三方导入数据融合未完成'
        }
      } else if (row.step == 4) {
        if (row.step_detail == 400) {
          str = row.step_status == 1 ? '资产评估已完成' : '资产评估未完成'
        } else if (row.step_detail == 401) {
          str = row.step_status == 1 ? '资产入账扫描已完成' : '资产入账扫描未完成'
        }
      } else if (row.step == 5) {
        if (row.status == 0) {
          str = '关联任务自定义未完成'
        } else {
          str = '已完成'
        }
      }
      return str
    },
    getDomainStatus(status) {
      let statusLabel = ''
      if (status == 0) {
        statusLabel = '等待扫描'
      } else if (status == 1) {
        statusLabel = '扫描中'
      } else if (status == 5) {
        statusLabel = '扫描完成'
      } else if (status == 3) {
        statusLabel = '扫描失败'
      } else if (status == 4) {
        statusLabel = '暂停扫描'
      } else {
        statusLabel = '扫描中'
      }
      return statusLabel
    },
    getTableStatus(status) {
      let statusLabel = ''
      if (status == 0) {
        statusLabel = '等待扫描'
      } else if (status == 1) {
        statusLabel = '扫描中'
      } else if (status == 2) {
        statusLabel = '扫描完成'
      } else if (status == 3) {
        statusLabel = '扫描失败'
      } else if (status == 4) {
        statusLabel = '暂停扫描'
      }
      return statusLabel
    },
    getState(val) {
      let str = ''
      if (val == 1) {
        str = '进行中'
      } else if (val == 2) {
        str = '已完成'
      } else {
        str = ''
      }
      return str
    },
    getTableData() {
      let obj = {}
      let runFunc = ''
      if (this.pageIcon == 1 || this.pageIcon == 2) {
        obj = {
          expand_source: this.pageIcon == 1 ? '0' : '1' // 0单位，1云端
        }
        obj.page = 1
        obj.per_page = 10
        runFunc = detectTaskList
      } else if (this.pageIcon == 3) {
        obj = {
          type: '',
          task_type: 1, // 任务分类 1 资产扫描 2 漏洞扫描,
          status: '', // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
          sort_order: 'asc',
          is_schedule: 0
        }
        obj.page = 1
        obj.per_page = 10
        runFunc = getAllTaskList
      } else if (this.pageIcon == 4) {
        obj.current_page = 1
        obj.per_page = 10
        runFunc = getAuditTask
      } else if (this.pageIcon == 5) {
        obj.current_page = 1
        obj.per_page = 10
        runFunc = getDetectTask
      } else if (this.pageIcon == 6) {
        obj.page = 1
        obj.per_page = 10
        obj.status = '' // 已完成
        runFunc = domainAssetTaskList
      }
      obj.operate_company_id = this.currentCompany
      this.loading = true
      runFunc(obj)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items ? res.data.items : []
          this.total = res.data.total ? res.data.total : 0
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    checkMoreData() {
      if (this.pageIcon == 1) {
        sessionStorage.setItem('menuId', '2-3')
        this.changeMenuId('2-3')
        this.$router.push('/unitIndex')
      } else if (this.pageIcon == 2) {
        sessionStorage.setItem('menuId', '2-2')
        this.changeMenuId('2-2')
        this.$router.push('/assetsCloud')
      } else if (this.pageIcon == 3) {
        sessionStorage.setItem('menuId', '2-1')
        this.changeMenuId('2-1')
        this.$router.push('/assetsScan')
      } else if (this.pageIcon == 4) {
        sessionStorage.setItem('menuId', '2-4')
        this.changeMenuId('2-4')
        this.$router.push('/checkTask')
      } else if (this.pageIcon == 5) {
        sessionStorage.setItem('menuId', '2-5')
        this.changeMenuId('2-5')
        this.$router.push('/statusTask')
      } else if (this.pageIcon == 6) {
        sessionStorage.setItem('menuId', '2-6')
        this.changeMenuId('2-6')
        this.$router.push('/domainTask')
      }
      return str
    },
    getDomainStatus(status) {
      let statusLabel = ''
      if (status == 0) {
        statusLabel = '等待扫描'
      } else if (status == 1) {
        statusLabel = '扫描中'
      } else if (status == 5) {
        statusLabel = '扫描完成'
      } else if (status == 3) {
        statusLabel = '扫描失败'
      } else if (status == 4) {
        statusLabel = '暂停扫描'
      } else {
        statusLabel = '扫描中'
      }
      return statusLabel
    },
    getTableStatus(status) {
      let statusLabel = ''
      if (status == 0) {
        statusLabel = '等待扫描'
      } else if (status == 1) {
        statusLabel = '扫描中'
      } else if (status == 2) {
        statusLabel = '扫描完成'
      } else if (status == 3) {
        statusLabel = '扫描失败'
      } else if (status == 4) {
        statusLabel = '暂停扫描'
      }
      return statusLabel
    },
    getState(val) {
      let str = ''
      if (val == 1) {
        str = '进行中'
      } else if (val == 2) {
        str = '已完成'
      } else {
        str = ''
      }
      return str
    },
    getTableData() {
      let obj = {}
      let runFunc = ''
      if (this.pageIcon == 1 || this.pageIcon == 2) {
        obj = {
          expand_source: this.pageIcon == 1 ? '0' : '1' // 0单位，1云端
        }
        obj.page = 1
        obj.per_page = 10
        runFunc = 'detectTaskList'
      } else if (this.pageIcon == 3) {
        obj = {
          type: '',
          task_type: 1, // 任务分类 1 资产扫描 2 漏洞扫描,
          status: '', // 0/1/2/3/4 等待扫描/扫描中/扫描完成/扫描失败/暂停扫描,
          sort_order: 'asc',
          is_schedule: 0
        }
        obj.page = 1
        obj.per_page = 10
        runFunc = 'getAllTaskList'
      } else if (this.pageIcon == 4) {
        obj.current_page = 1
        obj.per_page = 10
        runFunc = 'getAuditTask'
      } else if (this.pageIcon == 5) {
        obj.current_page = 1
        obj.per_page = 10
        runFunc = 'getDetectTask'
      } else if (this.pageIcon == 6) {
        obj.page = 1
        obj.per_page = 10
        obj.status = '' // 已完成
        runFunc = 'domainAssetTaskList'
      }
      obj.operate_company_id = this.currentCompany
      this.loading = true
      this[runFunc](obj)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items ? res.data.items : []
          this.total = res.data.total ? res.data.total : 0
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    checkMoreData() {
      if (this.pageIcon == 1) {
        sessionStorage.setItem('menuId', '2-3')
        this.changeMenuId('2-3')
        this.$router.push('/unitIndex')
      } else if (this.pageIcon == 2) {
        sessionStorage.setItem('menuId', '2-2')
        this.changeMenuId('2-2')
        this.$router.push('/assetsCloud')
      } else if (this.pageIcon == 3) {
        sessionStorage.setItem('menuId', '2-1')
        this.changeMenuId('2-1')
        this.$router.push('/assetsScan')
      } else if (this.pageIcon == 4) {
        sessionStorage.setItem('menuId', '2-4')
        this.changeMenuId('2-4')
        this.$router.push('/checkTask')
      } else if (this.pageIcon == 5) {
        sessionStorage.setItem('menuId', '2-5')
        this.changeMenuId('2-5')
        this.$router.push('/statusTask')
      } else if (this.pageIcon == 6) {
        sessionStorage.setItem('menuId', '2-6')
        this.changeMenuId('2-6')
        this.$router.push('/domainTask')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  .checkMore {
    padding: 0 0 12px 0;
    color: rgba(38, 119, 255, 1);
    text-align: right;
    cursor: pointer;
  }
}
</style>
