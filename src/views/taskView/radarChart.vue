<template>
  <div class="con">
    <div v-loading="loadingLd" id="chart_one"></div>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'

export default {
  props: ['taskIcon', 'radarTrendData'],
  data() {
    return {
      loadingLd: false,
      hidtoryData: [
        // 1，2，3，4，5，6
        {
          value: 0,
          itemStyle: {
            color: 'rgba(38, 119, 255, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(19, 183, 255, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(16, 213, 149, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(127, 148, 229, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(62, 105, 168, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(112, 157, 230, 1)'
          }
        }
      ],
      optionArr: {
        radarOption: {
          color: ['#2677FF'], // 测绘线条颜色以及中心阴影部分颜色
          radar: [
            // 雷达图数据:'单位资产测绘', '云端资产推荐', '资产扫描任务', '资产核查任务', '资产状态检测', '域名发现任务'
            {
              indicator: [
                { name: '单位资产测绘' },
                { name: '云端资产推荐' },
                { name: '资产扫描任务' },
                { name: '资产核查任务' },
                { name: '资产状态检测' },
                { name: '域名发现任务' }
              ],
              center: ['50%', '50%'],
              // radius: 120,
              startAngle: 90, // 旋转度数
              splitNumber: 3, // 雷达图分几圈
              splitArea: {
                areaStyle: {
                  color: [
                    'rgba(255, 255, 255, 0.9)',
                    'rgba(240, 245, 255, 1)',
                    'rgba(38, 119, 255, 0.12)'
                  ], // 雷达图背景颜色，由内向外
                  shadowColor: 'rgba(38, 119, 255, 0.12)',
                  shadowBlur: 2
                }
              },
              axisName: {
                color: 'rgba(98, 102, 108, 1)',
                backgroundColor: 'transparent',
                padding: [3, 5]
              },
              axisLine: {
                lineStyle: {
                  color: 'rgba(0,0,0,0)' // 雷达图中心向外的线
                }
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(38, 119, 255, 0.08)' // 雷达图分层的边框线
                }
              }
            }
          ],
          tooltip: {
            trigger: 'axis'
          },
          series: [
            {
              type: 'radar',
              tooltip: {
                trigger: 'item'
              },
              emphasis: {
                lineStyle: {
                  width: 2
                }
              },
              label: {
                normal: {
                  show: true,
                  formatter: (params) => {
                    return params.value
                  },
                  color: '#979797FF'
                }
              },
              data: [
                {
                  value: [],
                  name: '任务数量',
                  areaStyle: {
                    color: 'rgba(38, 119, 255, 0.16)',
                    size: '6px'
                  }
                }
              ]
            }
          ]
        }
      }
    }
  },
  watch: {
    radarTrendData() {
      this.getTaskChange()
      this.getTypeCount()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.getTaskChange()
    this.getTypeCount()
  },
  methods: {
    async getTaskChange() {
      this.myChart_one = this.$echarts.init(document.getElementById('chart_one'))
    },
    async getTypeCount() {
      this.loadingLd = true
      this.loadingLd = false
      this.asset_audit_count =
        this.radarTrendData.asset_audit && this.radarTrendData.asset_audit.total
          ? this.radarTrendData.asset_audit.total
          : 0
      this.asset_status_count =
        this.radarTrendData.asset_status && this.radarTrendData.asset_status.total
          ? this.radarTrendData.asset_status.total
          : 0
      this.detect_asset_task_count =
        this.radarTrendData.detect_asset_task && this.radarTrendData.detect_asset_task.total
          ? this.radarTrendData.detect_asset_task.total
          : 0
      this.domain_find_count =
        this.radarTrendData.domain_find && this.radarTrendData.domain_find.total
          ? this.radarTrendData.domain_find.total
          : 0
      this.recommend_record_count =
        this.radarTrendData.recommend_record && this.radarTrendData.recommend_record.total
          ? this.radarTrendData.recommend_record.total
          : 0
      this.scan_task_count =
        this.radarTrendData.scan_task && this.radarTrendData.scan_task.total
          ? this.radarTrendData.scan_task.total
          : 0
      // 雷达图和资产变化图数据
      let arr = [
        this.detect_asset_task_count,
        this.recommend_record_count,
        this.scan_task_count,
        this.asset_audit_count,
        this.asset_status_count,
        this.domain_find_count
      ]
      this.optionArr.radarOption.radar[0].indicator.forEach((item) => {
        item['max'] = Math.max.apply(null, arr)
      })
      // 雷达图数据:'单位资产测绘', '云端资产推荐', '资产扫描任务', '资产核查任务', '资产状态检测', '域名发现任务'
      this.optionArr.radarOption.series[0].data[0].value = arr
      this.pie(this.optionArr.radarOption, 'one')
    },
    pie(option, id) {
      this['myChart_' + id].clear()
      this['myChart_' + id].setOption(option, true)
      window.addEventListener('resize', () => {
        this['myChart_' + id].resize()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.con {
  width: 100%;
  height: 100%;
  & > div {
    width: 100%;
    height: 100%;
  }
}
</style>
