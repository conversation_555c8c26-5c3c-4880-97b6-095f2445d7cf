<template>
  <div class="right">
    <div class="barClass">
      <i class="point"></i>
      <span
        >总计：<i>{{
          (itemData.included_assets ? itemData.included_assets : 0) +
          (itemData.not_included_assets ? itemData.not_included_assets : 0)
        }}</i></span
      >
      <ul class="numClass">
        <li>{{ itemData.included_assets ? itemData.included_assets : 0 }}</li>
        <li>{{ itemData.not_included_assets ? itemData.not_included_assets : 0 }}</li>
      </ul>
      <ul>
        <li class="ulLeft" :style="{ width: setWidth('1') }"></li>
        <li class="ulRight" :style="{ width: setWidth('2') }"></li>
      </ul>
      <ul>
        <li>已纳入管理</li>
        <li>未纳入管理</li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: ['itemData'],
  watch: {
    itemData() {
      this.setWidth('1')
      this.setWidth('2')
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  methods: {
    setWidth(icon) {
      let included_assets = this.itemData.included_assets ? this.itemData.included_assets : 0
      let not_included_assets = this.itemData.not_included_assets
        ? this.itemData.not_included_assets
        : 0
      let sum = included_assets + not_included_assets
      let width = 0
      if (sum == 0) {
        width = 0
      } else {
        if (icon == 1) {
          width = included_assets / sum
        } else {
          width = not_included_assets / sum
        }
      }
      return (width * 100).toFixed(6) + '%'
    }
  }
}
</script>
<style lang="less" scoped></style>
