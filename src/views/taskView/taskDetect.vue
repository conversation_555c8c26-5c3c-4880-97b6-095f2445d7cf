<template>
  <div class="right">
    <div class="recommentClass">
      <div id="detect_one"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: ['itemData'],
  data() {
    return {
      detectData: [
        {
          value: 0,
          itemStyle: {
            color: 'rgba(38, 119, 255, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(248, 193, 54, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(255, 70, 70, 1)'
          }
        }
      ]
    }
  },
  watch: {
    itemData() {
      this.init()
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.myChart_detect = this.$echarts.init(document.getElementById('detect_one'))
    this.domainOption = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        data: ['资产台账', '疑似资产', '威胁资产'],
        axisTick: { show: false },
        axisLine: { show: true, lineStyle: { color: 'rgba(233, 235, 239, 1)' } },
        axisLabel: {
          color: 'rgba(98, 102, 108, 1)'
        }
      },
      yAxis: {
        splitLine: { show: false },
        axisTick: { show: false },
        axisLine: { show: false },
        axisLabel: { show: false }
      },
      color: ['rgba(38, 119, 255, 1)', 'rgba(248, 193, 54, 0.8)', 'rgba(255, 70, 70, 0.8)'],
      grid: {
        left: '2%',
        right: '2%',
        top: '0',
        bottom: '1%',
        containLabel: true
      },
      series: [
        {
          name: '数量',
          type: 'pictorialBar',
          barCategoryGap: '20%',
          symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
          itemStyle: {
            opacity: 1
          },
          emphasis: {
            itemStyle: {
              opacity: 1
            }
          },
          // data: [123, 60, 25],
          data: this.detectData,
          z: 10
        }
      ]
    }
    this.init()
  },
  methods: {
    init() {
      this.detectData[0].value = this.itemData.account_assets ? this.itemData.account_assets : 0
      this.detectData[1].value = this.itemData.suspected_assets ? this.itemData.suspected_assets : 0
      this.detectData[2].value = this.itemData.threat_assets ? this.itemData.threat_assets : 0
      this.domainOption.series[0].data = this.detectData
      this.pie('detect_one', 'one')
    },
    pie(id, options) {
      this.myChart_detect.clear()
      this.myChart_detect.setOption(this.domainOption, true)
      window.addEventListener('resize', () => {
        this.myChart_detect.resize()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.recommentClass {
  height: 100%;
  padding: 0 0;
  box-sizing: border-box;
  #detect_one {
    height: 112px;
    & > div {
      width: 100%;
    }
  }
}
</style>
