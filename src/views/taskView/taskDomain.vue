<template>
  <div class="right">
    <div class="recommentClass">
      <div id="domain_one"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: ['itemData'],
  data() {
    return {}
  },
  watch: {
    itemData() {
      this.domainOption.series[0].data = [
        {
          name: '枚举模式',
          value: this.itemData.enum_type
        },
        {
          name: '验证模式',
          value: this.itemData.verify_type
        }
      ]
      this.pie('domain_one', 'one')
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.myChart_domain = this.$echarts.init(document.getElementById('domain_one'))
    this.domainOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}<br/>{c}({d}%)'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '1%',
        containLabel: true
      },
      color: ['#3888F9', '#59D1BB'],
      series: [
        {
          name: '',
          type: 'pie',
          radius: ['50%', '65%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'outside'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14'
            }
          },
          labelLine: {
            show: true
          },
          data: this.itemData
        }
      ]
    }
    this.init()
  },
  methods: {
    init() {
      this.domainOption.series[0].data = [
        {
          name: '枚举模式',
          value: this.itemData.enum_type
        },
        {
          name: '验证模式',
          value: this.itemData.verify_type
        }
      ]
      this.pie('domain_one', 'one')
    },
    pie(id, options) {
      this.myChart_domain.clear()
      this.myChart_domain.setOption(this.domainOption, true)
      window.addEventListener('resize', () => {
        this.myChart_domain.resize()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.recommentClass {
  height: 100%;
  padding: 0 0;
  box-sizing: border-box;
  #domain_one {
    height: 112px;
    & > div {
      width: 100%;
    }
  }
}
</style>
