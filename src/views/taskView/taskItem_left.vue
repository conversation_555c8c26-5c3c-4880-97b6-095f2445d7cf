<template>
  <div class="left">
    <el-progress
      type="circle"
      :percentage="percentage ? percentage : 0"
      :format="format"
      :width="52"
    ></el-progress>
    <p>任务数量</p>
  </div>
</template>
<script>
export default {
  props: ['percentage', 'total'],
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  methods: {
    format(percentage) {
      return `${this.total}`
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.el-progress__text {
  color: rgba(55, 57, 60, 1) !important;
}
</style>
