<template>
  <div class="right">
    <div class="recommentClass">
      <div id="item_one"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: ['itemData'],
  data() {
    return {
      itemChart_one: null,
      recommentData: [
        // 台账，疑似，威胁
        {
          value: 0,
          itemStyle: {
            color: 'rgba(38, 119, 255, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(248, 193, 54, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(255, 70, 70, 1)'
          }
        }
      ]
    }
  },
  watch: {
    itemData() {
      this.getTaskChange()
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.itemChart_one = this.$echarts.init(document.getElementById('item_one'))
    this.recommentOption = {
      xAxis: {
        type: 'category',
        data: ['资产台账', '疑似资产', '威胁资产'],
        axisLine: {
          show: true,
          lineStyle: {
            color: '#D9E0EE'
          }
        },
        axisLabel: {
          color: '#62666C'
        }
      },
      yAxis: {
        show: false,
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#D9E0EE'
          }
        },
        axisLabel: {
          color: '#62666C'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(217,224,238,0.36)'
          }
        }
      },
      grid: {
        left: '2%',
        right: '2%',
        top: '0',
        bottom: '1%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis'
      },
      series: [
        {
          data: this.recommentData,
          name: '数量',
          type: 'bar',
          barWidth: '16px'
        }
      ]
    }
    this.getTaskChange()
  },
  methods: {
    async getTaskChange() {
      this.$nextTick(() => {
        this.recommentData.forEach((item, index) => {
          item.value = this.itemData[index] ? this.itemData[index] : 0
        })
        this.recommentOption.series[0].data = this.recommentData
        this.pie(this.recommentOption, 'one')
      })
    },
    pie(option, id) {
      this.itemChart_one.clear()
      this.itemChart_one.setOption(option, true)
      window.addEventListener('resize', () => {
        this.itemChart_one.resize()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.recommentClass {
  height: 100%;
  padding: 0 0;
  box-sizing: border-box;
  #item_one {
    height: 112px;
    & > div {
      width: 100%;
    }
  }
}
</style>
