<template>
  <div class="right">
    <div class="scanClass">
      <ul>
        <li
          ><span>发现IP数量</span><i>{{ ip }}</i
          ><span class="botLine" :style="{ width: (ip / totalNum) * 100 + '%' }"></span
        ></li>
        <li
          ><span>发现组件数量</span><i>{{ component }}</i
          ><span class="botLine" :style="{ width: (component / totalNum) * 100 + '%' }"></span
        ></li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: ['itemData'],
  data() {
    return {
      totalNum: 0,
      ip: 0,
      component: 0
    }
  },
  watch: {
    itemData() {
      this.ip = this.itemData.ip ? this.itemData.ip : 0
      this.component = this.itemData.component ? this.itemData.component : 0
      let arr = [this.ip, this.component]
      this.totalNum = Math.max.apply(null, arr) + 2 // 获取数量最大值
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.ip = this.itemData.ip ? this.itemData.ip : 0
    this.component = this.itemData.component ? this.itemData.component : 0
    let arr = [this.ip, this.component]
    this.totalNum = Math.max.apply(null, arr) + 2 // 获取数量最大值
  },
  methods: {}
}
</script>
<style lang="less" scoped>
.scanClass {
  height: 100%;
  ul {
    height: 100%;
    li {
      position: relative;
      height: 48px;
      line-height: 48px;
      display: flex;
      justify-content: space-between;
      padding: 0 12px;
      margin-bottom: 12px;
      opacity: 1;
      border-radius: 2px;
      background: rgba(245, 248, 252, 1);
      i {
        color: #2677ff;
        font-weight: 500;
      }
    }
    .botLine {
      position: absolute;
      bottom: 0;
      left: 0;
      display: inline-block;
      height: 2px;
      background: linear-gradient(90deg, rgba(78, 175, 255, 1) 0%, rgba(38, 118, 255, 1) 100%);
    }
  }
}
</style>
