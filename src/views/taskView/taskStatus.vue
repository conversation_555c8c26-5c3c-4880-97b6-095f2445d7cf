<template>
  <div class="right">
    <div class="barClass">
      <i class="point"></i>
      <span
        >总计：<i>{{
          (itemData.online_assets ? itemData.online_assets : 0) +
          (itemData.offline_assets ? itemData.offline_assets : 0)
        }}</i></span
      >
      <ul class="numClass">
        <li>{{ itemData.online_assets ? itemData.online_assets : 0 }}</li>
        <li>{{ itemData.offline_assets ? itemData.offline_assets : 0 }}</li>
      </ul>
      <ul>
        <li class="ulLeft" :style="{ width: setWidth('1') }"></li>
        <li class="ulRight" :style="{ width: setWidth('2') }"></li>
      </ul>
      <ul>
        <li>在线资产</li>
        <li>离线资产</li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: ['itemData'],
  watch: {
    itemData() {
      this.setWidth('1')
      this.setWidth('2')
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  methods: {
    setWidth(icon) {
      let online_assets = this.itemData.online_assets ? this.itemData.online_assets : 0
      let offline_assets = this.itemData.offline_assets ? this.itemData.offline_assets : 0
      let sum = online_assets + offline_assets
      let width = 0
      if (sum == 0) {
        width = 0
      } else {
        if (icon == 1) {
          width = online_assets / sum
        } else {
          width = offline_assets / sum
        }
      }
      return (width * 100).toFixed(6) + '%'
    }
  }
}
</script>
<style lang="less" scoped></style>
