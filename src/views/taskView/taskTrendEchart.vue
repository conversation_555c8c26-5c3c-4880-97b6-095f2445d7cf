<template>
  <div class="con">
    <div v-loading="loadingLd" id="chart_four"></div>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'

export default {
  props: ['taskIcon', 'radarTrendData'],
  data() {
    return {
      loadingLd: false,
      hidtoryData: [
        // 1，2，3，4，5，6
        {
          value: 0,
          itemStyle: {
            color: 'rgba(38, 119, 255, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(19, 183, 255, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(16, 213, 149, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(127, 148, 229, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(62, 105, 168, 1)'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: 'rgba(112, 157, 230, 1)'
          }
        }
      ],
      optionArr: {
        taskHistory: {
          xAxis: {
            type: 'category',
            data: [
              '单位资产测绘',
              '云端资产推荐',
              '资产扫描任务',
              '资产核查任务',
              '资产状态检测',
              '域名发现任务'
            ],
            axisLine: {
              show: true,
              lineStyle: {
                color: '#D9E0EE'
              }
            },
            axisLabel: {
              color: '#62666C'
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#D9E0EE'
              }
            },
            axisLabel: {
              color: '#62666C'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(217,224,238,0.36)'
              }
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            top: '8%',
            bottom: '3%',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis'
          },
          series: [
            {
              data: [],
              name: '数量',
              type: 'bar',
              barWidth: '16px'
            }
          ]
        }
      }
    }
  },
  watch: {
    radarTrendData() {
      this.getTaskChange()
      this.getTypeCount()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.getTaskChange()
    this.getTypeCount()
  },
  methods: {
    async getTaskChange() {
      this.myChart_four = this.$echarts.init(document.getElementById('chart_four'))
    },
    async getTypeCount() {
      this.loadingLd = true
      this.loadingLd = false
      this.asset_audit_count =
        this.radarTrendData.asset_audit && this.radarTrendData.asset_audit.total
          ? this.radarTrendData.asset_audit.total
          : 0
      this.asset_status_count =
        this.radarTrendData.asset_status && this.radarTrendData.asset_status.total
          ? this.radarTrendData.asset_status.total
          : 0
      this.detect_asset_task_count =
        this.radarTrendData.detect_asset_task && this.radarTrendData.detect_asset_task.total
          ? this.radarTrendData.detect_asset_task.total
          : 0
      this.domain_find_count =
        this.radarTrendData.domain_find && this.radarTrendData.domain_find.total
          ? this.radarTrendData.domain_find.total
          : 0
      this.recommend_record_count =
        this.radarTrendData.recommend_record && this.radarTrendData.recommend_record.total
          ? this.radarTrendData.recommend_record.total
          : 0
      this.scan_task_count =
        this.radarTrendData.scan_task && this.radarTrendData.scan_task.total
          ? this.radarTrendData.scan_task.total
          : 0
      // 雷达图和资产变化图数据
      let arr = [
        this.detect_asset_task_count,
        this.recommend_record_count,
        this.scan_task_count,
        this.asset_audit_count,
        this.asset_status_count,
        this.domain_find_count
      ]
      // 资产历史变化
      this.hidtoryData.forEach((item, index) => {
        item.value = arr[index]
      })
      this.optionArr.taskHistory.series[0].data = this.hidtoryData
      this.pie(this.optionArr.taskHistory, 'four')
    },
    pie(option, id) {
      this['myChart_' + id].clear()
      this['myChart_' + id].setOption(option, true)
      window.addEventListener('resize', () => {
        this['myChart_' + id].resize()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.con {
  width: 100%;
  height: 100%;
  & > div {
    width: 100%;
    height: 100%;
  }
}
</style>
