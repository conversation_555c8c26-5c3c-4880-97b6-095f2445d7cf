<template>
  <div class="container">
    <div class="headerTitle" style="width: calc(100% - 32px) !important">
      <div>
        <span
          class="goback"
          @click="$router.push({ path: '/unitIndex', query: { activeName: 'second' } })"
          ><i class="el-icon-arrow-left"></i><span>返回上一层</span
          ><span class="spline">/</span></span
        >
        单位资产测绘
      </div>
    </div>
    <div class="div">
      <div class="numberDiv">
        <div class="tuFinish"> </div>
        <div class="right">
          <div class="header">
            <el-tooltip
              slot="prepend"
              class="item"
              effect="dark"
              :content="companyTitleList && companyTitleList.slice(0, 3).join('、')"
              placement="top"
              :open-delay="100"
            >
              <span class="company blue-font-color">{{
                companyTitleList && companyTitleList.slice(0, 3).join('、')
              }}</span>
            </el-tooltip>
            等
            <span class="blue-font-color">{{ companyTitleList && companyTitleList.length }}</span>
            个企业资产测绘完成</div
          >
          <div class="middle"></div>
          <div class="content">
            <div class="listHeader">
              <img src="../../assets/images/taskSed.png" alt="" />
              输出结果如下
            </div>
            <ul class="list">
              <li>
                <span class="sort">1</span>关联发现进入台账的IP资产<span class="blue-font-color">
                  {{ asset_discovery_stat.asset_table }} </span
                >个，域名资产<span class="blue-font-color"> {{ result_stat.asset_domain }} </span
                >个，证书资产<span class="blue-font-color"> {{ result_stat.asset_cert }} </span
                >个，登录入口资产<span class="blue-font-color"> {{ result_stat.asset_login }} </span
                >个，URL（api）资产<span class="blue-font-color">
                  {{ asset_discovery_stat.url_api_num }} </span
                >个；
              </li>
              <li>
                <span class="sort">2</span>经过对比识别，发现影子资产<span class="blue-font-color">
                  {{ result_stat.asset_shadow }} </span
                >个；
              </li>
              <li>
                <span class="sort">3</span>发现数字资产<span class="blue-font-color">
                  {{ result_stat.digital }} </span
                >个，其中公众号<span class="blue-font-color">
                  {{ result_stat.digital_wechat }} </span
                >个，APP<span class="blue-font-color"> {{ result_stat.digital_app }} </span>个；
              </li>
              <li>
                <span class="sort">4</span>发现数据泄露事件<span class="blue-font-color">
                  {{ result_stat.data_leak }} </span
                >个，其中网盘<span class="blue-font-color">
                  {{ result_stat.data_leak_net_disk }} </span
                >个，文库<span class="blue-font-color"> {{ result_stat.data_leak_library }} </span
                >个，代码仓库<span class="blue-font-color"> {{ result_stat.data_leak_code }} </span
                >个；
              </li>
              <li>
                <span class="sort">5</span>发现风险事件<span class="blue-font-color">
                  {{ result_stat.risk_event }} </span
                >个。
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="task params div">
      <div class="title">
        <div class="block"></div>
        <div class="text">任务参数</div>
      </div>
      <div class="content">
        <div class="description">
          <div class="left"><img src="@/assets/images/unit/unitTaskParams.png" alt="" /></div>
          <div class="right">
            <div class="top"
              >本次您选择使用
              <span class="blue-font-color">{{
                detect_modeMap[detect_task_stat.detect_mode]
              }}</span>
              进行资产测绘</div
            >
            <div class="bottom" v-if="detect_task_stat.detect_mode == 1"
              >互联网资产梳理全面性80%、精准性85%、适用于第一次接触互联网暴露面或者对资产运营需求不深的用户</div
            >
            <div class="bottom" v-if="detect_task_stat.detect_mode == 2"
              >互联网资产梳理全面性90%、精准性90%、适用有一定资产运营经验，但是刚开始涉足互联网暴露面或者攻击面的用户</div
            >
            <div class="bottom" v-if="detect_task_stat.detect_mode == 3"
              >互联网资产梳理全面性95%、精准性98%、适用有一定资产梳理经验，可配合适用第三方技术和工具来发现隐藏的或未知资产的用户</div
            >
          </div>
          <div
            class="btn blue-font-color"
            @click="$router.push({ path: '/unitIndex', query: { activeName: 'first' } })"
          >
            试试其他模式
          </div>
        </div>
        <div class="taskContent">
          <div class="left">
            <div class="header">
              <span class="text1">测绘企业清单</span>
              <span class="text2"
                >控股比例：<span class="text1">{{
                  !detect_task_stat.percent && detect_task_stat.percent !== 0
                    ? '0%'
                    : detect_task_stat.percent
                }}</span></span
              >
            </div>
            <div class="treeContainer">
              <el-tree :data="detect_task_stat.detect_company_list" :props="defaultProps">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <el-tooltip
                    slot="prepend"
                    class="item"
                    effect="dark"
                    :content="node.label"
                    placement="top"
                    :open-delay="100"
                  >
                    <span class="tree-node">{{ node.label }}</span>
                  </el-tooltip>
                  <span class="holding" v-if="data.percent"
                    >控股 ：<span class="blue-text">{{ transferRate(data.percent) }}</span></span
                  >
                  <span class="master" v-if="data.company_flag == 'master'">主体</span>
                </span>
              </el-tree>
            </div>
          </div>
          <div class="right">
            <div class="top">
              <div class="header">已知线索表</div>
              <div
                class="listContainer"
                v-if="detect_task_stat.input_clues && detect_task_stat.input_clues.length !== 0"
              >
                <span
                  v-for="(item, key) in detect_task_stat.input_clues"
                  :key="key"
                  class="clueBlock"
                >
                  <span class="clueType">{{ clueTypeMap[item.type] }}</span>
                  <span v-if="item.type == 3">
                    <img
                      :src="item.content.includes('http') ? item.content : showSrcIp + item.content"
                      alt=""
                      style="width: 23px; height: 23px"
                    />
                  </span>
                  <span v-else>{{ item.content }}</span>
                </span>
              </div>
              <div class="listContainer" v-else>
                <div class="emptyClass">
                  <div>
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-kong"></use>
                    </svg>
                    <p>暂无数据</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="bottom">
              <div class="header">参数设置</div>
              <div class="bottomContainer">
                <div class="rowItem">
                  <span class="label">扫描端口：</span>
                  <span class="value">{{ scan_typeMap[detect_task_stat.scan_type] || '-' }}</span>
                </div>
                <div class="rowItem">
                  <span class="label">扫描带宽：</span>
                  <span class="value">{{ detect_task_stat.bandwidth || '-' }}</span
                  >&nbsp;kb
                </div>
                <div class="rowItem">
                  <span class="label">资产梳理范围：</span>
                  <span class="value">{{ fofa_rangeMap[detect_task_stat.fofa_range] || '-' }}</span>
                </div>
                <div class="rowItem">
                  <span class="label">离线资产：</span>
                  <span class="value">{{
                    off_assets_toMap[detect_task_stat.off_assets_to] || '-'
                  }}</span>
                </div>
                <div class="rowItem">
                  <span class="label">是否导入已知资产：</span>
                  <span class="value"
                    >{{ detect_task_stat.is_import_sure_ips ? '是' : '否' }} &nbsp;
                    <el-button
                      class="blue-text"
                      v-if="detect_task_stat.is_import_sure_ips"
                      type="text"
                      @click="download"
                      >下载文件</el-button
                    ></span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="task clue div">
      <div class="title">
        <div class="block"></div>
        <div class="text">线索获取详情</div>
      </div>
      <div class="content">
        <div class="top">
          <div class="stepContainer">
            <div class="step" v-for="(items, index) in clueStatIndex" :key="items.value">
              <div class="step_head">
                <div class="step_line"> </div>
                <div class="step_icon">
                  <div class="step__icon-inner">{{ index + 1 }}</div>
                </div>
              </div>
              <div class="step_main">
                <div class="step_title">{{ items.label }}</div>
                <div class="step_description">
                  <div
                    class="header"
                    :class="{ active: currentClueValue == items.value }"
                    @click="changeClueType(items)"
                  >
                    <span class="label">{{ items.title }}</span>
                    <span class="value">{{
                      clue_stat[items.value] && clue_stat[items.value].sum
                    }}</span>
                  </div>
                  <div class="clueType">
                    <span class="label">
                      <svg class="icon svg-icon" aria-hidden="true">
                        <use xlink:href="#icon-diamondNum"></use>
                      </svg>
                      IP
                    </span>
                    <span class="value">{{
                      clue_stat[items.value] && clue_stat[items.value].ip
                    }}</span>
                  </div>
                  <div class="clueType">
                    <span class="label">
                      <svg class="icon svg-icon" aria-hidden="true">
                        <use xlink:href="#icon-diamondNum"></use>
                      </svg>
                      域名
                    </span>
                    <span class="value">{{
                      clue_stat[items.value] && clue_stat[items.value].domain
                    }}</span>
                  </div>
                  <div class="clueType">
                    <span class="label">
                      <svg class="icon svg-icon" aria-hidden="true">
                        <use xlink:href="#icon-diamondNum"></use>
                      </svg>
                      证书
                    </span>
                    <span class="value">{{
                      clue_stat[items.value] && clue_stat[items.value].cert
                    }}</span>
                  </div>
                  <div class="clueType">
                    <span class="label">
                      <svg class="icon svg-icon" aria-hidden="true">
                        <use xlink:href="#icon-diamondNum"></use>
                      </svg>
                      ICON
                    </span>
                    <span class="value">{{
                      clue_stat[items.value] && clue_stat[items.value].icon
                    }}</span>
                  </div>
                  <div class="clueType">
                    <span class="label">
                      <svg class="icon svg-icon" aria-hidden="true">
                        <use xlink:href="#icon-diamondNum"></use>
                      </svg>
                      ICP
                    </span>
                    <span class="value">{{
                      clue_stat[items.value] && clue_stat[items.value].icp
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="clueTitle"
            >{{ currentClueLabel }} <span class="blue-font-color">{{ total || 0 }}</span></div
          >
          <div
            class="clueContent tableWapper"
            v-if="clueList && clueList.length != 0 && isShowMore"
          >
            <div ref="content" style="height: 100%">
              <LoadMore
                :id="currentClueValue"
                ref="LoadMore"
                :totalAllPage="totalAllPage"
                @setCurrentPage="setCurrentPage"
                @loadMore="onScrollBottom"
              >
                <span v-for="(item, index) in clueList" :key="index" class="clueBlock">
                  <span class="clueType">{{ clueTypeMap[item.type] }}</span>
                  <span v-if="item.type == 3">
                    <img
                      :src="item.content.includes('http') ? item.content : showSrcIp + item.content"
                      alt=""
                      style="width: 23px; height: 23px"
                    />
                  </span>
                  <span v-else>{{ item.content }}</span>
                </span>
              </LoadMore>
            </div>
          </div>
          <div
            class="clueContent clueEllipsis"
            v-else-if="clueList && clueList.length != 0 && !isShowMore"
          >
            <div ref="content">
              <span v-for="(item, index) in clueList" :key="index" class="clueBlock">
                <span class="clueType">{{ clueTypeMap[item.type] }}</span>
                <span v-if="item.type == 3">
                  <img
                    :src="item.content.includes('http') ? item.content : showSrcIp + item.content"
                    alt=""
                    style="width: 23px; height: 23px"
                  />
                </span>
                <span v-else>{{ item.content }}</span>
              </span>
              <div
                class="moreLoad"
                v-if="clueList && isShowMore && clueList.length != 0"
                @click="getMoreClue"
              >
                点击加载更多
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-more"></use>
                </svg>
              </div>
            </div>
          </div>
          <div class="clueContent clueEllipsis" v-else>
            <div class="emptyClass" ref="content">
              <div>
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-kong"></use>
                </svg>
                <p>暂无数据</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="task assets div">
      <div class="title">
        <div class="block"></div>
        <div class="text">资产发现详情</div>
      </div>
      <div class="content">
        <div class="imgContainer">
          <img src="../../assets/images/unit/unitAssetsFind.png" alt="" />
          <div class="block block1">
            自动发现<span class="blue-font-color"> {{ asset_discovery_stat.ip_discovery }} </span
            >个IP， 第三方导入<span class="blue-font-color">
              {{ asset_discovery_stat.ip_import }} </span
            >个IP
          </div>
          <div class="block block2">
            资产台帐入账<span class="blue-font-color"> {{ asset_discovery_stat.asset_table }} </span
            >个， 疑似资产入账<span class="blue-font-color">
              {{ asset_discovery_stat.asset_unsure }} </span
            >个， 威胁资产入账<span class="blue-font-color">
              {{ asset_discovery_stat.asset_threaten }} </span
            >个
          </div>
          <div class="block block3">
            识别出高可信度资产<span class="blue-font-color">
              {{ asset_discovery_stat.recommend_high }} </span
            >个， 中可信度资产<span class="blue-font-color">
              {{ asset_discovery_stat.recommend_middle }} </span
            >个， 低可信度资产<span class="blue-font-color">
              {{ asset_discovery_stat.recommend_low }} </span
            >个</div
          >
        </div>
      </div>
    </div>
    <div class="task relate div">
      <div class="title">
        <div class="block"></div>
        <div class="text">关联任务详情</div>
      </div>
      <div class="content">
        <div class="taskBox">
          <!-- <div class="taskItem" v-for="item in 4" :key="item"> -->
          <div class="taskItem" v-for="item in task_stat" :key="item.type">
            <div class="headerContainer" :class="{ active: item.is_show == 1 }">
              <span class="block">
                <img v-if="item.type == 1" src="../../assets/images/unit/relateTask1.png" alt="" />
                <img v-if="item.type == 2" src="../../assets/images/unit/relateTask2.png" alt="" />
                <img v-if="item.type == 3" src="../../assets/images/unit/relateTask3.png" alt="" />
                <img v-if="item.type == 4" src="../../assets/images/unit/relateTask4.png" alt="" />
                <img v-if="item.type == 5" src="../../assets/images/unit/relateTask5.png" alt="" />
              </span>
              <div class="text">{{ taskMap[item.type] }}</div>
            </div>
            <template v-if="item.is_show">
              <template v-if="item.type == 1">
                <div class="line">
                  <span class="text">APP</span>
                  <span class="value">{{ item.app }}</span>
                </div>
                <div class="line">
                  <span class="text">公众号</span>
                  <span class="value">{{ item.wechat }}</span>
                </div>
              </template>
              <template v-if="item.type == 2">
                <div class="line">
                  <span class="text">网盘</span>
                  <span class="value">{{ item.net_disk }}</span>
                </div>
                <div class="line">
                  <span class="text">文库</span>
                  <span class="value">{{ item.library }}</span>
                </div>
                <div class="line">
                  <span class="text">代码仓库</span>
                  <span class="value">{{ item.code }}</span>
                </div>
              </template>
              <template v-if="item.type == 3">
                <div class="line">
                  <span class="text">域名</span>
                  <span class="value">{{ item.domain }}</span>
                </div>
              </template>
              <template v-if="item.type == 4">
                <div class="line">
                  <span class="text">风险事件</span>
                  <span class="value">{{ item.risk_event }}</span>
                </div>
              </template>
              <template v-if="item.type == 5">
                <div class="line">
                  <span class="text">URL（api）资产</span>
                  <span class="value">{{ item.url_api_num }}</span>
                </div>
              </template>
              <div class="keywordContainer">
                <div class="keywordTitle">关键词</div>
                <div class="keyword" ref="keyword">
                  <div
                    :ref="'keywordBox' + item.type"
                    v-if="item.type != 4 && item.keyword && item.keyword.length != 0"
                  >
                    <span>{{ item.keyword.join('、') }}</span>
                  </div>
                  <div
                    :ref="'keywordBox' + item.type"
                    v-if="item.type == 4 && item.match_ip_full && item.match_ip_full.length != 0"
                  >
                    <span>{{ item.match_ip_full.join('、') }}</span>
                  </div>
                  <div
                    class="btn blue-text"
                    @click="moreClick(item.keyword)"
                    v-if="item.type == 1 && isShowKeywordMore1"
                  >
                    <span class="shenglue">……</span>
                    <span class="more">更多</span>
                  </div>
                  <div
                    class="btn blue-text"
                    @click="moreClick(item.keyword)"
                    v-if="item.type == 2 && isShowKeywordMore2"
                  >
                    <span class="shenglue">……</span>
                    <span class="more">更多</span>
                  </div>
                  <div
                    class="btn blue-text"
                    @click="moreClick(item.keyword)"
                    v-if="item.type == 3 && isShowKeywordMore3"
                  >
                    <span class="shenglue">……</span>
                    <span class="more">更多</span>
                  </div>
                  <div
                    class="btn blue-text"
                    @click="moreClick(item.match_ip_full)"
                    v-if="item.type == 4 && isShowKeywordMore4"
                  >
                    <span class="shenglue">……</span>
                    <span class="more">更多</span>
                  </div>
                  <div
                    class="btn blue-text"
                    @click="moreClick(item.keyword)"
                    v-if="item.type == 5 && isShowKeywordMore5"
                  >
                    <span class="shenglue">……</span>
                    <span class="more">更多</span>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="emptyClass">
                <div>
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-kong"></use>
                  </svg>
                  <p>暂无数据</p>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LoadMore from './load-more.vue'
import { mapGetters, mapState } from 'vuex'
import { detectClueList, detectReportInfo, detectAssetDownload } from '@/api/apiConfig/surveying.js'

export default {
  components: {
    LoadMore
  },
  data() {
    return {
      companyTitleList: [],
      off_assets_toMap: {
        1: '入账疑似资产',
        2: '入账资产台账'
      },
      fofa_rangeMap: {
        0: '全部',
        1: '近一年',
        2: '近半年'
      },
      scan_typeMap: {
        0: '列表端口',
        1: '常用端口',
        2: '全端口'
      },
      clueTypeMap: {
        0: '根域',
        1: '证书',
        2: 'ICP',
        3: 'ICON',
        4: '关键词',
        5: '子域名',
        6: 'IP'
      },
      detect_modeMap: {
        1: '智能模式',
        2: '标准模式',
        3: '专家模式'
      },
      isShowKeywordMore1: false,
      isShowKeywordMore2: false,
      isShowKeywordMore3: false,
      isShowKeywordMore4: false,
      isShowKeywordMore5: false,
      taskMap: {
        1: '数字资产',
        2: '数据泄露',
        3: '域名枚举',
        4: '风险事件',
        5: 'URL（api）资产'
      },
      loadState: true, // 是否显示底部加载状态，默认true
      finish: false, // 是否加载完成
      loading: false, // 是否正在加载中
      domHeight: 0, // 内容可视区的高度
      container: null, // 绑定能被监听滚动的元素
      currentPage: 1,
      totalAllPage: 0,
      total: 0,
      clueLoading: false,
      isShowMore: false, // 控制展开更多的显示与隐藏
      textHeight: null, // 框中内容的高度
      status: false, // 内容状态是否打开
      clueList: [],
      currentClueStep: '',
      currentClueValue: '',
      currentClueLabel: '',
      clueStatIndex: [
        {
          label: '已知线索',
          value: 'input',
          title: '输入已知线索',
          step: 1
        },
        {
          label: '初始线索',
          value: 'icp',
          title: '关联发现初始线索',
          step: 2
        },
        {
          label: '扩展线索',
          value: 'expend',
          title: '发现扩展线索',
          step: 3
        },
        {
          label: '线索总表',
          value: 'all',
          title: '生成线索总表',
          step: 4
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      result_stat: {},
      detect_task_stat: {},
      clue_stat: {},
      asset_discovery_stat: {},
      task_stat: {},
      user: {}
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.companyChange) {
        // 切换的时候直接返回上一页不继续执行，否则currentCompany值已变更会报错
        return
      }
      this.getInfo()
      this.changeClueType()
    }
  },
  computed: {
    ...mapState(['currentCompany', 'companyChange']),
    ...mapGetters(['getterCurrentCompany'])
  },
  mounted() {
    let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (userInfo) {
      this.user = userInfo.user
    }
    if (this.user.role == 2 && !this.currentCompany) return
    this.getInfo()
    this.changeClueType()
  },
  methods: {
    transferRate(rate) {
      if (rate == '0.00%') {
        return '-'
      } else {
        return rate.replace('.00', '')
      }
    },
    getMoreClue() {
      this.isShowMore = false
    },
    moreClick(word) {
      let wordText = word.join('、')
      this.$alert(wordText, '查看更多关键词', {
        cancelButtonText: '关闭',
        showCancelButton: true,
        showConfirmButton: false,
        dangerouslyUseHTMLString: true,
        customClass: 'codeClass'
      })
    },
    async download() {
      let obj = {
        task_id: this.$route.query.id,
        operate_company_id: this.currentCompany
      }
      let res = await detectAssetDownload(obj)
      if (res.code == 0) {
        this.$message.success('下载成功')
        window.location.href = res.data.file_path
      }
    },
    setCurrentPage(val) {
      if (val > this.totalAllPage) return
    },
    // 加载更多
    onScrollBottom(val) {
      console.log('onScrollBottom', val, this.totalAllPage, new Date().getTime())

      this.currentPage = val
      if (this.currentPage > this.totalAllPage) return
      this.getClueInfo()
    },
    loadClues() {
      this.currentPage++
      this.getClueInfo()
    },
    async getInfo() {
      let obj = {
        task_id: this.$route.query.id,
        operate_company_id: this.currentCompany
      }
      let res = await detectReportInfo(obj)
      if (res.code == 0) {
        this.result_stat = res.data.result_stat
        this.detect_task_stat = res.data.detect_task_stat
        this.task_stat = res.data.task_stat
        this.clue_stat = res.data.clue_stat
        this.asset_discovery_stat = res.data.asset_discovery_stat
        let companyTitleList = res.data.result_stat.company.map((item) => {
          return item.replace('（', '(').replace('）', ')')
        })
        this.companyTitleList = Array.from(new Set(companyTitleList))
        this.$nextTick(() => {
          for (let i = 1; i < 6; i++) {
            // 这里具体行数可依据需求自定义
            let lineHeight = 20 * 2
            let offsetHeight =
              (this.$refs['keywordBox' + i] &&
                this.$refs['keywordBox' + i][0] &&
                this.$refs['keywordBox' + i][0].offsetHeight) ||
              0
            if (offsetHeight > lineHeight) {
              this['isShowKeywordMore' + i] = true
            } else {
              this['isShowKeywordMore' + i] = false
            }
          }
        })
      }
    },
    changeClueType(item = {}) {
      this.currentClueValue = (item && item.value) || 'input'
      this.currentClueLabel = (item && item.label) || '已知线索'
      this.currentClueStep = (item && item.step) || 1
      this.currentPage = 1
      this.clueList = []
      this.isShowMore = false
      this.totalAllPage = 0
      this.getClueInfo()
    },

    async getClueInfo() {
      let obj = {
        clue_step: this.currentClueStep,
        task_id: this.$route.query.id,
        page: this.currentPage,
        per_page: 20,
        operate_company_id: this.currentCompany
      }
      let res = await detectClueList(obj)
      if (res.code == 0) {
        // if (this.currentPage == 1) {
        //   data = {
        //     items: [
        //       {
        //         content: 'huashunxinan.com',
        //         type: '0'
        //       },
        //       {
        //         content: 'huashunxinan.cn',
        //         type: '0'
        //       },
        //       {
        //         content: 'focii.cn',
        //         type: '0'
        //       },
        //       {
        //         content: 'baimaohui.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'vulfocus.cn',
        //         type: '0'
        //       },
        //       {
        //         content: 'huashunxinan.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'fofa.info',
        //         type: '0'
        //       },
        //       {
        //         content: 'foradar.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'gobysec.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'fofa.vip',
        //         type: '0'
        //       },
        //       {
        //         content: 'CN="www.huashunxinan.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="fd01.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="beta.foradar.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="foradar.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="api.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="vulfocus.cn"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="img.gamma.fofa.info"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="staticbeta.fofa.info"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="api.gamma.fofa.info"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="octra.fofa.vip"',
        //         type: '1'
        //       }
        //     ],
        //     page: 1,
        //     per_page: 20,
        //     total: 55
        //   }
        // }
        // if (this.currentPage == 2) {
        //   data = {
        //     items: [
        //       {
        //         content: 'huashunxinan.com',
        //         type: '0'
        //       },
        //       {
        //         content: 'huashunxinan.cn',
        //         type: '0'
        //       },
        //       {
        //         content: 'focii.cn',
        //         type: '0'
        //       },
        //       {
        //         content: 'baimaohui.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'vulfocus.cn',
        //         type: '0'
        //       },
        //       {
        //         content: 'huashunxinan.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'fofa.info',
        //         type: '0'
        //       },
        //       {
        //         content: 'foradar.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'gobysec.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'fofa.vip',
        //         type: '0'
        //       },
        //       {
        //         content: 'CN="www.huashunxinan.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="fd01.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="beta.foradar.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="foradar.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="api.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="vulfocus.cn"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="img.gamma.fofa.info"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="staticbeta.fofa.info"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="api.gamma.fofa.info"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="octra.fofa.vip"',
        //         type: '1'
        //       }
        //     ],
        //     page: 1,
        //     per_page: 20,
        //     total: 55
        //   }
        // }
        // if (this.currentPage == 3) {
        //   data = {
        //     items: [
        //       {
        //         content: 'huashunxinan.com',
        //         type: '0'
        //       },
        //       {
        //         content: 'huashunxinan.cn',
        //         type: '0'
        //       },
        //       {
        //         content: 'focii.cn',
        //         type: '0'
        //       },
        //       {
        //         content: 'baimaohui.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'vulfocus.cn',
        //         type: '0'
        //       },
        //       {
        //         content: 'huashunxinan.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'fofa.info',
        //         type: '0'
        //       },
        //       {
        //         content: 'foradar.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'gobysec.net',
        //         type: '0'
        //       },
        //       {
        //         content: 'fofa.vip',
        //         type: '0'
        //       },
        //       {
        //         content: 'CN="www.huashunxinan.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="fd01.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="beta.foradar.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="foradar.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="api.baimaohui.net"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="vulfocus.cn"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="img.gamma.fofa.info"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="staticbeta.fofa.info"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="api.gamma.fofa.info"',
        //         type: '1'
        //       },
        //       {
        //         content: 'CN="octra.fofa.vip"',
        //         type: '1'
        //       }
        //     ],
        //     page: 1,
        //     per_page: 20,
        //     total: 55
        //   }
        // }
        // if (this.currentPage > 3) {
        //   data = {
        //     items: [],
        //     page: 3,
        //     per_page: 20,
        //     total: 55
        //   }
        // }
        // if (this.currentPage == 3) {
        //   res.data = {
        //     items: [],
        //     page: 3,
        //     per_page: 20,
        //     total: 45
        //   }
        // }
        // this.clueList = this.clueList.concat(data.items || [])
        this.clueList = this.clueList.concat(res.data.items || [])
        // this.total = data.total
        this.total = res.data.total
        // 滚动触底加载数据处理
        // // 计算展开更多内容超出显示
        this.$nextTick(() => {
          // 这里具体行数可依据需求自定义
          let lineHeight = 40 * 5
          this.textHeight = `${lineHeight}px`
          // 当内容的高度大于现实的高度，则显示更多按钮
          if (this.$refs.content.offsetHeight >= lineHeight || this.total > 20) {
            this.isShowMore = true
          } else {
            this.isShowMore = false
          }
        })

        if (parseInt(this.total / 20) < 1) {
          this.totalAllPage = 1
        } else {
          this.totalAllPage =
            parseInt(this.total % 20) >= 0
              ? parseInt(this.total / 20) + 1
              : parseInt(this.total / 20)
        }

        // 假如数据拿成功并渲染
        // 如果没有更多数据，则加载完成
        if (this.currentPage >= this.totalAllPage) {
          this.$nextTick(() => {
            if (this.$refs.LoadMore) {
              this.$refs.LoadMore.finish = true
            }
          })
          return
        }
        // 关闭正在加载中
        if (this.$refs.LoadMore) {
          this.$refs.LoadMore.loading = false
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  font-size: 14px;
  /deep/.headerTitle {
    width: calc(100% - 32px);
    // width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding: 0 16px;
    .btn {
      font-weight: 400;
      color: #62666c;
      margin-left: 16px;
      &.el-button {
        padding: 0;
      }
      img {
        height: 14px;
      }
      &:hover {
        cursor: pointer;
        color: #2677ff;
      }
    }
  }
  .div {
    overflow: hidden;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  }

  .numberDiv {
    box-sizing: border-box;
    display: flex;
    width: 100%;
    height: 346px;
    padding-top: 18px;
    padding-left: 19px;
    padding-right: 21px;
    border-width: 6px 1px 0px 1px;
    border-style: solid;
    border-image: linear-gradient(180deg, #2677ff 0%, rgba(38, 119, 255, 0) 80%) 6 1 0 1;
    background:
      url('../../assets/images/taskBriefHeader.png') no-repeat right,
      linear-gradient(180deg, #e6efff 0%, #ffffff 100%);

    .middle {
      box-sizing: border-box;
      width: 100%;
      height: 1px;
      border: 1px dashed #cfdaeb;
    }

    .tuFinish {
      width: 120px;
      height: 120px;
      margin-right: 16px;
      background-repeat: no-repeat;
      background-size: 100%;
      background-position-y: 0px;
      background-image: url('../../assets/images/tuFinish.png');
    }

    .right {
      width: 0;
      flex: 1;

      .header {
        font-size: 18px;
        line-height: 28px;
        margin-top: 24px;
        margin-bottom: 16px;
        .company {
          display: inline-block;
          max-width: 60%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          vertical-align: middle;
        }
      }

      .content {
        margin-top: 20px;

        .listHeader {
          height: 22px;
          line-height: 22px;
          color: #62666c;

          img {
            height: 22px;
          }
        }

        .list {
          margin-top: 19px;

          li {
            height: 20px;
            line-height: 20px;
            margin-bottom: 16px;
            color: #37393c;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .sort {
            display: inline-block;
            height: 20px;
            width: 20px;
            margin-right: 8px;
            color: #2677ff;
            text-align: center;
            background: rgba(38, 119, 255, 0.1);
            border: 1px solid rgba(38, 119, 255, 0.1);
            border-radius: 2px;
          }
        }
      }
    }
  }

  .task {
    background-color: #fff;

    .title {
      display: flex;
      align-items: center;
      flex-direction: row;
      height: 46px;
      border-bottom: 1px solid #e9ebef;

      .text {
        // line-height: ;
        font-size: 16px;
      }

      .block {
        width: 4px;
        height: 16px;
        margin-right: 12px;
        border-radius: 0 2px 2px 0;
        background: #2677ff;
      }
    }
  }

  .params {
    height: 565px;

    .description {
      box-sizing: border-box;
      display: flex;
      // align-items: center;
      height: 124px;
      padding: 16px 22px 0 24px;
      background:
        url('../../assets/images/unit/unitDesc.png') no-repeat top right,
        linear-gradient(90deg, #e7f0ff 0%, #f4f7fc 100%);

      .left {
        width: 49px;
        height: 52px;
        margin-right: 12px;

        img {
          width: 100%;
        }
      }

      .right {
        flex: 1;
        width: 0;
        display: flex;
        flex-direction: column;
        padding: 4px 0 3px;

        div {
          height: 20px;
          line-height: 20px;
        }

        .top {
          margin-bottom: 5px;
          color: #37393c;
        }

        .bottom {
          color: #62666c;
        }
      }
      .btn {
        box-sizing: border-box;
        padding: 6px 12px;
        margin-top: 11px;
        // width: 110px;
        height: 32px;
        border-radius: 16px;
        // text-align: right;
        box-shadow: 0px 2px 2px 0px rgba(0, 67, 181, 0.08);
        border: 1px solid #ffffff;
        background: rgba(255, 255, 255, 0.5);
        &:hover {
          cursor: pointer;
          color: #2677ff;
        }
      }
    }

    .taskContent {
      display: flex;
      flex-direction: row;
      box-sizing: border-box;
      height: 434px;
      margin-top: -40px;
      padding: 24px 28px 28px;
      border-radius: 10px 10px 4px 4px;
      background-color: #fff;
      box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);

      .left {
        width: 300px;
        margin-right: 28px;

        .header {
          width: 100%;
          display: flex;
          justify-content: space-between;
          height: 20px;
          line-height: 20px;
          margin-bottom: 8px;

          .text1 {
            color: #37393c;
          }

          .text2 {
            color: #62666c;
          }
        }

        .treeContainer {
          box-sizing: border-box;
          height: 354px;
          border-radius: 4px;
          padding: 18px 14px;
          background: #f5f8fc;
          overflow: auto;

          /deep/.el-tree {
            color: #62666c;
            background: transparent;

            .el-tree-node > .el-tree-node__content {
              height: 32px;
            }

            .el-tree-node__content > .el-tree-node__expand-icon {
              padding: 4px 6px;
            }

            .el-tree-node__expand-icon {
              color: #62666c;
              &.is-leaf {
                // display: none;
                color: transparent;
              }
            }

            .el-tree-node__content {
              align-items: start;
            }
          }
        }
      }

      .right {
        width: 0;
        flex: 1;

        .header {
          height: 20px;
        }

        .top {
          height: 184px;
          margin-bottom: 24px;

          .listContainer {
            box-sizing: border-box;
            width: 100%;
            height: 156px;
            border-radius: 4px;
            padding: 16px;
            margin-top: 8px;
            background: #f5f8fc;
            overflow: auto;
          }
        }

        .bottomContainer {
          display: flex;
          flex-wrap: wrap;

          .rowItem {
            width: 50%;
            margin-top: 12px;

            .label {
              color: rgba(98, 102, 108, 0.6);
            }

            .value {
              color: #37393c;
            }
          }
        }
      }
    }
  }

  .clue {
    background: #ffffff;

    .content {
      .top {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        flex-direction: column;
        height: 336px;
        padding: 28px 0;
      }

      .bottom {
        box-sizing: border-box;
        position: relative;
        background: #f5f8fc;
        padding: 20px 32px;

        .moreLoad {
          box-sizing: border-box;
          position: absolute;
          bottom: 0;
          width: 100%;
          height: 82px;
          text-align: center;
          color: #2677ff;
          padding-top: 46px;
          background: linear-gradient(180deg, rgba(245, 248, 252, 0.56) 0%, #f5f8fc 55%);
        }

        .clueContent {
          &.tableWapper {
            height: 200px;
          }
          &.clueEllipsis {
            height: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 7;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
  }

  .assets {
    .content {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 52px 124px 61px 70px;

      .imgContainer {
        width: 100%;
        max-width: 1200px;
        min-width: 1006px;
        position: relative;
      }

      img {
        width: 100%;
      }

      .block {
        box-sizing: border-box;
        position: absolute;
        height: 32px;
        padding: 6px 12px;
        border: 1px solid #deeaff;
        background: linear-gradient(180deg, #edf4ff 0%, #f8fbff 100%);
      }

      .block1 {
        left: 35%;
        top: 0;

        transform: translateX(-90%) translateY(-30%);
      }

      .block2 {
        right: 113px;
        top: 0;
        transform: translateY(-30%);
      }

      .block3 {
        width: auto;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%) translateY(30%);
      }
    }
  }

  .relate {
    height: 388px;
    margin-bottom: 0;
    .content {
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      height: calc(100% - 46px);
      padding: 48px 28px 28px;
      width: 100%;
      overflow: auto;
      .taskBox {
        display: flex;
      }

      .taskItem {
        position: relative;
        width: 271px;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 20px;
        background-color: #f5f8fc;

        &:last-child {
          margin-right: 0;
        }

        .headerContainer {
          box-sizing: border-box;
          // width: 137px;
          height: 40px;
          margin: -20px 0 17px;
          padding: 1px;
          padding-right: 16px;
          line-height: 40px;
          border-radius: 20px 0 20px 0;
          // color: #2677FF;
          color: rgba(38, 119, 255, 0.7);
          background: linear-gradient(106deg, #d3efff 0%, #cddffc 100%);
          &.active {
            color: #fff;
            background: linear-gradient(106deg, #26b3ff 0%, #2677ff 100%);
          }
          .block {
            display: inline-block;
            box-sizing: border-box;
            width: 48px;
            height: 38px;
            padding: 4px 12px 3px;
            margin-right: 8px;
            line-height: 38px;
            text-align: center;
            border-radius: 19px 0 19px 0;
            border: 1px solid transparent;
            background: #ffffff;

            img {
              vertical-align: top;
            }
          }

          .text {
            display: inline-block;
            box-sizing: border-box;
            height: 38px;
            line-height: 38px;
            vertical-align: top;
          }
        }

        .line {
          display: flex;
          justify-content: space-between;
          box-sizing: border-box;
          width: 183px;
          // width: calc(100% - 88px);
          height: 32px;
          margin: 0 44px 12px;
          padding: 6px 12px;
          border: 1px solid #deeaff;
          border-radius: 4px;
          background: linear-gradient(180deg, #edf4ff 0%, #f8fbff 100%);
          .text {
            color: #62666c;
          }
          .value {
            color: #2677ff;
          }
        }

        .keywordContainer {
          box-sizing: border-box;
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 92px;
          padding: 12px 20px;
          background: #e3ecf9;

          .keywordTitle {
            margin-bottom: 8px;
            color: #37393c;
          }

          .keyword {
            position: relative;
            height: 40px;
            line-height: 20px;
            overflow: hidden;
            .btn {
              position: absolute;
              bottom: 0;
              right: 0;
              background-color: #e3ecf9;
              &:hover {
                cursor: pointer;
                color: #2677ff;
              }
            }
          }
        }
      }
    }
  }
}

.stepContainer {
  display: flex;

  .step {
    text-align: center;
    margin-right: 0;
    width: 224px;

    .step_head {
      position: relative;
      width: calc(100% - 28px);
      text-align: center;

      .step_line {
        position: absolute;
        background-color: #2677ff;
        height: 2px;
        top: 11px;
        left: 50%;
        right: -50%;
        margin-right: 0;
        transform: translateX(14px);
      }

      .step_icon {
        position: relative;
        z-index: 1;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 24px;
        font-size: 14px;
        box-sizing: border-box;
        background: #2677ff;
        border-radius: 50%;

        // border: 2px solid;
        // border-color: inherit;
        .step__icon-inner {
          display: inline-block;
          user-select: none;
          text-align: center;
          // font-weight: 700;
          line-height: 1;
          color: #fff;
        }
      }
    }

    .step_main {
      width: calc(100% - 28px);
      margin-top: 8px;

      .step_description {
        width: 100%;
        height: 216px;
        margin-top: 12px;
        color: #62666c;
        background: #f5f8fc;
        border-radius: 4px;

        div {
          box-sizing: border-box;
          display: flex;
          justify-content: space-between;
          height: 36px;
          padding: 8px 16px;
          line-height: 20px;
          text-align: left;
          font-size: 14px;

          .value {
            color: #2677ff;
          }
        }

        .header {
          border-radius: 4px;

          &.active {
            color: #2677ff;
            border: 1px solid #2677ff;
            background: linear-gradient(180deg, #f4f8ff 0%, #e1ecff 100%);
            box-shadow: 2px 0px 4px 0px rgba(0, 0, 0, 0.12);
          }
          &:hover {
            cursor: pointer;
            color: #2677ff;
          }
        }

        .clueType {
          .label {
            .svg-icon {
              width: 8px;
              color: #2677ff;
            }
          }
        }
      }
    }
  }

  .step:last-of-type .step_line {
    display: none;
  }
}
.tree-node {
  display: inline-block;
  width: 125px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.holding {
  font-size: 12px;
  padding: 3px 8px;
  margin-left: 4px;
  background: rgba(255, 255, 255, 0.5);
  box-sizing: border-box;
  border: 1px solid #cfdaeb;
  border-radius: 2px;
}
.master {
  font-size: 12px;
  box-sizing: border-box;
  padding: 3px 8px;
  margin-left: 4px;
  color: #2677ff;
  background: rgba(38, 119, 255, 0.12);
  border-radius: 2px;
}
.mainCompany {
  background: rgba(38, 119, 255, 0.12);
  color: #2677ff;
  border-radius: 2px;
}

.clueBlock {
  box-sizing: border-box;
  display: inline-block;
  line-height: 22px;
  height: 28px;
  padding: 2px 12px 2px 3px;
  margin-right: 12px;
  margin-top: 12px;
  color: #37393c;
  border-radius: 14px;
  border: 1px solid #cfdaeb;
  background: rgba(255, 255, 255, 0.5);

  .clueType {
    display: inline-block;
    color: #2677ff;
    height: 100%;
    // height: calc(100% - 4px);
    margin-right: 4px;
    padding: 0 8px;
    border-radius: 10px;
    background: rgba(38, 119, 255, 0.2);
  }
}
.blue-font-color {
  color: #2677ff;
}
.emptyClass {
  width: 100%;
  height: 80%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  text-align: center;
  svg {
    display: inline-block;
    font-size: 80px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}

.clueEllipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 7;
  -webkit-box-orient: vertical;
}
.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}
.blue-text {
  color: #2677ff;
}
</style>
