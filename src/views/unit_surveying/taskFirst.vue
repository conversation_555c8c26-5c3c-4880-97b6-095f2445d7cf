<template>
  <div class="myBox1" v-loading="changeCompanyLoading">
    <div class="content">
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="rules"
        label-width="130px"
        style="width: 100%"
      >
        <div class="module findModule">
          <div class="top">
            <div class="label">
              <div class="blueBlock"></div>
              线索发现设置
            </div>
            <div class="act" v-if="isShowFindModule" @click="unfoldOrCollapse('Find', false)"
              ><span>收起</span> <img :src="topArrow" alt=""
            /></div>
            <div class="act" v-else @click="unfoldOrCollapse('Find', true)"
              ><span>展开</span> <img :src="bottomArrow" alt="" />
            </div>
          </div>
          <div class="moduleBottom" v-if="isShowFindModule">
            <el-form-item label="" prop="companyName" class="companyDiv">
              <!-- is_real_customer == 1 代表在用户管理配置成客户，只能对本企业以及配置好的企业测绘 -->
              <span v-if="is_real_customer == 1" slot="label">
                企业名称：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  v-if="user.role != 1"
                  content="只支持对本企业以及控股企业进行资产梳理"
                  placement="top"
                >
                  <i
                    style="color: rgba(38, 119, 255, 0.2); font-size: 16px"
                    class="el-icon-question"
                  ></i>
                </el-tooltip>
              </span>
              <span v-else slot="label"> 企业名称： </span>
              <div class="select-button">
                <el-select
                  v-if="is_real_customer == 1"
                  v-model="addForm.companyName"
                  filterable
                  remote
                  clearable
                  @change="companyChange"
                  placeholder="请输入本企业名称或控股企业名称"
                  :remote-method="querySearch"
                  :loading="loading"
                >
                  <el-option
                    v-for="(item, index) in kehuCompanyResults"
                    :key="index"
                    :value="item.value"
                  >
                    {{ item.value }}
                  </el-option>
                </el-select>
                <!-- 安服账号不显示 -->
                <el-select
                  v-else
                  v-model="addForm.companyName"
                  filterable
                  remote
                  clearable
                  @change="companyChange"
                  placeholder="请输入企业名称进行搜索"
                  :remote-method="remoteMethod"
                  :loading="loading"
                >
                  <el-option v-for="(item, index) in companyList" :key="index" :value="item.value">
                    {{ item.value }}
                  </el-option>
                </el-select>
                <el-button
                  type="text"
                  :class="addForm.companyName ? 'companyBtn companyBlue' : 'companyBtn companyGray'"
                  @click.native="($event) => Inquire('')"
                  :loading="companyCheckLoading"
                  id="unit_sure"
                >
                  企业关系查询
                </el-button>
              </div>
            </el-form-item>
            <el-form-item class="treeWrap" v-if="treeIsTrue" label="">
              <p class="onekeyclass">
                <span>
                  <el-checkbox @change="onekeyCheck" v-model="isTreeChecked">全选</el-checkbox>
                  <span class="statistics"
                    >（共控股企业
                    {{
                      (companyCascadeEquityList[0] &&
                        companyCascadeEquityList[0].children &&
                        companyCascadeEquityList[0].children.length) ||
                      0
                    }}
                    家）</span
                  >
                </span>
                <!-- 安服支持重新获取 -->
                <el-button v-if="user.role == 2" type="text" @click="Inquire(1)"
                  >重新获取</el-button
                >
              </p>
              <el-tree
                class="companyTree"
                ref="companyTree"
                :data="companyCascadeEquityList"
                show-checkbox
                node-key="company_name"
                :check-strictly="true"
                :default-expand-all="true"
                :default-checked-keys="checkedTree"
                :props="defaultProps"
              >
                <el-checkbox @change="onekeyCheck" v-model="isTreeChecked">全选</el-checkbox>
                <span class="custom-tree-node" slot-scope="{ data }">
                  <el-tooltip class="nodeLabel" effect="light" placement="top" :open-delay="500">
                    <div slot="content">{{ data.company_name }}</div>
                    <span>{{ data.company_name }}</span>
                  </el-tooltip>
                  <businessTagVue
                    v-if="data.reg_status"
                    :reg_status="data.reg_status"
                  ></businessTagVue>
                  <span
                    v-if="data.level != 0"
                    :class="parseFloat(data.rate) > 50 ? 'treeNum bigNum' : 'treeNum smallNum'"
                    >控股：<span class="num">{{ transferRate(data.rate) }}</span></span
                  >
                </span>
              </el-tree>
            </el-form-item>
            <!-- 安服/超管 账号有此操作 v-if="user.role == 2 || user.role == 1" -->
            <el-form-item label="其他测绘企业：" label-suffix="opop">
              <el-input
                type="textarea"
                :rows="5"
                v-model="otherCompany"
                placeholder="请输入其他测绘企业,多个值请使用分号或换行分隔"
              ></el-input>
            </el-form-item>
            <el-form-item label="控股比例：" prop="percent">
              <template slot="label">
                控股比例：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <span slot="content" style="line-height: 24px">
                    控股比例的设置对上述（其他测绘企业）生效，如无需测绘（其他测绘企业）下的控股企业，保持默认配置0%即可（该配置项只对上方的其他测绘企业的控股比例范围生效）。
                  </span>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-input type="input" v-model="addForm.percent" placeholder="请输入控股比例">
                <span slot="suffix">%</span>
              </el-input>
            </el-form-item>
            <el-form-item label="已知IP：">
              <el-input
                type="textarea"
                :rows="3"
                v-model="content.content6"
                placeholder="请输入IP段，多个值请用分号或换行分隔，例如：**********或者**********/24，每条线索不能超过200字符，最多 100 个 CIDR 或单个 IP，所有输入的 CIDR 或 IP 解析为单个 IP 的总数必须少于 5000 个。"
              ></el-input>
            </el-form-item>
            <el-form-item label="已知域名：">
              <el-input
                type="textarea"
                :rows="2"
                v-model="content.content0"
                placeholder="请输入域名，多个值请用分号或换行分隔，例如：fofa.info，每条线索不能超过200字符"
              ></el-input>
            </el-form-item>
            <el-form-item label="已知证书：">
              <el-input
                type="textarea"
                :rows="2"
                v-model="content.content1"
                placeholder='请输入证书，多个值请用分号或换行分隔，例如：O="北京华顺信安科技有限公司"或者CN="fofa.info"，每条线索不能超过200字符'
              ></el-input>
            </el-form-item>
            <el-form-item label="已知ICP：">
              <el-input
                type="textarea"
                :rows="2"
                v-model="content.content2"
                placeholder="请输入ICP，多个值请用分号或换行分隔，例如：京ICP备********号 或者 京ICP备********号-3，每条线索不能超过200字符"
              ></el-input>
            </el-form-item>
            <el-form-item label="ICON：">
              <el-upload
                class="upload-demo"
                drag
                :action="uploadSrcIp + '/assets/account/files'"
                :headers="uploadHeaders"
                :before-upload="($event) => beforeIpUpload($event, 3)"
                :on-success="iconUploadSucc"
                :on-remove="iconUploadRemove"
                :on-error="uploadErr"
                list-type="picture"
                accept=".png,.ico,.bmp,.jpg,.jpeg"
                :file-list="fileListIcon"
                multiple
              >
                <img :src="yellowUploadIcon" alt="" />
                <div class="el-upload__text">将ICON拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip"
                  >支持上传.png,.ico,.bmp,.jpg,.jpeg文件，且大小不超过3M</div
                >
              </el-upload>
            </el-form-item>
          </div>
        </div>
        <div class="module modelModule">
          <div class="top">
            <div class="label">
              <div class="blueBlock"></div>
              模式设置
            </div>
            <div class="act" v-if="isShowModelModule" @click="unfoldOrCollapse('Model', false)"
              ><span>收起</span> <img :src="topArrow" alt=""
            /></div>
            <div class="act" v-else @click="unfoldOrCollapse('Model', true)"
              ><span>展开</span> <img :src="bottomArrow" alt=""
            /></div>
          </div>
          <div class="moduleBottom" v-if="isShowModelModule">
            <el-form-item label="模式设置：">
              <template slot="label">
                模式设置：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    选择单位资产测绘流程模式，具体的模式区别如下:
                    <ul>
                      <li>
                        智能模式：互联网资产梳理全面性80%、精准性85%、适用第一次接互联网暴露面或者对资产运营需求不深的用户；
                      </li>
                      <li>
                        标准模式：互联网资产梳理全面性90%、精准性90%、适用有一定资产运营经验，但是刚开始涉足互联网暴露面或者攻击面的用户；
                      </li>
                      <li>
                        专家模式：互联网资产梳理全面性95%、精准性98%、适用有一定资产梳理经验，可配合适用第三方技术和工具来发现隐藏的或未知资产的用户。
                      </li>
                    </ul>
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-radio-group v-model="addForm.detect_mode">
                <el-radio :label="1">智能模式</el-radio>
                <el-radio :label="2">标准模式</el-radio>
                <el-radio :label="3">专家模式</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
        <div class="module paramsModule">
          <div class="top">
            <div class="label">
              <div class="blueBlock"></div>
              参数设置
            </div>
            <div class="act" v-if="isShowParamsModule" @click="unfoldOrCollapse('Params', false)"
              ><span>收起</span> <img :src="topArrow" alt=""
            /></div>
            <div class="act" v-else @click="unfoldOrCollapse('Params', true)"
              ><span>展开</span> <img :src="bottomArrow" alt=""
            /></div>
          </div>
          <div class="moduleBottom" v-if="isShowParamsModule">
            <el-form-item label="扫描端口：">
              <template slot="label">
                扫描端口：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    作用于步骤5-资产扫描入账，支持配置关联发现资产的端口扫描范围，说明如下:<br />
                    <ul>
                      <li>
                        列表端口：针对通过线索关联的资产直接进行扫描，关联出来多少端口探测多少端口；
                      </li>
                      <li>
                        常用端口：针对关联发现的IP进行常用端口扫描，具体扫描端口见[系统管理-端口管理]全部常用端口列表；
                      </li>
                      <li>
                        全端口: 针对关联发现的IP进行全端口扫描，0-65535，此选项扫描时间较长。
                      </li>
                    </ul>
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-radio-group v-model="addForm.scan_type">
                <el-radio :label="0">列表端口</el-radio>
                <el-radio :label="1">常用端口</el-radio>
                <el-radio :label="2" :disabled="user.level == 0 && user.role == 3"
                  >全端口
                  <el-tooltip
                    class="item1"
                    effect="dark"
                    popper-class="chainClass12"
                    placement="top-start"
                  >
                    <p slot="content" style="line-height: 24px">
                      若要对资产执行漏洞扫描，请选择全端口扫描
                    </p>
                    <img style="vertical-align: top" src="@/assets/images/description.png" alt="" />
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="扫描带宽：" prop="bandwidth">
              <template slot="label">
                扫描带宽：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    作用于步骤5-资产扫描入账，支持配置资产扫描带宽，带宽设置高可以提高扫描速度，但是过高可能会影响到网络正常使用，最大值为5000。
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <newInputNumber
                class="inputNumber"
                :min="100"
                :max="5000"
                :step="100"
                v-model.number="addForm.bandwidth"
                placeholder="大于100，小于5000的整数"
              >
                <template slot="append">kb</template>
              </newInputNumber>
            </el-form-item>
            <el-form-item label="资产梳理范围：">
              <template slot="label">
                资产梳理范围：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    作用于步骤3-云端资产推荐、支持配置从fofa库获取数据的时间范围，时间范围越大数据会越全面，但是可能会造成部分杂音数据。
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-select v-model="addForm.fofa_range" placeholder="请选择">
                <el-option :value="0" label="全部"></el-option>
                <el-option :value="1" label="近一年"></el-option>
                <el-option :value="2" label="近半年"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="离线资产：">
              <template slot="label">
                离线资产：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    作用于步骤5-资产扫描入账，支持配置离线资产是否按照信任度评估结果入台账进行管理。
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-radio-group v-model="addForm.off_assets_to">
                <el-radio :label="1">入账疑似资产</el-radio>
                <el-radio :label="2">入账资产台账</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="Hunter数据导入：" v-if="user.role == 2">
              <template slot="label">
                Hunter导入：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    支持自动获取近一个月内的Hunter数据
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-radio-group v-model="addForm.is_need_hunter" :disabled="true">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="全球DNS解析：" v-if="user.role == 2">
              <template slot="label">
                全球DNS解析：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    支持自动获取域名的实时DNS解析数据
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-radio-group v-model="addForm.is_need_dnschecker">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="自动推导IP：" v-if="user.role == 2 && !userInfo.is_local">
              <template slot="label">
                自动推导IP：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    根据推荐拿到的资产，自动关联同C段相临近IP的资产数据
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-radio-group v-model="addForm.is_auto_expend_ip">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
        <div class="module assetsModule">
          <div class="top">
            <div class="label">
              <div class="blueBlock"></div>
              影子资产判定
            </div>
            <div class="act" v-if="isShowAssetsModule" @click="unfoldOrCollapse('Assets', false)"
              ><span>收起</span> <img :src="topArrow" alt=""
            /></div>
            <div class="act" v-else @click="unfoldOrCollapse('Assets', true)"
              ><span>展开</span> <img :src="bottomArrow" alt=""
            /></div>
          </div>
          <div class="moduleBottom" v-if="isShowAssetsModule">
            <el-form-item label="导入已知资产：">
              <template slot="label">
                导入已知资产：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    作用于步骤5-资产扫描入账，支持导入已知的资产列表，系统自动识别判定影子资产，并在台账中打上对应的标签，提高用户研判效率
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-upload
                class="upload-demo"
                drag
                :action="
                  uploadSrcIp + '/assets/account/upload?operate_company_id=' + currentCompany
                "
                :headers="uploadHeaders"
                :before-upload="($event) => beforeIpUpload($event, 3)"
                :on-success="assetsUploadSucc"
                :limit="1"
                :on-exceed="handleExceed"
                :on-remove="assetsUploadRemove"
                :on-error="uploadErr"
                accept=".xlsx,.XLSX"
                :file-list="fileListIcon"
              >
                <img :src="greenUploadIcon" alt="" />
                <div class="el-upload__text">将EXCEL拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">
                  <span>支持上传xlsx格式文件，且大小不超过3M</span>
                  <span class="downloadBtn" @click="downloadIpExcel()">点击下载模版</span>
                </div>
              </el-upload>
            </el-form-item>
          </div>
        </div>
        <div class="module taskModule">
          <div class="top">
            <div class="label">
              <div class="blueBlock"></div>
              关联任务
            </div>
            <div class="act" v-if="isShowTaskModule" @click="unfoldOrCollapse('Task', false)"
              ><span>收起</span> <img :src="topArrow" alt=""
            /></div>
            <div class="act" v-else @click="unfoldOrCollapse('Task', true)"
              ><span>展开</span> <img :src="bottomArrow" alt="" />
            </div>
          </div>
          <div class="moduleBottom" v-if="isShowTaskModule">
            <el-form-item label="关联任务：">
              <template slot="label">
                关联任务：
                <el-tooltip
                  class="item1"
                  effect="dark"
                  popper-class="chainClass12"
                  placement="top-start"
                >
                  <div slot="content" style="line-height: 24px">
                    <p> 任务执行过程中，支持利用任务中的线索信息进行关联任务的执行，说明如下: </p
                    ><ul>
                      <li
                        >数字资产:支持利用整个测绘流程中关联出来的所有企业名称进行数字资产的监测识别；</li
                      >
                      <li
                        >数据泄露:支持利用整个测绘流程中关联出来的所有关键词以及主域名线索进行数据泄露的监测识别；</li
                      >
                      <li>域名枚举:支持利用整个测绘流程中关联出来的所有域名进行下一层级的枚举；</li>
                      <li>风险事件:支持利用整个测绘流程中关联出来的所有资产进行风险事件的匹配;</li>
                      <li
                        >URL(API)资产:支持利用整个测绘流程中关联出来的所有域名进行URL目录的识别发现;</li
                      >
                      <li>业务系统:支持利用整个测绘流程关联出来的URL资产进行业务系统识别。</li>
                    </ul>
                  </div>
                  <img src="@/assets/images/description.png" alt="" />
                </el-tooltip>
              </template>
              <el-checkbox
                :disabled="companyInfo.limit_new_asset == 0"
                v-model="addForm.is_auto_data_assets"
                :true-label="1"
                :false-label="0"
                >数字资产</el-checkbox
              >
              <el-checkbox
                :disabled="companyInfo.limit_data_leak == 0"
                v-model="addForm.is_auto_leak_assets"
                :true-label="1"
                :false-label="0"
                >数据泄露</el-checkbox
              >
              <el-checkbox v-model="addForm.is_auto_domain_brust" :true-label="1" :false-label="0"
                >域名枚举</el-checkbox
              >
              <el-checkbox v-model="addForm.is_check_risk" :true-label="1" :false-label="0"
                >风险事件</el-checkbox
              >
              <el-checkbox v-model="addForm.is_auto_url_api" :true-label="1" :false-label="0"
                >URL(API)资产</el-checkbox
              >
              <el-checkbox v-model="addForm.is_auto_business_api" :true-label="1" :false-label="0"
                >业务系统</el-checkbox
              >
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <div class="footer">
      <el-button class="normalBtn" type="primary" :loading="sureLoading" @click="sureGetClue"
        >开始测绘</el-button
      >
    </div>
    <el-dialog
      class="elDialogAdd elDialogAddValidate"
      :close-on-click-modal="false"
      :visible.sync="otherCompanyValidVisible"
      width="500px"
    >
      <template slot="title">
        <span v-if="errorType == 'company'">其他测绘企业校验错误企业名称</span>
        <span v-if="errorType == 'assets'">导入已知资产错误格式IP</span>
      </template>
      <div class="dialog-body">
        <div class="dialog-item" v-if="errorType == 'company'"
          >以下输入的企业名称错误,可以重新复制到输入框中做修改</div
        >
        <div class="dialog-item" v-if="errorType == 'assets'"
          >以下输入的IP格式错误,可以复制到文件中重新上传</div
        >
        <div v-if="errorType == 'company'">
          <div class="dialog-item" v-for="(item, k) in errorCompanyList" :key="k">{{ item }}</div>
        </div>
        <div v-if="errorType == 'assets'">
          <div class="dialog-item" v-for="(item, k) in errorAssetsList" :key="k">{{ item }}</div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="otherCompanyValidVisible = false">取消</el-button>
        <el-button class="highBtn" type="primary" @click="otherComValidConfirm">
          <span v-if="errorType == 'company'">移除并复制错误企业名称</span>
          <span v-if="errorType == 'assets'">复制错误资产</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import businessTagVue from '../../components/businessTag.vue'
import newInputNumber from '@/components/input-number/index'

import { mapGetters, mapState } from 'vuex'
import { cluesGroupListNoPage } from '@/api/apiConfig/clue.js'
import { companyCascadeEquity } from '@/api/apiConfig/recommend.js'
import {
  companyBatchVerify,
  taskConfirm,
  taskParamsSave,
  getAssociate,
  kehuCompanyList
} from '@/api/apiConfig/surveying.js'
import { personInfo } from '@/api/apiConfig/person.js'

export default {
  components: {
    newInputNumber,
    businessTagVue
  },
  data() {
    let validatePercent = (rule, value, callback) => {
      const reg = /^([0-9]{1,2}|100)$/ //注意此处细节
      if (reg.test(+value) === true) {
        callback()
      } else {
        callback(new Error('请输入0到100的整数,包括0和100'))
      }
    }
    return {
      donotApi:false,
      companyTitleList: [],
      associatedTasks: [],
      isAssetsError: false,
      errorType: '', // 弹窗显示的错误类型company企业 assets资产
      checked: [],
      value: '',
      radio: '',
      topArrow: require('@/assets/images/topArrow.png'),
      bottomArrow: require('@/assets/images/bottomArrow.png'),
      yellowUploadIcon: require('@/assets/images/yellowUploadIcon.png'),
      greenUploadIcon: require('@/assets/images/greenUploadIcon.png'),
      isShowFindModule: true,
      isShowModelModule: true,
      isShowParamsModule: true,
      isShowAssetsModule: true,
      isShowTaskModule: true,
      correctCompanyList: [],
      errorCompanyList: [],
      correctAssetsList: [],
      errorAssetsList: [],
      otherCompanyValidVisible: false,
      changeCompanyLoading: false,
      groupLoading: false,
      treeIsTrue: false,
      sureLoading: false,
      loading: false,
      isTreeChecked: false,
      companyList: [],
      companyCascadeEquityList: [], // 控股企业列表
      otherCompany: '',
      isInquireClick: false,
      other_customer_company: [],
      checkedTree: [],
      groupArr: [],
      restaurants: [],
      addForm: {
        companyName: '',
        is_detect_scene: 0,
        expand_init_clues: false, // 是否拿场景分组中的线索作为初始线索进行扩展 0不扩展/1进行扩展
        scene_group_id: '', // 场景库线索分组选择
        percent: 0,
        scan_type: 0,
        bandwidth: 1000,
        fofa_range: 0,
        off_assets_to: 1,
        detect_mode: 1,
        is_need_hunter: 0,
        is_need_dnschecker: 0,
        is_auto_expend_ip: 0
      },
      companyCheckLoading: false,
      groupId: '',
      checked: '',
      rules: {
        companyName: [{ required: true, message: '请输入企业名称', trigger: 'blue' }],
        percent: [{ validator: validatePercent, trigger: 'blur' }],
        bandwidth: [
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请输入带宽'))
              } else if (/^(?:[1-9]\d*)$/.test(value) == false) {
                callback(new Error('请输入整数'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      },
      content: {
        content0: '',
        content1: '',
        content2: '',
        content3: [],
        content4: '',
        content5: '',
        content6: ''
      },
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileListIcon: [],
      defaultProps: {
        children: 'children',
        label: 'company_name'
      },
      user: {
        role: ''
      },
      kehuCompanyResults: [],
      is_real_customer: 0,
      userInfo: '',
      companyInfo: {
        limit_new_asset: 1,
        limit_data_leak: 1
      }
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.companyInfo = {
        limit_new_asset: 1,
        limit_data_leak: 1
      }
      this.getGroupList()
      if (this.user.role == 2) {
        // 安服
        this.changeCompanyLoading = true
        this.addForm = {
          companyName: '',
          is_detect_scene: 0,
          expand_init_clues: false, // 是否拿场景分组中的线索作为初始线索进行扩展 0不扩展/1进行扩展
          scene_group_id: '', // 场景库线索分组选择
          percent: 0,
          scan_type: 0,
          bandwidth: 1000,
          fofa_range: 0,
          off_assets_to: 1,
          detect_mode: 1,
          is_need_hunter: 0,
          is_need_dnschecker: 0,
          is_auto_expend_ip: 0
        }
        this.companyCheckLoading = false
        this.treeIsTrue = false
        this.isInquireClick = false

        setTimeout(() => {
          if (sessionStorage.getItem('companyInfo')) {
            this.companyInfo = JSON.parse(sessionStorage.getItem('companyInfo'))
          }
          this.is_real_customer = sessionStorage.getItem('companyInfo')
            ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.is_real_customer
            : ''
          if (this.is_real_customer == 1) {
            // 客户账号，需要控制只能操作自己及控股企业
            this.getSearchData() // 企业账号获取企业及控股企业列表
          } else {
            this.restaurants = []
          }
          this.changeCompanyLoading = false
        }, 1000)
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage', 'getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    if (this.user.role == 2) {
      // 安服
      if (!this.currentCompany) return
      this.is_real_customer = sessionStorage.getItem('companyInfo')
        ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.is_real_customer
        : ''
    } else {
      // 企业、超管
      this.is_real_customer = this.user ? this.user.is_real_customer : ''
    }
    if (this.is_real_customer == 1) {
      // 客户账号，需要控制只能操作自己及控股企业
      this.getSearchData() // 企业账号获取企业及控股企业列表
    } else {
      this.restaurants = []
    }
    this.getGroupList() // 获取已有场景列表
  },
  mounted() {
    this.addHeadStyle()
  },
  methods: {
    downloadIpExcel() {
      window.location.href = '/downloadTemplate/导入已知资产模板.xlsx'
    },
    handleExceed() {
      this.$message.warning('最多上传1个文件')
    },
    beforeIpUpload(file, limit) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    },
    assetsUploadSucc(response, file, fileList) {
      // 错误就给一个标记 他是有错误的 然后 删除文件后移除这个标记 提交的时候根据这个标记判断
      if (response.code == 0 && response.data && response.data.error_list.length > 0) {
        this.errorType = 'assets'
        this.isAssetsError = true
        this.errorAssetsList = response.data.error_list || []
        this.correctAssetsList = response.data.data_list || []
        this.otherCompanyValidVisible = true
      } else {
        this.addForm.import_sure_ips = response.data.data_list.map((item) => item.ip)
      }
    },
    assetsUploadRemove() {
      this.isAssetsError = false
      this.errorAssetsList = []
      this.correctAssetsList = []
    },
    unfoldOrCollapse(type, isShow) {
      this['isShow' + type + 'Module'] = isShow
    },
    addHeadStyle() {
      let lightStyle = document.createElement('style')
      lightStyle.type = 'text/css'
      lightStyle.id = 'head-style-light'
      window.document.head.appendChild(lightStyle)
    },
    // 动态给头部加样式
    selHightLightVlaue() {},
    transferRate(rate) {
      if (rate == '0.00%') {
        return '-'
      } else {
        return rate.replace('.00', '')
      }
    },
    companyChangeOther(val) {
      if (!val) {
        this.other_customer_company = []
      }
    },
    async remoteMethodOther(val) {
      if (val.trim()) {
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        this.checkedTree = []
        this.other_customer_company = []
        let obj = {
          name: val,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })
            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.other_customer_company = a
          }
        }, 200)
      } else {
        this.other_customer_company = []
      }
    },
    async getSearchData() {
      // 用户管理设置为【客户】后可测绘的企业列表
      let res = await kehuCompanyList({ operate_company_id: this.currentCompany })
      let data = []
      if (res.data && res.data.length > 0) {
        res.data.map((v) => {
          data.push({ value: v })
        })
      }
      this.restaurants = data
      this.kehuCompanyResults = data
    },
    // 客户账号输入企业名称
    querySearch(queryString) {
      let restaurants = this.restaurants
      this.kehuCompanyResults = []
      // 调用 callback 返回建议列表的数据
      if (queryString !== '') {
        // 有输入值，自动关联企业
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        this.treeIsTrue = false
        this.checkedTree = []
        let obj = {
          name: queryString,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })
            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.kehuCompanyResults = [...a]
          }
        }, 200)
      } else {
        // 没有输入值，展示客户可以测绘的企业
        this.kehuCompanyResults = queryString
          ? restaurants.filter(this.createFilter(queryString))
          : restaurants
      }
    },
    // 非客户账号输入企业名称，自动关联
    async remoteMethod(val) {
      if (val.trim()) {
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        this.treeIsTrue = false
        this.checkedTree = []
        this.companyList = []
        let obj = {
          name: val,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })

            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.companyList = a
          }
        }, 200)
      } else {
        this.companyList = []
      }
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    otherComValidConfirm() {
      this.errorType == 'assets'
      let copyErrorList = ''
      if (this.errorType == 'company') {
        this.otherCompany = this.correctCompanyList.join('\r')
        copyErrorList = this.errorCompanyList
      } else if (this.errorType == 'assets') {
        copyErrorList = this.errorAssetsList
      }
      // this.errorCompanyList.unshift(' ')
      copyErrorList.unshift(' ')
      this.$copyText(copyErrorList.join('\r')).then(
        (res) => {
          this.otherCompanyValidVisible = false
          this.$message.success('复制成功')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    companyOtherValidate(val) {
      return new Promise(async (resolve, reject) => {
        if (val.length == 0) return
        this.sureLoading = true
        let res = await companyBatchVerify({
          name: val,
          operate_company_id: this.currentCompany
        }).catch((error) => {
          this.sureLoading = false
          reject()
        })

        if (res.code == 0 && res.data && res.data.error_list.length > 0) {
          // 代表存在错误企业名称
          this.errorCompanyList = res.data.error_list || []
          this.correctCompanyList = res.data.correct_list || []
          resolve(true)
        } else {
          resolve(false)
        }
        this.sureLoading = false
      })
    },
    companyChange(val) {
      if (!val) {
        this.companyList = []
        this.kehuCompanyResults = [...this.restaurants] // 客户账号默认展示可测绘企业
        this.treeIsTrue = false
      }
    },
    groupOptionClick(item) {
      this.addForm.companyName = item.company_name
      this.addForm.scene_group_id = item.id
    },
    async drawTypeChange() {
      this.addForm.companyName = ''
      this.addForm.expand_init_clues = false // 是否拿场景分组中的线索作为初始线索进行扩展 0不扩展/1进行扩展
      this.addForm.scene_group_id = '' // 场景库线索分组选择
    },
    async getGroupList() {
      let obj = {
        per_page: 10,
        no_page: '1',
        is_detect_scene: 1, // 代表是单位资产测绘场景列表
        operate_company_id: this.currentCompany
      }
      this.groupLoading = true
      let res = await cluesGroupListNoPage(obj)
        .catch(() => {
          this.groupLoading = false
        })
        .catch(() => {
          this.groupArr = []
        })
      this.groupLoading = false
      let arr = res.data
      this.groupArr = arr
    },
    onekeyCheck() {
      if (this.isTreeChecked) {
        let arr = this.companyCascadeEquityList[0].children.map((item) => {
          return item.company_name
        })
        arr.push(this.addForm.companyName)
        this.$refs.companyTree.setCheckedKeys(arr)
      } else {
        this.$refs.companyTree.setCheckedKeys([])
      }
      this.$forceUpdate()
    },
    // 企业关系查询
    async Inquire(no_cache) {
      if (this.addForm.companyName != '') {
        this.companyCascadeEquityList = [
          {
            company_id: this.addForm.companyName,
            company_name: this.addForm.companyName,
            level: 0,
            children: []
          }
        ]
        let obj = {
          company_id: this.addForm.companyName,
          operate_company_id: this.currentCompany,
          no_cache: no_cache,
          not_need_icp: 1 // 不推送备案信息
        }
        this.companyCheckLoading = true
        this.treeIsTrue = true
        let res = await companyCascadeEquity(obj).catch(() => {
          this.companyCheckLoading = false
        })
        if (res.code == 0) {
          this.isInquireClick = true
          this.companyCheckLoading = false
          this.companyCascadeEquityList[0].children = res.data
          setTimeout(() => {
            this.checkedTree = res.data
              .filter((item) => {
                return parseFloat(item.rate) >= 50
              })
              .map((item) => {
                return item.company_name
              })
            this.checkedTree.push(this.addForm.companyName)
          }, 100)
        }
      }
    },
    iconUploadSucc(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        let obj = response.data
        this.content.content3.push(obj.url)
      } else {
        this.$message.error(file.response.message)
      }
    },
    iconUploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data.url
      })
      if (res.length == 0) {
        this.content.content3 = ''
      } else {
        this.content.content3 = res
      }
    },
    uploadErr(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    async sureGetClue() {
      if (!this.addForm.bandwidth) {
        this.$message.error('扫描带宽为必填项')
      } else if (/^(?:[1-9]\d*)$/.test(this.addForm.bandwidth) == false) {
        this.$message.error('扫描带宽请输入整数')
      }
      this.$refs.addFormRef.validate(async (valid) => {
        if (valid) {
          // 如果是客户账号，只能操作本企业及控股企业
          if (this.is_real_customer == 1) {
            if (
              this.restaurants
                .map((item) => {
                  return item.value
                })
                .indexOf(this.addForm.companyName) == -1
            ) {
              this.$message.error('暂无权限梳理该单位资产，请重新输入')
              return
            }
          }
          let clueData = []
          for (let i = 0; i <= 6; i++) {
            if (
              (i == 3 && this.content['content' + i].length > 0) ||
              (i != 3 && this.content['content' + i])
            ) {
              clueData.push({
                type: i,
                content:
                  i != 3
                    ? this.content['content' + i]
                        .split(/[；|;|\r\n]/)
                        .filter((item) => {
                          return item.trim()
                        })
                        .map((item) => {
                          return item.trim()
                        })
                    : this.content['content' + i]
              })
            }
          }
          if (this.otherCompany) {
            let otherCompany = this.otherCompany
              ? this.otherCompany.split(/[；|;|\r\n]/).filter((item) => {
                  return item.trim()
                })
              : []
            let validateRes = await this.companyOtherValidate(otherCompany).catch((error) => {
              console.log(error)
            })
            if (validateRes) {
              this.errorType = 'company'
              this.otherCompanyValidVisible = true
              return
            }
          }
          if (this.isAssetsError) {
            this.errorType = 'assets'
            this.otherCompanyValidVisible = true
            return
          }
          let otherCompany = this.otherCompany
            ? this.otherCompany
                .split(/[；|;|\r\n]/)
                .filter((item) => {
                  return item.trim()
                })
                .map((item) => {
                  return item.trim()
                })
            : []
          let company_list = []
          let no_need_controy_company = ''
          if (this.$refs.companyTree) {
            let companyTreeData = this.$refs.companyTree.getCheckedKeys()
            // 若企业关系查询数据存在，合并otherCompany其他测绘企业并去重
            // company_list = [...new Set(companyTreeData.concat(otherCompany))]
            company_list = companyTreeData
            no_need_controy_company = this.isInquireClick && companyTreeData.length == 0 ? '1' : ''
          } else {
            company_list = []
            // company_list = [...otherCompany]
          }

          this.sureLoading = true
          let percent = +this.addForm.percent
          let obj = {
            ...this.addForm,
            name: this.addForm.companyName,
            expand_init_clues: this.addForm.expand_init_clues ? 1 : 0,
            scene_group_id: this.addForm.scene_group_id,
            is_detect_scene: this.addForm.is_detect_scene,
            operate_company_id: this.currentCompany,
            company_list: company_list,
            other_company_list: otherCompany,
            has_other_company: this.otherCompany.length > 0 ? 1 : 0, // 是否有其他测绘企业输入
            data: clueData,
            no_need_controy_company,
            percent
          }
          let res1 = await taskParamsSave(obj).catch(() => {
            this.sureLoading = false
          })
          if (res1.code !== 0) return (this.sureLoading = false)
          let {data} = await personInfo({
            detail:1,
            operate_company_id:this.currentCompany
          })
            let res = await taskConfirm({
              detect_task_id: res1.data.detect_task_id,
              operate_company_id: this.currentCompany
            }).catch(() => {
              this.sureLoading = false
            })
            if (res.code == 0) {
              sessionStorage.setItem('mappingTaskId', res.data.detect_task_id)
              sessionStorage.setItem('mappingTaskGroupId', res.data.group_id)
              //子传父
              this.$emit('son', '2', res.data.detect_task_id)
            }
          this.sureLoading = false
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.highBtn {
  width: 174px;
}

.footer {
  // position: fixed;
  // bottom: 0;
  // left: 220px;
  // width: calc(100% - 220px) !important;

  // right: 0;
  // width: 100%;
  .normalBtn {
    width: 254px;
  }
}

.myBox1 {
  position: relative;
  height: calc(100% - 56px);
  width: 100%;

  .el-input__suffix-inner > i {
    margin-top: 0 !important;
  }
}

.content {
  width: calc(100% - 32px);
  height: 100%;

  // height: calc(100% - 40px);
  padding: 0 16px;
  overflow: auto;

  .module {
    width: 100%;
    margin-bottom: 20px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    background: #ffffff;
    border-radius: 4px;

    .top {
      height: 46px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e9ebef;

      // margin-bottom: 32px;
      // span {
      //   color: #a1b5d8;
      // }

      .label {
        display: flex;
        align-items: center;
        height: 2px;
        font-size: 16px;
        font-weight: 600;
        line-height: 16px;
        .blueBlock {
          display: inline-block;
          width: 4px;
          height: 16px;
          border-radius: 0 2px 2px 0;
          margin-right: 12px;
          background-color: #2677ff;
        }
      }

      .act {
        // line-height: 46px;
        display: flex;
        align-items: center;
        color: #2677ff;
        &:hover {
          cursor: pointer;
        }

        img {
          margin-right: 16px;
          margin-left: 4px;
        }
      }
    }

    .moduleBottom {
      // margin-top: 32px;
      padding: 32px 0;

      .inputNumber {
        width: 100%;
      }

      /deep/.el-select {
        width: 100% !important;

        .el-input__icon {
          // height: 30px;
          margin-top: 0;
        }
      }

      /deep/.el-form-item:last-child {
        margin-bottom: 0;
      }
    }
  }

  .findModule {
    // background:  url('../../assets/images/taskFirst1.png') no-repeat top center;
    background-image: url(../../assets/images/taskFirst1.png);
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top center;
    background-color: #fff;
  }

  .modelModule,
  .paramsModule,
  .taskModule {
    /deep/.el-form-item {
      .el-form-item__content {
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: start;

        height: 32px;

        .el-checkbox-group {
          height: 100%;
        }

        .el-checkbox {
          // height: 100%;
          line-height: 32px;
        }
      }
    }
  }

  /deep/.drawTypeClass {
    margin: 20px 0 20px 0;

    .el-form-item__label {
      line-height: 42px !important;

      img {
        line-height: 32px;
      }
    }
  }

  .treeWrap {
    .onekeyclass {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25px;
      background: #fff;
      .statistics {
        color: #62666c;
        font-size: 12px;
      }
    }
  }
}

.companyTree {
  height: 222px;
  overflow: auto;
  color: #37393c;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #d1d5dd;

  .custom-tree-node {
    display: flex;
    align-items: center;
    width: 100%;

    .nodeLabel {
      display: inline-block;
      max-width: 70%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.treeNum {
  display: inline-block;
  width: 83px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  margin-left: 16px;
  font-size: 12px;
  border-radius: 14px;
}

.bigNum {
  background: #f0f3f8;
  border: 1px solid #dce5f3;

  .num {
    color: #2677ff;
  }
}

.smallNum {
  background: #f0f3f8;
  border: 1px solid #dce5f3;

  .num {
    color: #ec8f3c;
  }
}
.companyGray {
  color: #ccd2db;
  // background: #CCD2DB;
}

.companyBlue {
  color: #2677ff;
  border: 0;
  &:hover {
    color: #4389ff;
  }
  // background: #2677FF !important;
}

/deep/.el-input__suffix-inner > i {
  margin-top: 3px;
}

/deep/.el-input__inner {
  height: 32px;
}

/deep/.companyDiv {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
  .select-button {
    position: relative;
    width: 100%;
    height: 32px;
    #unit_sure {
      position: absolute;
      top: 1px;
      right: 1px;
      height: 30px;
      margin-left: 0px !important;
      border-radius: 0px 4px 4px 0px;
      border: 0;
    }
  }
  .el-autocomplete {
    width: 83% !important;

    .el-input__inner {
      // border-radius: 4px 0px 0px 4px !important;
      border-radius: 4px !important;
      cursor: text;
      height: 32px;
    }

    .el-form-item__content .el-input__icon {
      line-height: 36px !important;
    }
  }

  .el-button {
    border: 0;
    padding: 0 15px !important;
  }
  .el-select {
    position: relative;
    .el-input__suffix {
      display: none;
    }
    .el-input__inner {
      padding-right: 125px;
    }
  }
}

/deep/.upload-demo {
  .el-upload-dragger {
    height: 134px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #eff2f7;
    img {
      width: 28px;
    }
  }

  .el-upload-list {
    // max-height: 200px !important;
    min-height: auto;
    // padding-bottom: 20px;
    // display: none;
  }
  .el-upload__text {
    color: #62666c;
  }

  .el-upload__tip {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: rgba(98, 102, 108, 0.7);

    .downloadBtn {
      color: #2677ff;

      &:hover {
        cursor: pointer;
      }
    }
  }
}

// /deep/.el-select {
//   width: 83% !important;
//   height: auto;
//   line-height: auto;

//   .el-input__inner {
//     // border-radius: 4px 0px 0px 4px !important;
//     cursor: text;
//     height: 40px;
//   }
// }

.errorTip {
  line-height: 20px;
  font-size: 12px;
  color: red;
}

.dialog-item {
  line-height: 30px;
}

.elDialogAddValidate {
  /deep/.el-dialog__body {
    min-height: 100px !important;
  }
}

/deep/ .el-form {
  padding: 0 !important;

  .el-form-item {
    width: 724px;
    margin-left: 40px;

    .el-form-item__label {
      display: flex;
      align-items: center;
      text-align: left;

      img {
        &:hover {
          cursor: pointer;
        }

        // margin-left: px;
      }

      &::before {
        display: none;
      }
    }

    &.is-required {
      position: relative;

      .el-form-item__content::after {
        position: absolute;
        right: -16px;
        top: 0;
        content: '*';
        font-size: 24px;
        // width: 10px;
        color: #f56c6c;
      }
    }
  }
}
/deep/.el-tooltip__popper {
  li::before {
    content: '';
    width: 6px;
    height: 6px;
    display: inline-block;
    border-radius: 50%;
    background: black;
    vertical-align: middle;
    margin-right: 14px;
  }
}
/deep/.el-input__suffix {
  right: 15px;
}
/deep/.el-select {
  display: block;
}
.btn {
  &:hover {
    color: #2677ff;
  }
}
</style>
