<template>
  <div class="taskBox">
    <taskHeader
      :companyName="taskInfoData.name"
      :isOuterProcess="isScan || progressBar"
      :progress="currentPercent"
      :currentStep="4"
    >
      <template #bottomText>
        <div class="progressContent" v-if="isScan && progressBar">
          {{ headText }}...
          <span
            style="color: #2677ff; cursor: pointer"
            v-if="tabIcon == 'two'"
            @click="$router.push({ path: '/assetsScan', query: { fromPath: '/unitIndex' } })"
            >（点击查看更精准扫描进度）</span
          >
        </div>
        <div class="progressContent" v-if="!isScan && !progressBar && tabIcon == 'one'">
          <img src="../../assets/images/taskSed.png" alt="" />
          <span>资产信任度评估完成：</span>高可信任资产：
          <span style="color: #2677ff">{{ tabNum[tabIcon]['high'] }}</span
          >条，中可信任资产： <span style="color: #2677ff">{{ tabNum[tabIcon]['middle'] }}</span
          >条，低可信任资产： <span style="color: #2677ff">{{ tabNum[tabIcon]['low'] }}</span
          >条
        </div>
        <div class="progressContent" v-if="!isScan && !progressBar && tabIcon == 'two'">
          <img src="../../assets/images/taskSed.png" alt="" />
          <span>资产入账扫描完成：</span>台账资产：
          <span style="color: #2677ff">{{ tabNum[tabIcon]['konwn_table_num'] }}</span
          >条，疑似资产：
          <span style="color: #2677ff">{{ tabNum[tabIcon]['unknown_table_num'] }}</span
          >条，威胁资产：
          <span style="color: #2677ff">{{ tabNum[tabIcon]['threat_table_num'] }}</span
          >条
        </div>
      </template>
      <template #buttonDiv>
        <el-button
          class="headerBtn"
          id="unit_recommend_end"
          @click="endTask"
          type="text"
          v-if="!isScan && tabIcon == 'one'"
        >
          <svg class="icon svg-icon icon-close" aria-hidden="true">
            <use xlink:href="#icon-close"></use>
          </svg>
          结束流程
        </el-button>
      </template>
    </taskHeader>
    <div class="myBox" v-if="!isScan">
      <div class="box">
        <div class="grayBox">
          <div v-if="tabIcon == 'one'">
            <span><span style="color: #2677ff; font-weight: 600">信任等级</span></span
            ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
              class="graylabel"
              >通过AI智能模型获取资产可信度等级，依据不同信任度进行资产分类入账</span
            >
          </div>
          <div v-if="tabIcon == 'two'">
            <span><span style="color: #2677ff; font-weight: 600">入账扫描</span></span
            ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
              class="graylabel"
              >通过AI智能模型获取资产可信度等级，依据不同信任度进行资产分类入账</span
            >
          </div>
        </div>
      </div>
      <div :class="progressBar ? 'boxTwo' : 'boxTwo1'" v-loading="loading">
        <div class="tableLabel">
          <div>
            <div>{{ tableText }}</div>
            <div class="confirmBox" style="margin-right: 16px">
              <el-radio-group
                v-model="tabActive"
                @change="changeTab(tabActive, 'isTab', 'yesLoading')"
              >
                <el-radio-button
                  :label="item.level"
                  v-for="(item, index) in tabList[tabIcon]"
                  :key="index"
                  >{{ item.name }}({{ tabNum[tabIcon][item.icon] }})</el-radio-button
                >
              </el-radio-group>
            </div>
            <el-input
              v-model="formInline.keyword"
              placeholder="请输入关键字检索"
              @keyup.enter.native="highCheck"
              id="keyword_keycheck"
            >
              <el-button slot="append" icon="el-icon-search" @click="highCheck"></el-button>
            </el-input>
            <span class="btn" @click="highCheckClick"
              ><img
                src="../../assets/images/filter.png"
                alt=""
                style="width: 16px; vertical-align: middle; margin-right: 3px"
              />高级筛选</span
            >
          </div>
          <div>
            <!-- <el-checkbox class="checkboxAll" v-model="checkedAll" @change="checkAllChange" id="keyword_all">选择全部</el-checkbox> -->
            <!-- 入账后删除 -->
            <el-button
              v-if="tabIcon == 'two'"
              class="normalBtnRe"
              type="primary"
              @click="remove"
              id="unit_del"
              >删除</el-button
            >
            <!-- 评估的删除 -->
            <el-button
              v-if="tabIcon == 'one'"
              class="normalBtnRe"
              type="primary"
              @click="removeMore"
              id="unit_del"
              >删除</el-button
            >
          </div>
        </div>
        <div class="myTable">
          <tableList
            ref="tableList"
            :tableData="tableData"
            :pageIcon="tabIcon == 'two' ? 'scan' : 'predict'"
            :pageTab="tabActive"
            :expend_id="taskInfoData"
            :checkedAll="checkedAll"
            :isClearSelection="isClearSelection"
            :levelIcon="String(this.tabActive)"
            @getLevelList="getCloudAssetsAnyslate"
            @getList="getSacnistData"
            @handleSelectionChange="handleSelectionChange"
          />
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="pageSizeArr"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
        <div class="footer">
          <!-- scanLoading || progressBar -->
          <!-- 入账扫描按钮置灰点击入账扫描按钮请求接口响应时；进度条正在进行时； -->
          <el-button
            v-if="!isOverTask"
            class="normalBtn"
            type="primary"
            @click="goScan"
            id="unit_scan"
            :disabled="scanLoading || progressBar"
            >资产入账扫描</el-button
          >
          <el-button
            v-if="isOverTask && taskInfoData.expand_source == 1"
            class="normalBtn"
            type="primary"
            @click="goAutoTaskCloud"
            id="unit_report"
            :disabled="progressBar"
            >关联任务自定义</el-button
          >
          <el-button
            v-if="isOverTask && taskInfoData.expand_source == 0"
            class="normalBtn"
            type="primary"
            :loading="autoTaskLoading"
            @click="goAutoTask"
            id="unit_report"
            :disabled="progressBar"
            >完成</el-button
          >
        </div>
      </div>
      <highCheckDrawerScan
        :highCheckdialog="highCheckdialog"
        :selectArr="cluesList"
        :formInline="formInline"
        @highCheck="highCheck"
        @highIsClose="highIsClose"
      />
    </div>
    <div class="fourthBox" v-else>
      <div class="loading" :class="{ scroll: isLoadingBgc }" v-if="!taskLoading">
        {{ tabIcon == 'one' ? '已完成以下资产的信任度评估' : '已完成以下资产的扫描入账' }}
      </div>
      <div ref="fourthBoxContainer" class="fourthBoxContainer" v-loading="taskLoading">
        <taskLoading :list="list">
          <template v-slot:item="{ data }">
            <div class="loadingItem">
              <el-tooltip effect="dark" placement="top" :open-delay="500" :content="data.ip">
                <span>{{ data.ip }}</span>
              </el-tooltip>
              <span class="port">{{ getTableItem(data.port) }}</span>
              <span class="protocol">{{ $punyCode.toUnicode(getTableItem(data.protocol)) }}</span>
              <el-tooltip
                effect="dark"
                placement="top"
                :open-delay="500"
                :content="getCompany(data.clue_company_name)"
              >
                <span>{{ getCompany(data.clue_company_name) }}</span>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                placement="top"
                :open-delay="500"
                :content="$punyCode.toUnicode(getTableItem(data.subdomain))"
              >
                <span>{{ $punyCode.toUnicode(getTableItem(data.subdomain)) }}</span>
              </el-tooltip>
              <span v-if="tabIcon == 'two'" class="blueBlock">入账到资产台账</span>
              <span v-if="tabIcon == 'one'" class="blueBlock">高可信度</span>
            </div>
          </template>
        </taskLoading>
      </div>
    </div>
  </div>
</template>
<script>
import taskLoading from './taskLoading.vue'
import taskHeader from './taskHeader.vue'
import * as animationData from '../../assets/images/data.json'
import Lottie from 'vue-lottie/src/lottie.vue'
import tableList from '../home_set/detectIndexTable.vue'
import highCheckDrawerScan from '../home_set/highCheck.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { getScanList, delScanList } from '@/api/apiConfig/api.js'
import { regRecommendClues } from '@/api/apiConfig/recommend.js'
import {
  goRelate,
  updateCloudAssetsCount,
  delCloudAssetsList,
  cloudAssetsList,
  cloudAssetsReport,
  cloudAssetsScan,
  cloudAssetsAnyslate,
  endDetectTask
} from '@/api/apiConfig/surveying.js'

export default {
  components: { Lottie, tableList, highCheckDrawerScan, taskHeader, taskLoading },
  props: ['taskInfoData', 'expandType'],
  data() {
    return {
      interval: 30 * 1000,
      isClearSelection: true,
      isLoadingBgc: true,
      taskLoading: false,
      loadingTimer1: null,
      loadingTimer2: null,
      list: [],
      headText: '正在进行资产评估…',
      cluesList: {},
      scanCondition: {},
      highCheckdialog: false,
      proIsTrue: true, // 智能模式下进度条显示隐藏，解决进度条100%-0%的动画效果
      againIsShow: false,
      scanLoading: false,
      loading: false,
      isOverTask: false, // 是否完成入账扫描
      currentPercent: 0,
      defaultOptions: {
        //json动画
        animationData: animationData.default
      },
      progressBar: true, //是否显示进度条
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      tableText: '资产信息',
      tabActive: 1,
      tabNum: {
        one: { high: '', middle: '', low: '' },
        // 'one': {a: '', b: '', c: '', d: ''},
        two: { konwn_table_num: '', unknown_table_num: '', threat_table_num: '' }
      },
      tabIcon: 'one',
      tabList: {
        one: [
          {
            level: 1,
            name: '高可信度',
            icon: 'high'
          },
          {
            level: 2,
            name: '中可信度',
            icon: 'middle'
          },
          {
            level: 3,
            name: '低可信度',
            icon: 'low'
          }
        ],
        // 'one': [
        //     {
        //         level: 1,
        //         name: 'A级',
        //         icon: 'a'
        //     },
        //     {
        //         level: 2,
        //         name: 'B级',
        //         icon: 'b'
        //     },
        //     {
        //         level: 3,
        //         name: 'C级',
        //         icon: 'c'
        //     },
        //     {
        //         level: 4,
        //         name: 'D级',
        //         icon: 'd'
        //     }
        // ],
        two: [
          {
            level: 1,
            name: '资产台账',
            icon: 'konwn_table_num'
          },
          {
            level: 0,
            name: '疑似资产',
            icon: 'unknown_table_num'
          },
          // {
          //     level: 2,
          //     name: '忽略资产',
          //     icon: 'b'
          // },
          {
            level: 3,
            name: '威胁资产',
            icon: 'threat_table_num'
          }
        ]
      },
      tableData: [],
      checkedArr: [],
      checkedAll: false,
      setIntervalLevel: null,
      isScan: true, //入账扫描
      formInline: {
        ip: '',
        clue_company_name: '',
        province: '', // 省份名称（传汉字）,
        keyword: '', // 123123,
        asn: '', // 123123,
        cert: '', // cert,
        icp: '', // icp,
        url: '',
        title: '', // title,
        protocol: '', // protocol,
        logo: '', // logo,
        port: '', // port,
        cname: '', // cname,
        domain: [], // domain
        latitude: '',
        longitude: '',
        state: '',
        reason: '',
        reason_type: '', // 证据链
        tags: '',
        hosts: '',
        rule_tags: [],
        last_update_time: [],
        second_confirm: '' // second_confirm  0/待确认 1/已经确认
      },
      autoTaskLoading: false
    }
  },
  watch: {
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    taskInfoData(val) {
      this.getInit()
    },
    tabIcon: {
      handler(val) {
        this.isClearSelection = true
        if (this.isScan && this.tabIcon == 'two') {
          this.list = []
          if (this.loadingTimer2) {
            clearTimeout(this.loadingTimer2)
            this.loadingTimer2 = null
          }
          this.getSacnistDataPushTimer()
        } else if (this.isScan && this.tabIcon == 'one') {
          this.list = []
          if (this.loadingTimer1) {
            clearTimeout(this.loadingTimer1)
            this.loadingTimer1 = null
          }
          this.getScanListPushTimer()
        } else if (!this.isScan) {
          this.list = []
          if (this.loadingTimer1) {
            clearTimeout(this.loadingTimer1)
            this.loadingTimer1 = null
          }
          if (this.loadingTimer2) {
            clearTimeout(this.loadingTimer2)
            this.loadingTimer2 = null
          }
        }
      }
      // immediate:true
    },
    isScan: {
      handler(val) {
        if (this.isScan && this.tabIcon == 'two') {
          this.list = []
          if (this.loadingTimer2) {
            clearTimeout(this.loadingTimer2)
            this.loadingTimer2 = null
          }
          this.getSacnistDataPushTimer()
        } else if (this.isScan && this.tabIcon == 'one') {
          this.list = []
          if (this.loadingTimer1) {
            clearTimeout(this.loadingTimer1)
            this.loadingTimer1 = null
          }
          this.getScanListPushTimer()
        } else if (!this.isScan) {
          this.list = []
          if (this.loadingTimer1) {
            clearTimeout(this.loadingTimer1)
            this.loadingTimer1 = null
          }
          if (this.loadingTimer2) {
            clearTimeout(this.loadingTimer2)
            this.loadingTimer2 = null
          }
        }
      }
    },
    list: {
      handler(newVal) {
        if (newVal.length == 0) {
          this.taskLoading = true
        } else {
          this.taskLoading = false
          this.$nextTick(() => {
            let dom = document.getElementsByClassName('fourthBoxContainer')[0]
            let innerDom = document.getElementsByClassName('list')[0]
            if (dom && innerDom && innerDom.clientHeight > dom.clientHeight) {
              // 出现滚动条
              this.isLoadingBgc = true // 显示模糊背景
              dom.scrollTop = dom.scrollHeight // 自动滚动到底部
            } else {
              this.isLoadingBgc = false
            }
          })
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['currentCompany', 'recommentFlag']),
    ...mapGetters(['getterWebsocketMessage'])
  },
  beforeDestroy() {
    this.isScan = false
    clearInterval(this.setIntervalLevel)
    this.setIntervalLevel = null
    if (this.loadingTimer1) {
      clearTimeout(this.loadingTimer1)
      this.loadingTimer1 = null
    }
    if (this.loadingTimer2) {
      clearTimeout(this.loadingTimer2)
      this.loadingTimer2 = null
    }
  },
  mounted() {
    this.getInit()
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    async endTask() {
      this.$confirm('确定结束流程?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            expend_id: this.taskInfoData.id,
            operate_company_id: this.currentCompany
          }
          let res = await endDetectTask(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.$emit('son', '1')
          }
        })
        .catch(() => {})
    },
    getCompany(item) {
      if (item) {
        if (Array.isArray(item)) {
          if (item.length == '0') {
            return ''
          } else {
            return item.join(',')
          }
        } else {
          return String(item)
        }
      } else {
        return ''
      }
    },
    getTableItem(item) {
      if (Array.isArray(item)) {
        if (item.length == 0) {
          return '-'
        }
      }
      if (item) {
        if (Array.isArray(item)) {
          return item.join(',')
        } else {
          return String(item)
        }
      } else {
        return '-'
      }
    },
    async getInit() {
      // 401|0未完成入帐扫描显示进度，401|1已完成入帐扫描不显示进度
      if (this.taskInfoData) {
        if (this.taskInfoData.step_detail == 401) {
          // 入账扫描步骤
          this.tabIcon = 'two' // 入账扫描
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式
            if (this.taskInfoData.step_status == 0) {
              // 正在扫描
              this.isScan = true
              this.proIsTrue = true
              this.currentPercent =
                this.taskInfoData.step_status == 0 ? this.taskInfoData.progress / 1 : 0
              this.headText =
                this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
              this.list = []
              if (this.loadingTimer2) clearTimeout(this.loadingTimer2)
              this.getSacnistDataPushTimer()
            } else {
              this.isOverTask = true // 是否已经点击了入账扫描，控制入账扫描按钮显示
              this.progressBar = this.taskInfoData.step_status == 0 ? true : false
              this.currentPercent =
                this.taskInfoData.step_status == 0 ? this.taskInfoData.progress / 1 : 0
              this.isScan = false // 已经扫描完成
            }
          } else {
            this.isOverTask = true // 是否已经点击了入账扫描，控制入账扫描按钮显示
            this.progressBar = this.taskInfoData.step_status == 0 ? true : false
            this.currentPercent =
              this.taskInfoData.step_status == 0 ? this.taskInfoData.progress / 1 : 0
            if (this.taskInfoData.step_status == 0) {
              // 正在扫描
              this.isScan = true // 正在入账扫描，控制特效图
              this.headText =
                this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
              this.list = []
              if (this.loadingTimer2) clearTimeout(this.loadingTimer2)
              this.getSacnistDataPushTimer()
            } else {
              this.isScan = false // 已经扫描完成，控制特效图
            }
          }
        } else if (this.taskInfoData.step_detail == 400) {
          // 资产评估步骤
          this.tabIcon = 'one'
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式
            if (this.taskInfoData.is_intellect_failed == 1) {
              // 代表智能模式入账扫描失败，此时需要手动触发入账扫描
              this.isScan = true
              this.proIsTrue = false
              this.headText = '评估完成，等待资产入账扫描'
              let obj = {
                expend_id: this.taskInfoData.id,
                operate_company_id: this.currentCompany
              }
              let res = await cloudAssetsScan(obj).catch(() => {
                this.loading = false
                this.scanLoading = false
              })
              if (res.code == 0) {
                this.proIsTrue = true
                // this.headText = '正在等待资产入账扫描…'
                this.headText =
                  this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
                if (this.loadingTimer1) clearTimeout(this.loadingTimer1)
                this.getScanListPushTimer()
              } else {
                this.proIsTrue = false
                this.headText = res.data // 失败的原因
              }
            } else {
              if (this.taskInfoData.step_status == 0) {
                // 正在评估
                this.isScan = true
                this.tabIcon = 'one'
                this.proIsTrue = true
                this.headText = '正在进行资产评估…'
                this.list = []
                if (this.loadingTimer1) clearTimeout(this.loadingTimer1)
                this.getScanListPushTimer()
                this.currentPercent =
                  this.taskInfoData.step_status == 0
                    ? this.taskInfoData.update_assets_level_progress / 1
                    : 0
              } else {
                // 评估完成,开始扫描
                this.isScan = true
                this.tabIcon = 'two'
                // this.headText = '正在进行资产入账扫描…'
                this.headText =
                  this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
                this.list = []
                if (this.loadingTimer2) clearTimeout(this.loadingTimer2)
                this.getSacnistDataPushTimer()
              }
            }
          } else {
            this.isOverTask = false // 是否已经点击了入账扫描，控制入账扫描按钮显示
            if (this.taskInfoData.step_status == 0) {
              // 正在评估
              this.isScan = true // 还未点击入账扫描
              this.progressBar = true
              this.list = []
              if (this.loadingTimer1) clearTimeout(this.loadingTimer1)
              this.getScanListPushTimer()
              this.currentPercent = this.taskInfoData.update_assets_level_progress / 1
              // this.getUpdateLevel()
            } else {
              // 评估完成，等待扫描
              this.isScan = false // 还未点击入账扫描
              this.progressBar = false
              this.currentPercent = 0
              clearInterval(this.setIntervalLevel)
              this.setIntervalLevel = null
            }
          }
        }
        // this.getCloudAssetsAnyslate('yesLoading')
      }
      // else {
      //     this.getCloudAssetsAnyslate('yesLoading')
      // }
      if (!this.isScan) {
        this.getCloudAssetsAnyslate('yesLoading')
      }
    },
    async againScan() {
      this.proIsTrue = false
      let obj = {
        expend_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      let res = await cloudAssetsScan(obj).catch(() => {
        this.loading = false
        this.scanLoading = false
      })
      if (res.code == 0) {
        this.proIsTrue = true
        this.againIsShow = false
        this.currentPercent = 0
        this.headText = this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
      }
    },
    // 每隔10请求一次评估列表
    getUpdateLevel() {
      this.setIntervalLevel = setInterval(() => {
        this.getCloudAssetsAnyslate('noLoading')
      }, 10000)
    },
    async handleMessage(res, o) {
      //执行扫描
      // detect_assets_tasks单位资产测绘模块入账 扫描
      if (
        res.cmd == 'detect_assets_tasks' &&
        res.data &&
        res.data.detect_assets_tasks_id == this.taskInfoData.id
      ) {
        if (res.data.status == 2) {
          this.$message.success('扫描成功！')
          this.progressBar = false // 进度条不显示
          this.isScan = false // 已经扫描完成，控制特效图
          this.currentPercent = 100
          this.currentId = '' // 控制推送结束后仅执行一次
          this.isOverTask = true
          this.getSacnistData('yesLoading')
          // 扫描结束需要触发此接口重新计算，解决es计算不准
          updateCloudAssetsCount({
            expend_id: this.taskInfoData.id,
            operate_company_id: this.currentCompany
          })
        } else if (res.data.status == 1) {
          // 正在扫描
          this.tabIcon = 'two'
          // 推送数据渲染到列表;因后端拆分任务扫描，会有多个进度而导致进度回退，所以小于当前进度不显示
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式
            this.isScan = true
            this.proIsTrue = true
            this.headText =
              this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
          } else {
            this.isScan = true
            this.currentId = res.data.user_id
            this.progressBar = true
            this.headText =
              this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
          }
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.isScan = false
          this.getSacnistData('yesLoading')
        }
      } else if (
        res.cmd == 'detect_assets_tasks_cloud' &&
        res.data &&
        res.data.detect_assets_tasks_id == this.taskInfoData.id
      ) {
        // detect_assets_tasks_cloud 云端推荐模块入账 扫描
        if (res.data.status == 2) {
          this.$message.success('扫描成功！')
          this.progressBar = false // 进度条不显示
          this.isScan = false // 已经扫描完成，控制特效图
          this.isOverTask = true
          this.currentPercent = 100
          this.currentId = '' // 控制推送结束后仅执行一次
          this.getSacnistData('yesLoading')
          // 扫描结束需要触发此接口重新计算，解决es计算不准
          updateCloudAssetsCount({
            expend_id: this.taskInfoData.id,
            operate_company_id: this.currentCompany
          })
        } else if (res.data.status == 1) {
          // 正在扫描
          this.tabIcon = 'two'
          this.currentId = res.data.user_id
          this.progressBar = true //
          // 推送数据渲染到列表;因后端拆分任务扫描，会有多个进度而导致进度回退，所以小于当前进度不显示
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          this.headText =
            this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.isScan = false
          this.getSacnistData('yesLoading')
        }
      } else if (
        res.cmd == 'update_assets_level' &&
        res.data &&
        this.taskInfoData.id == res.data.task_id
      ) {
        // update_assets_level 单位资产测绘模块资产评估
        this.recommentFlagChange(res.data.flag) // 存储云端推荐flag
        if (res.data.status == 2) {
          this.currentPercent = 100
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式
            this.isScan = true
            this.proIsTrue = false
            this.proIsTrue = true
            this.headText = '评估完成，等待资产入账扫描'
            this.currentPercent = 0
            setTimeout(() => {
              this.$emit('son', '4', this.taskInfoData.id) // 每个流程结束需要主动请求详情接口，获取最新进度
            }, 2000)
          } else {
            this.$message.success('评估成功！')
            this.isScan = false
            clearInterval(this.setIntervalLevel)
            this.setIntervalLevel = null
            this.progressBar = false // 进度条不显示
            this.currentId = '' // 控制推送结束后仅执行一次
            this.loading = true
            setTimeout(async () => {
              // 评估完成后列表数据不能及时更新，延时请求
              this.getCloudAssetsAnyslate('yesLoading')
            }, 2000)
          }
        } else if (res.data.status == 1) {
          // 正在扫描
          this.tabIcon = 'one'
          this.updateLevelIsTrue = true
          this.currentId = res.data.user_id
          this.progressBar = true // 进度条不显示
          this.isScan = true
          // 推送数据渲染到列表
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.getCloudAssetsAnyslate('yesLoading')
        }
      } else if (
        res.cmd == 'update_assets_level_cloud' &&
        res.data &&
        this.taskInfoData.id == res.data.task_id
      ) {
        // update_assets_level_cloud 云端推荐模块资产评估
        this.recommentFlagChange(res.data.flag) // 存储云端推荐flag
        if (res.data.status == 2) {
          this.$message.success('评估成功！')
          this.loading = true
          this.isScan = false
          this.headText = '评估完成，等待资产入账扫描'
          this.currentPercent = 0
          clearInterval(this.setIntervalLevel)
          this.setIntervalLevel = null
          this.progressBar = false // 进度条不显示
          this.currentId = '' // 控制推送结束后仅执行一次
          setTimeout(() => {
            // 评估完成后列表数据不能及时更新，延时请求
            this.getCloudAssetsAnyslate('yesLoading')
          }, 2000)
        } else if (res.data.status == 1) {
          // 正在扫描
          this.tabIcon = 'one'
          this.updateLevelIsTrue = true
          this.currentId = res.data.user_id
          this.progressBar = true // 进度条不显示
          // 推送数据渲染到列表
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.getCloudAssetsAnyslate('yesLoading')
        }
      }
      // 智能模式失败detect_direct_operate，需要主动云端推荐
      if (
        res.cmd == 'detect_direct_operate' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        this.proIsTrue = false
        this.againIsShow = true
        this.headText = res.data.error_msg // 失败原因
      }
    },
    getSacnistDataPushTimer(val) {
      this.list = []
      this.interval = 30 * 1000
      this.getSacnistDataPush(val)
      // if(this.loadingTimer1) clearInterval(this.loadingTimer1)
      // this.loadingTimer1 = setInterval(() => {
      //     this.getSacnistDataPush(val)
      // },180000)
    },
    async getSacnistDataPush() {
      let listObj = {
        status: this.tabActive, // 0 默认无任何状态(未知/疑似资产) 1/已认领(已确认)   2/忽略资产   3/威胁
        operate_company_id: this.currentCompany,
        expend_id: this.taskInfoData.id,
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline
      }
      let res = await cloudAssetsList(listObj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        let list = []
        res.data.items.forEach((item) => {
          let items = item.detail
          item.detail.forEach((detailItem) => {
            detailItem.id = item.id + detailItem.port
            detailItem.ip = item.ip
            list = list.concat(items)
          })
        })
        let newList = new Set(list)
        let newList1 = Array.from(newList)
        let copyList = JSON.parse(JSON.stringify(this.list))
        let newSelectList = newList1.filter(
          (item1) => !copyList.some((item2) => item1.id === item2.id)
        )
        this.list.push(...newSelectList)
        if (this.list.length !== 0) {
          this.interval = 5 * 60 * 1000
        }
        if (this.isScan) {
          if (this.loadingTimer1) {
            clearTimeout(this.loadingTimer1)
            this.loadingTimer1 = null
          }
          this.loadingTimer1 = setTimeout(this.getSacnistDataPush, this.interval)
        }
      }
    },
    // 获取入账扫描后的列表数据以及tab数量
    async getSacnistData(isLoading) {
      this.loading = isLoading == 'yesLoading' ? true : false
      let listObj = {
        status: this.tabActive, // 0 默认无任何状态(未知/疑似资产) 1/已认领(已确认)   2/忽略资产   3/威胁
        operate_company_id: this.currentCompany,
        expend_id: this.taskInfoData.id,
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline
      }
      let res = await cloudAssetsList(listObj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        res.data.items.forEach((ch) => {
          // 用于控制折叠展开
          ch.isExpand = false
          ch.myPopover = false
          ch.myPopoverFlag = true
          ch.detail &&
            ch.detail.forEach((v) => {
              v.isURLExpand = false
            })
        })
        this.tableData = res.data.items
        this.scanCondition = res.data.condition ? res.data.condition : {}
        this.total = res.data.total
        this.tabNum['two'].konwn_table_num = res.data.konwn_table_num
        this.tabNum['two'].unknown_table_num = res.data.unknown_table_num
        this.tabNum['two'].threat_table_num = res.data.threat_table_num
      }
    },
    // 获取资产等级tab数量以及对应列表数据
    async getCloudAssetsAnyslate(isLoading) {
      if (this.tabIcon == 'one') {
        this.changeTab(this.tabActive, '', 'yesLoading') // 请求列表

        // let res = await cloudAssetsAnyslate({ // 请求tab数量
        //     flag: this.recommentFlag,
        //     data: {
        //         expand_source: this.taskInfoData.expand_source,
        //         operate_company_id: this.currentCompany,
        //         ...this.formInline
        //     }
        // })
        // if (res.code == 0) {
        //     // ABCD:1234
        //     this.tabNum['one'] = res.data
        // }
      } else {
        this.getSacnistData(isLoading)
      }
    },
    getScanListPushTimer(val) {
      this.list = []
      this.interval = 30 * 1000
      this.getScanListPush(val)
    },
    async getScanListPush(val) {
      let res = await getScanList({
        flag: this.recommentFlag,
        query: {
          assets_confidence_level: 1,
          page: this.currentPage,
          per_page: this.pageSize,
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      }).catch(() => {})
      if (res.code == 0) {
        let list = []
        Object.keys(res.data.items).forEach((item) => {
          list = list.concat(res.data.items[item])
        })
        let newList = new Set(list)
        let newList1 = Array.from(newList)
        let copyList = JSON.parse(JSON.stringify(this.list))
        let newSelectList = newList1.filter(
          (item1) => !copyList.some((item2) => item1.id === item2.id)
        )
        this.list.push(...newSelectList)
        if (this.list.length !== 0) {
          this.interval = 5 * 60 * 1000
        }
        if (this.isScan) {
          if (this.loadingTimer2) {
            clearTimeout(this.loadingTimer2)
            this.loadingTimer2 = null
          }
          this.loadingTimer2 = setTimeout((val) => this.getScanListPush(val), this.interval)
        }
      }
    },
    // 切换查询等级数据
    async changeTab(val, isTab, isLoading) {
      if (isTab) {
        // tab切换要把分页重置
        this.isClearSelection = true
        this.currentPage = 1
        this.cluesList = {}
        this.formInline = {
          ip: '',
          clue_company_name: '',
          province: '', // 省份名称（传汉字）,
          keyword: '', // 123123,
          asn: '', // 123123,
          cert: '', // cert,
          icp: '', // icp,
          url: '',
          title: '', // title,
          protocol: '', // protocol,
          logo: '', // logo,
          port: '', // port,
          cname: '', // cname,
          domain: [], // domain
          latitude: '',
          longitude: '',
          state: '',
          reason: '',
          reason_type: '', // 证据链
          tags: '',
          hosts: '',
          rule_tags: [],
          last_update_time: [],
          second_confirm: '' // second_confirm  0/待确认 1/已经确认
        }
      }
      this.checkedArr = []
      if (this.tabIcon == 'one') {
        // 资产评估列表
        this.loading = isLoading == 'yesLoading' ? true : false
        let res = await getScanList({
          flag: this.recommentFlag,
          query: {
            assets_confidence_level: val,
            page: this.currentPage,
            per_page: this.pageSize,
            operate_company_id: this.currentCompany,
            ...this.formInline
          }
        }).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          this.loading = false
          let arr = []
          for (let key in res.data && res.data.items ? res.data.items : []) {
            arr.push({
              is_cdn: res.data.items[key][0].is_cdn ? res.data.items[key][0].is_cdn : false,
              ip: key,
              id: res.data.items[key][0].id,
              chain_list: res.data.items[key][0].chain_list,
              clue_company_name: res.data.items[key][0].clue_company_name,
              detail: res.data.items[key],
              rule_tags: [],
              hosts: [],
              isExpand: false,
              myPopover: false,
              myPopoverFlag: true,
              cloud_name: res.data.items[key][0].cloud_name,
              level_reason: res.data.items[key][0].level_reason
            })
          }
          this.tableData = arr.reverse()
          this.total = res.data.total
          let resnum = await cloudAssetsAnyslate({
            // 请求tab数量
            flag: this.recommentFlag,
            data: {
              expand_source: this.taskInfoData.expand_source,
              operate_company_id: this.currentCompany,
              ...this.formInline
            }
          })
          if (resnum.code == 0) {
            // ABCD:1234
            this.tabNum['one'] = resnum.data
          }
        }
      } else if (this.tabIcon == 'two') {
        // 入账扫描数据
        this.getSacnistData(isLoading)
      }
    },
    // 评估删除
    removeMore() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'account_del_cancel',
        confirmButtonClass: 'account_del_sure',
        customClass: 'account_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            flag: this.taskInfoData.expend_flags,
            data: {
              assets_confidence_level: String(this.tabActive),
              ip_array: this.checkedArr.map((item) => {
                return item.ip
              }),
              whole: this.checkedAll ? 1 : '',
              operate_company_id: this.currentCompany,
              ...this.formInline
            }
          }
          this.loading = true
          let res = await delScanList(obj).catch(() => {
            this.loading = false
          })
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.loading = false
            this.getCloudAssetsAnyslate('yesLoading')
          }
        })
        .catch(() => {})
    },
    // 入账扫描删除
    async remove() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        let obj = {
          id: this.checkedArr.map((item) => {
            return item.id
          }),
          expend_id: this.taskInfoData.id,
          status: this.tabActive,
          operate_company_id: this.currentCompany
        }
        let res = await delCloudAssetsList(obj).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          setTimeout(() => {
            this.loading = false
            this.$message.success('删除成功！')
            this.getSacnistData('yesLoading')
          }, 3000)
        }
      })
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.isClearSelection = false
      this.pageSize = val
      this.changeTab(this.tabActive, '', 'yesLoading')
    },
    handleCurrentChange(val) {
      this.isClearSelection = false
      this.currentPage = val
      this.changeTab(this.tabActive, '', 'yesLoading')
    },
    checkAllChange(row, index) {
      // return !this.checkedAll;
    },
    // 生成报告
    async goReport() {
      let obj = {
        expend_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      let res = await cloudAssetsReport(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
      }
    },
    goAutoTaskCloud() {
      this.$emit('son', '5') // 执行第五步
    },
    async goAutoTask() {
      let obj = {
        expend_id: this.taskInfoData.id,
        is_extract_clue: 1, //是否自动提取线索 0/1  不自动提取/自动提取
        is_check_risk: 0, // 是否自动监测风险事件 0/1  不自动/自动
        operate_company_id: this.currentCompany,
        is_auto_business_api: this.taskInfoData.is_auto_business_api // 是否自动关联业务系统  0/1 不自动/自动
      }
      this.autoTaskLoading = true
      let res = await goRelate(obj).catch(() => {
        this.autoTaskLoading = false
      }) // 确认、跳过
      this.recommentFlagChange('') // 清空flag
      if (res.code == 0) {
        this.autoTaskLoading = false
        this.$confirm('操作成功！是否立即生成报告?', '提示', {
          confirmButtonText: '立即生成',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            let obj = {
              expend_id: this.taskInfoData.id,
              operate_company_id: this.currentCompany
            }
            let res = await cloudAssetsReport(obj)
            if (res.code == 0) {
              this.$emit('son', '1')
              this.$message.success('操作成功！请稍后…')
            }
          })
          .catch(() => {
            this.$emit('son', '1')
          })
      }
    },
    // 入账扫描
    async goScan() {
      let obj = {
        expend_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      this.loading = true
      this.scanLoading = true
      let res = await cloudAssetsScan(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.scanLoading = false // 入账扫描按钮请求过程中暂时置灰
        this.loading = false
        const h = this.$createElement
        this.$message({
          type: 'success',
          message: h('div', null, [
            h('span', { style: 'color: #67C23A' }, `操作成功！`),
            h(
              'span',
              { style: 'color: #F8C136' },
              `${res.data.warn_message ? '(' + res.data.warn_message + ')' : ''}`
            )
          ])
        })
        this.tabActive = 1 // 切换到台账资产
        this.tabIcon = 'two'
        this.isScan = true // 正在入账扫描，控制特效图
        this.currentPercent = 0 // 入账扫描进度要从0开始，防止评估完成是100
        this.headText = this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
        this.progressBar = true // 显示进度条
        this.isOverTask = true // 是否已经点击了入账扫描，控制入账扫描按钮显示
      } else {
        this.tabIcon = 'two'
      }
    },
    highCheckClick() {
      this.highCheckdialog = true
      if (this.isOverTask) {
        // 已经完成入账扫描
        this.cluesList = { ...this.scanCondition }
      } else {
        // 评估完成还未入账扫描
        this.getRecommendCluesData()
      }
    },
    // 高级筛选条件
    async getRecommendCluesData() {
      // 线索
      this.loading = true
      let res = await regRecommendClues({
        flag: this.taskInfoData.expend_flags,
        data: {
          operate_company_id: this.currentCompany
        }
      }).catch(() => {
        this.loading = false
      })
      this.loading = false
      if (res.data && res.data.logo) {
        res.data.logo.forEach((item, index) => {
          item.id = index + 1
        })
      }
      this.cluesList = res.data ? res.data : []
    },
    highCheck() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.getCloudAssetsAnyslate('yesLoading')
    },
    highIsClose() {
      this.highCheckdialog = false
    }
  }
}
</script>
<style lang="less" scoped>
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.progressBarBox {
  padding-bottom: 4px !important;
}
.fourthBox {
  // padding: 20px 0;
  // padding-top: 20px;
  background: url('../../assets/images/unit/unitLoadingBgc.png') no-repeat;
  background-size: 100% 100%;
  .loading {
    padding-top: 20px;
  }

  .fourthBoxContainer {
    width: 100%;
    height: 100%;
    overflow: auto;
    display: flex;
    align-items: center;
    flex-direction: column;
  }
}
// .fourthBox {
//     // height: 100%;
//     height: calc(100% - 176px);
//     background: -webkit-linear-gradient(180deg, rgba(240,246,255,0.7) 0%, #FFFFFF 100%) !important;
//     background: -o-linear-gradient(180deg, rgba(240,246,255,0.7) 0%, #FFFFFF 100%) !important;
//     background: -moz-linear-gradient(180deg, rgba(240,246,255,0.7) 0%, #FFFFFF 100%) !important;
//     background: linear-gradient(180deg, rgba(240,246,255,0.7) 0%, #FFFFFF 100%) !important;
//     position: relative;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     flex-direction: column;
//     .fourthTitle {
//         position: absolute;
//         top: 20px;
//         left: 0px;
//     }
// }
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.loadingItem {
  & > span {
    width: 168px;
  }
  .protocol {
    width: 50px;
  }
  .port {
    width: 50px;
  }
}
.blueBlock {
  width: auto !important;
  box-sizing: border-box;
  height: 24px;
  line-height: 16px;
  font-size: 12px;
  padding: 4px 8px;
  text-align: center;
  color: #2677ff;
  background: rgba(38, 119, 255, 0.12);
}
</style>
