<template>
  <div class="headerContainer" :class="isExpand ? 'allExpand' : 'allFold'">
    <div class="header">
      <div class="left" v-if="isExpand">
        <div class="tu" :class="tuClassName" v-if="isOuterProcess"> </div>
        <div class="tuFinish" v-else> </div>
      </div>
      <div class="descriptionBox">
        <div class="top">
          <span class="companyName">{{ companyName }}</span>
          <span class="status">{{ outerStepStatusText }}</span>
          <div class="stepContainer">
            <div class="step" v-for="(item, index) in stepArr" :key="index">
              <div class="step_head" :class="`is-${updateStatus(index + 1)}`">
                <div class="step_line">
                  <i class="el-step__line-inner" :style="calcProgress(index + 2)"></i>
                </div>
                <el-tooltip effect="dark" placement="top" :open-delay="500" :content="item.title">
                  <div class="step_icon" v-if="updateStatus(index + 1) == 'success'">
                    <i class="el-step__icon-inner is-status el-icon-check"></i>
                  </div>
                  <div class="step_icon" v-else-if="updateStatus(index + 1) == 'process'">
                    <div class="step_icon_inner">{{ index + 1 }}</div>
                    <div class="step_icon_text">
                      {{ item.title }}
                    </div>
                  </div>
                  <div class="step_icon" v-else-if="updateStatus(index + 1) == 'wait'">
                    <div class="el-step__icon-inner">{{ index + 1 }}</div>
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="bottom" v-if="isExpand">
          <slot name="bottomText"></slot>
        </div>
      </div>
    </div>
    <div class="button">
      <slot name="buttonDiv"></slot>
    </div>
    <div
      class="progress"
      v-if="isOuterProcess"
      :class="{ start: progressCopy < 10, end: progressCopy > 90, finish: !isOuterProcess }"
    >
      <!-- <el-progress :percentage="90" :text-inside="true"></el-progress> -->
      <el-progress
        :percentage="progressCopy"
        :text-color="!isOuterProcess ? '#10D595' : '#2677FF'"
        :format="format"
        :text-inside="true"
      ></el-progress>
    </div>
    <div class="progress" v-else>
      <div class="progressFinish">
        <div class="progressFinish_innerText"> 已完成 </div>
      </div>
    </div>
    <div class="bottomBtn expand" v-if="isExpand" @click="exOrFo">
      <div class="btn">
        <img src="../../assets/images/headerFold.png" alt="" />
      </div>
    </div>
    <div class="bottomBtn fold" v-else @click="exOrFo">
      <div class="btn">
        <img src="../../assets/images/headerExpand.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  inject: ['getStepArr'],
  props: {
    tuClassName: {
      type: String,
      default: 'tuSecond'
    },
    progress: {
      type: [Number, String],
      default: 0
    },
    isOuterProcess: {
      type: Boolean,
      default: false
    },
    outerStepStatusText: {
      type: String,
      default: '测绘中'
    },
    companyName: {
      type: String,
      default: ''
    },
    currentStep: {
      type: Number,
      default: 1
    }
    // isExpand: {
    //   type: Boolean,
    //   default: true
    // }
  },
  computed: {
    stepArr() {
      return this.getStepArr()
    }
  },
  watch: {
    // taskStep: {
    //     handler(val) {
    //         this.taskStepCopy = val
    //     },
    //     immediate:true
    // },
    isOuterProcess: {
      handler(val) {
        if (!val) {
          this.progressCopy = 100
        } else {
          this.progressCopy = 0
        }
      },
      immediate: true
    },
    progress: {
      handler(val) {
        if (this.isOuterProcess) {
          this.progressCopy = val
        } else {
          this.progressCopy = 100
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      progressCopy: 0,
      isExpand: true,
      lineStyle: {},
      currentStepStatus: '',
      // stepArr: [
      //   {
      //     title: '输入企业名称',
      //   },
      //   {
      //     title: '资产线索获取',
      //   },
      //   {
      //     title: '云端资产推荐',
      //   },
      //   {
      //     title: '资产可信度评估',
      //   },
      // ],
      customColor: [
        // {color:'linear-gradient(90deg, #4EAFFF 0%, #2677FF 100%)',percentage: 99.99},
        // {color:'#05D4A7',percentage: 100}
      ]
    }
  },
  methods: {
    format(percentage) {
      return !this.isOuterProcess ? '已完成' : `${percentage}%`
    },
    exOrFo() {
      this.isExpand = !this.isExpand
    },
    updateStatus(val) {
      let currentStepStatus = ''
      if (val < this.currentStep) {
        currentStepStatus = 'success'
      } else if (val === this.currentStep) {
        currentStepStatus = 'process'
      } else {
        currentStepStatus = 'wait'
      }
      return currentStepStatus
    },
    calcProgress(stepNode) {
      let step = 100
      const style = {}
      style.transitionDelay = 150 * this.currentStep + 'ms'
      if (stepNode > this.currentStep) {
        step = 0
        style.transitionDelay = -150 * this.currentStep + 'ms'
      }
      style.borderWidth = step ? '1px' : 0
      style.width = step + '%'

      return style
    }
  }
}
</script>

<style lang="less" scoped>
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}

@keyframes running {
  0% {
    background-position-y: 0px;
  }

  100% {
    background-position-y: -17880px;
  }
}

.headerContainer {
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: 158px;
  border-radius: 4px 4px 0px 0px;
  &.allFold {
    transition: all 0.5s;
    height: 70px;
    background: linear-gradient(90deg, #eef5ff 0%, #ffffff 100%);
    // opacity: 0;
    box-shadow: inset 0px 0px 50px 0px rgba(15, 45, 104, 0.08);
    /deep/.el-progress-bar__innerText {
      display: none;
    }
    .progressFinish_innerText {
      display: none;
    }
  }
  &.allExpand {
    transition: all 0.5s;
    background:
      url('../../assets/images/unit/unitHeaderExpand.png') no-repeat right,
      linear-gradient(90deg, #eef5ff 0%, #ffffff 100%);
    background-size: auto 100%;
    // opacity: 1;
    box-shadow: inset 0px 0px 50px 0px rgba(15, 45, 104, 0.08);
    animation: vanishIn 2s;
  }
  @keyframes vanishIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  .header {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    // height: calc(100% - 24px);
    height: 100%;
    padding: 24px 24px 14px 20px;
    border: 2px solid #ffffff;
    border-bottom: none;
    .left {
      margin-right: 16px;
    }

    .descriptionBox {
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      // height: 100%;
      .top {
        display: flex;
        align-items: center;
        margin-bottom: 13px;

        span {
          margin-right: 5px;
        }

        .companyName {
          color: #2677ff;
          font-size: 18px;
          font-weight: 550;
        }
      }
    }
  }
  .button {
    position: absolute;
    top: 24px;
    right: 24px;
    color: #62666c;
    .headerBtn {
      color: #62666c;
      margin-left: 16px;
      &.el-button {
        padding: 0;
      }
      img {
        height: 14px;
      }
      &:hover {
        cursor: pointer;
        color: #2677ff;
      }
    }
  }
  .progress {
    position: absolute;
    bottom: 0;
    // width: calc(100% - 32px);
    width: 100%;
    height: 4px;
    .progressFinish {
      position: relative;
      height: 4px;
      background: #10d595 !important;
      .progressFinish_innerText {
        position: absolute;
        right: 5px;
        top: -28px;
        width: 58px;
        height: 24px;
        color: #10d595;
        line-height: 24px;
        font-weight: 600;
        text-align: center;
        border-radius: 2px;
        background-color: rgba(255, 255, 255, 0.7);
        z-index: 999;
      }
    }

    /deep/.el-progress {
      height: 4px;
      line-height: 4px;
      .el-progress-bar {
        height: 4px;
        display: unset;
        .el-progress-bar__outer {
          height: 4px !important;
          overflow: unset;
          border-radius: 0;
          background-color: rgba(186, 196, 217, 0.5) !important;
          .el-progress-bar__inner {
            border-radius: 0;
            background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);

            .el-progress-bar__innerText {
              position: absolute;
              bottom: 8px;
              right: 0;
              left: auto;
              z-index: 999;
              transform: translateX(50%);
              line-height: 20px;
              font-size: 14px;
              font-weight: 600;
              // color: #2677FF !important;
              width: 58px;
              height: 24px;
              line-height: 24px;
              background: rgba(255, 255, 255, 0.7);
              text-align: center;
              border-radius: 2px;
              box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
            }
          }
        }
      }
    }
    &.start {
      /deep/ .el-progress-bar__innerText {
        left: 0 !important;
        right: auto !important;
        transform: translateX(0) !important;
      }
    }
    &.end {
      /deep/ .el-progress-bar__innerText {
        right: 0 !important;
        left: auto !important;
        transform: translateX(0) !important;
      }
    }
    &.finish {
      /deep/.el-progress-bar__inner {
        background: #10d595 !important;

        //.el-progress-bar__innerText {
        // color: #10D595 !important;
        //}
      }
    }
  }
  .bottomBtn {
    position: absolute;
    left: 50%;
    bottom: -13px;
    width: 28px;
    height: 13px;
    background: #e6edfa;
    border-radius: 0 0 4px 4px;
    z-index: 2000;
    animation: vanishIn 0.1s;
    &:hover {
      cursor: pointer;
    }
    .btn {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    img {
      line-height: 13px;
      text-align: center;
      width: 12px;
      height: 6px;
    }
  }
  // .bgi {
  //   position: absolute;
  //   top: 0;
  //   right: 0;
  //   height: 100%;
  //   background-color: transparent;
  //   z-index: 22;
  // }
}

.stepContainer {
  display: flex;
  white-space: nowrap;

  .step {
    // flex-basis: 200px;
    &:last-child {
      max-width: 20px;

      .step_head {
        padding-right: 0;
      }
    }

    .step_head {
      position: relative;
      width: 100%;
      display: inline-block;
      padding-right: 8px;

      .step_line {
        margin-right: 0px;
        position: absolute;
        border-color: inherit;
        background-color: rgba(38, 119, 255, 0.2);
        height: 2px;
        top: 9px;
        left: 0;
        right: 0;
      }

      .step_icon {
        position: relative;
        z-index: 1;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 20px;
        height: 20px;
        font-size: 12px;
        box-sizing: border-box;

        transition: 0.15s ease-out;
        border-radius: 50%;
        border-color: inherit;

        .el-step__icon-inner {
          width: 100%;
          font-weight: 400;

          // color: #fff;
          &.is-status {
            transform: translateY(1px);
          }
        }
      }

      &.is-success {
        color: #ffffff;
        border-color: #2677ff;

        .step_icon {
          background: #2677ff;
        }
      }

      &.is-process {
        color: #2677ff;

        .step_icon {
          width: auto;
          border-radius: 12px;
          justify-content: left;
          background: #dde9ff;
          border: 1px solid;
          border-color: #2677ff;
          overflow: hidden;

          .step_icon_inner {
            width: 20px;
            height: 20px;
            border-radius: 12px;
            background-color: #fff;
            box-shadow: 2px 0px 2px 0px rgba(0, 26, 70, 0.12);
            text-align: center;
            line-height: 20px;
          }

          .step_icon_text {
            padding: 0 8px 0 4px;
          }
        }
      }

      &.is-wait {
        color: #2677ff;

        .step_icon {
          background: #dde9ff;
        }
      }
    }
  }
}

.tuFinish {
  width: 120px !important;
  height: 120px !important;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position-y: 0px;
  background-image: url('../../assets/images/tuFinish.png');
}
</style>
