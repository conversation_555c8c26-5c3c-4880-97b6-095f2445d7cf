<template>
  <div class="list">
    <div class="items" v-for="item in list" :key="item.id">
      <slot :data="item" name="item"></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      loading: false
    }
  },
  watch: {
    list: {
      handler(newVal) {
        if (newVal.length == 0) {
          this.loading = true
        } else {
          this.loading = false
        }
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="less" scoped>
.list {
  padding-top: 54px;
  padding-bottom: 20px;
}
.items {
  box-sizing: border-box;
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  margin-bottom: 12px;
  background: linear-gradient(270deg, #f3faff 0%, #ffffff 59%, #edf4ff 100%);
  border: 2px solid #ffffff;
  border-radius: 4px;
  box-shadow:
    0px 2px 4px 0px rgba(0, 0, 0, 0.08),
    inset 0px 0px 20px 0px rgba(0, 78, 208, 0.08);
}
</style>
