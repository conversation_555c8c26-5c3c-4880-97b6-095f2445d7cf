<template>
  <div class="taskBox">
    <taskHeader
      :isExpand="isHeaderEx"
      :companyName="taskInfoData.name"
      :isOuterProcess="expandTypeHead || progressBar"
      :progress="currentPercent"
      :currentStep="2"
    >
      <template #bottomText>
        <div class="progressContent" v-if="!expandTypeHead">
          <template v-if="progressBar && accuracyIndex == 0">
            <span
              >正在获取<template v-if="companyNameLoad"
                ><span class="companyNameLoad">{{ companyNameLoad }}</span
                >企业</template
              >初始线索中...</span
            >
          </template>
          <template v-else>
            <img src="../../assets/images/taskSed.png" alt="" />
            <span>{{ stepInfoArr[accuracyIndex].clueText }}：</span>
            <span style="color: #2677ff">{{ tabTotal }}</span
            >条，
            <span
              >IP段：<span style="color: #2677ff">{{ ip_num }}</span
              >条，</span
            >
            <span
              >域名：<span style="color: #2677ff">{{ domain_num }}</span
              >条，</span
            >
            <span
              >ICP：<span style="color: #2677ff">{{ icp_num }}</span
              >条，</span
            >
            <span
              >证书：<span style="color: #2677ff">{{ cert_num }}</span
              >条，</span
            >
            <span
              >ICON：<span style="color: #2677ff">{{ icon_num }}</span
              >条</span
            >
          </template>
        </div>
        <div class="progressContent" v-if="expandTypeHead">
          {{ headText }}
        </div>
      </template>
      <template #buttonDiv>
        <el-button
          class="headerBtn"
          type="text"
          v-if="!expandTypeHead && againIsTrue"
          @click="againGetClueFun"
        >
          <svg class="icon svg-icon icon-repeat" aria-hidden="true">
            <use xlink:href="#icon-repeat"></use>
          </svg>
          重新获取
        </el-button>
        <el-button
          :disabled="isEndingLoading"
          class="headerBtn"
          @click="ending"
          id="unit_end"
          type="text"
          v-if="!expandTypeHead"
        >
          <svg class="icon svg-icon icon-close" aria-hidden="true">
            <use xlink:href="#icon-close"></use>
          </svg>
          结束流程
        </el-button>
      </template>
    </taskHeader>
    <div class="myBox" v-if="!expandTypeHead">
      <div :class="progressBar ? 'boxTwo' : 'boxTwo1'">
        <div class="eltableBox">
          <ul class="ulBox">
            <li class="ultitle">线索分类</li>
            <li
              v-for="(item, index) in tabList"
              :key="index"
              @click="changeTab(item.label)"
              class="clueList"
              :style="
                tabActive == item.label ? 'background: #EAEFF6;color:#2677FF;font-weight: 500;' : ''
              "
            >
              <span class="fangkuai" v-if="item.label == 0"></span>
              <span class="fangkuai fangkuai1" v-else-if="item.label == 1"></span>
              <span class="fangkuai fangkuai2" v-else-if="item.label == 2"></span>
              <span class="fangkuai fangkuai4" v-else-if="item.label == 6"></span>
              <span class="fangkuai fangkuai3" v-else></span>
              {{ getType(item.name) }}
            </li>
          </ul>
          <div class="myTable" v-loading="loading">
            <div class="tableLabel">
              <div>
                <div>{{ stepInfoArr[accuracyIndex].tableText }}</div>
                <div style="color: rgba(98, 102, 108, 0.7); margin-left: 8px; font-size: 12px">{{
                  tableName
                }}</div>
                <el-input
                  v-if="accuracyIndex !== 0"
                  style="margin-left: 10px"
                  v-model="keyword"
                  placeholder="请输入线索源检索"
                  id="user_keycheck"
                >
                  <el-button slot="append" icon="el-icon-search"></el-button>
                </el-input>
              </div>
              <div>
                <el-button
                  class="normalBtnRe"
                  :loading="removeLoading"
                  type="primary"
                  @click="removeFun"
                  id="unit_del"
                  >忽略</el-button
                >
                <el-button
                  class="normalBtnRe"
                  type="primary"
                  v-if="user.role == 2"
                  @click="editCompanyName('more')"
                  >批量编辑</el-button
                >
                <el-button
                  class="normalBtnRe"
                  type="primary"
                  :disabled="progressBar"
                  @click="unclaimChecked"
                  >一键操作疑似线索</el-button
                >
                <el-button
                  class="normalBtnRe"
                  type="primary"
                  v-if="
                    accuracyIndex == 1 ||
                    accuracyIndex == 2 ||
                    accuracyIndex == 3 ||
                    accuracyIndex == 4
                  "
                  @click.native="clueConfirmSave(3)"
                  id="cloud_recommend"
                  >供应链标记</el-button
                >
                <!-- <el-button v-if="accuracyIndex == 3" class="normalBtn" type="primary" :loading="btnLoading" @click.native="clueConfirmSave(4)">ICP盗用标记</el-button> -->
                <el-button
                  class="normalBtnRe"
                  v-if="accuracyIndex == 5"
                  :loading="exportLoading"
                  type="primary"
                  @click="exportList"
                  id="unit_del"
                  >导出</el-button
                >
                <el-button
                  class="normalBtnRe"
                  v-if="accuracyIndex == 5 && user.role == 2"
                  :loading="noteLoading"
                  type="primary"
                  @click="noteBlackList"
                  id="unit_note"
                  >标记到黑名单</el-button
                >
                <el-button
                  class="normalBtn"
                  :disabled="progressBar"
                  v-if="accuracyIndex >= 0"
                  type="primary"
                  @click="insertClueShow"
                  id="unit_del"
                  >新增</el-button
                >
              </div>
            </div>
            <div class="myTableContent" ref="myTableContent">
              <clueTable
                ref="clueTable"
                @editCompanyName="editCompanyName"
                v-loading="tableLoading"
                @scrollChangeTab="scrollChangeTab"
                :handleIsShow="true"
                :onekey="onekey"
                @checkedArr="checkedArrFun"
                :tableData="tableData"
                :tableHeader="tableHeader"
                :progressBar="progressBar"
              ></clueTable>
            </div>
          </div>
        </div>
        <div class="footer">
          <el-button
            class="normalBtn"
            type="primary"
            @click="expanding"
            id="unit_clue"
            :disabled="progressBar || constraintRestrictions"
            v-if="accuracyIndex == 0"
            >扩展线索</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            @click="expandingTwo"
            id="unit_clue"
            :disabled="progressBar || constraintRestrictions"
            v-if="accuracyIndex == 1"
            >通过域名扩展</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            @click="expandingTwo"
            id="unit_clue"
            :disabled="progressBar || constraintRestrictions"
            v-if="accuracyIndex == 2"
            >通过ICP扩展</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            @click="expandingTwo"
            id="unit_clue"
            :disabled="progressBar || constraintRestrictions"
            v-if="accuracyIndex == 3"
            >通过证书扩展</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            @click="expandingTwo"
            id="unit_clue"
            :disabled="progressBar || constraintRestrictions"
            v-if="accuracyIndex == 4"
            >生成线索总表</el-button
          >
          <el-button
            class="normalBtn"
            type="primary"
            @click="expandingTwo"
            id="unit_clue"
            :disabled="progressBar || constraintRestrictions"
            v-if="accuracyIndex == 5"
            >云端资产推荐</el-button
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="myReturn"
            id="unit_return"
            :style="{ cursor: constraintRestrictions ? 'not-allowed' : 'pointer' }"
            :disabled="progressBar"
            v-if="accuracyIndex != 0 && accuracyIndex < 6"
            >返回</el-button
          >
        </div>
      </div>
      <el-dialog
        class="elDialogAdd elDialogAdd1"
        :close-on-click-modal="false"
        :visible.sync="clueExpansion"
        width="680px"
      >
        <template slot="title"> 扩展模式 </template>
        <div class="dialog-body">
          <div>
            <!-- <el-radio-group v-model="expandType">
                        <el-radio label="3" id="unit_clue_head">智能模式</el-radio>
                        <el-radio label="1" id="unit_clue_speed">标准模式</el-radio>
                        <el-radio label="2" id="unit_clue_depth">专家模式</el-radio>
                    </el-radio-group> -->
            当前已设置参数 {{ expandTypeMap[expandType] }}
            <div v-if="expandType == 1">
              <p class="modelShow" style="margin-top: 20px"
                >标准模式：通过标准化引导调用内置算法进行线索扩展和资产识别，从而确保识别和扩展的资产和线索准确、完整和可靠</p
              >
              <div class="cardClass">
                <div class="cardWrap" v-for="(item, index) in cardContent[0]" :key="index">
                  <el-card :body-style="{ padding: '0px' }">
                    <p class="title">{{ item.title }}</p>
                    <img :src="item.pic" class="imgBot" />
                    <p class="content">{{ item.desc }}</p>
                  </el-card>
                </div>
              </div>
            </div>
            <div v-if="expandType == 2">
              <p class="modelShow" style="margin-top: 10px"
                >专家模式：引入资产梳理专家可以帮助快速精准的获取资产数据，并且可以有效的扩展线索</p
              >
              <div class="cardClass">
                <div class="cardWrap" v-for="(item, index) in cardContent[1]" :key="index">
                  <el-card :body-style="{ padding: '0px' }">
                    <p class="title">{{ item.title }}</p>
                    <img :src="item.pic" class="imgBot" />
                    <p class="content">{{ item.desc }}</p>
                  </el-card>
                </div>
              </div>
            </div>
            <div v-if="expandType == 3">
              <p class="modelShow" style="margin: 10px 0 16px 0"
                >智能模式：通过内置自动化线索扩展和资产识别算法，快速、高效的识别和管理主体单位暴露面资产，并基于知识图谱进行线索呈现</p
              >
              <div class="cardClass">
                <div class="cardWrap" v-for="(item, index) in cardContent[2]" :key="index">
                  <el-card :body-style="{ padding: '0px' }">
                    <p class="title">{{ item.title }}</p>
                    <img :src="item.pic" class="imgBot" />
                    <p class="content">{{ item.desc }}</p>
                  </el-card>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button class="highBtnRe" @click="clueExpansion = false" id="unit_clue_sure"
            >关闭</el-button
          >
          <!-- 极速，深度 -->
          <el-button
            v-if="expandType != 3"
            class="highBtn"
            @click="clueExpansioning"
            id="unit_clue_cancel"
            >确定</el-button
          >
          <!-- 智能模式 -->
          <el-button v-else class="highBtn" @click="myExpandingAuto" id="unit_clue_cancel"
            >确定</el-button
          >
        </div>
      </el-dialog>
      <!-- 新建线索 -->
      <clueDialog
        :dialogFormVisibleInsert="dialogFormVisibleInsert"
        :is_detect="{ status: accuracyIndex }"
        :currentGroupId="taskInfoData.group_id"
        :tabActiveName="tabActiveName"
        :company_name="taskInfoData.name"
        :ruleForm="clueRuleForm"
        @insertClueSave="insertClueSave"
      />
      <suspectedCluesOperation
        ref="suspectedCluesOperation"
        :tabList="tabList"
        @removeOne="removeOne"
        @removeMore="removeMore"
        :user="user"
        :tableHeader="tableHeader"
        :tableData="tableData"
        :dialogVisible="suspectedClueVisible"
        @closeDialog="closeSuspectDialog"
      />
    </div>
    <div class="fourthBox" v-else>
      <div class="loading" :class="{ scroll: isLoadingBgc }" v-if="!taskLoading">
        已成功扩展出下列线索
      </div>
      <div ref="fourthBoxContainer" class="fourthBoxContainer" v-loading="taskLoading">
        <taskLoading :list="list">
          <template v-slot:item="{ data }">
            <div class="loadingItem">
              <span class="blueBlock">{{ tabListMap[data.type] }}</span>
              <span v-if="data.type != 3">{{ data.content }}</span>
              <span class="img" v-if="data.type == 3">
                <img
                  :src="data.content.includes('http') ? data.content : showSrcIp + data.content"
                  alt=""
                  style="width: 23px; height: 23px"
                />
              </span>
              <span>{{ data.clue_company_name }}</span>
              <span v-if="getSourcetype(data.chain_list)"
                >线索源： {{ getSource(data.chain_list, data.parent_id, data.source) }}</span
              >
            </div>
          </template>
        </taskLoading>
      </div>
    </div>
  </div>
</template>
<script>
import taskLoading from './taskLoading.vue'
import taskHeader from './taskHeader.vue'
import * as animationData from '../../assets/images/data.json'
import Lottie from 'vue-lottie/src/lottie.vue'
// import tableTooltip from "../../components/tableTooltip/tableTooltip.vue";
import clueTable from './taskSecondTable.vue'
import clueDialog from '../cloudRecommend/clueInsertDialog.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import suspectedCluesOperation from './suspectedCluesOperation'
import {
  exportCluesListV1,
  noteClueBlack,
  goPointClue,
  passClueV1,
  tabNumClues,
  fakeClue
} from '@/api/apiConfig/clue.js'
import {
  fakeClueIsThreat,
  cehuiReturn,
  generateClueAll,
  againGetClue,
  Expanding,
  getCluelist,
  endDetectTask,
  detectTaskInfo,
  cloudAssets,
  cluesUpdateSync
} from '@/api/apiConfig/surveying.js'

export default {
  components: { Lottie, clueDialog, clueTable, suspectedCluesOperation, taskHeader, taskLoading },
  props: ['taskInfoData'],
  data() {
    return {
      tableInterval: 30 * 1000,
      isEndingLoading: false,
      interval: 30 * 1000,
      keyword: '',
      companyNameLoad: '',
      isLoadingBgc: true,
      tableData_domain: [],
      tableData_icp: [],
      tableData_cert: [],
      tableData_ip: [],
      isHeaderEx: true, //header  true 展开 false 收起
      loadingTimer: null,
      list: [],
      suspectedTableData: [],
      suspectedClueVisible: false,
      noteLoading: false,
      onekey: false,
      againIsShow: false,
      btnLoading: false,
      currentPercent: 0, //进度条
      defaultOptions: {
        //json动画
        animationData: animationData.default
      },
      dialogFormVisibleInsert: false,
      tabActiveName: '0',
      againIsTrue: true, // 重新获取的按钮
      clueRuleForm: {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      },
      cardContent: [
        [
          // 标准模式
          {
            desc: '可获取主体单位90%以上的互联网暴露面资产',
            title: '全面性',
            pic: require('../../assets/images/clue_qm.png')
          },
          {
            desc: '通过内置算法+标准引导操作，资产暴露面精准度可达到90%以上',
            title: '精准性',
            pic: require('../../assets/images/clue_jz.png')
          },
          {
            desc: '有一定的资产运营经验，但是刚开始涉足互联网暴露面或者攻击面领域',
            title: '适用人群',
            pic: require('../../assets/images/clue_rq.png')
          },
          {
            desc: '定期资产互联网暴露面或者攻击面盘查场景',
            title: '使用场景',
            pic: require('../../assets/images/clue_cj.png')
          }
        ],
        [
          // 专家模式
          {
            desc: '可获取主体单位95%以上的互联网暴露面资产',
            title: '全面性',
            pic: require('../../assets/images/clue_qm.png')
          },
          {
            desc: '通过内置算法+专家介入，资产暴露面精准度可达到98%以上',
            title: '精准性',
            pic: require('../../assets/images/clue_jz.png')
          },
          {
            desc: '有一定的资产梳理经验，可配合使用第三方技术和工具，来发现隐藏的或未知的资产',
            title: '适用人群',
            pic: require('../../assets/images/clue_rq.png')
          },
          {
            desc: '全量资产盘点需求，且有应对监管需求的场景',
            title: '使用场景',
            pic: require('../../assets/images/clue_cj.png')
          }
        ],
        [
          // 智能模式
          {
            desc: '可获取主体单位80%以上的互联网暴露面资产',
            title: '全面性',
            pic: require('../../assets/images/clue_qm.png')
          },
          {
            desc: '通过内置算法，资产暴露面精准度可达到85%以上',
            title: '精准性',
            pic: require('../../assets/images/clue_jz.png')
          },
          {
            desc: '第一次接触互联网暴露面或者对资产运营需求不深',
            title: '适用人群',
            pic: require('../../assets/images/clue_rq.png')
          },
          {
            desc: '需要快速探测一定数量级互联网暴露面资产，且对暴露面资产全面性关注不高的场景',
            title: '使用场景',
            pic: require('../../assets/images/clue_cj.png')
          }
        ]
      ],
      progressBar: true, //是否显示进度条
      constraintRestrictions: false, //是否显示约束限制
      tabTotal: 0,
      total: 0,
      currentPage: 1,
      pageSize: 20,
      pageSizeArr: [10, 30, 50, 100],
      domain_num: 0,
      ip_num: 0,
      icp_num: 0,
      cert_num: 0,
      icon_num: 0,
      tableName: '可手动忽略非本企业的线索内容',
      tabActive: '6', // 左侧导航默认选中IP段：6
      tabList: [
        {
          label: '6',
          name: 'IP段'
        },
        {
          label: '0',
          name: '域名'
        },
        {
          label: '2',
          name: 'ICP'
        },
        {
          label: '1',
          name: '证书'
        },
        {
          label: '3',
          name: 'ICON'
        }
      ],
      tabListMap: {
        0: '域名',
        1: '证书',
        2: 'ICP',
        3: 'ICON',
        6: 'IP段',
        4: '关键词',
        5: '子域名'
      },
      tableLoading: false,
      tableData: [],
      tableDataOrigin: [],
      tableArr: [],
      tableHeader: [
        {
          label: '线索',
          name: 'content',
          fixed: 'left'
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          minWidth: '150px'
        }
      ],
      clueExpansion: false, //扩展线索框
      expandType: '2', //线索扩展类型，1、2、3：标准，专家，智能
      accuracyIndex: 0,
      stepInfoArr: [
        {
          clueSecondTitle: '初始线索',
          clueText: '成功获取企业初始线索',
          clueTitle: '依据企业名称获取单位资产初始线索',
          tableText: '初始线索'
        },
        {
          clueSecondTitle: '依据IP段扩展',
          clueText: '成功获取企业初始线索',
          clueTitle:
            '通过原始线索中的IP段进行线索扩展，扩展线索已自动补充至线索库，可通过域名继续扩展',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '依据域名扩展',
          clueText: '成功获取企业初始线索',
          clueTitle: '通过域名线索扩展，扩展线索已自动补充至线索库，可通过ICP继续扩展',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '依据ICP扩展',
          clueText: '成功获取企业初始线索',
          clueTitle: '通过ICP线索扩展，扩展线索已自动补充至线索库，可通过证书继续扩展',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '依据证书扩展',
          clueText: '成功获取企业初始线索',
          clueTitle: '通过证书线索扩展，扩展线索已自动补充至线索库，扩展完成后生成线索库',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '生成线索总表',
          clueText: '共生成资产线索',
          clueTitle: '依据原始线索和扩展线索生成线索总表，可依据线索总表进行资产推荐',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '线索扩展',
          clueText: '共生成资产线索',
          clueTitle: '依据原始线索扩展其他线索，可生成线索总表',
          tableText: '资产线索'
        }
      ],
      task_id: '',
      group_id: '',
      user: {
        role: ''
      },
      userInfo: '',
      checkedArr0: [],
      checkedArr1: [],
      checkedArr2: [],
      checkedArr3: [],
      checkedArr4: [],
      checkedArr5: [],
      checkedArr6: [],
      tabActiveNameStatus: 1,
      setTimer: null,
      loading: false,
      removeLoading: false,
      exportLoading: false,
      expandTypeHead: false,
      headText: '线索扩展',
      expandTypeMap: {
        3: '智能模式',
        1: '标准模式',
        2: '专家模式'
      }
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      if (this.user.role == 2) {
        // this.$refs['eltable'].clearSelection();
        this.tableData = []
        this.tableArr = []
        sessionStorage.removeItem('scrollTop')
      }
    },
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    taskInfoData(val) {
      this.getInit()
    },
    // 通过哪个分类扩展，左侧就不用显示哪个分类的导航
    accuracyIndex(val) {
      if (val == 0) {
        //初始线索
        this.tabActiveNameStatus = 1
      } else if (val == 5) {
        //资产推荐
        this.tabActiveNameStatus = 1
      } else {
        //扩展线索
        this.tabActiveNameStatus = 0
      }
      if (this.expandType == '1' || this.expandType == '3') {
        // 标准模式、智能模式
        this.tabActive = '6'
      } else {
        // 专家模式
        if (val == 1) {
          // IP段
          this.tabActive = '0'
        } else {
          // 其他
          this.tabActive = '6'
        }
      }
    },
    list: {
      handler(newVal) {
        if (newVal.length == 0) {
          this.taskLoading = true
        } else {
          this.taskLoading = false
          this.$nextTick(() => {
            let dom = document.getElementsByClassName('fourthBoxContainer')[0]
            let innerDom = document.getElementsByClassName('list')[0]
            if (dom && innerDom && innerDom.clientHeight > dom.clientHeight) {
              // 出现滚动条
              this.isLoadingBgc = true // 显示模糊背景
              dom.scrollTop = dom.scrollHeight // 自动滚动到底部
            } else {
              this.isLoadingBgc = false
            }
          })
        }
      },
      deep: true,
      immediate: true
    },
    keyword: {
      handler(newValOrigin) {
        let newVal = newValOrigin.trim()
        if (
          newVal &&
          (this.checkedArr0.length != 0 ||
            this.checkedArr1.length != 0 ||
            this.checkedArr2.length != 0 ||
            this.checkedArr3.length != 0 ||
            this.checkedArr4.length != 0 ||
            this.checkedArr5.length != 0 ||
            this.checkedArr6.length != 0)
        ) {
          this.keyword = ''
          this.$message.error('请取消所有勾选后再进行搜索!')
          return
        }
        // 筛选线索源
        if (!newVal) {
          this.tableData = this.tableDataOrigin
        } else if (newVal == '初始线索') {
          this.tableData = this.tableDataOrigin.filter((item) => {
            return (
              (!item.chain_list || (item.chain_list && item.chain_list.length == 0)) &&
              item.parent_id == 0 &&
              (item.source == 0 || item.source == 4)
            )
          })
        } else if (newVal == '扩展线索') {
          this.tableData = this.tableDataOrigin.filter((item) => {
            return (
              (!item.chain_list || (item.chain_list && item.chain_list.length == 0)) &&
              item.parent_id == 0 &&
              item.source == 2
            )
          })
        } else {
          this.tableData = this.tableDataOrigin.filter((item) => {
            if (item.chain_list && item.chain_list.length != 0) {
              if (
                item.chain_list.length >= 2 &&
                item.chain_list[item.chain_list.length - 2].content &&
                item.chain_list[item.chain_list.length - 2].content.indexOf(newVal) !== -1
              ) {
                return true
              } else {
                return (
                  item.chain_list[0].content && item.chain_list[0].content.indexOf(newVal) !== -1
                )
              }
            }
          })
        }
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2) {
      // 安服账号切换企业需要回到一级页面
      if (!this.currentCompany) return
      this.getInit()
    } else {
      this.getInit()
    }

    sessionStorage.removeItem('scrollTop')
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    getSourcetype(val) {
      //线索来源类型
      if (val.length != 0) {
        if (val.length >= 2) {
          if (val[val.length - 2].type != 3) {
            return true
          } else {
            return false
          }
        } else {
          if (val[0].type != 3) {
            return true
          } else {
            return false
          }
        }
      } else {
        return true
      }
    },
    getSource(val, parent_id, source) {
      //线索来源
      if (val.length != 0) {
        if (val.length >= 2) {
          return val[val.length - 2].content
        } else {
          return val[0].content
        }
      } else {
        // 初始线索：0、3、4、5，扩展线索：2
        if (parent_id == 0 && source == 0) {
          return '初始线索'
        } else if (parent_id == 0 && source == 2) {
          return '扩展线索'
        } else if (parent_id == 0 && source == 4) {
          return '初始线索'
        }
      }
    },
    closeSuspectDialog() {
      this.suspectedClueVisible = false
    },
    async noteBlackList() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要标记的数据！')
        return
      }
      let obj = this.__transferChecked()
      this.$confirm('此操作将把所勾选的数据标记为黑名单，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await noteClueBlack(obj).catch(() => {
          this.noteLoading = false
        })
        this.noteLoading = false
        if (res.code == 0) {
          this.$message.success('标记成功')
          this.checkedArr0 = []
          this.checkedArr1 = []
          this.checkedArr2 = []
          this.checkedArr3 = []
          this.checkedArr4 = []
          this.checkedArr5 = []
          this.checkedArr6 = []
          this.getTable(this.tabActiveNameStatus)
          this.getTab(this.tabActiveNameStatus)
        }
      })
    },
    // 批量以及单独修改企业名称
    editCompanyName(type, id) {
      let clueData = null
      if (type == 'more') {
        clueData = [
          ...this.checkedArr0,
          ...this.checkedArr1,
          ...this.checkedArr2,
          ...this.checkedArr3,
          ...this.checkedArr4,
          ...this.checkedArr5,
          ...this.checkedArr6
        ]
        if (clueData.length == 0) {
          this.$message.error('请选择要编辑的数据！')
          return
        }
      } else {
        clueData = [id]
      }
      this.$prompt('请输入新的企业名称', `${type == 'more' ? '批量' : ''}编辑企业名称`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'cloud_info_del_sure',
        cancelButtonClass: 'cloud_info_del_cancel',
        inputValidator: (value) => {
          if (value) {
            return true
          } else {
            return false
          }
        },
        inputErrorMessage: '请输入',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let res = await cluesUpdateSync({
              operate_company_id: this.currentCompany,
              ids: clueData,
              clue_company_name: instance.inputValue.trim()
            })
            if (res.code == 0) {
              done()
              this.checkedArr0 = []
              this.checkedArr1 = []
              this.checkedArr2 = []
              this.checkedArr3 = []
              this.checkedArr4 = []
              this.checkedArr5 = []
              this.checkedArr6 = []
              this.getTable(this.tabActiveNameStatus)
              this.getTab(this.tabActiveNameStatus)
            }
            setTimeout(() => {
              instance.confirmButtonLoading = false
            }, 300)
          } else {
            done()
          }
        }
      }).then(({ value }) => {
        this.$message({
          type: 'success',
          message: '修改成功'
        })
      })
    },
    // 滚动改变tab线索分类选中
    scrollChangeTab(type) {
      this.tabActive = String(type)
    },
    //切换线索分类
    changeTab(val) {
      var arr = this.tableData.map((item) => {
        return item.type
      })
      var num = arr.indexOf(Number(val))
      if (num != -1) {
        // 获取行高
        let dataList = document.getElementsByClassName('borderWrap')[0].clientHeight
        // 滚动距离
        document.getElementsByClassName('clueContent')[0].scrollTop = dataList * num
        setTimeout(() => {
          this.tabActive = val
        }, 100)
      }
    },
    getType(val) {
      //tabActive
      if (val == '域名') {
        return `域名(${this.domain_num})`
      } else if (val == 'ICP') {
        return `ICP(${this.icp_num})`
      } else if (val == '证书') {
        return `证书(${this.cert_num})`
      } else if (val == 'ICON') {
        return `ICON(${this.icon_num})`
      } else {
        return `IP段(${this.ip_num})`
      }
    },
    filterSuspectData() {
      this.suspectedTableData = this.tableData.filter((item) => item.is_highlight)
    },
    // 疑似资产一键选中
    unclaimChecked() {
      this.filterSuspectData()
      // this.onekey = this.onekey ? false : true
      if (this.suspectedTableData.length == 0) {
        this.$message.warning('暂无疑似线索可操作')
        return
      }
      this.suspectedClueVisible = true
    },
    // 处置
    async clueConfirmSave(icon) {
      if (this.tabNumStatus <= 0) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let clueData = [
        {
          id: this.checkedArr0,
          type: '0',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr1,
          type: '1',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr2,
          type: '2',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr3,
          type: '3',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr4,
          type: '4',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr5,
          type: '5',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr6,
          type: '6',
          is_all: '',
          keyword: ''
        }
      ]
      let actionFun = null
      let obj = null
      if (icon == 3) {
        // 供应链标记
        obj = {
          tab_status: this.tabActiveNameStatus,
          is_supply_chain: 1, // 0取消供应链 1设置为供应链
          group_id: this.taskInfoData.group_id,
          data: clueData,
          keyword: {
            domain_keyword: '',
            cert_keyword: '',
            icp_keyword: '',
            key_keyword: '',
            ip_keyword: ''
          },
          operate_company_id: this.currentCompany
        }
        actionFun = goPointClue
      } else if (icon == 4) {
        // ICP盗用标记
        obj = {
          tab_status: this.tabActiveNameStatus,
          group_id: this.taskInfoData.group_id,
          is_fake_icp: 1,
          data: clueData,
          keyword: {
            domain_keyword: '',
            cert_keyword: '',
            icp_keyword: '',
            key_keyword: '',
            ip_keyword: ''
          },
          operate_company_id: this.currentCompany
        }
        actionFun = fakeClue
      }
      this.currentGroupIds = this.currentGroupId
      this.btnLoading = true
      let res = await actionFun(obj).catch(() => {
        this.btnLoading = false
      })
      this.btnLoading = false
      if (res.code == 0) {
        if (icon == 4) {
          // icp盗用
          let obj = {
            id: [
              ...this.checkedArr0,
              ...this.checkedArr1,
              ...this.checkedArr2,
              ...this.checkedArr3,
              ...this.checkedArr4,
              ...this.checkedArr5,
              ...this.checkedArr6
            ],
            detect_task_id: this.taskInfoData.id,
            use_fake_clue: 1, // 是否使用icp盗用的线索推荐资产 1/是 0/不是
            operate_company_id: this.currentCompany
          }
          this.$confirm('标记完成线索是否支持推荐威胁资产?', '提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            cancelButtonClass: 'unit_del_cancel',
            confirmButtonClass: 'unit_del_sure',
            customClass: 'unit_del',
            closeOnClickModal: false,
            closeOnPressEscape: false,
            distinguishCancelAndClose: true,
            type: 'warning'
          })
            .then(async () => {
              obj.use_fake_clue = 1
              let res = await fakeClueIsThreat(obj)
              if (res.code == 0) {
                this.$message.success('操作成功！')
                this.getTable(0)
                this.getTab(0)
              }
            })
            .catch(async (action) => {
              if (action == 'cancel') {
                // 取消事件（否），关闭close时不执行
                obj.use_fake_clue = 0
                let res = await fakeClueIsThreat(obj)
                if (res.code == 0) {
                  this.$message.success('操作成功！')
                  this.getTable(0)
                  this.getTab(0)
                }
              }
            })
        } else {
          this.$message.success('操作成功！')
          this.getTable(0)
          this.getTab(0)
        }
      }
    },
    insertClueShow() {
      this.clueRuleForm = {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: ''
      }
      this.dialogFormVisibleInsert = true
      if (this.tabActiveName == 3) {
        this.clueRuleForm.content = []
      } else {
        this.clueRuleForm.content = ''
      }
      if (this.tabActiveName == '3') {
        this.$set(this.clueRuleForm, 'way', 1)
      } else {
        this.$set(this.clueRuleForm, 'way', 0)
      }
      this.fileList = []
    },
    // 线索保存
    insertClueSave(isClose) {
      this.dialogFormVisibleInsert = false
      this.clueRuleForm = {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      }
      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []
      if (!isClose) {
        // 关闭弹层不需要执行
        if (this.accuracyIndex == 0 || this.accuracyIndex == 5) {
          // 初始线索获取，生成线索总表status:1
          this.getTable(1)
          this.getTab(1)
        } else {
          // 其余情况0
          this.getTable(0)
          this.getTab(0)
        }
      }
    },
    __transferChecked() {
      // 导出以及标记黑名单的数据结构转换
      let clueData = [
        {
          id: this.checkedArr0,
          type: '0',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr1,
          type: '1',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr2,
          type: '2',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr3,
          type: '3',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr4,
          type: '4',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr5,
          type: '5',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr6,
          type: '6',
          is_all: '',
          keyword: ''
        }
      ]
      let obj = {
        tab_status: this.tabActiveNameStatus,
        group_id: this.taskInfoData.group_id,
        data: clueData,
        keyword: {
          domain_keyword: '',
          cert_keyword: '',
          icp_keyword: '',
          key_keyword: '',
          ip_keyword: ''
        },
        operate_company_id: this.currentCompany
      }
      return obj
    },
    async exportList() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要导出的数据！')
        return
      }
      let obj = this.__transferChecked()
      this.exportLoading = true
      let res = await exportCluesListV1(obj).catch(() => {
        this.exportLoading = false
      })
      if (res.code == 0) {
        this.exportLoading = false
        this.checkedArr0 = []
        this.checkedArr1 = []
        this.checkedArr2 = []
        this.checkedArr3 = []
        this.checkedArr4 = []
        this.checkedArr5 = []
        this.checkedArr6 = []
        this.download(this.showSrcIp + res.data.url)
      }
    },
    async getInit() {
      this.tableInterval = 30 * 1000
      this.progressBar = true
      if (this.taskInfoData) {
        this.currentPercent = parseFloat(this.taskInfoData.clue_progress)
        this.task_id = this.taskInfoData.id
        this.group_id = this.taskInfoData.group_id
        if (this.taskInfoData.step_detail == '200') {
          this.tableHeader = [
            {
              label: '线索',
              name: 'content',
              width: '310px'
            },
            {
              label: '企业名称',
              name: 'clue_company_name',
              minWidth: '150px'
            }
          ]
        } else {
          this.tableHeader = [
            {
              label: '线索',
              name: 'content',
              fixed: 'left',
              minWidth: '90px'
            },
            {
              label: '企业名称',
              name: 'clue_company_name',
              minWidth: '80px'
            },
            {
              label: '线索源',
              name: 'chain_list',
              minWidth: '50px'
            }
          ]
        }

        if (this.taskInfoData.step_detail == '200') {
          //公司名称扩展
          this.tabActive = '0'
          this.tabActiveNameStatus = 1
          this.accuracyIndex = 0
          if (this.taskInfoData.step_status == 0) {
            this.progressBar = true
            this.againIsTrue = false // 重新获取原始线索的按钮是否显示
            // this.getTable(1)
            // this.getTab(1)
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTable(1)
              this.getTab(1)
            }, this.tableInterval) // 每10秒刷新一次
          } else {
            clearInterval(this.setTimer)
            this.setTimer = null
            this.progressBar = false
            this.againIsTrue = true // 重新获取原始线索的按钮是否显示
            this.getTable(1)
            this.getTab(1)
          }
        } else if (this.taskInfoData.step_detail == '206') {
          //IP段扩展
          this.againIsTrue = false // 重新获取原始线索的按钮是否显示
          this.tabActive = '0'
          this.tabActiveNameStatus = 0
          this.accuracyIndex = 1
          this.expandType = '2'
          if (this.taskInfoData.step_status == '0') {
            this.progressBar = true
            this.getTable(0)
            this.getTab(0)
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTable(0)
              this.getTab(0)
            }, this.tableInterval) // 每10秒刷新一次
          } else {
            this.progressBar = false
            this.getTable(0)
            this.getTab(0)
          }
        } else if (this.taskInfoData.step_detail == '201') {
          //通过域名扩展
          this.againIsTrue = false // 重新获取原始线索的按钮是否显示
          this.tabActive = '2'
          this.tabActiveNameStatus = 0
          this.accuracyIndex = 2
          this.expandType = '2'
          if (this.taskInfoData.step_status == '0') {
            this.progressBar = true
            this.getTable(0)
            this.getTab(0)
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTable(0)
              this.getTab(0)
            }, this.tableInterval) // 每10秒刷新一次
          } else {
            this.progressBar = false
            this.getTable(0)
            this.getTab(0)
          }
        } else if (this.taskInfoData.step_detail == '202') {
          //通过icp扩展
          this.againIsTrue = false // 重新获取原始线索的按钮是否显示
          this.tabActive = '0'
          this.tabActiveNameStatus = 0
          this.accuracyIndex = 3
          this.expandType = '2'
          if (this.taskInfoData.step_status == '0') {
            this.progressBar = true
            this.getTable(0)
            this.getTab(0)
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTable(0)
              this.getTab(0)
            }, this.tableInterval) // 每10秒刷新一次
          } else {
            this.progressBar = false
            this.getTable(0)
            this.getTab(0)
          }
        } else if (this.taskInfoData.step_detail == '203') {
          //通过证书扩展
          this.againIsTrue = false // 重新获取原始线索的按钮是否显示
          this.tabActive = '0'
          this.tabActiveNameStatus = 0
          this.accuracyIndex = 4
          this.stepInfoArr[4] = {
            clueSecondTitle: '依据证书扩展',
            clueText: '成功获取企业初始线索',
            clueTitle: '通过证书线索扩展，扩展线索已自动补充至线索库，扩展完成后生成线索库',
            tableText: '资产线索'
          }
          this.expandType = '2'
          if (this.taskInfoData.step_status == '0') {
            this.progressBar = true
            this.getTable(0)
            this.getTab(0)
            clearInterval(this.setTimer)
            this.setTimer = null
            this.setTimer = setInterval(() => {
              this.getTable(0)
              this.getTab(0)
            }, this.tableInterval) // 每10秒刷新一次
          } else {
            this.progressBar = false
            this.getTable(0)
            this.getTab(0)
          }
        } else if (this.taskInfoData.step_detail == '204') {
          //极速扩展
          this.againIsTrue = false // 重新获取原始线索的按钮是否显示
          this.tabActive = '0'
          this.tabActiveNameStatus = 0
          this.accuracyIndex = 4
          this.expandType = '1'
          this.stepInfoArr[4] = {
            clueSecondTitle: '线索扩展',
            clueText: '共生成资产线索',
            clueTitle: '依据原始线索扩展其他线索，可生成线索总表',
            tableText: '资产线索'
          }
          if (this.taskInfoData.step_status == '0') {
            if (this.taskInfoData.is_intellect_mode == 1) {
              // 智能模式
              this.expandTypeHead = true
              this.headText = '正在扩展线索...'
              this.getTablePushTimer(0)
            } else {
              this.progressBar = true
              this.getTable(0)
              this.getTab(0)
              clearInterval(this.setTimer)
              this.setTimer = null
              this.setTimer = setInterval(() => {
                this.getTable(0)
                this.getTab(0)
              }, this.tableInterval) // 每10秒刷新一次
            }
          } else {
            if (this.taskInfoData.is_intellect_mode == 1) {
              // 智能模式
              this.expandTypeHead = true
              this.getTablePushTimer(0)
            } else {
              this.progressBar = false
              this.getTable(0)
              this.getTab(0)
            }
            this.getTablePush(0)
          }
        } else {
          //生成线索总表
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式
            if (this.taskInfoData.is_intellect_failed == 1) {
              // 智能模式失败
              this.expandTypeHead = true
              this.getTablePushTimer(0)
              // 云端资产推荐
              let obj = {
                group_id: this.taskInfoData.group_id,
                expend_id: this.taskInfoData.id,
                operate_company_id: this.currentCompany
              }
              let res = await cloudAssets(obj)
              if (res.code == 0) {
                this.recommentFlagChange(this.taskInfoData.expend_flags) // 存储云端推荐flag
                this.$emit('son', '3', this.taskInfoData.id, this.expandType)
              } else {
                // 失败原因
                this.againIsShow = true
                this.headText = res.data
              }
            } else {
              this.expandTypeHead = true
              this.headText = '已生成线索总表，准备云端推荐'
              // this.getTablePushTimer(0)
            }
          } else {
            this.againIsTrue = false // 重新获取原始线索的按钮是否显示
            this.expandType = String(this.taskInfoData.detect_type)
            this.tabActive = '0'
            this.progressBar = false
            this.expandTypeHead = false
            this.tabActiveNameStatus = 1
            this.accuracyIndex = 5
            this.getTable(1)
            this.getTab(1)
          }
        }
      }
      console.log('expandTypeHead||progressBar', this.expandTypeHead || this.progressBar)
    },
    certShow() {
      if (this.accuracyIndex != 4) {
        return true
      } else {
        if (this.expandType == '1') {
          return true
        } else {
          return false
        }
      }
    },
    async againFun() {
      // 云端资产推荐
      let obj = {
        group_id: this.taskInfoData.group_id,
        expend_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      let res = await cloudAssets(obj)
      if (res.code == 0) {
        this.recommentFlagChange(res.data.flag) // 存储云端推荐flag
        this.$emit('son', '3', this.taskInfoData.id, this.expandType)
      } else {
        this.expandTypeHead = true
        this.againIsShow = true
        this.headText = res.data.error_msg // 失败原因
        this.getTablePushTimer(0)
      }
    },
    async handleMessage(res, o) {
      //处理接收到的信息
      // 根据公司名称获取初始线索
      if (
        res.cmd == 'detect_assets_tip1_process' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        this.accuracyIndex = 0
        if (res.data.progress == 100) {
          if (!this.progressBar) return // 防止websocket多次推送100%，进度条已经隐藏了又出来了
          this.currentPercent = 100
          // setTimeout(()=>{
          //     this.progressBar = false
          //     this.againIsTrue = true // 重新获取原始线索的按钮是否显示
          // },500)
          this.progressBar = false
          this.againIsTrue = true // 重新获取原始线索的按钮是否显示
          this.getTable(1)
          this.getTab(1)
          clearInterval(this.setTimer)
          this.setTimer = null
          this.tableInterval = 30 * 1000
        } else {
          this.progressBar = true
          this.companyNameLoad = res.data.company_name
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
      // 完成极速扩展
      if (
        res.cmd == 'detect_assets_tip2_all' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        //极速扩展
        this.accuracyIndex = 4
        if (res.data.progress == 100) {
          if (!this.progressBar) return
          this.currentPercent = 100
          setTimeout(() => {
            this.currentPercent = 100
            this.progressBar = false
          }, 1000)
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式完成，主动触发详情接口，更新步骤
            setTimeout(() => {
              this.$emit('son', '3', this.taskInfoData.id)
            }, 10000)
          } else {
            this.getTable(0)
            this.getTab(0)
            clearInterval(this.setTimer)
            this.setTimer = null
            this.tableInterval = 30 * 1000
          }
        } else {
          this.progressBar = true
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式完成，主动触发详情接口，更新步骤
            //   this.$emit("son", '3', this.taskInfoData.id)
            // 定时调用任务
          }
        }
      }
      // 完成ip扩展
      if (
        res.cmd == 'detect_assets_tip2_ip' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        this.accuracyIndex = 1
        if (res.data.progress == 100) {
          if (!this.progressBar) return
          // this.currentPercent = 99
          // setTimeout(()=>{
          //   this.currentPercent = 100
          //   this.progressBar = false
          // },1000)
          this.currentPercent = 100
          this.progressBar = false
          this.getTable(0)
          this.getTab(0)
          clearInterval(this.setTimer)
          this.setTimer = null
          this.tableInterval = 30 * 1000
        } else {
          this.progressBar = true
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
      // 完成域名精准扩展
      if (
        res.cmd == 'detect_assets_tip2_domain' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        //根域扩展
        this.accuracyIndex = 2
        if (res.data.progress == 100) {
          if (!this.progressBar) return
          this.currentPercent = 99
          // setTimeout(()=>{
          //   this.currentPercent = 100
          //   this.progressBar = false
          // },1000)
          this.currentPercent = 100
          this.progressBar = false
          this.getTable(0)
          this.getTab(0)
          clearInterval(this.setTimer)
          this.setTimer = null
          this.tableInterval = 30 * 1000
        } else {
          this.progressBar = true
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
      // 完成icp精准扩展
      if (
        res.cmd == 'detect_assets_tip2_icp' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        //icp扩展
        this.accuracyIndex = 3
        if (res.data.progress == 100) {
          if (!this.progressBar) return
          this.currentPercent = 99
          // setTimeout(()=>{
          //   this.currentPercent = 100
          //   this.progressBar = false
          // },1000)
          this.currentPercent = 100
          this.progressBar = false
          this.getTable(0)
          this.getTab(0)
          clearInterval(this.setTimer)
          this.setTimer = null
          this.tableInterval = 30 * 1000
        } else {
          this.progressBar = true
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
      // 完成证书精准扩展
      if (
        res.cmd == 'detect_assets_tip2_cert' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        //证书扩展
        this.accuracyIndex = 4
        if (res.data.progress == 100) {
          if (!this.progressBar) return
          this.currentPercent = 99
          // setTimeout(()=>{
          //   this.currentPercent = 100
          //   this.progressBar = false
          // },1000)
          this.currentPercent = 100
          this.progressBar = false
          this.getTable(0)
          this.getTab(0)
          clearInterval(this.setTimer)
          this.setTimer = null
          this.tableInterval = 30 * 1000
        } else {
          this.progressBar = true
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        }
      }
      // 智能模式失败detect_direct_operate，需要主动云端推荐
      if (
        res.cmd == 'detect_direct_operate' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        this.expandTypeHead = true
        this.againIsShow = true
        this.headText = res.data.error_msg // 失败原因
      }
    },
    // 统计根域、icp等
    async getTab(val) {
      let obj = {
        group_id: this.taskInfoData.group_id,
        data: {
          status: val, // 线索总表和获取初始线索是1，其余为0
          operate_company_id: this.currentCompany,
          detect_task_id: this.taskInfoData.id
        }
      }
      let res = await tabNumClues(obj)
      if (res.code == 0) {
        this.tabTotal = 0
        if (res.data.clues_count.length != 0) {
          res.data.clues_count.forEach((val) => {
            if (val.type == '0') {
              this.domain_num = val.count
            }
            if (val.type == '1') {
              this.cert_num = val.count
            }
            if (val.type == '2') {
              this.icp_num = val.count
            }
            if (val.type == '3') {
              this.icon_num = val.count
            }
            if (val.type == '6') {
              this.ip_num = val.count
            }
          })
          // if(this.accuracyIndex == 0){//公司
          //     this.tabTotal = this.ip_num + this.domain_num + this.cert_num + this.icp_num + this.icon_num
          // }else if(this.accuracyIndex == 1){//IP段
          //     this.tabTotal = this.domain_num + this.cert_num + this.icp_num + this.icon_num
          // }else if(this.accuracyIndex == 2){//域名
          //     this.tabTotal = this.ip_num + this.cert_num + this.icp_num + this.icon_num
          // }else if(this.accuracyIndex == 3){//icp
          //     this.tabTotal = this.ip_num + this.cert_num + this.domain_num + this.icon_num
          // }else if(this.accuracyIndex == 4){//证书
          //     if(this.expandType == '1'){
          //         this.tabTotal = this.ip_num + this.domain_num + this.cert_num + this.icp_num + this.icon_num
          //     }else {
          //         this.tabTotal = this.ip_num + this.domain_num + this.icp_num + this.icon_num
          //     }
          // }else {
          //     this.tabTotal = this.ip_num + this.domain_num + this.cert_num + this.icp_num + this.icon_num
          // }
          this.tabTotal =
            this.ip_num + this.domain_num + this.cert_num + this.icp_num + this.icon_num
        }
      }
    },
    getTablePushTimer(val) {
      this.getTablePush(val)
      // if(this.loadingTimer) clearInterval(this.loadingTimer)
      // this.loadingTimer = setInterval(() => {
      //     this.getTablePush(val)
      // },180000)
    },
    // 获取所有线索列表  -- 用于加载中的数据展示  将所有数据合并 不分类别 并且去除已有数据 出现新的数据将滚动到最下方
    async getTablePush(val) {
      let obj = {
        keyword: '',
        page: this.currentPage,
        per_page: this.pageSize,
        status: val, // 线索总表和获取初始线索是1，其余为0
        group_id: this.taskInfoData.group_id,
        detect_task_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany,
        // type: this.tabActive,
        is_whole: 1
      }
      let res = await getCluelist(obj)
        .catch((err) => {
          if (err.code == 403) {
            this.constraintRestrictions = true
          } else {
            this.constraintRestrictions = false
          }
          this.$emit('restrictions', this.constraintRestrictions)
        })
        .finally(() => {
          this.tableLoading = false
        })
      if (res.code == 0) {
        let list = []
        Object.keys(res.data).forEach((item) => {
          list = list.concat(res.data[item])
        })
        let newList = new Set(list)
        let newList1 = Array.from(newList)
        let copyList = JSON.parse(JSON.stringify(this.list))
        let newSelectList = newList1.filter(
          (item1) => !copyList.some((item2) => item1.id === item2.id)
        )
        this.list.push(...newSelectList)
        if (this.list.length !== 0) {
          this.interval = 5 * 60 * 1000
        }
        if (this.expandTypeHead) {
          if (this.loadingTimer) {
            clearTimeout(this.loadingTimer)
            this.loadingTimer = null
          }
          this.loadingTimer = setTimeout((val) => this.getTablePush(val), this.interval, val)
        }
      }
    },
    // 获取所有线索列表
    async getTable(val) {
      this.tableArr = []
      this.checkedArr0 = []
      this.checkedArr1 = []
      this.checkedArr2 = []
      this.checkedArr3 = []
      this.checkedArr4 = []
      this.checkedArr5 = []
      this.checkedArr6 = []
      let obj = {
        keyword: '',
        page: this.currentPage,
        per_page: this.pageSize,
        status: val, // 线索总表和获取初始线索是1，其余为0
        group_id: this.taskInfoData.group_id,
        detect_task_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany,
        // type: this.tabActive,
        is_whole: 1
      }
      this.tableLoading = true
      let res = await getCluelist(obj)
        .catch((err) => {
          console.log('err', err)
          if (err.code == 403) {
            this.constraintRestrictions = true
          } else {
            this.constraintRestrictions = false
          }
          this.tableLoading = false
          this.$emit('restrictions', this.constraintRestrictions)
        })
        .finally(() => {
          this.tableLoading = false
        })
      this.tableLoading = false
      if (res && res.code == 0) {
        this.tableLoading = false
        this.tableData_domain = []
        this.tableData_icp = []
        this.tableData_cert = []
        this.tableData_ip = []
        // if(this.accuracyIndex == 1){ // ip段扩展
        //     this.tableData = this.tableArr.concat(res.data.domain).concat(res.data.icp).concat(res.data.cert).concat(res.data.icon)
        // }else if(this.accuracyIndex == 2){ // 域名扩展
        //     this.tableData = this.tableArr.concat(res.data.ip).concat(res.data.icp).concat(res.data.cert).concat(res.data.icon)
        // }else if(this.accuracyIndex == 3){ // icp扩展
        //     this.tableData = this.tableArr.concat(res.data.ip).concat(res.data.domain).concat(res.data.cert).concat(res.data.icon)
        // }else if(this.accuracyIndex == 4){ // 证书扩展
        //     if(this.expandType == '1'){ // 标准模式
        //         this.tableData = this.tableArr.concat(res.data.ip).concat(res.data.domain).concat(res.data.icp).concat(res.data.cert).concat(res.data.icon)
        //     }else { // 专家模式
        //         this.tableData = this.tableArr.concat(res.data.ip).concat(res.data.domain).concat(res.data.icp).concat(res.data.icon)
        //     }
        // }else {
        //     this.tableData = this.tableArr.concat(res.data.ip).concat(res.data.domain).concat(res.data.icp).concat(res.data.cert).concat(res.data.icon)
        // }

        this.tableData = this.tableArr
          .concat(res.data.ip)
          .concat(res.data.domain)
          .concat(res.data.icp)
          .concat(res.data.cert)
          .concat(res.data.icon)
        this.tableDataOrigin = JSON.parse(JSON.stringify(this.tableData))
        // tableData_domain,tableData_icp,tableData_cert用于智能模式判断
        this.tableData_domain = res.data.domain
        this.tableData_icp = res.data.icp
        this.tableData_cert = res.data.cert
        this.tableData_ip = res.data.ip
        if (this.tableDataOrigin.length !== 0) {
          clearInterval(this.setTimer)
          this.tableInterval = 5 * 60 * 1000
          this.setTimer = null
          this.setTimer = setInterval(() => {
            this.getTable(this.accuracyIndex == 0 || this.accuracyIndex == 5 ? 1 : 0)
            this.getTab(this.accuracyIndex == 0 || this.accuracyIndex == 5 ? 1 : 0)
          }, this.tableInterval)
        }
        this.onekey = false // 表格数据更新重置
        if (sessionStorage.getItem('scrollTop')) {
          setTimeout(() => {
            // 获取行高
            let dataList = document.getElementsByClassName('borderWrap')[0].clientHeight
            // 滚动距离
            document.getElementsByClassName('clueContent')[0].scrollTop =
              sessionStorage.getItem('scrollTop')
            let num = parseInt(sessionStorage.getItem('scrollTop') / dataList)
            let type = this.tableData[num].type //当前类型
            this.tabActive = type
          }, 500)
        } else {
          setTimeout(() => {
            if (document.getElementsByClassName('clueContent')[0]) {
              document.getElementsByClassName('clueContent')[0].scrollTop = 0
            }
            if (this.tableData.length != 0) {
              this.tabActive = String(this.tableData[0].type)
            }
          }, 500)
        }
        this.loading = false
      }
    },
    checkedArrFun(arr) {
      this.checkedArr0 = arr
        .filter((item) => {
          return String(item.type) == 0
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr1 = arr
        .filter((item) => {
          return String(item.type) == 1
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr2 = arr
        .filter((item) => {
          return String(item.type) == 2
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr3 = arr
        .filter((item) => {
          return String(item.type) == 3
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr4 = arr
        .filter((item) => {
          return String(item.type) == 4
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr5 = arr
        .filter((item) => {
          return String(item.type) == 5
        })
        .map((item) => {
          return item.id
        })
      this.checkedArr6 = arr
        .filter((item) => {
          return String(item.type) == 6
        })
        .map((item) => {
          return item.id
        })
    },
    async removeFun() {
      if (
        this.checkedArr0.length == 0 &&
        this.checkedArr1.length == 0 &&
        this.checkedArr2.length == 0 &&
        this.checkedArr3.length == 0 &&
        this.checkedArr4.length == 0 &&
        this.checkedArr5.length == 0 &&
        this.checkedArr6.length == 0
      ) {
        this.$message.error('请选择要操作的数据！')
        return
      }
      let clueData = [
        {
          id: this.checkedArr0,
          type: '0',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr1,
          type: '1',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr2,
          type: '2',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr3,
          type: '3',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr4,
          type: '4',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr5,
          type: '5',
          is_all: '',
          keyword: ''
        },
        {
          id: this.checkedArr6,
          type: '6',
          is_all: '',
          keyword: ''
        }
      ]
      this.removeMore(clueData)
    },
    removeMore(clueData) {
      this.$confirm('确定忽略数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      })
        .then(async () => {
          this.removeLoading = true
          let obj = {
            status: 2,
            tab_status: this.tabActiveNameStatus,
            group_id: this.taskInfoData.group_id,
            data: clueData,
            operate_company_id: this.currentCompany
          }
          let res = await passClueV1(obj).catch(() => {
            this.removeLoading = false
          })
          if (res.code == 0) {
            this.removeLoading = false
            this.$message.success('操作成功！')
            this.checkedArr0 = []
            this.checkedArr1 = []
            this.checkedArr2 = []
            this.checkedArr3 = []
            this.checkedArr4 = []
            this.checkedArr5 = []
            this.checkedArr6 = []
            this.getTable(this.tabActiveNameStatus)
            this.getTab(this.tabActiveNameStatus)
            this.$refs.suspectedCluesOperation &&
              this.$refs.suspectedCluesOperation.$refs.eltable.clearSelection()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.unit_del>.el-message-box__btns')
        del.children[0].id = 'unit_del_cancel'
        del.children[1].id = 'unit_del_sure'
      }, 50)
    },
    removeOne(id, type) {
      let data = [
        {
          id: [id],
          type: String(type)
        }
      ]
      this.$confirm('确定忽略数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            status: 2,
            tab_status: this.tabActiveNameStatus,
            group_id: this.taskInfoData.group_id,
            data: data,
            operate_company_id: this.currentCompany
          }
          let res = await passClueV1(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            for (var i = 0; i < this.tableData.length; i++) {
              if (this.tableData[i].id == id) {
                this.tableData.splice(i, 1)
              }
            }
            this.getTable(this.tabActiveNameStatus)
            this.getTab(this.tabActiveNameStatus)
            // this.doLayout(this, 'eltable')// 解决表格错位
          }
        })
        .catch(() => {})
    },
    // 根据IP段获取
    expanding() {
      // 没有获取到初始线索
      if (!this.progressBar && this.tabTotal == 0) {
        this.$message.warning(
          '未查询到企业资产初始线索信息，建议手动导入或者确认该企业是否存在初始线索'
        )
        return
      }
      //扩展线索框
      // this.clueExpansion = true
      if (this.taskInfoData.is_intellect_mode == 1) {
        this.expandType = '3' // 智能模式
      } else if (this.taskInfoData.detect_type == 1) {
        this.expandType = '1' // 标准模式
      } else if (this.taskInfoData.detect_type == 2) {
        this.expandType = '2' // 专家模式
      }
      if (this.expandType != 3) {
        this.clueExpansioning()
      } else {
        this.myExpandingAuto()
      }
      // this.expandType = '3'
    },
    clueExpansioning() {
      // 显示进度条
      this.againIsTrue = false // 重新获取原始线索的按钮是否显示
      this.progressBar = true
      this.currentPercent = 0
      this.clueExpansion = false
      this.tableInterval = 30 * 1000
      this.tableHeader = [
        {
          label: '线索',
          name: 'content',
          fixed: 'left',
          minWidth: '90px'
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          minWidth: '80px'
        },
        {
          label: '线索源',
          name: 'chain_list',
          minWidth: '50px'
        }
      ]
      this.myExpanding()
    },
    // 智能模式扩展
    async myExpandingAuto() {
      if (
        this.tableData_domain.length == 0 &&
        this.tableData_icp.length == 0 &&
        this.tableData_cert.length == 0 &&
        this.tableData_ip.length == 0
      ) {
        this.$message.warning('智能模式：域名、ICP、证书不能都没有数据，请确认后重新操作！')
        return
      }
      let obj = {
        detect_task_id: this.taskInfoData.id,
        detect_type: 1,
        operate_company_id: this.currentCompany,
        is_intellect_mode: 1 // is_intellect_mode 0/1 非智能模式/智能模式
      }
      this.headText = '正在获取线索…'
      this.currentPercent = 0
      let res = await Expanding(obj)
      this.expandTypeHead = true

      if (res.code == 0) {
        this.$emit('son', '2', this.taskInfoData.id)
        this.headText = '正在扩展线索'
        this.accuracyIndex = 6 // 智能模式扩展
      }
    },
    async expandingTwo() {
      this.filterSuspectData()
      if (this.suspectedTableData.length == 0) {
        this.expandingTwoNext()
        return
      }
      this.$confirm('当前列表存在疑似线索，如没有经过人工确认会造成误报，请问是否继续?', '提示', {
        distinguishCancelAndClose: true, // 设置关闭和取消独立
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        cancelButtonClass: 'keyword_del_cancel',
        confirmButtonClass: 'keyword_del_sure',
        customClass: 'keyword_del',
        type: 'warning'
      })
        .then(async () => {
          this.expandingTwoNext()
        })
        .catch(() => {})
    },
    async expandingTwoNext() {
      // 显示进度条
      this.progressBar = true
      this.tableHeader = [
        {
          label: '线索',
          name: 'content',
          fixed: 'left',
          minWidth: '90px'
        },
        {
          label: '企业名称',
          name: 'clue_company_name',
          minWidth: '80px'
        },
        {
          label: '线索源',
          name: 'chain_list',
          minWidth: '50px'
        }
      ]
      this.currentPercent = 0
      // 扩展
      if (this.accuracyIndex == 1) {
        // 通过域名扩展
        this.myExpanding()
      } else if (this.accuracyIndex == 2) {
        // 通过icp扩展
        this.myExpanding()
      } else if (this.accuracyIndex == 3) {
        // 通过证书扩展
        this.myExpanding()
      } else if (this.accuracyIndex == 4) {
        this.loading = true
        // 生成线索总表
        this.tableData = []
        this.progressBar = false
        if (!(this.expandTypeHead || this.progressBar)) {
          this.currentPercent = 100
        } else {
          this.currentPercent = 0
        }
        let obj = {
          operate_company_id: this.currentCompany,
          detect_task_id: this.taskInfoData.id
        }
        let res = await generateClueAll(obj).finally(() => {
          this.loading = false
        })
        if (res.code == 0) {
          sessionStorage.removeItem('scrollTop')
          this.tableArr = []
          this.tableData = []
          this.tabActive = '6'
          this.getTable(1)
          this.getTab(1)
          this.accuracyIndex = this.accuracyIndex + 1
        }
      } else {
        // 云端资产推荐
        let obj = {
          group_id: this.taskInfoData.group_id,
          expend_id: this.taskInfoData.id,
          operate_company_id: this.currentCompany
        }
        let res = await cloudAssets(obj)
        if (res.code == 0) {
          this.recommentFlagChange(res.data.flag) // 存储云端推荐flag
          this.$emit('son', '3', this.taskInfoData.id)
        }
      }
    },
    // 扩展
    async myExpanding() {
      // this.$refs.eltable.clearSelection();
      sessionStorage.removeItem('scrollTop')
      this.currentPage = 1
      this.tableData = []
      this.tableArr = []
      this.tabTotal = 0
      this.ip_num = 0
      this.domain_num = 0
      this.cert_num = 0
      this.icon_num = 0
      this.icp_num = 0
      let obj
      if (this.expandType == '1') {
        //标准模式
        obj = {
          detect_task_id: this.taskInfoData.id,
          detect_type: this.expandType,
          operate_company_id: this.currentCompany
        }
      } else {
        //专家模式,expand_clue_type/0/1/2/6 域名/证书/icp/ip段
        if (this.accuracyIndex == 1) {
          // 通过域名扩展
          obj = {
            detect_task_id: this.taskInfoData.id,
            detect_type: this.expandType,
            expand_clue_type: '0',
            operate_company_id: this.currentCompany
          }
        } else if (this.accuracyIndex == 2) {
          // 通过ICP扩展
          obj = {
            detect_task_id: this.taskInfoData.id,
            detect_type: this.expandType,
            expand_clue_type: '2',
            operate_company_id: this.currentCompany
          }
        } else if (this.accuracyIndex == 3) {
          // 通过证书扩展
          obj = {
            detect_task_id: this.taskInfoData.id,
            detect_type: this.expandType,
            expand_clue_type: '1',
            operate_company_id: this.currentCompany
          }
        } else if (this.accuracyIndex == 0) {
          // 通过IP段扩展
          obj = {
            detect_task_id: this.taskInfoData.id,
            detect_type: this.expandType,
            expand_clue_type: '6',
            operate_company_id: this.currentCompany
          }
        }
      }
      obj.is_intellect_mode = 0 // is_intellect_mode 0/1 非智能模式/智能模式
      let res = await Expanding(obj)
      if (res.code == 0) {
        if (res.data) {
          // 各类原始线索为空
          if (res.data.is_empty) {
            this.progressBar = false
            if (this.expandType == '1') {
              // 标准模式
              this.$message.warning('原始线索为空，请结束流程重新新建')
              this.getNextStep()
              this.loading = true
              this.getTable(0)
              this.getTab(0)
            } else {
              if (this.accuracyIndex == 0) {
                this.$message.warning('原始IP线索为空，已自动进行域名扩展')
                this.getNextStep()
                this.expandingTwo()
              } else if (this.accuracyIndex == 1) {
                this.$message.warning('原始域名线索为空，已自动进行ICP扩展')
                this.getNextStep()
                this.expandingTwo()
              } else if (this.accuracyIndex == 2) {
                this.$message.warning('原始ICP线索为空，已自动进行证书扩展')
                this.getNextStep()
                this.expandingTwo()
              } else if (this.accuracyIndex == 3) {
                this.$message.warning('原始证书线索为空，已自动生成线索总表')
                this.getNextStep()
                this.expandingTwo()
              } else {
                this.loading = true
                this.getTable(0)
                this.getTab(0)
              }
            }
          }
        } else {
          this.getTable(0)
          this.getTab(0)
          this.getcehui()
          // 这里每隔10秒刷新一次获取当前任务状态，为了解决推送过程中进度卡住，实际已经推送结束了
          clearInterval(this.setTimer)
          this.setTimer = null
          this.setTimer = setInterval(() => {
            this.getTable(0)
            this.getTab(0)
            this.getcehui()
          }, this.tableInterval) // 每10秒刷新一次
          this.getNextStep()
        }
      }
      this.$forceUpdate()
    },
    getNextStep() {
      if (this.expandType == '1' || this.expandType == '3') {
        this.accuracyIndex = 4
        this.stepInfoArr[4] = {
          clueSecondTitle: '线索扩展',
          clueText: '共生成资产线索',
          clueTitle: '依据原始线索扩展其他线索，可生成线索总表',
          tableText: '资产线索'
        }
      } else {
        this.accuracyIndex = this.accuracyIndex + 1
        this.tabActive = '0'
        if (this.accuracyIndex == 2) {
          this.tabActive = '6'
        }
        if (this.accuracyIndex == 3) {
          this.tabActive = '0'
        }
        if (this.accuracyIndex == 4) {
          this.tabActive = '0'
          this.stepInfoArr[4] = {
            clueSecondTitle: '依据证书扩展',
            clueText: '成功获取企业初始线索',
            clueTitle: '通过证书线索扩展，扩展线索已自动补充至线索库，扩展完成后生成线索库',
            tableText: '资产线索'
          }
        }
      }
    },
    async getcehui() {
      //获取测绘任务
      let obj = {
        taskId: '',
        data: {
          operate_company_id: this.currentCompany
        }
      }
      let res = await detectTaskInfo(obj)
      if (res.code == 0) {
        if (res.data) {
          // kuozhanTimer
          if (res.data.step_status == 1) {
            setTimeout(() => {
              // this.expandType =
              this.currentPercent = 100
              this.getTable(0)
              this.getTab(0)
              clearInterval(this.setTimer)
              this.setTimer = null
              setTimeout(() => {
                this.progressBar = false
              }, 200)
            }, 500)
          }
        }
      }
    },
    // 返回
    async myReturn() {
      if (this.constraintRestrictions) return
      // this.$refs.eltable.clearSelection();
      sessionStorage.removeItem('scrollTop')
      this.loading = true
      this.currentPage = 1
      this.total = 0
      this.tabTotal = 0
      this.ip_num = 0
      this.domain_num = 0
      this.cert_num = 0
      this.icon_num = 0
      this.icp_num = 0
      this.tableData = []
      this.tableArr = []
      let obj = {
        detect_task_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      let res = await cehuiReturn(obj)
      if (res.code == 0) {
        if (this.expandType == '1') {
          // 标准模式
          if (this.accuracyIndex == 5) {
            this.accuracyIndex = 4
            this.stepInfoArr[4] = {
              clueSecondTitle: '线索扩展',
              clueText: '共生成资产线索',
              clueTitle: '依据原始线索扩展其他线索，可生成线索总表',
              tableText: '资产线索'
            }
            this.againIsTrue = false // 重新获取原始线索的按钮是否显示
            this.getTable(0)
            this.getTab(0)
          } else {
            this.accuracyIndex = 0
            this.againIsTrue = true // 重新获取原始线索的按钮是否显示
            this.expandType = '1'
            this.againIsTrue = true // 重新获取原始线索的按钮是否显示
            this.tableHeader = [
              {
                label: '线索',
                name: 'content'
              },
              {
                label: '企业名称',
                name: 'clue_company_name',
                minWidth: '150px'
              }
            ]
            this.getTable(1)
            this.getTab(1)
          }
        } else {
          // 专家模式
          this.accuracyIndex = this.accuracyIndex - 1
          if (this.accuracyIndex == 4) {
            this.againIsTrue = false // 重新获取原始线索的按钮是否显示
            // 返回生成线索总表
            this.tabActive = '0'
            this.againIsTrue = false // 重新获取原始线索的按钮是否显示
            this.getTable(0)
            this.getTab(0)
          } else if (this.accuracyIndex == 3) {
            // 返回生成线索总表
            this.tabActive = '6'
            this.againIsTrue = false // 重新获取原始线索的按钮是否显示
            this.getTable(0)
            this.getTab(0)
          } else if (this.accuracyIndex == 2) {
            // 返回证书扩展
            this.tabActive = '0'
            this.againIsTrue = false // 重新获取原始线索的按钮是否显示
            this.getTable(0)
            this.getTab(0)
          } else if (this.accuracyIndex == 1) {
            this.againIsTrue = false // 重新获取原始线索的按钮是否显示
            // 返回icp扩展
            this.tabActive = '2'
            this.againIsTrue = false // 重新获取原始线索的按钮是否显示
            this.getTable(0)
            this.getTab(0)
          } else {
            this.tabActive = '6'
            this.againIsTrue = true // 重新获取原始线索的按钮是否显示
            this.expandType = '1'
            this.againIsTrue = true // 重新获取原始线索的按钮是否显示
            this.getTable(1)
            this.getTab(1)
            this.tableHeader = [
              {
                label: '线索',
                name: 'content'
              },
              {
                label: '企业名称',
                name: 'clue_company_name',
                minWidth: '150px'
              }
            ]
          }
        }
      }
    },
    // 重新获取原始线索
    async againGetClueFun() {
      this.$confirm('确定重新获取初始线索?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_end_cancel',
        confirmButtonClass: 'unit_end_sure',
        customClass: 'unit_end',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            detect_assets_id: this.taskInfoData.id,
            operate_company_id: this.currentCompany
          }
          let res = await againGetClue(obj)
          this.againIsTrue = false
          this.progressBar = true
          this.currentPercent = 0
          this.domain_num = 0
          this.tabTotal = 0
          this.ip_num = 0
          this.icp_num = 0
          this.cert_num = 0
          this.icon_num = 0
          this.tableData = []
          this.$message.success('操作成功！')
        })
        .catch(() => {})
    },
    // 结束流程
    async ending() {
      this.$confirm('确定结束流程?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_end_cancel',
        confirmButtonClass: 'unit_end_sure',
        customClass: 'unit_end',
        type: 'warning'
      })
        .then(async () => {
          this.isEndingLoading = true
          let obj = {
            expend_id: this.taskInfoData.id,
            operate_company_id: this.currentCompany
          }
          let res = await endDetectTask(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.$emit('son', '1')
            this.isEndingLoading = false
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.unit_end>.el-message-box__btns')
        del.children[0].id = 'unit_end_cancel'
        del.children[1].id = 'unit_end_sure'
      }, 50)
    }
  },
  beforeDestroy() {
    this.expandTypeHead = false
    clearInterval(this.setTimer)
    this.setTimer = null
    sessionStorage.removeItem('scrollTop')
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer)
      this.loadingTimer = null
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.el-dialog__body {
  min-height: 110px !important;
}
.dialog-body > div > div {
  color: #62666c;
  margin-bottom: 32px;
  .el-radio-group {
    font-size: 16px;
  }
  /deep/.el-radio {
    font-size: 16px;
    font-weight: bold;
    .el-radio__label {
      font-size: 16px;
    }
  }
}
.cardClass {
  height: 70%;
  margin-top: 2%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  img {
    display: block;
    width: 120px;
    height: 80px;
  }
  .cardWrap {
    height: 160px;
    width: 49%;
    margin-bottom: 15px;
    background: url('../../assets/images/fiveBg.png');
    background-size: cover;
    .el-card {
      position: relative;
      height: 100%;
      width: 100%;
      padding: 20px;
      box-sizing: border-box;
      background: transparent;
      .title {
        color: rgba(55, 57, 60, 1);
        font-weight: 500;
        font-size: 14px;
        margin-bottom: 12px;
      }
      .content {
        width: 68%;
        line-height: 24px;
        color: rgba(98, 102, 108, 1);
      }
      .imgBot {
        position: absolute;
        right: 0px;
        bottom: 4px;
      }
    }
  }
}
.el-radio-group {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.modelShow {
  color: #909399;
}
.el-table .cell {
  overflow: hidden !important;
  white-space: nowrap !important;
}
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.fourthBox {
  // padding: 20px 0;
  background: url('../../assets/images/unit/unitLoadingBgc.png') no-repeat;
  background-size: 100% 100%;
  // display: flex;
  // align-items: center;
  // //   justify-content: center;
  // flex-direction: column;
  .loading {
    padding-top: 20px;
  }
  .fourthBoxContainer {
    width: 100%;
    height: 100%;
    overflow: auto;
    display: flex;
    align-items: center;
    flex-direction: column;
  }
}
// .fourthBox {
// //   height: calc(100% - 176px) !important;
//     background: url('../../assets/images/unit/unitLoadingBgc.png') no-repeat;
// background-size: 100% 100%;
//   position: relative;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   flex-direction: column;
//   overflow: auto;
//   .fourthTitle {
//       position: absolute;
//       top: 20px;
//       left: 0px;
//   }
// }
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.suspected {
  color: #f2b824;
  background: rgba(248, 193, 54, 0.16);
  border-radius: 2px;
  border: 1px solid #f8c136;
  padding: 2px 6px;
}
/deep/.el-icon-warning {
  color: #e94747;
  font-size: 16px;
  vertical-align: middle;
  margin-right: 2px;
}
.eltableBox {
  display: flex;
  //   padding: 0px 20px 0px 0px;
  height: calc(100% - 56px) !important;
  //   height: 100% !important;
  justify-content: space-between;
}
.ulBox {
  width: 160px;
  height: 100%;
  background: rgba(255, 255, 255, 0.36);
  border-right: 1px solid #e9ebef;
  box-sizing: border-box;
  padding-top: 5px;
  li {
    color: #62666c;
    cursor: pointer;
    height: 44px;
    background: rgba(234, 239, 246, 0);
    display: flex;
    align-items: center;
  }
  .ultitle {
    font-weight: 500;
    color: #37393c;
    padding-left: 16px;
  }
}
.fangkuai {
  width: 6px;
  height: 6px;
  background: #2677ff;
  box-shadow: 0px 0px 4px 0px rgba(38, 119, 255, 0.74);
  border-radius: 1px;
  margin: 0px 8px 0px 16px;
}
.fangkuai6 {
  background: #5346ff;
}
.fangkuai5 {
  background: #5346ff;
}
.fangkuai4 {
  background: #5346ff;
}
.fangkuai2 {
  background: #05d4a7;
}
.fangkuai1 {
  background: #13b7ff;
}
.fangkuai3 {
  background: #ec8f3c;
}
.myTable {
  width: calc(100% - 170px);
  height: 100% !important;
  //   height: calc(100% - 12px) !important;
  //   padding: 0px !important;
  padding-right: 20px;
  background: linear-gradient(180deg, #ffffff 0%, rgba(240, 246, 255, 0.7) 100%);
}
.myTableContent {
  height: calc(100% - 64px);
  //   background-color: #fff;
}
/deep/.el-table {
  border: none !important;
}
.eltable_domain {
  border-left: 4px solid #2677ff !important;
  border-radius: 4px 0px 0px 4px;
}
.eltable_ip {
  border-left: 4px solid #1059d5 !important;
}
/deep/.boxTwo1 {
  height: 100% !important;
  //   height: calc(100% - 156px) !important;
  //   padding-top: 2px !important;
}
/deep/.boxTwo {
  //   height: calc(100% - 270px) !important;
  height: 100% !important;
  //   padding-top: 2px !important;
}
.eltable_icp {
  border-left: 4px solid #05d4a7 !important;
}
.eltable_cert {
  border-left: 4px solid #13b7ff !important;
}
.eltable_icon {
  border-left: 4px solid #ec8f3c !important;
}
.tableLabel {
  box-sizing: border-box;
  height: 64px;
  padding: 0px 0px 0px 4px !important;
  //   margin-bottom: 10px !important;
  //   margin-top: 10px !important;
}
.item {
  height: 100%;
  display: flex;
  justify-content: space-around;
  // border: 1px solid red;
  & > span {
    text-align: left;
  }
}
.blueBlock {
  width: auto !important;
  box-sizing: border-box;
  height: 24px;
  line-height: 18px;
  font-size: 12px;
  padding: 4px 8px;
  color: #2677ff;
  background: rgba(38, 119, 255, 0.12);
}
.companyNameLoad {
  color: #2677ff;
}
</style>
