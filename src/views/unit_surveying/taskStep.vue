<template>
  <div class="stepContainer">
    <div class="step" v-for="(item, index) in stepArr" :key="index">
      <div class="step_head" :class="`is-${updateStatus(index + 1)} ${stepType}`">
        <div class="step_line" :class="{ step_work: $route.path == '/workbench' }">
          <i class="el-step__line-inner" :style="calcProgress(index + 2)"></i>
        </div>
        <el-tooltip effect="dark" placement="top" :open-delay="500" :content="item.title">
          <div class="step_icon" v-if="updateStatus(index + 1) == 'success'">
            <template v-if="stepType == 'unit'">
              <i class="el-step__icon-inner is-status el-icon-check"></i>
            </template>
            <template v-if="stepType == 'view'">
              <div class="step_icon_inner"
                ><i class="el-step__icon-inner is-status el-icon-check"></i
              ></div>
              <div class="step_icon_text">
                {{ item.title }}
              </div>
            </template>
          </div>
          <div class="step_icon" v-else-if="updateStatus(index + 1) == 'process'">
            <div class="step_icon_inner">{{ index + 1 }}</div>
            <div class="step_icon_text">
              {{ item.title }}
            </div>
          </div>
          <div class="step_icon" v-else-if="updateStatus(index + 1) == 'wait'">
            <template v-if="stepType == 'unit'">
              <div class="el-step__icon-inner">{{ index + 1 }}</div>
            </template>
            <template v-if="stepType == 'view'">
              <div class="step_icon_inner">{{ index + 1 }}</div>
              <div class="step_icon_text">
                {{ item.title }}
              </div>
            </template>
          </div>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  inject: ['getStepArr'],
  props: {
    currentStep: {
      type: [Number, String],
      default: 1
    },
    stepType: {
      type: String,
      default: 'unit' //unit 测绘页面步骤条 view 概览页面步骤条
    }
  },
  computed: {
    stepArr() {
      return this.getStepArr()
    }
  },
  methods: {
    updateStatus(val) {
      let currentStepStatus = ''
      if (val < this.currentStep) {
        currentStepStatus = 'success'
      } else if (val === this.currentStep) {
        currentStepStatus = 'process'
      } else {
        currentStepStatus = 'wait'
      }
      return currentStepStatus
    },
    calcProgress(stepNode) {
      let step = 100
      const style = {}
      style.transitionDelay = 150 * this.currentStep + 'ms'
      if (stepNode > this.currentStep) {
        step = 0
        style.transitionDelay = -150 * this.currentStep + 'ms'
      }
      style.borderWidth = step ? '1px' : 0
      style.width = step + '%'

      return style
    }
  }
}
</script>

<style lang="less" scoped>
.stepContainer {
  height: 20px;
  line-height: 20px;
  display: flex;
  white-space: nowrap;

  .step {
    // flex-basis: 200px;
    &:last-child {
      max-width: 20px;

      .step_head {
        padding-right: 0;
      }
    }

    .step_head {
      position: relative;
      width: 100%;
      display: inline-block;
      padding-right: 8px;

      .step_line {
        // margin-left: 2px;
        // margin-right: 0px;
        position: absolute;
        border-color: inherit;
        background-color: rgba(38, 119, 255, 0.2);
        height: 2px;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto 2px auto 0px;
      }
      // .unit{
      .step_icon {
        position: relative;
        z-index: 1;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 20px;
        height: 20px;
        font-size: 12px;
        box-sizing: border-box;

        transition: 0.15s ease-out;
        border-radius: 50%;
        border-color: inherit;

        .el-step__icon-inner {
          width: 100%;
          font-weight: 400;

          // color: #fff;
          &.is-status {
            transform: translateX(-1px) translateY(1px);
          }
        }
      }
      // }

      &.view {
        .step_icon {
          width: auto;
          border-radius: 12px;
          justify-content: left;
          // background: #DDE9FF;
          background: #2677ff;
          // border: 1px solid;
          padding: 1px;
          border-color: #2677ff;
          overflow: hidden;

          .step_icon_inner {
            width: 18px;
            height: 18px;
            border-radius: 12px;
            background-color: #fff;
            box-shadow: 2px 0px 2px 0px rgba(0, 26, 70, 0.12);
            color: #2677ff;
            text-align: center;
            line-height: 18px;
          }

          .step_icon_text {
            padding: 0 8px 0 4px;
          }
        }
      }
      &.is-success {
        color: #ffffff;
        border-color: #2677ff;

        .step_icon {
          background: #2677ff;
        }
      }

      &.is-process {
        color: #2677ff;

        .step_icon {
          width: auto;
          border-radius: 12px;
          justify-content: left;
          background: #dde9ff;
          border: 1px solid;
          border-color: #2677ff;
          overflow: hidden;
          padding: 0;

          .step_icon_inner {
            width: 18px;
            height: 18px;
            border-radius: 12px;
            background-color: #fff;
            box-shadow: 2px 0px 2px 0px rgba(0, 26, 70, 0.12);
            text-align: center;
            line-height: 18px;
          }

          .step_icon_text {
            padding: 0 8px 0 4px;
          }
        }
      }

      &.is-wait {
        color: #2677ff;

        .step_icon {
          background: #dde9ff;
        }
        &.view {
          .step_icon {
            background: #dde9ff;
            border: none;
          }

          .step_icon_text {
            color: rgba(38, 119, 255, 0.7);
          }
        }
        // &.view{
        //   .step_icon {
        //     width: auto;
        //     border-radius: 12px;
        //     justify-content: left;
        //     background: #DDE9FF;
        //     border: 1px solid;
        //     border-color: #2677FF;
        //     overflow: hidden;

        //     .step_icon_inner {
        //       width: 20px;
        //       height: 20px;
        //       border-radius: 12px;
        //       background-color: #fff;
        //       box-shadow: 2px 0px 2px 0px rgba(0, 26, 70, 0.12);
        //       text-align: center;
        //       line-height: 20px;
        //     }

        //     .step_icon_text {
        //       padding: 0 8px 0 4px;
        //     }
        //   }
        // }
      }
    }
  }
}
.step_icon_text {
  height: 18px;
  line-height: 18px;
}
</style>
