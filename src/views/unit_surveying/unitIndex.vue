<template>
  <div v-if="!recommentIsShow" class="container">
    <div class="headerTitle" style="width: calc(100% - 32px) !important">
      <div>
        <span class="goback" v-if="activeName == 'first'" @click="goBack"
          ><i class="el-icon-arrow-left"></i><span>返回</span><span class="spline">/</span></span
        >
        <span
          class="goback"
          v-if="activeName == 'second'"
          @click="$router.push({ path: '/unitIndex', query: { activeName: 'first' } })"
          ><i class="el-icon-arrow-left"></i><span>返回上一层</span
          ><span class="spline">/</span></span
        >
        单位资产测绘
        <span v-if="activeName == 'second'"><span class="spline">/</span>测绘记录</span>
        <span class="headerShow" v-if="activeName == 'first'"
          ><i class="el-icon-warning"></i
          >通过多种模式的自动化线索扩展和资产识别算法，快速、高效地识别和管理目标单位暴露面资产，适用于全量资产盘点</span
        >
      </div>
      <div>
        <span
          class="btn"
          @click="$router.push({ path: '/unitIndex', query: { activeName: 'second' } })"
          v-if="activeName == 'first'"
        >
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-history"></use>
          </svg>
          测绘记录
        </span>
        <span class="btn" v-if="activeName == 'first'" @click="recommentIsShow = true">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-assets"></use>
          </svg>
          推荐资产库
        </span>
      </div>
    </div>
    <div class="home_header">
      <unitTask v-if="activeName == 'first'" :taskId="taskId" @restrictions="restrictions" />
      <unitRecord v-if="activeName == 'second'" @son="sonData" />
    </div>
  </div>
  <div v-else class="box">
    <ipAssets @closeLog="closeLog" />
  </div>
</template>

<script>
import unitTask from './unitTask.vue'
import ipAssets from '../assetsView/ipAssets.vue'
import unitRecord from './unitRecord.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
export default {
  components: { ipAssets, unitTask, unitRecord },
  data() {
    return {
      recommentIsShow: false,
      activeName: 'first',
      taskId: '',
      constraintRestrictions: false,
    }
  },
  watch: {
    getterWebsocketMessage(msg) {}
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage'])
  },
  mounted() {
    if (this.$route.query.activeName) {
      this.activeName = this.$route.query.activeName
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    restrictions(val) {
      this.constraintRestrictions = val
    },
    goBack() {
      // if(this.constraintRestrictions) return
      sessionStorage.setItem('menuId', '2-1')
      this.changeMenuId('2-1')
      this.$router.push('/assetsTanzhi')
    },
    // 关闭推荐资产库
    closeLog() {
      this.recommentIsShow = false
    },
    // 用于任务记录查看详情
    sonData(val) {
      this.activeName = 'first'
      this.taskId = val
    }
  }
}
</script>

<style lang="less" scoped>
.box {
  width: calc(100% - 32px);
  height: 100%;

  /deep/.container {
    margin: 0 16px 16px 16px;
    background-color: #fff;
  }
  /deep/.headerTitle {
    padding-left: 0 !important;
  }
}
.container {
  position: relative;
  width: 100%;
  height: 100%;
  // background: #FFFFFF;
  // box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.08);
  border-radius: 4px;
  // background: #fff;
  // padding: 56px 16px 16px 16px;

  /deep/.headerTitle {
    width: calc(100% - 32px);
    // width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    .btn {
      font-weight: 400;
      color: #62666c;
      margin-left: 16px;
      &.el-button {
        padding: 0;
      }
      img {
        height: 14px;
      }
      &:hover {
        cursor: pointer;
        color: #2677ff;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    & > .tabsWrap {
      position: relative;
      .el-tabs__nav {
        padding-left: 20px;
      }
      .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
        // min-width: 60px;
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        font-weight: bold;
        color: #2677ff;
        background: rgba(38, 119, 255, 0.08);
      }
      .el-tabs__active-bar {
        left: 4px;
        width: 100%;
        padding: 0 16px;
        background: #2677ff;
      }
      .el-tabs__header {
        margin: 0;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #e9ebef;
      }
      .el-tabs__item {
        padding: 0 16px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        // padding: 0;
        font-size: 14px;
        font-weight: 400;
        color: #62666c;
      }
    }
  }
}
/deep/.el-table {
  width: 99%;
  border: 0;
  .detail {
    padding: 0 0;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ebeef5;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    img {
      vertical-align: middle;
    }
    p {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    div {
      div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .el-table__body td.el-table__cell {
    padding: 0 !important;
  }
  /deep/.el-table__body .cell {
    padding: 0 !important;
  }
  .detail:last-child {
    border-bottom: 0;
  }
  .cell-other {
    padding: 0 0;
  }
  // 定义单元格文本超出不换行
  /deep/.cell-other {
    // width: 140px !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
  }
}
/deep/.tableWrap {
  padding: 0 20px;
}
/deep/.filterTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  & > div {
    .el-input {
      width: 240px;
    }
    .el-select {
      width: 240px;
    }
    & > span {
      font-weight: 400;
      color: #2677ff;
      line-height: 20px;
      // margin-left: 16px;
      cursor: pointer;
    }
  }
}

/deep/.el-upload__text {
  line-height: 30px;
}
</style>
