<template>
  <el-dialog
    class="elDialogAdd"
    :close-on-click-modal="false"
    :visible="dialogVisible"
    width="60%"
    @close="closeDialog"
  >
    <template slot="title">
      <span>一键操作疑似线索</span>
    </template>
    <div class="dialog-body">
      <div class="myTable">
        <div class="tableLabel">
          <div></div>
          <div>
            <el-button @click="ignoreItem" class="normalBtnRe" type="primary" id="keyword_more_open"
              >忽略</el-button
            >
          </div>
        </div>
        <div class="tableWrap" ref="myTableContent">
          <el-table
            ref="eltable"
            :data="suspectedTableData"
            row-key="id"
            border
            height="100%"
            style="width: 100%"
            @selection-change="selectDataChange"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          >
            <el-table-column
              type="selection"
              align="center"
              :reserve-selection="true"
              width="55"
            ></el-table-column>
            <el-table-column
              v-for="item in tableHeaderCopy"
              :key="item.name"
              :prop="item.name"
              align="left"
              :label="item.label"
              :min-width="item.minWidth"
            >
              <template slot="header">
                <span v-if="item.name == 'content'" class="tableHeaderBox">
                  <img class="headerIcon" src="@/assets/images/star.png" alt="" />
                  <span class="headerText">{{ item.label }}</span>
                </span>
                <span v-else>{{ item.label }}</span>
              </template>
              <template slot-scope="scope">
                <span v-if="item.name == 'chain_list'">
                  <el-tooltip
                    v-if="getSourcetype(scope.row[item.name])"
                    class="item"
                    effect="light"
                    placement="top"
                    :open-delay="500"
                  >
                    <div slot="content"
                      >{{
                        $punyCode.toUnicode(
                          getSource(scope.row[item.name], scope.row.parent_id, scope.row.source)
                        )
                      }}{{
                        scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                      }}</div
                    >
                    <span>{{
                      $punyCode.toUnicode(
                        getSource(scope.row[item.name], scope.row.parent_id, scope.row.source)
                      )
                    }}</span>
                  </el-tooltip>
                  <span v-else
                    ><img
                      :src="getImg(scope.row[item.name])"
                      alt=""
                      style="width: 23px; height: 23px"
                  /></span>
                </span>
                <!-- icon以及icon疑似线索展示 -->
                <span
                  v-else-if="
                    item.name == 'content' && scope.row[item.name] && scope.row['type'] == 3
                  "
                >
                  <span>
                    <!-- 疑似线索 -->
                    <el-tooltip class="item" effect="light" placement="top" :open-delay="500">
                      <span slot="content">
                        <span v-if="scope.row['is_highlight']">疑似线索</span>：
                        <img
                          :src="
                            scope.row[item.name].includes('http')
                              ? scope.row[item.name]
                              : showSrcIp + scope.row[item.name]
                          "
                          alt=""
                          style="width: 23px; height: 23px"
                        />
                        <span>{{ scope.row.hash }}</span>
                      </span>
                      <span>
                        <img
                          :src="
                            scope.row[item.name].includes('http')
                              ? scope.row[item.name]
                              : showSrcIp + scope.row[item.name]
                          "
                          alt=""
                          style="width: 23px; height: 23px"
                        />
                        <span>{{ scope.row.hash }}</span>
                      </span>
                    </el-tooltip>
                  </span>
                  <!-- ICON和证书需要展示对应线索在fofa上的资产数量 -->
                  <el-tooltip class="item" effect="light" placement="top" :open-delay="500">
                    <span slot="content">
                      <span>此线索在FOFA资产数量</span>：{{
                        scope.row.fofa_assets_num && scope.row.fofa_assets_num.num
                      }}
                      <span
                        v-loading="scope.row.fofaLoading"
                        v-if="scope.row.fofaLoading"
                        style="color: #409eff"
                        >去FOFA查看</span
                      >
                      <a
                        v-if="
                          String(
                            scope.row.fofa_assets_num && scope.row.fofa_assets_num.fofa_url
                          ).includes('http') && !scope.row.fofaLoading
                        "
                        style="color: #409eff"
                        :href="
                          String(scope.row.fofa_assets_num && scope.row.fofa_assets_num.fofa_url)
                        "
                        target="_blank"
                        >去FOFA查看</a
                      >
                    </span>
                    <!-- 企业账号，公共线索库暂不支持 -->
                    <img
                      v-if="user.role != 3 && $route.path == '/unitIndex'"
                      src="../../assets/images/fofasmall.svg"
                      class="fofaImg"
                      @mouseenter="getFofaNumV1(scope.row, scope.$index)"
                      alt=""
                    />
                  </el-tooltip>
                  <!-- <span class="originLine" v-if="scope.row['is_fake_icp'] == 1">ICP盗用</span> -->
                </span>
                <!-- 域名 -->
                <span
                  v-else-if="
                    item.name == 'content' && scope.row[item.name] && scope.row['type'] == 0
                  "
                >
                  <span v-if="scope.row['is_highlight']">
                    <!-- 疑似线索 -->
                    <el-tooltip class="item" effect="light" placement="top" :open-delay="500">
                      <span slot="content"
                        ><span>疑似线索</span>：{{
                          $punyCode.toUnicode(String(scope.row[item.name]))
                        }}{{
                          scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                        }}</span
                      >
                      <span class="maxWidthContent">
                        {{ $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '-') }}
                      </span>
                    </el-tooltip>
                  </span>
                  <span v-else>
                    <!-- 中文域名给提示 -->
                    <el-tooltip class="item" effect="dark" placement="top" :open-delay="500">
                      <div slot="content"
                        >{{ $punyCode.toUnicode(String(scope.row[item.name]))
                        }}{{
                          scope.row.punycode_domain ? '(' + scope.row.punycode_domain + ')' : ''
                        }}</div
                      >
                      <span class="maxWidthContent">{{
                        $punyCode.toUnicode(scope.row[item.name] ? scope.row[item.name] : '-')
                      }}</span>
                    </el-tooltip>
                  </span>
                  <!-- 域名展示whois信息 -->
                  <el-tooltip
                    class="item"
                    popper-class="chainClass"
                    effect="light"
                    placement="top"
                    :open-delay="500"
                  >
                    <span v-loading="whoisLoading" slot="content">
                      <span>whois信息</span>：
                      <p v-for="(v, index) in whiosHeader" :key="index">{{
                        v.label + (whoisInfo[v.name] || '-')
                      }}</p>
                    </span>
                    <img
                      src="../../assets/images/whois.svg"
                      class="whoisImg"
                      @mouseenter="getWhois(scope.row, scope.rowindex)"
                      alt=""
                    />
                  </el-tooltip>
                  <!-- <span class="originLine" v-if="scope.row['is_fake_icp'] == 1">ICP盗用</span> -->
                </span>
                <!-- icon/域名 之外的线索类型 -->
                <span
                  v-else-if="
                    item.name == 'content' &&
                    scope.row[item.name] &&
                    scope.row['type'] != 3 &&
                    scope.row['type'] != 0
                  "
                >
                  <el-tooltip
                    class="item"
                    effect="light"
                    placement="top"
                    v-if="scope.row['is_highlight']"
                    :open-delay="500"
                  >
                    <span slot="content"
                      ><span>疑似线索</span>：{{
                        scope.row[item.name] ? scope.row[item.name] : '-'
                      }}</span
                    >
                    <span class="maxWidthContent">
                      {{ scope.row[item.name] ? scope.row[item.name] : '-' }}
                    </span>
                  </el-tooltip>
                  <el-tooltip class="item" effect="light" placement="top" v-else :open-delay="500">
                    <span slot="content">{{
                      scope.row[item.name] ? scope.row[item.name] : '-'
                    }}</span>
                    <span class="maxWidthContent">
                      {{ scope.row[item.name] ? scope.row[item.name] : '-' }}
                    </span>
                  </el-tooltip>
                  <!-- 线索库ICON和证书需要展示对应线索在fofa上的资产数量 -->
                  <el-tooltip class="item" effect="light" placement="top" :open-delay="500">
                    <span slot="content">
                      <span>此线索在FOFA资产数量</span>：{{
                        scope.row.fofa_assets_num && scope.row.fofa_assets_num.num
                      }}
                      <div v-loading="scope.row.fofaLoading" v-if="scope.row.fofaLoading"
                        >去FOFA查看</div
                      >
                      <a
                        v-if="
                          String(
                            scope.row.fofa_assets_num && scope.row.fofa_assets_num.fofa_url
                          ).includes('http') && !scope.row.fofaLoading
                        "
                        style="color: #409eff"
                        :href="
                          String(scope.row.fofa_assets_num && scope.row.fofa_assets_num.fofa_url)
                        "
                        target="_blank"
                        >去FOFA查看</a
                      >
                    </span>
                    <!-- 公共线索库暂不支持；企业账号不支持 -->
                    <img
                      v-if="user.role != 3 && $route.path == '/unitIndex' && scope.row['type'] == 1"
                      src="../../assets/images/fofasmall.svg"
                      class="fofaImg"
                      @mouseenter="getFofaNumV1(scope.row, scope.$index)"
                      alt=""
                    />
                  </el-tooltip>
                  <!-- <span class="originLine" v-if="scope.row['is_fake_icp'] == 1">ICP盗用</span> -->
                </span>
                <span v-else-if="item.name == 'clue_company_name'">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    v-if="scope.row[item.name]"
                  >
                    <div class="popoverBtn">
                      <div
                        ><el-button
                          type="text"
                          @click="selectedByCompanyN(scope.row[item.name], true)"
                          >选中所有该公司</el-button
                        ></div
                      >
                      <div
                        ><el-button
                          type="text"
                          @click="selectedByCompanyN(scope.row[item.name], false)"
                          >取消选中所有该公司</el-button
                        ></div
                      >
                    </div>
                    <span slot="reference">
                      <span style="padding: 2px 0px; display: inline-block">{{
                        scope.row[item.name] ? scope.row[item.name] : '-'
                      }}</span>
                      <span
                        v-if="scope.row['equity_percent'] && scope.row['equity_percent'] != '-'"
                        class="kgBox"
                      >
                        <el-tag>
                          <span style="color: #37393c">
                            控股比例：{{ scope.row['equity_percent'] }}%
                          </span>
                        </el-tag>
                      </span>
                    </span>
                  </el-popover>
                  <span v-else style="padding: 2px 0px; display: inline-block">-</span>
                </span>
                <span v-else-if="item.name == 'arrow'">
                  <img class="arrowImg" src="@/assets/images/arrow.png" alt="" />
                </span>
                <span v-else-if="item.name == 'type'">
                  {{ getTypeName(scope.row[item.name]) }}
                </span>
                <span v-else>
                  <el-tooltip
                    v-if="scope.row[item.name]"
                    class="item"
                    effect="dark"
                    placement="top"
                    :open-delay="500"
                  >
                    <span slot="content">{{ scope.row[item.name] }}</span>
                    <span>{{ scope.row[item.name] }}</span>
                  </el-tooltip>
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" width="55" label="操作">
              <template slot-scope="{ row }">
                <el-button type="text" size="small" @click="$emit('removeOne', row.id, row.type)"
                  >忽略</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import { getFofanum, getWhoisInfo } from '@/api/apiConfig/api.js'

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => []
    },
    tableHeader: {
      type: Array,
      default: () => []
    },
    tabList: {
      type: Array,
      default: () => []
    },
    user: Object
  },
  data() {
    return {
      suspectedTableData: [],
      checkArr: [],
      tableHeaderCopy: [],
      fofa_assets_num: 0,
      fofa_assets_url: '',
      fofaLoading: false,
      checkedArr: [],
      whoisInfo: {
        registration_date: '-',
        expiration_date: '-',
        sponsoring_registrar: '-',
        registrant_name: '-',
        registrant_mobile: '-',
        registrant_org: '-'
      },
      whoisLoading: false,
      whiosHeader: [
        {
          label: '域名创建时间：',
          name: 'registration_date'
        },
        {
          label: '域名更新时间：',
          name: 'expiration_date'
        },
        {
          label: '域名注册商：',
          name: 'sponsoring_registrar'
        },
        {
          label: '联系人：',
          name: 'registrant_name'
        },
        {
          label: '注册人手机：',
          name: 'registrant_mobile'
        },
        {
          label: '注册人组织：',
          name: 'registrant_org'
        }
      ]
    }
  },
  watch: {
    tableHeader(val) {
      this.tableHeaderCopy = val.concat([
        {
          label: '线索分类',
          name: 'type',
          minWidth: '40px'
        }
      ])
    },
    tableData() {
      this.checkedAlls = false
      this.tableData.forEach((item) => {
        this.$set(item, 'checked', false)
        this.$set(item, 'fofaLoading', false)
      })
      this.checkedArr = []
      this.suspectedTableData = JSON.parse(JSON.stringify(this.tableData)).filter(
        (item) => item.is_highlight
      )
    }
  },
  computed: {
    ...mapState(['currentCompany'])
  },
  methods: {
    getTypeName(val) {
      let name = ''
      this.tabList.forEach((item) => {
        if (item.label == val) {
          name = item.name
        }
      })
      return name
    },
    selectDataChange(val) {
      this.checkArr = val
    },
    ignoreItem() {
      if (this.checkArr.length == 0) {
        this.$message.warning('请选择需要操作的数据')
        return
      }
      let checkArr = {}
      let checkArr1 = []

      this.tabList.forEach((item) => {
        checkArr[item.label] = { id: [], type: item.label, is_all: '', keyword: '' }
      })
      this.checkArr.forEach((item) => {
        checkArr[item.type].id.push(item.id)
      })
      // let checkArr1 =
      for (let k in checkArr) {
        checkArr1.push(checkArr[k])
      }
      this.$emit('removeMore', checkArr1)
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    getWhois(clue, clueindex) {
      if (clue.whoisInfo) {
        // 存在直接展示
        this.whoisInfo = clue.whoisInfo
      } else {
        // 不存在需要调用接口
        this.whoisLoading = true
        getWhoisInfo({
          id: clue.id,
          operate_company_id: this.currentCompany,
          from:
            this.$route.path == '/publicClueBank' || this.$route.path == '/clueOverview' ? 1 : '' // 公共线索库：from:1,和单位资产测绘区分
        })
          .then((res) => {
            this.whoisLoading = false
            if (res.data) {
              this.whoisInfo = res.data
              // 查到后给列表fofa_assets_num赋值，避免重复查询
              this.suspectedTableData[clueindex].whoisInfo = res.data
            }
          })
          .catch(() => {
            this.whoisLoading = false
          })
      }
    },
    getFofaNumV1(clue, clueindex) {
      if (clue.fofa_assets_num) return
      this.$set(this.suspectedTableData[clueindex], 'fofaLoading', true)
      getFofanum({
        id: clue.id,
        operate_company_id: this.currentCompany
      }).then((res) => {
        this.$set(this.suspectedTableData[clueindex], 'fofaLoading', false)
        if (res.data) {
          this.$set(this.suspectedTableData[clueindex], 'fofa_assets_num', {
            num: res.data.num,
            fofa_url: res.data.fofa_url
          })
        }
      })
    },
    getSourcetype(val) {
      //线索来源类型
      if (val.length != 0) {
        if (val.length >= 2) {
          if (val[val.length - 2].type != 3) {
            return true
          } else {
            return false
          }
        } else {
          if (val[0].type != 3) {
            return true
          } else {
            return false
          }
        }
      } else {
        return true
      }
    },
    getSource(val, parent_id, source) {
      //线索来源
      if (val.length != 0) {
        if (val.length >= 2) {
          return val[val.length - 2].content
        } else {
          return val[0].content
        }
      } else {
        // 初始线索：0、3、4、5，扩展线索：2
        if (parent_id == 0 && source == 0) {
          return '初始线索'
        } else if (parent_id == 0 && source == 2) {
          return '扩展线索'
        } else if (parent_id == 0 && source == 4) {
          return '初始线索'
        }
      }
    },
    getImg(val) {
      //线索图片
      if (val.length != 0) {
        if (val.length >= 2) {
          return val[val.length - 2].content.includes('http')
            ? val[val.length - 2].content
            : this.showSrcIp + val[val.length - 2].content
        } else {
          return val[0].content.includes('http') ? val[0].content : this.showSrcIp + val[0].content
        }
      }
    },
    // 根据企业名称筛选列表的企业 并筛选
    selectedByCompanyN(companyN, isSelected) {
      this.suspectedTableData.forEach((item) => {
        if (item['clue_company_name'] == companyN) {
          this.$set(item, 'checked', isSelected)
          this.$refs.eltable.toggleRowSelection(item, isSelected)
        }
        // return item['clue_company_name'] == companyN
      })
      // this.checkedArr = this.suspectedTableData.filter(item => { return item.checked })
      // this.$refs.eltable.toggleRowSelection(companyN);
      // this.$emit('checkedArr', this.checkedArr)
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.el-table {
  .el-table__body td.el-table__cell div {
    padding: 12px !important;
  }
}
.dialog-body {
  height: 450px;
  padding-bottom: 50px;
}
/deep/.el-table__body {
  overflow: auto;
  // height: 100%;
}
.elDialogAdd {
  // height: 80%;
  .tableWrap {
    height: calc(100% - 32px);
  }
  .myTable {
    padding: 0;
    height: 90%;
  }
}
/deep/.el-dialog__body {
  padding: 0 28px !important;
  height: 100%;
  max-height: 700px !important;
}
/deep/.el-table__cell {
  height: 51px !important;
}
.arrowImg {
  width: 30px;
}
.fofaImg {
  // vertical-align: middle;
  margin: 0 5px;
  padding: 4px 8px;
  border-radius: 4px;
  box-sizing: border-box;
  background: #dbe0e3;
}
.whoisImg {
  // vertical-align: middle;
  margin: 0 5px;
  padding: 6px 8px;
  border-radius: 4px;
  box-sizing: border-box;
  background: #e5eeff;
}

.tableHeaderBox {
  .headerIcon {
    width: 20px;
    vertical-align: middle;
  }
  .headerText {
    margin-left: 6px;
    vertical-align: middle;
  }
}
</style>
