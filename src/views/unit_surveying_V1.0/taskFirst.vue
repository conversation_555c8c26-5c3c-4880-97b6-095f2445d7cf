<template>
  <div class="myBox" v-loading="changeCompanyLoading">
    <div class="grayBox">
      <div>
        <span>输入企业名称</span
        ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
          class="graylabel"
          >依据企业名称获取单位资产初始线索</span
        >
      </div>
    </div>
    <div class="content">
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="rules"
        label-width="105px"
        style="width: 50%"
      >
        <el-form-item class="drawTypeClass" label="测绘类型">
          <el-radio-group v-model="addForm.is_detect_scene" @change="drawTypeChange">
            <el-radio :label="0" value="0">新建场景</el-radio>
            <el-radio :label="1" value="1">使用已有场景</el-radio>
          </el-radio-group>
          <el-select
            v-loading="groupLoading"
            v-if="addForm.is_detect_scene == 1"
            v-model="addForm.scene_group_id"
            placeholder="请选择场景"
          >
            <el-option
              v-for="item in groupArr"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              @click.native="groupOptionClick(item)"
            >
            </el-option>
          </el-select>
          <el-checkbox v-if="addForm.is_detect_scene == 1" v-model="addForm.expand_init_clues"
            >是否作为初始线索进行扩展</el-checkbox
          >
        </el-form-item>
        <el-form-item label="" prop="companyName" class="companyDiv">
          <!-- is_real_customer == 1 代表在用户管理配置成客户，只能对本企业以及配置好的企业测绘 -->
          <span v-if="is_real_customer == 1" slot="label">
            企业名称
            <el-tooltip
              class="item"
              effect="dark"
              v-if="user.role != 1"
              content="只支持对本企业以及控股企业进行资产梳理"
              placement="top"
            >
              <i
                style="color: rgba(38, 119, 255, 0.2); font-size: 16px"
                class="el-icon-question"
              ></i>
            </el-tooltip>
          </span>
          <span v-else slot="label"> 企业名称 </span>
          <el-select
            v-if="is_real_customer == 1"
            v-model="addForm.companyName"
            filterable
            remote
            clearable
            @change="companyChange"
            placeholder="请输入本企业名称或控股企业名称"
            :remote-method="querySearch"
            :loading="loading"
          >
            <el-option v-for="(item, index) in kehuCompanyResults" :key="index" :value="item.value">
              {{ item.value }}
            </el-option>
          </el-select>
          <!-- 安服账号不显示 -->
          <el-select
            v-else
            v-model="addForm.companyName"
            filterable
            remote
            clearable
            @change="companyChange"
            placeholder="请输入企业名称进行搜索"
            :remote-method="remoteMethod"
            :loading="loading"
          >
            <el-option v-for="(item, index) in companyList" :key="index" :value="item.value">
              {{ item.value }}
            </el-option>
          </el-select>
          <el-button
            :class="addForm.companyName ? 'normalBtnAA companyBlue' : 'normalBtnAA companyGray'"
            @click.native="($event) => Inquire('')"
            :loading="companyCheckLoading"
            id="unit_sure"
            >企业关系查询</el-button
          >
        </el-form-item>
        <el-form-item class="treeWrap" v-if="treeIsTrue" label="">
          <p class="onekeyclass">
            <el-checkbox @change="onekeyCheck" v-model="isTreeChecked">全选</el-checkbox>
            <!-- 安服支持重新获取 -->
            <el-button v-if="user.role == 2" type="text" @click="Inquire(1)">重新获取</el-button>
          </p>
          <el-tree
            class="companyTree"
            ref="companyTree"
            :data="companyCascadeEquityList"
            show-checkbox
            node-key="company_name"
            :check-strictly="true"
            :default-expand-all="true"
            :default-checked-keys="checkedTree"
            :props="defaultProps"
          >
            <el-checkbox @change="onekeyCheck" v-model="isTreeChecked">全选</el-checkbox>
            <span class="custom-tree-node" slot-scope="{ data }">
              <el-tooltip class="nodeLabel" effect="light" placement="top" :open-delay="500">
                <div slot="content">{{ data.company_name }}</div>
                <span>{{ data.company_name }}</span>
              </el-tooltip>
              <span
                v-if="data.level != 0"
                :class="parseFloat(data.rate) > 50 ? 'treeNum bigNum' : 'treeNum smallNum'"
                >控股：<span class="num">{{ transferRate(data.rate) }}</span></span
              >
            </span>
          </el-tree>
        </el-form-item>
        <!-- 安服/超管 账号有此操作 v-if="user.role == 2 || user.role == 1" -->
        <el-form-item label="">
          <el-input
            type="textarea"
            :rows="5"
            v-model="otherCompany"
            placeholder="请输入其他测绘企业,多个值请使用分号或换行分隔"
          ></el-input>
        </el-form-item>
        <el-form-item label="已知IP">
          <el-input
            type="textarea"
            :rows="3"
            v-model="content.content6"
            placeholder="请输入IP段，多个值请用分号或换行分隔，例如：**********或者**********/24，每条线索不能超过200字符，最多 100 个 CIDR 或单个 IP，所有输入的 CIDR 或 IP 解析为单个 IP 的总数必须少于 5000 个。"
          ></el-input>
        </el-form-item>
        <el-form-item label="已知域名">
          <el-input
            type="textarea"
            :rows="2"
            v-model="content.content0"
            placeholder="请输入域名，多个值请用分号或换行分隔，例如：fofa.info，每条线索不能超过200字符"
          ></el-input>
        </el-form-item>
        <el-form-item label="已知证书">
          <el-input
            type="textarea"
            :rows="2"
            v-model="content.content1"
            placeholder='请输入证书，多个值请用分号或换行分隔，例如：O="北京华顺信安科技有限公司"或者CN="fofa.info"，每条线索不能超过200字符'
          ></el-input>
        </el-form-item>
        <el-form-item label="已知ICP">
          <el-input
            type="textarea"
            :rows="2"
            v-model="content.content2"
            placeholder="请输入ICP，多个值请用分号或换行分隔，例如：京ICP备********号 或者 京ICP备********号-3，每条线索不能超过200字符"
          ></el-input>
        </el-form-item>
        <el-form-item label="ICON">
          <el-upload
            class="upload-demo"
            drag
            :action="uploadSrcIp + '/assets/account/files'"
            :headers="uploadHeaders"
            :before-upload="beforeIpUpload"
            :on-success="iconUploadSucc"
            :on-remove="iconUploadRemove"
            :on-error="uploadErr"
            list-type="picture"
            accept=".png,.ico,.bmp,.jpg,.jpeg"
            :file-list="fileListIcon"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将ICON拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip"
              >支持上传.png,.ico,.bmp,.jpg,.jpeg文件，且大小不超过3M</div
            >
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <div class="footer">
      <el-button class="normalBtn" type="primary" :loading="sureLoading" @click="sureGetClue"
        >推荐线索获取</el-button
      >
    </div>
    <el-dialog
      class="elDialogAdd elDialogAddValidate"
      :close-on-click-modal="false"
      :visible.sync="otherCompanyValidVisible"
      width="500px"
    >
      <template slot="title"> 其他测绘企业校验错误企业名称 </template>
      <div class="dialog-body">
        <div class="dialog-item">以下输入错误的企业名称,可以重新复制到输入框中做修改</div>
        <div
          ><div class="dialog-item" v-for="(item, k) in errorCompanyList" :key="k">{{
            item
          }}</div></div
        >
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="otherCompanyValidVisible = false">取消</el-button>
        <el-button class="highBtn" type="primary" @click="otherComValidConfirm"
          >移除并复制错误企业名称</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import { cluesGroupListNoPage } from '@/api/apiConfig/clue.js'
import { companyCascadeEquity } from '@/api/apiConfig/recommend.js'
import {
  companyBatchVerify,
  SureCompany,
  getAssociate,
  kehuCompanyList
} from '@/api/apiConfig/surveying.js'

export default {
  data() {
    return {
      correctCompanyList: [],
      errorCompanyList: [],
      otherCompanyValidVisible: false,
      changeCompanyLoading: false,
      groupLoading: false,
      treeIsTrue: false,
      sureLoading: false,
      loading: false,
      isTreeChecked: false,
      companyList: [],
      companyCascadeEquityList: [], // 控股企业列表
      otherCompany: '',
      isInquireClick: false,
      other_customer_company: [],
      checkedTree: [],
      groupArr: [],
      restaurants: [],
      addForm: {
        companyName: '',
        is_detect_scene: 0,
        expand_init_clues: false, // 是否拿场景分组中的线索作为初始线索进行扩展 0不扩展/1进行扩展
        scene_group_id: '' // 场景库线索分组选择
      },
      companyCheckLoading: false,
      groupId: '',
      checked: '',
      rules: {
        companyName: [{ required: true, message: '请输入企业名称', trigger: 'blue' }]
      },
      content: {
        content0: '',
        content1: '',
        content2: '',
        content3: [],
        content4: '',
        content5: '',
        content6: ''
      },
      uploadHeaders: {
        Authorization: localStorage.getItem('token')
      },
      uploadMaxCount: 1,
      fileListIcon: [],
      defaultProps: {
        children: 'children',
        label: 'company_name'
      },
      user: {
        role: ''
      },
      kehuCompanyResults: [],
      is_real_customer: 0,
      userInfo: ''
    }
  },
  watch: {
    getterCurrentCompany(val) {
      this.getGroupList()
      if (this.user.role == 2) {
        // 安服
        this.changeCompanyLoading = true
        this.addForm = {
          companyName: '',
          is_detect_scene: 0,
          expand_init_clues: false, // 是否拿场景分组中的线索作为初始线索进行扩展 0不扩展/1进行扩展
          scene_group_id: '' // 场景库线索分组选择
        }
        setTimeout(() => {
          this.is_real_customer = sessionStorage.getItem('companyInfo')
            ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.is_real_customer
            : ''
          if (this.is_real_customer == 1) {
            // 客户账号，需要控制只能操作自己及控股企业
            this.getSearchData() // 企业账号获取企业及控股企业列表
          } else {
            this.restaurants = []
          }
          this.changeCompanyLoading = false
        }, 1000)
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage', 'getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    if (this.user.role == 2) {
      // 安服
      if (!this.currentCompany) return
      this.is_real_customer = sessionStorage.getItem('companyInfo')
        ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.is_real_customer
        : ''
    } else {
      // 企业、超管
      this.is_real_customer = this.user ? this.user.is_real_customer : ''
    }
    if (this.is_real_customer == 1) {
      // 客户账号，需要控制只能操作自己及控股企业
      this.getSearchData() // 企业账号获取企业及控股企业列表
    } else {
      this.restaurants = []
    }
    this.getGroupList() // 获取已有场景列表
  },
  mounted() {
    this.addHeadStyle()
  },
  methods: {
    beforeIpUpload(file, limit = 3) {
      const isLt1M = file.size / 1024 / 1024 < limit
      if (!isLt1M) {
        this.$message.error(`上传文件不能超过 ${limit}MB!`)
      }
      return isLt1M
    },
    addHeadStyle() {
      let lightStyle = document.createElement('style')
      lightStyle.type = 'text/css'
      lightStyle.id = 'head-style-light'
      window.document.head.appendChild(lightStyle)
    },
    // 动态给头部加样式
    selHightLightVlaue() {},
    transferRate(rate) {
      if (rate == '0.00%') {
        return '-'
      } else {
        return rate.replace('.00', '')
      }
    },
    companyChangeOther(val) {
      if (!val) {
        this.other_customer_company = []
      }
    },
    async remoteMethodOther(val) {
      if (val !== '') {
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        this.checkedTree = []
        this.other_customer_company = []
        let obj = {
          name: val,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })
            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.other_customer_company = a
          }
        }, 200)
      } else {
        this.other_customer_company = []
      }
    },
    async getSearchData() {
      // 用户管理设置为【客户】后可测绘的企业列表
      let res = await kehuCompanyList({ operate_company_id: this.currentCompany })
      let data = []
      if (res.data && res.data.length > 0) {
        res.data.map((v) => {
          data.push({ value: v })
        })
      }
      this.restaurants = data
      this.kehuCompanyResults = data
    },
    // 客户账号输入企业名称
    querySearch(queryString) {
      let restaurants = this.restaurants
      this.kehuCompanyResults = []
      // 调用 callback 返回建议列表的数据
      if (queryString !== '') {
        // 有输入值，自动关联企业
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        this.treeIsTrue = false
        this.checkedTree = []
        let obj = {
          name: queryString,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })
            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.kehuCompanyResults = [...a]
          }
        }, 200)
      } else {
        // 没有输入值，展示客户可以测绘的企业
        this.kehuCompanyResults = queryString
          ? restaurants.filter(this.createFilter(queryString))
          : restaurants
      }
    },
    // 非客户账号输入企业名称，自动关联
    async remoteMethod(val) {
      if (val !== '') {
        // 企业关系查询完后重新输入企业需要清空已查询到的数据
        this.treeIsTrue = false
        this.checkedTree = []
        this.companyList = []
        let obj = {
          name: val,
          operate_company_id: this.currentCompany
        }
        setTimeout(async () => {
          let res = await getAssociate(obj)
          if (res.code == 0) {
            let a = res.data.map((item) => {
              return item.name
            })

            a = a.map((item) => {
              return { value: `${item}` }
            })
            this.companyList = a
          }
        }, 200)
      } else {
        this.companyList = []
      }
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    otherComValidConfirm() {
      this.otherCompany = this.correctCompanyList.join('\r')

      this.errorCompanyList.unshift(' ')
      this.$copyText(this.errorCompanyList.join('\r')).then(
        (res) => {
          this.otherCompanyValidVisible = false
          this.$message.success('复制成功')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    companyOtherValidate(val) {
      return new Promise(async (resolve, reject) => {
        if (val.length == 0) return
        this.sureLoading = true
        let res = await companyBatchVerify({
          name: val,
          operate_company_id: this.currentCompany
        }).catch((error) => {
          this.sureLoading = false
          reject()
        })

        if (res.code == 0 && res.data && res.data.error_list.length > 0) {
          // 代表存在错误企业名称
          this.errorCompanyList = res.data.error_list || []
          this.correctCompanyList = res.data.correct_list || []
          resolve(true)
        } else {
          resolve(false)
        }
        this.sureLoading = false
      })
    },
    companyChange(val) {
      if (!val) {
        this.companyList = []
        this.kehuCompanyResults = [...this.restaurants] // 客户账号默认展示可测绘企业
        this.treeIsTrue = false
      }
    },
    groupOptionClick(item) {
      this.addForm.companyName = item.company_name
      this.addForm.scene_group_id = item.id
    },
    async drawTypeChange() {
      this.addForm.companyName = ''
      this.addForm.expand_init_clues = false // 是否拿场景分组中的线索作为初始线索进行扩展 0不扩展/1进行扩展
      this.addForm.scene_group_id = '' // 场景库线索分组选择
    },
    async getGroupList() {
      let obj = {
        per_page: 10,
        no_page: '1',
        is_detect_scene: 1, // 代表是单位资产测绘场景列表
        operate_company_id: this.currentCompany
      }
      this.groupLoading = true
      let res = await cluesGroupListNoPage(obj)
        .catch(() => {
          this.groupLoading = false
        })
        .catch(() => {
          this.groupArr = []
        })
      this.groupLoading = false
      let arr = res.data
      this.groupArr = arr
    },
    onekeyCheck() {
      if (this.isTreeChecked) {
        let arr = this.companyCascadeEquityList[0].children.map((item) => {
          return item.company_name
        })
        arr.push(this.addForm.companyName)
        this.$refs.companyTree.setCheckedKeys(arr)
      } else {
        this.$refs.companyTree.setCheckedKeys([])
      }
      this.$forceUpdate()
    },
    // 企业关系查询
    async Inquire(no_cache) {
      if (this.addForm.companyName != '') {
        this.companyCascadeEquityList = [
          {
            company_id: this.addForm.companyName,
            company_name: this.addForm.companyName,
            level: 0,
            children: []
          }
        ]
        let obj = {
          company_id: this.addForm.companyName,
          operate_company_id: this.currentCompany,
          no_cache: no_cache,
          not_need_icp: 1 // 不推送备案信息
        }
        this.companyCheckLoading = true
        this.treeIsTrue = true
        let res = await companyCascadeEquity(obj).catch(() => {
          this.companyCheckLoading = false
        })
        if (res.code == 0) {
          this.isInquireClick = true
          this.companyCheckLoading = false
          this.companyCascadeEquityList[0].children = res.data
          setTimeout(() => {
            this.checkedTree = res.data
              .filter((item) => {
                return parseFloat(item.rate) >= 50
              })
              .map((item) => {
                return item.company_name
              })
            this.checkedTree.push(this.addForm.companyName)
          }, 100)
        }
      }
    },
    iconUploadSucc(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        let obj = response.data
        this.content.content3.push(obj.url)
      } else {
        this.$message.error(file.response.message)
      }
    },
    iconUploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data.url
      })
      if (res.length == 0) {
        this.content.content3 = ''
      } else {
        this.content.content3 = res
      }
    },
    uploadErr(err, file, fileList) {
      let myError = err.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
    },
    async sureGetClue() {
      this.$refs.addFormRef.validate(async (valid) => {
        if (valid) {
          // 如果是客户账号，只能操作本企业及控股企业
          if (this.is_real_customer == 1) {
            if (
              this.restaurants
                .map((item) => {
                  return item.value
                })
                .indexOf(this.addForm.companyName) == -1
            ) {
              this.$message.error('暂无权限梳理该单位资产，请重新输入')
              return
            }
          }
          let clueData = []
          for (let i = 0; i <= 6; i++) {
            if (
              (i == 3 && this.content['content' + i].length > 0) ||
              (i != 3 && this.content['content' + i])
            ) {
              clueData.push({
                type: i,
                content:
                  i != 3
                    ? this.content['content' + i]
                        .split(/[；|;|\r\n]/)
                        .filter((item) => {
                          return item.trim()
                        })
                        .map((item) => {
                          return item.trim()
                        })
                    : this.content['content' + i]
              })
            }
          }
          if (this.otherCompany) {
            let otherCompany = this.otherCompany
              ? this.otherCompany.split(/[；|;|\r\n]/).filter((item) => {
                  return item.trim()
                })
              : []
            let validateRes = await this.companyOtherValidate(otherCompany).catch((error) => {
              console.log(error)
            })
            if (validateRes) {
              this.otherCompanyValidVisible = true
              return
            }
          }
          let otherCompany = this.otherCompany
            ? this.otherCompany
                .split(/[；|;|\r\n]/)
                .filter((item) => {
                  return item.trim()
                })
                .map((item) => {
                  return item.trim()
                })
            : []
          let company_list = []
          let no_need_controy_company = ''
          if (this.$refs.companyTree) {
            let companyTreeData = this.$refs.companyTree.getCheckedKeys()
            // 若企业关系查询数据存在，合并otherCompany其他测绘企业并去重
            company_list = [...new Set(companyTreeData.concat(otherCompany))]
            no_need_controy_company = this.isInquireClick && companyTreeData.length == 0 ? '1' : ''
          } else {
            company_list = [...otherCompany]
          }
          this.sureLoading = true
          let obj = {
            name: this.addForm.companyName,
            expand_init_clues: this.addForm.expand_init_clues ? 1 : 0,
            scene_group_id: this.addForm.scene_group_id,
            is_detect_scene: this.addForm.is_detect_scene,
            operate_company_id: this.currentCompany,
            company_list: company_list,
            has_other_company: this.otherCompany.length > 0 ? 1 : 0, // 是否有其他测绘企业输入
            data: clueData,
            no_need_controy_company
          }
          let res = await SureCompany(obj).catch(() => {
            this.sureLoading = false
          })
          if (res.code == 0) {
            sessionStorage.setItem('mappingTaskId', res.data.detect_task_id)
            sessionStorage.setItem('mappingTaskGroupId', res.data.group_id)
            //子传父
            this.$emit('son', '2', res.data.detect_task_id)
            this.sureLoading = false
          }
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.highBtn {
  width: 174px;
}
.myBox {
  position: relative;
  height: 100%;
  .el-input__suffix-inner > i {
    margin-top: 0 !important;
  }
}
.content {
  width: 100%;
  background: url('../../assets/images/taskFirst.png') no-repeat center;
  height: calc(100% - 115px);
  background-size: cover;
  display: flex;
  justify-content: center;
  overflow: auto;
  /deep/.drawTypeClass {
    margin: 20px 0 20px 0;
    .el-form-item__label {
      line-height: 42px !important;
    }
  }
  .treeWrap {
    .onekeyclass {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25px;
      background: #fff;
    }
  }
}
.companyTree {
  height: 222px;
  overflow: auto;
  color: #37393c;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #d1d5dd;
  .custom-tree-node {
    display: flex;
    align-items: center;
    width: 100%;
    .nodeLabel {
      display: inline-block;
      max-width: 70%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.treeNum {
  display: inline-block;
  width: 83px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  margin-left: 16px;
  font-size: 12px;
  border-radius: 14px;
}
.bigNum {
  background: #f0f3f8;
  border: 1px solid #dce5f3;
  .num {
    color: #2677ff;
  }
}
.smallNum {
  background: #f0f3f8;
  border: 1px solid #dce5f3;
  .num {
    color: #ec8f3c;
  }
}
#unit_sure {
  margin-left: 0px !important;
  border-radius: 0px 4px 4px 0px;
  border: 0;
}
.companyGray {
  background: #ccd2db;
}
.companyBlue {
  background: #2677ff !important;
}
/deep/.el-input__suffix-inner > i {
  margin-top: 3px;
}
/deep/.companyDiv {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
  .el-autocomplete {
    width: 83% !important;
    .el-input__inner {
      border-radius: 4px 0px 0px 4px !important;
      cursor: text;
      height: 32px;
    }
    .el-form-item__content .el-input__icon {
      line-height: 36px !important;
    }
  }
}
/deep/.upload-demo .el-upload-list {
  max-height: 200px !important;
  padding-bottom: 20px;
}
/deep/.el-select {
  width: 83% !important;
  height: auto;
  line-height: auto;
  .el-input__inner {
    border-radius: 4px 0px 0px 4px !important;
    cursor: text;
    height: 40px;
  }
}
.errorTip {
  line-height: 20px;
  font-size: 12px;
  color: red;
}
.dialog-item {
  line-height: 30px;
}
.elDialogAddValidate {
  /deep/.el-dialog__body {
    min-height: 100px !important;
  }
}
</style>
