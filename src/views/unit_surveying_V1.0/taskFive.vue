<template>
  <div class="myBox">
    <div class="box">
      <div class="grayBox">
        <div v-if="$route.path == '/checkTask'">
          <span>核查模式 > <span style="color: #2677ff; font-weight: 600">选择核查模式</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >选择目标资产列表需要核查的资产库</span
          >
        </div>
        <div v-else>
          <span class="unitCompany"
            >当前企业：
            <el-tooltip
              class="item"
              effect="dark"
              :content="taskInfoData.name"
              placement="top"
              :open-delay="500"
            >
              <span>{{ taskInfoData.name }}</span>
            </el-tooltip>
          </span>
          <span
            >关联任务 > <span style="color: #2677ff; font-weight: 600">关联任务自定义</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >针对资产盘点的结果，自定义后续的关联任务，支持到对应模块进行查看</span
          >
        </div>
      </div>
    </div>
    <div class="boxTwo">
      <div class="myTable">
        <el-row v-if="$route.path != '/checkTask'" class="cardClass">
          <el-col
            :span="8"
            v-for="(item, index) in cardContent"
            :key="index"
            :offset="index > 0 ? 1 : 0"
          >
            <el-card :body-style="{ padding: '0px' }">
              <el-checkbox class="title" v-if="index == 0" v-model="is_check_risk">{{
                item.checkText
              }}</el-checkbox>
              <el-checkbox class="title" v-if="index == 1" v-model="is_extract_clue">{{
                item.checkText
              }}</el-checkbox>
              <el-checkbox class="title" v-if="index == 2" v-model="is_auto_business_api">{{
                item.checkText
              }}</el-checkbox>
              <img :src="item.pic" class="imgBot" />
              <p class="content">{{ item.desc }}</p>
            </el-card>
          </el-col>
        </el-row>
        <el-row v-if="$route.path == '/checkTask'" class="cardClass">
          <el-col
            :span="8"
            v-for="(item, index) in cardCheckContent"
            :key="index"
            :offset="index > 0 ? 2 : 0"
          >
            <el-card :body-style="{ padding: '0px' }">
              <el-radio class="title" v-if="index == 0" v-model="audit_type" label="1">{{
                item.checkText
              }}</el-radio>
              <el-radio class="title" v-if="index == 1" v-model="audit_type" label="2">{{
                item.checkText
              }}</el-radio>
              <img :src="item.pic" class="imgBot" />
              <p class="content">{{ item.desc }}</p>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div class="footer">
        <el-button
          v-if="$route.path != '/checkTask'"
          class="normalBtn"
          type="primary"
          @click="goDownTask(1)"
          id="unit_scan"
          >确认下发</el-button
        >
        <el-button
          v-if="$route.path == '/checkTask'"
          :loading="checkLoading"
          class="normalBtn"
          type="primary"
          @click="goDownTask(3)"
          id="unit_scan"
          >输出核查结果</el-button
        >
        <el-button
          v-if="$route.path != '/checkTask'"
          class="normalBtnRe"
          type="primary"
          @click="goDownTask(0)"
          id="unit_scan"
          >跳过</el-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import Lottie from 'vue-lottie/src/lottie.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { startAudit } from '@/api/apiConfig/api.js'
import { goRelate, cloudAssetsReport } from '@/api/apiConfig/surveying.js'

export default {
  components: { Lottie },
  props: ['taskInfoData', 'secondParams'],
  data() {
    return {
      checkLoading: false,
      is_auto_business_api: false,
      is_check_risk: false,
      is_extract_clue: false,
      audit_type: '1',
      cardCheckContent: [
        {
          desc: '将导入的资产列表与资产台账进行数据对比，判断是否都存在于资产台账中',
          checkText: '资产台账核查',
          pic: require('../../assets/images/five_left.png')
        },
        {
          desc: '将导入的资产列表与资产空间检索的推荐资产库进行数据对比，判断是否都存在于推荐资产库中',
          checkText: '推荐资产库核查',
          pic: require('../../assets/images/five_right.png')
        }
      ],
      cardContent: [
        {
          desc: '将扫描入账后的资产进行风险事件的检测，结果可在事件告警页面进行查看',
          checkText: '是否需要进行风险事件的检测',
          pic: require('../../assets/images/five_left.png')
        },
        {
          desc: '针对入到资产台账中的资产进行线索的提取，包括证书、ICON、关键词等，可到企业线索库中进行查看',
          checkText: '是否对台账资产进行线索提取',
          pic: require('../../assets/images/five_right.png')
        },
        {
          desc: '针对入到资产台账中的URL资产进行一键关联到业务系统,结果可在业务系统页面进行查看',
          checkText: '是否对资产进行一键关联到业务系统',
          pic: require('../../assets/images/five_middle.png')
        }
      ]
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage'])
  },
  beforeDestroy() {
    clearInterval(this.setIntervalLevel)
    this.setIntervalLevel = null
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    // 入账扫描
    async goDownTask(icon) {
      let obj = null
      if (icon == 1) {
        // 确认下发
        if (!this.is_check_risk && !this.is_extract_clue && !this.is_auto_business_api) {
          this.$message.error('请选择任务！')
          return
        }
        obj = {
          expend_id: this.taskInfoData.id,
          is_extract_clue: this.is_extract_clue ? 1 : 0, //是否自动提取线索 0/1  不自动提取/自动提取
          is_check_risk: this.is_check_risk ? 1 : 0, // 是否自动监测风险事件 0/1  不自动/自动
          is_auto_business_api: this.is_auto_business_api ? 1 : 0, // 是否关联业务系统 0/1  不关联/关联
          operate_company_id: this.currentCompany
        }
      } else if (icon == 0) {
        // 跳过
        obj = {
          expend_id: this.taskInfoData.id,
          is_extract_clue: 0, //是否自动提取线索 0/1  不自动提取/自动提取
          is_check_risk: 0, // 是否自动监测风险事件 0/1  不自动/自动
          is_auto_business_api: this.is_auto_business_api ? 1 : 0, // 是否关联业务系统 0/1  不自动/自动
          operate_company_id: this.currentCompany
        }
      } else {
        // 输出核查结果
        obj = {
          audit_type: this.audit_type / 1,
          file_path: this.secondParams.file_path,
          operate_company_id: this.currentCompany
        }
      }
      if (icon == 3) {
        // 输出核查结果
        this.checkLoading = true
        let res = await startAudit(obj).catch(() => {
          this.checkLoading = false
        })
        if (res.code == 0) {
          this.checkLoading = false
          this.$emit('son', '3', res.data.task_id)
        } else {
          this.checkLoading = false
        }
      } else {
        // 单位资产、云端推荐流程
        let res = await goRelate(obj) // 确认、跳过
        if (res.code == 0) {
          this.$confirm('操作成功！是否立即生成报告?', '提示', {
            confirmButtonText: '立即生成',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(async () => {
              let obj = {
                expend_id: this.taskInfoData.id,
                operate_company_id: this.currentCompany
              }
              let res = await cloudAssetsReport(obj)
              if (res.code == 0) {
                this.$emit('son', '1')
                this.$message.success('操作成功！请稍后…')
              }
            })
            .catch(() => {
              this.$emit('son', '1')
            })
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.progressBarBox {
  padding-bottom: 4px !important;
}
.fourthBox {
  height: 100%;
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .fourthTitle {
    position: absolute;
    top: 20px;
    left: 0px;
  }
}
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.boxTwo {
  height: calc(100% - 97px) !important;
}
/deep/.myTable {
  padding: 0px 20px;
  height: calc(100% - 56px) !important;
  overflow: hidden;
  .cardClass {
    height: 70%;
    margin-top: 5%;
    display: flex;
    justify-content: space-evenly;
    img {
      display: block;
      width: 100px;
      height: 100px;
    }
    .el-col-8 {
      height: 100%;
      align-items: center;
      height: 259.44px;
      width: 461.22px;
      background: url('../../assets/images/fiveBg.png');
      background-size: cover;
      .el-card {
        position: relative;
        height: 100%;
        width: 100%;
        padding: 20px;
        box-sizing: border-box;
        background: transparent;
        .title {
          color: rgba(55, 57, 60, 1);
          font-weight: 500;
          font-size: 14px;
          margin-bottom: 12px;
        }
        .content {
          line-height: 24px;
          color: rgba(98, 102, 108, 1);
        }
        .imgBot {
          position: absolute;
          right: 17px;
          bottom: 4px;
        }
      }
    }
  }
}
</style>
