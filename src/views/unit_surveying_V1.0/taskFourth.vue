<template>
  <div class="myBox" v-if="!isScan">
    <div class="box">
      <div class="grayBox">
        <div v-if="tabIcon == 'one'">
          <span class="unitCompany"
            >当前企业：
            <el-tooltip
              class="item"
              effect="dark"
              :content="taskInfoData.name"
              placement="top"
              :open-delay="500"
            >
              <span>{{ taskInfoData.name }}</span>
            </el-tooltip>
          </span>
          <span
            >资产可信度评估 > <span style="color: #2677ff; font-weight: 600">信任等级</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >通过AI智能模型获取资产可信度等级，依据不同信任度进行资产分类入账</span
          >
        </div>
        <div v-if="tabIcon == 'two'">
          <span class="unitCompany"
            >当前企业：
            <el-tooltip
              class="item"
              effect="dark"
              :content="taskInfoData.name"
              placement="top"
              :open-delay="500"
            >
              <span>{{ taskInfoData.name }}</span>
            </el-tooltip>
          </span>
          <span>资产入账 > <span style="color: #2677ff; font-weight: 600">入账扫描</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >通过AI智能模型获取资产可信度等级，依据不同信任度进行资产分类入账</span
          >
        </div>
        <div v-if="tabIcon == 'one'">
          <span style="color: #62666c; cursor: pointer" @click="ending" id="unit_end"
            ><i class="el-icon-switch-button" style="margin-right: 5px"></i>结束流程</span
          >
        </div>
      </div>
      <div v-if="progressBar" class="progressBarBox">
        <div class="tu tuFourth">
          <!-- <Lottie :options="defaultOptions" :height="100" :width="100"  /> -->
        </div>
        <div class="progressBox">
          <div v-if="tabIcon == 'one'">正在评估</div>
          <div v-if="tabIcon == 'two'">正在入账扫描</div>
          <el-progress
            :stroke-width="14"
            :percentage="currentPercent"
            style="margin-bottom: 20px"
          ></el-progress>
        </div>
      </div>
      <div v-else>
        <div v-if="tabIcon == 'one'" class="progressBoxFinish">
          <span><i class="el-icon-success"></i>评估完成</span>
          <div class="progressContent">
            <img src="../../assets/images/taskSed.png" alt="" />
            <span>资产信任度评估完成：</span>高可信任资产：
            <span style="color: #2677ff">{{ tabNum[tabIcon]['high'] }}</span
            >条，中可信任资产： <span style="color: #2677ff">{{ tabNum[tabIcon]['middle'] }}</span
            >条，低可信任资产： <span style="color: #2677ff">{{ tabNum[tabIcon]['low'] }}</span
            >条
          </div>
        </div>
        <div v-if="tabIcon == 'two'" class="progressBoxFinish">
          <span><i class="el-icon-success"></i>扫描完成</span>
          <div class="progressContent">
            <img src="../../assets/images/taskSed.png" alt="" />
            <span>资产入账扫描完成：</span>台账资产：
            <span style="color: #2677ff">{{ tabNum[tabIcon]['konwn_table_num'] }}</span
            >条，疑似资产：
            <span style="color: #2677ff">{{ tabNum[tabIcon]['unknown_table_num'] }}</span
            >条，威胁资产：
            <span style="color: #2677ff">{{ tabNum[tabIcon]['threat_table_num'] }}</span
            >条
          </div>
        </div>
      </div>
    </div>
    <div :class="progressBar ? 'boxTwo' : 'boxTwo1'" v-loading="loading">
      <div class="tableLabel">
        <div>
          <div>{{ tableText }}</div>
          <div class="confirmBox" style="margin-right: 16px">
            <el-radio-group
              v-model="tabActive"
              @change="changeTab(tabActive, 'isTab', 'yesLoading')"
            >
              <el-radio-button
                :label="item.level"
                v-for="(item, index) in tabList[tabIcon]"
                :key="index"
                >{{ item.name }}({{ tabNum[tabIcon][item.icon] }})</el-radio-button
              >
            </el-radio-group>
          </div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="highCheck"
            id="keyword_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="highCheck"></el-button>
          </el-input>
          <span @click="highCheckClick"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <!-- <el-checkbox class="checkboxAll" v-model="checkedAll" @change="checkAllChange" id="keyword_all">选择全部</el-checkbox> -->
          <!-- 入账后删除 -->
          <el-button
            v-if="tabIcon == 'two'"
            class="normalBtnRe"
            type="primary"
            @click="remove"
            id="unit_del"
            >删除</el-button
          >
          <!-- 评估的删除 -->
          <el-button v-if="tabIcon == 'one'" class="normalBtnRe" type="primary" @click="removeMore"
            >删除</el-button
          >
        </div>
      </div>
      <div class="myTable">
        <tableList
          ref="tableList"
          :tableData="tableData"
          :pageIcon="tabIcon == 'two' ? 'scan' : 'predict'"
          :pageTab="tabActive"
          :expend_id="taskInfoData"
          :checkedAll="checkedAll"
          :isClearSelection="isClearSelection"
          :levelIcon="String(this.tabActive)"
          @getLevelList="getCloudAssetsAnyslate"
          @getList="getSacnistData"
          @handleSelectionChange="handleSelectionChange"
        />
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
      <div class="footer">
        <!-- scanLoading || progressBar -->
        <!-- 入账扫描按钮置灰点击入账扫描按钮请求接口响应时；进度条正在进行时； -->
        <el-button
          v-if="!isOverTask"
          class="normalBtn"
          type="primary"
          @click="goScan"
          id="unit_scan"
          :disabled="scanLoading || progressBar"
          >资产入账扫描</el-button
        >
        <el-button
          v-if="isOverTask"
          class="normalBtn"
          type="primary"
          @click="goAutoTask"
          id="unit_report"
          :disabled="progressBar"
          >关联任务自定义</el-button
        >
      </div>
    </div>
    <highCheckDrawerScan
      :highCheckdialog="highCheckdialog"
      :selectArr="cluesList"
      :formInline="formInline"
      @highCheck="highCheck"
      @highIsClose="highIsClose"
    />
  </div>
  <div v-else class="fourthBox">
    <div class="fourthTitle">
      <div class="grayBox">
        <div v-if="tabIcon == 'one'">
          <span
            >资产可信度评估 > <span style="color: #2677ff; font-weight: 600">信任等级</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >通过AI智能模型获取资产可信度等级，依据不同信任度进行资产分类入账</span
          >
        </div>
        <div v-if="tabIcon == 'two'">
          <span>资产入账 > <span style="color: #2677ff; font-weight: 600">入账扫描</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >通过AI智能模型获取资产可信度等级，依据不同信任度进行资产分类入账</span
          >
        </div>
      </div>
    </div>
    <div class="tu yuTu tuFifth" lazy></div>
    <div v-if="proIsTrue" style="margin: 12px 8px 10px 0px"
      ><span style="margin: 0px 8px 0px 0px">{{ headText }}</span
      ><span style="color: #2677ff">{{ currentPercent }}%</span></div
    >
    <div v-if="proIsTrue" class="fourthProgressBox"
      ><el-progress
        :stroke-width="14"
        :percentage="currentPercent"
        style="margin-bottom: 20px"
      ></el-progress
    ></div>
    <div v-if="againIsShow" class="unitError">
      <p class="unitText"><i class="el-icon-warning"></i>{{ headText }}</p>
      <el-button style="margin-left: 0" class="normalBtn" type="primary" @click="againScan"
        >重新扫描</el-button
      >
    </div>
  </div>
</template>
<script>
import * as animationData from '../../assets/images/data.json'
import Lottie from 'vue-lottie/src/lottie.vue'
import tableList from '../home_set/detectIndexTable.vue'
import highCheckDrawerScan from '../home_set/highCheck.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { getScanList, delScanList } from '@/api/apiConfig/api.js'
import { regRecommendClues } from '@/api/apiConfig/recommend.js'
import {
  updateCloudAssetsCount,
  delCloudAssetsList,
  cloudAssetsList,
  cloudAssetsReport,
  cloudAssetsScan,
  cloudAssetsAnyslate,
  endDetectTask
} from '@/api/apiConfig/surveying.js'

export default {
  components: { Lottie, tableList, highCheckDrawerScan },
  props: ['taskInfoData', 'expandType'],
  data() {
    return {
      isClearSelection: true,
      headText: '正在进行资产评估…',
      cluesList: {},
      scanCondition: {},
      highCheckdialog: false,
      proIsTrue: true, // 智能模式下进度条显示隐藏，解决进度条100%-0%的动画效果
      againIsShow: false,
      scanLoading: false,
      loading: false,
      isOverTask: false, // 是否完成入账扫描
      currentPercent: 0,
      defaultOptions: {
        //json动画
        animationData: animationData.default
      },
      progressBar: true, //是否显示进度条
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      tableText: '资产信息',
      tabActive: 1,
      tabNum: {
        one: { high: '', middle: '', low: '' },
        two: { konwn_table_num: '', unknown_table_num: '', threat_table_num: '' }
      },
      tabIcon: 'one',
      tabList: {
        one: [
          {
            level: 1,
            name: '高可信度',
            icon: 'high'
          },
          {
            level: 2,
            name: '中可信度',
            icon: 'middle'
          },
          {
            level: 3,
            name: '低可信度',
            icon: 'low'
          }
        ],
        // 'one': [
        //     {
        //         level: 1,
        //         name: '高可信度',
        //         icon: 'a'
        //     },
        //     {
        //         level: 2,
        //         name: 'B级',
        //         icon: 'b'
        //     },
        //     {
        //         level: 3,
        //         name: 'C级',
        //         icon: 'c'
        //     },
        //     {
        //         level: 4,
        //         name: 'D级',
        //         icon: 'd'
        //     }
        //     // {
        //     //     level: 1,
        //     //     name: 'A级',
        //     //     icon: 'a'
        //     // },
        //     // {
        //     //     level: 2,
        //     //     name: 'B级',
        //     //     icon: 'b'
        //     // },
        //     // {
        //     //     level: 3,
        //     //     name: 'C级',
        //     //     icon: 'c'
        //     // },
        //     // {
        //     //     level: 4,
        //     //     name: 'D级',
        //     //     icon: 'd'
        //     // }
        // ],
        two: [
          {
            level: 1,
            name: '资产台账',
            icon: 'konwn_table_num'
          },
          {
            level: 0,
            name: '疑似资产',
            icon: 'unknown_table_num'
          },
          // {
          //     level: 2,
          //     name: '忽略资产',
          //     icon: 'b'
          // },
          {
            level: 3,
            name: '威胁资产',
            icon: 'threat_table_num'
          }
        ]
      },
      tableData: [],
      checkedArr: [],
      checkedAll: false,
      setIntervalLevel: null,
      isScan: true, //入账扫描
      formInline: {
        ip: '',
        clue_company_name: '',
        province: '', // 省份名称（传汉字）,
        keyword: '', // 123123,
        asn: '', // 123123,
        cert: '', // cert,
        icp: '', // icp,
        url: '',
        title: '', // title,
        protocol: '', // protocol,
        logo: '', // logo,
        port: '', // port,
        cname: '', // cname,
        domain: [], // domain
        latitude: '',
        longitude: '',
        state: '',
        reason: '',
        reason_type: '', // 证据链
        tags: '',
        hosts: '',
        rule_tags: [],
        last_update_time: [],
        second_confirm: '' // second_confirm  0/待确认 1/已经确认
      }
    }
  },
  watch: {
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    taskInfoData(val) {
      this.getInit()
    },
    tabIcon: {
      handler(val) {
        this.isClearSelection = true
      }
    }
  },
  computed: {
    ...mapState(['currentCompany', 'recommentFlag']),
    ...mapGetters(['getterWebsocketMessage'])
  },
  beforeDestroy() {
    clearInterval(this.setIntervalLevel)
    this.setIntervalLevel = null
  },
  mounted() {
    this.getInit()
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    // 结束流程
    async ending() {
      this.$confirm('确定结束流程?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_end_cancel',
        confirmButtonClass: 'unit_end_sure',
        customClass: 'unit_end',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            expend_id: this.taskInfoData.id,
            operate_company_id: this.currentCompany
          }
          let res = await endDetectTask(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.$emit('son', '1')
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.unit_end>.el-message-box__btns')
        del.children[0].id = 'unit_end_cancel'
        del.children[1].id = 'unit_end_sure'
      }, 50)
    },
    async getInit() {
      // 401|0未完成入帐扫描显示进度，401|1已完成入帐扫描不显示进度
      if (this.taskInfoData) {
        if (this.taskInfoData.step_detail == 401) {
          // 入账扫描步骤
          this.tabIcon = 'two' // 入账扫描
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式
            if (this.taskInfoData.step_status == 0) {
              // 正在扫描
              this.isScan = true
              this.proIsTrue = true
              this.currentPercent =
                this.taskInfoData.step_status == 0 ? this.taskInfoData.progress / 1 : 0
              this.headText =
                this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
            } else {
              this.isOverTask = true // 是否已经点击了入账扫描，控制入账扫描按钮显示
              this.progressBar = this.taskInfoData.step_status == 0 ? true : false
              this.currentPercent =
                this.taskInfoData.step_status == 0 ? this.taskInfoData.progress / 1 : 0
              this.isScan = false // 已经扫描完成
            }
          } else {
            this.isOverTask = true // 是否已经点击了入账扫描，控制入账扫描按钮显示
            this.progressBar = this.taskInfoData.step_status == 0 ? true : false
            this.currentPercent =
              this.taskInfoData.step_status == 0 ? this.taskInfoData.progress / 1 : 0
            if (this.taskInfoData.step_status == 0) {
              // 正在扫描
              this.isScan = true // 正在入账扫描，控制特效图
              this.headText =
                this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
            } else {
              this.isScan = false // 已经扫描完成，控制特效图
            }
          }
        } else if (this.taskInfoData.step_detail == 400) {
          // 资产评估步骤
          this.tabIcon = 'one'
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式
            if (this.taskInfoData.is_intellect_failed == 1) {
              // 代表智能模式入账扫描失败，此时需要手动触发入账扫描
              this.isScan = true
              this.proIsTrue = false
              this.headText = '评估完成，等待资产入账扫描'
              let obj = {
                expend_id: this.taskInfoData.id,
                operate_company_id: this.currentCompany
              }
              let res = await cloudAssetsScan(obj).catch(() => {
                this.loading = false
                this.scanLoading = false
              })
              if (res.code == 0) {
                this.proIsTrue = true
                // this.headText = '正在等待资产入账扫描…'
                this.headText =
                  this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
              } else {
                this.proIsTrue = false
                this.headText = res.data // 失败的原因
              }
            } else {
              if (this.taskInfoData.step_status == 0) {
                // 正在评估
                this.isScan = true
                this.tabIcon = 'one'
                this.proIsTrue = true
                this.headText = '正在进行资产评估…'
                this.currentPercent =
                  this.taskInfoData.step_status == 0
                    ? this.taskInfoData.update_assets_level_progress / 1
                    : 0
              } else {
                // 评估完成,开始扫描
                this.isScan = true
                this.tabIcon = 'two'
                // this.headText = '正在进行资产入账扫描…'
                this.headText =
                  this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
              }
            }
          } else {
            this.isOverTask = false // 是否已经点击了入账扫描，控制入账扫描按钮显示
            if (this.taskInfoData.step_status == 0) {
              // 正在评估
              this.isScan = true // 还未点击入账扫描
              this.progressBar = true
              this.currentPercent = this.taskInfoData.update_assets_level_progress / 1
              this.getUpdateLevel()
            } else {
              // 评估完成，等待扫描
              this.isScan = false // 还未点击入账扫描
              this.progressBar = false
              this.currentPercent = 0
              clearInterval(this.setIntervalLevel)
              this.setIntervalLevel = null
            }
          }
        }
        this.getCloudAssetsAnyslate('yesLoading')
      } else {
        this.getCloudAssetsAnyslate('yesLoading')
      }
    },
    async againScan() {
      this.proIsTrue = false
      let obj = {
        expend_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      let res = await cloudAssetsScan(obj).catch(() => {
        this.loading = false
        this.scanLoading = false
      })
      if (res.code == 0) {
        this.proIsTrue = true
        this.againIsShow = false
        this.currentPercent = 0
        this.headText = this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
      }
    },
    // 每隔10请求一次评估列表
    getUpdateLevel() {
      this.setIntervalLevel = setInterval(() => {
        this.getCloudAssetsAnyslate('noLoading')
      }, 10000)
    },
    async handleMessage(res, o) {
      //执行扫描
      // detect_assets_tasks单位资产测绘模块入账 扫描
      if (
        res.cmd == 'detect_assets_tasks' &&
        res.data &&
        res.data.detect_assets_tasks_id == this.taskInfoData.id
      ) {
        if (res.data.status == 2) {
          this.$message.success('扫描成功！')
          this.progressBar = false // 进度条不显示
          this.isScan = false // 已经扫描完成，控制特效图
          this.currentPercent = 100
          this.currentId = '' // 控制推送结束后仅执行一次
          this.isOverTask = true
          this.getSacnistData('yesLoading')
          // 扫描结束需要触发此接口重新计算，解决es计算不准
          updateCloudAssetsCount({
            expend_id: this.taskInfoData.id,
            operate_company_id: this.currentCompany
          })
        } else if (res.data.status == 1) {
          // 正在扫描
          this.tabIcon = 'two'
          // 推送数据渲染到列表;因后端拆分任务扫描，会有多个进度而导致进度回退，所以小于当前进度不显示
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式
            this.isScan = true
            this.proIsTrue = true
            this.headText =
              this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
          } else {
            this.currentId = res.data.user_id
            this.progressBar = true
            this.headText =
              this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
          }
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.isScan = false
          this.getSacnistData('yesLoading')
        }
      } else if (
        res.cmd == 'detect_assets_tasks_cloud' &&
        res.data &&
        res.data.detect_assets_tasks_id == this.taskInfoData.id
      ) {
        // detect_assets_tasks_cloud 云端推荐模块入账 扫描
        if (res.data.status == 2) {
          this.$message.success('扫描成功！')
          this.progressBar = false // 进度条不显示
          this.isScan = false // 已经扫描完成，控制特效图
          this.isOverTask = true
          this.currentPercent = 100
          this.currentId = '' // 控制推送结束后仅执行一次
          this.getSacnistData('yesLoading')
          // 扫描结束需要触发此接口重新计算，解决es计算不准
          updateCloudAssetsCount({
            expend_id: this.taskInfoData.id,
            operate_company_id: this.currentCompany
          })
        } else if (res.data.status == 1) {
          // 正在扫描
          this.tabIcon = 'two'
          this.currentId = res.data.user_id
          this.progressBar = true //
          // 推送数据渲染到列表;因后端拆分任务扫描，会有多个进度而导致进度回退，所以小于当前进度不显示
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          this.headText =
            this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.isScan = false
          this.getSacnistData('yesLoading')
        }
      } else if (
        res.cmd == 'update_assets_level' &&
        res.data &&
        this.taskInfoData.id == res.data.task_id
      ) {
        // update_assets_level 单位资产测绘模块资产评估
        this.recommentFlagChange(res.data.flag) // 存储云端推荐flag
        if (res.data.status == 2) {
          this.currentPercent = 100
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式
            this.isScan = true
            this.proIsTrue = false
            this.proIsTrue = true
            this.headText = '评估完成，等待资产入账扫描'
            this.currentPercent = 0
            setTimeout(() => {
              this.$emit('son', '4', this.taskInfoData.id) // 每个流程结束需要主动请求详情接口，获取最新进度
            }, 2000)
          } else {
            this.$message.success('评估成功！')
            this.isScan = false
            clearInterval(this.setIntervalLevel)
            this.setIntervalLevel = null
            this.progressBar = false // 进度条不显示
            this.currentId = '' // 控制推送结束后仅执行一次
            this.loading = true
            setTimeout(async () => {
              // 评估完成后列表数据不能及时更新，延时请求
              this.getCloudAssetsAnyslate('yesLoading')
            }, 2000)
          }
        } else if (res.data.status == 1) {
          // 正在扫描
          this.tabIcon = 'one'
          this.updateLevelIsTrue = true
          this.currentId = res.data.user_id
          this.progressBar = true // 进度条不显示
          this.isScan = true
          // 推送数据渲染到列表
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.getCloudAssetsAnyslate('yesLoading')
        }
      } else if (
        res.cmd == 'update_assets_level_cloud' &&
        res.data &&
        this.taskInfoData.id == res.data.task_id
      ) {
        // update_assets_level_cloud 云端推荐模块资产评估
        this.recommentFlagChange(res.data.flag) // 存储云端推荐flag
        if (res.data.status == 2) {
          this.$message.success('评估成功！')
          this.loading = true
          this.isScan = false
          this.headText = '评估完成，等待资产入账扫描'
          this.currentPercent = 0
          clearInterval(this.setIntervalLevel)
          this.setIntervalLevel = null
          this.progressBar = false // 进度条不显示
          this.currentId = '' // 控制推送结束后仅执行一次
          setTimeout(() => {
            // 评估完成后列表数据不能及时更新，延时请求
            this.getCloudAssetsAnyslate('yesLoading')
          }, 2000)
        } else if (res.data.status == 1) {
          // 正在扫描
          this.tabIcon = 'one'
          this.updateLevelIsTrue = true
          this.currentId = res.data.user_id
          this.progressBar = true // 进度条不显示
          // 推送数据渲染到列表
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.getCloudAssetsAnyslate('yesLoading')
        }
      }
      // 智能模式失败detect_direct_operate，需要主动云端推荐
      if (
        res.cmd == 'detect_direct_operate' &&
        res.data &&
        res.data.task_id == this.taskInfoData.id
      ) {
        this.proIsTrue = false
        this.againIsShow = true
        this.headText = res.data.error_msg // 失败原因
      }
    },
    // 获取入账扫描后的列表数据以及tab数量
    async getSacnistData(isLoading) {
      this.loading = isLoading == 'yesLoading' ? true : false
      let listObj = {
        status: this.tabActive, // 0 默认无任何状态(未知/疑似资产) 1/已认领(已确认)   2/忽略资产   3/威胁
        operate_company_id: this.currentCompany,
        expend_id: this.taskInfoData.id,
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline
      }
      let res = await cloudAssetsList(listObj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        res.data.items.forEach((ch) => {
          // 用于控制折叠展开
          ch.isExpand = false
          ch.myPopover = false
          ch.myPopoverFlag = true
          ch.detail &&
            ch.detail.forEach((v) => {
              v.isURLExpand = false
            })
        })
        this.tableData = res.data.items
        this.scanCondition = res.data.condition ? res.data.condition : {}
        this.total = res.data.total
        this.tabNum['two'].konwn_table_num = res.data.konwn_table_num
        this.tabNum['two'].unknown_table_num = res.data.unknown_table_num
        this.tabNum['two'].threat_table_num = res.data.threat_table_num
      }
    },
    // 获取资产等级tab数量以及对应列表数据
    async getCloudAssetsAnyslate(isLoading) {
      if (this.tabIcon == 'one') {
        this.changeTab(this.tabActive, '', 'yesLoading') // 请求列表
        let res = await cloudAssetsAnyslate({
          // 请求tab数量
          flag: this.recommentFlag,
          data: {
            expand_source: this.taskInfoData.expand_source,
            operate_company_id: this.currentCompany,
            ...this.formInline
          }
        })
        if (res.code == 0) {
          // ABCD:1234
          this.tabNum['one'] = res.data
        }
      } else {
        this.getSacnistData(isLoading)
      }
    },
    // 切换查询等级数据
    async changeTab(val, isTab, isLoading) {
      if (isTab) {
        // tab切换要把分页重置
        this.isClearSelection = true
        this.currentPage = 1
        this.cluesList = {}
        this.formInline = {
          ip: '',
          clue_company_name: '',
          province: '', // 省份名称（传汉字）,
          keyword: '', // 123123,
          asn: '', // 123123,
          cert: '', // cert,
          icp: '', // icp,
          url: '',
          title: '', // title,
          protocol: '', // protocol,
          logo: '', // logo,
          port: '', // port,
          cname: '', // cname,
          domain: [], // domain
          latitude: '',
          longitude: '',
          state: '',
          reason: '',
          reason_type: '', // 证据链
          tags: '',
          hosts: '',
          rule_tags: [],
          last_update_time: [],
          second_confirm: '' // second_confirm  0/待确认 1/已经确认
        }
      }
      this.checkedArr = []
      if (this.tabIcon == 'one') {
        // 资产评估列表
        this.loading = isLoading == 'yesLoading' ? true : false
        let res = await getScanList({
          flag: this.recommentFlag,
          query: {
            assets_confidence_level: val,
            page: this.currentPage,
            per_page: this.pageSize,
            operate_company_id: this.currentCompany,
            ...this.formInline
          }
        }).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          this.loading = false
          let arr = []
          for (let key in res.data && res.data.items ? res.data.items : []) {
            arr.push({
              is_cdn: res.data.items[key][0].is_cdn ? res.data.items[key][0].is_cdn : false,
              ip: key,
              id: res.data.items[key][0].id,
              chain_list: res.data.items[key][0].chain_list,
              clue_company_name: res.data.items[key][0].clue_company_name,
              detail: res.data.items[key],
              rule_tags: [],
              hosts: [],
              isExpand: false,
              myPopover: false,
              myPopoverFlag: true,
              cloud_name: res.data.items[key][0].cloud_name,
              level_reason: res.data.items[key][0].level_reason
            })
          }
          this.tableData = arr.reverse()
          this.total = res.data.total
          let resnum = await cloudAssetsAnyslate({
            // 请求tab数量
            flag: this.recommentFlag,
            data: {
              expand_source: this.taskInfoData.expand_source,
              operate_company_id: this.currentCompany,
              ...this.formInline
            }
          })
          if (resnum.code == 0) {
            // ABCD:1234
            this.tabNum['one'] = resnum.data
          }
        }
      } else if (this.tabIcon == 'two') {
        // 入账扫描数据
        this.getSacnistData(isLoading)
      }
    },
    // 评估删除
    removeMore() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'account_del_cancel',
        confirmButtonClass: 'account_del_sure',
        customClass: 'account_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            flag: this.taskInfoData.expend_flags,
            data: {
              assets_confidence_level: String(this.tabActive),
              ip_array: this.checkedArr.map((item) => {
                return item.ip
              }),
              whole: this.checkedAll ? 1 : '',
              operate_company_id: this.currentCompany,
              ...this.formInline
            }
          }
          this.loading = true
          let res = await delScanList(obj).catch(() => {
            this.loading = false
          })
          if (res.code == 0) {
            this.$message.success('删除成功！')
            this.loading = false
            this.getCloudAssetsAnyslate('yesLoading')
          }
        })
        .catch(() => {})
    },
    // 入账扫描删除
    async remove() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'unit_del_cancel',
        confirmButtonClass: 'unit_del_sure',
        customClass: 'unit_del',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        let obj = {
          id: this.checkedArr.map((item) => {
            return item.id
          }),
          expend_id: this.taskInfoData.id,
          status: this.tabActive,
          operate_company_id: this.currentCompany
        }
        let res = await delCloudAssetsList(obj).catch(() => {
          this.loading = false
        })
        if (res.code == 0) {
          setTimeout(() => {
            this.loading = false
            this.$message.success('删除成功！')
            this.getSacnistData('yesLoading')
          }, 3000)
        }
      })
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.isClearSelection = false
      this.pageSize = val
      this.changeTab(this.tabActive, '', 'yesLoading')
    },
    handleCurrentChange(val) {
      this.isClearSelection = false
      this.currentPage = val
      this.changeTab(this.tabActive, '', 'yesLoading')
    },
    checkAllChange(row, index) {
      // return !this.checkedAll;
    },
    // 生成报告
    async goReport() {
      let obj = {
        expend_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      let res = await cloudAssetsReport(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
      }
    },
    goAutoTask() {
      this.$emit('son', '5') // 执行第五步
    },
    // 入账扫描
    async goScan() {
      let obj = {
        expend_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      this.loading = true
      this.scanLoading = true
      let res = await cloudAssetsScan(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.scanLoading = false // 入账扫描按钮请求过程中暂时置灰
        this.loading = false
        const h = this.$createElement
        this.$message({
          type: 'success',
          message: h('div', null, [
            h('span', { style: 'color: #67C23A' }, `操作成功！`),
            h(
              'span',
              { style: 'color: #F8C136' },
              `${res.data.warn_message ? '(' + res.data.warn_message + ')' : ''}`
            )
          ])
        })
        this.tabActive = 1 // 切换到台账资产
        this.tabIcon = 'two'
        this.isScan = true // 正在入账扫描，控制特效图
        this.currentPercent = 0 // 入账扫描进度要从0开始，防止评估完成是100
        this.headText = this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
        this.progressBar = true // 显示进度条
        this.isOverTask = true // 是否已经点击了入账扫描，控制入账扫描按钮显示
      } else {
        this.tabIcon = 'two'
      }
    },
    highCheckClick() {
      this.highCheckdialog = true
      if (this.isOverTask) {
        // 已经完成入账扫描
        this.cluesList = { ...this.scanCondition }
      } else {
        // 评估完成还未入账扫描
        this.getRecommendCluesData()
      }
    },
    // 高级筛选条件
    async getRecommendCluesData() {
      // 线索
      this.loading = true
      let res = await regRecommendClues({
        flag: this.taskInfoData.expend_flags,
        data: {
          operate_company_id: this.currentCompany
        }
      }).catch(() => {
        this.loading = false
      })
      this.loading = false
      if (res.data && res.data.logo) {
        res.data.logo.forEach((item, index) => {
          item.id = index + 1
        })
      }
      this.cluesList = res.data ? res.data : []
    },
    highCheck() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.getCloudAssetsAnyslate('yesLoading')
    },
    highIsClose() {
      this.highCheckdialog = false
    }
  }
}
</script>
<style lang="less" scoped>
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.progressBarBox {
  padding-bottom: 4px !important;
}
.fourthBox {
  height: 100%;
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .fourthTitle {
    position: absolute;
    top: 20px;
    left: 0px;
  }
}
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.boxTwo {
  height: calc(100% - 235px) !important;
}
</style>
