<template>
  <div class="myBox" v-if="!isScan">
    <!-- 推荐资产-资产评估-入账扫描 -->
    <div class="box">
      <div class="grayBox">
        <div>
          <span class="unitCompany"
            >当前企业：
            <el-tooltip
              class="item"
              effect="dark"
              :content="taskInfoData.name"
              placement="top"
              :open-delay="500"
            >
              <span>{{ taskInfoData.name }}</span>
            </el-tooltip>
          </span>
          <span>云端资产推荐 > <span style="color: #2677ff; font-weight: 600">推荐资产</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >获取企业云端资产信息</span
          >
        </div>
        <div>
          <span id="unit_recommend_end" class="overTaskClass" @click="endTask"
            ><i class="el-icon-switch-button" style="margin-right: 5px"></i>结束流程</span
          >
        </div>
      </div>
      <div v-if="progressBar" class="progressBarBox">
        <div class="tu tuThird">
          <!-- <Lottie :options="defaultOptions" :height="100" :width="100"  /> -->
        </div>
        <div class="progressBox">
          <div>正在推荐资产数据</div>
          <el-progress
            color="rgba()"
            :stroke-width="14"
            :percentage="currentPercent"
            style="margin-bottom: 20px"
          ></el-progress>
          <div class="progressContent">
            <img src="../../assets/images/taskSed.png" alt="" />
            <span>成功获取资产数据：</span>
            <span style="color: #2677ff">{{ total }}</span
            >条，可以导入第三方数据进行资产融合
          </div>
        </div>
      </div>
      <div v-else>
        <div class="progressBoxFinish">
          <span><i class="el-icon-success"></i>获取资产完成</span>
          <div class="progressContent">
            <img src="../../assets/images/taskSed.png" alt="" />
            <span>成功获取资产数据：</span>
            <span style="color: #2677ff">{{ total }}</span
            >条，可以导入第三方数据进行资产融合
          </div>
        </div>
      </div>
    </div>
    <div :class="progressBar ? 'boxTwo' : 'boxTwo1'" v-loading="loading">
      <div class="tableLabel">
        <div>
          <p>资产信息</p>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="highCheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="highCheck"></el-button>
          </el-input>
          <span @click="highCheckClick"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <!-- 正在推荐时不能导入 -->
          <el-button
            class="normalBtnRe"
            type="primary"
            :disabled="progressBar"
            @click="removeMore"
            id="unit_export"
            >删除</el-button
          >
          <el-button
            class="normalBtnRe"
            type="primary"
            :loading="isOtherDataUploading"
            :disabled="progressBar"
            @click="uploadOtherPlate"
            id="unit_export"
            >三方导入<span v-if="isOtherDataUploading">{{ otherDataCurrProgress }}</span></el-button
          >
        </div>
      </div>
      <div class="myTable">
        <tableList
          ref="tableList"
          :tableData="tableData"
          pageIcon="third"
          :checkedAll="checkedAll"
          :isClearSelection="isClearSelection"
          @getcloudAssets="getcloudAssets"
          @handleSelectionChange="handleSelectionChange"
        />
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
      <div class="footer">
        <el-button
          class="normalBtn"
          type="primary"
          @click="goPredict"
          id="unit_predict"
          :disabled="progressBar || tableData.length == 0 || this.otherLoading"
          >资产信任度评估</el-button
        >
      </div>
    </div>
    <highCheckDrawerScan
      :highCheckdialog="highCheckdialog"
      :selectArr="cluesList"
      :formInline="formInline"
      @highCheck="highCheck"
      @highIsClose="highIsClose"
    />
    <el-dialog
      @close="closeOtherDataDialog"
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <template slot="title"> 三方导入数据 </template>
      <div class="dialog-body">
        <p class="downloadClass">
          <i class="el-icon-warning"></i>请点击下载
          <span @click="downloadOther('三方数据导入模板.xlsx')">三方导入模板.xlsx</span>
        </p>
        <p class="downloadClass">
          <i class="el-icon-warning"></i>请点击下载
          <span @click="downloadOther('三方数据导入模板.csv')">三方导入模板.csv</span>
        </p>
        <el-upload
          class="upload-demo"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          accept=".xlsx,.csv"
          :multiple="true"
          :before-upload="beforeIpUpload"
          :on-success="ipUploadSuccess"
          :on-remove="uploadRemove"
          :on-error="ipUploadError"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">支持上传xlsx,csv 文件，且大小不超过20M</div>
        </el-upload>
      </div>
      <span @click="createHutter" id="unit_hunter">生成Hunter查询语句</span>
      <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisible = false" id="unit_export_cancel"
          >关闭</el-button
        >
        <!-- <el-button class="highBtn" @click="otherDataSave" id="unit_export_save">确定</el-button> -->
        <el-button
          :loading="otherLoading"
          class="highBtn"
          @click="otherDataSave"
          id="unit_export_save"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      class="elDialogAdd"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleHutter"
      width="650px"
    >
      <template slot="title"> 查询语句 </template>
      <div class="dialog-body" v-loading="hunterLoading">
        <p class="hunterClass" v-for="(item, index) in hunterData" :key="index">
          {{ index + 1 }}、{{ item }}
          <span class="el-icon-document-copy" @click="copyHunter(item)"></span>
        </p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="highBtnRe"
          @click="dialogFormVisibleHutter = false"
          id="unit_hutter_cancel"
          >关闭</el-button
        >
        <el-button class="highBtn" @click="copyClick(hunterData)">一键复制</el-button>
      </div>
    </el-dialog>
  </div>
  <div v-else class="fourthBox">
    <div class="fourthTitle">
      <div class="grayBox">
        <div>
          <span>云端资产推荐><span style="color: #2677ff; font-weight: 600">推荐资产</span></span
          ><img src="../../assets/images/triangle.png" alt="" class="graySan" /><span
            class="graylabel"
            >依据资产线索获取云端资产信息</span
          >
        </div>
      </div>
    </div>
    <div class="tu yuTu tuThird" lazy></div>
    <div style="margin: 12px 8px 10px 0px"
      ><span style="margin: 0px 8px 0px 0px">{{ headText }}</span
      ><span style="color: #2677ff" v-if="currentPercent != 0">{{ currentPercent }}%</span></div
    >
    <div class="fourthProgressBox"
      ><el-progress
        :stroke-width="14"
        :percentage="currentPercent"
        style="margin-bottom: 20px"
      ></el-progress
    ></div>
  </div>
</template>
<script>
import * as animationData from '../../assets/images/data.json'
import Lottie from 'vue-lottie/src/lottie.vue'
import tableList from '../home_set/detectIndexTable.vue'
import highCheckDrawerScan from '../home_set/highCheck.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { getScanList, delScanList } from '@/api/apiConfig/api.js'
import { regRecommendClues } from '@/api/apiConfig/recommend.js'
import {
  endDetectTask,
  cloudAssetsPredict,
  cloudAssetsHunter,
  cloudAssetsConfirm
} from '@/api/apiConfig/surveying.js'

export default {
  components: { Lottie, tableList, highCheckDrawerScan },
  props: ['taskInfoData', 'expand_source', 'expandType'],
  data() {
    return {
      isClearSelection: true,
      otherDataCurrProgress: 0,
      isOtherDataUploading: false,
      cluesList: {},
      formInline: {
        ip: '',
        clue_company_name: '',
        province: '', // 省份名称（传汉字）,
        keyword: '', // 123123,
        asn: '', // 123123,
        cert: '', // cert,
        icp: '', // icp,
        url: '',
        title: '', // title,
        protocol: '', // protocol,
        logo: '', // logo,
        port: '', // port,
        cname: '', // cname,
        domain: [], // domain
        latitude: '',
        longitude: '',
        state: '',
        reason: '',
        reason_type: '', // 证据链
        tags: '',
        hosts: '',
        rule_tags: [],
        last_update_time: [],
        second_confirm: '' // second_confirm  0/待确认 1/已经确认
      },
      headText: '正在推荐资产数据',
      isScan: false,
      highCheckdialog: false,
      hunterLoading: false,
      loading: false,
      currentPercent: 0,
      defaultOptions: {
        //json动画
        animationData: animationData.default
      },
      uploadPath: [],
      uploadAction: `${this.uploadSrcIp}/detect_assets/cloud/other/files?encode=0`,
      uploadHeaders: { Authorization: localStorage.getItem('token') },
      uploadMaxCount: 10,
      fileList: [],
      otherLoading: false,
      dialogFormVisible: false,
      dialogFormVisibleHutter: false,
      hunterData: [],
      progressBar: false, //是否显示进度条
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizeArr: [10, 30, 50, 100],
      tableData: [],
      checkedArr: [],
      checkedAll: false,
      setTimer: null,
      successIp: ''
    }
  },
  watch: {
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    taskInfoData(val) {
      this.getInit()
    }
  },
  computed: {
    ...mapState(['currentCompany', 'recommentFlag']),
    ...mapGetters(['getterWebsocketMessage', 'getterRecommentFlag'])
  },
  beforeDestroy() {},
  mounted() {
    this.getInit()
  },
  methods: {
    ...mapMutations(['recommentFlagChange']),
    removeMore() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'account_del_cancel',
        confirmButtonClass: 'account_del_sure',
        customClass: 'account_del',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            flag: this.taskInfoData.expend_flags,
            data: {
              ip_array: this.checkedArr.map((item) => {
                return item.ip
              }),
              whole: this.checkedAll ? 1 : '',
              operate_company_id: this.currentCompany,
              ...this.formInline
            }
          }
          this.loading = true
          let res = await delScanList(obj).catch(() => {
            this.loading = false
          })
          if (res.code == 0) {
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.$message.success('删除成功！')
            this.loading = false
            this.getcloudAssets('yesLoading')
          }
        })
        .catch(() => {})
    },
    highCheckClick() {
      this.highCheckdialog = true
      this.getRecommendCluesData()
    },
    // 高级筛选条件
    async getRecommendCluesData() {
      // 线索
      this.loading = true
      let res = await regRecommendClues({
        flag: this.taskInfoData.expend_flags,
        data: {
          operate_company_id: this.currentCompany
        }
      }).catch(() => {
        this.loading = false
      })
      this.loading = false
      if (res.data && res.data.logo) {
        res.data.logo.forEach((item, index) => {
          item.id = index + 1
        })
      }
      this.cluesList = res.data ? res.data : []
    },
    highCheck() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.getcloudAssets('yesLoading')
    },
    highIsClose() {
      this.highCheckdialog = false
    },
    getInit() {
      // 300|0未完成推荐显示进度，300|1已完成推荐不显示进度
      if (this.taskInfoData) {
        if (this.taskInfoData.step_detail == 300) {
          this.currentPercent =
            this.taskInfoData.step_status == 0 ? this.taskInfoData.expend_progress / 1 : 0
          this.progressBar = false
          if (this.taskInfoData.step_status == 0) {
            // 未推荐完成定时请求
            if (this.taskInfoData.is_intellect_mode == 1) {
              // 智能模式
              this.isScan = true
              this.headText = '正在进行资产推荐…'
            } else {
              this.isScan = true // 正在推荐，控制特效图
              this.getcloudAssets('yesLoading')
            }
          } else {
            if (this.taskInfoData.is_intellect_mode == 1) {
              // 智能模式
              this.isScan = true
              this.headText = '推荐完成，准备资产评估'
            } else {
              this.isScan = false
              this.getcloudAssets('yesLoading')
            }
          }
        } else if (this.taskInfoData.step_detail == 301) {
          // 资产融合
          this.isScan = false
          this.currentPercent =
            this.taskInfoData.step_status == 0 ? this.taskInfoData.expend_progress / 1 : 0
          this.progressBar = this.taskInfoData.step_status == 0 ? true : false
          this.getcloudAssets('yesLoading')
        }
      } else {
        this.isScan = false
        this.getcloudAssets('yesLoading')
      }
    },
    async endTask() {
      this.$confirm('确定结束流程?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let obj = {
            expend_id: this.taskInfoData.id,
            operate_company_id: this.currentCompany
          }
          let res = await endDetectTask(obj)
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.$emit('son', '1')
          }
        })
        .catch(() => {})
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (res.cmd == 'recommend_progress' && res.data && this.taskInfoData.id == res.data.task_id) {
        //单位资产测绘-云端推荐
        this.recommentFlagChange(res.data.flag) // 存储云端推荐flag
        if (res.data.status == 2) {
          this.progressBar = false // 进度条不显示
          this.currentId = '' // 控制推送结束后仅执行一次
          if (this.taskInfoData.is_intellect_mode == 1) {
            // 智能模式
            this.headText = '推荐完成，准备资产评估'
            setTimeout(() => {
              this.$emit('son', '4', this.taskInfoData.id) // 每个流程结束需要主动请求详情接口，获取最新进度
            }, 3000)
          } else {
            this.isScan = false // 已经推荐完成，控制特效图
            if (this.$route.path != '/assetsCloud') {
              // 单位资产测绘
              this.loading = true
              setTimeout(() => {
                this.getcloudAssets('yesLoading')
              }, 1500)
              this.$confirm('推荐成功！是否需要导入第三方数据?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
                .then(async () => {
                  this.uploadOtherPlate()
                })
                .catch(() => {})
            } else {
              // 云端推荐
              this.loading = true
              setTimeout(() => {
                this.getcloudAssets('yesLoading')
              }, 1500)
            }
          }
        } else if (res.data.status == 1) {
          // 正在扫描
          // 推送进度
          this.currentId = res.data.user_id
          this.progressBar = false // 进度条不显示
          this.isScan = true
          // 推送进度;因后端拆分任务扫描，会有多个进度而导致进度回退，所以小于当前进度不显示
          // 进度调优显示
          this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        } else if (res.data.status == 4) {
          // 暂停扫描
        } else {
          // 3 扫描失败
          this.getcloudAssets('yesLoading')
        }
      } else if (
        res.cmd == 'detect_recommend_assets_import' &&
        res.data &&
        this.taskInfoData.id == res.data.detect_task_id
      ) {
        if (res.data.progress == 100) {
          this.otherDataCurrProgress = 99.9
          setTimeout(() => {
            this.otherDataCurrProgress = 100
            this.isOtherDataUploading = false
            this.$message.success(`成功导入${this.successIp}个IP`)
          }, 100)
        } else {
          // 正在上传中
          this.isOtherDataUploading = true
          this.otherDataCurrProgress = this.setProgress(
            this.otherDataCurrProgress,
            res.data.progress
          )
        }
      }
    },
    async getcloudAssets(isLoading) {
      // 获取云端推荐列表
      let that = this
      let obj = {
        flag: this.recommentFlag,
        query: {
          level: '',
          page: this.currentPage,
          per_page: this.pageSize,
          expand_source: this.taskInfoData.expand_source,
          operate_company_id: this.currentCompany,
          ...this.formInline
        }
      }
      this.loading = isLoading == 'yesLoading' ? true : false
      let res = await getScanList(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        let arr = []
        for (let key in res.data.items) {
          let chain_list = []
          if (res.data.items[key]) {
            // 端口的chain_list聚合去重
            res.data.items[key].forEach((item) => {
              if (item.chain_list) {
                chain_list.push(...item.chain_list)
              }
            })
          }
          let resetChainList = that.uniqChainArr(chain_list) // 多条线索链去重
          arr.push({
            is_cdn: res.data.items[key][0].is_cdn ? res.data.items[key][0].is_cdn : false,
            ip: key,
            id: res.data.items[key][0].id,
            chain_list: resetChainList,
            clue_company_name: res.data.items[key][0].clue_company_name,
            detail: res.data.items[key],
            rule_tags: [],
            hosts: [],
            isExpand: false,
            myPopover: false,
            myPopoverFlag: true,
            cloud_name: res.data.items[key][0].cloud_name
          })
        }
        this.tableData = arr.reverse()
        this.total = res.data.total
      }
    },
    // 多条线索链去重
    uniqChainArr(arr) {
      let newarr = [] //盛放去重后数据的新数组
      for (let el of arr) {
        //循环arr数组对象的内容, i是数组
        let flag = true //建立标记，判断数据是否重复，true为不重复
        for (let elk of newarr) {
          //循环新数组的内容
          let reg =
            String(
              el.map((item) => {
                return item.content
              })
            ) ==
            String(
              elk.map((item) => {
                return item.content
              })
            )
          if (reg) {
            flag = false
          }
        }
        if (flag) {
          //判断是否重复
          newarr.push(el) //不重复的放入新数组。  新数组的内容会继续进行上边的循环。
        }
      }
      return newarr
    },
    async createHutter() {
      this.dialogFormVisibleHutter = true
      this.hunterLoading = true
      let res = await cloudAssetsHunter({
        id: this.taskInfoData.id,
        data: {
          operate_company_id: this.currentCompany
        }
      }).catch(() => {
        this.hunterLoading = false
      })
      if (res.code == 0) {
        this.hunterLoading = false
        this.hunterData = res.data
      }
    },
    copyClick(data) {
      let that = this
      let text = ''
      data.forEach((item, index) => {
        text += item + '\n'
      })
      this.$copyText(text).then(
        (res) => {
          that.$message.success('查询语句已复制到剪贴板！')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    copyHunter(val) {
      let that = this
      this.$copyText(val).then(
        function (e) {
          that.$message.success('查询语句已复制到剪贴板！')
        },
        function (e) {}
      )
    },
    uploadOtherPlate() {
      this.dialogFormVisible = true
      this.fileList = []
      this.uploadPath = []
    },
    // 三方导入数据保存
    async otherDataSave() {
      if (this.uploadPath.length == 0) {
        this.$message.error('请上传文件')
        return
      }
      this.otherLoading = true
      this.isOtherDataUploading = false
      let res = await cloudAssetsConfirm({
        file: this.uploadPath,
        detect_task_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.otherLoading = false
      })
      if (res.code == 0) {
        this.successIp = res.data.count_success_ips
        this.$message.success(`成功导入${res.data.count_success_ips}个IP`)
        this.dialogFormVisible = false
        this.otherLoading = false
        // setTimeout(() => { // 解决导入后列表不能及时更新的问题
        //     this.dialogFormVisible = false
        //     this.otherLoading = false
        this.getcloudAssets('yesLoading')
        // }, 3000)
      }else {
        this.otherLoading = false
      }
    },
    // 三方导入模板下载
    downloadOther(name) {
      window.location.href = `/downloadTemplate/${name}`
    },
    beforeIpUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 20
      if (!isLt1M) {
        this.$message.error('上传文件不能超过 20MB!')
      }
      return isLt1M
    },
    ipUploadSuccess(response, file, fileList) {
      if (file.response.code == 0) {
        this.$message.success('上传成功')
        if (file.response && file.response.data && file.response.data.url) {
          this.uploadPath.push(file.response.data.url)
        }
      } else {
        this.$message.error(file.response.message)
      }
    },
    uploadRemove(file, fileList) {
      let res = fileList.map((item) => {
        return item.response.data
      })
      this.uploadPath = res.map((item) => {
        return item.url
      })
    },
    ipUploadError(err, file, fileList) {
      this.$message.error(JSON.parse(err.message).message)
    },
    removeOne() {},
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.isClearSelection = false
      this.pageSize = val
      this.getcloudAssets('yesLoading')
    },
    handleCurrentChange(val) {
      this.isClearSelection = false
      this.currentPage = val
      this.getcloudAssets('yesLoading')
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    // 资产信任度评估
    async goPredict() {
      if (this.otherLoading) return this.$message.warning('请等待三方导入完成')
      this.loading = true
      let obj = {
        expend_id: this.taskInfoData.id,
        operate_company_id: this.currentCompany
      }
      let res = await cloudAssetsPredict(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.$emit('son', '4', this.taskInfoData.id, this.expandType) // 执行第四步
      }
    },
    // 三方导入弹窗关闭
    closeOtherDataDialog() {
      if (this.otherDataCurrProgress == 100 || !this.otherLoading) return
      this.isOtherDataUploading = true
    }
  }
}
</script>
<style lang="less" scoped>
#unit_hunter {
  display: inline-block;
  color: #2677ff;
  padding: 10px 0;
  cursor: pointer;
}
.hunterClass {
  position: relative;
  padding: 0 0 0 10px;
  line-height: 30px;
  .el-icon-document-copy {
    position: absolute;
    top: 8px;
    left: -10px;
    color: #2677ff;
    cursor: pointer;
  }
}
.tu {
  animation: running 6s steps(149) infinite;
  -webkit-animation: running 6s steps(149) infinite;
}
@keyframes running {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -17880px;
  }
}
.fourthBox {
  height: 100%;
  background: -webkit-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -o-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: -moz-linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .fourthTitle {
    position: absolute;
    top: 20px;
    left: 0px;
  }
}
.fourthProgressBox {
  width: 400px;
}
.fourthProgressBox /deep/.el-progress__text {
  display: none;
}
.tisi {
  font-size: 12px;
  font-weight: 400;
  color: rgba(98, 102, 108, 0.7);
}
.yuTu {
  animation: running1 6s steps(149) infinite;
  -webkit-animation: running1 6s steps(149) infinite;
}
@keyframes running1 {
  0% {
    background-position-y: 0px;
  }
  100% {
    background-position-y: -26820px;
  }
}
.boxTwo {
  height: calc(100% - 208px) !important;
}
</style>
