<template>
  <div class="container">
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="Advanced"
            id="keyword_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="keyword_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <!-- <el-checkbox class="checkboxAll" v-model="checkedAll" @change="checkAllChange" id="keyword_all">选择全部</el-checkbox> -->
          <el-button
            class="normalBtnRe"
            type="primary"
            @click="remove('more')"
            id="keyword_more_del"
            >删除</el-button
          >
        </div>
      </div>
      <!-- 高级筛选条件 -->
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="keyword"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <!-- 只有单位资产测绘才有企业名称 -->
          <el-table-column
            v-for="item in tableHeader.filter((item) => {
              return !item.path || item.path == $route.path
            })"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'step'">{{ getStep(scope.row) }}</span>
              <span v-else-if="item.name == 'relate_task'">{{
                Number(scope.row.is_check_risk) +
                Number(scope.row.is_auto_data_assets) +
                Number(scope.row.is_auto_leak_assets) +
                Number(scope.row.is_auto_domain_brust)
              }}</span>
              <span v-else>{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="left" label="操作" width="200">
            <template slot-scope="scope">
              <!-- 只有已完成的才有生成报告 -->
              <el-button
                v-if="scope.row.step == 5 && scope.row.status == 2"
                type="text"
                size="small"
                @click="createReport(scope.row.id)"
                id="keyword_del"
                >生成报告</el-button
              >
              <!-- 已完成的没有查看详情 -->
              <el-button
                v-if="!(scope.row.step == 5 && scope.row.status == 2)"
                type="text"
                size="small"
                @click="reviewInfo(scope.row, '1')"
                id="keyword_del"
                >查看详情</el-button
              >
              <!-- 只有云端推荐已完成的才有推荐资产 -->
              <el-button
                v-if="
                  (scope.row.step == 3 &&
                    (scope.row.step_detail == 300 || scope.row.step_detail == 301) &&
                    scope.row.step_status == 1) ||
                  scope.row.step > 3
                "
                type="text"
                size="small"
                @click="reviewInfo(scope.row, '2')"
                id="keyword_del"
                >推荐记录</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="remove('one', scope.row.id)"
                id="keyword_del"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <tableTooltip :tableCellMouse="tableCellMouse"></tableTooltip>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <!-- <el-form-item label="状态：" prop="status">
            <el-select filterable v-model="formInline.status" placeholder="请选择" clearable @change="selectChange($event, 'status', statusArr, true, false)">
              <el-option v-for="item in statusArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="添加时间：" prop="created_at_range">
            <el-date-picker
              v-model="formInline.created_at_range"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button
            class="highBtnRe"
            @click="resetForm('drawerForm')"
            id="keyword_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="keyword_filter_select"
            >筛选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import {
  detectTaskList,
  cloudAssetsReport,
  detectTaskInfo,
  delDetectTask
} from '@/api/apiConfig/surveying.js'

export default {
  components: {
    tableTooltip,
    hightFilter
  },
  data() {
    return {
      highTabShow: [
        {
          label: '添加时间',
          name: 'created_at_range',
          type: 'date'
        }
      ],
      highlist: null,
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      formInline: {
        id: [],
        page: 1,
        per_page: 10,
        keyword: '',
        operate_company_id: ''
      },
      checkedAll: false,
      highCheckdialog: false,
      tableHeader: [
        {
          label: '企业名称',
          name: 'name',
          minWidth: 120,
          path: '/unitIndex'
        },
        {
          label: '线索数量',
          name: 'clues_count',
          minWidth: 60
        },
        {
          label: '进度',
          name: 'step',
          minWidth: 120
        },
        {
          label: '台账资产',
          name: 'sure_ip_num',
          minWidth: 60
        },
        {
          label: '疑似资产',
          name: 'unsure_ip_num',
          minWidth: 60
        },
        {
          label: '威胁资产',
          name: 'threaten_ip_num',
          minWidth: 60
        },
        {
          label: '关联任务',
          name: 'relate_task'
        },
        {
          label: '发起人',
          name: 'username'
        },
        {
          label: '发起时间',
          name: 'created_at',
          minWidth: 120
        }
      ],
      statusArr: [
        // 状态 0/1 禁用/启用
        {
          name: '禁用',
          id: 0
        },
        {
          name: '启用',
          id: 1
        }
      ],
      checkedArr: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      ruleForm: {
        way: '0',
        name: ''
      },
      rules: {
        name: [{ required: true, message: ' ', trigger: 'blur' }]
      },
      loading: false,
      otherLoading: false,
      editFlag: false,
      user: {
        role: ''
      },
      taskInfoData: {}
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
    this.getData()
    this.getcehui()
  },
  watch: {
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
        this.getcehui()
      }
    },
    tableData(val) {
      this.doLayout(this, 'eltable')
    },
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    }
  },
  computed: {
    ...mapState(['currentCompany', 'recommentFlag']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  methods: {
    handleMessage(res, o) {
      //处理接收到的信息
      if (
        res.cmd == 'detect_assets_tip1_process' ||
        res.cmd == 'detect_assets_tip2_domain' ||
        res.cmd == 'detect_assets_tip2_icp' ||
        res.cmd == 'detect_assets_tip2_cert' ||
        res.cmd == 'detect_assets_tip2_all' ||
        (res.cmd == 'detect_assets_tip2_ip' && res.data && res.data.task_id == this.taskInfoData.id)
      ) {
        if (parseInt(res.data.progress) == '100') {
          this.getData()
        }
      }
      if (res.cmd == 'recommend_progress' && res.data && this.recommentFlag == res.data.flag) {
        if (res.data.status == 2) {
          this.getData()
        }
      }
      if (
        res.cmd == 'detect_assets_tasks' &&
        res.data &&
        res.data.detect_assets_tasks_id == this.taskInfoData.id
      ) {
        if (res.data.status == 2) {
          this.getData()
        }
      }
      if (res.cmd == 'update_assets_level' && res.data && res.data.flag == this.recommentFlag) {
        if (res.data.status == 2) {
          this.getData()
        }
      }
    },
    async getcehui() {
      let obj = {
        taskId: '',
        data: {
          expand_source: this.$route.meta.expand_source, // 1云端推荐，0单位资产测绘
          operate_company_id: this.currentCompany
        }
      }
      let res = await detectTaskInfo(obj)
      if (res.code == 0) {
        this.taskInfoData = res.data
      }
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    async createReport(id) {
      let obj = {
        expend_id: id,
        operate_company_id: this.currentCompany
      }
      let res = await cloudAssetsReport(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
      }
    },
    // 查看详情
    reviewInfo(item, icon) {
      if (icon == 1) {
        this.$emit('son', item.id) // 切换到流程测绘页面，把任务记录id传过去
      } else {
        // pageIcon: 1代表是单位测绘和云端推荐画报跳转过去的，不显示执行扫描按钮
        this.$router.push({ path: '/scanReg', query: { pageIocn: '1', flag: item.expend_flags } })
      }
    },
    // 进度详情
    getStep(row) {
      let str = ''
      if (row.step == 2) {
        if (row.step_detail == 200) {
          str = row.step_status == 1 ? '获取基础线索已完成' : '获取基础线索未完成'
        } else if (row.step_detail == 201) {
          str = row.step_status == 1 ? '域名扩展已完成' : '正在进行域名扩展'
        } else if (row.step_detail == 202) {
          str = row.step_status == 1 ? 'ICP扩展已完成' : '正在进行ICP扩展'
        } else if (row.step_detail == 203) {
          str = row.step_status == 1 ? '证书扩展已完成' : '正在进行证书扩展'
        } else if (row.step_detail == 204) {
          str = row.step_status == 1 ? '扩展线索已完成' : '扩展线索未完成'
        } else if (row.step_detail == 205) {
          str = row.step_status == 1 ? '线索总表已生成' : '线索总表未生成'
        } else if (row.step_detail == 206) {
          str = row.step_status == 1 ? 'IP段扩展已完成' : '正在进行IP段扩展'
        }
      } else if (row.step == 3) {
        if (row.step_detail == 300) {
          str = row.step_status == 1 ? '云端推荐已完成' : '云端推荐未完成'
        } else if (row.step_detail == 301) {
          str = row.step_status == 1 ? '三方导入数据融合已完成' : '三方导入数据融合未完成'
        }
      } else if (row.step == 4) {
        if (row.step_detail == 400) {
          str = row.step_status == 1 ? '资产评估已完成' : '资产评估未完成'
        } else if (row.step_detail == 401) {
          str = row.step_status == 1 ? '资产入账扫描已完成' : '资产入账扫描未完成'
        }
      } else if (row.step == 5) {
        if (row.status == 0) {
          str = '关联任务自定义未完成'
        } else {
          str = '已完成'
        }
      }
      return str
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    getData() {
      this.loading = true
      this.formInline.page = this.currentPage
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.expand_source = this.$route.meta.expand_source
      detectTaskList(this.formInline)
        .then((res) => {
          this.loading = false
          this.tableData = res.data.items
          this.total = res.data.total
          // 全选操作
          if (this.checkedAll) {
            this.tableData.forEach((row) => {
              this.$refs.eltable.toggleRowSelection(row, true)
            })
          }
        })
        .catch((error) => {
          this.tableData = []
          this.total = 0
          this.loading = false
        })
    },
    async remove(icon, id) {
      if (icon == 'more') {
        if (this.checkedArr.length == 0) {
          this.$message.error('请选择数据！')
          return
        }
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'keyword_del_cancel',
        confirmButtonClass: 'keyword_del_sure',
        customClass: 'keyword_del',
        type: 'warning'
      })
        .then(async () => {
          this.formInline.page = this.currentPage
          this.formInline.per_page = this.pageSize
          this.formInline.expand_source = this.$route.meta.expand_source
          if (this.checkedAll) {
            this.formInline.id = icon == 'one' ? [id] : []
          } else {
            this.formInline.id =
              icon == 'one'
                ? [id]
                : this.checkedArr.map((item) => {
                    return item.id
                  })
          }
          let obj = Object.assign({}, this.formInline)
          this.loading = true
          let res = await delDetectTask(obj).catch(() => {
            this.loading = false
          })
          if (res.code == 0) {
            this.$message.success('操作成功！')
            this.loading = false
            this.checkedAll = false
            this.$refs.eltable.clearSelection()
            this.currentPage = this.updateCurrenPage(
              this.total,
              this.checkedArr,
              this.currentPage,
              this.pageSize
            ) // 更新页码
            this.getData()
          }
        })
        .catch(() => {})
      setTimeout(() => {
        var del = document.querySelector('.keyword_del>.el-message-box__btns')
        del.children[0].id = 'keyword_del_cancel'
        del.children[1].id = 'keyword_del_sure'
      }, 50)
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.formInline = {
        id: [],
        page: 1,
        per_page: 10,
        keyword: '',
        operate_company_id: ''
      }
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
          display: flex;
          align-items: center;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
    }
    .el-table {
      border: 0;
      .el-table__body td.el-table__cell div {
        padding: 12px !important;
      }
    }
  }
}
</style>
