<template>
  <div class="container">
    <div class="headerTitle">升级中心</div>
    <div class="home_header">
      <div class="localiHeader">
        <div>
          <div>
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-bianzu"></use>
            </svg>
            <span style="margin-left: 12px"> 互联网资产攻击面管理平台 </span>
          </div>
          <el-button class="normalBtnRe" type="primary" id="scan_more_del" @click="upgradeHistory"
            >升级历史记录</el-button
          >
        </div>
        <p class="pSpecial textSpecial"
          >当前版本： <span>{{ uploadSetObj.version }}</span></p
        >
        <p
          >最近更新时间：<span>{{ uploadSetObj.time }}</span></p
        >
        <p class="textSpecial"
          >更新方式：
          <span>{{
            uploadSetObj.type == 1
              ? '在线升级：在线获取可以升级的版本列表，选择后自动进行对应版本的升级'
              : uploadSetObj.type == 2
                ? '离线升级：需要手动上传升级文件进行升级'
                : ''
          }}</span></p
        >
      </div>
      <div class="localiMain">
        <div class="mainTitle">
          <span>选择升级方式</span>
          <div class="uploadMessage">升级前请确保无正在进行或者等待的任务</div>
        </div>
        <div class="mainUploader">
          <div @click="linClick">
            <p><img src="../../assets/images/localized/green.png" alt="" /><span>在线升级</span></p>
            <p>在线获取可以升级的版本列表，选择后自动进行对应版本的升级</p>
            <img src="../../assets/images/localized/line.png" alt="" class="statusImg" />
          </div>
          <div class="loaderSpecial" @click="noLineClick">
            <p><img src="../../assets/images/localized/gray.png" alt="" /><span>离线升级</span></p>
            <p>需要手动上传升级文件进行升级</p>
            <img src="../../assets/images/localized/noline.png" alt="" class="statusImg" />
          </div>
        </div>
      </div>
      <el-dialog
        class="elDialogAdd"
        :close-on-click-modal="false"
        :visible.sync="dialogFormVisible"
        :before-close="dialogFormVisibleClose"
        :width="'700px'"
      >
        <template slot="title"> 在线升级列表 </template>
        <div class="dialog-body1" v-loading="loading">
          <el-table
            border
            :data="tableData"
            row-key="id"
            :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
            ref="eltable"
            height="350px"
            style="width: 100%"
          >
            <el-table-column
              v-for="item in tableHeaderList"
              :key="item.id"
              :prop="item.name"
              align="left"
              :show-overflow-tooltip="true"
              :min-width="item.minWidtth"
              :label="item.label"
            >
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="left" width="80">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="updateList(scope.row)" id="scan_del"
                  >升级</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- <div slot="footer" class="dialog-footer">
        <el-button class="highBtnRe" @click="dialogFormVisibleClose" id="scan_cancel">关闭</el-button>
        <el-button class="highBtn"  @click="insertSave" id="scan_sure">确定</el-button>
      </div> -->
      </el-dialog>
      <el-drawer title="升级历史记录" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
        <div class="hostoryMain">
          <div class="historyBox" v-for="(item, key) in historyData" :key="key">
            <p class="specialVersion"
              >版本号：<span>{{ item.version }}</span></p
            >
            <p
              >发布时间：<span>{{ item.created_at }}</span></p
            >
            <p
              >升级时间：<span>{{ item.updated_at }}</span></p
            >
            <div style="margin-top: 10px">升级内容：</div>
            <div style="margin-top: 5px">{{ item.content }}</div>
          </div>
        </div>
      </el-drawer>
      <upgradeUpload
        :uploadVisible="uploadVisible"
        @uploadVisibleClose="uploadVisibleClose"
        @saveSuccess="saveSuccess"
        :uploadIdObj="uploadIdObj"
        :uploadFlag="false"
      ></upgradeUpload>
    </div>
  </div>
</template>

<script>
import upgradeUpload from './upgradeUpload.vue'
import { mapGetters, mapState } from 'vuex'
import { getupgradeList, getupgradeHistory, getLineUpdate } from '@/api/apiConfig/api.js'

export default {
  components: { upgradeUpload },
  data() {
    return {
      dialogFormVisible: false,
      uploadVisible: false,
      loading: false,
      highCheckdialog: false,
      tableData: [],
      uploadIdObj: {},
      uploadSetObj: {
        version: '',
        time: '',
        type: ''
      },
      historyData: [],
      tableHeaderList: [
        {
          label: '升级版本',
          name: 'version',
          minWidtth: '80'
        },
        {
          label: '升级内容',
          name: 'content',
          minWidtth: '80'
        },
        {
          label: '发布时间',
          name: 'created_at',
          minWidtth: '80'
        }
      ]
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    async linClick() {
      this.loading = true
      this.dialogFormVisible = true
      let res = await getupgradeList({ operate_company_id: this.currentCompany })
      this.loading = false
      if (res.code == 0) {
        this.tableData = res.data
      }
    },
    async updateList(row) {
      this.loading = true
      let res = await getLineUpdate({
        version: row.version,
        address: row.address,
        operate_company_id: this.currentCompany
      })
      this.loading = false
      if (res.code == 0) {
        this.dialogFormVisible = false
        this.$router.push({
          path: '/upgrading'
        })
      }
    },
    saveSuccess() {
      this.uploadVisible = false
    },
    dialogFormVisibleClose() {
      this.dialogFormVisible = false
    },
    noLineClick() {
      this.uploadVisible = true
    },
    uploadVisibleClose(val) {
      this.uploadVisible = false
    },
    upgradeHistory() {
      this.highCheckdialog = true
    },
    async getTablist() {
      let res = await getupgradeHistory({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        //   uploadSetObj:{
        //   version:'',
        //   time:'',
        //   type:''
        // },
        this.uploadSetObj.version = res.data.current
        this.uploadSetObj.time = res.data.upgrade_time
        this.uploadSetObj.type = res.data.type
        this.historyData = res.data.list.data
      }
    }
    // insertSave() {}
  },
  mounted() {
    this.getTablist()
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .downloadClass1 {
    position: absolute;
    top: -41.5px;
    right: 0px;
    height: 40px;
    line-height: 40px;
    color: #62666c;

    i {
      font-size: 14px;
      color: #2677ff;
      margin: 0 8px 0 16px;
    }
    .el-icon-close {
      position: absolute;
      right: 10px;
      top: 12px;
      font-size: 16px;
      color: #9fa6af;
      // font-weight: bold;
    }
    span {
      color: #2677ff;
    }
    .task {
      display: inline-block;
      max-width: 186px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #37393c;
    }
  }
  /deep/.el-dialog__body {
    padding: 16px 28px 28px 28px !important;
  }
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    // padding: 20px;
    box-sizing: border-box;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 60px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}
.localiHeader {
  width: 100%;
  height: 27%;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  padding: 32px;
  div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 20px;
    font-weight: 600;
    color: rgba(55, 57, 60, 1);
    box-sizing: border-box;
    .icon {
      font-size: 33px;
    }
  }
  p {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    // line-height: 0px;
    color: rgba(98, 102, 108, 1);
    margin-top: 15px;
    span {
      color: rgba(55, 57, 60, 1);
    }
  }
  .textSpecial {
    span {
      margin-left: 22px;
    }
  }
  .pSpecial {
    margin-top: 18px;
  }
}
.localiMain {
  width: 100%;
  height: 73%;
  box-sizing: border-box;
  padding: 32px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%),
    linear-gradient(90deg, rgba(240, 246, 255, 1) 0%, rgba(255, 255, 255, 1) 100%);
  .mainTitle {
    display: flex;
    width: 100%;
    height: 28px;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: rgba(55, 57, 60, 1);
  }
  .mainUploader {
    width: 100%;
    height: 50%;
    display: flex;
    justify-content: center;
    margin-top: 8%;
    div {
      cursor: pointer;
      width: 38.7%;
      height: 100%;
      border-radius: 4px;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
      background-image: url('../../assets/images/localized/banner.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: relative;
      p:nth-child(2) {
        font-size: 14px;
        font-weight: 400;
        color: rgba(55, 57, 60, 1);
      }
      p {
        box-sizing: border-box;
        padding: 20px 0 0 20px;
        display: flex;
        align-items: center;
        img {
          width: 8px;
          height: 8px;
        }
        span {
          margin-left: 9px;
          font-size: 14px;
          font-weight: 500;
          color: rgba(55, 57, 60, 1);
        }
      }
      .statusImg {
        width: 115px;
        height: 122px;
        position: absolute;
        bottom: 0;
        right: 17px;
      }
    }
  }
  .uploadMessage {
    width: 238px;
    height: 28px;
    background: rgba(235, 238, 245, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 400;
    color: rgba(98, 102, 108, 1);
    margin-left: 4px;
  }
  .loaderSpecial {
    margin-left: 31px;
  }
}
.hostoryMain {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
}
.historyBox {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  background: rgba(255, 255, 255, 1);
  margin-top: 15px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
  p {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(55, 57, 60, 1);
  }
}
.specialVersion {
  font-size: 16px;
  font-weight: 500;
  color: rgba(55, 57, 60, 1);
  span {
    // margin-left: 9px;
    font-size: 16px;
    font-weight: 500;
    color: rgba(55, 57, 60, 1);
  }
}
.dialog-body1 {
  min-height: 300px;
  max-height: 500px;
}
</style>
