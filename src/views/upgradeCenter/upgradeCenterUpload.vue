<template>
  <div class="wrapper">
    <upgradeCenter v-if="!centerShowFlag"></upgradeCenter>
    <localizedDeployment v-if="centerShowFlag"></localizedDeployment>
  </div>
</template>

<script>
import upgradeCenter from './upgradeCenter'
import localizedDeployment from './localizedDeployment.vue'

export default {
  components: { upgradeCenter, localizedDeployment },
  data() {
    return {
      centerShowFlag: false
    }
  },
  mounted() {
    if (sessionStorage.getItem('userMessage')) {
      this.centerShowFlag = JSON.parse(sessionStorage.getItem('userMessage')).is_local
    }
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
}
</style>
