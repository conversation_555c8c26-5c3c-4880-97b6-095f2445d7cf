<template v-loading="mainLoading">
  <el-dialog
    class="elDialogAdd"
    :close-on-click-modal="false"
    :visible.sync="uploadVisible"
    :before-close="uploadVisibleClose"
    :width="'585px'"
  >
    <template slot="title"> 上传升级文件 </template>
    <div class="dialog-body" v-loading="loading">
      <el-form
        :model="uploadForm"
        :rules="rules1"
        style="padding: 0 !important"
        ref="addRuleForm"
        label-width="120px"
        class="demo-ruleForm"
        v-if="uploadFlag"
      >
        <el-form-item label="版本号" prop="version">
          <el-input v-model="uploadForm.version" placeholder="请输入版本号"></el-input>
        </el-form-item>
        <el-form-item label="升级内容" prop="content">
          <el-input
            v-model="uploadForm.content"
            placeholder="请输入升级内容"
            type="textarea"
          ></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="uploadForm.type" placeholder="请选择">
            <!-- <el-option :key="0" :label="'FORadar升级'" :value="'1'"></el-option> -->
            <el-option :key="1" :label="'Fd01升级'" :value="'2'"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <el-form
        :model="ruleForm"
        :rules="rules"
        style="padding: 0 !important"
        ref="addRuleForm"
        label-width="120px"
        class="demo-ruleForm"
        v-if="uploadFlag"
      >
        <el-form-item label="镜像包名称" prop="file_name">
          <el-input v-model="ruleForm.file_name" placeholder="请输入镜像包名称"></el-input>
        </el-form-item>
        <el-form-item label="镜像版本号" prop="version">
          <el-input v-model="ruleForm.version" placeholder="请输入镜像版本号"></el-input>
        </el-form-item>
        <el-form-item label="镜像名称" prop="image_name">
          <el-input v-model="ruleForm.image_name" placeholder="请输入镜像名称"></el-input>
        </el-form-item>
      </el-form>
      <div class="uploadClass">
        <div class="uploadLeft" v-if="uploadFlag">升级包</div>
        <div :class="uploadFlag ? 'uploadRight' : 'uploadRight uploadRightSpecial'">
          <uploader
            :options="options"
            class="uploader-example"
            @file-added="onFileAdded"
            @file-progress="onFileProgress"
            @file-error="onFileError"
            @file-success="onFileSuccess"
            :autoStart="false"
            ref="uploader"
          >
            <uploader-unsupport></uploader-unsupport>
            <uploader-drop>
              <uploader-btn
                style="
                  width: 95%;
                  height: 110px;
                  background: #ffffff;
                  border-radius: 4px;
                  border: 1px dashed #d9d9d9;
                "
              >
                <p class="uploaderIcon">
                  <i class="el-icon-upload"></i>
                </p>

                <p class="uploaderText"> 将文件拖到此处，或<span>点击上传</span> </p>
              </uploader-btn>
              <div class="uploadeAction"> 上传程序升级包，文件要求.gz格式，不超过2000M </div>
              <div class="uploadeProgress" v-if="percentValue != 0 && percentValue != 100">
                <el-progress :percentage="Number(percentValue.toFixed(0))"></el-progress>
              </div>
              <div class="uploadeProgress" v-if="urlName != ''">
                {{ urlName }}
              </div>
            </uploader-drop>
            <!-- <uploader-list></uploader-list> -->
          </uploader>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer" v-if="uploadFlag">
      <el-button class="highBtnRe" @click="uploadVisibleClose" id="report_add_cancel"
        >取消</el-button
      >
      <el-button class="highBtn" @click="insertSave" id="report_add_sure">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import SparkMD5 from 'spark-md5'
import { setUploadFiles } from '@/api/apiConfig/api.js'

export default {
  props: ['uploadVisible', 'uploadIdObj', 'uploadFlag'],
  data() {
    let self = this
    return {
      editAssetsObj: {},
      loading: false,
      ruleForm: {
        file_name: 'fd01_image.bz2',
        version: '',
        image_name: 'php-fd01'
      },
      mainLoading: false,
      uploadForm: {
        version: '',
        content: '',
        type: '2'
      },
      uploaderUrl: '',
      editId: '',
      urlName: '',
      percentValue: 0,
      options: {
        // 上传地址
        target: function () {
          if (self.uploadFlag) {
            return `${self.uploadSrcIp}/versions/upload`
          } else {
            return `${self.uploadSrcIp}/upgrade/upload`
          }
        },
        chunkSize: 102400 * 2 * 5, //1...MB
        // 上传并发数
        simultaneousUploads: 1, //并发上传数
        // 单文件上传
        singleFile: true,
        headers: {
          Authorization: localStorage.getItem('token')
        },
        // token请求头
        processParams(params) {
          return self.setAssets(params)
        },
        testChunks: true,
        // 操作断点续传及秒传
        checkChunkUploadedByResponse: function (chunk, message) {
          let objMessage = JSON.parse(message)
          let uploadList = []
          if (objMessage.data.uploaded !== null) {
            objMessage.data.uploaded.forEach((v) => {
              uploadList.push(Number(v))
            })
          }
          // 为了防止最后一片取消
          uploadList.pop()
          if (objMessage.data.skipUpload === true) {
            return true
          }
          return (uploadList || []).indexOf(chunk.offset + 1) >= 0
        }
      },
      rules: {
        file_name: [{ required: true, message: '请输入镜像包名称', trigger: 'blur' }],
        version: [{ required: true, message: '请输入镜像版本号', trigger: 'blur' }],
        image_name: [{ required: true, message: '请输入镜像名称', trigger: 'blur' }]
      },
      rules1: {
        content: [{ required: true, message: '请输入升级内容', trigger: 'blur' }],
        version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
        type: [{ required: true, message: '请选择升级类型', trigger: 'change' }]
      }
    }
  },
  watch: {
    uploadVisible: {
      handler(newName, oldName) {
        // this.ruleForm={
        //         file_name:'',
        //         version:'',
        //         image_name:'',
        //     }
        //     this.uploadForm={
        //       version:'',
        //       content:'',
        //       type:''
        //     },
        //     //  this.uploaderUrl='',
        //      this.editId='',
        //      this.urlName='',
        //      this.percentValue=0
      },
      // 开启深度监听
      deep: true
    },
    uploadIdObj: {
      handler(newName, oldName) {
        this.editAssetsObj = newName
        if (this.editAssetsObj.id) {
          this.uploadForm = {
            version: newName.version,
            content: newName.content,
            type: String(newName.type)
          }
          this.ruleForm = {
            file_name: newName.images[0].file_name,
            version: newName.images[0].version,
            image_name: newName.images[0].image_name
          }
          this.uploaderUrl = newName.address
          this.editId = newName.id
        } else {
          this.ruleForm = {
            file_name: 'fd01_image.bz2',
            version: '',
            image_name: 'php-fd01'
          }
          this.uploadForm = {
            version: '',
            content: '',
            type: '2'
          }
          this.editId = ''
          this.urlName = ''
        }
        this.percentValue = 0
        //  if(this.editAssetsObj.type!='1'){
        //   this.uploadForm={
        //       version:newName.version,
        //       content:newName.content,
        //       type:String(newName.type)
        //   }
        //   this.uploaderUrl=newName.address
        //   this.editId=newName.id
        //  }else{
        //   this.ruleForm={
        //       file_name:'',
        //       version:'',
        //       image_name:'',
        //   }

        //   this.uploadForm={
        //     version:'',
        //     content:'',
        //     type:'1'
        //   },
        //    this.editId='',
        //    this.urlName='',
        //    this.percentValue=0
        //  }
        // //  this.uploaderUrl='',
        //  this.editId='',
        //  this.urlName='',
        //  this.percentValue=0
      },
      immediate: true,
      // 开启深度监听
      deep: true
    }
  },
  computed: {
    uploader() {
      return this.$refs.uploader.uploader
    }
  },
  methods: {
    uploadVisibleClose() {
      if (this.editId == '') {
        this.$emit('uploadVisibleClose', false)
      } else {
        this.$emit('uploadVisibleClose', true)
      }
    },
    onFileError(rootFile, file, response, chunk) {
      this.$message.error('上传失败，请重试')
      this.loading = false
      this.percentValue = 0
    },
    async insertSave() {
      let data = []
      data.push(this.ruleForm)
      let obj = {
        id: this.editId,
        ...this.uploadForm,
        address: this.uploaderUrl,
        images: data
      }
      let res = await setUploadFiles(obj)
      // if(res.code==0)
      if (res.code == 0) {
        if (this.editId == '') {
          this.$message.success('保存成功')
        } else {
          this.$message.success('编辑成功')
        }
        this.$emit('saveSuccess')
      }
    },
    onFileSuccess(rootFile, file, response, chunk) {
      this.uploaderUrl = ''
      this.$message.success('上传成功')
      this.loading = false
      this.urlName = rootFile.name
      // 本地化部署
      if (!this.uploadFlag) {
        // this.mainLoading=true
        this.uploadVisible = false
        this.$router.push({
          path: '/upgrading'
        })
      }
      if (response) {
        this.uploaderUrl = JSON.parse(response).data.url
      }
    },
    setAssets(params) {
      let data = []
      data.push(this.ruleForm)
      return {
        chunk_number: params.chunkNumber,
        total_chunks: params.totalChunks,
        filename: params.filename,
        identifier: params.identifier,
        total_size: params.totalSize,
        images: JSON.stringify(data),
        version: this.uploadForm.version
      }
    },
    // 点击选择文件
    onFileAdded(file) {
      this.loading = true
      let name = file.name.substring(file.name.lastIndexOf('.') + 1)
      this.fileSize = (file.size / 1024 / 1024).toFixed(2)
      if (this.fileSize > 2000) {
        this.$message.error('上传文件过大')
        this.uploader.cancel()
      } else if (name !== 'gz') {
        this.$message.error('不支持上传的文件格式')
        this.uploader.cancel()
      } else {
        this.computeMD5(file)
      }
    },
    computeMD5(file) {
      let fileReader = new FileReader()
      let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice
      let currentChunk = 0
      const chunkSize = 10 * 1024 * 1000
      let chunks = Math.ceil(file.size / chunkSize)
      let spark = new SparkMD5.ArrayBuffer()
      file.pause()
      loadNext()
      fileReader.onload = (e) => {
        spark.append(e.target.result)
        if (currentChunk < chunks) {
          currentChunk++
          loadNext()
        } else {
          let md5 = spark.end()
          this.computeMD5Success(md5, file)
        }
      }
      fileReader.onerror = function () {
        this.$message.error(`文件${file.name}读取出错，请检查该文件`)
        file.cancel()
      }
      function loadNext() {
        let start = currentChunk * chunkSize
        let end = start + chunkSize >= file.size ? file.size : start + chunkSize
        fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
      }
    },
    // 文件进度的回调
    onFileProgress(rootFile, file, chunk) {
      this.percentValue = this.uploader.progress() * 100
      if (this.uploader.progress() * 100 > 99) {
        this.loading = true
      } else {
        this.loading = false
      }
      let status = chunk.xhr.status
      let response = chunk.xhr.response
      if (response && JSON.parse(response).code !== 0) {
        //clearInterval(this.setTimfunc);
        this.$message.error(JSON.parse(response).message)
        // this.cancelUpgrade();
      }
      if (status == 401) {
        location.reload()
      }
    },
    computeMD5Success(md5, file) {
      // 将自定义参数直接加载uploader实例的opts上
      Object.assign(this.uploader.opts, {
        query: {
          ...this.params
        }
      })
      file.uniqueIdentifier = md5
      this.uploader.resume()
      //this.progress(md5);
    }
  }
}
</script>
<style lang="less" scoped>
.uploader-drop {
  width: 100%;
  border: none !important;
  background: none !important;
  box-sizing: border-box !important;
  padding: 0 !important;
}
.uploaderIcon {
  width: 100%;
  height: 80px;
  text-align: center;
  box-sizing: border-box;
}
.uploaderText {
  width: 100%;
  height: 14px;
  text-align: center;
  font-size: 14px;
  color: rgb(96, 98, 102);
  line-height: 14px;
  span {
    color: #409eff;
  }
}
.uploadIcon {
  width: 100%;
  font-size: 46px;
  display: flex;
  justify-content: center;
  height: 90px;
  align-items: center;
}
.uploadeAction {
  width: 300px;
  height: 42px;
  font-size: 12px;
  font-family: MicrosoftYaHei;
  color: #666666;
  line-height: 42px;
}
.uploadeProgress {
  width: 100%;
  height: 30px;
}
.uploadWarpBox {
  // background-image: url("../assets/imgages/uploaderBanner.png");
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9998;
  color: white;
}
.uploadClass {
  box-sizing: border-box;
  // padding: 0 40px 0 28px;
  display: flex;
}
.footer {
  text-align: right;
  padding-top: 15px;
}
// .emptyBox {
//   height: 120px;
//   width: 100%;
// }
.uploader-file-status {
  display: none !important;
}
.uploadDetails {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  div {
    width: 100%;
    text-align: center;
  }
  .iconfont {
    font-size: 72px !important;
    color: #2a75f1 !important;
  }
  .uploadDetailsTitle {
    //width: 216px;
    height: 18px;
    font-size: 18px;
    font-family: MicrosoftYaHeiSemibold;
    color: #ffffff;
    line-height: 18px;
    margin-top: 51px;
  }
}
.uploadLeft {
  width: 120px;
  font-size: 14px;
  color: #606266;
  text-align: right;
  padding: 0 12px 0 0;
  box-sizing: border-box;
}
.uploadRight {
  width: 409px;
}
.uploadRightSpecial {
  width: 100%;
}
.el-icon-upload {
  font-size: 67px;
  color: #c0c4cc;
}
</style>
