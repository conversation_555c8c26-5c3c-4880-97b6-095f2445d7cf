<template>
  <div class="container">
    <div class="headerTitle"
      >升级中心
      <el-button class="normalBtnRe" type="primary" id="scan_more_del" @click="upgradeHistory"
        >升级历史记录</el-button
      >
    </div>
    <div class="home_header">
      <el-tabs v-model="activeName">
        <el-tab-pane label="在线升级" name="1"> </el-tab-pane>
        <el-tab-pane label="离线升级" name="2"> </el-tab-pane>
      </el-tabs>
      <div v-if="activeName == 1" class="localiMain" v-loading="loading">
        <div class="versionClass">
          <!-- 当前版数据 -->
          <div v-for="item in uploadSetObj" :key="item.id">
            <p style="border-bottom: 1px solid #ddd; line-height: 30px; margin-top: 0">
              <span class="blueLine">服务：{{ item.service }}</span>
            </p>
            <p class="pSpecial"
              >当前版本：<span>{{ item.version }}</span></p
            >
            <el-tooltip
              class="item"
              effect="dark"
              :content="item.upgrade_time ? transferTime(item.upgrade_time) : '-'"
              placement="top"
              :open-delay="500"
            >
              <p>最近更新时间：{{ item.upgrade_time ? transferTime(item.upgrade_time) : '-' }}</p>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              popper-class="chainClass"
              placement="top"
              :open-delay="500"
            >
              <span slot="content">{{
                item.type == 1
                  ? '在线升级，在线获取可以升级的版本列表，选择后自动进行对应版本的升级'
                  : item.type == 2
                    ? '离线升级，需要手动上传升级文件进行升级'
                    : '-'
              }}</span>
              <p class="textSpecial"
                >更新方式：{{
                  item.type == 1
                    ? '在线升级，在线获取可以升级的版本列表，选择后自动进行对应版本的升级'
                    : item.type == 2
                      ? '离线升级，需要手动上传升级文件进行升级'
                      : '-'
                }}</p
              >
            </el-tooltip>
          </div>
        </div>
        <!-- <el-divider content-position="left">请选择升级包，自动选中同一批次的升级包</el-divider> -->
        <!-- 升级包数据 -->
        <div class="versionClass upgradeVersionClass">
          <div v-for="itemWrap in uploadSetObj" :key="itemWrap.id">
            <p v-if="getCheckedNum(itemWrap.service) > 0"
              >已选中：<span style="color: #2677ff">{{ getCheckedNum(itemWrap.service) }}</span
              >项,自动选中同一批次</p
            >
            <div
              class="emptyClass"
              v-if="
                tableData.filter((to) => {
                  return to.service == itemWrap.service
                }).length == 0
              "
            >
              <div>
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-kong"></use>
                </svg>
                <p>暂无升级包</p>
              </div>
            </div>
            <div
              v-else
              v-for="item in tableData.filter((to) => {
                return to.service == itemWrap.service
              })"
              :key="item.id"
            >
              <p style="border-bottom: 1px solid #ddd; line-height: 30px; margin-top: 0">
                <el-checkbox v-model="item.checked" @change="selectOne($event, item)">
                  <span class="grayLine"
                    >版本：<span>{{ item.version }}</span></span
                  >
                </el-checkbox>
              </p>
              <!-- <p class="pSpecial">版本：<span>{{item.version}}</span></p> -->
              <el-tooltip
                class="item"
                effect="dark"
                :content="item.created_at ? transferTime(item.created_at) : '-'"
                placement="top"
                :open-delay="500"
              >
                <p>发布时间：{{ item.created_at ? transferTime(item.created_at) : '-' }}</p>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                popper-class="chainClass"
                placement="top"
                :open-delay="500"
              >
                <span slot="content">
                  <mavon-editor
                    v-model="item.content"
                    class="formContent"
                    :editable="false"
                    :subfield="false"
                    defaultOpen="preview"
                    :toolbarsFlag="false"
                  />
                </span>
                <p class="textSpecial">升级内容：{{ item.content ? item.content : '-' }}</p>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
      <div v-if="activeName == 1" class="footer">
        <el-button
          class="normalBtn"
          :loading="checkLoading"
          type="primary"
          @click="updateList"
          id="unit_scan"
          >升级</el-button
        >
      </div>
      <outlineUpgrade
        v-if="activeName == 2"
        :uploadVisible="uploadVisible"
        @uploadVisibleClose="uploadVisibleClose"
        @saveSuccess="saveSuccess"
        :uploadIdObj="uploadIdObj"
        :uploadFlag="false"
      />
      <el-drawer title="升级历史记录" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
        <div class="hostoryMain">
          <div class="historyBox" v-for="(item, key) in historyData" :key="key">
            <p class="specialVersion"
              >版本号：<span>{{ item.version }}</span></p
            >
            <p class="specialVersion"
              >服务：<span>{{ item.service }}</span></p
            >
            <p
              >发布时间：<span>{{ item.created_at }}</span></p
            >
            <p
              >升级时间：<span>{{ item.updated_at }}</span></p
            >
            <div style="margin-top: 10px">升级内容：</div>
            <mavon-editor
              v-if="item.content"
              v-model="item.content"
              class="formContent"
              :editable="false"
              :subfield="false"
              defaultOpen="preview"
              :toolbarsFlag="false"
            />
            <span v-else>-</span>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import outlineUpgrade from './outlineUpgrade.vue'
import { mavonEditor } from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'
import { mapGetters, mapState } from 'vuex'
import { getupgradeList, getupgradeHistory, getLineUpdate } from '@/api/apiConfig/api.js'

export default {
  components: { outlineUpgrade, mavonEditor },
  data() {
    return {
      activeName: '1',
      checkLoading: false,
      uploadVisible: false,
      loading: false,
      highCheckdialog: false,
      tableData: [],
      uploadIdObj: {},
      uploadSetObj: [],
      versionInfo: {
        version: '',
        service: '',
        upgrade_time: '',
        type: ''
      },
      historyData: [],
      tableHeaderList: [
        {
          label: '服务',
          name: 'service',
          minWidtth: '100'
        },
        {
          label: '升级版本',
          name: 'version',
          minWidtth: '80'
        },
        {
          label: '升级内容',
          name: 'content',
          minWidtth: '100'
        },
        {
          label: '批次号',
          name: 'batch',
          minWidtth: '80'
        },
        {
          label: '发布时间',
          name: 'created_at',
          minWidtth: '120'
        }
      ]
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    getCheckedNum(curService) {
      return this.tableData.filter((to) => {
        return to.service == curService && to.checked
      }).length
    },
    cellClass(row) {
      if (row.columnIndex === 0) {
        return 'disabledCheck'
      }
    },
    // 选中升级包
    selectOne(checked, row) {
      if (checked) {
        // 选中其中一个，同时选中同一批次的所有数据
        let sameBatch = []
        if (row.batch) {
          sameBatch = this.tableData.filter((item) => {
            return item.batch == row.batch
          }) // 获取同一批次的数据,同时选中
          this.tableData.forEach((toItem) => {
            toItem.checked = false
          })
          sameBatch.forEach((item) => {
            this.tableData.forEach((toItem) => {
              if (toItem.id == item.id) {
                toItem.checked = true
              }
            })
          })
        } else {
          sameBatch = [row]
          this.tableData.forEach((toItem) => {
            toItem.checked = false
          })
          sameBatch.forEach((item) => {
            this.tableData.forEach((toItem) => {
              if (toItem.id == item.id) {
                toItem.checked = true
              }
            })
          })
        }
      } else {
        // 取消选中,同时取消所有选中的数据
        this.tableData.forEach((toItem) => {
          if (toItem.checked) {
            toItem.checked = false
          }
        })
      }
      this.$forceUpdate()
    },
    compareVersion(v1, v2) {
      // 这是微信小程序官方给出的比较方法
      v1 = v1.split('.')
      v2 = v2.split('.')
      const len = Math.max(v1.length, v2.length)

      while (v1.length < len) {
        v1.push('0')
      }
      while (v2.length < len) {
        v2.push('0')
      }

      for (let i = 0; i < len; i++) {
        const num1 = parseInt(v1[i])
        const num2 = parseInt(v2[i])

        if (num1 > num2) {
          return 1
        } else if (num1 < num2) {
          return -1
        }
      }
      return 0
    },
    async updateList(row) {
      this.checkedArr = this.tableData.filter((item) => {
        return item.checked
      }) // 获取选中的数据
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择升级包！')
        return
      }
      let obj = []
      this.checkedArr.forEach((item) => {
        obj.push({
          address: item.address,
          version: item.version,
          service: item.service
        })
      })
      this.checkLoading = true
      let res = await getLineUpdate({
        batch: this.checkedArr[0].batch,
        update_list: obj
      }).catch(() => {
        this.checkLoading = false
      })
      this.checkLoading = false
      if (res.code == 0) {
        this.$router.push({
          path: '/upgrading'
        })
      }
    },
    saveSuccess() {
      this.uploadVisible = false
    },
    noLineClick() {
      this.uploadVisible = true
    },
    uploadVisibleClose(val) {
      this.uploadVisible = false
    },
    upgradeHistory() {
      this.highCheckdialog = true
    },
    async getTablist() {
      // 获取当前服务版本
      this.loading = true
      let res = await getupgradeHistory({ operate_company_id: this.currentCompany }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        let uploadSetObj = res.data.versions ? res.data.versions : []
        this.historyData = res.data.list.data
        // 获取在线升级列表
        let resList = await getupgradeList({ operate_company_id: this.currentCompany }).catch(
          () => {
            this.loading = false
          }
        )
        this.loading = false
        if (resList.code == 0) {
          // resList.data = [
          //   {
          //     id: 1,
          //     version: '1.11.8',
          //     service: 'code',
          //     batch: '1'
          //   },
          //   {
          //     id: 2,
          //     version: '1.11.9',
          //     service: 'code',
          //     batch: '2'
          //   },
          //   {
          //     id: 3,
          //     version: '1.11.3',
          //     service: 'code',
          //     batch: '2'
          //   },
          //   {
          //     id: 4,
          //     version: '1.22.4',
          //     service: 'file',
          //     batch: '1'
          //   },
          //   {
          //     id: 5,
          //     version: '1.22.5',
          //     service: 'file',
          //     batch: '2'
          //   }
          // ]
          let newRes = resList.data ? resList.data : []
          // 当前服务有升级包的排到前面----开始
          let hasUpdrageData = newRes.map((item) => {
            return item.service
          })
          let hasArr = []
          let nohasArr = []
          uploadSetObj.forEach((item) => {
            if (hasUpdrageData.includes(item.service)) {
              hasArr.push(item)
            } else {
              nohasArr.push(item)
            }
          })
          this.uploadSetObj = hasArr.concat(nohasArr)
          // 当前服务有升级包的排到前面----结束
          let filterData = []
          newRes.forEach((item) => {
            this.uploadSetObj.forEach((obj) => {
              // 过滤出版本号大于等于当前服务版本号的数据
              if (
                item.service == obj.service &&
                this.compareVersion(item.version, obj.version) > 0
              ) {
                filterData.push(item)
              }
            })
          })
          this.tableData = filterData
        }
      }
    }
  },
  mounted() {
    this.getTablist()
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .headerTitle {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .downloadClass1 {
    position: absolute;
    top: -41.5px;
    right: 0px;
    height: 40px;
    line-height: 40px;
    color: #62666c;

    i {
      font-size: 14px;
      color: #2677ff;
      margin: 0 8px 0 16px;
    }
    .el-icon-close {
      position: absolute;
      right: 10px;
      top: 12px;
      font-size: 16px;
      color: #9fa6af;
      // font-weight: bold;
    }
    span {
      color: #2677ff;
    }
    .task {
      display: inline-block;
      max-width: 186px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #37393c;
    }
  }
  /deep/.el-dialog__body {
    padding: 16px 28px 28px 28px !important;
  }
  /deep/.el-table--border .el-table__cell {
    border-right: 1px solid #ebeef5 !important;
  }
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    height: 100%;
    // padding: 20px;
    box-sizing: border-box;
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 120px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 120px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
    .el-table {
      border: 0;
    }
  }
}
.localiHeader {
  width: 100%;
  height: 11%;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  padding: 32px;
  & > .headerBtn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 20px;
    font-weight: 600;
    color: rgba(55, 57, 60, 1);
    box-sizing: border-box;
    .icon {
      font-size: 33px;
    }
  }
}
.localiMain {
  width: 100%;
  height: 88%;
  overflow: auto;
  box-sizing: border-box;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
  .mainTitle {
    display: flex;
    width: 100%;
    height: 28px;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: rgba(55, 57, 60, 1);
  }
  .el-divider--horizontal {
    margin: 10px 0;
  }
  .versionClass {
    // display: flex;
    // align-content: center;
    // flex-wrap: nowrap;
    padding: 16px 32px 0px 32px;
    white-space: nowrap;
    & > div {
      width: 17.5%;
      display: inline-block;
      padding: 5px 16px 10px 16px;
      margin-bottom: 16px;
      margin-right: 0.9%;
      box-sizing: border-box;
      opacity: 1;
      background: #ffffff;
      box-shadow: 0px 4px 20px 0px rgba(16, 33, 62, 0.08);
      border-top: 4px solid #2677ff;
      border-radius: 4px 4px 0px 0px;
      border: 1px solid rgba(233, 235, 239, 1);
      border-bottom: 0;
      p {
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 0px;
        color: rgba(98, 102, 108, 1);
        margin-top: 8px;
        padding-bottom: 5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        span {
          color: rgba(55, 57, 60, 1);
        }
      }
    }
    & > div:last-child {
      margin-right: 0;
    }
    .textSpecial {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      span {
        display: inline-block;
        width: 100%;
        // overflow: hidden;
        // white-space: nowrap;
        // text-overflow: ellipsis;
      }
    }
  }
  .upgradeVersionClass {
    height: 74%;
    margin-top: -31px;
    .emptyClass {
      height: 96%;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      text-align: center;
      background: transparent;
      box-shadow: none;
      svg {
        display: inline-block;
        font-size: 100px;
      }
      p {
        line-height: 25px;
        color: #d1d5dd;
        span {
          margin-left: 4px;
          color: #2677ff;
          cursor: pointer;
        }
      }
    }
    & > div {
      height: 100%;
      overflow: auto;
      background: linear-gradient(180deg, rgba(247, 250, 255, 1) 0%, rgba(240, 243, 250, 1) 100%);
      border-radius: 0px 0px 4px 4px;
      border: 1px solid rgba(233, 235, 239, 1);
      border-top: 0;
      & > div {
        width: 100%;
        padding: 5px 16px 10px 16px;
        margin-top: 16px;
        box-sizing: border-box;
        opacity: 1;
        background: #ffffff;
        box-shadow: 0px 4px 20px 0px rgba(16, 33, 62, 0.08);
        // border-top: 4px solid #E4ECF8;
        p {
          font-size: 14px;
          font-weight: 400;
          letter-spacing: 0px;
          color: rgba(98, 102, 108, 1);
          margin-top: 8px;
          padding-bottom: 5px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          span {
            color: rgba(55, 57, 60, 1);
          }
        }
      }
    }
  }
  .mainUploader {
    width: 100%;
    height: 50%;
    display: flex;
    justify-content: center;
    margin-top: 8%;
    & > div {
      cursor: pointer;
      width: 38.7%;
      height: 100%;
      border-radius: 4px;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
      background-image: url('../../assets/images/localized/banner.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: relative;
      p:nth-child(2) {
        font-size: 14px;
        font-weight: 400;
        color: rgba(55, 57, 60, 1);
      }
      p {
        box-sizing: border-box;
        padding: 20px 0 0 20px;
        display: flex;
        align-items: center;
        img {
          width: 8px;
          height: 8px;
        }
        span {
          margin-left: 9px;
          font-size: 14px;
          font-weight: 500;
          color: rgba(55, 57, 60, 1);
        }
      }
      .statusImg {
        width: 115px;
        height: 122px;
        position: absolute;
        bottom: 0;
        right: 17px;
      }
    }
  }
  .uploadMessage {
    width: 238px;
    height: 28px;
    background: rgba(235, 238, 245, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 400;
    color: rgba(98, 102, 108, 1);
    margin-left: 4px;
  }
  .loaderSpecial {
    margin-left: 31px;
  }
}
.footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  background: #ffffff;
  box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.08);
}
.hostoryMain {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
}
.historyBox {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  background: rgba(255, 255, 255, 1);
  margin-top: 15px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
  p {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(55, 57, 60, 1);
  }
}
.specialVersion {
  font-size: 16px;
  font-weight: 500;
  color: rgba(55, 57, 60, 1);
  span {
    // margin-left: 9px;
    font-size: 16px;
    font-weight: 500;
    color: rgba(55, 57, 60, 1);
  }
}
.dialog-body1 {
  min-height: 300px;
  max-height: 500px;
}
/* 去掉全选按钮 */
::v-deep .el-table .disabledCheck .cell .el-checkbox__inner {
  display: none !important;
}

// 这里可以修改全选框那部分内容，如果不想添加，可忽略这部分代码
::v-deep .el-table .disabledCheck .cell::before {
  content: ' ';
  text-align: center;
  line-height: 37px;
}
::v-deep .el-table th.el-table__cell::after {
  display: none;
}
</style>
