<template>
  <div class="uploadWrap">
    <el-form label-width="110px">
      <el-form-item label="是否重启服务" prop="restart">
        <el-radio-group v-model="restart">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div class="uploadRight">
      <div class="uploadClass">
        <div>
          <uploader
            :options="options"
            class="uploader-example"
            @file-added="onFileAdded"
            @file-progress="onFileProgress"
            @file-error="onFileError"
            @file-success="onFileSuccess"
            :autoStart="false"
            ref="uploader"
          >
            <uploader-unsupport></uploader-unsupport>
            <uploader-drop>
              <uploader-btn
                style="
                  width: 95%;
                  height: 159px;
                  background: #ffffff;
                  border-radius: 4px;
                  border: 1px dashed #d9d9d9;
                "
              >
                <p class="uploaderIcon">
                  <i class="el-icon-upload"></i>
                </p>

                <p class="uploaderText"> 将文件拖到此处，或<span>点击上传</span> </p>
              </uploader-btn>
              <div class="uploadeAction"> 上传程序升级包，文件要求.dat格式，不超过2000M </div>
              <div class="uploadeProgress" v-if="percentValue != 0 && percentValue != 100">
                <el-progress :percentage="Number(percentValue.toFixed(0))"></el-progress>
              </div>
              <div class="uploadeProgress" v-if="urlName != ''">
                {{ urlName }}
              </div>
            </uploader-drop>
            <!-- <uploader-list></uploader-list> -->
          </uploader>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import SparkMD5 from 'spark-md5'
import { mavonEditor } from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'
import { eidtUploadFiles, getbatchList } from '@/api/apiConfig/api.js'

export default {
  components: { mavonEditor },
  props: ['uploadVisible', 'uploadIdObj'],
  data() {
    let self = this
    return {
      editAssetsObj: {},
      batchList: [],
      file_list: [],
      currentBatch: '',
      loading: false,
      contentNew: '',
      ruleForm: {
        file_name: 'foradar_image.bz2',
        version: '',
        image_name: 'php-foradar'
      },
      mainLoading: false,
      uploadFormArr: [],
      uploadForm: {
        version: '',
        content: '',
        type: '1'
      },
      uploaderUrl: '',
      editId: '',
      urlName: [],
      percentValue: 0,
      options: {
        // 上传地址
        target: `${self.uploadSrcIp}/upgrade/upload`,
        chunkSize: 102400 * 2 * 5, //1...MB
        // 上传并发数
        simultaneousUploads: 1, //并发上传数
        // 单文件上传
        singleFile: true,
        headers: {
          Authorization: localStorage.getItem('token')
        },
        // token请求头
        processParams(params) {
          return self.setAssets(params)
        },
        testChunks: true,
        // 操作断点续传及秒传
        checkChunkUploadedByResponse: function (chunk, message) {
          let objMessage = JSON.parse(message)
          let uploadList = []
          if (objMessage.data.uploaded !== null) {
            objMessage.data.uploaded.forEach((v) => {
              uploadList.push(Number(v))
            })
          }
          // 为了防止最后一片取消
          uploadList.pop()
          if (objMessage.data.skipUpload === true) {
            return true
          }
          return (uploadList || []).indexOf(chunk.offset + 1) >= 0
        }
      },
      rulesArr: [
        {
          content: [{ required: true, message: '请输入升级内容', trigger: 'blur' }],
          version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
          type: [{ required: true, message: '请选择升级类型', trigger: 'change' }]
        }
      ],
      restart: 0
    }
  },
  watch: {
    uploadVisible: {
      handler(newName, oldName) {},
      // 开启深度监听
      deep: true
    }
  },
  computed: {
    uploader() {
      return this.$refs.uploader.uploader
    }
  },
  methods: {
    addItem() {
      this.uploadFormArr.push({
        version: '',
        content: '',
        service: ''
      })
      this.rulesArr.push({
        content: [{ required: true, message: '请输入升级内容', trigger: 'blur' }],
        version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
        type: [{ required: true, message: '请选择升级类型', trigger: 'change' }]
      })
    },
    // 删除
    deleteItem(index) {
      this.uploadFormArr.splice(index, 1)
      this.this.rulesArr.splice(index, 1)
    },
    uploadVisibleClose() {
      if (this.editId == '') {
        this.$emit('uploadVisibleClose', false)
      } else {
        this.$emit('uploadVisibleClose', true)
      }
    },
    onFileError(rootFile, file, response, chunk) {
      let myError = response.toString() //转字符串
      myError = myError.replace('Error: ', '') // 去掉前面的" Error: "
      myError = JSON.parse(myError) //转对象
      this.$message.error(myError.message)
      this.percentValue = 0
    },
    //生成32位随机数
    getNum() {
      var chars = [
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'j',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z'
      ]
      var nums = ''
      for (var i = 0; i < 32; i++) {
        var id = parseInt(Math.random() * 61)
        nums += chars[id]
      }
      return nums
    },
    async insertSave() {
      let obj = []
      let batch = ''
      // 升级中心上传多组值需要batch批次号
      if (this.uploadFormArr.length > 1) {
        batch = this.getNum()
      }
      this.uploadFormArr.forEach((item, index) => {
        obj.push({
          id: this.editId,
          version: item.version,
          content: item.content,
          service: item.service,
          address: this.uploaderUrl,
          batch: batch
        })
      })
      let res = await eidtUploadFiles(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.$emit('saveSuccess')
      }
    },
    onFileSuccess(rootFile, file, response, chunk) {
      this.uploaderUrl = ''
      this.$message.success('上传成功')
      this.loading = false
      this.urlName = rootFile.name
      // 本地化部署
      this.uploadVisible = false
      this.$router.push({
        path: '/upgrading'
      })
      if (response) {
        this.uploaderUrl = JSON.parse(response).data.url
      }
    },
    setAssets(params) {
      let version = params.filename.split('_')[0] // 截取文件名称的版本号
      let data = []
      data.push(this.ruleForm)
      return {
        chunk_number: params.chunkNumber,
        total_chunks: params.totalChunks,
        filename: params.filename,
        identifier: params.identifier,
        total_size: params.totalSize,
        version: version,
        restart: this.restart // 离线需要restart字段
      }
    },
    // 点击选择文件
    onFileAdded(file) {
      this.loading = true
      let name = file.name.substring(file.name.lastIndexOf('.') + 1)
      this.fileSize = (file.size / 1024 / 1024).toFixed(2)
      if (this.fileSize > 2000) {
        this.$message.error('上传文件过大')
        this.uploader.cancel()
      } else if (name !== 'dat') {
        this.$message.error('不支持上传的文件格式')
        this.uploader.cancel()
      } else {
        this.computeMD5(file)
      }
    },
    computeMD5(file) {
      let fileReader = new FileReader()
      let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice
      let currentChunk = 0
      const chunkSize = 10 * 1024 * 1000
      let chunks = Math.ceil(file.size / chunkSize)
      let spark = new SparkMD5.ArrayBuffer()
      file.pause()
      loadNext()
      fileReader.onload = (e) => {
        spark.append(e.target.result)
        if (currentChunk < chunks) {
          currentChunk++
          loadNext()
        } else {
          let md5 = spark.end()
          this.computeMD5Success(md5, file)
        }
      }
      fileReader.onerror = function () {
        this.$message.error(`文件${file.name}读取出错，请检查该文件`)
        file.cancel()
      }
      function loadNext() {
        let start = currentChunk * chunkSize
        let end = start + chunkSize >= file.size ? file.size : start + chunkSize
        fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
      }
    },
    // 文件进度的回调
    onFileProgress(rootFile, file, chunk) {
      this.percentValue = this.uploader.progress() * 100
      if (this.uploader.progress() * 100 > 99) {
        this.loading = true
      } else {
        this.loading = false
      }
      let status = chunk.xhr.status
      let response = chunk.xhr.response
      if (response && JSON.parse(response).code !== 0) {
        //clearInterval(this.setTimfunc);
        this.$message.error(JSON.parse(response).message)
        // this.cancelUpgrade();
      }
      if (status == 401) {
        location.reload()
      }
    },
    computeMD5Success(md5, file) {
      console.log('走这里了吗')
      // 将自定义参数直接加载uploader实例的opts上
      Object.assign(this.uploader.opts, {
        query: {
          ...this.params
        }
      })
      file.uniqueIdentifier = md5
      this.uploader.resume()
      //this.progress(md5);
    }
  }
}
</script>
<style lang="less" scoped>
.uploadWrap {
  width: 100%;
  height: calc(100% - 60px);
  padding: 15% 30% 0 30%;
  box-sizing: border-box;
  margin: 0 auto;
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.7) 0%, #ffffff 100%) !important;
}
/deep/.el-form {
  padding: 0 !important;
  .el-form-item .el-form-item__label {
    line-height: 40px;
  }
}
.uploader-drop {
  width: 100%;
  // display:flex;
  // align-items: center;
  border: none !important;
  background: none !important;
  box-sizing: border-box !important;
  padding: 0 !important;
}
.uploader-btn {
  margin-right: 20px;
}
.uploaderIcon {
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  i {
    font-size: 67px;
    color: #c0c4cc;
    margin: 40px 0 16px;
    line-height: 50px;
  }
}
.uploaderText {
  width: 100%;
  height: 14px;
  text-align: center;
  font-size: 14px;
  color: rgb(96, 98, 102);
  line-height: 14px;
  span {
    color: #409eff;
  }
}
.uploadeAction {
  font-size: 12px;
  color: #606266;
  margin-top: 16px;
  line-height: 30px;
}
.uploadeProgress {
  width: 100%;
  height: 30px;
}
.uploadWarpBox {
  // background-image: url("../assets/imgages/uploaderBanner.png");
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9998;
  color: white;
}
.footer {
  text-align: right;
  padding-top: 15px;
}
// .emptyBox {
//   height: 120px;
//   width: 100%;
// }
.uploader-file-status {
  display: none !important;
}
.uploadDetails {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  div {
    width: 100%;
    text-align: center;
  }
  .iconfont {
    font-size: 72px !important;
    color: #2a75f1 !important;
  }
  .uploadDetailsTitle {
    //width: 216px;
    height: 18px;
    font-size: 18px;
    font-family: MicrosoftYaHeiSemibold;
    color: #ffffff;
    line-height: 18px;
    margin-top: 51px;
  }
}
.uploadLeft {
  width: 110px;
  font-size: 14px;
  color: #606266;
  text-align: right;
  padding: 0 12px 0 0;
  box-sizing: border-box;
}

// .el-icon-upload{
//   font-size: 67px;
//   color: #C0C4CC;
// }
.el-divider__text {
  font-size: 16px;
  color: #2677ff;
}
.el-divider {
  background-color: rgb(64, 158, 255);
}
</style>
