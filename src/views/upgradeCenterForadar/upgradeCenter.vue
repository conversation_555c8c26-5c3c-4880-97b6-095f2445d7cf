<template>
  <div class="container">
    <!-- <div class="downloadClass1">
      <div>
        <span style="color: #62666C;marginRight:18px">当前版本：v1.1.2</span>
        <span style="color: #62666C;marginRight:18px">升级时间：2021.10.06 15：00</span>
        <span class="myStatus myyellowStatus">升级历史记录</span>
      </div>
    </div> -->
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="amd架构" name="1"></el-tab-pane>
      <el-tab-pane label="arm架构" name="2"></el-tab-pane>
    </el-tabs>
    <div class="headerTitle">升级中心</div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="addForm.keyword"
            @keyup.enter.native="checkFuncList"
            placeholder="请输入关键字进行搜索"
            id="scan_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
        </div>
        <div>
          <el-button class="normalBtn" type="primary" id="upload_mold" @click="uploaderShow"
            >上传升级</el-button
          >
        </div>
      </div>
      <div :class="hightFilterIsShow()" v-loading="loading" style="padding: 0px 20px">
        <el-table
          border
          :data="tableData"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            v-for="(item, itemIndex) in tableHeader"
            :key="itemIndex"
            align="left"
            :prop="item.name"
            :label="item.label"
            :fixed="item.fixed"
            :min-width="item.minWidth"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'type' && Number(scope.row[item.name]) == 1"
                >FORadar升级</span
              >
              <span v-else-if="item.name == 'type' && Number(scope.row[item.name]) == 2"
                >Fd01升级</span
              >
              <span v-else-if="item.name == 'address'">
                <span
                  style="color: #2677ff; cursor: pointer"
                  @click="downloadFun(scope.row[item.name])"
                  >下载升级包</span
                >
              </span>
              <el-tooltip
                v-else-if="item.name == 'content'"
                effect="dark"
                popper-class="chainClass"
                placement="top"
                :open-delay="500"
              >
                <span slot="content">
                  <mavon-editor
                    v-model="scope.row[item.name]"
                    class="formContent"
                    :editable="false"
                    :subfield="false"
                    defaultOpen="preview"
                    :toolbarsFlag="false"
                  />
                </span>
                <p class="textSpecial">{{ scope.row[item.name] ? scope.row[item.name] : '-' }}</p>
              </el-tooltip>
              <span v-else>
                <el-tooltip
                  v-if="scope.row[item.name]"
                  class="item"
                  effect="dark"
                  placement="top"
                  :open-delay="500"
                >
                  <span slot="content">
                    <div v-html="scope.row[item.name]"></div>
                  </span>
                  <span v-html="scope.row[item.name]"></span>
                </el-tooltip>
                <span v-else>-</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" width="180">
            <template slot-scope="scope">
              <span>
                <!-- <el-button type="text" size="small"  id="scan_del" @click="editObj(scope.row)">编辑</el-button> -->
                <!-- <el-button type="text" size="small"  id="scan_del">升级</el-button> -->
                <!-- <el-button type="text" size="small"  id="scan_del">下载</el-button> -->
                <el-button type="text" size="small" id="scan_del" @click="upgradeDel(scope.row.id)"
                  >删除</el-button
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizeArr"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <upgradeUpload
      :activeName="activeName"
      :uploadVisible="uploadVisible"
      @uploadVisibleClose="uploadVisibleClose"
      @saveSuccess="saveSuccess"
      :uploadIdObj="uploadIdObj"
      :uploadFlag="true"
    ></upgradeUpload>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { mavonEditor } from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'
import upgradeUpload from './upgradeUpload.vue'
import { getUploadListForadar, delUploadListForadar } from '@/api/apiConfig/api.js'

export default {
  components: { mavonEditor, upgradeUpload },
  data() {
    return {
      activeName: '1',
      uploadIdObj: {},
      uploadVisible: false,
      loading: false,
      keyword: '',
      pageSizeArr: [10, 20, 30, 40],
      pageSize: 10,
      total: 0,
      currentPage: 1,
      tableData: [],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      tableHeader: [
        {
          label: '版本号',
          name: 'version',
          minWidth: '80'
        },
        {
          label: '升级内容',
          name: 'content',
          minWidth: '300'
        },
        {
          label: '服务',
          name: 'service',
          minWidth: '120'
        },
        {
          label: '升级包',
          name: 'address',
          minWidth: '100'
        },

        {
          label: '发布时间',
          name: 'created_at',
          minWidth: '120'
        }
      ],
      addForm: {
        keyword: '',
        page: 1,
        per_page: 10,
        version: '',
        batch: '',
        service: ''
      }
      // systemType: ''
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    handleClick(val) {
      this.getTableList()
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    checkFuncList() {
      this.currentPage = 1
      this.getTableList()
    },
    downloadFun(url) {
      this.download(url.includes('http') ? url : this.showSrcIp + url)
    },
    async upgradeDel(id) {
      let obj = {
        id: id,
        operate_company_id: this.currentCompany
      }
      this.$confirm('确定删除数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'scan_del_cancel',
        confirmButtonClass: 'scan_del_sure',
        customClass: 'scan_del',
        type: 'warning'
      })
        .then(async () => {
          let res = await delUploadListForadar(obj)
          if (res.code == 0) {
            this.$message.success('删除成功')
            this.getTableList()
          }
        })
        .catch(() => {})
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getTableList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTableList()
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    async getTableList() {
      let obj = {
        operate_company_id: this.currentCompany,
        keyword: this.addForm.keyword,
        page: this.currentPage,
        per_page: this.pageSize,
        version: this.addForm.version,
        batch: this.addForm.batch,
        service: this.addForm.service,
        system_type: +this.activeName
      }
      this.loading = true
      let res = await getUploadListForadar(obj)
      this.loading = false
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
      }
    },
    uploaderShow() {
      this.uploadVisible = true
      this.uploadIdObj = {}
    },
    uploadVisibleClose(val) {
      this.uploadVisible = false
    },
    saveSuccess() {
      this.uploadVisible = false
      this.getTableList()
    },
    editObj(obj) {
      this.uploadVisible = true
      this.uploadIdObj = obj
    }
  },
  mounted() {
    this.getTableList()
  }
}
</script>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .downloadClass1 {
    position: absolute;
    top: -41.5px;
    right: 0px;
    height: 40px;
    line-height: 40px;
    color: #62666c;

    i {
      font-size: 14px;
      color: #2677ff;
      margin: 0 8px 0 16px;
    }
    .el-icon-close {
      position: absolute;
      right: 10px;
      top: 12px;
      font-size: 16px;
      color: #9fa6af;
      // font-weight: bold;
    }
    span {
      color: #2677ff;
    }
    .task {
      display: inline-block;
      max-width: 186px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #37393c;
    }
  }
  .dialog-body {
    /deep/.upload-demo {
      width: 100%;
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
      .downloadText {
        margin-left: 5px;
        color: #4285f4;
        cursor: pointer;
      }
      .el-upload-list {
        height: 50px;
        overflow-y: auto;
      }
    }
  }
  /deep/.home_header {
    position: relative;
    // height: 100%;
    height: calc(100% - 66px);
  }
  /deep/.el-tabs {
    .el-tabs__nav {
      padding-left: 20px;
    }
    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active {
      width: 100px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      font-weight: bold;
      color: #2677ff;
      background: rgba(38, 119, 255, 0.08);
    }
    .el-tabs__active-bar {
      left: 4px;
      width: 100%;
      background: #2677ff;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e9ebef;
    }
    .el-tabs__item {
      width: 100px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      padding: 0;
      font-size: 14px;
      font-weight: 400;
      color: #62666c;
    }
  }

  .filterTab {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    & > div {
      display: flex;
      align-items: center;
      .normalBtnRe {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .el-input {
        width: 240px;
      }
      & > span {
        font-weight: 400;
        color: #2677ff;
        line-height: 20px;
        margin-left: 16px;
        cursor: pointer;
      }
    }
  }
  .tableWrapFilter {
    height: calc(100% - 180px) !important;
  }
  .tableWrap {
    height: calc(100% - 129px);
    padding: 0px 20px;
  }
  .el-table {
    border: 0;
  }
}
// }
</style>
