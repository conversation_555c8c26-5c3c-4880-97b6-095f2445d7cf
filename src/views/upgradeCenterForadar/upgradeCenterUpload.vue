<template>
  <div class="wrapper">
    <upgradeCenter v-if="!centerShowFlag"></upgradeCenter>
    <localizedDeployment v-if="centerShowFlag"></localizedDeployment>
  </div>
</template>

<script>
import upgradeCenter from './upgradeCenter'
import localizedDeployment from './localizedDeployment.vue'
export default {
  components: { upgradeCenter, localizedDeployment },
  computed: {
    centerShowFlag() {
      let centerShowFlag = false
      if (sessionStorage.getItem('userMessage')) {
        centerShowFlag = JSON.parse(sessionStorage.getItem('userMessage')).is_local
      }
      return centerShowFlag
    }
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
}
</style>
