<template v-loading="mainLoading">
  <el-dialog
    class="elDialogAdd"
    :close-on-click-modal="false"
    :visible.sync="uploadVisible"
    :before-close="uploadVisibleClose"
    :width="!uploadFlag ? '550px' : '80%'"
  >
    <template slot="title">
      上传升级文件
      <el-button
        v-if="uploadFlag"
        style="margin-left: 10px"
        size="small"
        type="primary"
        @click="addItem"
        >添加</el-button
      >
    </template>
    <div class="dialog-body">
      <!-- <el-form-item label="服务器架构" prop="public"> -->
      服务器架构：
      <el-select v-model="publicData" placeholder="请选择" clearable>
        <el-option label="amd架构" :value="1"></el-option>
        <el-option label="arm架构" :value="2"></el-option>
      </el-select>
      <!-- </el-form-item> -->
      <!-- v-loading="loading" -->
      <el-form
        v-for="(item, index) in uploadFormArr"
        :key="index"
        :model="item"
        :rules="rulesArr[index]"
        style="padding: 0 !important"
        ref="addRuleForm"
        :label-width="!uploadFlag ? '100px' : '0'"
        class="demo-ruleForm"
      >
        <el-divider v-if="uploadFlag" content-position="left"
          >{{ `第${index + 1}组` }}
          <i v-if="index > 0" class="el-icon-delete" @click="deleteItem(index)"></i
        ></el-divider>
        <el-form-item v-if="!uploadFlag" label="是否重启服务" prop="restart">
          <el-radio-group v-model="restart">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <div style="display: flex; justify-content: space-between">
          <div class="uploadClass">
            <!-- <div class="uploadLeft" >升级包</div> -->
            <div class="uploadRight">
              <uploader
                :options="options"
                class="uploader-example"
                @file-added="onFileAdded($event, index)"
                @file-progress="onFileProgress"
                @file-error="onFileError"
                @file-success="onFileSuccess"
                :autoStart="false"
                :ref="'uploader' + index"
              >
                <uploader-unsupport></uploader-unsupport>
                <uploader-drop>
                  <uploader-btn
                    style="
                      width: 95%;
                      height: 110px;
                      background: #ffffff;
                      border-radius: 4px;
                      border: 1px dashed #d9d9d9;
                    "
                  >
                    <p class="uploaderIcon">
                      <i class="el-icon-upload"></i>
                    </p>
                    <p class="uploaderText">
                      <span>点击上传</span>,{{
                        `${!uploadFlag ? '支持.dat格式' : '支持 .gz 格式'}`
                      }}
                      <!-- ,不超过2000M -->
                    </p>
                  </uploader-btn>
                  <div>
                    <div
                      class="uploadeProgress"
                      v-if="item.percentValue != 0 && item.percentValue != 100"
                    >
                      <el-progress :percentage="Number(item.percentValue.toFixed(0))"></el-progress>
                    </div>
                    <div class="uploadeProgress" v-if="urlName.length > 0">
                      {{ urlName[index] }}
                    </div>
                  </div>
                </uploader-drop>
              </uploader>
            </div>

            <el-form-item v-if="uploadFlag" label="" prop="version">
              版本号：<el-input
                disabled
                v-model="item.version"
                placeholder="请输入版本号"
              ></el-input>
            </el-form-item>
            <el-form-item v-if="uploadFlag" label="" prop="service">
              服务：<el-input disabled v-model="item.service" placeholder="请输入服务"></el-input>
            </el-form-item>
          </div>
          <!-- <el-form-item v-if="editAssetsObj.id" style="width: 28% !important" label="批次号" prop="service">
              <el-select filterable v-model="currentBatch" placeholder="请选择批次号" multiple collapse-tags>
                <el-option :label="v" :value="v" v-for="(v, i) in batchList" :key="i"></el-option>
              </el-select>
            </el-form-item> -->

          <el-form-item
            style="width: 130% !important; margin-left: 10px"
            label=""
            v-if="uploadFlag"
            prop="content"
          >
            <!--展示 <mavon-editor v-model="contentNew"  class="formContent" :editable="false" :subfield="false" defaultOpen="preview" :toolbarsFlag="false" /> -->
            <mavon-editor
              v-model="item.content"
              :subfield="false"
              defaultOpen="edit"
              placeholder="请输入升级内容"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer" v-if="uploadFlag">
      <el-button class="highBtnRe" @click="uploadVisibleClose" id="report_add_cancel"
        >取消</el-button
      >
      <el-button class="highBtn" @click="insertSave" id="report_add_sure">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import SparkMD5 from 'spark-md5'
import { mavonEditor } from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'
import { eidtUploadFiles, getbatchList } from '@/api/apiConfig/api.js'

export default {
  components: { mavonEditor },
  // uploadFlag: true sass升级中心，false 本地化升级包
  props: ['uploadVisible', 'uploadIdObj', 'uploadFlag', 'activeName'],
  data() {
    let self = this
    return {
      publicData: 1,
      editAssetsObj: {},
      batchList: [],
      file_list: [],
      currentBatch: '',
      loading: false,
      contentNew: '',
      ruleForm: {
        file_name: 'foradar_image.bz2',
        version: '',
        image_name: 'php-foradar'
      },
      mainLoading: false,
      uploadFormArr: [
        {
          version: '1.22.3',
          content: '',
          service: '',
          percentValue: 0
        }
      ],
      // uploadForm:{
      //   version:'',
      //   content:'',
      //   type:'1'
      // },
      uploaderUrl: [],
      editId: '',
      urlName: [],
      percentValue: 0,
      options: {
        // 上传地址
        target: function () {
          if (self.uploadFlag) {
            // foradar升级中心
            return `${self.uploadSrcIp}/foradar/versions/upload`
          } else {
            // foradar本地化
            return `${self.uploadSrcIp}/upgrade/upload`
          }
        },
        chunkSize: 102400 * 2 * 5, //1...MB
        // 上传并发数
        simultaneousUploads: 1, //并发上传数
        // 单文件上传
        singleFile: true,
        headers: {
          Authorization: localStorage.getItem('token')
        },
        // token请求头
        processParams(params) {
          return self.setAssets(params)
        },
        testChunks: true,
        // 操作断点续传及秒传
        checkChunkUploadedByResponse: function (chunk, message) {
          let objMessage = JSON.parse(message)
          let uploadList = []
          if (objMessage.data.uploaded !== null) {
            objMessage.data.uploaded.forEach((v) => {
              uploadList.push(Number(v))
            })
          }
          // 为了防止最后一片取消
          uploadList.pop()
          if (objMessage.data.skipUpload === true) {
            return true
          }
          return (uploadList || []).indexOf(chunk.offset + 1) >= 0
        }
      },
      rulesArr: [
        {
          content: [{ required: true, message: '请输入升级内容', trigger: 'blur' }],
          version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
          type: [{ required: true, message: '请选择升级类型', trigger: 'change' }]
        }
      ],
      restart: 0,
      uploader: null,
      uploaderIndex: 0 // 当前上传的分组
    }
  },
  watch: {
    activeName: {
      handler(val) {
        this.publicData = +val
      },
      immediate: true
    },
    uploadVisible: {
      handler(newName, oldName) {},
      // 开启深度监听
      deep: true
    },
    //  编辑
    uploadIdObj: {
      handler(newName, oldName) {
        this.editAssetsObj = newName
        if (this.editAssetsObj.id) {
          this.uploadFormArr = [
            {
              version: newName.version,
              content: newName.content,
              service: newName.service,
              percentValue: 0
            }
          ]
          this.uploaderUrl = [newName.address]
          this.editId = newName.id
          this.currentBatch = newName.batch
          getbatchList().then((res) => {
            if (res.code == 0) {
              this.batchList = res.data.filter((item) => {
                return item
              })
            }
          })
        } else {
          this.uploadFormArr = [
            {
              version: '',
              content: '',
              service: '',
              percentValue: 0
            }
          ]
          this.editId = ''
          this.urlName = []
        }
        this.percentValue = 0
      },
      immediate: true,
      // 开启深度监听
      deep: true
    }
  },
  methods: {
    addItem() {
      this.uploadFormArr.push({
        version: '',
        content: '',
        service: '',
        percentValue: 0
      })
      this.rulesArr.push({
        content: [{ required: true, message: '请输入升级内容', trigger: 'blur' }],
        version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
        type: [{ required: true, message: '请选择升级类型', trigger: 'change' }]
      })
    },
    // 删除
    deleteItem(index) {
      this.uploadFormArr.splice(index, 1)
      this.this.rulesArr.splice(index, 1)
    },
    uploadVisibleClose() {
      if (this.editId == '') {
        this.$emit('uploadVisibleClose', false)
      } else {
        this.$emit('uploadVisibleClose', true)
      }
    },
    onFileError(rootFile, file, response, chunk) {
      this.$message.error(`${response.message}`)
      this.loading = false
      this.uploadFormArr[this.uploaderIndex].percentValue = 0
      // this.percentValue=0
    },
    //生成32位随机数
    getNum() {
      var chars = [
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'j',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z'
      ]
      var nums = ''
      for (var i = 0; i < 32; i++) {
        var id = parseInt(Math.random() * 61)
        nums += chars[id]
      }
      return nums
    },
    async insertSave() {
      let obj = []
      let batch = ''
      // 升级中心上传多组值需要batch批次号
      if (this.uploadFormArr.length > 1) {
        batch = this.getNum()
      }
      if (!this.publicData) return this.$message.error('请选择服务器架构')
      this.uploadFormArr.forEach((item, index) => {
        obj.push({
          id: this.editId,
          version: item.version,
          content: item.content,
          service: item.service,
          address: this.uploaderUrl[index],
          batch: batch,
          system_type: this.publicData
        })
      })
      let res = await eidtUploadFiles(obj)
      if (res.code == 0) {
        this.$message.success('操作成功！')
        this.$emit('saveSuccess')
      }
    },
    onFileSuccess(rootFile, file, response, chunk) {
      this.$message.success('上传成功')
      this.loading = false
      // 版本号和服务提取
      this.urlName[this.uploaderIndex] = rootFile.name // 显示当前上传文件名称
      let extName = file.name.substring(file.name.lastIndexOf('.') + 1) // 后缀名称
      if (extName == 'dat') {
        // POC包，1.21.8_23041900_poc_update.dat
        let fileName = rootFile.name.split('_')
        this.uploadFormArr[this.uploaderIndex].version = fileName[0] // 自动补充版本号
        this.uploadFormArr[this.uploaderIndex].service = fileName.length >= 3 ? fileName[2] : '' // 自动补充service
      } else {
        // 1.22.3_file.tar.gz,文件格式固定的: 版本号_service.其他.gz
        let fileName = rootFile.name.split('_')
        this.uploadFormArr[this.uploaderIndex].version = fileName[0] // 自动补充版本号
        this.uploadFormArr[this.uploaderIndex].service = fileName[1].split('.')[0] // 自动补充service
      }

      this.$forceUpdate() // 更新名称数组长度
      // 本地化部署
      if (!this.uploadFlag) {
        // this.mainLoading=true
        this.uploadVisible = false
        this.$router.push({
          path: '/upgrading'
        })
      }
      if (response) {
        this.uploaderUrl[this.uploaderIndex] = JSON.parse(response).data.url
      }
    },
    setAssets(params) {
      let version = params.filename.split('_')[0] // 截取文件名称的版本号
      let data = []
      data.push(this.ruleForm)
      return {
        chunk_number: params.chunkNumber,
        total_chunks: params.totalChunks,
        filename: params.filename,
        identifier: params.identifier,
        total_size: params.totalSize,
        version: version, // this.uploadForm.version
        restart: this.restart // 离线需要restart字段
      }
    },
    // 点击选择文件
    onFileAdded(file, index) {
      this.uploaderIndex = index
      this['uploader' + this.uploaderIndex] =
        this.$refs['uploader' + this.uploaderIndex][0].uploader
      this.loading = true
      let name = file.name.substring(file.name.lastIndexOf('.') + 1)
      this.fileSize = (file.size / 1024 / 1024).toFixed(2)
      // if (this.fileSize > 2000) {
      //   this.$message.error("上传文件过大");
      //   this['uploader'+ this.uploaderIndex].cancel();
      // } else
      if (name != 'gz' && name != 'dat') {
        this.$message.error('不支持上传的文件格式')
        this['uploader' + this.uploaderIndex].cancel()
      } else {
        this.computeMD5(file)
      }
    },
    computeMD5(file) {
      let fileReader = new FileReader()
      let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice
      let currentChunk = 0
      const chunkSize = 10 * 1024 * 1000
      let chunks = Math.ceil(file.size / chunkSize)
      let spark = new SparkMD5.ArrayBuffer()
      file.pause()
      loadNext()
      fileReader.onload = (e) => {
        spark.append(e.target.result)
        if (currentChunk < chunks) {
          currentChunk++
          loadNext()
        } else {
          let md5 = spark.end()
          this.computeMD5Success(md5, file)
        }
      }
      fileReader.onerror = function () {
        this.$message.error(`文件${file.name}读取出错，请检查该文件`)
        file.cancel()
      }
      function loadNext() {
        let start = currentChunk * chunkSize
        let end = start + chunkSize >= file.size ? file.size : start + chunkSize
        fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
      }
    },
    // 文件进度的回调
    onFileProgress(rootFile, file, chunk) {
      this.uploadFormArr[this.uploaderIndex].percentValue =
        this['uploader' + this.uploaderIndex].progress() * 100
      // this.percentValue = this['uploader'+ this.uploaderIndex].progress() * 100;
      if (this['uploader' + this.uploaderIndex].progress() * 100 > 99) {
        this.loading = true
      } else {
        this.loading = false
      }
      let status = chunk.xhr.status
      let response = chunk.xhr.response
      if (response && JSON.parse(response).code !== 0) {
        //clearInterval(this.setTimfunc);
        this.$message.error(JSON.parse(response).message)
        // this.cancelUpgrade();
      }
      if (status == 401) {
        location.reload()
      }
    },
    computeMD5Success(md5, file) {
      // 将自定义参数直接加载uploader实例的opts上
      Object.assign(this['uploader' + this.uploaderIndex].opts, {
        query: {
          ...this.params
        }
      })
      file.uniqueIdentifier = md5
      this['uploader' + this.uploaderIndex].resume()
      //this.progress(md5);
      console.log('走这里了吗2')
    }
  }
}
</script>
<style lang="less" scoped>
.elDialogAdd .el-dialog .el-form .el-form-item {
  width: 98% !important;
}
.uploader-drop {
  width: 100%;
  // display:flex;
  // align-items: center;
  border: none !important;
  background: none !important;
  box-sizing: border-box !important;
  padding: 0 !important;
}
.uploader-btn {
  margin-right: 20px;
}
.uploaderIcon {
  width: 100%;
  height: 75px;
  text-align: center;
  box-sizing: border-box;
  i {
    font-size: 45px !important;
  }
}
.uploaderText {
  width: 100%;
  height: 14px;
  text-align: center;
  font-size: 14px;
  color: rgb(96, 98, 102);
  line-height: 14px;
  span {
    color: #409eff;
  }
}
.uploadIcon {
  width: 100%;
  font-size: 46px;
  display: flex;
  justify-content: center;
  height: 50px;
  align-items: center;
}
.uploadeProgress {
  width: 100%;
  height: 30px;
}
.uploadWarpBox {
  // background-image: url("../assets/imgages/uploaderBanner.png");
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9998;
  color: white;
}
.uploadClass {
  width: 100%;
  box-sizing: border-box;
  // padding: 0 40px 0 28px;
  // display: flex;
}
.footer {
  text-align: right;
  padding-top: 15px;
}
// .emptyBox {
//   height: 120px;
//   width: 100%;
// }
.uploader-file-status {
  display: none !important;
}
.uploadDetails {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  div {
    width: 100%;
    text-align: center;
  }
  .iconfont {
    font-size: 72px !important;
    color: #2a75f1 !important;
  }
  .uploadDetailsTitle {
    //width: 216px;
    height: 18px;
    font-size: 18px;
    font-family: MicrosoftYaHeiSemibold;
    color: #ffffff;
    line-height: 18px;
    margin-top: 51px;
  }
}
.uploadLeft {
  width: 110px;
  font-size: 14px;
  color: #606266;
  text-align: right;
  padding: 0 12px 0 0;
  box-sizing: border-box;
}
.uploadRight {
  width: 100%;
}
.uploadRightSpecial {
  width: 50%;
}
// .el-icon-upload{
//   font-size: 67px;
//   color: #C0C4CC;
// }
.el-divider__text {
  font-size: 16px;
  color: #2677ff;
}
.el-divider {
  background-color: rgb(64, 158, 255);
}
</style>
