<template>
  <div class="container" v-loading="loading">
    <div class="headerTitle">
      <span>
        URL(API)资产
        <span v-if="notifyFilterId">
          / {{ notifyFilterMsg }}
          <span style="color: #2677ff; cursor: pointer" @click="clearNotifyFilter">恢复默认</span>
        </span>
      </span>
      <taskTip class="taskTip" @updateList="getData" :tipDataInfo="tipData" />
    </div>
    <div class="home_header">
      <div class="filterTab">
        <div>
          <el-input
            v-model="formInline.keyword"
            placeholder="请输入关键字检索"
            @keyup.enter.native="checkFuncList"
            id="user_keycheck"
          >
            <el-button slot="append" icon="el-icon-search" @click="checkFuncList"></el-button>
          </el-input>
          <span @click="highCheckdialog = true" id="user_filter" style="width: 80px"
            ><img
              src="../../assets/images/filter.png"
              alt=""
              style="width: 16px; vertical-align: middle; margin-right: 3px"
            />高级筛选</span
          >
        </div>
        <div>
          <el-checkbox
            class="checkboxAll"
            v-model="checkedAll"
            @change="checkAllChange"
            id="user_all"
            >选择全部</el-checkbox
          >
          <el-button
            class="normalBtnRe"
            style="margin-left: 10px"
            type="primary"
            id="url_del"
            @click="delUrl"
            >删除</el-button
          >
          <el-button
            class="normalBtnRe"
            style="margin-left: 10px"
            type="primary"
            id="url_export"
            @click="exportUrl"
            :loading="exportLoadingBtn"
            :disabled="exportLoadingBtn"
            >导出</el-button
          >
          <el-button class="normalBtn" type="primary" id="url_import" @click="importVisible = true"
            >导入</el-button
          >
        </div>
      </div>
      <hightFilter
        id="hightFilter"
        :highTabShow="highTabShow"
        :highlist="highlist"
        :total="total"
        pageIcon="user"
        @highcheck="highCheck"
      ></hightFilter>
      <div :class="hightFilterIsShow()">
        <el-table
          border
          :data="tableData"
          v-loading="loading"
          row-key="id"
          :header-cell-style="{ background: '#F2F3F5', color: '#62666C' }"
          @selection-change="handleSelectionChange"
          @cell-mouse-enter="showTooltip"
          @cell-mouse-leave="hiddenTooltip"
          ref="eltable"
          height="100%"
          style="width: 100%"
        >
          <template slot="empty">
            <div class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
          </template>
          <el-table-column
            type="selection"
            align="center"
            :reserve-selection="true"
            :selectable="handleSelectable"
            width="55"
          >
          </el-table-column>
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :prop="item.name"
            align="left"
            :min-width="item.minWidth"
            :fixed="item.fixed"
            :label="item.label"
          >
            <template slot-scope="scope">
              <span v-if="item.name == 'url' && scope.row[item.name]">
                <el-tooltip
                  :open-delay="500"
                  class="item"
                  popper-class="chainClass"
                  effect="dark"
                  placement="top"
                >
                  <div slot="content">
                    {{ scope.row[item.name] }}
                    <el-tooltip
                      effect="light"
                      class="item"
                      placement="top"
                      content="一键复制"
                      :open-delay="500"
                    >
                      <i
                        class="el-icon-document-copy"
                        @click="copyTextText(scope.row['url'])"
                        style="color: #2677ff; cursor: pointer; margin-left: 6px"
                      ></i>
                    </el-tooltip>
                  </div>
                  <a
                    v-if="scope.row[item.name] && String(scope.row[item.name]).includes('http')"
                    :href="scope.row[item.name]"
                    target="_blank"
                    >{{ scope.row[item.name] }}</a
                  >
                  <p class="ellipsis" v-else>{{
                    scope.row[item.name] ? scope.row[item.name] : '-'
                  }}</p>
                </el-tooltip>
              </span>
              <span v-else>
                <template v-if="scope.row[item.name]">
                  <span>
                    <el-tooltip
                      :open-delay="500"
                      class="item"
                      popper-class="chainClass"
                      effect="dark"
                      placement="top"
                    >
                      <div slot="content">
                        {{ scope.row[item.name] }}
                      </div>
                      <span>{{ scope.row[item.name] }}</span>
                    </el-tooltip>
                  </span>
                </template>
                <template v-else> - </template>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-drawer title="高级筛选" :visible.sync="highCheckdialog" direction="rtl" ref="drawer">
      <div class="demo-drawer__content">
        <el-form :model="formInline" ref="drawerForm" label-width="84px">
          <el-form-item label="主域名：" prop="top_domain">
            <el-select
              v-model="formInline.top_domain"
              placeholder="请选择主域名"
              multiple
              collapse-tags
              @change="selectChange($event, 'top_domain', condition.top_domain, false, true)"
            >
              <el-option
                :label="v"
                :value="v"
                v-for="(v, i) in condition.top_domain"
                :key="i"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网站标题：" prop="title">
            <el-select
              v-model="formInline.title"
              placeholder="请选择网站标题"
              multiple
              collapse-tags
              @change="selectChange($event, 'title', condition.title, false, true)"
            >
              <el-option
                :label="v"
                :value="v"
                v-for="(v, i) in condition.title"
                :key="i"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="域名：" prop="domain">
            <el-select
              v-model="formInline.domain"
              placeholder="请选择域名"
              multiple
              collapse-tags
              @change="selectChange($event, 'domain', condition.domain, false, true)"
            >
              <el-option
                :label="v"
                :value="v"
                v-for="(v, i) in condition.domain"
                :key="i"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现时间：" prop="created_at">
            <el-date-picker
              v-model="formInline.created_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="更新时间：" prop="updated_at">
            <el-date-picker
              v-model="formInline.updated_at"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="highBtnRe" @click="resetForm()" id="user_filter_repossess"
            >重置</el-button
          >
          <el-button class="highBtn" @click="checkFuncList" id="user_filter_select">筛选</el-button>
        </div>
      </div>
    </el-drawer>
    <batchImport
      ref="batchImport"
      title="批量文件导入"
      uploadAction="/import_url_api"
      :btnLoading="importBtnLoading"
      :dialogVisible="importVisible"
      @save="importSave"
      @closeDialog="closeImportDialog"
      downloadForbidUrlName="URL-API导入模版"
      downloadForbidUrl="/downloadTemplate/URL-API导入模版.xlsx"
    />
  </div>
</template>

<script>
import taskTip from './taskTip.vue'
import tableTooltip from '../../components/tableTooltip/tableTooltip.vue'
import hightFilter from '../../components/assets/highTab.vue'
import batchImport from '../../components/assets/batchImport.vue'
import { mapGetters, mapState } from 'vuex'
import {
  urlList,
  urlListExport,
  urlListImport,
  urlListAdd,
  urlListDel
} from '@/api/apiConfig/api.js'

export default {
  components: { tableTooltip, hightFilter, batchImport, taskTip },
  data() {
    return {
      exportLoadingBtn: false,
      notifyFilterMsg: '',
      notifyFilterId: '',
      tipData: {},
      uploadAction: '',
      condition: {
        domain: [],
        title: [],
        url: []
      },
      importVisible: false,
      importBtnLoading: false,
      roleArr: [],
      tableHeader: [
        {
          label: 'URL',
          name: 'url',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '网站标题',
          name: 'title',
          fixed: 'left',
          minWidth: 120
        },
        // {
        //   label: '主域名',
        //   name: 'top_domain',
        //   fixed: 'left',
        //   minWidth: 120
        // },
        {
          label: '域名',
          name: 'domain',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '发现时间',
          name: 'created_at',
          fixed: 'left',
          minWidth: 120
        },
        {
          label: '更新时间',
          name: 'updated_at',
          fixed: 'left',
          minWidth: 120
        }
      ],
      checkedAll: false,
      loading: false,
      checkedArr: [],
      tableCellMouse: {
        cellDom: null, // 鼠标移入的cell-dom
        hidden: null, // 是否移除单元格
        row: null // 行数据
      },
      currentPage: 1,
      pageSize: 10,
      tableData: [],
      highCheckdialog: false,
      formInline: {},
      total: 0,
      highlist: null,
      highTabShow: [
        {
          label: '网站标题',
          name: 'title',
          type: 'select'
        },
        {
          label: '域名',
          name: 'domain',
          type: 'select'
        },
        {
          label: '发现时间',
          name: 'created_at',
          type: 'date'
        },
        {
          label: '更新时间',
          name: 'updated_at',
          type: 'date'
        }
      ],
      user: {
        role: ''
      }
    }
  },
  watch: {
    getterCompanyChange(val) {
      if (this.notifyFilterId) {
        this.clearNotifyFilter()
      }
    },
    getterCurrentCompany(val) {
      this.currentPage = 1
      // 切换账号去除全选
      this.checkedAll = false
      this.tableData.forEach((row) => {
        this.$refs.eltable.toggleRowSelection(row, false)
      })
      if (this.user.role == 2) {
        this.getData()
      }
    }
  },
  computed: {
    ...mapGetters(['getterCurrentCompany', 'getterCompanyChange']),
    ...mapState(['currentCompany'])
  },
  mounted() {
    this.notifyFilterId = this.$route.query.notifyFilterId
    this.notifyFilterMsg = this.$route.query.notifyFilterMsg
    let userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (userInfo) {
      this.user = userInfo.user
    }
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getData()
    } else {
      this.getData()
    }
  },
  methods: {
    clearNotifyFilter() {
      this.$router.replace({ path: '/urlAsset', query: {} })
    },
    copyTextText(text) {
      this.$copyText(text).then(
        (res) => {
          this.$message.success('复制成功')
        },
        (err) => {
          this.$message.error('复制失败')
        }
      )
    },
    async importSave(data) {
      if (!data.data_list || data.data_list.length == 0) {
        this.$message.error('请上传文件')
        return
      }
      this.importBtnLoading = true
      let res = await urlListAdd({
        operate_company_id: this.currentCompany,
        data: data.data_list
      }).catch(() => {
        this.importBtnLoading = false
      })
      if (res.code == 0) {
        this.importVisible = false
        this.importBtnLoading = false
        this.getData()
      }
    },
    closeImportDialog() {
      this.importVisible = false
      this.$refs.batchImport.fileDataArray = []
      this.$refs.batchImport.fileList = []
    },
    async exportUrl() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      let params = {
        ...this.formInline,
        id: this.checkedAll
          ? []
          : this.checkedArr.map((item) => {
              return item.id
            }),
        operate_company_id: this.currentCompany
      }
      try {
        this.exportLoadingBtn = true
        let res = await urlListExport(params)
        if (res.code == 0) {
          this.$message.success('导出成功！')
          this.download(this.showSrcIp + res.data.url)
          this.$refs.eltable.clearSelection()
          this.checkedAll = false
          this.exportLoadingBtn = false
        }
      } catch (error) {
        this.exportLoadingBtn = false
      }
    },
    delUrl() {
      if (this.checkedArr.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      this.$confirm('确定删除数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'cloud_info_del_cancel',
        confirmButtonClass: 'cloud_info_del_sure',
        customClass: 'cloud_info_del',
        type: 'warning'
      }).then(async () => {
        let params = {
          ...this.formInline,
          id: this.checkedAll
            ? []
            : this.checkedArr.map((item) => {
                return item.id
              }),
          operate_company_id: this.currentCompany
        }
        let res = await urlListDel(params)
        if (res.code == 0) {
          this.$message.success('操作成功！')
          this.checkedAll = false
          this.$refs.eltable.clearSelection()
          this.getData()
        }
      })
    },
    resetForm() {
      this.formInline = {
        url: '',
        title: '',
        domain: '',
        created_at: [],
        updated_at: []
      }
    },
    async getData() {
      this.loading = true
      this.formInline.website_message_id = this.notifyFilterId || ''
      let obj = {
        page: this.currentPage,
        per_page: this.pageSize,
        ...this.formInline,
        operate_company_id: this.currentCompany
      }
      let res = await urlList(obj).catch((error) => {
        this.loading = false
      })
      this.loading = false
      if (res.code == 0) {
        this.tableData = res.data.items
        this.total = res.data.total
        this.condition = res.data.condition
        // 全选操作
        if (this.checkedAll) {
          this.tableData.forEach((row) => {
            this.$refs.eltable.toggleRowSelection(row, true)
          })
        }
        this.tipData = res.data.url_api_task
        if ((!this.total || this.total == 0) && this.notifyFilterId) {
          this.$message.error('该批新增URL(API)资产已被删除')
        }
      }
    },
    checkAllChange() {
      if (this.checkedAll) {
        this.tableData.forEach((row) => {
          this.$refs.eltable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.eltable.clearSelection()
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    handleSelectable(row, index) {
      return !this.checkedAll
    },
    // 鼠标移入cell
    showTooltip(row, column, cell) {
      this.tableCellMouse.cellDom = cell
      this.tableCellMouse.row = row
      this.tableCellMouse.hidden = false
    },
    // 鼠标移出cell
    hiddenTooltip() {
      this.tableCellMouse.hidden = true
    },
    handleSelectionChange(val) {
      this.checkedArr = val
    },
    highCheck(val) {
      this.formInline = Object.assign(this.highlist)
      this.checkFuncList()
    },
    // 高级筛选
    checkFuncList() {
      this.highCheckdialog = false
      this.currentPage = 1
      this.highlist = Object.assign({}, this.formInline) // 用于高级筛选标签显示
      this.hightFilterIsShow()
      this.getData()
    },
    hightFilterIsShow() {
      let bol = ''
      if (
        document.getElementById('hightFilter') &&
        document.getElementById('hightFilter').offsetHeight > 0
      ) {
        bol = 'tableWrap tableWrapFilter'
      } else {
        bol = 'tableWrap'
      }
      return bol
    },
    // val:下拉选中项，name:v-model字段值，arr:下拉列表，isCh:是否需要转换中文，isMul:是否多选
    selectChange(val, name, arr, isCh, isMul) {
      if (isMul) {
        // 下拉多选
        this.formInline['ch_' + name] = []
        val.forEach((item) => {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (item == ar.id || item == ar.value) {
                this.formInline['ch_' + name].push(ar.name)
              }
            } else {
              // 不需要转换中文的
              if (item == ar) {
                this.formInline['ch_' + name].push(ar)
              }
            }
          })
        })
      } else {
        // 下拉单选
        this.formInline['ch_' + name] = ''
        if (!String(val)) {
          this.formInline['ch_' + name] = ''
        } else {
          arr.forEach((ar) => {
            if (isCh) {
              // 需要转换中文的
              if (val == ar.id || val == ar.value) {
                this.formInline['ch_' + name] = ar.name
              }
            } else {
              // 不需要转换中文的
              if (val == ar) {
                this.formInline['ch_' + name] = ar
              }
            }
          })
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.headerTitle {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .taskTip {
    font-weight: 400;
  }
}
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.home_header {
    position: relative;
    height: 100%;
    .filterTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      & > div {
        display: flex;
        align-items: center;
        .normalBtnRe {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-input {
          width: 240px;
        }
        & > span {
          font-weight: 400;
          color: #2677ff;
          line-height: 20px;
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }
    .tableWrapFilter {
      height: calc(100% - 180px) !important;
    }
    .tableWrap {
      height: calc(100% - 129px);
      padding: 0px 20px;
    }
  }
}
.emptyClass {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  svg {
    display: inline-block;
    font-size: 120px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
