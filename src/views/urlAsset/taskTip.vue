<template>
  <div>
    <div v-if="tipData">
      <el-tooltip placement="top" :open-delay="500">
        <div slot="content" style="display: flex">
          <span style="color: #62666c">任务记录：</span>
          <span class="task">{{ `${tipData.name}任务` }}</span>
          <span
            class="myStatus"
            :class="item.className"
            v-for="item in statusMap"
            :key="item.status"
          >
            <span v-if="tipData.status == item.status">{{ item.name }}</span>
          </span>
          <span v-if="tipData.status == 1 || tipData.status == 4"
            >{{ `${tipData.progress}%` }}<span class="tipDataContent"></span
          ></span>
        </div>
        <div style="display: flex !important; align-items: center">
          <span style="color: #62666c">任务记录：</span>
          <span class="task">{{ `${tipData.name}任务` }}</span>
          <span
            class="myStatus"
            :class="item.className"
            v-for="item in statusMap"
            :key="item.status"
          >
            <span v-if="tipData.status == item.status">{{ item.name }}</span>
          </span>
          <span v-if="tipData.status == 1 || tipData.status == 4"
            >{{ `${tipData.progress}%` }}<span class="tipDataContent"></span
          ></span>
        </div>
      </el-tooltip>
    </div>
    <div v-else>暂无任务</div>
  </div>
</template>

<script>
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import { mapState, mapGetters } from 'vuex'

export default {
  props: {
    tipDataInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // currentId: '',
      loading: false,
      statusMap: [
        {
          name: '等待执行',
          className: 'mygrayStatus',
          status: 0
        },
        {
          name: '执行中',
          className: 'myblueStatus',
          status: 1
        },
        // {
        //   name:'扫描完成',
        //   className:'mygreenStatus',
        //   status:2
        // },
        // {
        //   name:'扫描失败',
        //   className:'myredStatus',
        //   status:3
        // },
        // {
        //   name:'暂停扫描',
        //   className:'myyellowStatus',
        //   status:4
        // },
        {
          name: '执行成功',
          className: 'myyellowStatus',
          status: 5
        }
      ]
    }
  },
  computed: {
    tipData() {
      return this.tipDataInfo
    },
    ...mapState(['currentCompany']),
    ...mapGetters(['getterWebsocketMessage'])
  },
  watch: {
    // // 监听到端有返回信息
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    }
  },
  methods: {
    handleMessage(res, o) {
      //处理接收到的信息
      if (
        res.cmd == 'url_api_progress' &&
        this.tipData &&
        this.tipData.id &&
        this.tipData.id == res.data.task_id
      ) {
        this.runningFunc(res)
      }
    },
    // websocket执行
    runningFunc(res) {
      if (res.data.status == 5) {
        resetMessage.success('执行成功！')
        // this.currentId = '' // 控制推送结束后仅执行一次
        this.loading = true
        setTimeout(() => {
          this.$emit('updateList')
        }, 2000)
      } else if (res.data.status == 1) {
        // 正在扫描
        // this.currentId = res.data.task_id
        // 推送数据渲染到列表
        this.$set(this.tipData, 'status', res.data.status)
        this.$set(this.tipData, 'target', res.data.target)
        this.$set(this.tipData, 'progress', res.data.progress)
        this.$set(this.tipData, 'use_seconds', res.data.use_seconds)
        this.$set(this.tipData, 'start_at', res.data.start_at)
      } else if (res.data.status == 4) {
        // 暂停扫描
      } else {
        // 3 扫描失败
        this.$emit('updateList')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.myStatus {
  display: flex;
  align-items: center;
}
.mygreenStatus {
  color: #10d58c !important;
}
.myyellowStatus {
  color: #ffbc00 !important;
}
.mygrayStatus {
  color: gray !important;
}
.myblueStatus {
  color: #2677ff !important;
}
.myredStatus {
  color: #de3737 !important;
}
.tipDataContent {
  color: #62666c !important;
}
</style>
