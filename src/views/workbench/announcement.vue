<template>
  <div class="conAnnounce">
    <div class="conAssets-top">
      <span
        class="title"
        :class="{ active: item.value == activeItem.value }"
        @click="handleClick(item)"
        v-for="item in tabList"
        :key="item.value"
        >{{ item.label }}</span
      >
      <!-- <img src="../../assets/images/arrowRight.png" alt="" /> -->
    </div>
    <div class="announce-list" v-loading="loading">
      <template v-if="activeItem.value == 'notice' && announceList.length != 0">
        <ul class="con-list">
          <li class="notice-li" v-for="(item, index) in announceList" :key="index">
            <el-tooltip placement="top" :content="item.notice" class="list-tool">
              <p class="li-content">
                {{ item.notice }}
              </p>
            </el-tooltip>
            <p class="li-time"> 更新时间：{{ item.up_at_start }}至{{ item.up_at_end }} </p>
            <p class="li-bottom">
              <span class="status">系统公告</span>
              <span class="time">{{ item.created_at }}</span>
            </p>
          </li>
        </ul>
      </template>
      <template v-else-if="activeItem.value == 'dataLeak' && dataListData.length != 0">
        <ul class="con-list">
          <vue-seamless-scroll
            :data="dataListData"
            class="list-main"
            id="screenListBox"
            :class-option="classOption"
            ref="seamlessScroll"
          >
            <li class="intelligence-li" v-for="(item, index) in dataListData" :key="index">
              <el-tooltip
                class="item"
                effect="dark"
                :content="item.special_project_name"
                placement="top"
              >
                <span class="name-box">
                  <span class="name">
                    {{ item.special_project_name }}
                  </span>
                </span>
              </el-tooltip>
              <span style="display: flex">
                <span class="info-item" style="flex: 1">
                  <span class="label">涉及源地址数量：</span>
                  <span class="value">{{ item.data_volume }}</span>
                </span>
                <span class="time" style="width: 168px">{{ item.last_update_time }}</span>
              </span>
            </li>
          </vue-seamless-scroll>
        </ul>
      </template>
      <template v-else-if="activeItem.value == 'special' && eventListData.length != 0">
        <ul class="con-list" @click="scrollClick($event)">
          <vue-seamless-scroll
            :data="eventListData"
            class="list-main"
            id="screenListBox"
            :class-option="classOption"
            ref="seamlessScroll"
          >
            <li class="intelligence-li" v-for="(item, index) in eventListData" :key="index">
              <template v-if="item.name">
                <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                  <span class="name-box">
                    <span class="name event">
                      {{ item.name }}
                    </span>
                    <span class="grade redLine">{{ item.category }}</span>
                  </span>
                </el-tooltip>
              </template>
              <span class="name" v-else>---</span>
              <span style="display: flex">
                <span style="flex: 1">
                  <span class="info-item">
                    <span class="label">互联网影响面：</span>
                    <span class="value">{{ item.ip_count }}</span>
                  </span>
                  <span class="info-item">
                    <span class="label">披露时间：</span>
                    <span class="value">{{ item.creation_time }}</span>
                  </span>
                </span>
                <span class="time" style="width: 168px">{{ item.creation_time }}</span>
              </span>
            </li>
          </vue-seamless-scroll>
          <!-- <li v-if="eventCount > 10">
            <span class="more-data">
              查看更多
              <svg class="icon svg-icon" aria-hidden="true">
                <use xlink:href="#icon-more-right"></use>
              </svg>
              <svg class="icon svg-icon" aria-hidden="true">
                <use xlink:href="#icon-more-right"></use>
              </svg>
            </span>
          </li> -->
        </ul>
      </template>

      <div v-else class="emptyClass">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-kong"></use>
        </svg>
        <p>暂无数据</p>
      </div>
      <div class="fix-end" v-if="activeItem.value != 'notice'">
        <span class="more-data" @click="jumpToIntelligence()">
          查看更多
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-more-right"></use>
          </svg>
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-more-right"></use>
          </svg>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import vueSeamlessScroll from 'vue-seamless-scroll'
import { announcementList, dataList, eventList } from '@/api/apiConfig/api.js'

export default {
  components: { vueSeamlessScroll },
  data() {
    return {
      limitMoveNumMax: 0,
      classOption: {
        step: 0.6, // 数值越大速度滚动越快
        limitMoveNum: this.limitMoveNumMax, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      },
      loading: false,
      announceList: [],
      user: {},
      userInfo: {},
      activeItem: { label: '专项报警', value: 'special' },
      tabList: [
        { label: '专项报警', value: 'special' },
        { label: '数据情报', value: 'dataLeak' },
        { label: '通知公告', value: 'notice' }
      ],
      dataListData: [],
      hotCount: 0,
      eventListData: [],
      eventCount: 0,
      riskLevelArrClassMap: {
        低危: 'yellowLine',
        中危: 'originLine',
        高危: 'redLine',
        严重: 'deepRedLine'
      }
    }
  },

  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      await this.getEventList()
    } else {
      await this.getEventList()
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getEventList()
      }
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    jumpToIntelligence() {
      this.changeMenuId('10-3')
      sessionStorage.setItem('menuId', '10-3')
      this.$router.push({
        path: '/intelligenceCenterv1',
        query: { actTabVal: this.activeItem.value }
      })
    },
    scrollClick(e) {
      let data = e.target
      console.log(data, '---scrollClick')
    },
    async getData() {
      this.loading = true
      let res = await announcementList({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.announceList = res.data
        for (var i = 0; i < this.announceList.length; i++) {
          this.announceList[i].created_at = this.announceList[i].up_at_end.slice(0, 10)
          // this.announceList[i].notice = this.announceList[i].notice.slice(0, 12)
        }
      }
    },
    async getHotData() {
      this.loading = true
      let res = await dataList({
        page: 1,
        per_page: 10,
        sort: ['last_update_time desc'],
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      this.dataListData = res.data.items || []
      this.hotCount = res.data.total || 0
      this.loading = false
    },
    async getEventList() {
      this.loading = true
      let res = await eventList({
        page: 1,
        per_page: 10,
        sort: ['creation_time desc'],
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      this.eventListData = res.data.items || []
      this.eventCount = res.data.total || 0
      this.loading = false
    },
    handleClick(item) {
      this.activeItem = item
      if (item.value == 'dataLeak') {
        this.getHotData()
      } else if (item.value == 'special') {
        this.getEventList()
      } else {
        this.getData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  }
}
</script>

<style lang="less" scoped>
.conAnnounce {
  width: 35%;
  // flex: 1;
  height: 490px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);

  .conAssets-top::before {
    content: '';
    position: absolute;
    left: 0px;
    top: 18px;
    width: 4px;
    height: 16px;
    background-color: #2677ff;
    border-radius: 0 2px 2px 0;
  }

  .conAssets-top {
    cursor: pointer;
    position: relative;
    // cursor: pointer;
    height: 47px;
    line-height: 47px;
    // padding-top: 12px;
    padding-left: 16px;
    border-bottom: 1px solid #e9ebef;

    & > .title {
      box-sizing: border-box;
      // font-family: PingFangSC-Semibold;
      display: inline-block;
      height: 46px;
      color: #62666c;
      font-size: 16px;
      vertical-align: middle;
      margin-right: 24px;

      &.active {
        font-weight: 600;
        color: #2677ff;
        border-bottom: 2px solid #2677ff;
      }
    }

    img {
      margin-left: 8px;
      vertical-align: middle;
    }
  }

  .announce-list {
    box-sizing: border-box;
    width: 100%;
    // width: calc(100% - 32px);
    height: calc(100% - 48px);
    // overflow: auto;
    position: relative;
    padding-bottom: 45px;

    .con-list {
      height: 100%;
      overflow: hidden;
      padding: 0 16px;

      .intelligence-li {
        padding: 20px 0 16px;
        // display: flex;
        // justify-content: space-between;
        // align-items: center;
        border-bottom: 1px solid #e9ebef;

        .icon {
          font-size: 8px;
          margin-right: 8px;
          color: #ccd3e1;
          &.active {
            color: #2677ff;
          }
        }
        .name-box {
          width: 100%;
          display: flex;
        }
        .name {
          width: calc(100% - 58px);
          margin-bottom: 10px;
          color: #37393c;
          font-size: 14px;
          font-weight: 600;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          &.event {
            width: calc(100% - 86px);
          }
        }
        .info-item {
          display: block;
          margin-bottom: 8px;
          color: #62666c;
          .value {
            color: #37393c;
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
        .time {
          color: #a8acb3;
          text-align: right;
        }
      }
    }
    .fix-end {
      box-sizing: border-box;
      position: absolute;
      bottom: 0;
      width: calc(100% - 32px);
      height: 138px;
      margin: 0 16px;
      padding-top: 82px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 58%);
    }
    .more-data {
      display: inline-block;
      width: 100%;
      height: 40px;
      cursor: pointer;
      color: #2677ff;
      text-align: center;
      line-height: 40px;
      background: rgba(38, 119, 255, 0.08);
      backdrop-filter: blur(4px);
      .icon {
        height: 12px;
        width: 6px;
        margin-right: 0;
      }
    }
    // .con-list {
    //   width: 100%;
    .notice-li {
      width: 100%;
      // width: calc(100% - 32px);
      // height: 109px;
      // font-family: PingFangSC-Regular;
      border-bottom: 1px solid #e9ebef;

      .item {
        margin: 4px;
      }

      .li-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-top: 10px;
        width: 100%;
        height: 20px;
        line-height: 20px;
        // font-family: PingFangSC-Semibold;
        color: #37393c;
        margin-bottom: 4px;
        font-weight: 600;
      }

      .li-time {
        color: #a8acb3;
        font-size: 12px;
        margin-bottom: 8px;
      }

      .li-bottom {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;

        .status {
          line-height: 24px;
          padding: 0 8px 0 8px;
          font-size: 12px;
          color: #2677ff;
          background: rgba(38, 119, 255, 0.12);
          border-radius: 2px;
        }

        .time {
          color: #a8acb3;
          // font-family: PingFangSC-Regular;
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
    // }
  }

  .emptyClass {
    height: 444px;
    text-align: center;
    vertical-align: middle;

    svg {
      display: inline-block;
      font-size: 120px;
      margin-top: 150px;
    }

    p {
      line-height: 25px;
      color: #d1d5dd;

      span {
        margin-left: 4px;
        color: #2677ff;
        cursor: pointer;
      }
    }
  }
}

/deep/ .el-tooltip__popper {
  width: 20% !important;
}

.el-tooltip__popper {
  width: 20% !important;
}
.data-list {
  width: calc(100% - 32px);
  height: 425px;
  overflow: auto;
  padding: 0 16px;
  & > li {
    height: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ebef;
    &:last-child {
      border: none;
    }
    .icon {
      font-size: 8px;
      margin-right: 8px;
      color: #ccd3e1;
      &.active {
        color: #2677ff;
      }
    }
    .name {
      width: calc(100% - 72px);
      color: #37393c;
      font-size: 14px;
      font-weight: 600;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .time {
      color: #a8acb3;
      text-align: right;
    }
  }
  .more-data {
    width: 100%;
    height: 40px;
    color: #2677ff;
    text-align: center;
    line-height: 40px;
    background: rgba(38, 119, 255, 0.08);
    backdrop-filter: blur(4px);
    .icon {
      height: 12px;
      width: 6px;
      margin-right: 0;
    }
  }
}
</style>
