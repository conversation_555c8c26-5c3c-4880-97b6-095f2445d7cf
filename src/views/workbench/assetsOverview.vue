<template>
  <div class="conAssets" v-loading="loading">
    <div class="conAssets-box">
      <div class="conAssets-top">
        <span class="title">资产总览</span>
        <img src="../../assets/images/arrowRight.png" alt="" />
      </div>
      <div class="assets-box">
        <div class="assets-top">
          <div class="top-left">
            <div class="taizhang">
              <div class="taizhang-box">
                <div class="left-box">
                  <div class="taizhang-left">
                    <p class="left-title" @click="handleDoing('1-3-1', '/assetsLedger')"
                      ><i class="blue-star"></i>资产台账</p
                    >
                    <p class="left-num">
                      <span class="number">
                        {{ myOverview.sure_ip_num ? myOverview.sure_ip_num : 0 }}
                      </span>
                      <span
                        class="rate-blue"
                        :class="{
                          'rate-red': myRateData.account_rate < 0,
                          'rate-gray': myRateData.account_rate == 0
                        }"
                      >
                        <i
                          style="margin-right: 8px"
                          :class="{
                            'el-icon-minus': myRateData.account_rate == 0,
                            'el-icon-caret-top': myRateData.account_rate > 0,
                            'el-icon-caret-bottom': myRateData.account_rate < 0
                          }"
                          >{{
                            Math.abs(myRateData.account_rate) == 0
                              ? 0
                              : Math.abs(myRateData.account_rate).toFixed(1)
                          }}%</i
                        >
                        <el-tooltip
                          :open-delay="500"
                          content="当前月对比上个月新增数量"
                          placement="top"
                          effect="dark"
                        >
                          <i
                            class="el-icon-question"
                            style="color: #b1bdd1; margin: 3px 0px 0px 1px; font-size: 14px"
                          ></i>
                        </el-tooltip>
                      </span>

                      <!-- <span class="rate-blue">
                        <i class="el-icon-caret-top">{{
                            (Math.abs(myRateData.account_rate) * 100).toFixed()
                          }}%</i></span> -->
                    </p>
                  </div>
                  <div id="asset-account"></div>
                  <!-- <img src="../../assets/images/workbench/zichantaizhang.png" alt="" /> -->
                </div>
                <div class="left-list">
                  <div class="left-li" v-for="(item, index) in footerArr" :key="index">
                    <span class="list-title">
                      {{ item.title }}
                    </span>
                    <span class="list-num">{{ myOverview[item.word] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="top-right" v-for="(assetsItem, index) of rightArr" :key="index">
            <div class="right-conbox">
              <p class="right-title" @click="handleDoing(assetsItem.id, assetsItem.path)">
                <i class="yel-star" :class="index == 1 ? 'red-star' : ''"></i>{{ assetsItem.title }}
              </p>
              <p class="right-num">
                <span class="number">
                  {{ myOverview[assetsItem.word] ? myOverview[assetsItem.word] : 0 }}
                </span>
                <span class="rate-yel" :class="index == 1 ? 'rate-red' : ''">
                  <i
                    style="margin-right: 8px"
                    :class="{
                      'el-icon-minus': myRateData[assetsItem.rate] == 0,
                      'el-icon-caret-top': myRateData[assetsItem.rate] > 0,
                      'el-icon-caret-bottom': myRateData[assetsItem.rate] < 0
                    }"
                    >{{
                      Math.abs(myRateData[assetsItem.rate]) == 0
                        ? 0
                        : Math.abs(myRateData[assetsItem.rate]).toFixed(1)
                    }}%
                  </i>
                  <el-tooltip
                    :open-delay="500"
                    content="当前月对比上个月新增数量"
                    placement="top"
                    effect="dark"
                  >
                    <i
                      class="el-icon-question"
                      style="color: #b1bdd1; margin: 3px 0px 0px 1px; font-size: 14px"
                    ></i>
                  </el-tooltip>
                </span>
              </p>
            </div>
            <div id="unclaim-account" v-if="assetsItem.word == 'unsure_ip_num'"></div>
            <div id="threaten-account" v-if="assetsItem.word == 'threaten_ip_num'"></div>
            <!-- <img :src="assetsItem.img" alt="" /> -->
          </div>
        </div>
        <div class="assets-bottom">
          <div class="bottom-list" v-for="(item, n) in boxData" :key="n">
            <div class="bottom-list-box">
              <div style="margin: 0 auto; margin-bottom: 10px">
                <p class="right-title" @click="handleDoing(item.id, item.path)">
                  <i class="blue-star"></i>{{ item.title }}
                </p>
                <p class="right-num">
                  <span class="number">
                    {{ myOverview[item.word] ? myOverview[item.word] : 0 }}
                  </span>
                  <!-- <span class="rate-blue" :class="{'rate-red':}"> -->
                  <span
                    class="rate-blue"
                    :class="{
                      'rate-red': myRateData[item.rate] < 0,
                      'rate-gray': myRateData[item.rate] == 0
                    }"
                  >
                    <i
                      :class="{
                        'el-icon-minus': myRateData[item.rate] == 0,
                        'el-icon-caret-top': myRateData[item.rate] > 0,
                        'el-icon-caret-bottom': myRateData[item.rate] < 0
                      }"
                      >{{
                        Math.abs(myRateData[item.rate]) == 0
                          ? 0
                          : (Math.abs(myRateData[item.rate]) * 100).toFixed(1)
                      }}%</i
                    >
                  </span>
                  <!-- <span class="rate-blue">
                    <i class="el-icon-minus" v-if="myRateData[item.rate] == 0">{{
                      (Math.abs(myRateData[item.rate]) * 100).toFixed()
                    }}%</i>
                  </span> -->
                </p>
              </div>
              <div class="list-li" v-for="(liItem, num) in item.footer" :key="num">
                <span class="li-title">{{ liItem.title }}</span>
                <span class="li-num">{{ myOverview[liItem.word] }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import { getOverviewStatistics, assetsViewRateCount } from '@/api/apiConfig/api.js'

export default {
  data() {
    return {
      last_month_range: null,
      now_month_range: null,
      assetsChart: null,
      unclaimChart: null,
      threatenChart: null,
      loading: false,
      myRateData: {
        account_rate: 0,
        unsure_rate: 0,
        threaten_rate: 0,
        digital_rate: 0,
        login_rate: 0,
        domain_rate: 0,
        cert_rate: 0
      },
      myOverview: {
        app_num: 0,
        sure_ip_num: 0,
        sure_port_num: 0,
        sure_rule_num: 0,
        threaten_ip_num: 0,
        threaten_port_num: 0,
        threaten_rule_num: 0,
        unsure_ip_num: 0,
        unsure_port_num: 0,
        unsure_rule_num: 0,
        digital_num: 0,
        wechat_num: 0,
        xiaochengxu_num: 0,
        login_total: 0,
        login_to_confirmed: 0,
        login_confirmed: 0,
        login_ignore: 0,
        domain_total: 0,
        domain_father: 0,
        domain_child: 0,
        cert_total: 0,
        cert_valid: 0,
        cert_notValid: 0
      }, //资产概览-数量统计
      rightArr: [
        {
          title: '疑似资产',
          word: 'unsure_ip_num',
          rate: 'unsure_rate',
          img: require('../../assets/images/workbench/yisigzt.png'),
          id: '1-4',
          path: '/unclaimCloud'
        },
        {
          title: '威胁资产',
          word: 'threaten_ip_num',
          rate: 'threaten_rate',
          img: require('../../assets/images/workbench/wxgzt.png'),
          id: '3-4',
          path: '/threatAssets'
        }
      ],
      footerArr: [
        {
          title: '端口数',
          word: 'sure_port_num'
        },
        {
          title: '组件数',
          word: 'sure_rule_num'
        }
      ],
      boxData: [
        {
          title: '数字资产',
          word: 'digital_num',
          rate: 'digital_rate',
          img: require('../../assets/images/taskicon4.png'),
          id: '1-7',
          path: '/newAssets',
          footer: [
            {
              title: '公众号',
              word: 'wechat_num'
            },
            {
              title: '小程序',
              word: 'xiaochengxu_num'
            },
            {
              title: 'APP',
              word: 'app_num'
            }
          ]
        },
        {
          title: '登录入口',
          word: 'login_total',
          rate: 'login_rate',
          img: require('../../assets/images/taskicon5.png'),
          id: '1-3-2',
          path: '/loginEntry',
          footer: [
            {
              title: '待确认',
              word: 'login_to_confirmed'
            },
            {
              title: '已确认',
              word: 'login_confirmed'
            },
            {
              title: '已忽略',
              word: 'login_ignore'
            }
          ]
        },
        {
          title: '域名资产',
          word: 'domain_total',
          rate: 'domain_rate',
          img: require('../../assets/images/taskicon6.png'),
          id: '1-3-3',
          path: '/domainAsset',
          footer: [
            {
              title: '主域名',
              word: 'domain_father'
            },
            {
              title: '子域名',
              word: 'domain_child'
            }
          ]
        },
        {
          title: '证书资产',
          word: 'cert_total',
          rate: 'cert_rate',
          img: require('../../assets/images/taskicon7.png'),
          id: '1-3-4',
          path: '/certAsset',
          footer: [
            {
              title: '可信',
              word: 'cert_valid'
            },
            {
              title: '不可信',
              word: 'cert_notValid'
            }
          ]
        }
      ],
      user: {}
    }
  },
  async mounted() {
    this.initInChart()

    if (this.user.role == 2) {
      if (!this.currentCompany) return
      await this.getData()
    } else {
      await this.getData()
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    initInChart() {
      this.assetsChart = this.$echarts.init(document.getElementById('asset-account'), null, {
        renderer: 'svg'
      })
      this.unclaimChart = this.$echarts.init(document.getElementById('unclaim-account'), null, {
        renderer: 'svg'
      })
      this.threatenChart = this.$echarts.init(document.getElementById('threaten-account'), null, {
        renderer: 'svg'
      })
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: { show: false },
        tooltip: { show: false },
        color: ['rgba(38, 119, 255, 0.3)'],
        grid: {
          top: 0,
          left: 0,
          right: '20px',
          bottom: '-12px',
          containLabel: true
        },
        xAxis: {
          show: false,
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['上月新增', '本月新增'],
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          }
        },
        series: [
          {
            name: '2024',
            type: 'bar',
            data: [],
            label: {
              show: true,
              position: 'right'
              //     formatter: function(params) {
              //     const value = params.value;
              //     const category = params.name;
              //     return `${category}: ${value}`;
              // }
            }
          }
        ]
      }
      this.assetsChart.setOption(option, true)
      this.unclaimChart.setOption(option, true)
      this.threatenChart.setOption(option, true)

      this.assetsChart.on('click', (params) => {
        this.handleChartClick(params, '/assetsLedger')
      })
      this.unclaimChart.on('click', (params) => {
        this.handleChartClick(params, '/unclaimCloud')
      })
      this.threatenChart.on('click', (params) => {
        this.handleChartClick(params, '/threatAssets')
      })

      window.addEventListener('resize', () => {
        this.assetsChart.resize()
        this.unclaimChart.resize()
        this.threatenChart.resize()
      })
    },

    handleChartClick(params, path) {
      let obj = null
      const categoryName = params.name
      if (categoryName === '上月新增') {
        obj = this.last_month_range
      } else if (categoryName === '本月新增') {
        obj = this.now_month_range
      }
      this.$router.push({
        path,
        query: {
          monthRange: obj
        }
      })
    },

    async getData() {
      //资产概览-数量统计
      this.loading = true
      let res = await getOverviewStatistics({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        for (let i in this.myOverview) {
          this.myOverview[i] = res.data[i] ? res.data[i] : 0
        }
        this.myOverview.digital_num =
          this.myOverview.app_num + this.myOverview.wechat_num + this.myOverview.xiaochengxu_num
        this.myRateData.account_rate = res.data.sure_ip_rate || 0
        this.myRateData.threaten_rate = res.data.threaten_ip_rate || 0
        this.myRateData.unsure_rate = res.data.un_sure_ip_rate || 0
        let sure_ip_chart = res.data.sure_ip_chart.map((item) => item.num)
        let un_sure_ip_chart = res.data.un_sure_ip_chart.map((item) => item.num)
        let threaten_ip_chart = res.data.threaten_ip_chart.map((item) => item.num)
        this.now_month_range = res.data.now_month_range
        this.last_month_range = res.data.last_month_range
        this.assetsChart.setOption({
          series: [
            {
              data: sure_ip_chart
            }
          ]
        })
        this.unclaimChart.setOption({
          series: [
            {
              data: un_sure_ip_chart
            }
          ],
          color: ['rgba(255, 194, 38, 0.3)']
        })
        this.threatenChart.setOption({
          series: [
            {
              data: threaten_ip_chart
            }
          ],
          color: ['rgba(255, 70, 70, 0.3)']
        })
        this.getRate()
      }
    },
    async getRate() {
      let res = await assetsViewRateCount({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.myOverview.login_total = res.data.login.total ? res.data.login.total : 0
        this.myOverview.login_to_confirmed = res.data.login.to_confirmed
          ? res.data.login.to_confirmed
          : 0
        this.myOverview.login_confirmed = res.data.login.confirmed ? res.data.login.confirmed : 0
        this.myOverview.login_ignore = res.data.login.ignore ? res.data.login.ignore : 0
        this.myOverview.domain_total = res.data.domain.total ? res.data.domain.total : 0
        this.myOverview.domain_father = res.data.domain.father ? res.data.domain.father : 0
        this.myOverview.domain_child = res.data.domain.child ? res.data.domain.child : 0
        this.myOverview.cert_total = res.data.cert.total ? res.data.cert.total : 0
        this.myOverview.cert_valid = res.data.cert.valid ? res.data.cert.valid : 0
        this.myOverview.cert_notValid = res.data.cert.notValid ? res.data.cert.notValid : 0

        // this.myRateData = {
        //   // account_rate: res.data.ledger ? res.data.ledger : 0,
        //   // unsure_rate: res.data.doubt ? res.data.doubt : 0,
        //   // threaten_rate: res.data.threaten ? res.data.threaten : 0,
        //   digital_rate: res.data.digital ? res.data.digital : 0,
        //   domain_rate: res.data.domain.rate ? res.data.domain.rate : 0,
        //   login_rate: res.data.login.rate ? res.data.login.rate : 0,
        //   cert_rate: res.data.cert.rate ? res.data.cert.rate : 0,
        // };
        this.myRateData.digital_rate = res.data.digital ? res.data.digital : 0
        this.myRateData.domain_rate = res.data.domain.rate ? res.data.domain.rate : 0
        this.myRateData.login_rate = res.data.login.rate ? res.data.login.rate : 0
        this.myRateData.cert_rate = res.data.cert.rate ? res.data.cert.rate : 0
      }
    },

    handleDoing(id, path) {
      sessionStorage.setItem('menuId', id)
      this.changeMenuId(id)
      this.$router.push(path)
    }
  }
}
</script>
<style lang="less" scoped>
.conAssets {
  // width: 68%;
  flex: 2;
  height: 490px;
  // margin: 0 auto;
  border-radius: 4px;
  margin-right: 20px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  background-color: #fff;
  .conAssets-box {
    .conAssets-top::before {
      content: '';
      position: absolute;
      left: 0px;
      top: 18px;
      width: 4px;
      height: 16px;
      background-color: #2677ff;
      border-radius: 0 2px 2px 0;
    }
    .conAssets-top {
      position: relative;
      // cursor: pointer;
      height: 47px;
      line-height: 47px;
      // padding-top: 12px;
      padding-left: 16px;
      border-bottom: 1px solid #e9ebef;
      .title {
        // font-family: PingFangSC-Semibold;
        color: #37393c;
        font-size: 16px;
        font-weight: 600;
        vertical-align: middle;
      }
      img {
        margin-left: 8px;
        vertical-align: middle;
      }
    }
    .assets-box {
      width: calc(100% - 48px);
      margin: 0 auto;
      .assets-top {
        height: 150px;
        display: flex;
        margin-top: 24px;
        // justify-content: space-between;
        .top-left {
          flex: 2.1;
          // width: 364px;
          height: 150px;
          background-color: #f5f8fc;
          border-radius: 4px;
          min-width: 48%;
          .taizhang {
            // width: 364px;
            overflow: hidden;
          }
          .taizhang-box {
            width: calc(100% - 40px);
            margin: 0 auto;
            margin-top: 20px;
            // margin-left: 10%;
            .left-box {
              display: flex;
              justify-content: space-between;
              .taizhang-left {
                width: 36%;
                margin-right: 10px;
                .left-title {
                  // font-family: PingFangSC-Semibold;
                  color: #37393c;
                  margin-bottom: 5px;
                  cursor: pointer;
                }
                .blue-star {
                  display: inline-block;
                  width: 6px;
                  height: 6px;
                  margin-right: 5px;
                  border-radius: 1px;
                  -webkit-transform: rotate(135deg);
                  -ms-transform: rotate(135deg);
                  transform: rotate(135deg);
                  background: #2677ff;
                  vertical-align: middle;
                }
                .left-num {
                  display: flex;
                  align-items: center;
                  // font-family: PingFangSC-Semibold;
                  font-size: 24px;
                  color: #37393c;
                  margin-left: 10px;
                  .number {
                    font-weight: 600;
                  }
                }
              }
              img {
                display: inline-block;
                margin-left: 15px;
                width: 100%;
                height: 51px;
              }
              #asset-account {
                display: inline-block;
                margin-left: 15px;
                width: 60%;
                // height: 51px;
              }
            }
            .left-list {
              margin-top: 25px;
              display: flex;
              justify-content: space-between;
              margin-bottom: 5px;

              .left-li {
                width: 45%;
                height: 30px;
                // padding: 5px 0 0 12px;
                line-height: 30px;
                background: linear-gradient(180deg, #edf4ff 0%, #f8fbff 100%);
                border: 1px solid #deeaff;
                border-radius: 4px;
                display: flex;
                justify-content: space-between;
                // margin-left: 12px;
                .list-title {
                  // font-family: PingFangSC-Regular;
                  color: #62666c;
                  display: inline-block;
                  margin-left: 12px;
                }
                .list-num {
                  // font-family: PingFangSC-Regular;
                  color: #2677ff;
                  display: inline-block;
                  margin-right: 14px;
                }
              }
            }
          }
        }

        .top-right {
          flex: 1;
          min-width: 23%;
          // width: 174px;
          height: 150px;
          box-sizing: border-box;

          background-color: #f5f8fc;
          margin-left: 16px;
          border-radius: 4px;
          padding: 20px 20px 0 20px;
          .right-box {
            // width: calc(100% - 70px);
            // margin: 0 auto;
            padding: 20px 20px 0 20px;
            margin: 0 auto;
            margin-bottom: 10px;
            // .right-conbox {
            //   // width: 90%;
            //   margin: 0 auto;
            //   margin-bottom: 10px;
            // }
          }
          img {
            width: 100%;
            height: 41px;
          }
          #unclaim-account,
          #threaten-account {
            box-sizing: border-box;
            height: calc(100% - 54px);
            display: inline-block;
            // margin-left: 15px;
            padding: 8px 0 8px 8px;
            width: 100%;
          }
          .right-title {
            // // font-family: PingFangSC-Semibold;
            color: #37393c;
            margin-bottom: 5px;
            cursor: pointer;
          }
          .yel-star {
            margin-top: -3px;
            display: inline-block;
            width: 6px;
            height: 6px;
            margin-right: 5px;
            border-radius: 1px;
            -webkit-transform: rotate(135deg);
            -ms-transform: rotate(135deg);
            transform: rotate(135deg);
            background: #ffc226;
            vertical-align: middle;
          }
          .red-star {
            background: #ff4646;
          }
          .right-num {
            // font-family: PingFangSC-Semibold;
            font-size: 24px;
            color: #37393c;
            display: flex;
            align-items: center;
            margin-left: 10px;
            .number {
              font-weight: 600;
            }
            .rate-yel {
              position: relative;
              padding: 0 8px;
              display: inline-block;
              margin-left: 10px;
              padding-right: 10px;
              // width: 65%;
              height: 20px;
              line-height: 20px;
              background: rgba(255, 194, 38, 0.12);
              border-radius: 10px;
              color: #ffc226;
              font-size: 14px;
              text-align: center;
            }
          }
        }
      }
      .assets-bottom {
        height: 235px;
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        margin-bottom: 24px;
        .bottom-list:nth-child(1) {
          margin-left: 0 !important;
        }
        .bottom-list {
          flex: 1;
          background-color: #f5f8fc;
          min-width: 23%;
          margin-left: 16px;
          box-sizing: border-box;
          border-radius: 4px;
          .bottom-list-box {
            width: calc(100% - 40px);
            margin: 0 auto;
            margin-top: 20px;
            .right-title {
              color: #37393c;
              margin-bottom: 5px;
              cursor: pointer;
            }
            .right-num {
              font-size: 24px;
              color: #37393c;
              // margin-top: 20px;
              display: flex;
              align-items: center;
              margin-left: 10px;
              .number {
                font-weight: 600;
              }
            }
            .list-li:nth-last-child(1) {
              margin-bottom: 20px;
            }
            .list-li {
              height: 30px;
              line-height: 30px;
              width: 100%;
              border: 1px solid #deeaff;
              background: linear-gradient(180deg, #edf4ff 0%, #f8fbff 100%);
              border-radius: 4px;
              display: flex;
              justify-content: space-between;
              margin-top: 12px;

              .li-title {
                // font-family: PingFangSC-Regular;
                color: #62666c;
                display: inline-block;
                // margin-top: 6px;
                margin-left: 12px;
                // margin-right: 40px;
              }
              .li-num {
                color: #2677ff;
                // font-family: PingFangSC-Regular;
                display: inline-block;
                // margin-top: 6px;
                margin-right: 12px;
              }
            }
          }
        }
      }
    }
  }
  .blue-star {
    margin-top: -3px;
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 5px;
    border-radius: 1px;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg);
    background: #2677ff;
    vertical-align: middle;
  }
  .rate-blue {
    position: relative;
    padding: 0 8px;
    display: inline-block;
    margin-left: 8px;
    padding-right: 10px;
    height: 20px;
    line-height: 20px;
    background: rgba(38, 119, 255, 0.1216);
    border-radius: 10px;
    color: #2677ff;
    font-size: 14px;
    text-align: center;
    /deep/ .el-icon-minus {
      margin-left: 2%;
    }
  }
  .el-tooltip {
    position: absolute;
    top: 0;
    right: 2px;
  }
  .rate-red {
    position: relative;
    color: #ff4646 !important;
    background: rgba(255, 70, 70, 0.08) !important;
  }
  .rate-gray {
    background: #e9ebef;
    color: #a8acb3;
  }
}
</style>
