<template>
  <div class="conData">
    <div class="top-title">
      <div class="headerEcharts">
        <div class="conAssets-top">
          <span class="con-title">企业线索库</span>
          <img src="../../assets/images/arrowRight.png" alt="" />
        </div>
        <el-badge :value="redNum" class="badge" v-if="redNum != 0">
          <span class="goHandle" @click="handleDoing"
            >去处理<span class="el-icon-d-arrow-right"></span
          ></span>
        </el-badge>
        <span class="goHandle" v-else @click="handleDoing"
          >去处理<span class="el-icon-d-arrow-right"></span
        ></span>
      </div>
    </div>
    <div class="content">
      <div class="content-title">
        <div class="title-total">
          <span class="total">场景选择</span>
          <span class="total-num">({{ groupArr.length }})</span>
        </div>
        <div class="title-list" v-loading="groupLoading">
          <!-- <div class=" title-li" :class="{'li-active': active==0 }" @click="active=0">
            <span class="li-company">默认</span>
            <span class="li-num">(300)</span>
          </div> -->
          <div
            class="title-li"
            :class="{ 'li-active': active == index }"
            @click="active = index"
            v-for="(groupItem, index) in groupArr"
            :key="index"
          >
            <!-- <span class="li-company" @click="clueMore(groupItem.id)">{{ groupItem.name }}</span> -->
            <span class="li-company" @click="clueMore(groupItem.id)">{{ groupItem.name }}</span>
            <span class="li-num">({{ groupItem.collect }})</span>
          </div>
        </div>
      </div>
      <div class="content-main">
        <div class="content-box">
          <div class="main-top" v-loading="navLoading">
            <div
              class="top-li"
              v-for="(nameItem, index) of tabData.filter((item) => {
                return !item.has_subdomain_tab || this.has_subdomain_tab == item.has_subdomain_tab
              })"
              :key="index"
              @click="
                specificActive = index
                activeName = nameItem.name
                getClueChart(currentGroupId, activeName)
              "
              :class="{ 'top-active': index == specificActive }"
            >
              <p class="top-name">{{ nameItem.label }}</p>
              <p class="top-num">{{ nameItem.count }}</p>
            </div>
          </div>
          <div class="main-bottom">
            <div class="echart" v-loading="navLoading">
              <div class="echart-box" id="clue-echart"></div>
            </div>
            <div style="width: 43%">
              <div class="bottom-list" v-loading="statusLoading">
                <div class="list-li" v-for="(statuItem, n) in statusList" :key="n">
                  <p class="li-top">
                    <img class="itemImg" :src="statuItem.img" alt="" />
                    <!-- <span>  -->
                    <span class="li-tit"> {{ statuItem.title }}</span>
                    <img src="../../assets/images/arrow-data.png" alt="" />
                    <!-- </span> -->
                  </p>
                  <p class="li-bottom" :class="{ red: n == 0 }">
                    {{ groupArr[active] ? groupArr[active].tab_num[n].count : 0 }}
                  </p>
                </div>
              </div>
              <div class="bottom-more">
                <div class="more-title">
                  <span
                    class="title-detail"
                    @click="detailActive = 0"
                    :class="{ 'detail-active': detailActive == 0 }"
                    >单个扩展</span
                  >
                  <span
                    class="title-detail"
                    @click="detailActive = 1"
                    :class="{ 'detail-active': detailActive == 1 }"
                    >批量新增</span
                  >
                </div>
                <div class="more-content">
                  <div class="grid-content single" v-if="detailActive == 0">
                    <div class="singleExpand">
                      <el-input
                        :placeholder="clueTypePlaceholder"
                        v-model="ruleForm.clueContent"
                        class="input-with-select"
                      >
                        <el-select
                          v-model="ruleForm.clueType"
                          slot="prepend"
                          placeholder="请选择"
                          id="cloud_select"
                        >
                          <!-- // 此处不支持ICON和子域名扩展 -->
                          <el-option
                            v-for="item in tabData.filter((to) => {
                              return to.name != '3' && to.name != '5'
                            })"
                            :key="item.name"
                            :label="item.label"
                            :value="item.name"
                          ></el-option>
                        </el-select>
                      </el-input>
                    </div>
                    <div class="bot">
                      <!-- <span></span> -->
                      <el-button
                        class="normalBtnSmall"
                        @click.native="batchAddClueFunOne()"
                        id="cloud_ignore"
                        >扩展</el-button
                      >
                    </div>
                  </div>
                  <div class="grid-content batch" v-if="detailActive == 1">
                    <el-select
                      class="grid-select"
                      style="width: 100%"
                      v-model="clueRuleForm.way"
                      placeholder="请选择方式"
                    >
                      <el-option label="手动输入" :value="0"></el-option>
                      <el-option label="从文件导入" :value="1"></el-option>
                    </el-select>
                    <el-checkbox class="check" v-model="clueRuleForm.is_auto_expend"
                      >自动扩展</el-checkbox
                    >
                    <div class="bot">
                      <el-button class="normalBtnSmall" @click="insertClueShow" id="cloud_ignore"
                        >新增</el-button
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 新建线索 -->
    <cluedialogs
      :dialogFormVisibleInsert="dialogFormVisibleInsert"
      :has_subdomain_tab="has_subdomain_tab"
      :currentGroupId="currentGroupId"
      :activeName="activeName"
      :ruleForm="clueRuleForm"
      @insertClueSave="insertClueSave"
    />
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { clueChart } from '@/api/apiConfig/api.js'
import { insertCluesV1, cluesGroupList, tabNumClues } from '@/api/apiConfig/clue.js'
import { getRunningClueJob } from '@/api/apiConfig/recommend.js'

export default {
  components: {
    cluedialogs: () => import('@/views/cloudRecommend/clueInsertDialog.vue')
  },
  data() {
    return {
      has_subdomain_tab: 0,
      batchOneLoading: false,
      currentGroupId: '',
      active: 0,
      specificActive: 0,
      specificName: ['域名', '子域名', '证书', 'ICP', 'ICON', '关键字', 'IP段'],
      statusList: [
        {
          img: require('../../assets/images/tobeConfirmed.png'),
          title: '待确认',
          num: 0
        },
        {
          img: require('../../assets/images/confirmed.png'),
          title: '已确认',
          num: 0
        },
        {
          img: require('../../assets/images/ignored.png'),
          title: '已忽略',
          num: 0
        }
      ],
      detailActive: 0,
      ruleForm: {
        clueType: '0',
        clueContent: ''
      },
      tabData: [
        {
          name: '0',
          label: '域名',
          count: '',
          placeholder: '请输入域名,例：fofa.info'
        },
        {
          name: '1',
          label: '证书',
          count: '',
          placeholder: '请输入证书，例：O="北京华顺信安科技有限公司"或者CN="fofa.info"'
        },
        {
          name: '2',
          label: 'ICP',
          count: '',
          placeholder: '请输入ICP，例：京ICP备18024709号 或者 京ICP备18024709号-3'
        },
        {
          name: '3',
          label: 'ICON',
          count: '',
          placeholder: '请输入icon'
        },
        {
          name: '4',
          label: '关键词',
          count: '',
          placeholder: '请输入关键词，例：北京华顺信安科技有限公司'
        },
        {
          name: '5',
          label: '子域名',
          count: '',
          has_subdomain_tab: 1,
          placeholder: '请输入子域名'
        },
        {
          name: '6',
          label: 'IP段',
          count: '',
          placeholder: '请输入IP段'
        }
      ],
      lineProgress: 0,
      groupCurrentPage: 1,
      groupLoading: false,
      groupArr: [],
      currentGroupId: '',
      formInline: {
        no_page: '', // 1 没有分页，不传值 有分页
        keyword: '',
        page: 1,
        per_page: 10,
        status: '', // 已确认线索
        group_id: '' // 安服角色分组id
      },
      keywordObj: {
        domain_keyword: '',
        cert_keyword: '',
        icp_keyword: '',
        key_keyword: '',
        ip_keyword: '',
        subdomain_keyword: ''
      },
      isNew: false, //是否为新增线索
      currentGroupIds: '',
      activeNameStatus: 0,
      tabDataTmp: [],
      cluesCount: [],
      navLoading: false,
      statusLoading: false,
      redNum: 0,
      activeName: 0,
      defaultList: [],
      sureList: [],
      ignoreList: [],
      allList: [],
      myCharts: null,
      clueRuleForm: {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      },
      dialogFormVisibleInsert: false,
      fileList: [],
      user: {},
      userInfo: {},
      group_id: ''
    }
  },
  created() {
    // this.activeName = sessionStorage.getItem('activeTabName')
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (this.user.role == 1) {
      // 超管账号有权限
      this.has_subdomain_tab = 1
    } else if (this.user.role == 2) {
      // 安服
      if (this.currentCompany) {
        if (this.currentCompany == -1) {
          // 安服账号本身,不用配置就有权限
          this.has_subdomain_tab = 1
        } else {
          // 安服操作的企业,要你根据用户管理的配置判断
          this.has_subdomain_tab = sessionStorage.getItem('companyInfo')
            ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.has_subdomain_tab
            : ''
        }
      }
    } else {
      // 企业账号
      this.has_subdomain_tab = this.user ? this.user.has_subdomain_tab : ''
    }
    if (this.user.role == 2 && !this.currentCompany) return
    if (this.$route.query.group_id) {
      // 获取场景分组
      this.currentGroupId = String(this.$route.query.group_id)
      this.currentGroupIds = String(this.$route.query.group_id)
      this.isNew = true
      this.getTaskResultData()
    } else {
      this.isNew = false
      this.getTaskResultData()
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany']),
    clueTypePlaceholder() {
      return this.tabData[this.ruleForm.clueType].placeholder
    }
  },
  watch: {
    getterCurrentCompany(msg) {
      if (this.user.role == 2) {
        this.getTaskResultData()
      }
      setTimeout(() => {
        if (this.currentCompany == -1) {
          // 安服账号本身,不用配置就有权限
          this.has_subdomain_tab = 1
        } else {
          // 安服操作的企业,要你根据用户管理的配置判断
          this.has_subdomain_tab = sessionStorage.getItem('companyInfo')
            ? JSON.parse(sessionStorage.getItem('companyInfo')).owner.has_subdomain_tab
            : ''
        }
      }, 100)
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    // 线索保存
    insertClueSave() {
      this.dialogFormVisibleInsert = false
      this.currentGroupIds = this.currentGroupId
      this.clueRuleForm = {
        content: '',
        way: 0,
        comment: '',
        is_auto_expend: 0,
        clue_company_name: '',
        file: ''
      }
      this.getTaskResultData()
      // this.getTaskResultData()
      // this.getCompanyClueBaseFun()
    },
    insertClueShow() {
      this.clueRuleForm.content = ''
      this.clueRuleForm.comment = ''
      this.clueRuleForm.clue_company_name = ''
      this.dialogFormVisibleInsert = true
      this.clueRuleForm.content = this.clueRuleForm.way == 1 ? [] : ''
      this.fileList = []
    },
    async batchAddClueFunOne() {
      if (!this.ruleForm.clueContent) {
        this.$message.error('请输入线索内容')
        return
      }
      let clueData = [
        {
          type: this.ruleForm.clueType,
          content: [this.ruleForm.clueContent]
        }
      ]
      let obj = {
        group_id: this.currentGroupId,
        data: clueData,
        is_auto_expend: '1', // 单独扩展
        file: '',
        operate_company_id: this.currentCompany
      }
      this.batchOneLoading = true
      let res = await insertCluesV1(obj).catch(() => {
        this.batchOneLoading = false
      })
      if (res.code == 0) {
        this.batchOneLoading = false
        this.$message.success('操作成功！')
        this.ruleForm.clueContent = ''
        // this.getRunningJob()
        // this.getTaskResultData()
      }
    },
    // 获取当前正在执行任务
    async getRunningJob() {
      this.lineProgress = 0
      let res = await getRunningClueJob({
        group_id: this.currentGroupId,
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.toDoList = res.data
        if (!res.data) return
        this.currentId = res.data.task_id
        // 推送数据渲染到列表
        this.lineProgress = Number(res.data.progress)
        this.lineStart_at = res.data.start_time
        this.lineUseSeconds = res.data.use_seconds
        this.lineClueNum = res.data.total
      }
    },
    async getClueData(tmp, flag) {
      // 忽略:2,确认:1,待确认：0

      // 搜索page为1
      if (tmp == 'tmp') {
        this.currentPage = 1
        this.formInline.page = 1
        if (this['checkedArr' + this.activeName].length > 0) {
          this['checkedArr' + this.activeName] = []
        }
        // 清空当前页面已选中全部数据 而不是取消表头全选
        this.tableData.forEach((row) => {
          this.$refs['eltable' + this.activeName].toggleRowSelection(row, false)
        })
        this.checkedAllObj[this.checkedAllArr[this.activeName]] = false
      } else {
        this.formInline.page = this.currentPage
      }
      this.formInline.per_page = this.pageSize
      this.formInline.operate_company_id = this.currentCompany
      this.formInline.group_id = this.currentGroupId // 分组id
      this.formInline.no_page = ''
      this.formInline.status = this.activeNameStatus
      this.formInline.keyword = this.keywordObj[this.tabKeywordArr[this.activeName]]
      let obj = {
        type: this.activeName,
        query: this.formInline,
        operate_company_id: this.currentCompany
      }
      // this.loading = true
      // let res = await cluesList(obj).catch(() => { this.loading = false })
      // this.loading = false
      // let arr = res.data && res.data.items ? res.data.items : []
      // let arrayNew = []
      // arr.map(item => {
      // arrayNew.push(Object.assign({}, item, { type: this.activeName }))
      // })
      // this.tableData = arrayNew
      // this.total = res.data && res.data.total ? res.data.total : 0
      // 全选操作
      // this.$nextTick(() => {
      // if (this.checkedAllObj[this.checkedAllArr[this.activeName]]) {
      // this.tableData.forEach(row => {
      // this.$refs['eltable' + this.activeName].toggleRowSelection(row, true);
      // });//////////
      // }
      // })
    },
    // 获取分组列表数据
    async getTaskResultData(val) {
      this.statusLoading = true
      let obj = {
        page: this.groupCurrentPage,
        per_page: 10,
        no_page: '1',
        operate_company_id: this.currentCompany
      }
      this.groupLoading = true
      let res = await cluesGroupList(obj)
        .catch(() => {
          this.groupLoading = false
          this.statusLoading = false
        })
        .catch(() => {
          this.groupArr = []
        })
      this.groupLoading = false
      this.statusLoading = false
      let arr = res.data
      arr.forEach((item) => {
        item.isChecked = false
        item['num'] = item.collect ? item.collect : 0 // 所有线索数量
      })
      this.groupArr = arr
      // if (!this.currentGroupId) { // 当前分组id不存在
      this.currentGroupId = this.groupArr.length > 0 ? String(this.groupArr[0].id) : ''
      this.currentGroupIds = this.groupArr.length > 0 ? String(this.groupArr[0].id) : ''
      // }
      if (this.currentGroupId) {
        this.total = res.data.total
        // 分组下面的待确认，已确认，忽略菜单数量
        if (!val) {
          this.getRunningJob()
          this.getClueData()
          // this.getTabNum()
          this.getTabNum('tmp')
          this.getClueChart(this.currentGroupId, this.activeName)
        }
        if (this.groupArr.length != 0) {
          this.groupArr.forEach((item, index) => {
            item.isHighlighted = false
            if (item.id == this.currentGroupIds) {
              this.groupArr[index].isHighlighted = true
            }
          })
        }
        this.redNum = this.groupArr[this.active].tab_num[0].count
      }
    },
    // 获得分组后的柱状图数据
    async getClueChart(id, typeName) {
      let obj = {
        operate_company_id: this.currentCompany,
        type: typeName,
        group_id: id
      }
      let res = await clueChart(obj).catch(() => {})
      if (res.code == 0) {
        this.sureList.length = 0
        this.defaultList.length = 0
        this.ignoreList.length = 0
        this.allList.length = 0
        for (var k in res.data) {
          let s = res.data[k].sure_num
          this.sureList.push(s)
          let d = res.data[k].deafult_num
          this.defaultList.push(d)
          let i = res.data[k].ingore_num
          this.ignoreList.push(i)
          let a = res.data[k].all_num
          this.allList.push(a)
        }
        this.chartsInit()
      }
    },
    async getTabNum(tmp) {
      let obj = {}
      if (tmp) {
        obj = {
          group_id: this.currentGroupId,
          data: {
            status: '',
            operate_company_id: this.currentCompany
          }
        }
      } else {
        obj = {
          group_id: this.currentGroupId,
          data: {
            status: this.activeNameStatus,
            operate_company_id: this.currentCompany
          }
        }
      }

      let res = await tabNumClues(obj)
      this.tabNumStatus = 0 // 获取总数，用于删除、导出等操作无数据提示
      this.extendNum = res.data.expand_finish
      if (res.data.clues_count) {
        if (tmp) {
          this.tabData.forEach((item) => {
            res.data.clues_count.forEach((ch) => {
              if (item['name'] == ch['type']) {
                this.tabNumStatus += ch.count
                item.count = ch.count
              }
            })
          })
        } else {
          this.tabData.forEach((item) => {
            res.data.clues_count.forEach((ch) => {
              if (item['name'] == ch['type']) {
                this.tabNumStatus += ch.count
                item.count = ch.count
              }
            })
          })
        }
      }
    },
    // 点击获取域名等数据
    async clueMore(id) {
      this.group_id = id
      this.navLoading = true
      this.specificActive = 0
      let obj = {}
      obj = {
        group_id: id,
        data: {
          status: '',
          operate_company_id: this.currentCompany
        }
      }
      this.currentGroupId = id
      let groupId = id
      let res = await tabNumClues(obj)
      if (res.data.clues_count) {
        this.navLoading = false
        this.tabData.forEach((item) => {
          res.data.clues_count.forEach((ch) => {
            if (item['name'] == ch['type']) {
              item.count = ch.count
            }
          })
        })
        this.redNum = this.groupArr[this.active].tab_num[0].count
        this.getClueChart(groupId, 0)
        this.chartsInit()
      }
    },
    // 绘制柱状图
    chartsInit() {
      this.myCharts = this.$echarts.init(document.getElementById('clue-echart'))
      let options = {
        tooltip: {
          trigger: 'item',
          // formatter:'{a}{b}:{c}'
          formatter: function (params) {
            var str = `<div style = "background:url( ${require('../../assets/images/clueBG.png')}) no-repeat center center ;font-family: PingFang SC;font-size: 14px;line-height: 20px;color: #62666C; ">${
              params.seriesName + params.name
            }: <span style="color:#2677FF;font-size: 14px;font-weight: 600;line-height: 20px;">${params.value}</span></div>`
            // parms.marker
            return str
          },
          position: 'top',
          borderColor: '#F5F8FC'
          // axisPointer:{
          //   snap:true
          // }
        },
        grid: {
          bottom: '30%'
        },
        title: {
          text: this.tabData[this.specificActive].label + '来源统计',
          textStyle: {
            fontSize: 14,
            // fontFamily: "PingFang SC",
            fontWeight: 500
          }
        },
        legend: {
          data: ['全部', '待确认', '已确认', '已忽略'],
          right: 10,
          // orient: 'vertical',
          top: 0
          // backgroundColor: '#ccc'
        },
        xAxis: {
          // type:'value',
          interval: 70,
          axisTick: {
            show: false
          },
          data: ['初始线索', '扩展线索', '循环线索'],
          axisLine: {
            lineStyle: {
              color: '#E9EBEF'
            }
          },
          axisLabel: {
            color: '#62666C',
            // fontFamily: 'PingFang SC',
            fontSize: 12,
            lineHeight: 20,
            margin: 5
          }
        },
        yAxis: {
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
            //   show:true
          }
        },
        series: [
          {
            barWidth: 10,
            barGap: '100%',
            type: 'bar',
            data: this.allList,
            name: '全部',
            color: {
              type: 'linear',
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: '#4EAFFF'
                },
                {
                  offset: 1,
                  color: '#2677FF'
                }
              ]
            }
          },
          {
            barWidth: 10,
            barGap: '100%',
            type: 'bar',
            data: this.defaultList,
            name: '待确认',
            color: {
              type: 'linear',
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: '#FFAE66'
                },
                {
                  offset: 1,
                  color: '#FF7900'
                }
              ]
            }
            // backgroundStyle: {
            //   color: 'linear-gradient(0deg, #4EAFFF 0%, #2677FF 100%)'
            // }
          },
          {
            barWidth: 10,
            barGap: '100%',
            type: 'bar',
            data: this.sureList,
            name: '已确认',
            color: {
              type: 'linear',
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: '#50FFC6'
                },
                {
                  offset: 1,
                  color: '#10D595'
                }
              ]
            }
            // backgroundStyle: {
            //   color: 'linear-gradient(0deg, #4EAFFF 0%, #2677FF 100%)'
            // }
          },
          {
            barWidth: 10,
            barGap: '100%',
            type: 'bar',
            data: this.ignoreList,
            name: '已忽略',
            color: {
              type: 'linear',
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: '#E9EBEF'
                },
                {
                  offset: 1,
                  color: '#D1D5DD'
                }
              ]
            }
            // backgroundStyle: {
            //   color: 'linear-gradient(0deg, #4EAFFF 0%, #2677FF 100%)'
            // }
          }
        ]
      }
      this.myCharts.setOption(options)
    },
    handleDoing() {
      sessionStorage.setItem('menuId', '1-6-2')
      this.changeMenuId('1-6-2')
      this.$router.push({
        path: '/companyBank',
        query: {
          group_id: this.group_id
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.conData {
  width: calc(100% - 40px);
  margin: 0 auto;
  height: 433px;
  background: #fff;
  margin-top: 20px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  overflow: hidden;
  .top-title {
    width: 100%;
    height: 47px;
    // border-bottom: 1px solid #e9ebef;
    //  padding-bottom: 10px;
    .headerEcharts {
      padding: 0 0 0 0 !important;
      position: relative;
      width: 100%;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #e9ebef;
      .conAssets-top::before {
        content: '';
        position: absolute;
        left: 0px;
        top: 19px;
        width: 4px;
        height: 16px;
        background-color: #2677ff;
        border-radius: 0 2px 2px 0;
      }
      .conAssets-top {
        position: relative;
        // cursor: pointer;
        height: 47px;
        // width: 100%;
        // padding-top: 12px;
        line-height: 47px;
        padding-left: 16px;

        .con-title {
          // font-family: PingFangSC-Semibold;
          color: #37393c;
          font-size: 16px;
          font-weight: 600;
          vertical-align: middle;
        }
        img {
          margin-left: 8px;
          vertical-align: middle;
        }
      }
    }
    // .item {
    //   margin-top: 3px;
    //   margin-right: 40px;
    //   position: absolute !important;
    //   left: 93%;
    //   top: 16px;
    //   width: 25%;
    // }
    .badge /deep/ .el-badge__content,
    .is-fixed {
      // right: 90%;
      height: 12px;
      line-height: 12px;
      font-size: 10px;
      left: 3px !important;
      top: 10px !important;
      right: unset !important;
      background: #ff4646 !important;
    }
    .goHandle {
      // // font-family: PingFang SC;
      color: #62666c;
      cursor: pointer;
      font-size: 14px;
      margin-right: 16px;
      line-height: 47px;

      & > span {
        margin-left: 5px;
      }
    }
  }
  .content {
    height: 383px;
    width: 100%;
    overflow: hidden;
    display: flex;

    .content-title {
      width: 21%;
      height: 384px;
      border: 1px solid #e9ebef;
      background: linear-gradient(180deg, #ffffff 0%, #eef5ff 100%);
      border-top: 0;
      .title-total {
        height: 48px;
        .total {
          display: inline-block;
          margin-left: 20px;
          margin-top: 16px;
          color: #37393c;
          // font-family: PingFang SC;
        }
        .total-num {
          margin-top: 16px;
          display: inline-block;
          color: #62666c;
          margin-left: 4px;
          // font-family: PingFang SC;
        }
      }
      .title-list {
        height: 335px;
        overflow-y: auto;
        .title-li {
          width: 100%;
          height: 44px;
          line-height: 44px;
          display: flex;
          cursor: pointer;
          .li-company {
            display: inline-block;
            margin-left: 20px;
            color: #62666c;
            // font-family: PingFang SC;
            width: 75%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .li-num {
            text-align: right;
            color: #9ba4b2;
            // font-family: PingFang SC;
            display: inline-block;
            margin-right: 16px;
            width: 13%;
          }
        }
        .li-active {
          background: rgba(234, 239, 246, 0.7);
          position: relative;
          .li-company {
            color: #2677ff;
            font-weight: 600;
          }
          .li-num {
            color: #62666c;
          }
        }
        .li-active::before {
          content: '';
          position: absolute;
          left: 0px;
          top: 0;
          width: 4px;
          height: 44px;
          background-color: #2677ff;
        }
      }
    }
    .content-main {
      width: 79%;
      .content-box {
        width: calc(100% - 56px);
        margin: 0 auto;
        height: 386px;
        .main-top {
          width: 100%;
          height: 86px;
          background: #f5f8fc;
          margin-top: 24px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          .top-li {
            box-sizing: border-box;
            width: 15%;
            height: 86px;
            overflow: hidden;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            // margin-right: 1%;
            .top-name {
              // font-family: PingFang SC;
              color: #62666c;
              text-align: center;
              // margin-top: 18px;
            }
            .top-num {
              // font-family: PingFang SC;
              color: #37393c;
              font-size: 20px;
              text-align: center;
              font-weight: 600;
              margin-top: 8px;
            }
          }
          .top-active {
            background: linear-gradient(180deg, #f4f8ff 0%, #e1ecff 100%);
            border: 1px solid #2677ff;
            box-shadow: 2px 0px 4px 0px rgba(0, 0, 0, 0.12);
            border-radius: 4px;

            .top-num {
              color: #2677ff;
            }
            .top-name {
              color: #37393c;
            }
          }
        }
        .main-bottom {
          margin-top: 15px;
          width: 100%;
          display: flex;
          .echart {
            width: 57%;
            height: 300px;
            .echart-box {
              height: 100%;
              width: 100%;
            }
          }
          .bottom-list {
            width: 100%;
            height: 86px;
            background: #f5f8fc;
            display: flex;
            margin-top: 16px;
            overflow: hidden;
            border-radius: 4px;
            justify-content: space-between;
            .list-li:nth-child(1) {
              margin-left: 25px;
            }
            .list-li {
              width: 40%;
              margin-top: 18px;
              margin-right: 17px;
              .li-top {
                display: flex;
                align-items: center;

                // width: 30%;
                .itemImg {
                  // vertical-align: middle;
                  width: 20px;
                  height: 20px;
                }
                span {
                  // vertical-align: middle;
                  color: #62666c;
                  // font-family: PingFang SC;
                  font-size: 14px;
                  display: inline-block;
                  margin-left: 6px;
                  line-height: 20px;
                  // margin-bottom: 8px;
                }
                .li-tit {
                  margin-right: 4px;
                }
              }
              .li-bottom {
                // font-family: PingFang SC;
                color: #37393c;
                font-size: 20px;
                // width: 50%;
                // text-align: center;
                margin-left: 33px;
                margin-top: 8px;
                font-weight: 600;
              }
              .red {
                color: #ff4646;
              }
            }
          }
          .bottom-more {
            width: 100%;
            height: 130px;
            background: #f5f8fc;
            border-radius: 4px;
            margin-top: 16px;
            .more-title {
              height: 44px;
              // line-height: 20px;
              width: 100%;
              border-bottom: 1px solid #e3e5ea;
              .title-detail {
                height: 44px;
                line-height: 44px;
                color: #62666c;
                // font-family: PingFang SC;
                display: inline-block;
                margin-left: 20px;
                cursor: pointer;
              }
              .detail-active {
                color: #2677ff;
                border-bottom: 2px solid #2677ff;
                font-weight: 500;
              }
            }
            .more-content {
              padding: 0 16px;
              div {
                // .singleExpand {
                //   display: flex;
                //   .el-input-group__prepend {
                //     vertical-align: baseline !important;
                //     border: 0;
                //     padding: 0 !important;
                //   }
                //   .el-select {
                //     margin: 0 !important;
                //   }
                // }
                &.single {
                  border-radius: 0px 4px 4px 0px;
                  .bot {
                    border-radius: 0px 4px 4px 0px;
                  }
                }
                .bot {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  border: 1px solid #2677ff;
                  background-color: #2677ff;

                  // margin-top: 16px;
                  .normalBtnSmall {
                    width: 50px;
                    height: 36px;
                    line-height: 24px;
                    // padding: 0px 10px;
                    text-align: center;
                    color: #fff;
                    font-size: 12px;
                    background: #2677ff;
                    border: 0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.grid-content {
  position: relative;
  // height: 140px;
  padding-top: 24px;
  z-index: 99;
  opacity: 1;
  border-radius: 4px;
  box-sizing: border-box;
  // background: linear-gradient(180deg, rgba(212, 228, 255, 0.08) 0%, rgba(38, 119, 255, 0.06) 100%), rgba(245, 248, 252, 1);
  overflow: hidden;
  display: flex;
  align-items: center;
  /deep/.el-input-group__prepend {
    width: 89px;
    background-color: #fff;
    border-right: 1px solid;
  }
  &.batch {
    .grid-select {
      box-sizing: border-box;
      height: 36px;
      border: 1px solid #2677ff;
      border-radius: 4px;
      margin-right: 12px;
      /deep/.el-input__inner {
        height: 34px;
      }
    }
    .check {
      margin-right: 12px;
      color: #62666c;
    }
    .bot {
      border-radius: 4px;
    }
  }

  /deep/.el-input__inner {
    // height: 36px;
    // border: 1px solid #2677ff;
    // border-radius: 4px;
    // height: 100%;
    border: 0;
  }
  .el-progress-bar {
    padding-right: 0;
    .el-progress-bar__outer {
      background: rgba(209, 213, 221, 0.5);
    }
  }
  .el-progress__text {
    color: #2677ff;
    font-size: 14px !important;
  }
  .imgBot {
    position: absolute;
    bottom: -19px;
    z-index: -1;
    left: 9px;
  }
  .loadingList {
    margin-top: 12px;
  }
  .title {
    display: flex;
    justify-content: space-between;
    color: rgba(55, 57, 60, 1);
    font-size: 14px;
    margin-bottom: 12px;
    span {
      color: rgba(55, 57, 60, 1);
      font-size: 14px !important;
    }
  }
  .el-input__inner {
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(209, 213, 221, 1);
  }
  .expandOver {
    // width: 100%;
    display: flex;
    justify-content: space-between;
    height: 36px;
    line-height: 36px;
    opacity: 1;
    padding: 0 12px;
    color: #2677ff;
    border-radius: 4px;
    background: linear-gradient(90deg, rgba(38, 119, 255, 0.12) 0%, rgba(38, 119, 255, 0.06) 100%);
    span {
      i {
        margin-right: 4px;
      }
    }
  }
  .singleExpand {
    width: 85%;
    display: flex;
    background-color: #fff;
    border: 1px solid #2677ff;
    border-radius: 4px 0px 0px 4px;
    /deep/.el-input-group__prepend {
      width: 78px;
      vertical-align: baseline !important;
      border: 0;
      padding: 0 !important;
      border-right: 0;
    }
    /deep/.el-select {
      height: 36px;
      margin: 0 !important;
    }
  }
  .bot {
    width: 15%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-top: 16px;
    .normalBtnSmall {
      text-align: center;
      width: 100% !important;
      height: 26px;
      line-height: 24px;
      padding: 0 10px;
      color: #fff;
      font-size: 12px;
      background: #2677ff;
    }
  }
}
/deep/.el-input-group {
  line-height: 36px;
  & > .el-input__inner {
    height: 16px;
    line-height: 36px;
    display: inline-block;
    border-left: 1px solid #d8d8d8;
  }
}
// /deep/.el-input-group__prepend>.el-input__inner{
//   display: inline-block;
// }
</style>
