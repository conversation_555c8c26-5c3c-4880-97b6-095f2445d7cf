<template>
  <div class="conWrap">
    <div class="conAssets-top">
      <span class="title">常用功能</span>
      <img src="../../assets/images/arrowRight.png" alt="" />
    </div>
    <div class="myBoxRightFooter">
      <div class="RightFooterLeft" v-loading="taskIsLoading" @click="go('/unitIndex', '2-1')">
        <div class="RightFooterLeftTop">
          <div>
            <img src="../../assets/images/functionImg.png" class="Top-img" alt="" />
            <span class="Top-title">单位资产测绘</span>
          </div>
          <!-- <div class="RightFooterLeftTopTu">
            <img src="../../assets/images/cehui.png" alt="">
          </div> -->
        </div>
        <div class="RightFooterLeftFt wuFt" v-if="istaskInfoData">
          <div>当前暂无测绘任务</div>
          <el-button class="normalBtn" type="primary" @click="go('/unitIndex', '2-1')"
            >去测绘>></el-button
          >
        </div>
        <div class="RightFooterLeftFt" v-else>
          <div class="RightFooterLeftL">当前任务：</div>
          <div class="company">{{
            taskInfoData && taskInfoData.name ? taskInfoData.name : ''
          }}</div>
          <div v-if="isProgress">
            <el-progress
              :stroke-width="6"
              :percentage="currentPercent"
              style="margin-bottom: 8px"
            ></el-progress>
          </div>
          <!-- <div class="text">{{text}}</div> -->
        </div>
      </div>
      <div class="RightFooterRg">
        <div
          v-for="(item, index) in myList"
          :key="index"
          :class="item.flag ? 'FooterRgCard FooterRgCardSpecail' : 'FooterRgCard'"
          @click="go(`${item.path}`, item.id)"
        >
          <div>
            <img :src="item.img" alt="" class="FooterRgCardimg" />
            <span>{{ item.label }}</span>
            <span v-if="item.id == '1-6-2'" class="redBox">{{ companyClueNum }}</span>
          </div>
        </div>
        <div class="FooterRgCard" v-if="isNew" @click="goNew">
          <div>
            <img src="../../assets/images/assetsAnalyze.png" alt="" class="FooterRgCardimg" />
            <span>新增线索</span>
            <span class="redBox">{{ clouesNum }}</span>
          </div>
        </div>
        <div class="FooterRgCard" @click="goScreenAssets">
          <div>
            <img
              src="../../assets/images/anazlyBig.png"
              style="width: 25px"
              class="FooterRgCardimg"
            />
            <span>暴露面分析大屏</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import { getNewCloues } from '@/api/apiConfig/api.js'
import { detectTaskInfo } from '@/api/apiConfig/surveying.js'

export default {
  data() {
    return {
      taskIsLoading: false,
      myList: [
        {
          id: '2-1',
          label: '云端资产推荐',
          path: '/assetsCloud',
          img: require('../../assets/images/assetsCloud.png')
        },

        {
          id: '2-1',
          label: '资产扫描任务',
          path: '/assetsScan',
          img: require('../../assets/images/assetsScan.png')
        },
        {
          id: '1-3-1',
          label: '导入台账',
          path: '/assetsLedger',
          img: require('../../assets/images/assetsLedger.png'),
          flag: true
        },
        {
          id: '1-6-2',
          label: '企业线索库',
          path: '/companyBank',
          img: require('../../assets/images/clueDatabase.png'),
          flag: true
        }
        //   {
        //     id:'',
        //       label: '新增线索',
        //       path: '/assetsCloud',
        //       img: require('../../assets/images/assetsCloud.png')
        //   },
      ],
      taskInfoData: '',
      user: {
        role: ''
      },

      currentPercent: 0,
      isProgress: false,
      istaskInfoData: true,
      text: '输入企业名称',
      isNew: false, //是否有新增线索
      group_id: 0,
      clouesNum: 0,
      companyClueNum: 0
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getcehui()
        this.getCloues()
      }
    },
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    }
  },
  computed: {
    ...mapState(['currentCompany', 'recommentFlag']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getcehui()
      this.getCloues()
    } else {
      this.getcehui()
      this.getCloues()
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    imgStyle(val) {
      //改变img大小
      if (val == '/assetsCloud') {
        return 'FooterRgCardimg'
      } else if (val == '/assetsCloud') {
        return 'FooterRgCardimg1'
      } else {
        return 'FooterRgCardimg2'
      }
    },
    async getcehui() {
      //获取测绘任务
      this.taskIsLoading = true
      this.taskInfoData = ''
      let obj = {
        taskId: '',
        data: {
          operate_company_id: this.currentCompany
        }
      }
      let res = await detectTaskInfo(obj).catch(() => {
        this.taskIsLoading = false
      })
      if (res.code == 0) {
        this.taskIsLoading = false
        this.taskInfoData = res.data // 任务详情
        if (this.taskInfoData) {
          this.istaskInfoData = false
          if (this.taskInfoData.step == 1 || this.taskInfoData.step == 2) {
            this.currentPercent = parseFloat(this.taskInfoData.clue_progress)
          } else {
            if (this.taskInfoData.step == 3) {
              this.currentPercent = parseFloat(this.taskInfoData.expend_progress)
            } else {
              if (this.taskInfoData.step_detail == 400) {
                this.currentPercent = parseFloat(this.taskInfoData.update_assets_level_progress)
              } else {
                this.currentPercent = parseFloat(this.taskInfoData.progress)
              }
            }
          }
          if (this.taskInfoData.step_status == 0) {
            this.isProgress = true
          } else {
            this.isProgress = false
          }
          if (this.taskInfoData.step == 1) {
            this.text = '输入企业名称'
          } else if (this.taskInfoData.step == 2) {
            this.text = '资产线索获取'
          } else if (this.taskInfoData.step == 3) {
            this.text = '云端资产推荐'
          } else {
            this.text = '资产可信度评估'
          }
        } else {
          this.istaskInfoData = true
        }
      }
    },
    handleMessage(res, o) {
      //处理接收到的信息
      if (
        res.cmd == 'detect_assets_tip1_process' ||
        res.cmd == 'detect_assets_tip2_domain' ||
        res.cmd == 'detect_assets_tip2_icp' ||
        res.cmd == 'detect_assets_tip2_cert' ||
        res.cmd == 'detect_assets_tip2_all' ||
        (res.cmd == 'detect_assets_tip2_ip' && res.data && res.data.task_id == this.taskInfoData.id)
      ) {
        this.currentPercent = parseFloat(res.data.progress)
        if (parseInt(res.data.progress) == '100') {
          this.isProgress = false
        } else {
          this.isProgress = true
        }
        this.istaskInfoData = false
        this.text = '资产线索获取'
      }
      if (res.cmd == 'recommend_progress' && res.data && this.recommentFlag == res.data.flag) {
        this.currentPercent = parseFloat(res.data.progress)
        if (res.data.status == 2) {
          this.isProgress = false
        } else {
          this.isProgress = true
        }
        this.istaskInfoData = false
        this.text = '云端资产推荐'
      }
      if (
        res.cmd == 'detect_assets_tasks' &&
        res.data &&
        res.data.detect_assets_tasks_id == this.taskInfoData.id
      ) {
        this.currentPercent = parseFloat(res.data.progress)
        if (res.data.status == 2) {
          this.isProgress = false
        } else {
          this.isProgress = true
        }
        this.istaskInfoData = false
        this.text = '资产可信度评估'
      }
      if (res.cmd == 'update_assets_level' && res.data && res.data.flag == this.recommentFlag) {
        this.currentPercent = parseFloat(res.data.progress)
        if (res.data.status == 2) {
          this.isProgress = false
        } else {
          this.isProgress = true
        }
        this.istaskInfoData = false
        this.text = '资产可信度评估'
      }
      if (res.cmd == 'extract_asset_clues_job') {
        if (res.data.status == 2) {
          this.getCloues()
        }
      }
    },
    goScreenAssets() {
      window.open('/screenAssets', '_blank')
    },
    go(val, id) {
      //跳转页面
      sessionStorage.setItem('menuId', id)
      this.changeMenuId(id)
      this.$router.push(`${val}`)
    },

    async getCloues() {
      //获取新增线索
      this.isNew = false
      this.clouesNum = 0
      this.companyClueNum = 0
      this.group_id = 0
      let res = await getNewCloues({ operate_company_id: this.currentCompany })
      if (res.code == 0) {
        if (res.data) {
          if (res.data.num != 0) {
            this.isNew = true
            this.clouesNum = res.data.num
            this.companyClueNum = res.data.company_clue_num
            this.group_id = res.data.group_id
          } else {
            this.isNew = false
          }
        }
      }
    },
    goNew() {
      sessionStorage.setItem('menuId', '1-6-2')
      this.changeMenuId('1-6-2')
      this.$router.push({ path: '/companyBank', query: { group_id: this.group_id } })
    }
  }
}
</script>
<style lang="less" scoped>
.conWrap {
  width: calc(100% - 40px);
  margin: 0 auto;
  height: 200px;
  // margin-bottom: 12px;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  margin-top: 20px;
}
.conAssets-top::before {
  content: '';
  position: absolute;
  left: 0px;
  top: 18px;
  width: 4px;
  height: 16px;
  background-color: #2677ff;
  border-radius: 0 2px 2px 0;
}
.conAssets-top {
  position: relative;
  // cursor: pointer;
  height: 47px;
  line-height: 47px;
  // padding-top: 12px;
  padding-left: 16px;
  border-bottom: 1px solid #e9ebef;
  .title {
    // font-family: PingFangSC-Semibold;
    color: #37393c;
    font-size: 16px;
    font-weight: 600;
    vertical-align: middle;
    background-color: unset;
  }
  img {
    margin-left: 8px;
    vertical-align: middle;
  }
}

.myBoxRightFooter {
  display: flex;
  width: 100%;
  height: 160px;
  align-items: center;
  justify-content: center;
}
.RightFooterLeft {
  width: 35%;
  height: 112px;
  background: -webkit-linear-gradient(135deg, #062c6b 0%, #041e4b 100%);
  background: -o-linear-gradient(135deg, #062c6b 0%, #041e4b 100%);
  background: -moz-linear-gradient(135deg, #062c6b 0%, #041e4b 100%);
  background: linear-gradient(135deg, #062c6b 0%, #041e4b 100%);
  border-radius: 4px;
  margin-right: 16px;
  margin-left: 24px;
  padding: 0 16px;
  box-sizing: border-box;
  background-image: url('../../assets/images/cehuibg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  cursor: pointer;
  .RightFooterLeftFt {
    color: rgba(255, 255, 255, 0.7);
    margin-top: 13px;
    margin-bottom: 20px;
    .RightFooterLeftL {
      display: inline-block;
    }
    .normalBtn {
      margin-top: 12px;
      margin-left: 0px !important;
      background: -webkit-linear-gradient(90deg, #4eafff 0%, #2677ff 100%, #2677ff 100%);
      background: -o-linear-gradient(90deg, #4eafff 0%, #2677ff 100%, #2677ff 100%);
      background: -moz-linear-gradient(90deg, #4eafff 0%, #2677ff 100%, #2677ff 100%);
      background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%, #2677ff 100%);
    }
    .company {
      color: #ffffff;
      margin-top: 4px;
      margin-bottom: 8px;
      display: inline-block;
    }
    .text {
      color: #ffffff;
    }
    /deep/ .el-progress__text {
      top: -24px !important;
      color: #ffffff !important;
    }
  }
  .wuFt {
    display: flex;
    margin-bottom: 16px !important;
    justify-content: space-between;
    div {
      line-height: 32px;
    }
    button {
      margin-top: 0 !important;
      margin-right: 16px !important;
    }
  }
}
.RightFooterRg {
  display: flex;
  width: calc(100% - 236px);
  height: 112px;
  position: relative;
  flex-wrap: wrap;
  .FooterRgCard:nth-child(3) {
    margin-right: 24px;
  }
  .FooterRgCard {
    color: #37393c;
    width: 22.5%;
    height: 48px;
    background: #f5f8fc;
    border-radius: 4px;
    margin-bottom: 16px;
    margin-right: 2%;
    cursor: pointer;
    vertical-align: middle;
  }
  .FooterRgCardSpecail {
    display: inline-block;
  }
}
.screenBox {
  // width:48.5%;
  // height: 44%;
  position: absolute;
  cursor: pointer;
  right: 0;
  top: 0;
  // background:url('../../assets/images/screenAssets.png') no-repeat;
  // background-size: 100% 100%;
  padding: 13px 14px 12px 11px;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
  div {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  img {
    position: absolute;
    bottom: 12px;
    left: 11px;
    width: 24px;
    height: 24px;
  }
}
.FooterRgCard > div {
  height: 100%;
  // width: 14%;
  display: flex;
  align-items: center;
  margin-right: 2%;
}
.FooterRgCardimg {
  height: 20px;
  margin-left: 12px;
  margin-right: 8px;
}
.FooterRgCardimg1 {
  margin-left: 14px;
  margin-right: 12px;
  width: 12%;
  height: 18px;
}
.FooterRgCardimg2 {
  margin-left: 16px;
  margin-right: 12px;
  width: 14%;
  height: 22px;
}
// .RightFooterRg>.FooterRgCard:first-child {
//     margin-right: 3%;
// }
// .RightFooterRg>.FooterRgCard:nth-child(3) {
//     margin-right: 3%;
// }
// .RightFooterRg>.FooterRgCard:nth-child(5) {
//     margin-right: 3%;
// }
.RightFooterLeftTop {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  margin-top: 14px;
  .Top-img {
    vertical-align: middle;
    width: 22px;
    height: 20px;
  }
  .Top-title {
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
  }
  .RightFooterLeftTopTu {
    width: 40px;
    height: 40px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
/deep/.el-progress-bar__outer {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(1px);
}
/deep/.el-progress-bar {
  position: relative;
  padding-right: 0px;
}
/deep/.el-progress__text {
  position: absolute;
  top: 25px;
  right: 0px;
  font-weight: 500;
  color: #ffffff;
}
/deep/.el-progress-bar__inner {
  background-color: #268bff;
}
.redBox {
  color: #2677ff;
  margin-left: 5px;
  font-weight: 600;
}
</style>
