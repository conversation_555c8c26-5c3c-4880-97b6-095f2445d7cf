<template>
  <div class="conData">
    <div class="top-title">
      <div class="headerEcharts">
        <div class="conAssets-top">
          <span class="con-title">数据泄漏</span>
          <img src="../../assets/images/arrowRight.png" alt="" />
        </div>
        <el-badge
          :value="totalNum"
          class="badge"
          v-if="totalNum != 0"
          :class="{ 'big-num': totalNum > 100 }"
        >
          <span @click="handleDoing" class="goHandle"
            >去处理<span class="el-icon-d-arrow-right"></span
          ></span>
        </el-badge>
        <span @click="handleDoing" class="goHandle" v-else
          >去处理<span class="el-icon-d-arrow-right"></span
        ></span>
      </div>
    </div>
    <div class="data-content" v-loading="loading">
      <div class="content-left">
        <div class="left-title">
          <p class="left-top"> <i></i>正在监控关键词 </p>
          <p class="left-num">
            <span class="number">
              {{ dataList.keyword_num }}
            </span>
            <span
              class="rate-blue"
              :class="{
                'rate-red': dataList.keyword_rate < 0,
                'rate-gray': dataList.keyword_rate == 0
              }"
            >
              <i
                style="margin-right: 8px"
                :class="{
                  'el-icon-minus': dataList.keyword_rate == 0,
                  'el-icon-caret-top': dataList.keyword_rate > 0,
                  'el-icon-caret-bottom': dataList.keyword_rate < 0
                }"
                >{{
                  Math.abs(dataList.keyword_rate) == 0
                    ? 0
                    : Math.abs(dataList.keyword_rate).toFixed(1)
                }}%</i
              >
            </span>
          </p>
        </div>
        <!-- <div class="left-echart" v-if="dataList.keyword_to_num && dataList.keyword_to_num.length > 5"> -->
        <div class="left-echart">
          <div class="svg-one-container">
            <keywordChart ref="keywordChart" :chartData="dataList.keyword_to_num" />
          </div>
        </div>
        <!-- <div class="left-echart" id="left-echart" >
        </div> -->
        <!-- <div class="left-echart" >
          <div class="svg-one-container svg-animation-run">
            <div id="left-echart"></div>
          </div>
          <div class="svg-one-container svg-animation-run">
            <div id="left-echart1"></div>
          </div>
        </div> -->
      </div>
      <div class="content-right">
        <div class="right-top">
          <div class="right-title">
            <div class="left-title right">
              <p class="left-top"> <i></i>数据泄漏总数 </p>
              <p class="left-num">
                <span class="number">
                  {{ dataList.leak_data_all_num }}
                </span>
                <span
                  class="rate-blue"
                  :class="{
                    'rate-red': dataList.leak_data_rate < 0,
                    'rate-gray': dataList.leak_data_rate == 0
                  }"
                >
                  <i
                    style="margin-right: 8px"
                    :class="{
                      'el-icon-minus': dataList.leak_data_rate == 0,
                      'el-icon-caret-top': dataList.leak_data_rate > 0,
                      'el-icon-caret-bottom': dataList.leak_data_rate < 0
                    }"
                    >{{ Math.abs(dataList.leak_data_rate).toFixed(1) }}%</i
                  >
                </span>
                <!-- <span class="rate-blue rate-red">
                  <i class="el-icon-caret-top">{{ (dataList.leak_data_rate || 0).toFixed(1) +'%' }}</i></span> -->
              </p>
            </div>
            <!-- 暂时隐藏 -->
            <!-- <img src="../../assets/images/dataBreach.png" alt=""> -->
          </div>
          <div class="list">
            <div class="list-li" v-for="(item, index) of breachLists" :key="index">
              <p class="li-top">
                <img class="itemImg" :src="item.img" alt="" />
                <span class="li-content li-top-content">
                  <span style="vertical-align: middle">{{ item.title }}</span>
                  <img style="margin-left: 8%" src="../../assets/images/arrow-data.png" alt="" />
                </span>
              </p>
              <p class="li-bottom" :class="{ red: index == 0 }">
                {{ dataList[item.keyword] }}
              </p>
            </div>
          </div>
        </div>
        <div class="right-bottom">
          <div class="list list-bottom">
            <div class="list-li" v-for="(item, index) of appList" :key="index">
              <p class="li-top">
                <img class="itemImg" :src="item.img" alt="" />
                <span class="li-content">{{ item.title }}</span>
              </p>
              <p class="li-bottom bottom-num">
                {{ dataList[item.keyword] }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import keywordChart from './keywordChart.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import { dataBeachList } from '@/api/apiConfig/api.js'

export default {
  components: { keywordChart },
  data() {
    return {
      breachLists: [
        {
          title: '待确认',
          img: require('../../assets/images/tobeConfirmed.png'),
          keyword: 'leak_data_default_num'
        },
        {
          title: '已确认',
          img: require('../../assets/images/confirmed.png'),
          keyword: 'leak_data_sure_num'
        },
        {
          title: '已忽略',
          img: require('../../assets/images/ignored.png'),
          keyword: 'leak_data_ingore_num'
        }
      ],
      appList: [
        {
          title: '网盘',
          img: require('../../assets/images/netdisc.png'),
          keyword: 'wangpan_num'
        },
        {
          title: '文库',
          img: require('../../assets/images/library.png'),
          keyword: 'wenku_num'
        },
        {
          title: '代码仓库',
          img: require('../../assets/images/codeRepository.png'),
          keyword: 'code_num'
        }
      ],
      loading: false,
      dataList: {},
      totalNum: 0,
      numList: [],
      nameList: [],
      userInfo: {},
      user: {
        role: ''
      }
    }
  },

  async mounted() {
    if (this.user.role == 2 && !this.currentCompany) return
    await this.getData()
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.numList = []
        this.nameList = []
        this.getData()
      }
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },

  methods: {
    ...mapMutations(['changeMenuId']),
    async getData() {
      this.loading = true
      let res = await dataBeachList({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.totalNum = this.dataList.leak_data_default_num
        this.dataList = res.data
        for (var i = 0; i < res.data.keyword_to_num.length; i++) {
          this.numList.push(res.data.keyword_to_num[i].num)
          this.nameList.push(res.data.keyword_to_num[i].keyword)
        }
        if (this.dataList.keyword_to_num && this.dataList.keyword_to_num.length !== 0) {
          this.$refs.keywordChart.init(this.dataList.keyword_to_num)
        }
      }
    },
    handleDoing() {
      sessionStorage.setItem('menuId', '3-2')
      this.changeMenuId('3-2')
      this.$router.push('/dataLeak')
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  }
}
</script>

<style lang="less" scoped>
.conData {
  width: 68%;
  height: 382px;
  background-color: #fff;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  margin-right: 20px;
  margin-top: 20px;
  overflow: hidden;

  .top-title {
    width: 100%;
    height: 47px;
    // border-bottom: 1px solid #e9ebef;
    //  padding-bottom: 10px;
    .headerEcharts {
      padding: 0 !important;
      position: relative;
      width: 100%;
      height: 47px;
      border-bottom: 1px solid #e9ebef;
      display: flex;
      justify-content: space-between;
      .conAssets-top::before {
        content: '';
        position: absolute;
        left: 0px;
        top: 18px;
        width: 4px;
        height: 16px;
        background-color: #2677ff;
        border-radius: 0 2px 2px 0;
      }
      .conAssets-top {
        position: relative;
        // cursor: pointer;
        height: 47px;
        // width: 100%;
        // padding-top: 12px;
        line-height: 47px;
        padding-left: 16px;

        .con-title {
          // font-family: PingFangSC-Semibold;
          color: #37393c;
          font-size: 16px;
          font-weight: 600;
          vertical-align: middle;
        }
        img {
          margin-left: 8px;
          vertical-align: middle;
        }
      }
    }
    /deep/.el-badge {
      position: relative;
      .badge .el-badge__content,
      .is-fixed {
        // right: 90%;
        height: 12px;
        line-height: 12px;
        font-size: 10px;
        right: 40px;
        // left: auto !important;
        top: 10px !important;
        // right: unset !important;
        background: #ff4646 !important;
      }
    }

    .goHandle {
      // font-family: PingFang SC;
      color: #62666c;
      cursor: pointer;
      font-size: 14px;
      line-height: 47px;
      margin-right: 16px;

      & > span {
        margin-left: 5px;
      }
    }
  }
  .scrollWrap {
    height: 270px;
    overflow: hidden;
  }
  .data-content {
    width: calc(100% - 48px);
    margin: 0 auto;
    margin-top: 24px;
    margin-bottom: 24px;
    display: flex;
    justify-content: space-evenly;
    .content-left {
      width: 50%;
      margin-right: 16px;
      height: 265px;
      background: #f5f8fc;
      border-radius: 4px;
      padding-top: 20px;
      margin-right: 2%;

      .left-echart {
        position: relative;
        width: calc(100% - 40px);
        margin: 0 auto;
        // padding-top: 15px;
        height: 187px;
        overflow: hidden;
        // background-color: #fff;
        .svg-one-container {
          position: absolute;
          top: 0;
          left: 0;
          width: auto;
          height: 100%;
          background: rgba(255, 255, 255, 0);
        }
        .svg-animation-run-one {
          animation: svgmoveone_first 80s infinite linear;
        }
        .svg-animation-run-two {
          animation: svgmoveone_second 80s infinite linear;
        }
        @keyframes svgmoveone_first {
          0% {
            transform: translateX(0) translateZ(0);
          }
          100% {
            transform: translateX(-100%) translateZ(0);
          }
        }
        @keyframes svgmoveone_second {
          0% {
            transform: translateX(100%) translateZ(0);
          }
          100% {
            transform: translateX(0) translateZ(0);
          }
        }
      }
    }
    .content-right {
      width: 50%;
      height: 265px;
      .right-top {
        width: 100%;
        height: 185px;
        background: #f5f8fc;
        border-radius: 4px;
        // padding: 20px 20px 0 20px;
        // margin-right: 24px;
        overflow: hidden;
        box-sizing: border-box;

        .right-title {
          border-bottom: 1px solid #e3e5ea;
          height: 75px;
          display: flex;
          margin-right: 20px;
          margin-top: 20px;
          margin-left: 20px;
          box-sizing: border-box;
          .right {
            margin-left: 0 !important;
            margin-right: 20px;
          }
          img {
            // margin-left: 7%;
            width: 50%;
            height: 46px;
            vertical-align: middle;
          }
        }
      }
      .right-bottom {
        width: 100%;
        height: 86px;
        background: #f5f8fc;
        border-radius: 4px;
        margin-top: 16px;
        // padding-top: 20px;
        overflow: hidden;
        .list-bottom {
          // width: 100%;
          display: flex;
          box-sizing: border-box;
          justify-content: space-between;
          // margin-right: 20px;

          .list-li {
            width: 30%;
            margin-right: 0;
          }
          .list-li:nth-last-child(1) {
            margin-right: 20px;
          }
        }
        .bottom-num {
          margin-left: 26px !important;
        }
      }
    }
    .list {
      display: flex;
      justify-content: space-between;
      width: 90%;
      // margin: 0 auto;
      margin-top: 20px;
      margin-left: 28px;
      // margin-right: 30px;

      .list-li {
        width: 40%;
        margin-right: 5%;
        .li-top {
          display: flex;
          img {
            vertical-align: middle;
          }
          // width: 30%;
          .itemImg {
            vertical-align: middle;
            // width: 20px;
            height: 20px;
          }
          .li-content {
            vertical-align: middle;
            color: #62666c;
            // font-family: PingFang SC;
            font-size: 14px;
            display: inline-block;
            margin-left: 6px;
            line-height: 20px;
          }
          .li-top-content {
            width: 70%;
          }
        }
        .li-bottom {
          // font-family: PingFang SC;
          margin-top: 8px;
          color: #37393c;
          font-size: 20px;
          font-weight: 600;
          margin-left: 26px;
        }
        .red {
          color: #ff4646;
        }
      }
    }
    .left-title {
      // margin-top: 20px;
      margin-left: 20px;
      // overflow: hidden;
      vertical-align: middle;
      .left-top {
        // font-family: PingFangSC-Semibold;
        font-size: 14px;
        color: #37393c;
        height: 20px;
        line-height: 20px;
        i {
          margin-top: -3px;
          display: inline-block;
          width: 6px;
          height: 6px;
          margin-right: 5px;
          border-radius: 1px;
          -webkit-transform: rotate(135deg);
          -ms-transform: rotate(135deg);
          transform: rotate(135deg);
          background: #2677ff;
          vertical-align: middle;
        }
      }
      .left-num {
        // font-family: PingFangSC-Semibold;
        font-size: 24px;
        color: #37393c;
        margin-left: 12px;
        display: flex;
        align-items: center;
        margin-top: 8px;
        .number {
          font-weight: 600;
        }
      }
      .rate-blue {
        padding: 0 8px;
        display: inline-block;
        margin-left: 8px;
        height: 20px;
        line-height: 20px;
        background: rgba(38, 119, 255, 0.1216);
        border-radius: 10px;
        color: #2677ff;
        font-size: 14px;
        text-align: center;
        /deep/ .el-icon-minus {
          margin-left: 2%;
        }
      }
      .rate-red {
        color: #ff4646 !important;
        background: rgba(255, 70, 70, 0.08) !important;
      }
      .rate-gray {
        background: #e9ebef;
        color: #a8acb3;
      }
    }
  }
}
</style>
