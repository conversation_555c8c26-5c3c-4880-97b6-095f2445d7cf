<template>
  <div class="conWrap">
    <div class="top-title">
      <div class="headerEcharts">
        <div class="conAssets-top">
          <span class="con-title">事件告警</span>
          <img src="../../assets/images/arrowRight.png" alt="" />
        </div>
        <el-badge :value="totalNum" class="badge" v-if="totalNum != 0">
          <span @click="goHandle" class="goHandle"
            >去处理<span class="el-icon-d-arrow-right"></span
          ></span>
        </el-badge>
        <span @click="goHandle" class="goHandle" v-else
          >去处理<span class="el-icon-d-arrow-right"></span
        ></span>
      </div>
    </div>
    <div class="list-all">
      <ul class="title">
        <li>事件名称</li>
        <li>受影响资产</li>
        <li>状态</li>
      </ul>
      <vue-seamless-scroll
        class="scrollWrap"
        v-if="dataList.length > 0"
        :data="dataList"
        :class-option="classOption"
        ref="seamlessScroll"
      >
        <ul class="content" v-for="item in dataList" :key="item.id">
          <li style="color: rgba(38, 119, 255, 1)">{{ item.name }}</li>
          <li class="ruleItemBox">
            <p
              style="text-align: center; width: 100%"
              v-if="get_affect_assets(item.affect_assets).length == 0"
              >-</p
            >
            <div class="ruleItemBox1">
              <span
                class="ruleItem"
                v-for="ch in get_affect_assets(item.affect_assets, 'filter')"
                :key="ch"
                >{{ ch }}</span
              >
            </div>
            <div class="total" v-if="get_affect_assets(item.affect_assets).length > 2">
              <el-popover
                placement="top"
                width="315"
                style="padding-right: 0px !important; padding-left: 0px !important"
                popper-class="rulePopover"
                trigger="click"
              >
                <div class="myruleItemBox">
                  <span
                    class="myruleItem"
                    v-for="(v, i) in get_affect_assets(item.affect_assets)"
                    :key="i"
                    >{{ v }}</span
                  >
                </div>
                <div
                  slot="reference"
                  v-if="get_affect_assets(item.affect_assets).length > 2"
                  class="ruleItemNum"
                >
                  共{{ get_affect_assets(item.affect_assets).length }}条
                </div>
              </el-popover>
            </div>
          </li>
          <li>
            <span class="yelLine" v-if="!item.status || item.status == 0">待处理</span>
            <span class="blLine" v-if="item.status == 1">已处理</span>
            <span class="originLine" v-if="item.status == 2">已忽略</span>
          </li>
        </ul>
      </vue-seamless-scroll>
      <div v-else class="emptyClass">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-kong"></use>
        </svg>
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState, mapMutations } from 'vuex'
import vueSeamlessScroll from 'vue-seamless-scroll'
import { eventWaringList } from '@/api/apiConfig/api.js'

export default {
  components: { vueSeamlessScroll },
  props: ['taskIcon'],
  data() {
    return {
      dataList: [],
      limitMoveNumMax: 0,
      classOption: {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: this.limitMoveNumMax, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      },
      totalNum: 0
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.getTableList()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      this.getTableList()
    } else {
      this.getTableList()
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goHandle() {
      sessionStorage.setItem('menuId', '3-3')
      this.changeMenuId('3-3')
      this.$router.push('/eventWarning')
    },
    get_affect_assets(val, filter) {
      let arr = []
      if (val) {
        if (filter) {
          arr = val.slice(0, 2)
        } else {
          arr = val
        }
      } else {
        arr = []
      }
      return arr
    },
    async getTableList() {
      let res = await eventWaringList({
        page: this.currentPage,
        per_page: 50,
        event_status: 0, // 只展示待处理的
        operate_company_id: this.currentCompany
      })
      if (res.code == 0) {
        this.dataList = res.data.items ? res.data.items : []
        this.totalNum = res.data.total ? res.data.total : 0
        this.limitMoveNumMax = this.dataList.length
      }
    }
  }
}
</script>
<style lang="less" scoped>
.conWrap {
  width: 35%;
  margin-top: 20px;
  height: 382px;
  border-radius: 4px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  .top-title {
    width: 100%;
    height: 47px;
    // border-bottom: 1px solid #e9ebef;
    //  padding-bottom: 10px;
    .headerEcharts {
      padding: 0 0 0 0 !important;
      position: relative;
      width: 100%;
      height: 47px;
      border-bottom: 1px solid #e9ebef;
      justify-content: space-between;
      .conAssets-top::before {
        content: '';
        position: absolute;
        left: 0px;
        top: 18px;
        width: 4px;
        height: 16px;
        background-color: #2677ff;
        border-radius: 0 2px 2px 0;
      }
      .conAssets-top {
        position: relative;
        // cursor: pointer;
        height: 47px;
        // width: 100%;
        // padding-top: 12px;
        line-height: 47px;
        padding-left: 16px;

        .con-title {
          // font-family: PingFangSC-Semibold;
          color: #37393c;
          font-size: 16px;
          font-weight: 600;
          vertical-align: middle;
        }
        img {
          margin-left: 8px;
          vertical-align: middle;
        }
      }
    }
    // .item {
    //   // margin-top: 3px;
    //   // margin-right: 40px;
    //   position: absolute !important;
    //   right: 0;
    // }
    .badge /deep/ .el-badge__content,
    .is-fixed {
      // right: 90%;
      height: 12px;
      line-height: 12px;
      font-size: 10px;
      left: 7px !important;
      top: 10px !important;
      right: unset !important;
      background: #ff4646 !important;
    }
    .big-num {
      left: -7px !important;
      top: 9px !important;
    }
    .goHandle {
      // font-family: PingFang SC;
      color: #62666c;
      cursor: pointer;
      font-size: 14px;
      line-height: 47px;
      margin-right: 16px;

      & > span {
        margin-left: 5px;
      }
    }
  }

  .scrollWrap {
    height: 268px;
    overflow: hidden;
  }
  .emptyClass {
    height: 75%;
    text-align: center;
    vertical-align: middle;
    svg {
      display: inline-block;
      font-size: 120px;
      margin-top: 80px;
    }
    p {
      line-height: 25px;
      color: #d1d5dd;
      span {
        margin-left: 4px;
        color: #2677ff;
        cursor: pointer;
      }
    }
  }
  .list-all {
    width: calc(100% - 48px);
    margin: 0 auto;
    margin-top: 24px;
  }
  .title {
    li {
      padding: 14px 20px 14px 15px !important;
    }
  }
  ul {
    height: 44px;
    li:nth-child(1) {
      width: 33%;
      vertical-align: middle;
    }
    li:nth-child(2) {
      width: 50%;
    }
    li:nth-child(3) {
      width: 30%;
      text-align: center;
    }
    .ruleItemBox {
      // padding: 8px;
      display: flex;
      // flex-wrap: wrap !important;
      align-items: center;
      padding: 0 12px !important;
      .total {
        width: 80px;
      }
    }
    .ruleItemBox1 {
      width: 0;
      flex: 1;
      line-height: 44px;
      // width: calc(100% - 80px);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .ruleItem,
    .ruleItemNum {
      line-height: 16px;
      padding: 2px 10px;
      margin: 5px 8px 5px 0px;
      background: #ffffff;
      border-radius: 14px;
      border: 1px solid #d1d5dd;
      white-space: pre-wrap;
    }
    .ruleItemNum {
      display: inline-block;
      background: #f0f3f8;
      border: 1px solid #dce5f3;
      cursor: pointer;
    }
  }
  .yelLine {
    display: inline-block !important;
    padding: 0 8px;
    height: 22px;
    line-height: 22px;
    border-radius: 4px;
    box-sizing: border-box;
    background: rgba(255, 194, 38, 0.16);
    color: #ffc226;
    // margin-left: 8px;
    vertical-align: middle;
  }
  .blLine {
    display: inline-block !important;
    padding: 0 8px;
    height: 22px;
    line-height: 22px;
    border-radius: 4px;
    box-sizing: border-box;
    background: rgba(38, 119, 255, 0.12);
    color: #2677ff;
    margin-left: 8px;
    vertical-align: middle;
  }
}
.content {
  li {
    padding: 16px 14px !important;
  }
}
</style>
