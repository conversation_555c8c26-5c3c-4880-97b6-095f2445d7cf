<template>
  <div class="svg-container" v-if="!loading">
    <div class="svg-one-container" :class="{ 'svg-animation-run-one': chatData.length >= 5 }">
      <div class="svg-one">
        <svg :width="width + 'px'" :height="height + 'px'">
          <!-- <g> -->
          <g :transform="'translate(-' + startPointTransform + ',0)'">
            <!-- transform="translate(-4px,0)" -->
            <g>
              <path
                class="svg-line-path"
                :d="chatData.map((item) => item.category.d).join(' ') + chatData_line_path"
                fill="url(#zr1-g0)"
                fill-opacity="0.7"
              />
              <path
                class="svg-line-path"
                :d="chatData.map((item) => item.category.d).join(' ') + chatData_line"
                fill="none"
                stroke="rgba(38, 119, 255, 1)"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </g>
            <g v-for="(point, i) in chatData" :key="i">
              <foreignObject
                :x="point.category.d.split(' ')[1]"
                :y="point.category.d.split(' ')[2]"
                width="160"
                height="160"
              >
                <circle
                  :cx="point.category.d.split(' ')[1]"
                  :cy="point.category.d.split(' ')[2]"
                  r="4"
                  stroke="#fff"
                  stroke-width="2"
                  style="fill: rgba(38, 119, 255, 1)"
                >
                </circle>
                <el-tooltip class="item" effect="dark" placement="top" :key="i">
                  <div slot="content"> {{ point.category.keyword }} {{ point.category.num }} </div>
                  <p
                    :style="'animation-delay: ' + (Math.random() * 2).toFixed(6) + 's'"
                    class="word-blink"
                    :x="point.category.t.x"
                    :y="point.category.t.y"
                    fill="#333"
                    fontSize="12px"
                    >{{ point.category.keyword }}</p
                  >
                </el-tooltip>
              </foreignObject>
            </g>
          </g>
          <defs>
            <linearGradient
              gradientUnits="objectBoundingBox"
              x1="0"
              y1="0"
              x2="0"
              y2="1"
              id="zr1-g0"
            >
              <stop offset="0%" stop-color="rgb(38,119,255)" stop-opacity="0.32"></stop>
              <stop offset="100%" stop-color="rgb(38,119,255)" stop-opacity="0.04"></stop>
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
    <div class="svg-one-container svg-animation-run-two" v-if="chatData.length >= 5">
      <div class="svg-one">
        <svg :width="width + 'px'" :height="height + 'px'">
          <g :transform="'translate(-' + startPointTransform + ',0)'">
            <g>
              <path
                class="svg-line-path"
                :d="chatData.map((item) => item.category.d).join(' ') + chatData_line_path"
                fill="url(#zr1-g0)"
                fill-opacity="0.7"
              />
              <path
                class="svg-line-path"
                :d="chatData.map((item) => item.category.d).join(' ')"
                fill="none"
                stroke="rgba(38, 119, 255, 1)"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </g>
            <g v-for="(point, i) in chatData" :key="i">
              <foreignObject
                :x="point.category.d.split(' ')[1]"
                :y="point.category.d.split(' ')[2]"
                width="160"
                height="160"
              >
                <circle
                  :cx="point.category.d.split(' ')[1]"
                  :cy="point.category.d.split(' ')[2]"
                  r="4"
                  stroke="#fff"
                  stroke-width="2"
                  style="fill: rgba(38, 119, 255, 1)"
                >
                </circle>
                <el-tooltip class="item" effect="dark" placement="top" :key="i">
                  <div slot="content"> {{ point.category.keyword }} {{ point.category.num }} </div>
                  <p
                    class="word-blink"
                    :x="point.category.t.x"
                    :y="point.category.t.y"
                    fill="#333"
                    fontSize="12px"
                    >{{ point.category.keyword }}</p
                  >
                </el-tooltip>
              </foreignObject>
            </g>
          </g>
          <defs>
            <linearGradient
              gradientUnits="objectBoundingBox"
              x1="0"
              y1="0"
              x2="0"
              y2="1"
              id="zr1-g0"
            >
              <stop offset="0%" stop-color="rgb(38,119,255)" stop-opacity="0.32"></stop>
              <stop offset="100%" stop-color="rgb(38,119,255)" stop-opacity="0.04"></stop>
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 图表高度
    height: {
      type: Number,
      default: 190
    },
    // Y 轴标签宽度
    yLabelWidth: {
      type: Number,
      default: 100
    },
    // 柱子宽度
    barWidth: {
      type: Number,
      default: 20
    },
    // Y 轴刻度数量
    yTickCount: {
      type: Number,
      default: 9
    },
    // 标签字体大小
    labelFontSize: {
      type: Number,
      default: 12
    },
    // Y 轴标签右边距
    yLabelPaddingRight: {
      type: Number,
      default: 8
    },
    // X 轴标签上边距
    xLabelPaddingTop: {
      type: Number,
      default: 8
    }
  },
  created() {
    this.loading = true
  },
  data() {
    return {
      chartData: [],
      loading: false,
      width: '',
      yMaxValue: '',
      startPointTransform: 0,
      colors: ['red', 'blue'],
      chatData: [],
      // category: [],
      coordinateLeftTopX: 0,
      coordinateLeftTopY: 0,
      verticalAxisHeight: 0,
      horizontalAxisWidth: 0,
      yGap: 0,
      yLineList: [],
      chatData_start: {},
      chatData_end: {},
      chatData_line_path: '',
      chatData_line: ''
    }
  },
  methods: {
    init(chartData) {
      this.chartData = chartData
      this.width = this.chartData.length * 64
      // 坐标系左上角点的x坐标
      this.coordinateLeftTopX = this.yLabelWidth
      // 坐标系左上角点的y坐标
      this.coordinateLeftTopY = this.labelFontSize / 2
      // 坐标系的高度
      this.verticalAxisHeight =
        this.height - this.coordinateLeftTopY - this.labelFontSize - this.xLabelPaddingTop
      // 坐标系的宽度
      // this.horizontalAxisWidth = this.width - this.coordinateLeftTopX;
      // y 轴刻度线的间距
      this.yGap = this.verticalAxisHeight / this.yTickCount
      let maxValue = Math.max.apply(
        Math,
        this.chartData.map((item) => {
          return item.num
        })
      )
      this.yMaxValue = maxValue + 400
      const yUnit = this.yMaxValue / this.yTickCount
      this.yLineList = Array.from({ length: this.yTickCount + 1 }).map(
        (_, i) => this.yMaxValue - yUnit * i
      )

      this.chatData = this.generateChartData(this.chartData)
      this.startPointTransform = 32
      this.loading = false
    },
    generateChartData(list) {
      const chartData = []
      const len = list.length
      // 平分横向坐标宽度
      // const averageWidth = this.horizontalAxisWidth / list.length;
      const averageWidth = 64

      for (let i = 0; i < len; i++) {
        const item = list[i]
        let category = []
        // x坐标刻度点
        const tickPosition = averageWidth * (i + 0.5)
        // 多条折线图
        if (Array.isArray(item)) {
          category = item.value.map((c) => this.genCategory(c, tickPosition, i))
        } else if (Object.prototype.toString.call(item) === '[object Object]') {
          // 一条折线图
          category = this.genCategory(item, tickPosition, i)
        } else {
          throw new Error('value必须为对象或者数组')
        }

        chartData.push({
          tickPosition,
          category
        })
      }
      this.chatData_start = chartData[0].category
      this.chatData_end = chartData[this.chartData.length - 1].category
      this.chatData_end.endY = this.chatData_end.p.x + 64
      this.chatData_line_path = `L${this.chatData_end.endY} ${this.chatData_start.p.y} L${this.chatData_end.endY} 180 L${this.chatData_start.p.x} 180Z`
      this.chatData_line = `L${this.chatData_end.endY} ${this.chatData_start.p.y}`
      return chartData
    },
    genCategory(v, x, index) {
      // 计算y坐标点
      const yPosition =
        (1 - v.num / this.yMaxValue) * this.verticalAxisHeight + this.coordinateLeftTopY
      return {
        ...v,
        yPosition,
        d: `${index === 0 ? 'M' : 'L'} ${x} ${yPosition}`,
        t: {
          // 文字位置
          x: x - 2,
          y: yPosition + 18
        },
        p: {
          x,
          y: yPosition
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.svg-one {
  width: auto;
  height: 100%;
}

.svg-container {
  position: relative;
  width: auto;
  height: 100%;
  &:hover {
    .svg-one-container {
      animation-play-state: paused;
    }
    .svg-animation-run-two {
      animation-play-state: paused;
    }
    .svg-animation-run-one {
      animation-play-state: paused;
    }
    .word-blink {
      width: 30px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      animation-play-state: paused;
    }
  }
}
.svg-one-container {
  position: absolute;
  top: 0;
  left: 0;
  width: auto;
  height: 100%;
  background: rgba(255, 255, 255, 0);
}
.svg-animation-run-one {
  animation: svgmoveone_first 80s infinite linear;
}
.svg-animation-run-two {
  // display: none;
  // transform: translateX(100%) translateZ(0);
  animation: svgmoveone_second 80s infinite linear;
}
@keyframes svgmoveone_first {
  0% {
    transform: translateX(0) translateZ(0);
  }
  100% {
    transform: translateX(-100%) translateZ(0);
  }
}
@keyframes svgmoveone_second {
  0% {
    transform: translateX(100%) translateZ(0);
  }
  100% {
    transform: translateX(0) translateZ(0);
  }
}
.word-blink {
  display: inline-block;
  width: 56px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  animation: blink_15BvN 1s infinite alternate cubic-bezier(0.71, 0.01, 0.33, 1.02);
  &:hover {
    color: #15141a;
    cursor: pointer;
  }
}
@keyframes blink_15BvN {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.3;
  }
}
</style>
