<template>
  <div class="container">
    <div class="conSelf">
      <div class="recommend">
        <img src="../../assets/images/touxiang.png" alt="" />
        <span class="hi">
          Hi，
          <span class="f-label" v-if="userObj.role == 3 && userObj.level == 0">测试</span>
          <b v-if="userObj.role == 3"
            >{{ companyObj.name }}<el-divider direction="vertical"></el-divider
            ><i>{{ userObj.name }}</i></b
          >
          <b v-if="userObj.role == 2">{{ userObj.name }}</b>
          ~欢迎回来！
        </span>
      </div>
      <div class="star">
        <img src="../../assets/images/workstar.png" />
      </div>
    </div>
    <div class="total-static" v-loading="loading">
      <div class="total-list" v-for="(data, index) in dataList" :key="index">
        <div class="list-left">
          <img :src="data.img" alt="" />
        </div>
        <div class="list-right">
          <p class="num">{{ numList[data.keyword] }}</p>
          <p class="title" @click="handleDoing(data.id, data.path)">{{ data.title }}</p>
          <!-- <p class="title">{{ data.title }}</p> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState, mapGetters } from 'vuex'
import { worktableList } from '@/api/apiConfig/api.js'
import { personInfo } from '@/api/apiConfig/person.js'

export default {
  data() {
    return {
      userObj: {},
      companyObj: {},
      user: {},
      name: '',
      dataList: [
        {
          img: require('../../assets/images/workbench/xiansuo.png'),
          title: '线索总数',
          keyword: 'clue_num',
          id: '1-6-2',
          path: '/companyBank'
        },
        {
          img: require('../../assets/images/workbench/zichan.png'),
          title: '资产总数',
          keyword: 'table_ip_num',
          id: '1-3-1',
          path: '/assetsLedger'
        },
        {
          img: require('../../assets/images/workbench/zujian.png'),
          title: '组件总数',
          keyword: 'sure_rule_num',
          id: '1-3-1',
          path: '/assetsLedger'
        },
        {
          img: require('../../assets/images/workbench/loudong.png'),
          title: '漏洞总数',
          keyword: 'poc_num',
          id: '3-1-2',
          path: '/repairLeakScan'
        },
        {
          img: require('../../assets/images/workbench/shuju.png'),
          title: '数据泄露总数',
          keyword: 'leak_num',
          id: '3-2',
          path: '/dataLeak'
        },
        {
          img: require('../../assets/images/workbench/shijian.png'),
          title: '事件告警总数',
          keyword: 'warn_num',
          id: '3-3',
          path: '/eventWarning'
        }
      ],
      numList: {
        clue_num: 0,
        poc_num: 0,
        table_ip_num: 0,
        sure_rule_num: 0,
        leak_num: 0,
        warn_num: 0
      },
      loading: false
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  async mounted() {
    if (this.user.role == 2) {
      if (!this.currentCompany) return
      await this.getData()
      this.selfInfo()
    } else {
      await this.getData()
      this.selfInfo()
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    async selfInfo() {
      let userMessage = await personInfo({
        detail: 1,
        operate_company_id: this.currentCompany
      })
      if (userMessage.code == 0) {
        let userInfo = userMessage.data
        this.userObj = userMessage.data.user
        this.name = this.user.name
        if (userInfo.company) {
          this.companyObj = userInfo.company
        }
      }
    },
    async getData() {
      this.loading = true
      let res = await worktableList({
        operate_company_id: this.currentCompany
      }).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        for (var k in this.numList) {
          this.numList[k] = res.data[k]
        }
      }
    },
    handleDoing(id, path) {
      sessionStorage.setItem('menuId', id)
      this.changeMenuId(id)
      this.$router.push(path)
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.selfInfo()
        this.getData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 225px;
  width: 100%;
  .conSelf {
    background: url(../../assets/images/workBG.png) no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 165px;
    position: relative;
    padding-top: 28px;
    .recommend {
      // margin-top: 25px;
      margin-left: 25px;
      margin-bottom: 28px;
      img {
        vertical-align: middle;
      }
      .hi {
        // font-family: PingFangSC-Semibold;
        font-size: 16px;
        color: #3d3d3d;
        height: 20px;
        margin-left: 12px;
        vertical-align: middle;
      }
    }
    .star {
      position: absolute;
      right: 2%;
      top: -1%;
    }
  }
  .total-static {
    box-sizing: border-box;
    position: absolute;
    top: 105px;
    left: 20px;
    width: calc(100% - 40px);
    margin: 0 auto;
    height: 120px;
    display: flex;
    background-color: #fff;
    justify-content: space-between;
    border-radius: 4px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
    padding-right: 30px;

    .total-list:nth-child(1) {
      margin-left: 30px;
    }
    // .total-list:nth-last-child(1){
    //   margin-right: 30px;
    // }
    .total-list {
      display: flex;
      margin-top: 30px;
      width: 14%;
      // margin-left: 30px;
    }
    .list-left {
      width: 56px;
      height: 56px;
      background-color: rgba(38, 119, 255, 0.08);
      margin-right: 12px;
      text-align: center;
      line-height: 42px;
      // img {
      //   // margin: 15px 0 0 12px;
      // }
    }
    .list-right {
      .num {
        font-size: 24px;
        color: #37393c;
        // font-family: PingFangSC-Semibold;
        font-weight: 600;
      }
      .title {
        // font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #62666c;
        cursor: pointer;
      }
    }
  }
}
</style>
