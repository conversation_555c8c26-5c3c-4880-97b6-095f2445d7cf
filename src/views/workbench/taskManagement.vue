<template>
  <div class="conTask">
    <div class="con-nav">
      <div class="headerEchart">
        <div class="headerEchart-top">
          <span>
            <span class="top-tit">任务管理</span>
            <img src="../../assets/images/arrowRight.png" alt=""
          /></span>
        </div>
      </div>
      <div class="task-nav">
        <div
          class="el-col"
          :class="{ 'el-active': index == num }"
          v-for="(navItem, index) of navLists"
          :key="index"
        >
          <div @click="navClick(navItem, index)">
            <span class="el-dropdown-link">
              {{ navItem.taskLabel }}
            </span>
          </div>
          <div class="arrow-top arrow-box" v-show="index == num">
            <b class="top"><i class="top-arrow1"></i><i class="top-arrow2"></i></b>
          </div>
        </div>
      </div>
    </div>
    <div class="task-content">
      <!-- <div class="arrow-top arrow-box">
        <b class="top" :style="num >= 1 ? { left: 11.2 * num + 6 + '%' } : ''"><i class="top-arrow1"></i><i class="top-arrow2"></i></b>
      </div> -->
      <template v-if="taskInfo && taskInfo.id">
        <div class="task-bottom">
          <div class="task-box" v-loading="taskLoading">
            <div class="left">
              <div
                class="tu"
                v-if="currentPercent !== 100 || (stepArr && !(taskInfo.step > stepArr.length))"
              ></div>
              <div class="tuFinish" v-else></div>
            </div>
            <div class="task-title">
              <p class="title-top">
                <el-tooltip
                  :open-delay="500"
                  :content="taskInfo.name"
                  class="item"
                  popper-class="chainClass"
                  effect="dark"
                  placement="top"
                >
                  <span class="company-name">{{ taskInfo.name || '' }}</span>
                </el-tooltip>
                <span class="statu" v-if="isFinish">已完成</span>
                <span class="statu" v-else>{{ taskStatus }}</span>
                <taskStep v-if="stepArr" stepType="view" :currentStep="taskInfo.step" />
              </p>
              <p class="title-bottom">
                <img v-if="taskInfoTip" src="../../assets/images/taskImg.png" alt="" />
                <!--<span class="title-content" v-if="num==0">成功获取企业初始线索：<span class="title-num">200</span>条；确认使用线索：<span class="title-num">160</span>条；忽略线索：<span class="title-num">160</span>条 </span>
                <span class="title-content" v-if="num==1">成功获取企业初始线索：<span class="title-num">200</span>条；确认使用线索：<span class="title-num">160</span>条；忽略线索：<span class="title-num">160</span>条 </span>
                <span class="title-content" v-if="num==2">发现的IP：<span class="title-num">200</span>个，发现组件：<span class="title-num">160</span>个</span>
                <span class="title-content" v-if="num==3">初始域名：<span class="title-num">20</span>个，爆破层级：<span class="title-num">10</span>级，结果数量：<span class="title-num">200</span>个</span>
                <span class="title-content" v-if="num==4">已纳入管理资产：<span class="title-num">20</span>个，未纳入管理资产：<span class="title-num">10</span>个，新增端口资产：<span class="title-num">200</span>个</span>
                <span class="title-content" v-if="num==5">导入：<span class="title-num">20</span>个资产，可访问资产：<span class="title-num">10</span>个，不可访问资产：<span class="title-num">20</span>个</span>
                <span class="title-content" v-if="num==6">成功发现高危漏洞：<span class="title-num">20</span>个，中危漏洞：<span class="title-num">10</span>个</span>
                <span class="title-content" v-if="num==7">----</span> -->
                <span class="title-content" v-html="taskInfoTip"></span>
              </p>
            </div>
            <div class="task-button">
              <span class="button button-one" @click="goTaskPage()">查看任务</span>
              <span
                class="button button-two"
                v-if="taskDetailType"
                @click="jumpPage(stepItem, 1)"
                >{{ taskDetailType }}</span
              >
              <!-- <span class="button button-two" @click="jumpPage(stepItem,1)">任务简报</span> -->
              <!-- <span class="button button-two" @click="jumpPage(stepItem,2)" v-if="num == 2 || num == 3 || num == 6">任务详情</span> -->
              <!-- <span class="button button-two" @click="jumpPage(stepItem,3)" v-if="num == 4 || num == 5">任务记录</span> -->
            </div>
          </div>
          <div class="progress" v-if="currentPercent == 100">
            <div class="progressFinish">
              <div class="progressFinish_innerText"> 已完成 </div>
            </div>
          </div>
          <el-progress
            v-else
            :text-inside="true"
            :format="format"
            :stroke-width="4"
            :percentage="currentPercent"
          ></el-progress>
        </div>
        <div class="task-detail">
          <div class="task-name">
            <div class="name">
              <span class="font detail-left">任务名称：</span>
              <span class="font detail-right">{{ taskInfo.name || '' }}</span>
            </div>
            <div class="time">
              <span class="font detail-left">开始时间：</span>
              <span class="font detail-right">{{ taskInfo.created_at || '' }}</span>
            </div>
          </div>
          <div class="task-clue">
            <div class="clue-left">{{ stepItem.tagName }}： </div>
            <div
              class="clue-center"
              v-if="!taskInfo.listData || (taskInfo.listData && taskInfo.listData.length == 0)"
              >暂无数据</div
            >
            <div class="clue-center" v-else>
              <div class="clue-container">
                <span
                  class="clueBlock"
                  v-for="(item, index) in taskInfo.listData.slice(0, 50)"
                  :key="index"
                >
                  <template v-if="num == 0 || num == 1">
                    <span class="clueType">{{ clueTypeMap[item.type] }}</span>
                    <span v-if="item.type == 3">
                      <img
                        :src="
                          item.content.includes('http') ? item.content : showSrcIp + item.content
                        "
                        alt=""
                        style="width: 23px; height: 23px"
                      />
                    </span>
                    <span v-else>{{ item.content }}</span>
                  </template>
                  <template v-else-if="num == 4">
                    <span class="clueContent">{{ item.ip }}</span>
                  </template>
                  <template v-else-if="num == 5">
                    <span class="clueContent" v-if="taskInfo.task_type == 2">{{ item.url }}</span>
                    <span class="clueContent" v-else>{{ item.ip }}:{{ item.port }}</span>
                  </template>
                  <template v-else-if="num == 6">
                    <span class="clueContent"
                      >{{ item.common_title }}
                      <span v-if="item.host_info">-- {{ item.host_info }}</span></span
                    >
                  </template>
                  <template v-else>
                    <span class="clueContent">{{ item }}</span>
                  </template>
                </span>
              </div>
              <!-- <div class="more">更多</div> -->
            </div>
            <div class="clue-right">
              共<span class="title-num number-last">{{
                taskInfo.listData ? taskInfo.listData.length : '0'
              }}</span
              >条
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="task-bottom">
          <div class="task-box" v-loading="taskLoading">
            <div class="left">
              <div class="tuFinish"></div>
            </div>
            <div class="task-title">
              <div class="task-title-top">
                <img src="../../assets/images/taskType.png" alt="" />
                {{ stepItem.taskLabel }}
              </div>
              <div class="task-title-middle">{{ stepItem.description }}</div>
              <div class="task-title-bottom" @click="goTaskPage">开始任务</div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { resetMessage } from '@/utils/resetMessage.js' // 页面多个请求报错信息一样时只提示一次
import { mapGetters, mapState, mapMutations } from 'vuex'
import taskStep from '../unit_surveying/taskStep.vue'
import {
  unitTask,
  recommendTask,
  assetsScanTask,
  domainScanTask,
  assetsCheckTask,
  assetsOnlineTask,
  pocTask,
  fakeDetectTask,
  auditTaskInfo,
  statusDetectTaskInfo
} from '@/api/apiConfig/api.js'
import { tabNumClues } from '@/api/apiConfig/clue.js'
import { domainAssetProgreeUpdate } from '@/api/apiConfig/domain.js'
import { fakeTabCount, fakeTaskInfo } from '@/api/apiConfig/phishing.js'

export default {
  components: { taskStep },
  provide() {
    return {
      getStepArr: () => this.navLists[this.num].stepArr
    }
  },
  data() {
    return {
      taskDetailType: '',
      clueTypeMap: {
        0: '根域',
        1: '证书',
        2: 'ICP',
        3: 'ICON',
        4: '关键词',
        5: '子域名',
        6: 'IP',
        10: 'FID'
      },
      stepItem: {},
      intervalTimer: null,
      taskStatus: '测绘中',
      list: [],
      currentPercent: 0,
      tabIcon: 'one',
      tabNum: {
        one: { high: '', middle: '', low: '' },
        // 'one': {a: '', b: '', c: '', d: ''},
        two: { konwn_table_num: '', unknown_table_num: '', threat_table_num: '' }
      },
      taskLoading: false,
      fakeStepInfoArr: [
        {
          clueSecondTitle: '初始线索',
          clueText: '成功获取企业初始线索',
          clueTitle: '依据企业名称获取单位资产初始线索',
          tableText: '初始线索'
        },
        {
          clueSecondTitle: '线索扩展',
          clueText: '成功获取特征线索',
          clueTitle: '通过域名线索扩展，扩展线索已自动补充至线索库',
          tableText: '特征线索'
        },
        {
          clueSecondTitle: '关键词获取',
          clueText: '关键词',
          clueTitle: '支持自定义主体词和辅助词，进行关键词联想',
          tableText: '关键词自定义'
        },
        {
          clueSecondTitle: '关键词获取',
          clueText: '关键词获取',
          clueTitle: '通过基础关键词进行智能联想，联想后的线索已自动补充至线索库',
          tableText: '关键词联想'
        },
        {
          clueSecondTitle: '已生成线索总表',
          clueText: '成功获取特征线索',
          clueTitle: '已生成线索总表，可进行仿冒数据发现',
          tableText: '线索总表'
        }
      ],
      stepInfoArr: [
        {
          clueSecondTitle: '初始线索',
          clueText: '成功获取企业初始线索',
          clueTitle: '依据企业名称获取单位资产初始线索',
          tableText: '初始线索'
        },
        {
          clueSecondTitle: '依据IP段扩展',
          clueText: '成功获取企业初始线索',
          clueTitle:
            '通过原始线索中的IP段进行线索扩展，扩展线索已自动补充至线索库，可通过域名继续扩展',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '依据域名扩展',
          clueText: '成功获取企业初始线索',
          clueTitle: '通过域名线索扩展，扩展线索已自动补充至线索库，可通过ICP继续扩展',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '依据ICP扩展',
          clueText: '成功获取企业初始线索',
          clueTitle: '通过ICP线索扩展，扩展线索已自动补充至线索库，可通过证书继续扩展',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '依据证书扩展',
          clueText: '成功获取企业初始线索',
          clueTitle: '通过证书线索扩展，扩展线索已自动补充至线索库，扩展完成后生成线索库',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '生成线索总表',
          clueText: '共生成资产线索：',
          clueTitle: '依据原始线索和扩展线索生成线索总表，可依据线索总表进行资产推荐',
          tableText: '资产线索'
        },
        {
          clueSecondTitle: '线索扩展',
          clueText: '共生成资产线索',
          clueTitle: '依据原始线索扩展其他线索，可生成线索总表',
          tableText: '资产线索'
        }
      ],
      expandType: '1',
      accuracyIndex: 1,
      taskInfo: {
        listData: []
      },
      // navLists: ['单位资产测绘', '云端资产推荐', '资产扫描任务', '域名发现任务', '资产核查任务', '资产状态检测', '漏洞扫描任务', '钓鱼仿冒发现'],
      navLists: [
        {
          taskLabel: '单位资产测绘',
          taskPath: '/unitIndex',
          stepArr: [
            {
              title: '输入企业名称'
            },
            {
              title: '推荐线索获取'
            },
            {
              title: '云端资产推荐'
            },
            {
              title: '资产可信度评估'
            }
          ],
          description:
            '通过企业名称一键式梳理互联网暴露面资产，依托多种模式的自动化线索扩展和资产识别算法，快速、高效。',
          tagName: '线索'
        },
        {
          taskLabel: '云端资产推荐',
          taskPath: '/assetsCloud',
          stepArr: [
            {
              title: '目标线索选择'
            },
            {
              title: '云端资产推荐'
            },
            {
              title: '资产可信度评估'
            },
            {
              title: '关联任务自定义'
            }
          ],
          description:
            '支持自定义线索进行影子资产的发现，帮助用户快速、精准的获取资产数据，适用于不同场景资产盘点。',
          tagName: '线索'
        },
        {
          taskLabel: '资产扫描任务',
          taskPath: '/assetsScan',
          description:
            '支持针对目标IP、IP段、域名进行深度探测，发现其开放的端口、协议、组件、地理位置等信息。',
          tagName: '扫描目标'
        },
        {
          taskLabel: '域名发现任务',
          taskPath: '/domainTask',
          description: '支持对目标域名实施域名枚举以及验证，帮助用户发现更多的域名资产信息。',
          tagName: '域名结果'
        },
        {
          taskLabel: '资产核查任务',
          taskPath: '/checkTask',
          stepArr: [
            {
              title: '导入核查资产'
            },
            {
              title: '选择核查资产'
            },
            {
              title: '输出核查结果'
            }
          ],
          description:
            '快速盘点用户输入资产是否已纳入系统进行管理，支持对资产台账以及推荐资产库的全量数据进行核查研判。',
          tagName: '未纳管资产'
        },
        {
          taskLabel: '资产状态检测',
          taskPath: '/statusTask',

          stepArr: [
            {
              title: '导入检测资产'
            },
            {
              title: '输出检测结果'
            }
          ],
          description: '快速判断用户导入资产的存活状态并输出结果。',
          tagName: '可访问资产'
        },
        {
          taskLabel: '漏洞扫描任务',
          taskPath: '/leakScan',
          description:
            '针对客户资产进行高价值PoC检测，发现存在的漏洞和安全弱点，并及时提修复建议和措施。',
          tagName: '发现漏洞'
        },
        {
          taskLabel: '钓鱼仿冒发现',
          taskPath: '/phishingTask',
          stepArr: [
            {
              title: '输入企业名称'
            },
            {
              title: '特征线索获取'
            },
            {
              title: '仿冒数据发现'
            },
            {
              title: '数据存活性检测'
            }
          ],
          description:
            '通过企业名称自动化发现钓鱼仿冒网站，结合线索扩展算法以及关联词联想技术实现数据的高效率获取。',
          tagName: '可访问钓鱼网站URL'
        }
      ],
      num: 0,
      stepArr: [
        {
          title: '输入企业名称'
        },
        {
          title: '推荐线索获取'
        },
        {
          title: '云端资产推荐'
        },
        {
          title: '资产可信度评估'
        }
      ],
      currentStep: 1,
      isFinish: false,
      taskInfoTip: '',
      taskInfo: {},
      companyName: '',
      progressBar: false,
      setTimer: null,
      userInfo: {},
      user: {
        role: ''
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany', 'getterWebsocketMessage'])
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  mounted() {
    this.stepItem = this.navLists[0]
    if (this.user.role == 2 && !this.currentCompany) return
    this.getTaskInfo(0)
  },
  watch: {
    getterWebsocketMessage(msg) {
      this.handleMessage(msg)
    },
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.taskInfo = {}
        this.num = 0
        this.navClick(this.navLists[0], 0)
        // this.getTaskInfo(0);
      }
    }
  },
  methods: {
    ...mapMutations(['changeMenuId']),
    goTaskPage() {
      this.changeMenuId('2-1')
      this.$router.push(this.stepItem.taskPath)
    },
    jumpPage(stepItem, type) {
      this.changeMenuId('2-1')
      if (this.num == 0 && this.taskInfo.is_show == 1) {
        this.$router.push({ path: '/taskBriefing', query: { id: this.taskInfo.id } })
      } else if (this.num == 2) {
        this.$router.push({
          path: '/alreadyTask_viewlist',
          query: {
            id: this.taskInfo.id,
            task_type: this.taskInfo.task_type,
            status: this.taskInfo.status
          }
        })
      } else if (this.num == 3) {
        if (this.taskInfo.status == 5) {
          let str = ''
          if (this.taskInfo.modify == '0') {
            // 枚举模式
            str = 'enumeration'
          } else {
            // 验证模式
            str = 'verification'
          }
          this.$router.push({
            path: '/domainTaskResult', // 去审核logShenhe
            query: {
              operate_company_id: this.currentCompany,
              id: this.taskInfo.id,
              type: str
            }
          })
        }
      } else if (this.num == 6) {
        this.$router.push({
          path: '/taskRepairLeakScan',
          query: {
            id: this.taskInfo.id,
            task_type: this.taskInfo.task_type,
            status: this.taskInfo.status
          }
        })
      } else if (this.num == 7) {
        if (this.taskInfo.status == 2) {
          this.$router.push({
            path: '/phishingTaskRecordInfo',
            query: { id: this.taskInfo.id, flag: this.taskInfo.expend_flags }
          })
        }
      } else {
        this.$router.push({ path: stepItem.taskPath, query: { activeName: 'second' } })
      }
    },
    format(percentage) {
      return percentage === 100 ? '已完成' : `${percentage}%`
    },
    async handleMessage(res, o) {
      //处理接收到的信息
      if (this.num == 0) {
        // if (this.taskInfo.step == 2) {
        // 单位资产测绘第二步
        if (
          res.cmd == 'detect_assets_tip1_process' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          this.accuracyIndex = 0
          // if(res.data.progress == 100){
          //   if (!this.progressBar) return // 防止websocket多次推送100%，进度条已经隐藏了又出来了
          //   this.currentPercent = 100
          //   this.progressBar = false
          // }else{
          //   this.progressBar = true
          //   // 进度调优显示
          //   this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          // }
        }
        // 完成ip扩展
        if (
          res.cmd == 'detect_assets_tip2_ip' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          this.accuracyIndex = 1
          if (res.data.progress == 100) {
            if (!this.progressBar) return
            this.currentPercent = 100
            setTimeout(() => {
              this.currentPercent = 100
              this.progressBar = false
            }, 1000)
            this.getTaskInfo(0)
          } else {
            this.progressBar = true
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
        }
        // 完成域名精准扩展
        if (
          res.cmd == 'detect_assets_tip2_domain' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          //根域扩展
          this.accuracyIndex = 2
          if (res.data.progress == 100) {
            if (!this.progressBar) return
            this.currentPercent = 100
            setTimeout(() => {
              this.currentPercent = 100
              this.progressBar = false
            }, 1000)
            this.getTaskInfo(0)
          } else {
            this.progressBar = true
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
        }
        // 完成icp精准扩展
        if (
          res.cmd == 'detect_assets_tip2_icp' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          //icp扩展
          this.accuracyIndex = 3
          if (res.data.progress == 100) {
            if (!this.progressBar) return
            this.currentPercent = 100
            setTimeout(() => {
              this.currentPercent = 100
              this.progressBar = false
            }, 1000)
            this.getTaskInfo(0)
          } else {
            this.progressBar = true
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
        }
        if (
          res.cmd == 'detect_assets_tip2_all' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          //极速扩展
          this.accuracyIndex = 4
          if (res.data.progress == 100) {
            if (!this.progressBar) return
            this.currentPercent = 100
            setTimeout(() => {
              this.currentPercent = 100
              this.progressBar = false
            }, 1000)
            if (this.taskInfo.is_intellect_mode == 1) {
              // 智能模式完成，主动触发详情接口，更新步骤
              setTimeout(() => {
                this.getTaskInfo(0)
              }, 10000)
            } else {
              this.getTaskInfo(0)
            }
          } else {
            this.progressBar = true
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
        }
        // if (res.data.progress == 100) {
        //   if (!this.progressBar) return
        //   this.currentPercent = 100
        //   setTimeout(() => {
        //     this.currentPercent = 100
        //     this.progressBar = false
        //   }, 1000)
        //   this.getTaskInfo(0);
        // } else {
        //   this.progressBar = true
        //   this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        // }
        // }
        // if (this.taskInfo.step == 3) {
        if (
          res.cmd == 'detect_direct_operate' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          this.taskInfoTip = res.data.error_msg // 失败原因
        }
        // 第三步
        if (res.cmd == 'recommend_progress' && res.data && this.taskInfo.id == res.data.task_id) {
          //单位资产测绘-云端推荐
          if (res.data.status == 2) {
            if (this.taskInfo.is_intellect_mode == 1) {
              // 智能模式
              this.taskInfoTip = '推荐完成，准备资产评估'
              this.currentPercent = 100
              setTimeout(() => {
                this.getTaskInfo(0)
              }, 3000)
            } else {
              this.currentPercent = 100
              this.getTaskInfo(0)
            }
          } else if (res.data.status == 1) {
            // 正在扫描
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
        }
        // }
        // if (this.taskInfo.step == 4) {
        // 第四步
        if (
          (res.cmd == 'detect_assets_tasks' || res.cmd == 'detect_assets_tasks_cloud') &&
          res.data &&
          res.data.detect_assets_tasks_id == this.taskInfo.id
        ) {
          if (res.data.status == 2) {
            this.currentPercent = 100
            this.getTaskInfo(0)
          } else if (res.data.status == 1) {
            // 正在扫描
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
            this.taskInfoTip =
              this.currentPercent == 0 ? '正在等待信任度评估…' : '正在进行信任度评估…'
            // this.taskInfoTip = this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
          }
        } else if (
          (res.cmd == 'update_assets_level' || res.cmd == 'update_assets_level_cloud') &&
          res.data &&
          this.taskInfo.id == res.data.task_id
        ) {
          if (res.data.status == 2) {
            this.currentPercent = 100

            if (this.taskInfo.is_intellect_mode == 1) {
              // 智能模式
              this.taskInfoTip = '评估完成，等待资产入账扫描'
              setTimeout(() => {
                this.getTaskInfo(0)
              }, 2000)
            } else {
              this.getTaskInfo(0)
            }
          } else if (res.data.status == 1) {
            // 正在扫描
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
            this.taskInfoTip =
              this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
          }
        }
        // }
      }
      if (this.num == 1) {
        // if (this.taskInfo.step == 2) {
        if (
          res.cmd == 'detect_assets_tip1_process' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          this.accuracyIndex = 0
          if (res.data.progress == 100) {
            if (!this.progressBar) return
            this.currentPercent = 100
            setTimeout(() => {
              this.currentPercent = 100
              this.progressBar = false
            }, 1000)
            this.getTaskInfo(1)
          } else {
            this.progressBar = true
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
        }
        // 完成ip扩展
        if (
          res.cmd == 'detect_assets_tip2_ip' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          this.accuracyIndex = 1
          if (res.data.progress == 100) {
            if (!this.progressBar) return
            this.currentPercent = 100
            setTimeout(() => {
              this.currentPercent = 100
              this.progressBar = false
            }, 1000)
            this.getTaskInfo(1)
          } else {
            this.progressBar = true
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
        }
        // 完成域名精准扩展
        if (
          res.cmd == 'detect_assets_tip2_domain' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          //根域扩展
          this.accuracyIndex = 2
          if (res.data.progress == 100) {
            if (!this.progressBar) return
            this.currentPercent = 100
            setTimeout(() => {
              this.currentPercent = 100
              this.progressBar = false
            }, 1000)
            this.getTaskInfo(1)
          } else {
            this.progressBar = true
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
        }

        // }
        // if (this.taskInfo.step == 3) {
        if (
          res.cmd == 'detect_direct_operate' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          this.taskInfoTip = res.data.error_msg // 失败原因
        }
        // 第三步
        if (res.cmd == 'recommend_progress' && res.data && this.taskInfo.id == res.data.task_id) {
          //单位资产测绘-云端推荐

          if (res.data.status == 2) {
            if (this.taskInfo.is_intellect_mode == 1) {
              // 智能模式
              this.taskInfoTip = '推荐完成，准备资产评估'
            } else {
              this.currentPercent = 100
              this.getTaskInfo(1)
            }
          } else if (res.data.status == 1) {
            // 正在扫描
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
          this.taskInfo.step = 2
        }
        // }
        // if (this.taskInfo.step == 4) {
        // 第四步
        if (
          (res.cmd == 'detect_assets_tasks' || res.cmd == 'detect_assets_tasks_cloud') &&
          res.data &&
          res.data.detect_assets_tasks_id == this.taskInfo.id
        ) {
          if (res.data.status == 2) {
            this.currentPercent = 100
            this.getTaskInfo(1)
          } else if (res.data.status == 1) {
            // 正在扫描
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
            this.taskInfoTip =
              this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
            this.taskInfo.step = 3
          }
        } else if (
          (res.cmd == 'update_assets_level' || res.cmd == 'update_assets_level_cloud') &&
          res.data &&
          this.taskInfo.id == res.data.task_id
        ) {
          if (res.data.status == 2) {
            this.currentPercent = 100
            this.getTaskInfo(1)
            if (this.taskInfo.is_intellect_mode == 1) {
              // 智能模式
              this.taskInfoTip = '评估完成，等待资产入账扫描'
            }
          } else if (res.data.status == 1) {
            // 正在扫描
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
            this.taskInfoTip =
              this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
            this.taskInfo.step = 3
          }
        }
        // }
      }
      if (this.num == 2) {
        // if((res.cmd == 'finish_risk_ip_count'||res.cmd == 'type_threat_audit') && res.data && res.data.task_id == this.taskInfo.id){ // 更新风险资产
        //   if(res.data.progress == 100){
        //     this.currentPercent = 100
        //   }else {
        //     // 进度调优显示
        //     this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
        //   }
        // }
        if (res.cmd == 'type_threat_audit') {
          this.getTaskInfo(2)
        }
        if (
          (res.cmd == 'scan_task_progress' || res.cmd == 'scan_task_recpmmand_progress') &&
          res.data.type != 1 &&
          res.data.task_id == this.taskInfo.id
        ) {
          this.runningFunc(res)
        }
      }
      if (this.num == 3) {
        if (res.cmd == 'domain_tasks' && res.data.domain_tasks_id == this.taskInfo.id) {
          this.currentPercent = res.data.progress
          if (res.data.status == 5) {
            this.currentPercent = 100
            this.getTaskInfo(3)
          } else if (res.data.status == 1) {
            if (res.data.progress == 100) {
              // 避免状态为1时进度已经达到100但是页面不提示完成信息，可手动变成99.9，状态为2时正常提示
              res.data.progress = 99.9
            }
            this.currentPercent = res.data.progress
          } else if (res.data.status == 4) {
            // 暂停扫描
            this.currentPercent = res.data.progress
          } else {
            this.getTaskInfo(3)
          }
        }
      }
      if (this.num == 4) {
      }
      if (this.num == 7) {
        // if (this.taskInfo.step == 2) {
        if (
          res.cmd == 'detect_assets_tip1_process' &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          this.accuracyIndex = 0
          if (res.data.progress == 100) {
            this.currentPercent = 100
          } else {
            // 进度调优显示
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
        }
        if (
          (res.cmd == 'detect_assets_tip1_process' || res.cmd == 'detect_assets_tip2_domain') &&
          res.data &&
          res.data.task_id == this.taskInfo.id
        ) {
          this.accuracyIndex = 1
          if (res.data.progress == 100) {
            this.currentPercent = 100
          } else {
            // 进度调优显示
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
          }
        }
        // }
      }
    },
    // websocket执行
    runningFunc(res) {
      if (!('scan_poc_num' in res.data) || res.data.user_id == this.currentCompany) {
        // 漏洞核查与资产、漏洞扫描区分，scan_poc_num存在是漏洞核查任务
        if (res.data.status == 2) {
          if (res.cmd == 'scan_task_progress') {
            resetMessage.success('扫描成功！')
          }
          this.getTaskInfo(2)
        } else if (res.data.status == 1) {
          // 正在扫描
          this.taskStatus = '扫描中'
          this.currentPercent = 100
          if (res.data.progress == 100) {
            // 避免状态为1时进度已经达到100但是页面不提示完成信息，可手动变成99.9，状态为2时正常提示
            res.data.progress = 99.9
          }
          this.currentPercent = res.data.progress
        }
      } else if (res.data.status == 4) {
        // 暂停扫描
        this.currentPercent = res.data.progress
        this.taskStatus = '暂停扫描'
      } else {
        this.getTaskInfo(2)
      }
    },
    navClick(item, index) {
      this.num = index
      this.isFinish = false
      this.stepArr = item.stepArr
      this.stepItem = item
      this.currentStep = 1
      this.taskInfoTip = ''
      this.currentPercent = 0
      this.taskInfo = {}
      this.taskInfo.listData = []
      this.taskDetailType = ''
      clearInterval(this.setTimer)
      this.setTimer = null
      // 点击nav获取不同任务的任务详情
      this.getTaskInfo(index)
    },
    async getTaskInfo(taskType) {
      this.taskLoading = true
      if (taskType == 0) {
        let res = await unitTask({ operate_company_id: this.currentCompany })
        const { code, data } = res

        if (code == 0) {
          if (!data) {
            this.taskLoading = false
            return
          }
          this.taskInfo = data || {}
          this.taskInfo.name = data.name || '---'
          this.taskInfo.listData = data.clue_list
          this.currentPercent = 0

          if (this.taskInfo.is_show == 1) {
            this.taskDetailType = '任务简报'
          } else {
            this.taskDetailType = '测绘记录'
          }
          if (data.step == 5) {
            this.isFinish = true
            this.currentPercent = 100
            this.taskLoading = false
            this.taskInfoTip = `资产入账扫描完成：台账资产：<span class="title-num">${this.taskInfo.sure_ip_num}</span>条；疑似资产：<span class="title-num">${this.taskInfo.unsure_ip_num}</span>条；威胁资产：<span class="title-num">${this.taskInfo.threaten_ip_num}</span>条`
          } else if (data.step == 4) {
            if (this.taskInfo.step_detail == 401) {
              // 入账扫描步骤
              this.currentPercent =
                this.taskInfo.step_status == 0 ? this.taskInfo.progress / 1 : 100
              this.tabIcon = 'two' // 入账扫描
              this.taskInfoTip = `资产入账扫描完成：台账资产：<span class="title-num">${this.taskInfo.konwn_table_num}</span>条，疑似资产：<span class="title-num">${this.taskInfo.unknown_table_num}</span>条，威胁资产：<span class="title-num">${this.taskInfo.threat_table_num}</span>条`
              if (this.taskInfo.step_status == 0) {
                // 正在评估
                this.taskInfoTip =
                  this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
              }
            } else if (this.taskInfo.step_detail == 400) {
              // 资产评估步骤
              this.tabIcon = 'one'
              this.taskInfoTip = `资产信任度评估完成：高可信任资产：<span class="title-num">${this.taskInfo.high}</span>条，中可信任资产：<span class="title-num">${this.taskInfo.middle}</span>条，低可信任资产：<span class="title-num">${this.taskInfo.low}</span>条`
              if (this.taskInfo.is_intellect_mode == 1) {
                // 智能模式
                if (this.taskInfo.is_intellect_failed == 1) {
                  // 代表智能模式入账扫描失败，此时需要手动触发入账扫描
                  this.taskInfoTip = '评估完成，等待资产入账扫描'
                } else {
                  if (this.taskInfo.step_status == 0) {
                    // 正在评估
                    this.taskInfoTip = '正在进行资产评估…'
                    this.currentPercent =
                      this.taskInfo.step_status == 0
                        ? this.taskInfo.update_assets_level_progress / 1
                        : 0
                  } else {
                    this.currentPercent = 100
                    this.taskInfoTip =
                      this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
                  }
                }
              } else {
                if (this.taskInfo.step_status == 0) {
                  // 正在评估
                  this.taskInfoTip = '正在进行资产评估…'
                  this.currentPercent = this.taskInfo.update_assets_level_progress / 1
                } else {
                  this.currentPercent = 100
                }
              }
            }
            this.taskLoading = false
          } else if (data.step == 3) {
            this.currentPercent =
              this.taskInfo.step_status == 0 ? this.taskInfo.expend_progress / 1 : 100
            // this.taskInfo.step_status == 0 ? true : false
            this.taskInfoTip = `成功获取资产数据：<span class="title-num">${data.all_ip_num}</span>条，可以导入第三方数据进行资产融合`
            this.taskLoading = false
            if (this.taskInfo.step_detail == 300 && this.taskInfo.step_status == 0) {
              this.taskInfoTip = '正在推荐资产'
            } else if (this.taskInfo.step_detail == 300 && this.taskInfo.is_intellect_mode == 1) {
              this.taskInfoTip = '推荐完成，准备资产评估'
            }
          } else if (data.step == 2) {
            this.currentPercent = parseFloat(this.taskInfo.clue_progress)
            if (this.taskInfo.is_intellect_mode == 1) {
              this.expandType = '3'
            } else if (this.taskInfo.detect_type == 1) {
              this.expandType = '1'
            } else if (this.taskInfo.detect_type == 2) {
              this.expandType = '2'
            }
            if (this.taskInfo.step_detail == '200') {
              //公司名称扩展
              this.accuracyIndex = 0
              // 0
              // this.taskInfoTip = `成功获取企业初始线索<span class="title-num">${data.step}</span>条，IP段：<span class="title-num">10</span>条，域名：<span class="title-num">10</span>条，ICP：<span class="title-num">10</span>条，证书：<span class="title-num">10</span>条，ICON：<span class="title-num">10</span>条`;
            } else if (this.taskInfo.step_detail == '206') {
              this.accuracyIndex = 1
            } else if (this.taskInfo.step_detail == '201') {
              this.accuracyIndex = 2
            } else if (this.taskInfo.step_detail == '202') {
              this.accuracyIndex = 3
            } else if (this.taskInfo.step_detail == '203' || this.taskInfo.step_detail == '204') {
              this.accuracyIndex = 4
            } else {
              if (this.taskInfo.is_intellect_mode != 1) {
                this.accuracyIndex = 5
              }
            }

            this.getUnitTab(1, 1)
          } else if (data.step == 1) {
            // this.taskInfoTip = `成功发现高危漏洞：<span class="title-num">${data.step}</span>个，中危漏洞：<span class="title-num">10</span>个`;
          }
          this.taskInfo.step = data.step
          // this.currentStep = 5;
        }
      } else if (taskType == 1) {
        let res = await recommendTask({ operate_company_id: this.currentCompany })
        const { code, data } = res

        if (code == 0) {
          this.taskInfo = data || {}
          this.taskInfo.name = this.taskInfo.name || '---'
          this.taskInfo.listData = this.taskInfo.clue_list || []
          this.taskInfo.step = data ? data.step : '1'
          if (data && (data.step == 3 || data.step == 4)) {
            this.taskInfo.step = data.step - 1
          }

          // if(this.taskInfo.step_detail == 401){
          //   this.currentPercent = this.taskInfo.step_status == 0 ? this.taskInfo.progress / 1 : 0
          //   if (this.taskInfo.step_status == 0) { // 正在扫描
          //     this.taskInfoTip = this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
          //   }else{
          //     this.currentPercent = 100
          //   }
          // }
          this.taskStatus = '推荐中'
          if (this.taskInfo.step == 5) {
            this.isFinish = true
            this.currentPercent = 100
            this.taskInfoTip = `资产入账扫描完成：台账资产：<span class="title-num">${this.taskInfo.konwn_table_num}</span>条，疑似资产：<span class="title-num">${this.taskInfo.unknown_table_num}</span>条，威胁资产：<span class="title-num">${this.taskInfo.threat_table_num}</span>条`
          } else if (this.taskInfo.step == 3) {
            if (this.taskInfo.step_detail == 401) {
              // 入账扫描步骤
              this.currentPercent = this.taskInfo.step_status == 0 ? this.taskInfo.progress / 1 : 0
              this.tabIcon = 'two' // 入账扫描
              this.taskInfoTip = `资产入账扫描完成：台账资产：<span class="title-num">${this.taskInfo.konwn_table_num}</span>条，疑似资产：<span class="title-num">${this.taskInfo.unknown_table_num}</span>条，威胁资产：<span class="title-num">${this.taskInfo.threat_table_num}</span>条`
              if (this.taskInfo.step_status == 0) {
                // 正在评估
                this.taskInfoTip =
                  this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
              } else {
                this.currentPercent = 100
              }
            } else if (this.taskInfo.step_detail == 400) {
              // 资产评估步骤
              this.tabIcon = 'one'
              this.taskInfoTip = `资产信任度评估完成：高可信任资产：<span class="title-num">${this.taskInfo.high}</span>条，中可信任资产：<span class="title-num">${this.taskInfo.middle}</span>条，低可信任资产：<span class="title-num">${this.taskInfo.low}</span>条`
              if (this.taskInfo.is_intellect_mode == 1) {
                // 智能模式
                if (this.taskInfo.is_intellect_failed == 1) {
                  // 代表智能模式入账扫描失败，此时需要手动触发入账扫描
                  this.taskInfoTip = '评估完成，等待资产入账扫描'
                } else {
                  if (this.taskInfo.step_status == 0) {
                    // 正在评估
                    this.taskInfoTip = '正在进行资产评估…'
                    this.currentPercent =
                      this.taskInfo.step_status == 0
                        ? this.taskInfo.update_assets_level_progress / 1
                        : 0
                  } else {
                    this.currentPercent = 100
                    this.taskInfoTip =
                      this.currentPercent == 0 ? '正在等待资产入账扫描…' : '正在进行资产入账扫描…'
                  }
                }
              } else {
                if (this.taskInfo.step_status == 0) {
                  // 正在评估
                  this.taskInfoTip = '正在进行资产评估…'
                  this.currentPercent = this.taskInfo.update_assets_level_progress / 1
                } else {
                  this.currentPercent = 100
                }
              }
            }
            this.taskLoading = false
          } else if (this.taskInfo.step == 2) {
            this.currentPercent =
              this.taskInfo.step_status == 0 ? this.taskInfo.expend_progress / 1 : 100
            // this.taskInfo.step_status == 0 ? true : false
            this.taskInfoTip = `成功获取资产数据：<span class="title-num">${data.all_ip_num}</span>条，可以导入第三方数据进行资产融合`
            this.taskLoading = false
            if (this.taskInfo.step_detail == 300 && this.taskInfo.step_status == 0) {
              this.taskInfoTip = '正在推荐资产'
            } else if (this.taskInfo.step_detail == 300 && this.taskInfo.is_intellect_mode == 1) {
              this.taskInfoTip = '推荐完成，准备资产评估'
            }
          }

          this.taskLoading = false
        }
      } else if (taskType == 2) {
        let res = await assetsScanTask({ operate_company_id: this.currentCompany })
        const { code, data } = res
        this.taskInfo = data || {}

        if (code == 0) {
          this.taskStatus = '扫描中'
          this.taskInfo.name = this.taskInfo.name || '---'
          this.taskInfo.listData = this.taskInfo.ips || []
          this.currentPercent = this.taskInfo.progress / 1
          this.taskLoading = false
          this.taskDetailType = '任务详情'
          if (this.taskInfo.status == 0) {
            this.taskStatus = '等待扫描'
          } else if (this.taskInfo.status == 1) {
            this.taskStatus = '扫描中'
          } else if (this.taskInfo.status == 2) {
            this.currentPercent = 100
            this.taskStatus = '扫描完成'
            this.getResult(this.taskInfo, 1)
          } else if (this.taskInfo.status == 3) {
            this.taskStatus = '扫描失败'
          } else if (this.taskInfo.status == 4) {
            this.taskStatus = '暂停扫描'
          }
        }
      } else if (taskType == 3) {
        let res = await domainScanTask({ operate_company_id: this.currentCompany })
        const { code, data } = res
        this.taskInfo = data || {} || {}

        if (code == 0) {
          this.taskStatus = '发现中'

          this.taskInfo.name = this.taskInfo.name || '---'
          // this.taskInfo.step = data.step
          this.taskInfo.listData = this.taskInfo.task_result_domain || []
          if (!this.taskInfo.modify) {
            this.taskInfoTip = `初始域名：<span class="title-num">${this.taskInfo.domain_num}</span>个，爆破层级：<span class="title-num">${this.taskInfo.level}</span>层，结果数量：<span class="title-num">${this.taskInfo.num}</span>个`
          } else {
            this.taskInfoTip = `初始域名：<span class="title-num">${this.taskInfo.domain_num}</span>个，结果数量：<span class="title-num">${this.taskInfo.num}</span>个`
          }
          this.taskLoading = false
          this.currentPercent = parseFloat(this.taskInfo.progress)
          if (this.taskInfo.status == 0) {
            this.taskStatus = '等待扫描'
          } else if (this.taskInfo.status == 1) {
            // this.currentPercent = this.taskInfo.progress
            this.taskStatus = '扫描中'
            clearInterval(this.setTimer)
            this.setTimer = null
            if (parseFloat(this.taskInfo.progress) / 1 < 49) {
              this.setTimer = setInterval(() => {
                domainAssetProgreeUpdate({ id: [this.taskInfo.id] }).then((res) => {
                  if (res.code == 0) {
                    res.data.forEach((item) => {
                      if (item.id == this.taskInfo.id) {
                        this.currentPercent = item.progress
                      }
                    })
                  }
                })
              }, 10000) // 每20秒刷新一次
            }
          } else if (this.taskInfo.status == 5) {
            this.currentPercent = 100
            this.taskDetailType = '查看结果'
            this.taskStatus = '扫描完成'
          } else if (this.taskInfo.status == 3) {
            this.taskStatus = '扫描失败'
          } else if (this.taskInfo.status == 4) {
            this.taskStatus = '暂停扫描'
          }
        }
      } else if (taskType == 4) {
        let res = await assetsCheckTask({ operate_company_id: this.currentCompany })
        const { code, data } = res
        this.taskInfo = data || {}
        if (code == 0) {
          this.taskStatus = '核查中'
          this.taskDetailType = '任务记录'
          this.taskInfo.name = this.taskInfo.name || '---'
          this.taskInfo.listData = this.taskInfo.list || []

          if (this.taskInfo.status) {
            this.taskInfo.step = 3
            if (this.taskInfo.status == 1) {
              this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
              this.getRunningTask('check')
            } else if (this.taskInfo.status == 2) {
              // 已完成
              this.currentPercent = 100
              this.taskInfo.step = 4
              this.taskStatus = '核查完成'
              this.taskInfoTip = `已纳入管理资产<span class="title-num">${this.taskInfo.assets_included}</span>个，未纳入管理资产：<span class="title-num">${this.taskInfo.assets_not_included}</span>个，新增端口资产：<span class="title-num">${this.taskInfo.assets_port}</span>个`
            }
          }
        }
        this.taskLoading = false
      } else if (taskType == 5) {
        let res = await assetsOnlineTask({ operate_company_id: this.currentCompany })
        const { code, data } = res
        this.taskInfo = data || {}

        if (code == 0) {
          this.taskStatus = '检测中'
          this.taskDetailType = '任务记录'
          this.taskInfo.name = this.taskInfo.name || '---'
          this.taskInfo.step = this.taskInfo.progress_state || '1'
          this.taskInfo.listData = this.taskInfo.list || []

          if (this.taskInfo.progress_state) {
            if (this.taskInfo.progress_state == 1) {
              this.taskInfo.step = 2
              this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
              this.getRunningTask('online')
            } else if (this.taskInfo.progress_state == 2) {
              // 已完成
              this.taskInfo.step = 3
              this.currentPercent = 100
              this.taskStatus = '检测完成'
              this.taskInfoTip = `导入资产<span class="title-num">${this.taskInfo.total}</span>个，可访问资产：<span class="title-num">${this.taskInfo.online_assets}</span>个，不可访问资产：<span class="title-num">${this.taskInfo.unonline_assets}</span>个`
            }
            // if (this.taskInfo.status == 2) { // 已完成
            //   this.taskInfo.step = 3
            // }
          }
          this.taskLoading = false
        }
      } else if (taskType == 6) {
        let res = await pocTask({ operate_company_id: this.currentCompany })
        const { code, data } = res
        this.taskInfo = data || {}
        if (code == 0) {
          this.taskStatus = '扫描中'
          this.taskDetailType = '查看详情'
          this.taskInfo.name = this.taskInfo.name || '---'
          // this.taskInfo.step = data.step
          this.taskInfo.listData = this.taskInfo.poc_list || []

          this.taskLoading = false
          if (this.taskInfo.is_audit == 1) {
            if (this.taskInfo.status == 0) {
              this.taskStatus = '等待扫描'
            } else if (this.taskInfo.status == 1) {
              this.taskStatus = '扫描中'
            } else if (this.taskInfo.status == 2) {
              this.taskInfoTip = `发现严重漏洞：<span class="title-num">${this.taskInfo.critical}</span>个，高危漏洞：<span class="title-num">${this.taskInfo.high}</span>个，中危漏洞：<span class="title-num">${this.taskInfo.medium}</span>个，低危漏洞：<span class="title-num">${this.taskInfo.low}</span>个`
              this.currentPercent = 100
              this.taskStatus = '扫描完成'
            } else if (this.taskInfo.status == 3) {
              this.taskStatus = '扫描失败'
            } else if (this.taskInfo.status == 4) {
              this.taskStatus = '暂停扫描'
            }
          } else if (this.taskInfo.is_audit == 0) {
            this.taskStatus = '等待审核'
          } else {
            this.taskStatus = '审核驳回'
          }
        }
      } else if (taskType == 7) {
        let res = await fakeDetectTask({ operate_company_id: this.currentCompany })
        const { code, data } = res
        this.taskInfo = data || {}
        this.navLists[7].stepArr = [
          {
            title: '输入企业名称'
          },
          {
            title: '特征线索获取'
          },
          {
            title: '仿冒数据发现'
          },
          {
            title: '数据存活性检测'
          }
        ]
        if (code == 0) {
          this.taskStatus = '发现中'
          this.taskInfo.name = this.taskInfo.name || '---'
          this.taskInfo.listData = this.taskInfo.online_url || []
          this.taskLoading = false
          if (this.taskInfo.status == 2) {
            this.taskInfo.step = 5
            // if(this.taskInfo.step_detail == 402 && this.taskInfo.status == 2){
            //   this.taskInfo.step = 6
            // }
          }
          if (this.taskInfo.step == 2) {
            let tabActiveNameStatus = 1
            this.currentPercent = parseFloat(this.taskInfo.clue_progress)
            if (this.taskInfo.step_detail == '200') {
              //公司名称获取初始线索
              this.accuracyIndex = 0
              tabActiveNameStatus = 1
            } else if (this.taskInfo.step_detail == '201') {
              // 通过域名扩展完成
              this.accuracyIndex = 1
              tabActiveNameStatus = 0
            } else if (this.taskInfo.step_detail == '202') {
              // 关键词模式选择完毕
              this.accuracyIndex = 2
              tabActiveNameStatus = 0
            } else if (this.taskInfo.step_detail == '204') {
              // 开始生成线索总表
              this.accuracyIndex = 3
              tabActiveNameStatus = 0
            } else {
              this.accuracyIndex = 4
              tabActiveNameStatus = 1
            }
            this.getUnitTab(tabActiveNameStatus, 0)
          } else if (
            this.taskInfo.step == 3 ||
            this.taskInfo.step == 4 ||
            this.taskInfo.step == 5
          ) {
            clearInterval(this.setTimer)
            if (this.taskInfo.step_detail == 300) {
              this.currentPercent =
                this.taskInfo.step_status == 0 ? this.taskInfo.expend_progress / 1 : 100
              if (this.taskInfo.step_status == 0) {
                // 正在发现
                this.taskInfoTip = '正在进行仿冒数据发现…'
              } else {
                this.getFakeTaskInfo2()
              }
            } else if (this.taskInfo.step_detail == 301) {
              this.currentPercent =
                this.taskInfo.step_status == 0 ? this.taskInfo.expend_progress / 1 : 100
              if (this.taskInfo.step_status == 0) {
                // 正在发现
                this.taskInfoTip = '正在导入数据…'
              } else {
                this.taskInfoTip = '仿冒数据发现'
              }
            } else if (this.taskInfo.step_detail == 400) {
              this.currentPercent =
                this.taskInfo.step_status == 0 ? this.taskInfo.progress / 1 : 100
              if (this.taskInfo.step_status == 0) {
                // 正在发现
                this.taskInfoTip = '正在进行数据存活性探测…'
                clearInterval(this.setTimer)
                this.setTimer = null
                this.setTimer = setInterval(() => {
                  this.getFakeTaskInfo()
                }, 5000)
              } else {
                this.taskInfoTip = `获取仿冒数据：<span class="title-num">${this.taskInfo.fake_num}</span>条`
              }
            } else if (this.taskInfo.step_detail == 401) {
              this.navLists[7].stepArr = [
                {
                  title: '输入企业名称'
                },
                {
                  title: '特征线索获取'
                },
                {
                  title: '仿冒数据发现'
                },
                {
                  title: '数据存活性检测'
                },
                {
                  title: '深度探测'
                }
              ]
              this.taskInfo.step = 5
              this.currentPercent =
                this.taskInfo.step_status == 0 ? this.taskInfo.progress / 1 : 100
              if (this.taskInfo.step_status == 0) {
                // 正在发现
                this.taskInfoTip = '正在进行入账威胁并深度探测…'
                clearInterval(this.setTimer)
                this.setTimer = null
                this.setTimer = setInterval(() => {
                  this.getFakeTaskInfo()
                }, 5000)
              } else {
                this.taskInfoTip = `获取仿冒数据：<span class="title-num">${this.taskInfo.fake_num}</span>条`
              }
            } else if (this.taskInfo.step_detail == 402) {
              this.navLists[7].stepArr = [
                {
                  title: '输入企业名称'
                },
                {
                  title: '特征线索获取'
                },
                {
                  title: '仿冒数据发现'
                },
                {
                  title: '数据存活性检测'
                },
                {
                  title: '深度探测'
                }
              ]
              this.taskInfo.step = 5
              this.currentPercent =
                this.taskInfo.step_status == 0 ? this.taskInfo.progress / 1 : 100
              if (this.taskInfo.step_status == 0) {
                // 正在发现
                this.taskInfoTip = '正在进行入账威胁并深度探测…'
                clearInterval(this.setTimer)
                this.setTimer = null
                this.setTimer = setInterval(() => {
                  this.getFakeTaskInfo()
                }, 5000)
              } else {
                this.taskInfoTip = `获取仿冒数据：<span class="title-num">${this.taskInfo.fake_num}</span>条`
              }
            } else {
              this.taskInfoTip = '数据存活性检测'
            }
          }
          if (this.taskInfo.status == 2) {
            this.taskDetailType = '发现结果'
            this.taskInfo.step = 5
            this.currentPercent = 100
            if (this.taskInfo.step_detail == 402) {
              this.taskInfo.step = 6
            }
          }
        }
      }
    },
    getResult(row, task_type) {
      // asset_num、rule_num都不存在或者都等于0，显示【未发现网络资产】
      // threat_num不存在或者都等于0，显示【未发现漏洞】
      let str = ''
      if (row.status == 2 || row.status == 3) {
        // 任务扫描成功或失败才显示结果
        if (task_type == 1) {
          // 资产
          let strSp = row.asset_num && row.asset_num / 1 > 0 ? ',' : ''
          if (row.asset_num && row.asset_num / 1 > 0) {
            // str += '发现IP：' + row.asset_num
            str += `发现IP：<span class="title-num">${row.asset_num}</span>`
          }
          if (row.rule_num && row.rule_num / 1 > 0) {
            // str += strSp + '发现组件：' + row.rule_num
            str += `${strSp} 发现组件：<span class="title-num">${row.rule_num}</span>`
          }
          if (
            (!row.asset_num || row.asset_num / 1 == 0) &&
            (!row.rule_num || row.rule_num / 1 == 0)
          ) {
            str = '未发现网络资产'
          }
        } else {
          // 漏洞
          if (row.threat_num && row.threat_num / 1 > 0) {
            // str = '发现漏洞：' + row.threat_num
            str += `发现漏洞：<span class="title-num">${row.threat_num}</span>`
          } else {
            str = '未发现漏洞'
          }
        }
      } else {
        str = '-'
      }
      this.taskInfoTip = str
      // return str
    },
    async getFakeTaskInfo2() {
      // 获取仿冒数据量
      let res1 = await fakeTabCount({
        task_id: this.taskInfo.id,
        operate_company_id: this.currentCompany
      })
      let domain_total = 0
      let fake_total = 0
      if (res1.code == 0) {
        domain_total = res1.data && res1.data.domain_total ? res1.data.domain_total : 0
        fake_total = res1.data && res1.data.fake_total ? res1.data.fake_total : 0
        let totalTabNum = domain_total + fake_total
        this.taskInfoTip = `获取仿冒数据：<span class="title-num">${totalTabNum}</span>条`
      }
    },
    async getFakeTaskInfo() {
      // 获取任务详情数据，任务记录id存在，查询对应的详情数据；不存在后端直接查询未完成的任务数据，有返回值则展示，没有返回值新建流程
      let id = this.taskInfo.id ? this.taskInfo.id : ''
      let obj = {
        taskId: id,
        data: {
          operate_company_id: this.currentCompany
        }
      }
      this.loading = true
      let res = await fakeTaskInfo(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        if (res.data.step_status == 1) {
          this.currentPercent = 100 // 仿冒数据发现的进度
          clearInterval(this.setTimer)
          this.setTimer = null
        } else {
          if (this.taskInfo.step_detail == 400) {
            this.currentPercent = res.data.progress / 1 // 探活的进度
          } else {
            this.currentPercent = res.data.expend_progress / 1 // 仿冒数据发现的进度
          }
        }
      }
    },
    async getRunningTask(type = 'check') {
      let obj = {
        task_id: this.taskInfo.id,
        operate_company_id: this.currentCompany
      }
      let funcName = ''
      funcName = type == 'check' ? auditTaskInfo : statusDetectTaskInfo
      let res = await funcName(obj)
      if (res.code == 0) {
        if (res.data) {
          if (res.data.status == 1) {
            // 正在进行
            this.currentPercent = this.setProgress(this.currentPercent, res.data.progress)
            clearTimeout(this.intervalTimer)
            this.intervalTimer = null
            this.intervalTimer = setTimeout(() => {
              // 任务未完成，定时请求接口
              this.getRunningTask(type)
            }, 3000)
          } else if (res.data.status == 2) {
            // 已完成
            clearTimeout(this.intervalTimer)
            this.intervalTimer = null
            this.currentPercent = 100
          }
        }
      } else {
        clearTimeout(this.intervalTimer)
        this.intervalTimer = null
      }
    },
    // async getfakeModelList() {
    //   let res = await fakeModelList({
    //     fake_detect_task_id: this.taskInfo.id,
    //     operate_company_id: this.currentCompany
    //   })
    //   if (res.code == 0) {
    //     let main_keyword_num =
    //       res.data.main_keyword && res.data.main_keyword.length ? res.data.main_keyword.length : 0
    //     let node_keyword_num =
    //       res.data.node_keyword && res.data.node_keyword.length ? res.data.node_keyword.length : 0
    //     this.taskInfoTip = `${this.fakeStepInfoArr[this.accuracyIndex].clueText}<span class="title-num">${main_keyword_num + node_keyword_num}</span>条，主体词：<span class="title-num">${main_keyword_num}</span>条，辅助词：<span class="title-num">${node_keyword_num}</span>条`
    //   }
    // },
    async getUnitTab(val, type) {
      // type 0代表钓鱼仿冒 1代表单位资产测绘
      let obj = {
        group_id: this.taskInfo.group_id,
        data: {
          status: val, // 线索总表和获取初始线索是1，其余为0
          operate_company_id: this.currentCompany,
          detect_task_id: this.taskInfo.id,
          fake_detect_task_id: this.taskInfo.id
        }
      }
      if (type == 0) {
        obj = {
          group_id: this.taskInfo.group_id,
          data: {
            status: val, // 线索总表和获取初始线索是1，其余为0
            operate_company_id: this.currentCompany,
            fake_detect_task_id: this.taskInfo.id
          }
        }
      } else if (type == 1) {
        obj = {
          group_id: this.taskInfo.group_id,
          data: {
            status: val, // 线索总表和获取初始线索是1，其余为0
            operate_company_id: this.currentCompany,
            detect_task_id: this.taskInfo.id
          }
        }
      }
      let res = await tabNumClues(obj)
      let tabTotal = 0
      let domain_num = 0
      let cert_num = 0
      let icp_num = 0
      let icon_num = 0
      let ip_num = 0
      let keyword_num = 0
      let fid_num = 0
      if (res.data.clues_count.length != 0) {
        res.data.clues_count.forEach((val) => {
          if (val.type == '0') {
            domain_num = val.count
          }
          if (val.type == '1') {
            cert_num = val.count
          }
          if (val.type == '2') {
            icp_num = val.count
          }
          if (val.type == '3') {
            icon_num = val.count
          }
          if (val.type == '4') {
            keyword_num = val.count
          }
          if (val.type == '6') {
            ip_num = val.count
          }
          if (val.type == '10') {
            fid_num = val.count
          }
        })
        if (this.num == 0) {
          if (this.accuracyIndex == 0) {
            //公司
            tabTotal = ip_num + domain_num + cert_num + icp_num + icon_num
            this.taskInfoTip = `${this.stepInfoArr[this.accuracyIndex].clueText}<span class="title-num">${tabTotal}</span>条，IP段：<span class="title-num">${ip_num}</span>条，域名：<span class="title-num">${domain_num}</span>条，ICP：<span class="title-num">${icp_num}</span>条，证书：<span class="title-num">${cert_num}</span>条，ICON：<span class="title-num">${icon_num}</span>条`
          } else if (this.accuracyIndex == 1) {
            //IP段
            tabTotal = domain_num + cert_num + icp_num + icon_num
            this.taskInfoTip = `${this.stepInfoArr[this.accuracyIndex].clueText}<span class="title-num">${tabTotal}</span>条，域名：<span class="title-num">${domain_num}</span>条，ICP：<span class="title-num">${icp_num}</span>条，证书：<span class="title-num">${cert_num}</span>条，ICON：<span class="title-num">${icon_num}</span>条`
          } else if (this.accuracyIndex == 2) {
            //域名
            tabTotal = ip_num + cert_num + icp_num + icon_num
            this.taskInfoTip = `${this.stepInfoArr[this.accuracyIndex].clueText}<span class="title-num">${tabTotal}</span>条，IP段：<span class="title-num">${ip_num}</span>条，ICP：<span class="title-num">${icp_num}</span>条，证书：<span class="title-num">${cert_num}</span>条，ICON：<span class="title-num">${icon_num}</span>条`
          } else if (this.accuracyIndex == 3) {
            //icp
            tabTotal = ip_num + cert_num + domain_num + icon_num
            this.taskInfoTip = `${this.stepInfoArr[this.accuracyIndex].clueText}<span class="title-num">${tabTotal}</span>条，IP段：<span class="title-num">${ip_num}</span>条，域名：<span class="title-num">${domain_num}</span>条，证书：<span class="title-num">${cert_num}</span>条，ICON：<span class="title-num">${icon_num}</span>条`
          } else if (this.accuracyIndex == 4) {
            //证书
            if (this.expandType == '1') {
              tabTotal = ip_num + domain_num + cert_num + icp_num + icon_num
              this.taskInfoTip = `${this.stepInfoArr[this.accuracyIndex].clueText}<span class="title-num">${tabTotal}</span>条，IP段：<span class="title-num">${ip_num}</span>条，域名：<span class="title-num">${domain_num}</span>条，ICP：<span class="title-num">${icp_num}</span>条，证书：<span class="title-num">${cert_num}</span>条，ICON：<span class="title-num">${icon_num}</span>条`
            } else {
              tabTotal = ip_num + domain_num + icp_num + icon_num
              this.taskInfoTip = `${this.stepInfoArr[this.accuracyIndex].clueText}<span class="title-num">${tabTotal}</span>条，IP段：<span class="title-num">${ip_num}</span>条，域名：<span class="title-num">${domain_num}</span>条，ICP：<span class="title-num">${icp_num}</span>条，ICON：<span class="title-num">${icon_num}</span>条`
            }
          } else {
            tabTotal = ip_num + domain_num + cert_num + icp_num + icon_num
            this.taskInfoTip = `${this.stepInfoArr[this.accuracyIndex].clueText}<span class="title-num">${tabTotal}</span>条，IP段：<span class="title-num">${ip_num}</span>条，域名：<span class="title-num">${domain_num}</span>条，ICP：<span class="title-num">${icp_num}</span>条，证书：<span class="title-num">${cert_num}</span>条，ICON：<span class="title-num">${icon_num}</span>条`
          }
          if (this.taskInfo.step_detail == 204 && this.taskInfo.step_status == 0) {
            this.taskInfoTip = '正在扩展线索...'
          } else if (this.taskInfo.step_detail == 204 && this.taskInfo.is_intellect_mode == 1) {
            this.taskInfoTip = '已生成线索总表，准备云端推荐'
          }
        } else if (this.num == 7) {
          tabTotal = domain_num + keyword_num + icon_num + fid_num
          this.taskInfoTip = `${this.fakeStepInfoArr[this.accuracyIndex].clueText}<span class="title-num">${tabTotal}</span>条，域名：<span class="title-num">${domain_num}</span>条，ICON：<span class="title-num">${icon_num}</span>条，关键字：<span class="title-num">${keyword_num}</span>条，FID：<span class="title-num">${fid_num}</span>条`
        }
      }

      this.taskLoading = false
    }
    // async handleMessage(res, o) {
    //   //处理接收到的信息
    //   // 根据公司名称获取初始线索
    //   if (
    //     res.cmd == "detect_assets_tip1_process" &&
    //     res.data &&
    //     res.data.task_id == this.taskInfo.id
    //   ) {
    //   }
    // },
  },
  beforeDestroy() {
    clearInterval(this.setTimer)
    this.setTimer = null
  }
}
</script>

<style lang="less" scoped>
.conTask {
  width: calc(100% - 40px);
  // height: 434px;
  margin: 0 auto;
  border-radius: 4px;
  border: 2px solid #ffffff;
  box-shadow:
    0px 4px 8px 0px rgba(0, 0, 0, 0.06),
    inset 0px 0px 80px 0px rgba(2, 21, 55, 0.04);
  margin-top: 20px;
  .con-nav {
    height: 129px;
    width: 100%;
    background: linear-gradient(180deg, #ffffff 0%, #e3eeff 100%);
    .headerEchart {
      position: relative;
      widows: 100%;
      height: 47px;
      border-bottom: 1px solid #e9ebef;
      .headerEchart-top {
        margin-left: 1.2%;
        //   margin-top: 10px;
        margin-right: 1.2%;
        height: 47px;
        line-height: 47px;
        //   display: flex;
        //   justify-content: space-between;
        span {
          display: inline-block;
          // width: 90%;
          // font-family: PingFangSC-Semibold;
        }
        .top-tit {
          vertical-align: middle;
          line-height: 47px;
          font-size: 16px;
          color: #37393c;
          font-weight: 600;
        }
        .setting {
          display: inline-block;
          // float: right;
          width: 9%;
          text-align: end;
          // font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #62666c;
        }
        img {
          vertical-align: middle;
          margin-left: 8px;
        }
      }
    }
    .headerEchart-top::before {
      content: '';
      position: absolute;
      left: 0px;
      top: 18px;
      width: 4px;
      height: 16px;
      background-color: #2677ff;
      border-radius: 0 2px 2px 0;
    }
    .task-nav {
      margin-top: 24px;
      height: 84px;
      display: flex;
      .el-dropdown-link {
        color: #62666c;
      }
      .el-icon-arrow-down {
        font-size: 12px;
      }
      .demonstration {
        display: block;
        color: #8492a6;
        font-size: 14px;
        margin-bottom: 20px;
      }
      .el-col {
        width: 10% !important;
        height: 40px;
        border: 1px solid #d8e1f3;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 4px;
        text-align: center;
        line-height: 40px;
        margin-left: 1.2%;
        cursor: pointer;
        position: relative;
      }
      .el-col:nth-child(1) {
        margin-left: 1.9%;
      }
      .el-active {
        background: linear-gradient(180deg, #f4f8ff 0%, #e1ecff 100%);
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
        border-radius: 4px;
        border: 1px solid #2677ff;

        .el-dropdown-link {
          color: #2677ff;
          font-weight: 600;
        }
      }
    }
    .arrow-top {
      .top {
        width: 1.7%;
        height: 20px;
        position: absolute;
        left: 42%;
        top: 35px;
        z-index: 2;
        /*兼容ie8-*/
      }
      .top-o {
        left: 17%;
      }
      // .top-t{
      //   left: ;
      // }

      .top-arrow1,
      .top-arrow2 {
        width: 0;
        height: 0;
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 5;
        /*兼容ie8-*/
        border-top: 10px transparent dashed;
        border-left: 10px transparent dashed;
        border-right: 10px transparent dashed;
        border-bottom: 10px white solid;
        overflow: hidden;
      }

      .top-arrow1 {
        border-bottom: 10px #e3eeff solid;
      }

      .top-arrow2 {
        top: 3px;
        /*重要*/
        border-bottom: 10px white solid;
      }
    }
  }
  .task-content {
    // height: 305px;
    height: auto;
    width: 100%;
    background-color: #fff;
    position: relative;

    .task-bottom {
      width: 100%;
      height: 180px;
      background: url(../../assets/images/taskBackground.png) no-repeat;
      background-position: right;
      .task-box {
        position: relative;
        //   padding-right: 50px;
        width: 100%;
        height: 170px;
        overflow: hidden;
        display: flex;
        align-items: center;
        z-index: 10;
        .left {
          width: 120px;
          height: 120px;
          margin-left: 30px;
          margin-top: 10px;
        }
        .tu {
          // margin-top: 36px;
          background-image: url(../../assets/images/dongtu1.png);
          width: 120px !important;
          height: 120px !important;
          background-repeat: no-repeat;
          background-size: 100%;
          background-position-y: 0px;
          animation: running 6s steps(149) infinite;
          -webkit-animation: running 6s steps(149) infinite;
          // display: inline-block;
        }
        .tuFinish {
          width: 120px !important;
          height: 120px !important;
          background-repeat: no-repeat;
          background-size: 100%;
          background-position-y: 0px;
          background-image: url('../../assets/images/tuFinish.png');
        }
        @keyframes running {
          0% {
            background-position-y: 0px;
          }
          100% {
            background-position-y: -17880px;
          }
        }
        .task-title {
          width: 65%;
          // margin-top: 60px;
          padding: 60px 0;
          margin-left: 20px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          .title-top {
            line-height: 28px;
            display: flex;
            align-items: center;
            .company-name {
              max-width: 252px;
              color: #2677ff;
              // font-family: PingFang SC;
              font-size: 18px;
              font-weight: 600;
              line-height: 28px;
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
            }
            .statu {
              display: inline-block;
              margin-left: 0.3%;
              color: #37393c;
              // font-family: PingFang SC;
              font-size: 18px;
              line-height: 28px;
              margin-right: 0.7%;
            }
          }
        }
        .task-button {
          position: absolute;
          // margin-top: 73px;
          // margin-right: 50px;
          right: 50px;
          top: 0;
          bottom: 0;
          margin: auto;
          text-align: right;
          width: 188px;
          height: 36px;
          .button {
            width: 88px;
            height: 36px;
            display: inline-block;
            text-align: center;
            line-height: 36px;
            // font-family: PingFang SC;
            border-radius: 4px;
            &:hover {
              cursor: pointer;
            }
          }
          .button-one {
            background: linear-gradient(90deg, #26b3ff 0%, #2677ff 100%);
            color: #ffffff;
            margin-right: 5%;
          }
          .button-two {
            border: 1px solid #2677ff;
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(4px);
            color: #2677ff;
          }
        }
      }
      /deep/ .el-progress-bar {
        padding: 0 !important;
      }
      /deep/ .el-progress-bar__innerText {
        position: relative;
        top: -20px;
        color: #2677ff !important;
        z-index: 100;
        margin-bottom: 6px;
        // font-family: PingFang SC;
        font-weight: 500;
      }
      /deep/ .el-progress-bar__outer {
        overflow: visible;
      }
      .progress {
        position: absolute;
        bottom: 120px;
        // width: calc(100% - 32px);
        width: 100%;
        height: 4px;
        .progressFinish {
          position: relative;
          height: 4px;
          background: #10d595 !important;
          .progressFinish_innerText {
            position: absolute;
            right: 5px;
            top: -28px;
            width: 58px;
            height: 24px;
            color: #10d595;
            line-height: 24px;
            font-weight: 600;
            text-align: center;
            border-radius: 2px;
            background-color: rgba(255, 255, 255, 0.7);
            z-index: 999;
          }
        }

        /deep/.el-progress {
          height: 4px;
          line-height: 4px;
          .el-progress-bar {
            height: 4px;
            display: unset;
            .el-progress-bar__outer {
              height: 4px !important;
              overflow: unset;
              border-radius: 0;
              background-color: rgba(186, 196, 217, 0.5) !important;
              .el-progress-bar__inner {
                border-radius: 0;
                background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);

                .el-progress-bar__innerText {
                  position: absolute;
                  bottom: 8px;
                  right: 0;
                  left: auto;
                  z-index: 999;
                  transform: translateX(50%);
                  line-height: 20px;
                  font-size: 14px;
                  font-weight: 600;
                  // color: #2677FF !important;
                  width: 58px;
                  height: 24px;
                  line-height: 24px;
                  background: rgba(255, 255, 255, 0.7);
                  text-align: center;
                  border-radius: 2px;
                  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
                }
              }
            }
          }
        }
        &.start {
          /deep/ .el-progress-bar__innerText {
            left: 0 !important;
            right: auto !important;
            transform: translateX(0) !important;
          }
        }
        &.end {
          /deep/ .el-progress-bar__innerText {
            right: 0 !important;
            left: auto !important;
            transform: translateX(0) !important;
          }
        }
        &.finish {
          /deep/.el-progress-bar__inner {
            background: #10d595 !important;

            //.el-progress-bar__innerText {
            // color: #10D595 !important;
            //}
          }
        }
      }
    }
    .task-detail {
      padding-left: 50px;
      padding-right: 52px;
      // width: 100%;
      height: 121px;
      background: #f5f8fc;
      overflow: hidden;
      .font {
        // font-family: PingFang SC;
        font-size: 14px;
        height: 20px;
        line-height: 20px;
        color: #62666c;
      }
      .detail-left {
        color: #62666c;
        margin-right: 4px;
        display: inline-block;
      }
      .detail-right {
        color: #37393c;
      }
      .task-name {
        display: flex;
        width: 100%;

        // width: calc(100% - 102px);
        height: 60px;
        border-bottom: 1px solid #e3e5ea;
        line-height: 60px;

        div {
          width: 50%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        // .name {
        //   margin-right: 143px;
        // }
      }
      .task-clue {
        display: flex;
        height: 60px;
        line-height: 60px;
        color: #62666c;

        .clue-center {
          position: relative;
          flex: 1;
          width: 0;
          overflow-x: auto;
          overflow-y: hidden;
          height: 60px;
          white-space: nowrap;

          .clue-container {
            width: 100%;
            // position: absolute;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .more {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto;
          }
        }
        .clue-right {
          width: 60px;
        }
      }
    }
  }
}
.title-bottom {
  margin-top: 12px;
  img {
    vertical-align: middle;
    margin-right: 0.6%;
  }
  .title-content {
    // font-family: PingFang SC;
    color: #62666c;
    font-size: 14px;
    line-height: 20px;
  }
}
/deep/.title-num {
  color: #2677ff;
  // font-family: PingFang SC;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  display: inline-block;
  margin-right: 4px;
}
.number-last {
  margin-left: 4px;
}
.clueBlock {
  box-sizing: border-box;
  display: inline-block;
  line-height: 22px;
  height: 28px;
  padding: 2px 12px 2px 3px;
  margin-right: 12px;
  margin-top: 12px;
  color: #37393c;
  border-radius: 14px;
  border: 1px solid #cfdaeb;
  background: rgba(255, 255, 255, 0.5);

  .clueType {
    display: inline-block;
    color: #2677ff;
    height: 100%;
    // height: calc(100% - 4px);
    margin-right: 4px;
    padding: 0 8px;
    border-radius: 10px;
    background: rgba(38, 119, 255, 0.2);
  }
}
.clueContent {
  margin-left: 10px;
}
.task-title-top {
  color: #2677ff;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 500;
  img {
    width: 22px;
    height: 20px;
  }
}
.task-title-middle {
  margin-bottom: 28px;
}
.task-title-bottom {
  cursor: pointer;
  width: 103px;
  height: 36px;
  line-height: 36px;
  border-radius: 4px;
  text-align: center;
  color: #ffffff;
  background: linear-gradient(90deg, #26b3ff 0%, #2677ff 100%);
}
</style>
