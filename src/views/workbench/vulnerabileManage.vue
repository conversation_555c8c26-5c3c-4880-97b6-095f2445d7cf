<template>
  <div class="conVulner" v-loading="loading">
    <div class="top-title">
      <div class="headerEcharts">
        <div class="conAssets-top">
          <span class="con-title" style="cursor: text">漏洞管理</span>
          <img src="../../assets/images/arrowRight.png" alt="" />
        </div>
        <el-badge
          :value="titleList.un_repair_num"
          class="badge"
          v-if="titleList.un_repair_num != 0"
        >
          <span class="goHandle" @click="handleDoing()"
            >去处理<span class="el-icon-d-arrow-right"></span
          ></span>
        </el-badge>
        <span class="goHandle" @click="handleDoing()" v-else
          >去处理<span class="el-icon-d-arrow-right"></span
        ></span>
      </div>
    </div>
    <div class="vulner-types">
      <div
        class="type-li"
        v-for="(typeItem, index) of types"
        :key="index"
        @click="
          typeActive = index
          changeList(typeItem)
        "
        :class="{ 'type-active': index == typeActive }"
      >
        <span class="type-title">{{ typeItem.title }}</span>
        <span class="type-num">{{ titleList[typeItem.word] ? titleList[typeItem.word] : 0 }}</span>
      </div>
    </div>
    <div class="total">
      <div class="total-left">
        <div class="vulner-title">严重漏洞排行TOP5</div>
        <div v-if="vulnersList" class="componentClass" v-loading="loading">
          <div class="componentClass-box">
            <div
              class="list-compo"
              v-for="(item, index) in vulnersList"
              :key="index"
              @click="num = index"
              :class="{ active: num == index }"
            >
              <div class="top">
                <div>
                  <span :class="'indexClass' + index">{{ index + 1 }}</span>

                  <i class="numClass">{{
                    item.vulnerability
                      ? item.vulnerability.length > 28
                        ? item.vulnerability.slice(0, 26) + '...'
                        : item.vulnerability
                      : '-'
                  }}</i>
                </div>
                <span class="numClass">{{ item.count ? item.count : 0 }}</span>
              </div>

              <el-progress
                :percentage="(item.count / totalNum) * 100"
                :show-text="false"
                :color="colorArr[index]"
              ></el-progress>
            </div>
          </div>
          <div class="componentClass-content">
            <div
              class="componentClass-ips"
              :class="{ 'list-top': num == 0, 'list-last': num == 4 }"
              v-if="vulnersList[num]"
            >
              <div
                class="ip-list"
                v-for="(ipItem, n) in vulnersList[num].ips ? vulnersList[num].ips : []"
                :key="n"
              >
                <el-tooltip class="item" effect="dark" placement="top">
                  <span slot="content">
                    {{ ipItem[0] ? ipItem[0] : '-----' }}
                  </span>
                  <span class="ip" @click="jumpPageByFilter({ url: ipItem[0] })"
                    >{{ ipItem[0] ? ipItem[0] : '-----' }}
                  </span>
                </el-tooltip>
                <span class="status" :class="{ 'status-red': ipItem[1] == '未修复' }">{{
                  ipItem[1] ? ipItem[1] : '---'
                }}</span>
                <span class="time">{{ ipItem[2] ? ipItem[2] : '-----' }}</span>
              </div>
            </div>
            <div v-else class="emptyClass">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-kong"></use>
              </svg>
              <p>暂无数据</p>
            </div>
            <div
              class="moreModule"
              @click="jumpPageByFilter({ vulnerability: vulnersList[num].vulnerability || '-' })"
            >
              查看更多
              <svg class="icon svg-icon" aria-hidden="true">
                <use xlink:href="#icon-more-right"></use>
              </svg>
              <svg class="icon svg-icon" aria-hidden="true">
                <use xlink:href="#icon-more-right"></use>
              </svg>
            </div>
          </div>
        </div>
        <div v-else class="emptyClass" v-loading="loading">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-kong"></use>
          </svg>
          <p>暂无数据</p>
        </div>
      </div>
      <div class="total-right" v-loading="loading" :key="currentCompany + activeItem">
        <div class="right-title">漏洞风险等级统计</div>
        <template v-if="titleList[activeItem] != 0">
          <div class="right-picture" id="cirChart"> </div>
        </template>
        <template v-else>
          <div class="emptyClass" v-loading="loading">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-kong"></use>
            </svg>
            <p>暂无数据</p>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState, mapGetters } from 'vuex'
import { pocworkList } from '@/api/apiConfig/api.js'

export default {
  data() {
    return {
      activeItem: 'all_num',
      types: [
        {
          title: '全部漏洞',
          word: 'all_num'
        },
        {
          title: '未修复漏洞',
          word: 'un_repair_num'
        },
        {
          title: '已修复漏洞',
          word: 'repair_num'
        }
      ],
      typeActive: 0,
      totalNum: 200,
      titleList: {},
      vulnersList: {},
      ips: [],
      colorArr: [
        'linear-gradient(90deg, #FF7A7A 0%, #FF4646 100%)',
        'linear-gradient(90deg, #FFAE66 0%, #FF7900 100%)',
        'linear-gradient(90deg, #FFAE66 0%, #FF7900 100%)',
        'linear-gradient(90deg, #4EAFFF 0%, #2677FF 100%)',
        'linear-gradient(90deg, #4EAFFF 0%, #2677FF 100%)'
      ],
      num: 0,
      loading: false,
      echartList: {},
      userInfo: {},
      user: {
        role: ''
      }
    }
  },

  async mounted() {
    if (this.user.role == 2 && !this.currentCompany) return
    await this.getData()
  },

  methods: {
    ...mapMutations(['changeMenuId']),
    jumpPageByFilter(query) {
      this.$emit('scrollToBottom')
      this.$router.push({
        path: '/repairLeakScan',
        query: { ...query, flag: this.activeItem == 'repair_num' ? '1' : '0' }
      })
      sessionStorage.setItem('menuId', '3-1-2')
      this.changeMenuId('3-1-2')
    },
    async getData() {
      // 漏洞管理
      this.loading = true
      this.vulnersList = null
      let obj = {
        operate_company_id: this.currentCompany,
        state: this.typeActive
      }
      let res = await pocworkList(obj).catch(() => {
        this.loading = false
      })
      if (res.code == 0) {
        this.loading = false
        this.vulnersList = res.data.statistic
        this.titleList = res.data.count
        this.echartList = res.data.chart
        this.useData()
        if (this.titleList[this.activeItem] != 0) {
          this.$nextTick(() => {
            this.makeEchart()
          })
        }
      }
    },
    changeList(item) {
      this.getData()
      this.num = 0
      this.activeItem = item.word
    },
    handleDoing() {
      sessionStorage.setItem('menuId', '3-1-2')
      this.changeMenuId('3-1-2')
      this.$router.push('/repairLeakScan')
    },
    // 处理ips数据
    useData() {
      for (var i = 0; i < this.vulnersList.length; i++) {
        if (this.vulnersList[i].ips) {
          for (var j = 0; j < this.vulnersList[i].ips.length; j++) {
            if (this.vulnersList[i].ips[j]) {
              this.vulnersList[i].ips[j] = this.vulnersList[i].ips[j].split('#')
            }
          }
        }
      }
    },
    makeEchart() {
      this.cirChart = this.$echarts.init(document.getElementById('cirChart'))
      if (!this.cirChart) return
      let options = {
        // left:'center',
        // top:'center',
        legend: {
          type: 'plain',
          orient: 'horizontal',
          right: '10%',
          // top: 'bottom',
          bottom: 15,
          itemGap: 20, // 上下间距
          itemWidth: 16, // 宽度
          itemHeight: 10 // 高度
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            labelLine: {
              normal: {
                show: false
              },
              emphasis: {
                show: true
              },
              tooltip: {
                show: false
              }
            },
            label: {
              normal: {
                show: false,
                position: 'center',
                formatter: '{value|{d}%}\n{value|{b}}\n{label|{c}}',
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 16,
                    textBorderWidth: 0,
                    color: 'rgb(98, 102, 108)'
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 14,
                    textBorderWidth: 0,
                    color: 'rgb(98, 102, 108)'
                  }
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '12'
                }
              }
            },
            // emphasis: {
            //   label: {
            //     show: true,
            //     fontSize: 40,
            //     fontWeight: 'bold'
            //   }
            // },
            color: ['#990504', '#FF4646', '#FF7900', '#FFC226'],
            data: [
              {
                color: '#990504',
                value: this.echartList.critical,
                name: '严重'
              },
              {
                color: '#FF4646',
                value: this.echartList.high,
                name: '高危'
              },
              {
                color: '#FF7900',
                value: this.echartList.medium,
                name: '中危'
              },
              {
                color: '#FFC226',
                value: this.echartList.low,
                name: '低危'
              }
            ]
          }
        ]
      }
      this.cirChart.setOption(options)
    }
  },
  created() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userMessage'))
    if (this.userInfo) {
      this.user = this.userInfo.user
    }
  },
  watch: {
    getterCurrentCompany(val) {
      if (this.user.role == 2) {
        this.vulnersList = {}
        this.getData()
      }
    }
  },
  computed: {
    ...mapState(['currentCompany']),
    ...mapGetters(['getterCurrentCompany'])
  }
}
</script>

<style lang="less" scoped>
.conVulner {
  width: calc(100% - 40px);
  margin: 0 auto;
  height: 520px;
  background: #fff;
  margin-top: 20px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  overflow: hidden;
  .top-title {
    width: 100%;
    height: 47px;
    // border-bottom: 1px solid #e9ebef;
    //  padding-bottom: 10px;
    .headerEcharts {
      padding: 0 0 0 0 !important;
      position: relative;
      width: 100%;
      display: flex;
      border-bottom: 1px solid #e9ebef;
      justify-content: space-between;
      .conAssets-top::before {
        content: '';
        position: absolute;
        left: 0px;
        top: 18px;
        width: 4px;
        height: 16px;
        background-color: #2677ff;
        border-radius: 0 2px 2px 0;
      }
      .conAssets-top {
        position: relative;
        cursor: pointer;
        height: 47px;
        // width: 100%;
        // padding-top: 12px;
        line-height: 47px;
        padding-left: 16px;

        .con-title {
          // // font-family: PingFangSC-Semibold;
          color: #37393c;
          font-size: 16px;
          font-weight: 600;
          vertical-align: middle;
        }
        img {
          margin-left: 8px;
          vertical-align: middle;
        }
      }
    }
    // .item {
    //   margin-top: 3px;
    //   margin-right: 40px;
    //   position: absolute !important;
    //   left: 93%;
    //   top: 16px;
    //   width: 25%;
    // }
    .badge /deep/ .el-badge__content,
    .is-fixed {
      // right: 90%;
      height: 12px;
      line-height: 12px;
      font-size: 10px;
      left: 7px !important;
      top: 10px !important;
      right: unset !important;
      background: #ff4646 !important;
    }
    .goHandle {
      // font-family: PingFang SC;
      color: #62666c;
      cursor: pointer;
      font-size: 14px;
      margin-right: 16px;
      line-height: 47px;

      & > span {
        margin-left: 5px;
      }
    }
  }
  .vulner-types {
    display: inline-block;
    width: auto;
    height: 48px;
    background: #f5f8fc;
    border-radius: 4px;
    margin-top: 24px;
    margin-left: 24px;

    .type-li {
      display: inline-block;
      margin-right: 8px;
      cursor: pointer;
      margin-top: 8px;
      padding: 0 12px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      .type-title {
        // font-family: PingFang SC;
        color: #62666c;
      }
      .type-num {
        color: #2677ff;
        // font-family: PingFang SC;
        display: inline-block;
        margin-left: 4px;
      }
    }
    .type-li:nth-child(1) {
      margin-left: 8px;
    }
    .type-active {
      background-color: #fff;
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
      border-radius: 4px;
      .type-title {
        color: #37393c;
      }
    }
  }

  .total {
    display: flex;
    width: 100%;
    .total-left {
      width: 66%;
      .vulner-title {
        color: #37393c;
        // font-family: PingFang SC;
        font-weight: 500;
        margin: 16px 0 12px 24px;
        font-size: 16px;
      }

      .componentClass {
        width: 98%;
        margin-left: 24px;
        display: flex;
        height: 330px;
        .componentClass-box {
          display: flex;
          height: 330px;
          margin-bottom: 24px;
          flex-wrap: wrap;
          align-content: flex-start;
          // margin-top: 10px;
          width: 51%;
          & > div {
            width: 100%;
            height: 66px;
            // margin-bottom: 14px;
            // padding: 8px 3%;
          }
          /deep/.el-progress-bar__outer {
            background: rgba(30, 31, 33, 0.08) !important;
          }
          .active {
            background-color: #ecf0f6;
            border-radius: 4px 0 0 4px;
          }
          .top {
            margin-top: 16px;
            margin-left: 16px;
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            margin-right: 16px;
            & > div > span {
              display: inline-block;
              width: 18px;
              height: 18px;
              text-align: center;
              line-height: 18px;
              color: #fff;
              opacity: 1;
              border-radius: 2px;
              margin-right: 8px;
            }
            img {
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }
            .numClass {
              color: rgba(55, 57, 60, 1);
              font-size: 14px;
              // font-family: PingFang SC;
            }
            .indexClass0 {
              background: #ff4646;
            }
            .indexClass1 {
              background: #ff7900;
            }
            .indexClass2 {
              background: #ff7900;
            }
            .indexClass3 {
              background: #2677ff;
            }
            .indexClass4 {
              background: #2677ff;
            }
          }
          /deep/ .el-progress {
            margin-left: 16px;
            margin-right: 16px;
          }
        }
        .list-top {
          border-radius: 0 4px 4px 4px !important;
        }
        .list-last {
          border-radius: 4px 4px 4px 0 !important;
        }
        .componentClass-content {
          position: relative;
          width: 50%;
          .moreModule {
            box-sizing: border-box;
            z-index: 99;
            color: #2677ff;
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 50px;
            // margin-bottom: -20px;
            padding-top: 14px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 64%);
            text-align: center;

            &:hover {
              cursor: pointer;
            }

            .icon {
              height: 12px;
              width: 6px;
              margin-right: 0;
            }
          }
          .componentClass-ips {
            box-sizing: border-box;
            background-color: #ecf0f6;
            width: 100%;
            border-radius: 4px;
            height: 320px;
            margin-bottom: 24px;
            padding-bottom: 50px;
            overflow-y: auto;
            .ip-list {
              height: 30px;
              background-color: #fff;
              padding: 10px 2% 0 2%;
              font-size: 14px;
              box-shadow: 0px 2px 4px 0px rgba(0, 43, 115, 0.1);
              margin-top: 12px;
              margin-left: 16px;
              margin-right: 16px;
              border-radius: 4px;
              display: flex;
              justify-content: space-between;
              .ip {
                width: calc(100% - 210px);
                color: #2677ff;
                height: 24px;
                line-height: 24px;
                display: inline-block;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                &:hover {
                  cursor: pointer;
                }
              }
              .status {
                color: #2677ff;
                height: 24px;
                background: rgba(38, 119, 255, 0.12);
                border-radius: 4px;
                text-align: center;
                line-height: 24px;
                display: inline-block;
                padding: 0 5px;
              }
              .status-red {
                background: rgba(255, 70, 70, 0.1216);
                color: #ff4646;
              }
              .time {
                color: #62666c;
                display: inline-block;
                height: 24px;
                line-height: 24px;
              }
            }
            .ip-list:nth-child(1) {
              margin-top: 16px;
            }
          }
        }

        .list-compo:nth-child(1) /deep/ .el-progress-bar__inner {
          background: linear-gradient(90deg, #ff7a7a 0%, #ff4646 100%);
        }
        .list-compo:nth-child(2) /deep/ .el-progress-bar__inner {
          background: linear-gradient(90deg, #ffae66 0%, #ff7900 100%);
        }
        .list-compo:nth-child(3) /deep/ .el-progress-bar__inner {
          background: linear-gradient(90deg, #ffae66 0%, #ff7900 100%);
        }
        .list-compo:nth-child(4) /deep/ .el-progress-bar__inner {
          background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
        }
        .list-compo:nth-child(5) /deep/ .el-progress-bar__inner {
          background: linear-gradient(90deg, #4eafff 0%, #2677ff 100%);
        }
      }
    }
    .total-right {
      width: 32%;
      margin-left: 20px;
      margin-right: 24px;
      .right-title {
        color: #37393c;
        // font-family: PingFang SC;
        font-size: 16px;
        font-weight: 500;
        margin: 16px 0 12px 0;
      }
      .right-picture {
        width: 100%;
        height: 330px;
        margin-right: 4px;
        background: #f5f8fc;
        border-radius: 4px;
      }
    }
  }
}
.emptyClass {
  height: 80%;
  text-align: center;
  vertical-align: middle;
  svg {
    display: inline-block;
    font-size: 120px;
    margin-top: 90px;
  }
  p {
    line-height: 25px;
    color: #d1d5dd;
    span {
      margin-left: 4px;
      color: #2677ff;
      cursor: pointer;
    }
  }
}
</style>
