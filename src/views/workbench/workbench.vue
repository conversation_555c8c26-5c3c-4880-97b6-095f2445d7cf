<template>
  <div class="workContent">
    <selfIntroduction />
    <div
      style="
        display: flex;
        width: calc(100% - 40px);
        margin: 0 auto;
        margin-top: 20px;
        justify-content: space-between;
      "
    >
      <assetsOverview />
      <announcement />
    </div>
    <commonFunctions />
    <clueDatabase />
    <taskManagement />
    <div
      style="
        display: flex;
        width: calc(100% - 40px);
        margin: 0 auto;
        justify-content: space-between;
      "
    >
      <dataBreach />
      <eventWaining />
    </div>
    <vulnerabileManage @scrollToBottom="scrollToBottom" />
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import commonFunctions from './commonFunctions.vue'
import selfIntroduction from './selfIntroduction.vue'
import assetsOverview from './assetsOverview.vue'
import eventWaining from './eventWaining.vue'
import announcement from './announcement.vue'
import dataBreach from './dataBreach.vue'
import clueDatabase from './clueDatabase.vue'
import vulnerabileManage from './vulnerabileManage.vue'
import taskManagement from './taskManagement.vue'

export default {
  components: {
    commonFunctions,
    selfIntroduction,
    assetsOverview,
    eventWaining,
    announcement,
    dataBreach,
    clueDatabase,
    vulnerabileManage,
    taskManagement
  },
  data() {
    return {
      name: ''
    }
  },
  computed: {
    ...mapState(['scrollHeight'])
  },
  mounted() {
    document.getElementsByClassName('main_body')[0].scrollTop = this.scrollHeight
  },
  methods: {
    ...mapMutations(['setJumpScrollHeight']),
    scrollToBottom() {
      let scrollHeight = document.getElementsByClassName('main_body')[0].scrollTop
      this.setJumpScrollHeight(scrollHeight)
    }
  }
}
</script>

<style lang="less" scoped>
.workContent {
  width: 100%;
  // margin: 0 auto;
  margin-bottom: 20px;
  overflow: hidden;
}
</style>
