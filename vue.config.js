const path = require('path')

const webpack = require('webpack')
// 导入compression-webpack-plugin
const CompressionWebpackPlugin = require('compression-webpack-plugin')

const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzer
// 定义压缩文件类型
const resolve = function (dir) {
  return path.join(__dirname, dir)
}
const timesTamp = new Date().getTime() + '-1.11.9'
// const baseURL = "172.16.20.86:8080";// 10.11.12.41/10.10.10.189/10.11.12.36
const baseURL = '10.10.10.189' // 10.11.12.41/10.10.10.189/10.11.12.36
// const baseURL = '10.11.12.41'// 10.11.12.41/10.10.10.189/10.11.12.41
// const baseURL = 'foradar.baimaohui.net' // 10.11.12.41/10.10.10.189/10.11.12.41

module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: true, // 是否开启eslint保存检测
  productionSourceMap: false, // 是否在   构建生产包时生成sourcdeMap，防止源码泄露

  //babel会忽略node_modules中的文件。如果需要Babel转译，单独列出来。
  transpileDependencies: [
    'terser-webpack-plugin',
    'enquire.js',
    'vue-echarts',
    'resize-detector',
    'ant-design-vue',
    'element-ui',
    'vuex-module-decorators'
  ],

  chainWebpack: (config) => {
    config.plugins.delete('prefetch').delete('preload') //只加载当前页面需要的js

    config.resolve.alias
      .set('@', resolve('src')) /* 别名配置 */
      .set('@v', resolve('src/views'))
      .set('@api', resolve('src/api'))
      .set('@assets', resolve('src/assets'))
      .set('@comp', resolve('src/components'))
      .set('@utils', resolve('src/utils'))
      .set('@store', resolve('store'))
    config.optimization.runtimeChunk('single')
    config.module
      .rule('images')
      .use('url-loader')
      .tap((options) => {
        options.name = `img/[name].[ext]?v=${timesTamp}`
        options.fallback = {
          loader: 'file-loader',
          options: {
            name: `img/[name].[ext]?v=${timesTamp}`
          }
        }
        return options
      })
    // config.plugin("webpack-bundle-analyzer").use(require("webpack-bundle-analyzer").BundleAnalyzerPlugin)
  },
  configureWebpack: (config) => {
    config.devtool = false
    if (process.env.NODE_ENV === 'production') {
      // splitChunks 配置
      config.optimization.splitChunks = {
        // "initial"(入口) | "all"(推荐) | "async"(默认异步)
        chunks: 'all',
        // 最小 30 kb，拆分出来的 bundle 大于 30 kb
        minSize: 30000,
        // 最大
        maxSize: 0,
        // 最小 chunk ，默认1，引用1次就拆分，针对 cacheGroups 里面的配置，解决代码重复引用
        minChunks: 1,
        // 最大同时异步请求数
        maxAsyncRequests: 50,
        // 最大初始化请求数
        maxInitialRequests: 50,
        // chunk 名字分割符
        automaticNameDelimiter: '~',
        name: true,
        // 缓存策略组配置
        cacheGroups: {
          vendors: {
            name: 'vendors', // chunk 名字，字符串或者函数，不设置会按照共享的 chunks 命名
            test: /[\\/]node_modules[\\/]/, // 正则：哪些模块需要拆分
            priority: -10 // 缓存组优先级
          },
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true // 是否使用已有的 chunk，如果当前的模块已经包含在其它的 chunk 里面了就不重复生成新的。
          },
          // vue
          vue: {
            name: 'vue',
            test: /[\\/]node_modules[\\/]vue[\\/]/,
            priority: 1
          },
          // vuex
          vuex: {
            name: 'vuex',
            test: /[\\/]node_modules[\\/]vuex[\\/]/,
            priority: 1
          },
          // vue-router
          'vue-router': {
            name: 'vue-router',
            test: /[\\/]node_modules[\\/]vue-router[\\/]/,
            priority: 1
          },
          // element-ui
          'element-ui': {
            name: 'element-ui',
            test: /[\\/]node_modules[\\/]element-ui[\\/]/,
            priority: 1
          },
          // ant-design-vue
          'ant-design-vue': {
            name: 'ant-design-vue',
            test: /[\\/]node_modules[\\/]ant-design-vue[\\/]/,
            priority: 1
          },
          // @antv
          'ant-design-vue@': {
            name: 'ant-design-vue@',
            test: /[\\/]node_modules[\\/]@antv[\\/]/,
            priority: 1
          },
          // wl-explorer 文件系统
          mockjs2: {
            name: 'mockjs2',
            test: /[\\/]node_modules[\\/]mockjs2[\\/]/,
            priority: 1
          },
          // echarts 图表
          echarts: {
            name: 'echarts',
            test: /[\\/]node_modules[\\/]echarts[\\/]/,
            priority: 1
          },
          // highcharts 图表
          highcharts: {
            name: 'highcharts',
            test: /[\\/]node_modules[\\/]highcharts[\\/]/,
            priority: 1
          },
          // @ant-design
          '@ant-design': {
            name: '@ant-design',
            test: /[\\/]node_modules[\\/]@ant-design[\\/]/,
            priority: 1
          },
          // Sortable 表格拖动
          Sortable: {
            name: 'Sortable',
            test: /[\\/]node_modules[\\/]Sortable[\\/]/,
            priority: 1
          },
          // vue-i18n 国际化
          'vue-i18n': {
            name: 'vue-i18n',
            test: /[\\/]node_modules[\\/]vue-i18n[\\/]/,
            priority: 1
          },
          // moment
          moment: {
            name: 'moment',
            test: /[\\/]node_modules[\\/]moment[\\/]/,
            priority: 1
          },
          // zrender Echarts 画布依赖
          zrender: {
            name: 'zrender',
            test: /[\\/]node_modules[\\/]zrender[\\/]/,
            priority: 1
          },
          // lodash 实用js库
          // 'lodash': {
          //     name: 'lodash',
          //     test: /[\\/]node_modules[\\/]lodash[\\/]/,
          //     priority: 1
          // },
          //viewerjs
          viewerjs: {
            name: 'viewerjs',
            test: /[\\/]node_modules[\\/]viewerjs[\\/]/,
            priority: 1
          },
          //d3.js
          d3: {
            name: 'd3',
            test: /[\\/]node_modules[\\/]d3[\\/]/,
            priority: 1
          },
          //lottie-web
          'lottie-web': {
            name: 'lottie-web',
            test: /[\\/]node_modules[\\/]lottie-web[\\/]/,
            priority: 1
          },
          //pinyin-pro
          'pinyin-pro': {
            name: 'pinyin-pro',
            test: /[\\/]node_modules[\\/]pinyin-pro[\\/]/,
            priority: 1
          },
          'zm-tree-org': {
            name: 'zm-tree-org',
            test: /[\\/]node_modules[\\/]zm-tree-org[\\/]/,
            priority: 1
          },
          'hsxa-ui': {
            name: 'hsxa-ui',
            test: /[\\/]node_modules[\\/]hsxa-ui[\\/]/,
            priority: 1
          }
        }
      }
      // 生产环境取消 console.log
      config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true
      // 给打包js文件添加has值
      config.output.filename = `js/[name]${timesTamp}.js`
      config.output.chunkFilename = `js/[name]${timesTamp}.js`
    }
  },
  css: {
    extract: {
      filename: `./static/css/[name].css?v=${timesTamp}`,
      chunkFilename: `./static/css/[name].css?v=${timesTamp}`
    }
  },
  devServer: {
    port: '8080',
    proxy: {
      '/pc': {
        target: 'https://' + baseURL + '/api/v1', // 10.10.11.28/10.10.10.189
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/pc': '/'
        }
      },
      '/golang': {
        // target: 'http://' + '************:8080' + '/api/v1', // 方军本地环境
        target: 'https://' + baseURL + '/hack/api/v1',
        secure: false,
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/golang': '/'
        }
      },
      '/showImg': {
        target: 'https://' + baseURL,
        secure: false,
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/showImg': '/'
        }
      },
      '/wsIp': {
        target: 'wss://' + baseURL + '/ws?token=',
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/wsIp': ''
        }
      }
    },

    disableHostCheck: true // 这是由于新版的webpack-dev-server出于安全考虑，默认检查hostname，如果hostname 不是配置内的，将中断访问。
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [
        path.resolve(__dirname, './src/assets/css/common.less') //使用path.resolve
      ]
    }
  }
}
